# Dependencies
node_modules/
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
/lib/
/lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Environment files
.env
.env.*
.env.local
.env.development
.env.production
.venv
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.cursorrules

# Logs
logs
*.log
npm-debug.log*
.pnpm-debug.log*

# Next.js
.next/
out/

# Testing
coverage/
.nyc_output/

# Production
build/
dist/
# Compiled Next.js files that shouldn't be in src
src/**/dist/

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.notes/
plans/


# Vercel
.vercel

# Firebase
.firebase/
.aider*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

plans/
.plans/
.cursor/
.data/
.bogs/

# Added by <PERSON> Task Master
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/
.windsurfrules
package-lock.json
package.json
README-task-master.md
scripts
.Diagnostic