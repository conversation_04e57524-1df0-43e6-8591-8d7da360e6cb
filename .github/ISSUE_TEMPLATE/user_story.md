---
name: "👤 User Story"
about: "Create a user story to capture user needs and value"
title: "[Story]: As a [role] I want to [action] so that [benefit]"
labels: ["user-story"]
assignees: []
---

## 👤 User Story

### 📝 Story Description
As a [role]
I want to [action/feature]
So that [benefit/value]

### 🎯 Business Value
Describe the business value this story delivers.

### 📋 Acceptance Criteria
- [ ] Given [context/precondition]
  When [action/trigger]
  Then [expected outcome]
- [ ] Given [context/precondition]
  When [action/trigger]
  Then [expected outcome]

### 🎨 UI/UX Requirements
- [ ] Wireframes/Mockups
- [ ] Design Specifications
- [ ] Accessibility Requirements

### 📊 Technical Scope
- Frontend Changes:
- Backend Changes:
- Database Changes:
- API Changes:

### 🧪 Testing Scenarios
1. Scenario 1:
   - Given:
   - When:
   - Then:
2. Scenario 2:
   - Given:
   - When:
   - Then:

### 📏 Story Points
- Estimate: [1, 2, 3, 5, 8, 13]
- Complexity: [Low/Medium/High]
- Priority: [Must Have/Should Have/Could Have/Won't Have]

### 🔄 Dependencies
- [ ] Dependency 1
- [ ] Dependency 2

### 📚 Additional Information
- Related Documentation:
- External References:
- Notes:

### ✅ Definition of Ready
- [ ] Story is clear and well-defined
- [ ] Acceptance criteria are specific and testable
- [ ] UI/UX requirements are provided
- [ ] Dependencies are identified
- [ ] Story is sized and prioritized

### ✅ Definition of Done
- [ ] Acceptance criteria met
- [ ] Code reviewed and approved
- [ ] Tests written and passing
- [ ] Documentation updated
- [ ] UI/UX requirements implemented
- [ ] Product Owner review completed
- [ ] Deployed to staging environment 