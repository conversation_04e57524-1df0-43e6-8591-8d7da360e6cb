---
name: "📋 Task"
about: "Create a specific, actionable task"
title: "[Task]: "
labels: ["task"]
assignees: []
---

## 📋 Task

### 📝 Task Description
A clear and concise description of what needs to be done.

### 🎯 Objective
What is the purpose of this task?

### ✅ Deliverables
- [ ] Deliverable 1
- [ ] Deliverable 2
- [ ] Deliverable 3

### 📊 Technical Requirements
- [ ] Requirement 1
- [ ] Requirement 2
- [ ] Requirement 3

### 🔄 Implementation Steps
1. Step 1
2. Step 2
3. Step 3

### ⏱️ Estimated Effort
- Time Estimate: [hours/days]
- Complexity: [Low/Medium/High]
- Priority: [Low/Medium/High]

### 🚧 Dependencies
- [ ] Dependency 1
- [ ] Dependency 2

### 🧪 Testing Criteria
- [ ] Test Case 1
- [ ] Test Case 2

### 📚 Required Resources
- Tools:
- Access:
- Documentation:

### 👥 Stakeholders
- Assigned to:
- Reviewer:
- Others:

### 📝 Notes
Any additional notes or context.

### ✅ Definition of Done
- [ ] Code Complete
- [ ] Tests Passing
- [ ] Documentation Updated
- [ ] PR Created
- [ ] Code Reviewed 