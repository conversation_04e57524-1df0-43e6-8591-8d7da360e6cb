rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if a user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to check if the current user owns the document
    function isOwner(userId) {
      return request.auth.uid == userId;
    }

    // Helper function to check if a role is public
    function isPublicRole(roleId) {
      return get(/databases/$(database)/documents/roles/$(roleId)).data.is_public == true;
    }

    // Helper function to check if a template is public
    function isPublicTemplate(roleId, templateId) {
      return get(/databases/$(database)/documents/roles/$(roleId)/templates/$(templateId)).data.is_public == true;
    }

    // User collection - only authenticated users can access their own documents
    match /users/{userId} {
      allow read: if isAuthenticated() && isOwner(userId);
      allow write: if isAuthenticated() && isOwner(userId);

      // User's roles subcollection - only the owner can access their roles
      match /roles/{roleId} {
        allow read: if isAuthenticated() && isOwner(userId);
        allow write: if isAuthenticated() && isOwner(userId);
      }
    }

    // Roles collection - allow public access to public roles
    match /roles/{roleId} {
      allow read: if isPublicRole(roleId) || (isAuthenticated() && resource.data.user_id == request.auth.uid);
      allow write: if isAuthenticated() && (resource.data.user_id == request.auth.uid || !exists(/databases/$(database)/documents/roles/$(roleId)));

      // Templates subcollection - allow public access to public templates
      match /templates/{templateId} {
        allow read: if isPublicTemplate(roleId, templateId) || (isAuthenticated() && get(/databases/$(database)/documents/roles/$(roleId)).data.user_id == request.auth.uid);
        allow write: if isAuthenticated() && get(/databases/$(database)/documents/roles/$(roleId)).data.user_id == request.auth.uid;
      }

      // Questions subcollection - only accessible for public templates
      match /templates/{templateId}/questions/{questionId} {
        allow read: if isPublicTemplate(roleId, templateId) || (isAuthenticated() && get(/databases/$(database)/documents/roles/$(roleId)).data.user_id == request.auth.uid);
        allow write: if isAuthenticated() && get(/databases/$(database)/documents/roles/$(roleId)).data.user_id == request.auth.uid;
      }

      // Evaluation criteria subcollection - only accessible for public templates
      match /templates/{templateId}/evaluationCriteria/{criterionId} {
        allow read: if isPublicTemplate(roleId, templateId) || (isAuthenticated() && get(/databases/$(database)/documents/roles/$(roleId)).data.user_id == request.auth.uid);
        allow write: if isAuthenticated() && get(/databases/$(database)/documents/roles/$(roleId)).data.user_id == request.auth.uid;
      }

      // Job postings subcollection - allow public access for public roles
      match /job_postings/{postingId} {
        allow read: if isPublicRole(roleId) || (isAuthenticated() && get(/databases/$(database)/documents/roles/$(roleId)).data.user_id == request.auth.uid);
        allow write: if isAuthenticated() && get(/databases/$(database)/documents/roles/$(roleId)).data.user_id == request.auth.uid;
      }
    }

    // Public interview sessions - allow full access to the session and nested collections
    match /public_interview_sessions/{sessionId} {
      allow read, write: if true;

      // Allow access to nested collections
      match /{document=**} {
        allow read, write: if true;
      }
    }

    // Candidates collection - for unauthenticated users applying to jobs
    match /candidates/{candidateId} {
      // Allow creation and basic operations without authentication
      allow create: if request.resource.data.email != null
                    && request.resource.data.fullName != null;

      // Allow reading and updating candidate data without authentication for public access
      allow read, update: if true;

      // Applications subcollection - allow full public access
      match /applications/{applicationId} {
        // Allow full access to applications for public candidates
        allow read, write: if true;

        // Evaluations subcollection - IMPORTANT: Allow full access for any user without authentication
        match /evaluations/{evaluationId} {
          allow read, write: if true;
        }

        // Interview sessions subcollection - allow full access for public interviews
        match /interviews/{interviewId} {
          allow create, read, update, delete: if true;
        }
      }
    }

    // Public evaluations collection for anonymous users
    match /public_evaluations/{evaluationId} {
      allow read, write: if true;

      // Allow access to nested collections
      match /{document=**} {
        allow read, write: if true;
      }
    }

    // Public applications collection for anonymous users
    match /public_applications/{applicationId} {
      allow read, write: if true;

      // Allow access to nested collections
      match /{document=**} {
        allow read, write: if true;
      }
    }

    // Evaluations collection - allow full public access with nested collections
    match /evaluations/{evaluationId} {
      allow read, write: if true;

      // Allow access to nested collections
      match /{document=**} {
        allow read, write: if true;
      }
    }

    // Applications collection - allow public access for anonymous users
    match /applications/{applicationId} {
      allow read, write: if true;

      // Allow access to nested collections
      match /{document=**} {
        allow read, write: if true;
      }
    }
  }
}