rules_version = '2';

// Allows a user to upload resources only to their own area
service firebase.storage {
  match /b/{bucket}/o {
    // Helper function to check if a user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if the current user owns the document
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    // Helper function to check if file is a resume
    function isResumeFile(filename) {
      return filename.matches('.*\\.(pdf|doc|docx)$');
    }
    
    // Allow authenticated users to access their own files
    match /users/{userId}/{allPaths=**} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // Allow access to application files by role owners
    match /applications/{roleId}/{applicationId}/{allPaths=**} {
      allow read, write: if isAuthenticated();
    }
    
    // Public candidate applications - allow uploads for candidates applying to jobs
    match /candidates/{candidateId}/applications/{applicationId}/{allPaths=**} {
      allow read, write;
    }
    
    // Public application uploads - allow uploads for public applications
    match /public_applications/{applicationId}/{allPaths=**} {
      allow read, write;
    }
    
    // Public evaluations - allow uploads for public evaluations
    match /evaluations/{evaluationId}/{allPaths=**} {
      allow read, write;
    }
    
    // Allow public uploads to temporary location
    match /public_uploads/{roleId}/{applicationId}/{allPaths=**} {
      // Allow creation of files without authentication for temporary resume uploads
      allow read, write;
    }
  }
}
