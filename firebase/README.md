# Firebase Configuration

This directory contains Firebase configuration files for the Recruiva application.

## Files

- `firebase.json` - Main Firebase configuration file
- `firestore.rules` - Security rules for Firestore database
- `firestore.indexes.json` - Index configurations for Firestore

## Usage

All Firebase-related commands should be run from the `frontend` directory:

```bash
# Deploy all Firebase configurations
npm run firebase:deploy

# Deploy only Firestore rules
npm run firebase:deploy:rules

# Deploy only Firestore indexes
npm run firebase:deploy:indexes

# Start Firebase emulators for local development
npm run firebase:emulators
```

## Environment Variables

The application uses Firebase in both frontend and backend:

### Frontend (Next.js)
Required environment variables in `.env`:
```
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=
```

### Backend (FastAPI)
Required environment variables in `.env`:
```
FIREBASE_PROJECT_ID=
FIREBASE_PRIVATE_KEY_ID=
FIREBASE_PRIVATE_KEY=
FIREBASE_CLIENT_EMAIL=
FIREBASE_CLIENT_ID=
FIREBASE_STORAGE_BUCKET=
```

## Architecture

- Frontend uses Firebase Web SDK for authentication and real-time updates
- Backend uses Firebase Admin SDK for server-side operations
- Both use Firestore as the primary database
- Firebase Storage is used for file uploads

## Security

- Firestore security rules are defined in `firestore.rules`
- Backend uses service account credentials for admin access
- Frontend uses restricted API keys for client-side access 