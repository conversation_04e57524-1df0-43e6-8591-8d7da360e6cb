# Authentication Implementation

## Overview
The authentication system uses Firebase Authentication for user management and Firestore for storing additional user data. It supports both Google OAuth and email/password authentication methods.

## Features
- Google OAuth Sign-in/Registration
- Email/Password Sign-in/Registration
- Workspace vs Personal Account Differentiation
- Protected Routes
- Session Management
- User Profile Management

## Implementation Details

### Configuration

#### Firebase Setup
1. Create a Firebase project
2. Enable Authentication methods:
   - Google Sign-in
   - Email/Password
3. Set up Firestore database
4. Configure Firebase in the frontend:
   ```typescript
   // lib/firebase.ts
   import { initializeApp } from 'firebase/app';
   import { getAuth } from 'firebase/auth';

   const firebaseConfig = {
     // Your Firebase config (from Firebase Console)
   };

   const app = initializeApp(firebaseConfig);
   export const auth = getAuth(app);
   ```

### Frontend Implementation

#### Auth Context (`contexts/auth-context.tsx`)
Manages authentication state and provides auth methods throughout the application.

Key Features:
- User state management
- Authentication methods
- Route protection
- Error handling
- Session persistence

States:
```typescript
const [user, setUser] = useState<User | null>(null);
const [loading, setLoading] = useState(true);
const [error, setError] = useState<string | null>(null);
const [isRegistrationInProgress, setIsRegistrationInProgress] = useState(false);
const [isSignInInProgress, setIsSignInInProgress] = useState(false);
```

#### Authentication Methods

1. Google Sign-in:
```typescript
const signInWithGoogle = async (requireWorkEmail: boolean = false) => {
  // 1. Initialize Google Auth Provider
  // 2. Show sign-in popup
  // 3. Verify user exists in Firestore
  // 4. Handle workspace vs personal email requirements
  // 5. Update last sign-in time
  // 6. Redirect to dashboard
}
```

2. Email/Password Sign-in:
```typescript
const signInWithEmail = async (email: string, password: string) => {
  // 1. Attempt sign-in with credentials
  // 2. Verify user exists in Firestore
  // 3. Handle errors
}
```

3. Google Registration:
```typescript
const registerWithGoogle = async (requireWorkEmail: boolean = false) => {
  // 1. Show Google sign-in popup
  // 2. Verify email requirements
  // 3. Create Firestore user document
  // 4. Set user metadata
  // 5. Redirect to dashboard
}
```

#### User Document Structure
```typescript
interface UserData {
  uid: string;
  email: string;
  fullName: string;
  role: string;
  createdAt: string;
  updatedAt: string;
  photoURL?: string;
  isWorkspaceUser: boolean;
  authProvider: 'google' | 'email';
  status: 'active' | 'inactive';
  emailVerified: boolean;
  lastSignInTime: string;
  creationTime: string;
  accountType: 'personal' | 'workspace';
}
```

### Protected Routes
Routes under `/dashboard/*` require authentication. Users are redirected to sign-in if not authenticated.

### Error Handling
Common error scenarios:
- Invalid credentials
- Non-existent user
- Workspace email requirement
- Permission denied
- Network errors
- Popup closed by user

### Sign-in Flow
1. User initiates sign-in
2. Authentication with Firebase
3. Check user document in Firestore
4. Verify account type if required
5. Update last sign-in time
6. Redirect to dashboard

### Registration Flow
1. User initiates registration
2. Authentication with Firebase
3. Check email requirements
4. Create user document in Firestore
5. Set initial user metadata
6. Redirect to dashboard

### Session Management
- Firebase handles token management
- Auth state listener updates local state
- Automatic token refresh
- Secure token storage

### Security Rules
Firestore security rules should be configured to:
- Allow read/write to user's own document
- Prevent unauthorized access
- Validate data structure
- Enforce user permissions

Example:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## Best Practices
1. Always verify user existence in Firestore
2. Keep sensitive operations server-side
3. Implement proper error handling
4. Use type-safe interfaces
5. Maintain secure session management
6. Follow Firebase security best practices
7. Implement proper cleanup on errors
8. Use loading states for better UX
9. Provide clear error messages
10. Maintain audit logs for security events

## Future Improvements
1. Multi-factor authentication
2. Password recovery flow
3. Email verification
4. Session timeout handling
5. Account deletion
6. Role-based access control
7. OAuth provider expansion
8. Security event logging
9. Rate limiting
10. Enhanced error tracking 