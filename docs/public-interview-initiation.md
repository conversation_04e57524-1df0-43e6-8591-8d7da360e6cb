# Public Interview Initiation Flow

This document outlines the complete flow of how public interviews are initiated in Re<PERSON>ruiva when a user clicks on "Instant Interview". It traces the logic and data flow across frontend and backend components.

## Overview

The public interview feature allows candidates to apply for a position and immediately participate in an AI-driven interview without requiring authentication. This document details the entire process from the moment a candidate clicks "Instant Interview" to the point where the interview session is established with the AI agent.

## Flow Diagram

```
User clicks "Instant Interview" → Application Form → Resume Upload/Evaluation → Interview Session Creation → WebRTC Connection → AI Interview
```

## Firestore Document Creation

Two primary documents are created during the public interview process:

1. **Application Document** - Stores candidate information
2. **Public Interview Session Document** - Stores interview session data

## Detailed Flow

### 1. Frontend Initiation

#### 1.1 Entry Point

- The entry point is typically a "Instant Interview" button on a job details page (`/jobs/[id]/page.tsx`).
- When clicked, the user is directed to the instant interview page (`/instant-interview/[roleId]/page.tsx`).

#### 1.2 Application Form

- The `InstantInterviewPage` component renders a `CandidateApplicationForm` component.
- The form collects:
  - Full name
  - Email
  - Phone number (optional)
  - Resume (file upload)

#### 1.3 Form Submission Process

When the user submits the form, the following happens in `CandidateApplicationForm.onSubmit`:

1. Validates the form data and resume file
2. Checks for existing applications with the same email
3. Creates an application record in Firebase
4. Uploads the resume to Firebase Storage
5. Optionally evaluates the resume against the job requirements
6. Calls the `onComplete` callback with:
   ```typescript
   {
     applicationId: string;
     evaluation?: ResumeEvaluationResponse | BasicEvaluationResponse;
     isPassed?: boolean;
   }
   ```

### 2. Application Handling

#### 2.1 Application Completion

The `handleApplicationComplete` function in `InstantInterviewPage`:

1. Stores application data in localStorage:
   ```javascript
   localStorage.setItem('applicationId', applicationId);
   localStorage.setItem('roleId', roleId);
   ```
2. If resume evaluation was performed, stores the results:
   ```javascript
   localStorage.setItem('resumeEvaluation', JSON.stringify(evaluation));
   localStorage.setItem('resumeEvaluationResult', isPassed ? 'PASS' : 'FAIL');
   ```
3. Shows a success toast message
4. Redirects to the interview page:
   ```javascript
   router.push(`/instant-interview/${roleId}/interview?${queryParams.toString()}`);
   ```

### 3. Interview Session Initialization

#### 3.1 Interview Page Loading

The `PublicInterviewPage` component in `/instant-interview/[roleId]/interview/page.tsx`:

1. Retrieves parameters from the URL and localStorage
2. Sets public interview flags:
   ```javascript
   localStorage.setItem('isPublicInterview', 'true');
   ```
3. Calls `initializeInterview()` to set up the interview session

#### 3.2 Session Creation

The `createPublicInterviewSession` function:

1. Checks for existing active sessions
2. Prepares the session request payload:
   ```typescript
   const payload: PublicInterviewPayload = { 
     role_id: roleId,
     template_id: templateId, // Optional
     candidate_id: candidateId, // Optional
     application_id: applicationId, // Optional
     resume_text: resumeText, // Optional
     job_posting: jobPosting, // Optional
     candidate_name: candidateName // Optional
   };
   ```
3. Makes a POST request to the backend endpoint:
   ```typescript
   const response = await apiClient.post<SessionResponse>(
     `/realtime/public-interview-session?t=${requestTimestamp}`,
     payload
   );
   ```
4. Validates the response and handles retries if needed
5. Returns the session data:
   ```typescript
   {
     session_id: string;
     client_secret: {
       value: string;
       expires_at: number;
     };
     transcript_id: string;
     role_id: string;
     template_id?: string;
     is_public: true;
   }
   ```

### 4. Backend Processing

#### 4.1 API Endpoint

The backend handles the request in `create_public_interview_session` function in `/backend/app/api/v1/endpoints/realtime.py`:

1. Receives the request with role ID and optional parameters
2. Creates an anonymous application if none is provided:
   ```python
   anonymous_application = {
     "id": application_id,
     "roleId": request.role_id,
     "fullName": "Anonymous Candidate",
     "email": f"anonymous-{application_id[:8]}@example.com",
     "status": "interviewing",
     "source": "instant_interview",
     "created_at": datetime.now().isoformat(),
     "updated_at": datetime.now().isoformat(),
     "is_anonymous": True
   }
   ```
3. Fetches role data from the database
4. Initializes the interview agent

#### 4.2 Context Building

The backend builds the interview context using `get_public_interview_context` in `/backend/app/services/realtime/agents/interview_agent/services.py`:

1. Fetches role details
2. Gets application data if available
3. Extracts resume text from application data
4. Retrieves or creates an interview template
5. Formats required and preferred skills
6. Formats interview questions
7. Adds special public interview instructions
8. Builds the final context:
   ```python
   context = {
     "role_title": role.get("title", ""),
     "role_level": role.get("level", ""),
     "role_summary": role.get("summary", ""),
     "required_skills": required_skills_str,
     "preferred_skills": preferred_skills_str,
     "years_of_experience": role.get("yearsOfExperience", ""),
     "interview_stage": stage_info.get("stage", ""),
     "interview_duration": stage_info.get("duration", ""),
     "custom_instructions": stage_info.get("customInstructions", ""),
     "interview_questions": interview_questions_str,
     "evaluation_criteria": evaluation_criteria_str,
     "public_interview_info": public_interview_info,
     "candidate_resume": resume_text or "No resume data available.",
     "job_posting": role.get("description", role.get("summary", "No detailed job posting available.")),
     "metadata": metadata_str
   }
   ```

#### 4.3 System Prompt Construction

The system prompt for the interview agent is constructed using:

1. The interview agent prompt template from `/backend/app/services/realtime/agents/interview_agent/prompts/interview_agent.prompt`
2. The context data from `get_public_interview_context`
3. Special public interview instructions:
   ```
   ### SPECIAL INSTRUCTIONS FOR PUBLIC INTERVIEW
   This is a public interview for a candidate applying to this role. The candidate has not signed in and is accessing the interview through a public link.
   
   Special handling:
   1. Introduce yourself as the AI Interviewer for the role and explain the interview process clearly.
   2. Be particularly welcoming and supportive to put the candidate at ease.
   3. Explain that this is an initial screening interview for the role.
   4. Ask for their name at the beginning of the interview.
   5. Let them know their responses will be shared with the hiring team.
   6. At the end, thank them and explain what happens next in the process.
   ```

#### 4.4 OpenAI Session Creation

The backend creates a realtime session with OpenAI:

1. Prepares the payload with the formatted prompt and functions
2. Selects the appropriate model (e.g., `gpt-4o-mini-realtime-preview`)
3. Makes a request to OpenAI's realtime API:
   ```python
   response = await client.post(
     'https://api.openai.com/v1/realtime/sessions',
     headers={
       'Authorization': f'Bearer {api_key}',
       'Content-Type': 'application/json'
     },
     json=payload
   )
   ```
4. Creates a session response with:
   ```python
   session_response = SessionResponse(
     session_id=session_data.get("id"),
     client_secret={
       "value": session_data.get("client_secret", {}).get("value"),
       "expires_at": session_data.get("client_secret", {}).get("expires_at", 0)
     },
     transcript_id=transcript_id,
     role_id=request.role_id,
     template_id=template_id,
     candidate_id=request.candidate_id,
     application_id=application_id,
     is_public=True
   )
   ```
5. Stores the session data in Firestore:
   ```python
   await firebase_service.create_public_interview_session(session_dict)
   ```

### 5. Frontend Interview Experience

#### 5.1 WebRTC Connection

Once the session is created, the frontend:

1. Verifies the session token
2. Checks for device permissions
3. Renders the `DarkModeVideoCall` component with the session data
4. Establishes a WebRTC connection with OpenAI's realtime API
5. Begins the interview with the AI agent

#### 5.2 Interview Transcript Management

During the interview:

1. The frontend updates the transcript in Firebase using the `update_public_transcript` endpoint
2. The transcript is stored in the `public_interview_sessions` collection
3. After the interview, the transcript can be evaluated using the `evaluate_public_interview` endpoint

## Data Structures

### Frontend to Backend Request

```typescript
interface PublicInterviewPayload {
  role_id: string;
  template_id?: string;
  candidate_id?: string;
  application_id?: string;
  resume_text?: string;
  job_posting?: string;
  candidate_name?: string;
}
```

### Backend to Frontend Response

```typescript
interface SessionResponse {
  session_id: string;
  client_secret: {
    value: string;
    expires_at: number;
  };
  transcript_id: string;
  role_id: string;
  template_id?: string;
  template_name?: string;
  stage?: string;
  stage_index?: number;
  candidate_id?: string;
  application_id?: string;
  is_public: boolean;
}
```

### Firestore Document Creation Logic

#### 1. Application Document Creation

The application document is created in two different ways depending on the flow:

**Frontend Flow** (`CandidateApplicationForm.onSubmit` in `/frontend/src/components/application/CandidateApplicationForm.tsx`):
```javascript
// Create application data
const applicationData: Omit<CandidateApplication, 'id' | 'roleId' | 'created_at' | 'updated_at'> = {
  fullName: data.fullName,
  email: data.email,
  phoneNumber: data.phoneNumber || '',
  resumeUrl: '', // Will be updated after upload
  linkedInUrl: '',
  githubUrl: '',
  portfolioUrl: '',
  status: 'applied', // Set initial status to 'applied'
};

// Save application to get ID
applicationId = await saveApplication(roleId, applicationData);
```

The `saveApplication` function in `/frontend/src/lib/firebase/index.ts` creates the document:
```javascript
export const saveApplication = async (roleId: string, applicationData: Omit<CandidateApplication, 'id' | 'roleId' | 'created_at' | 'updated_at'>): Promise<string> => {
  // Create a new document reference with auto-generated ID
  const applicationRef = doc(collection(db, 'applications'));
  const applicationId = applicationRef.id;

  // Add timestamp fields
  const now = new Date().toISOString();
  const completeData = {
    ...applicationData,
    id: applicationId,
    roleId: roleId,
    created_at: now,
    updated_at: now
  };

  // Save to Firestore
  await setDoc(applicationRef, completeData);
  return applicationId;
};
```

**Backend Flow** (Anonymous application in `create_public_interview_session` in `/backend/app/api/v1/endpoints/realtime.py`):
```python
# Create a minimal application record
anonymous_application = {
    "id": application_id,
    "roleId": request.role_id,
    "fullName": "Anonymous Candidate",
    "email": f"anonymous-{application_id[:8]}@example.com",
    "status": "interviewing",
    "source": "instant_interview",
    "created_at": datetime.now().isoformat(),
    "updated_at": datetime.now().isoformat(),
    "is_anonymous": True
}

# Save to applications collection
app_ref = firebase_service.db.collection("applications").document(application_id)
app_ref.set(anonymous_application)
```

#### 2. Public Interview Session Document Creation

The public interview session document is created in the backend (`create_public_interview_session` in `/backend/app/api/v1/endpoints/realtime.py`):

```python
# Store session information for later reference
session_dict = {
    "session_id": session_response.session_id,
    "client_secret": {
        "value": session_response.client_secret["value"],
        "expires_at": session_response.client_secret["expires_at"],
    },
    "transcript_id": transcript_id,
    "role_id": request.role_id,
    "template_id": template_id,
    "is_public": True,
    "created_at": datetime.utcnow(),
    "status": "initialized"
}

# Add template name if available
if hasattr(session_response, 'template_name') and session_response.template_name:
    session_dict["template_name"] = session_response.template_name
    session_dict["templateName"] = session_response.template_name

# Add stage information if available
if hasattr(session_response, 'stage') and session_response.stage:
    session_dict["stage"] = session_response.stage
    session_dict["interviewStage"] = session_response.stage

# Add stage index if available
if hasattr(session_response, 'stage_index') and session_response.stage_index is not None:
    session_dict["stage_index"] = session_response.stage_index
    session_dict["stageIndex"] = session_response.stage_index

# Add candidate and application IDs if provided
if request.candidate_id:
    session_dict["candidateId"] = request.candidate_id
    session_dict["candidate_id"] = request.candidate_id
if request.application_id:
    session_dict["applicationId"] = request.application_id
    session_dict["application_id"] = request.application_id

# Add candidate name if provided
if hasattr(request, 'candidate_name') and request.candidate_name:
    session_dict["candidateName"] = request.candidate_name
    session_dict["candidate_name"] = request.candidate_name

# Save the session data in Firestore
await firebase_service.create_public_interview_session(session_dict)
```

The `create_public_interview_session` method in `FirebaseService` creates the document:
```python
async def create_public_interview_session(self, session_data: Dict[str, Any]) -> None:
    """
    Create a new public interview session document in Firestore.
    
    Args:
        session_data: Dictionary containing session data
    """
    try:
        session_id = session_data.get("session_id")
        if not session_id:
            raise ValueError("Session ID is required")
            
        # Create the document in the public_interview_sessions collection
        session_ref = self.db.collection("public_interview_sessions").document(session_id)
        session_ref.set(session_data)
        
        return session_id
    except Exception as e:
        logger.error(f"Error creating public interview session: {str(e)}")
        raise
```

### Firestore Document Schemas

#### 1. Application Document Schema

```
applications/
  {application_id}/
    id: string                    // Application ID (same as document ID)
    roleId: string                // ID of the role being applied for
    fullName: string              // Candidate's full name
    email: string                 // Candidate's email address
    phoneNumber?: string          // Candidate's phone number (optional)
    resumeUrl?: string            // URL to the uploaded resume in Firebase Storage
    linkedInUrl?: string          // LinkedIn profile URL (optional)
    githubUrl?: string            // GitHub profile URL (optional)
    portfolioUrl?: string         // Portfolio URL (optional)
    status: string                // Application status (e.g., "applied", "interviewing")
    source: string                // Source of the application (e.g., "instant_interview")
    created_at: string            // ISO timestamp of creation
    updated_at: string            // ISO timestamp of last update
    is_anonymous?: boolean        // Flag for anonymous applications (backend-created)
    evaluations?: Array           // Array of resume evaluations (if performed)
    resumeText?: string           // Parsed text from the resume (if available)
```

#### 2. Public Interview Session Document Schema

```
public_interview_sessions/
  {session_id}/
    session_id: string            // OpenAI session ID (same as document ID)
    client_secret: {              // OpenAI client secret for authentication
      value: string               // Secret value
      expires_at: number          // Expiration timestamp (Unix seconds)
    }
    transcript_id: string         // ID for the interview transcript
    role_id: string               // ID of the role being interviewed for
    template_id?: string          // ID of the interview template used
    is_public: boolean            // Flag indicating this is a public session (always true)
    created_at: timestamp         // Creation timestamp
    status: string                // Session status (e.g., "initialized", "in_progress", "completed")
    
    // Optional fields based on availability
    template_name?: string        // Name of the interview template
    templateName?: string         // Duplicate of template_name (for compatibility)
    stage?: string                // Interview stage (e.g., "Screening", "Technical")
    interviewStage?: string       // Duplicate of stage (for compatibility)
    stage_index?: number          // Index of the stage in the interview process
    stageIndex?: number           // Duplicate of stage_index (for compatibility)
    candidateId?: string          // Candidate ID (usually email address)
    candidate_id?: string         // Duplicate of candidateId (for compatibility)
    applicationId?: string        // ID of the linked application document
    application_id?: string       // Duplicate of applicationId (for compatibility)
    candidateName?: string        // Candidate's name
    candidate_name?: string       // Duplicate of candidateName (for compatibility)
    
    // Added during the interview
    messages?: Array<{            // Array of interview messages
      role: string                // Message role (system, user, assistant)
      content: string             // Message content
      timestamp?: number          // Message timestamp
    }>
    transcript?: {                // Nested transcript object (for backward compatibility)
      messages: Array             // Same as messages array above
      updated_at: timestamp       // Last update timestamp
      message_count: number       // Count of messages in the transcript
    }
    updated_at?: timestamp        // Last update timestamp
    message_count?: number        // Count of messages in the transcript
    
    // Added after interview completion
    evaluation_id?: string        // ID of the linked evaluation document (if evaluated)
    completed_at?: timestamp      // Completion timestamp
```

## Error Handling and Fallbacks

1. **Session Creation Failures**: The frontend attempts up to 3 retries with exponential backoff
2. **WebRTC Connection Failures**: Falls back to text-based interview mode
3. **Missing Templates**: Creates default screening template with standard questions
4. **Missing Role Data**: Creates minimal role data structure with default values
5. **Anonymous Applications**: Creates anonymous application records for tracking

## Conclusion

The public interview initiation flow in Recruiva provides a seamless experience for candidates to apply and immediately participate in an AI-driven interview. The process leverages Firebase for data storage, OpenAI's realtime API for the interview agent, and WebRTC for real-time communication. The system is designed with multiple fallbacks and error handling to ensure a reliable experience.
