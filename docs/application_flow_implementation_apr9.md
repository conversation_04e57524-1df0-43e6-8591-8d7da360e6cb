# Application Flow Implementation Documentation

## Overview

This document outlines the implementation details of the application flow in Recruiva, focusing on how candidates interact with the system when applying for roles and participating in AI-led interviews. The system has been refactored to use a consolidated storage approach, using the main `applications` collection as the single source of truth.

## Application Storage Architecture

### Storage Model

Our application system has been streamlined to eliminate redundancy while maintaining full functionality:

1. **Single Source of Truth: `applications` Collection**
   - Primary storage location for all application data
   - Stores complete application records including resume URLs, status, and metadata
   - Used by both authenticated and unauthenticated (public) flows

2. **Reference in Candidate Collection**
   - A reference to the application is maintained in `candidates/{candidateId}/applications/{applicationId}`
   - Contains only essential data to associate applications with candidates
   - Not intended for primary data storage, only for relationship tracking

3. **Minimal LocalStorage Usage**
   - Only stores IDs and minimal tracking information
   - No longer stores complete application data
   - Used for session continuity only

### Data Flow

```
User Submission → saveApplication() → applications collection
                                    → candidate reference (if email provided)
                                    → minimal localStorage tracking
```

## Key Components

### Backend API Endpoints

1. **`POST /api/v1/applications`**
   - Creates a new application record in the `applications` collection
   - Creates a reference in the candidate's subcollection if an email is provided
   - Initial status is set to `applied` (previously was `pending`)

2. **`POST /api/v1/realtime/public-interview-session`**
   - Creates an interview session for candidates applying through the instant interview flow
   - Creates an anonymous application in the `applications` collection if no application ID is provided

3. **`POST /api/v1/realtime/update-public-transcript`**
   - Updates transcript data for public interviews
   - Associates with application records in the `applications` collection

### Frontend Firebase Utilities

1. **`saveApplication()`**
   - Creates application records in the `applications` collection
   - Creates reference in the candidate's subcollection when applicable
   - Stores minimal tracking information in localStorage

2. **`uploadResume()`**
   - Uploads resume files to Firebase Storage
   - Uses a consistent storage path format: `applications/{applicationId}/{fileName}`
   - Updates the application record with the resume URL

3. **`getApplication()`**
   - Retrieves application data from the `applications` collection
   - Falls back to candidate subcollection only for backward compatibility during transition

4. **`checkExistingApplication()`**
   - Checks if a candidate has already applied for a specific role
   - Queries the `applications` collection by roleId and email

5. **`updateApplicationWithTranscript()`**
   - Updates application with interview transcript data
   - Only modifies records in the `applications` collection

### Frontend Components

1. **`CandidateApplicationForm`**
   - Simplified application submission process
   - Uses the consolidated storage approach
   - Sets the initial status to `applied`

2. **Public Interview Flow**
   - Updated to use the `applications` collection for all operations
   - Simplified data paths for interview transcripts and evaluations

3. **Interview Setup Page**
   - `frontend/src/app/instant-interview/[roleId]/interview/page.tsx`
   - Retrieves application data from the consolidated `applications` collection
   - Updates application with interview transcript data
   - Manages interview session metadata in a dedicated `public_interview_sessions` collection
   - Handles realtime transcript updates while maintaining application references

## Application Status Flow

1. **Initial Application**
   - Status: `applied` (previously `pending`)
   - Created in `applications` collection

2. **Interview Process**
   - Status updated to `interviewed` after completing an interview
   - Transcript ID associated with the application

3. **Evaluation Results**
   - Status updated to `shortlisted` or `rejected` based on evaluation
   - Evaluation data linked to the application

## Storage Paths

### Firebase Storage

- **Resumes**: `applications/{applicationId}/resume.{extension}`
- **Transcripts**: `applications/{applicationId}/interviews/{transcriptId}`
- **Evaluations**: `applications/{applicationId}/evaluations/{evaluationId}`

### Firestore Collections

- **Primary Application Data**: `applications/{applicationId}`
- **Candidate Reference**: `candidates/{candidateId}/applications/{applicationId}`
- **Interview Sessions**: `public_interview_sessions/{sessionId}`
- **Interview Data**: `applications/{applicationId}/interviews/{sessionId}`
- **Evaluation Data**: `applications/{applicationId}/evaluations/{evaluationId}`

## Implementation Notes

### Migration Considerations

- No data migration was required for this implementation
- Legacy code paths checking candidate subcollections are maintained for backward compatibility but will be phased out
- New applications are created only in the main `applications` collection

### Simplified Implementation

The consolidated approach offers several advantages:

1. **Consistency**: Single source of truth eliminates data discrepancies
2. **Simplified Queries**: Applications can be found in a known location
3. **Reduced Storage**: No duplicate data stored across collections
4. **Better Performance**: Fewer writes and reads required across the system
5. **Cleaner Code**: More straightforward implementation without complex fallbacks

## Code Examples

### Creating an Application

```typescript
// Backend API - applications.py
application_data = {
    "id": application_id,
    "roleId": application.role_id,
    "status": "applied",  // Initial status
    "created_at": SERVER_TIMESTAMP,
    "updated_at": SERVER_TIMESTAMP,
    "is_public": application.is_public
}

# Save to applications collection - single source of truth
applications_ref = db.collection("applications").document(application_id)
applications_ref.set(application_data)

# Create reference in candidate collection if email provided
if application.email:
    candidate_app_ref = db.collection("candidates").document(application.email).collection("applications").document(application_id)
    candidate_app_ref.set(application_data)
```

### Frontend Utilities

```typescript
// Save application to Firebase
const applicationId = await saveApplication(roleId, applicationData);

// Upload resume and update application
const resumeUrl = await uploadResume(roleId, resumeFile, applicationId);

// Checking existing applications
const existingApplication = await checkExistingApplication(roleId, email);

// Update with transcript
await updateApplicationWithTranscript(applicationId, transcriptId);
```

### Interview Page Implementation

```typescript
// Update application with interview transcript (from interview page)
if (sessionResponse.transcript_id && appId && !sessionResponse.reused) {
  try {
    // For public interviews, default to stage 0 (Screening)
    const stageIndex = 0;
    const stageName = "Screening";

    // Update the application with the transcript ID using our utility function
    // This will update the application in the main applications collection
    await updateApplicationWithTranscript(appId, sessionResponse.transcript_id, stageIndex, stageName);
    console.log(`Application ${appId} updated with transcript ID: ${sessionResponse.transcript_id}`);

    // Store session info in the public_interview_sessions collection for reference
    try {
      // Update or create session document as needed
      const docRef = doc(db, "public_interview_sessions", sessionResponse.transcript_id);
      await setDoc(docRef, {
        session_id: sessionResponse.session_id,
        transcript_id: sessionResponse.transcript_id,
        role_id: roleId,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp(),
        status: 'in_progress',
        is_public: true,
        applicationId: appId,
        messages: [] // Initialize with empty messages array
      }, { merge: true });
    } catch (error) {
      // Non-critical error, continue with interview
      console.error('Error updating session document:', error);
    }
  } catch (error) {
    console.error('Error updating application with transcript:', error);
  }
}
```

## Conclusion

The implementation of a consolidated application storage approach has significantly simplified the codebase while maintaining all the required functionality. By designating the `applications` collection as the single source of truth, we've eliminated redundancy, improved data consistency, and created a more maintainable system. This approach supports both authenticated and public application flows, ensuring a seamless experience for all users while reducing storage overhead and complexity.
