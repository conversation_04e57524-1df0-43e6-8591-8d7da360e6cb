# Interview Evaluation Feature Documentation

## Overview

The Interview Evaluation feature provides AI-powered assessment of candidate interviews, generating comprehensive reports with scores, analysis, and hiring recommendations. This feature works for both authenticated users and public interview sessions, providing consistent evaluation based on predefined criteria.

## Key Components

### Backend Components

1. **Interview Evaluation Service**
   - Processes interview transcripts using OpenAI's GPT models
   - Analyzes candidate responses against job requirements
   - Generates structured evaluation reports

2. **Evaluation API Endpoints**
   - `/evaluations` - Authenticated evaluation endpoints
   - `/public/evaluations` - Public evaluation endpoints
   - Supports both manual and automatic evaluation triggers

3. **Firestore Integration**
   - Stores evaluation results
   - Maintains relationship between interviews and evaluations
   - Enables real-time status updates

### Frontend Components

1. **Evaluation Service Layer**
   - Provides interface to backend API
   - Handles caching and polling for evaluation status
   - Manages error handling and retry logic

2. **Interview Setup Page**
   - Displays completed interviews
   - Provides "Evaluate with AI" button for eligible interviews
   - Shows evaluation status and results

3. **Evaluation Report Dialog**
   - Displays comprehensive evaluation results
   - Shows scores, analysis, and recommendations
   - Provides detailed breakdown of candidate performance

## Evaluation Flow

1. **Evaluation Request**
   - User clicks "Evaluate with AI" button for a completed interview
   - Frontend sends request to backend API
   - Backend acknowledges request and returns evaluation ID

2. **Evaluation Processing**
   - Backend retrieves interview transcript
   - Processes transcript using OpenAI GPT models
   - Analyzes responses against job requirements and evaluation criteria
   - Generates structured evaluation report

3. **Status Polling**
   - Frontend polls for evaluation status
   - Updates UI to reflect current status (pending, completed, failed)

4. **Results Display**
   - When evaluation is complete, "View Evaluation" button appears
   - Clicking button opens Evaluation Report Dialog
   - Dialog displays comprehensive evaluation results

## Evaluation Report Structure

1. **Evaluation Summary**
   - Overall score
   - Final decision (Go/No Go/Maybe)
   - Evaluation date

2. **Competency Scorecard**
   - Scores for individual competencies
   - Weighted scores based on importance
   - Total weighted score

3. **Question-by-Question Assessment**
   - Individual questions and answers
   - Score for each response
   - Detailed feedback on each answer

4. **Between the Lines**
   - Qualitative observations
   - Strengths and areas for improvement
   - Behavioral insights

5. **Final Decision**
   - Hiring recommendation
   - Detailed reasoning for decision
   - Key factors influencing the decision

## API Reference

### Authenticated Endpoints

#### Request Evaluation

```
POST /evaluations
```

**Request Body:**
```json
{
  "interviewId": "string",
  "roleId": "string",
  "applicationId": "string" (optional)
}
```

**Response:**
```json
{
  "evaluation_id": "string"
}
```

#### Get Evaluation

```
GET /evaluations/{evaluation_id}
```

**Response:**
```json
{
  "id": "string",
  "status": "pending" | "completed" | "failed",
  "data": {
    "overallScore": number,
    "decision": "Go" | "No Go" | "Maybe",
    "criteria": [...],
    "questionAssessments": [...],
    "summary": "string",
    "strengths": ["string"],
    "weaknesses": ["string"],
    "interviewerNotes": "string"
  },
  "created_at": "string",
  "updated_at": "string",
  "interview_id": "string",
  "role_id": "string",
  "application_id": "string",
  "error": "string" (optional)
}
```

#### Get Evaluations by Role

```
GET /evaluations?roleId={role_id}
```

**Response:**
```json
[
  {
    "id": "string",
    "status": "pending" | "completed" | "failed",
    ...
  }
]
```

#### Get Evaluations by Interview

```
GET /evaluations?interviewId={interview_id}
```

**Response:**
```json
[
  {
    "id": "string",
    "status": "pending" | "completed" | "failed",
    ...
  }
]
```

### Public Endpoints

#### Request Public Evaluation

```
POST /public/evaluations
```

**Request Body:**
```json
{
  "interviewId": "string",
  "roleId": "string",
  "applicationId": "string" (optional)
}
```

**Response:**
```json
{
  "evaluation_id": "string"
}
```

#### Get Public Evaluation

```
GET /public/evaluations/{evaluation_id}
```

**Response:**
Same as authenticated Get Evaluation

#### Auto-Evaluate Public Interview

```
POST /public/auto-evaluate/{interview_id}
```

**Response:**
```json
{
  "status": "success" | "error",
  "evaluation_id": "string" (if success),
  "message": "string" (if error)
}
```

## Error Handling

The evaluation feature includes robust error handling for various scenarios:

1. **Missing Transcript**
   - Error message: "Interview transcript not found"
   - User action: Ensure interview has a valid transcript

2. **Service Unavailable**
   - Error message: "Service unavailable"
   - User action: Try again later

3. **Processing Error**
   - Error message: "Failed to process interview transcript"
   - User action: Check transcript quality or try again

4. **Authorization Error**
   - Error message: "Unauthorized access"
   - User action: Ensure proper authentication

## Best Practices

1. **Template Setup**
   - Define clear evaluation criteria in interview templates
   - Include specific competencies with appropriate weights
   - Provide detailed descriptions for each criterion

2. **Interview Quality**
   - Ensure interviews cover all required competencies
   - Ask specific questions that allow for meaningful evaluation
   - Maintain consistent interview structure

3. **Evaluation Review**
   - Always review AI-generated evaluations
   - Consider evaluation as a tool to assist decision-making
   - Look for patterns across multiple evaluations

## Troubleshooting

1. **Evaluation Stuck in Pending State**
   - Issue: Evaluation status remains "pending" for extended period
   - Solution: Check backend logs for processing errors
   - Workaround: Cancel and restart evaluation

2. **Missing Evaluation Data**
   - Issue: Evaluation completed but missing certain sections
   - Solution: Check template setup for required criteria
   - Workaround: Add missing criteria and re-evaluate

3. **Incorrect Evaluation Results**
   - Issue: Evaluation results don't match expectations
   - Solution: Review interview transcript for quality issues
   - Workaround: Adjust template criteria and re-evaluate

## Future Enhancements

1. **Customizable Evaluation Templates**
   - Allow users to define custom evaluation criteria
   - Support different evaluation models for different roles

2. **Comparative Analysis**
   - Compare multiple candidates for the same role
   - Provide ranking and comparative strengths/weaknesses

3. **Integration with ATS**
   - Automatically update candidate status based on evaluation
   - Sync evaluation results with applicant tracking system

## Support

For issues with the Interview Evaluation feature, please contact <NAME_EMAIL> or open an issue in the project repository.
