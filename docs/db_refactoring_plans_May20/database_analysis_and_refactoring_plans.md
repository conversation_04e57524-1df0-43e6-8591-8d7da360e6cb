# Database Analysis and Refactoring Plans for Recruiva

## Table of Contents

1. [Introduction](#introduction)
2. [Current Database Implementation](#current-database-implementation)
   - [Database Technology](#database-technology)
   - [Schema Structure](#schema-structure)
   - [Collections and Relationships](#collections-and-relationships)
   - [Security Rules](#security-rules)
3. [Frontend-Backend Interactions](#frontend-backend-interactions)
   - [Data Flow](#data-flow)
   - [API Integration](#api-integration)
4. [Current Challenges and Issues](#current-challenges-and-issues)
   - [Performance Bottlenecks](#performance-bottlenecks)
   - [Scalability Concerns](#scalability-concerns)
   - [Security Considerations](#security-considerations)
   - [Data Redundancy](#data-redundancy)
   - [Maintenance Challenges](#maintenance-challenges)
5. [Refactoring Plan 1: Enhanced NoSQL Architecture](#refactoring-plan-1-enhanced-nosql-architecture)
   - [Proposed Changes](#proposed-changes-1)
   - [Technology Stack](#technology-stack-1)
   - [Implementation Strategy](#implementation-strategy-1)
   - [Benefits and Drawbacks](#benefits-and-drawbacks-1)
6. [Refactoring Plan 2: Hybrid SQL/NoSQL Solution](#refactoring-plan-2-hybrid-sqlnosql-solution)
   - [Proposed Changes](#proposed-changes-2)
   - [Technology Stack](#technology-stack-2)
   - [Implementation Strategy](#implementation-strategy-2)
   - [Benefits and Drawbacks](#benefits-and-drawbacks-2)
7. [Refactoring Plan 3: Microservices with Dedicated Datastores](#refactoring-plan-3-microservices-with-dedicated-datastores)
   - [Proposed Changes](#proposed-changes-3)
   - [Technology Stack](#technology-stack-3)
   - [Implementation Strategy](#implementation-strategy-3)
   - [Benefits and Drawbacks](#benefits-and-drawbacks-3)
8. [Comparison of Refactoring Plans](#comparison-of-refactoring-plans)
   - [Performance](#performance)
   - [Flexibility](#flexibility)
   - [Scalability](#scalability)
   - [Security](#security)
   - [Complexity of Refactoring](#complexity-of-refactoring)
9. [Conclusion and Recommendations](#conclusion-and-recommendations)

## Introduction

Recruiva is a recruitment platform that helps streamline the hiring process by managing roles, candidates, applications, interviews, and evaluations. This document provides a comprehensive analysis of the current database implementation in Recruiva and proposes three distinct refactoring plans to improve performance, flexibility, scalability, and security while maintaining the core functionality of the application.

## Current Database Implementation

### Database Technology

Recruiva currently uses **Firebase** as its primary database solution, specifically **Cloud Firestore**, which is a NoSQL, document-oriented database. The application leverages the following Firebase services:

- **Firestore**: For storing structured data in document collections
- **Firebase Authentication**: For user authentication and management
- **Firebase Storage**: For storing file assets (resumes, etc.)
- **Firebase Security Rules**: For access control

### Schema Structure

The database follows a document-oriented NoSQL structure with the following main collections and subcollections:

#### Main Collections

1. **users**: Stores user information
   - **roles** (subcollection): Roles created by each user
     - **templates** (subcollection): Interview templates for each role
     - **candidates** (subcollection): Candidates for each role
     - **intakeTranscripts** (subcollection): Transcripts of intake calls
     - **evaluations** (subcollection): Evaluations for candidates
     - **interviews** (subcollection): Interview sessions

2. **roles**: Top-level collection for published roles
   - **templates** (subcollection): Interview templates for each role
   - **job_postings** (subcollection): Job postings for the role

3. **candidates**: Stores candidate information
   - **applications** (subcollection): Applications submitted by candidates
     - **evaluations** (subcollection): Evaluations for each application
     - **interviews** (subcollection): Interview sessions for applications

4. **applications**: Top-level collection for all applications

5. **evaluations**: Top-level collection for evaluations

6. **public_interview_sessions**: Public interview session data

7. **public_applications**: Public application data

8. **public_evaluations**: Public evaluation data

### Collections and Relationships

The following diagram represents the key collections and their relationships:

```
users
  └── roles
      ├── templates
      │   ├── questions
      │   └── evaluationCriteria
      ├── candidates
      ├── intakeTranscripts
      ├── interviews
      └── evaluations

roles (public)
  ├── templates
  │   ├── questions
  │   └── evaluationCriteria
  └── job_postings

candidates
  └── applications
      ├── evaluations
      └── interviews

applications (top-level)

evaluations (top-level)

public_interview_sessions

public_applications

public_evaluations
```

### Security Rules

Firebase security rules control access to data with a role-based approach:

1. **Authentication-based Access**: Most collections require authentication
2. **User-specific Data**: Users can only access their own data
3. **Public Access for Specific Collections**: Some collections (public_*) allow unauthenticated access
4. **Role-based Permissions**: Different access levels for owners vs. viewers

Key security rule patterns:

```
// Helper function to check if a user is authenticated
function isAuthenticated() {
  return request.auth != null;
}

// Helper function to check if the current user owns the document
function isOwner(userId) {
  return request.auth.uid == userId;
}

// User collection - only authenticated users can access their own documents
match /users/{userId} {
  allow read: if isAuthenticated() && isOwner(userId);
  allow write: if isAuthenticated() && isOwner(userId);
}

// Public collections - allow public access
match /public_interview_sessions/{sessionId} {
  allow read, write: if true;
}
```

## Frontend-Backend Interactions

### Data Flow

The application follows a hybrid client-server architecture:

1. **Backend-driven API**: Most secure operations pass through backend API endpoints
2. **Direct Firestore Access**: Some frontend components interact directly with Firestore for read operations
3. **Browser Storage**: Local storage is used for preserving interview state during sessions

Key data flow patterns:

1. **Interview Process**:
   - Frontend initiates session -> API creates transcript -> Real-time updates via Firebase
   - Transcript completion triggers evaluation creation

2. **Application Submission**:
   - Form submission -> Backend API -> Firestore storage
   - Resume upload -> Firebase Storage -> URL stored in application record

### API Integration

The backend provides RESTful API endpoints that interact with the database:

1. **Role Management**:
   - Create, read, update, delete roles
   - Manage templates, job postings, and intake transcripts

2. **Application Processing**:
   - Submit applications
   - Upload resumes
   - Create and retrieve evaluations

3. **Interview Management**:
   - Create interview transcripts
   - Update and complete transcripts
   - Generate evaluations

## Current Challenges and Issues

### Performance Bottlenecks

1. **Inefficient Queries**:
   - Multiple round trips for nested data
   - Client-side filtering instead of server-side queries
   - Lack of proper indexing for complex queries

2. **Data Loading Issues**:
   - Large document fetches without pagination
   - Loading entire collection contents for filtering operations

3. **Transaction Handling**:
   - Inconsistent transaction management for multi-document updates
   - Potential race conditions in high-concurrency scenarios

### Scalability Concerns

1. **Document Size Limitations**:
   - Firestore has a 1MB limit per document, which could be problematic for large transcripts
   - Nested collections approach has scaling limitations for roles with many candidates

2. **Rate Limiting**:
   - Firebase has quotas for read/write operations that could become costly at scale
   - No batch processing for high-volume operations

3. **Indexing Overhead**:
   - Each query pattern requires custom indexes, which grow with collection size
   - Complex filters require compound indexes that increase write costs

### Security Considerations

1. **Rule Complexity**:
   - Current security rules include public access paths that could be exploited
   - Complex validation logic in rules is hard to maintain

2. **Direct Client Access**:
   - Some frontend code directly accesses Firestore, bypassing backend validation
   - Public endpoints lack rate limiting and throttling

3. **Authentication Handling**:
   - Token validation relies on Firebase without additional verification

### Data Redundancy

1. **Duplicated Storage**:
   - Same data stored in multiple collections (applications in user-specific paths and top-level)
   - Inconsistent update patterns causing data divergence

2. **Denormalization Issues**:
   - Redundant storage of role data in applications and evaluations
   - Updates to source data don't propagate to denormalized copies

### Maintenance Challenges

1. **Schema Drift**:
   - NoSQL flexibility has led to inconsistent document structures
   - Field normalization required in multiple places

2. **Error Handling**:
   - Inconsistent error patterns across services
   - Retry logic implemented inconsistently

3. **Code Organization**:
   - Firebase logic spread across multiple services
   - Singleton pattern makes testing difficult

## Refactoring Plan 1: Enhanced NoSQL Architecture

This plan focuses on optimizing the current Firebase NoSQL architecture with better data modeling and performance improvements.

### Proposed Changes 1

1. **Data Modeling Improvements**:
   - Flatten deeply nested collections
   - Create a consistent document structure with strictly defined schemas
   - Consolidate duplicated collections into single sources of truth

2. **Indexed Access Patterns**:
   - Create optimized compound indexes for common query patterns
   - Implement pagination for all list operations
   - Add field-level filtering with server-side execution

3. **Caching and Performance**:
   - Implement Redis cache layer for frequently accessed data
   - Add Firestore data bundle caching for common queries
   - Create materialized views for dashboard statistics

4. **Consistency Improvements**:
   - Use Firebase transactions for all multi-document operations
   - Implement optimistic concurrency control for data updates
   - Add server-side triggers for maintaining data consistency

5. **Backend Validation**:
   - Move all data access behind API endpoints
   - Add comprehensive validation layers
   - Implement rate limiting and throttling

### Technology Stack 1

- **Primary Database**: Cloud Firestore (optimized)
- **Caching Layer**: Redis or Firebase local caching
- **Backend**: Enhanced FastAPI with dedicated validation layer
- **Authentication**: Firebase Auth with custom claims
- **Storage**: Firebase Storage with improved lifecycle management
- **Function Layer**: Firebase Functions for data triggers and background jobs

### Implementation Strategy 1

1. **Phase 1: Schema Standardization**
   - Define strict TypeScript interfaces and Python models
   - Create database schema validation tools
   - Audit and fix inconsistent documents

2. **Phase 2: Query Optimization**
   - Analyze common query patterns
   - Create optimized indexes
   - Implement pagination and filtering

3. **Phase 3: API Consolidation**
   - Move direct Firestore access behind API endpoints
   - Add validation layers
   - Implement proper error handling

4. **Phase 4: Caching Implementation**
   - Add Redis caching for frequent queries
   - Implement cache invalidation
   - Create background refresh jobs

5. **Phase 5: Performance Testing and Tuning**
   - Load test with realistic data volumes
   - Fine-tune indexes and query patterns
   - Optimize transaction patterns

### Benefits and Drawbacks 1

**Benefits:**
- Maintains current technology stack familiarity
- Incremental implementation with minimal downtime
- Addresses immediate performance issues
- Lower learning curve for development team

**Drawbacks:**
- Limited by Firebase's fundamental constraints
- Scaling costs may still be high for certain operations
- Complex querying limitations still exist
- Security rule management remains complex

## Refactoring Plan 2: Hybrid SQL/NoSQL Solution

This plan introduces a relational database for structured data while keeping NoSQL for unstructured content, providing the best of both worlds.

### Proposed Changes 2

1. **Database Splitting**:
   - Move structured data (users, roles, applications) to PostgreSQL
   - Keep unstructured data (transcripts, evaluations) in Firestore
   - Create a data synchronization layer

2. **Relational Schema**:
   - Design normalized PostgreSQL schema for core entities
   - Implement proper foreign key constraints
   - Add efficient indexes for common queries

3. **Service Boundary Definition**:
   - Create clear domain boundaries between services
   - Define data ownership and access patterns
   - Implement proper data encapsulation

4. **API Gateway**:
   - Create unified API gateway for client interactions
   - Implement request routing to appropriate services
   - Add comprehensive request validation

5. **Transaction Management**:
   - Implement distributed transactions where needed
   - Use event-based consistency for cross-database operations
   - Add eventual consistency where appropriate

### Technology Stack 2

- **Structured Data**: PostgreSQL
- **Unstructured Data**: Cloud Firestore
- **Backend**: FastAPI with SQLAlchemy ORM
- **Authentication**: Combined JWT and Firebase Auth
- **Cache Layer**: Redis
- **API Gateway**: API Gateway with Lambda or FastAPI
- **Storage**: AWS S3 or Firebase Storage

### Implementation Strategy 2

1. **Phase 1: Schema Design**
   - Design relational schema for structured data
   - Define entity relationships
   - Create migration scripts

2. **Phase 2: Data Migration**
   - Implement one-way sync from Firestore to PostgreSQL
   - Validate data consistency
   - Switch read operations to PostgreSQL

3. **Phase 3: API Refactoring**
   - Update API endpoints to use new data sources
   - Implement unified API gateway
   - Create service-specific APIs

4. **Phase 4: Write Migration**
   - Switch write operations to PostgreSQL
   - Implement backward sync where needed
   - Validate end-to-end operations

5. **Phase 5: Service Optimization**
   - Implement caching for frequent queries
   - Optimize query patterns
   - Add monitoring and analytics

### Benefits and Drawbacks 2

**Benefits:**
- Better performance for relational queries
- Reduced costs for structured data operations
- Improved data integrity with constraints
- More flexible query capabilities

**Drawbacks:**
- Increased system complexity
- Managing two database technologies
- Synchronization challenges
- Higher implementation effort

## Refactoring Plan 3: Microservices with Dedicated Datastores

This plan completely restructures the application into microservices with purpose-built datastores for each domain.

### Proposed Changes 3

1. **Domain-Based Microservices**:
   - Split application into domain-specific services (Users, Roles, Applications, Interviews, Evaluations)
   - Implement API gateway for client interactions
   - Use event-driven architecture for cross-service communication

2. **Purpose-Built Datastores**:
   - Select optimal database for each domain:
     - Users/Roles: PostgreSQL
     - Applications: PostgreSQL with JSON columns
     - Interviews: MongoDB for transcript storage
     - Evaluations: ElasticSearch for searchable evaluations
     - Analytics: ClickHouse for time-series data

3. **Event Sourcing and CQRS**:
   - Implement event sourcing for core business processes
   - Separate command and query responsibilities
   - Create read models optimized for specific use cases

4. **Distributed Authentication**:
   - Implement OAuth 2.0 with JWT tokens
   - Create centralized user management service
   - Add fine-grained permission model

5. **Infrastructure as Code**:
   - Containerize all services with Docker
   - Implement Kubernetes for orchestration
   - Use Terraform for infrastructure provisioning

### Technology Stack 3

- **User Service**: PostgreSQL + SpringBoot
- **Role Service**: PostgreSQL + FastAPI
- **Application Service**: PostgreSQL + FastAPI
- **Interview Service**: MongoDB + Node.js
- **Evaluation Service**: ElasticSearch + FastAPI
- **Analytics Service**: ClickHouse + FastAPI
- **Message Bus**: Kafka or RabbitMQ
- **API Gateway**: Kong or Traefik
- **Authentication**: Keycloak or Auth0
- **Monitoring**: Prometheus + Grafana
- **Deployment**: Kubernetes + Terraform

### Implementation Strategy 3

1. **Phase 1: Domain Modeling**
   - Define bounded contexts
   - Design service interfaces
   - Create event schema

2. **Phase 2: Core Infrastructure**
   - Set up Kubernetes cluster
   - Implement API gateway
   - Configure message bus

3. **Phase 3: Service Migration**
   - Implement services one domain at a time
   - Start with non-critical services
   - Gradually shift traffic to new services

4. **Phase 4: Data Migration**
   - Create ETL processes for each datastore
   - Validate data consistency
   - Implement bidirectional sync during transition

5. **Phase 5: Frontend Adaptation**
   - Update frontend to use API gateway
   - Implement new authentication flow
   - Optimize client-side performance

### Benefits and Drawbacks 3

**Benefits:**
- Optimal performance for each domain
- Independent scaling of services
- Flexibility to evolve technology stack
- Improved fault isolation

**Drawbacks:**
- Highest implementation complexity
- Operational overhead of multiple technologies
- Distributed system challenges
- Higher initial cost and time investment

## Comparison of Refactoring Plans

### Performance

| Plan | Score (1-10) | Rationale |
|------|--------------|-----------|
| Enhanced NoSQL | 7 | Improved through caching and query optimization, but still limited by Firestore constraints |
| Hybrid SQL/NoSQL | 8 | Better performance for structured queries, optimal storage for different data types |
| Microservices | 9 | Purpose-built datastores provide optimal performance for each domain |

### Flexibility

| Plan | Score (1-10) | Rationale |
|------|--------------|-----------|
| Enhanced NoSQL | 6 | Limited by Firebase's query capabilities and structure |
| Hybrid SQL/NoSQL | 8 | SQL offers more flexible queries, while NoSQL provides flexibility for unstructured data |
| Microservices | 9 | Complete freedom to evolve each service independently |

### Scalability

| Plan | Score (1-10) | Rationale |
|------|--------------|-----------|
| Enhanced NoSQL | 7 | Firebase provides good automatic scaling, but with cost and performance constraints |
| Hybrid SQL/NoSQL | 8 | Better scaling for structured data, potential bottlenecks at sync points |
| Microservices | 9 | Independent scaling for each service based on demand |

### Security

| Plan | Score (1-10) | Rationale |
|------|--------------|-----------|
| Enhanced NoSQL | 7 | Improved through API consolidation, but still complex rule management |
| Hybrid SQL/NoSQL | 8 | Better control through traditional database security, simplified rules |
| Microservices | 9 | Fine-grained permissions, isolated services, dedicated security components |

### Complexity of Refactoring

| Plan | Score (1-10, lower is easier) | Rationale |
|------|--------------------------------|-----------|
| Enhanced NoSQL | 4 | Incremental improvements to existing architecture |
| Hybrid SQL/NoSQL | 7 | Significant changes but maintains parts of current system |
| Microservices | 9 | Complete restructuring of the application architecture |

## Conclusion and Recommendations

Based on the analysis of the current database implementation and the comparison of refactoring plans, the following recommendations are provided:

### Short-term (3-6 months)
**Recommendation: Enhanced NoSQL Architecture**

This plan offers the quickest path to performance improvements while minimizing disruption. Key actions:
- Implement API consolidation to improve security
- Add caching for frequent queries
- Optimize indexes and query patterns
- Standardize document schemas

### Mid-term (6-12 months)
**Recommendation: Hybrid SQL/NoSQL Solution**

Once short-term improvements are in place, gradually introduce PostgreSQL for structured data to gain better query performance and data integrity. Key actions:
- Design relational schema for core entities
- Implement data migration with validation
- Gradually shift read and write operations
- Add comprehensive API gateway

### Long-term (12+ months)
**Recommendation: Selective Microservices**

Rather than converting the entire system to microservices at once, adopt a selective approach based on scaling needs. Key actions:
- Identify domains with highest performance or scaling needs
- Implement targeted microservices for those domains
- Use event-driven architecture for integration
- Gradually expand microservices adoption based on business value

This phased approach allows the system to evolve while delivering continuous improvements and minimizing risk.