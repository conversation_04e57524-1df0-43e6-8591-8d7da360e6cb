# Frontend Files to Update for Firebase Schema Migration

This document lists all the files that need to be updated to align with the new Firebase database schema defined in `docs/db_refactoring_plans_May20/firebase_database_design_specification.md`.

## Type Definitions

### 1. Data Model Interfaces

| File | Changes Needed |
|------|---------------|
| `frontend/src/types/role.ts` | Update `Role` and `CandidateApplication` interfaces to match new schema |
| `frontend/src/services/templates/api.ts` | Update `InterviewTemplate`, `Question`, and `Criterion` interfaces |
| `frontend/src/services/interviews/api.ts` | Update `Interview` interface to match new schema |
| `frontend/src/services/evaluation/types.ts` | Update `Evaluation` interface to match new schema |
| `frontend/src/services/resume/types.ts` | Create new `ResumeEvaluation` interface |

## API Services

### 2. Service Implementations

| File | Changes Needed |
|------|---------------|
| `frontend/src/services/roles/service.ts` | Update to work with new `jobs` collection |
| `frontend/src/services/applications/service.ts` | Update to work with new `applications` collection |
| `frontend/src/services/templates/service.ts` | Update to work with new `templates` collection |
| `frontend/src/services/interviews/service.ts` | Update to work with new `interviews` collection |
| `frontend/src/services/evaluation/service.ts` | Update to work with new `interviewEvaluations` collection |
| `frontend/src/services/resume/service.ts` | Create new service for `resumeEvaluations` collection |

### 3. API Endpoints

| File | Changes Needed |
|------|---------------|
| `frontend/src/services/roles/api.ts` | Update API endpoints to match new schema |
| `frontend/src/services/applications/api.ts` | Update API endpoints to match new schema |
| `frontend/src/services/templates/api.ts` | Update API endpoints to match new schema |
| `frontend/src/services/interviews/api.ts` | Update API endpoints to match new schema |
| `frontend/src/services/evaluation/api.ts` | Update API endpoints to match new schema |
| `frontend/src/services/resume/api.ts` | Create new API endpoints for `resumeEvaluations` |

## Components

### 4. Dashboard Components

| File | Changes Needed |
|------|---------------|
| `frontend/src/app/dashboard/page.tsx` | Update to use new API services |
| `frontend/src/components/dashboard/RecentRoles.tsx` | Update to use new `jobs` collection |
| `frontend/src/components/dashboard/RecentApplications.tsx` | Update to use new `applications` collection |

### 5. Role Components

| File | Changes Needed |
|------|---------------|
| `frontend/src/app/roles/page.tsx` | Update to use new `jobs` collection |
| `frontend/src/app/roles/[id]/page.tsx` | Update to use new `jobs` collection |
| `frontend/src/components/roles/RoleForm.tsx` | Update to match new `jobs` schema |
| `frontend/src/components/roles/RoleCard.tsx` | Update to match new `jobs` schema |
| `frontend/src/components/roles/RoleList.tsx` | Update to use pagination and field selection |

### 6. Application Components

| File | Changes Needed |
|------|---------------|
| `frontend/src/app/applications/page.tsx` | Update to use new `applications` collection |
| `frontend/src/app/applications/[id]/page.tsx` | Update to use new `applications` collection |
| `frontend/src/components/application/ApplicationForm.tsx` | Update to match new `applications` schema |
| `frontend/src/components/application/ApplicationCard.tsx` | Update to match new `applications` schema |
| `frontend/src/components/application/ApplicationList.tsx` | Update to use pagination and field selection |

### 7. Interview Components

| File | Changes Needed |
|------|---------------|
| `frontend/src/app/roles/[id]/interview-setup/[stageIndex]/page.tsx` | Update to use new `interviews` collection |
| `frontend/src/app/instant-interview/[roleId]/interview/page.tsx` | Update to use new `interviews` collection |
| `frontend/src/components/interviews/InterviewCard.tsx` | Update to match new `interviews` schema |
| `frontend/src/components/interviews/InterviewList.tsx` | Update to use pagination and field selection |

### 8. Evaluation Components

| File | Changes Needed |
|------|---------------|
| `frontend/src/components/evaluation/EvaluationReport.tsx` | Update to match new `interviewEvaluations` schema |
| `frontend/src/components/evaluation/EvaluationSummary.tsx` | Update to match new `interviewEvaluations` schema |
| `frontend/src/components/evaluation/EvaluationList.tsx` | Update to use pagination and field selection |

### 9. Template Components

| File | Changes Needed |
|------|---------------|
| `frontend/src/components/templates/TemplateForm.tsx` | Update to match new `templates` schema |
| `frontend/src/components/templates/QuestionsList.tsx` | Update to match new `questions` schema |
| `frontend/src/components/templates/CriteriaList.tsx` | Update to match new `criteria` schema |
| `frontend/src/components/templates/QuestionForm.tsx` | Update to match new `questions` schema |
| `frontend/src/components/templates/CriterionForm.tsx` | Update to match new `criteria` schema |

## Firebase Integration

### 10. Direct Firestore Access

| File | Changes Needed |
|------|---------------|
| `frontend/src/lib/firebase/applications.ts` | Replace with API calls to backend |
| `frontend/src/lib/firebase/transcript.ts` | Replace with API calls to backend |
| `frontend/src/lib/firebase/simplified-evaluations.ts` | Replace with API calls to backend |
| `frontend/src/app/roles/[id]/interview-setup/[stageIndex]/page.tsx` | Remove direct Firestore queries |
| `frontend/src/app/instant-interview/[roleId]/interview/page.tsx` | Remove direct Firestore queries |

## Context Providers

### 11. User Context

| File | Changes Needed |
|------|---------------|
| `frontend/src/contexts/auth-context.tsx` | Add companyId to user context |
| `frontend/src/hooks/use-auth.ts` | Update to include companyId |

## Utilities

### 12. Migration Utilities

| File | Changes Needed |
|------|---------------|
| `frontend/src/lib/utils/migration.ts` | Create new file for migration utilities |
| `frontend/src/lib/utils/validation.ts` | Add validation for new schema |

## Configuration

### 13. Feature Flags

| File | Changes Needed |
|------|---------------|
| `frontend/src/config/features.ts` | Create new file for feature flags |

## Tests

### 14. Test Files

| File | Changes Needed |
|------|---------------|
| `frontend/src/__tests__/services/*.test.ts` | Update to test new API services |
| `frontend/src/__tests__/components/*.test.tsx` | Update to test new components |

## Summary

In total, approximately 50 files need to be updated to align with the new Firebase database schema. The changes include:

1. Updating data model interfaces to match the new schema
2. Updating API services to work with the new collections
3. Updating components to use the new API services
4. Replacing direct Firestore access with API calls
5. Adding support for field selection, pagination, and multi-tenancy

For detailed implementation plans, see the [Frontend Refactoring Plan](./frontend_refactoring_plan.md).
