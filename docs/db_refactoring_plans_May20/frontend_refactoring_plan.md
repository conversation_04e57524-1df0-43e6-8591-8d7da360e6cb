# Frontend Refactoring Plan for New Firebase Schema

This document outlines the comprehensive changes needed in the frontend codebase to align with the new Firebase database schema defined in `docs/db_refactoring_plans_May20/firebase_database_design_specification.md`.

## Overview of Changes

The frontend needs to be updated to work with the new flattened database structure, which includes:

1. Updating data models/interfaces to match the new schema
2. Replacing direct Firestore access with API calls
3. Implementing field selection and pagination
4. Updating UI components to work with the new data structure
5. Adding support for multi-tenancy with companyId

## Data Model Updates

### 1. Role/Job Interface Updates

The `Role` interface needs to be updated to match the new `jobs` collection:

```typescript
// Update in frontend/src/types/role.ts
export interface Role {
  id: string;
  userId: string;
  companyId: string; // New field
  title: string;
  summary: string;
  status: RoleStatus;
  jobType: JobType;
  priority: RolePriority;
  departmentId?: string;
  departmentName?: string;
  hiringManagerId?: string;
  hiringManagerContact?: string;
  location: {
    city: string;
    remoteStatus: string; // Remote, Hybrid, On-site
  };
  requiredSkills: Record<string, boolean>;
  preferredSkills: Record<string, boolean>;
  keyResponsibilities: string[];
  experience: {
    minimum: number;
    preferred: number;
  };
  education: {
    degree: string;
    field: string;
  };
  salary: {
    min: number;
    max: number;
    currency: string;
  };
  benefits: {
    healthInsurance: boolean;
    vacationDays: number;
    retirementPlan: boolean;
  };
  interviewProcess: InterviewStage[];
  team: string;
  teamDynamic: string;
  keyStakeholders: string[];
  isPublished: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}
```

### 2. Application Interface Updates

Update the `CandidateApplication` interface to match the new `applications` collection:

```typescript
// Update in frontend/src/types/role.ts
export interface CandidateApplication {
  id: string;
  jobId: string; // Changed from roleId
  candidateId: string;
  userId: string;
  companyId: string; // New field
  status: string;
  source: string;
  resumeId: string; // Changed from resumeUrl
  parsedResumeText?: string;
  submittedAt: string;
  updatedAt: string;
  currentStage?: {
    name: string;
    startedAt: string;
  };
  notes?: string;
  tags?: string[];
  customFields?: Record<string, any>;
  referralInfo?: {
    name: string;
    relationship: string;
  };
  resumeEvaluationScore?: number;
  overallScore?: number;
  relevantExperience?: number;
  hasPendingAction: boolean;
  lastInteractionAt?: string;
  rejectionReason?: string;
  nextStep?: string;
}
```

### 3. Template Interface Updates

Update the `InterviewTemplate` interface to match the new `templates` collection:

```typescript
// Update in frontend/src/services/templates/api.ts
export interface InterviewTemplate {
  id: string;
  jobId: string; // Changed from roleId
  userId: string;
  companyId: string; // New field
  createdBy: string;
  stageName: string; // Changed from stage
  stageIndex: number;
  customInstructions: string;
  duration: string;
  status: TemplateStatus;
  passRate: number;
  statistics: {
    averageScore: number;
    topScore: number;
    topPerformerIds: string[];
    candidatesInterviewed: number;
    passRate: number;
  };
  createdAt: string;
  updatedAt: string;
}
```

### 4. Question Interface Updates

Update the `Question` interface to match the new `questions` collection:

```typescript
// Update in frontend/src/services/templates/api.ts
export interface Question {
  id: string;
  templateId: string;
  jobId: string; // New field
  userId: string; // New field
  companyId: string; // New field
  stageName: string; // New field
  stageIndex: number; // New field
  question: string;
  text: string; // Duplicate for compatibility
  purpose: string;
  idealAnswerCriteria: string;
  statistics: {
    averageScore: number;
    topScore: number;
    totalAnswers: number;
    topScoringCandidateId: string;
  };
  sequence: number;
  createdAt: string;
  updatedAt: string;
}
```

### 5. Criterion Interface Updates

Update the `Criterion` interfaces to match the new `criteria` collection:

```typescript
// Update in frontend/src/services/templates/api.ts
export interface CriterionBase {
  id: string;
  templateId: string;
  jobId: string; // New field
  userId: string; // New field
  companyId: string; // New field
  stageName: string; // New field
  stageIndex: number; // New field
  type: CriterionType;
  criteria: string;
  description?: string;
  sequence: number; // New field
  createdAt: string; // New field
  updatedAt: string; // New field
}
```

### 6. Interview Interface Updates

Create or update the `Interview` interface to match the new `interviews` collection:

```typescript
// Update in frontend/src/services/interviews/api.ts
export interface Interview {
  id: string;
  applicationId?: string;
  roleId: string; // Will be renamed to jobId
  templateId?: string;
  candidateId?: string;
  userId: string;
  companyId: string; // New field
  sessionId?: string;
  stageName: string;
  stageIndex: number;
  startTime?: string;
  endTime?: string;
  duration?: number;
  status: string;
  transcriptId?: string;
  evaluationId?: string;
  hasEvaluation: boolean;
  notes?: string;
  questions?: Array<{
    id: string;
    question: string;
    purpose?: string;
  }>;
  isAutomated: boolean;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
}
```

### 7. Transcript Interface Updates

Create or update the `Transcript` interface to match the new `transcripts` collection:

```typescript
// Create in frontend/src/services/transcripts/api.ts
export interface Transcript {
  id: string;
  applicationId?: string;
  jobId: string; // Changed from roleId
  type: string;
  candidateId?: string;
  candidateName?: string;
  userId: string;
  companyId: string; // New field
  sessionId?: string;
  interviewId?: string;
  templateId?: string;
  stageName?: string;
  stageIndex?: number;
  messages: Array<{
    id: string;
    role: string;
    content: string;
    timestamp: number;
  }>;
  messageCount: number;
  clientSecret?: {
    value: string;
    expiresAt: number;
  };
  evaluationId?: string;
  hasEvaluation: boolean;
  evaluationSummary?: {
    decision?: string;
    overallScore?: number;
    confidence?: string;
    summary?: string;
    minimumPassRate?: number;
  };
  status: string;
  summary?: string;
  keyInsights?: string[];
  metadata?: {
    duration?: number;
    wordCount?: number;
    questionCount?: number;
  };
  startedAt: string;
  completedAt?: string;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
  processingStatus?: string;
}
```

### 8. Evaluation Interface Updates

Update the `Evaluation` interface to match the new `interviewEvaluations` collection:

```typescript
// Update in frontend/src/services/evaluation/types.ts
export interface Evaluation {
  id: string;
  metadata: {
    applicationId: string;
    jobId: string; // Changed from roleId
    interviewId: string;
    evaluatedAt: string;
    evaluatedBy: string;
  };
  companyId: string; // New field
  evaluationSummary: {
    candidateName: string;
    job: string;
    overallScore: number;
    decision: string;
    confidence: string;
    minimumPassRate: number;
    summary: string;
  };
  betweenTheLines: Array<{
    criteria: string;
    observation: string;
    impact: string;
  }>;
  decisionReasoning: {
    strengths: string[];
    concerns: string[];
    keyFactors: string[];
    finalRecommendation: string;
  };
  disqualifierCheck: Array<{
    criteria: string;
    evidence: string;
    explanation: string;
    triggered: boolean;
  }>;
  questionAnalysis: Array<{
    question: string;
    answer: string;
    evaluation: string;
    relatedCompetencies: string[];
    strengths: string[];
    weaknesses: string[];
  }>;
  scorecardEvaluation: Array<{
    competency: string;
    reasoning: string;
    score: number;
    weight: number;
    weightedScore: number;
  }>;
}
```

### 9. Resume Evaluation Interface Updates

Create a new interface for the `resumeEvaluations` collection:

```typescript
// Create in frontend/src/services/resume/types.ts
export interface ResumeEvaluation {
  id: string;
  applicationId: string;
  resumeId: string;
  candidateId: string;
  evaluatorId: string;
  scores: Record<string, number>;
  overallScore: number;
  evaluationSummary?: string;
  strengths?: string[];
  weaknesses?: string[];
  recommendation: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  notes?: string;
}
```

## API Service Updates

The frontend should move away from direct Firestore access and use API services instead. Here are the key changes needed:

### 1. Replace Direct Firestore Queries

Replace all direct Firestore queries in the frontend with API calls. For example:

```typescript
// BEFORE: Direct Firestore access
const fetchCompletedInterviews = async (roleId: string, stageIndex: number) => {
  const interviewsRef = collection(db, "public_interview_sessions");
  const q = query(
    interviewsRef,
    where("roleId", "==", roleId)
  );
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
};

// AFTER: API service call
const fetchCompletedInterviews = async (roleId: string, stageIndex: number) => {
  const response = await interviewsService.listInterviews({
    jobId: roleId,
    stageIndex: stageIndex,
    status: 'completed'
  });
  return response.data;
};
```

### 2. Implement Field Selection

Update API services to support field selection to reduce data transfer:

```typescript
// Add field selection to API calls
const getRoleSummary = async (roleId: string) => {
  return rolesService.getRole(roleId, {
    fields: ['id', 'title', 'status', 'priority', 'updatedAt']
  });
};
```

### 3. Implement Pagination

Add pagination support to all list operations:

```typescript
// Add pagination to list operations
const getApplicationsPage = async (page: number, pageSize: number) => {
  return applicationsService.listApplications({
    page,
    pageSize,
    status: 'active'
  });
};
```

### 4. Add CompanyId to All Queries

Update all API services to include companyId in queries:

```typescript
// Add companyId to all queries
const getUserRoles = async (userId: string, companyId: string) => {
  return rolesService.listRoles({
    userId,
    companyId
  });
};
```

## Component Updates

The following components need to be updated to work with the new data structure:

### 1. Dashboard Components

Update the dashboard components to use the new API services and data structures:

- `frontend/src/app/dashboard/page.tsx`
- `frontend/src/components/dashboard/RecentRoles.tsx`
- `frontend/src/components/dashboard/RecentApplications.tsx`

### 2. Role Components

Update role-related components:

- `frontend/src/app/roles/page.tsx`
- `frontend/src/app/roles/[id]/page.tsx`
- `frontend/src/components/roles/RoleForm.tsx`
- `frontend/src/components/roles/RoleCard.tsx`

### 3. Application Components

Update application-related components:

- `frontend/src/app/applications/page.tsx`
- `frontend/src/app/applications/[id]/page.tsx`
- `frontend/src/components/application/ApplicationForm.tsx`
- `frontend/src/components/application/ApplicationCard.tsx`

### 4. Interview Components

Update interview-related components:

- `frontend/src/app/roles/[id]/interview-setup/[stageIndex]/page.tsx`
- `frontend/src/app/instant-interview/[roleId]/interview/page.tsx`
- `frontend/src/components/interviews/InterviewCard.tsx`

### 5. Evaluation Components

Update evaluation-related components:

- `frontend/src/components/evaluation/EvaluationReport.tsx`
- `frontend/src/components/evaluation/EvaluationSummary.tsx`

## Service Implementation Updates

The following services need to be updated or created to work with the new database schema:

### 1. RolesService Updates

Update `frontend/src/services/roles/service.ts` to work with the new schema:

```typescript
// Update API methods to use new schema
async getRole(roleId: string, options?: { fields?: string[] }): Promise<Role | null> {
  try {
    // Add field selection support
    const queryParams: Record<string, any> = {};
    if (options?.fields) {
      queryParams.fields = options.fields.join(',');
    }

    // Add companyId from user context
    const user = await this.userService.getCurrentUser();
    if (user?.companyId) {
      queryParams.companyId = user.companyId;
    }

    const response = await apiClient.get<Role>(`/jobs/${roleId}`, { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching role:', error);
    return null;
  }
}

async listRoles(options?: {
  page?: number;
  pageSize?: number;
  status?: string;
  fields?: string[];
  companyId?: string;
}): Promise<{ data: Role[]; pagination: { total: number; page: number; pageSize: number; } }> {
  try {
    const queryParams: Record<string, any> = {
      page: options?.page || 1,
      pageSize: options?.pageSize || 10
    };

    if (options?.status) {
      queryParams.status = options.status;
    }

    if (options?.fields) {
      queryParams.fields = options.fields.join(',');
    }

    // Add companyId from options or user context
    if (options?.companyId) {
      queryParams.companyId = options.companyId;
    } else {
      const user = await this.userService.getCurrentUser();
      if (user?.companyId) {
        queryParams.companyId = user.companyId;
      }
    }

    const response = await apiClient.get<{ data: Role[]; pagination: any }>('/jobs', { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching roles:', error);
    return { data: [], pagination: { total: 0, page: 1, pageSize: 10 } };
  }
}
```

### 2. ApplicationsService Updates

Update `frontend/src/services/applications/service.ts` to work with the new schema:

```typescript
async getApplication(applicationId: string, options?: {
  fields?: string[];
  include?: string[];
}): Promise<ExtendedApplication | null> {
  try {
    const queryParams: Record<string, any> = {};

    if (options?.fields) {
      queryParams.fields = options.fields.join(',');
    }

    if (options?.include) {
      queryParams.include = options.include.join(',');
    }

    const response = await apiClient.get<ExtendedApplication>(`/applications/${applicationId}`, { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching application:', error);
    return null;
  }
}

async listApplications(options?: {
  jobId?: string;
  status?: string;
  page?: number;
  pageSize?: number;
  companyId?: string;
}): Promise<{ data: ExtendedApplication[]; pagination: any }> {
  try {
    const queryParams: Record<string, any> = {
      page: options?.page || 1,
      pageSize: options?.pageSize || 10
    };

    if (options?.jobId) {
      queryParams.jobId = options.jobId;
    }

    if (options?.status) {
      queryParams.status = options.status;
    }

    // Add companyId from options or user context
    if (options?.companyId) {
      queryParams.companyId = options.companyId;
    } else {
      const user = await this.userService.getCurrentUser();
      if (user?.companyId) {
        queryParams.companyId = user.companyId;
      }
    }

    const response = await apiClient.get<{ data: ExtendedApplication[]; pagination: any }>('/applications', { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching applications:', error);
    return { data: [], pagination: { total: 0, page: 1, pageSize: 10 } };
  }
}
```

### 3. TemplatesService Updates

Update `frontend/src/services/templates/service.ts` to work with the new schema:

```typescript
async getTemplate(jobId: string, templateId: string, options?: {
  fields?: string[];
  include?: string[];
}): Promise<InterviewTemplate | null> {
  try {
    const queryParams: Record<string, any> = {};

    if (options?.fields) {
      queryParams.fields = options.fields.join(',');
    }

    if (options?.include) {
      queryParams.include = options.include.join(',');
    }

    // Add companyId from user context
    const user = await this.userService.getCurrentUser();
    if (user?.companyId) {
      queryParams.companyId = user.companyId;
    }

    const response = await apiClient.get<InterviewTemplate>(`/templates/${templateId}`, { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching template:', error);
    return null;
  }
}

async listTemplates(jobId: string, options?: {
  page?: number;
  pageSize?: number;
  stageName?: string;
  fields?: string[];
  companyId?: string;
}): Promise<{ data: InterviewTemplate[]; pagination: any }> {
  try {
    const queryParams: Record<string, any> = {
      jobId,
      page: options?.page || 1,
      pageSize: options?.pageSize || 10
    };

    if (options?.stageName) {
      queryParams.stageName = options.stageName;
    }

    if (options?.fields) {
      queryParams.fields = options.fields.join(',');
    }

    // Add companyId from options or user context
    if (options?.companyId) {
      queryParams.companyId = options.companyId;
    } else {
      const user = await this.userService.getCurrentUser();
      if (user?.companyId) {
        queryParams.companyId = user.companyId;
      }
    }

    const response = await apiClient.get<{ data: InterviewTemplate[]; pagination: any }>('/templates', { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching templates:', error);
    return { data: [], pagination: { total: 0, page: 1, pageSize: 10 } };
  }
}
```

### 4. InterviewsService Updates

Update `frontend/src/services/interviews/service.ts` to work with the new schema:

```typescript
async getInterview(interviewId: string, options?: {
  fields?: string[];
  include?: string[];
}): Promise<Interview | null> {
  try {
    const queryParams: Record<string, any> = {};

    if (options?.fields) {
      queryParams.fields = options.fields.join(',');
    }

    if (options?.include) {
      queryParams.include = options.include.join(',');
    }

    // Add companyId from user context
    const user = await this.userService.getCurrentUser();
    if (user?.companyId) {
      queryParams.companyId = user.companyId;
    }

    const response = await apiClient.get<Interview>(`/interviews/${interviewId}`, { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching interview:', error);
    return null;
  }
}

async listInterviews(options?: {
  jobId?: string;
  applicationId?: string;
  status?: string;
  page?: number;
  pageSize?: number;
  companyId?: string;
}): Promise<{ data: Interview[]; pagination: any }> {
  try {
    const queryParams: Record<string, any> = {
      page: options?.page || 1,
      pageSize: options?.pageSize || 10
    };

    if (options?.jobId) {
      queryParams.jobId = options.jobId;
    }

    if (options?.applicationId) {
      queryParams.applicationId = options.applicationId;
    }

    if (options?.status) {
      queryParams.status = options.status;
    }

    // Add companyId from options or user context
    if (options?.companyId) {
      queryParams.companyId = options.companyId;
    } else {
      const user = await this.userService.getCurrentUser();
      if (user?.companyId) {
        queryParams.companyId = user.companyId;
      }
    }

    const response = await apiClient.get<{ data: Interview[]; pagination: any }>('/interviews', { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching interviews:', error);
    return { data: [], pagination: { total: 0, page: 1, pageSize: 10 } };
  }
}
```

### 5. EvaluationService Updates

Update `frontend/src/services/evaluation/service.ts` to work with the new schema:

```typescript
async getEvaluation(evaluationId: string, options?: {
  fields?: string[];
  include?: string[];
}): Promise<Evaluation | null> {
  try {
    const queryParams: Record<string, any> = {};

    if (options?.fields) {
      queryParams.fields = options.fields.join(',');
    }

    if (options?.include) {
      queryParams.include = options.include.join(',');
    }

    // Add companyId from user context
    const user = await this.userService.getCurrentUser();
    if (user?.companyId) {
      queryParams.companyId = user.companyId;
    }

    const response = await apiClient.get<Evaluation>(`/evaluations/${evaluationId}`, { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching evaluation:', error);
    return null;
  }
}

async listEvaluations(options?: {
  jobId?: string;
  applicationId?: string;
  interviewId?: string;
  page?: number;
  pageSize?: number;
  companyId?: string;
}): Promise<{ data: Evaluation[]; pagination: any }> {
  try {
    const queryParams: Record<string, any> = {
      page: options?.page || 1,
      pageSize: options?.pageSize || 10
    };

    if (options?.jobId) {
      queryParams.jobId = options.jobId;
    }

    if (options?.applicationId) {
      queryParams.applicationId = options.applicationId;
    }

    if (options?.interviewId) {
      queryParams.interviewId = options.interviewId;
    }

    // Add companyId from options or user context
    if (options?.companyId) {
      queryParams.companyId = options.companyId;
    } else {
      const user = await this.userService.getCurrentUser();
      if (user?.companyId) {
        queryParams.companyId = user.companyId;
      }
    }

    const response = await apiClient.get<{ data: Evaluation[]; pagination: any }>('/evaluations', { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching evaluations:', error);
    return { data: [], pagination: { total: 0, page: 1, pageSize: 10 } };
  }
}
```

## Migration Strategy

To safely migrate the frontend to the new database schema, follow these steps:

### 1. Create New API Services

1. Create new API services that work with the new schema
2. Implement field selection, pagination, and companyId support
3. Test the new services with the backend API

### 2. Update Data Models

1. Update TypeScript interfaces to match the new schema
2. Create migration utilities to convert between old and new formats
3. Add validation to ensure data integrity

### 3. Update Components Incrementally

1. Update one component at a time to use the new API services
2. Test each component thoroughly before moving to the next
3. Use feature flags to enable/disable new components in production

### 4. Remove Direct Firestore Access

1. Identify all direct Firestore access in the codebase
2. Replace with API calls to the backend
3. Remove Firebase imports that are no longer needed

### 5. Add Multi-Tenant Support

1. Update user context to include companyId
2. Add companyId to all API calls
3. Update UI to show company-specific data

## Testing Strategy

1. Create unit tests for all new API services
2. Create integration tests for component interactions
3. Test pagination and field selection
4. Test multi-tenant scenarios
5. Perform end-to-end testing of common user flows

## Conclusion

This refactoring plan outlines the changes needed to align the frontend with the new Firebase database schema. By following this plan, the frontend will be updated to use the new flattened structure, implement field selection and pagination, and support multi-tenancy with companyId.