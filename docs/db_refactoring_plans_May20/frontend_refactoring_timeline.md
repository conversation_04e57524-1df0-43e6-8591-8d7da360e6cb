# Frontend Refactoring Implementation Timeline

This document outlines the recommended timeline for implementing the frontend refactoring plan to align with the new Firebase database schema.

## Overview

The implementation is divided into 5 phases, with each phase building on the previous one. The total estimated time for implementation is 4-6 weeks, depending on team size and complexity.

## Phase 1: Create New API Services (Week 1-2)

### Week 1: Data Model Updates

| Day | Tasks | Deliverables |
|-----|-------|-------------|
| 1-2 | Update Role/Job interfaces | Updated `types/role.ts` |
| 3-4 | Update Application interfaces | Updated application interfaces |
| 5 | Update Template, Question, and Criterion interfaces | Updated template interfaces |

### Week 2: API Service Implementation

| Day | Tasks | Deliverables |
|-----|-------|-------------|
| 1-2 | Implement RolesService with field selection and pagination | Updated `services/roles/service.ts` |
| 3-4 | Implement ApplicationsService with field selection and pagination | Updated `services/applications/service.ts` |
| 5 | Implement TemplatesService with field selection and pagination | Updated `services/templates/service.ts` |

## Phase 2: Update Core Components (Week 3-4)

### Week 3: Dashboard and Role Components

| Day | Tasks | Deliverables |
|-----|-------|-------------|
| 1-2 | Update Dashboard components | Updated dashboard components |
| 3-5 | Update Role components | Updated role components |

### Week 4: Application and Interview Components

| Day | Tasks | Deliverables |
|-----|-------|-------------|
| 1-2 | Update Application components | Updated application components |
| 3-5 | Update Interview components | Updated interview components |

## Phase 3: Update Evaluation Components (Week 5)

| Day | Tasks | Deliverables |
|-----|-------|-------------|
| 1-2 | Implement EvaluationService with field selection and pagination | Updated `services/evaluation/service.ts` |
| 3-4 | Update Evaluation components | Updated evaluation components |
| 5 | Create ResumeEvaluationService and components | New resume evaluation service and components |

## Phase 4: Remove Direct Firestore Access (Week 6)

| Day | Tasks | Deliverables |
|-----|-------|-------------|
| 1-2 | Identify all direct Firestore access | List of files with direct Firestore access |
| 3-4 | Replace with API calls to backend | Updated files with API calls |
| 5 | Remove Firebase imports that are no longer needed | Cleaned up imports |

## Phase 5: Add Multi-Tenant Support (Week 7)

| Day | Tasks | Deliverables |
|-----|-------|-------------|
| 1-2 | Update user context to include companyId | Updated auth context |
| 3-4 | Add companyId to all API calls | Updated API services |
| 5 | Update UI to show company-specific data | Updated UI components |

## Testing and Deployment (Week 8)

| Day | Tasks | Deliverables |
|-----|-------|-------------|
| 1-2 | Create unit tests for all new API services | New test files |
| 3-4 | Create integration tests for component interactions | New integration tests |
| 5 | Deploy to staging environment | Deployed application |

## Detailed Task Breakdown

### Phase 1: Create New API Services

1. **Update Data Models**
   - Update `Role` interface to match `jobs` collection
   - Update `CandidateApplication` interface to match `applications` collection
   - Update `InterviewTemplate` interface to match `templates` collection
   - Update `Question` interface to match `questions` collection
   - Update `Criterion` interface to match `criteria` collection
   - Update `Interview` interface to match `interviews` collection
   - Update `Evaluation` interface to match `interviewEvaluations` collection
   - Create new `ResumeEvaluation` interface for `resumeEvaluations` collection

2. **Implement API Services**
   - Update `RolesService` to work with new `jobs` collection
   - Update `ApplicationsService` to work with new `applications` collection
   - Update `TemplatesService` to work with new `templates` collection
   - Update `InterviewsService` to work with new `interviews` collection
   - Update `EvaluationService` to work with new `interviewEvaluations` collection
   - Create new `ResumeEvaluationService` for `resumeEvaluations` collection

### Phase 2: Update Core Components

1. **Update Dashboard Components**
   - Update `dashboard/page.tsx` to use new API services
   - Update `RecentRoles.tsx` to use new `jobs` collection
   - Update `RecentApplications.tsx` to use new `applications` collection

2. **Update Role Components**
   - Update `roles/page.tsx` to use new `jobs` collection
   - Update `roles/[id]/page.tsx` to use new `jobs` collection
   - Update `RoleForm.tsx` to match new `jobs` schema
   - Update `RoleCard.tsx` to match new `jobs` schema
   - Update `RoleList.tsx` to use pagination and field selection

3. **Update Application Components**
   - Update `applications/page.tsx` to use new `applications` collection
   - Update `applications/[id]/page.tsx` to use new `applications` collection
   - Update `ApplicationForm.tsx` to match new `applications` schema
   - Update `ApplicationCard.tsx` to match new `applications` schema
   - Update `ApplicationList.tsx` to use pagination and field selection

4. **Update Interview Components**
   - Update `roles/[id]/interview-setup/[stageIndex]/page.tsx` to use new `interviews` collection
   - Update `instant-interview/[roleId]/interview/page.tsx` to use new `interviews` collection
   - Update `InterviewCard.tsx` to match new `interviews` schema
   - Update `InterviewList.tsx` to use pagination and field selection

### Phase 3: Update Evaluation Components

1. **Update Evaluation Components**
   - Update `EvaluationReport.tsx` to match new `interviewEvaluations` schema
   - Update `EvaluationSummary.tsx` to match new `interviewEvaluations` schema
   - Update `EvaluationList.tsx` to use pagination and field selection

2. **Create Resume Evaluation Components**
   - Create `ResumeEvaluationReport.tsx` for `resumeEvaluations` collection
   - Create `ResumeEvaluationSummary.tsx` for `resumeEvaluations` collection
   - Create `ResumeEvaluationList.tsx` with pagination and field selection

### Phase 4: Remove Direct Firestore Access

1. **Identify Direct Firestore Access**
   - Scan codebase for imports from `firebase/firestore`
   - Identify all direct Firestore queries

2. **Replace with API Calls**
   - Replace direct Firestore access in `applications.ts` with API calls
   - Replace direct Firestore access in `transcript.ts` with API calls
   - Replace direct Firestore access in `simplified-evaluations.ts` with API calls
   - Remove direct Firestore queries from components

### Phase 5: Add Multi-Tenant Support

1. **Update User Context**
   - Add companyId to user context
   - Update auth hooks to include companyId

2. **Add CompanyId to API Calls**
   - Add companyId to all API services
   - Add companyId to all API calls

3. **Update UI**
   - Update UI to show company-specific data
   - Add company selector for admin users

## Conclusion

This timeline provides a structured approach to implementing the frontend refactoring plan. By following this timeline, the team can safely migrate the frontend to the new Firebase database schema while minimizing disruption to users.
