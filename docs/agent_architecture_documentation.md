# Recruiva Agent Architecture Documentation

## Overview

This document provides a comprehensive overview of Recruiva's agent architecture, including the modular realtime API, various agent implementations (intake, job posting, email), and how they interact with the rest of the system. This documentation reflects the current implementation as of March 2025.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Modular Realtime API](#modular-realtime-api)
3. [Agent Types](#agent-types)
   - [Intake Agent](#intake-agent)
   - [Job Posting Agent](#job-posting-agent)
   - [Email Agent](#email-agent)
4. [Function Registry](#function-registry)
5. [Chat Completion Service](#chat-completion-service)
6. [Prompt Management](#prompt-management)
7. [Integration with Frontend](#integration-with-frontend)
8. [Error Handling and Logging](#error-handling-and-logging)
9. [Future Improvements](#future-improvements)

## Architecture Overview

Recruiva's agent architecture is built on a modular design that allows for easy extension and maintenance. The core components include:

- **Modular Realtime API**: Handles WebRTC connections and agent-specific logic
- **Agent Loader**: Dynamically loads agent-specific components
- **Function Registry**: Manages available functions for AI agents
- **Chat Completion Service**: Handles interactions with OpenAI's API
- **Prompt Management**: Loads and formats prompts for different agents

This architecture allows for:
- Easy addition of new agent types
- Separation of concerns between API endpoints and business logic
- Reuse of common components across different agent types
- Improved testability and maintainability

## Modular Realtime API

The Realtime API has been refactored to implement a modular, agent-based architecture. This abstraction separates agent-specific logic from the main API endpoints, allowing for easy addition of new agent types.

### Key Components

- **`realtime.py`**: Main API endpoints for session creation and call management
- **`agent_loader.py`**: Dynamically loads agent-specific components
- **Agents Directory Structure**:
  ```
  backend/app/services/realtime/
  ├── agents/
  │   ├── intake_agent/
  │   │   ├── prompts/
  │   │   │   └── intake_agent
  │   │   ├── functions.py
  │   │   ├── context.py
  │   │   └── services.py
  │   ├── interview_agent/
  │   │   ├── prompts/
  │   │   │   └── interview_agent
  │   │   ├── functions.py
  │   │   ├── context.py
  │   │   └── services.py
  │   └── common/
  │       ├── prompts/
  │       ├── functions.py
  │       ├── context.py
  │       └── services.py
  ├── agent_loader.py
  ```

### Agent Loader

The `AgentLoader` class is responsible for dynamically loading agent-specific components:

```python
class AgentLoader:
    def __init__(self, agent_type: str):
        self.agent_type = agent_type
        self.base_path = f"app.services.realtime.agents.{agent_type}"
        self.common_path = "app.services.realtime.agents.common"

    def load_module(self, module_name: str):
        try:
            return importlib.import_module(f"{self.base_path}.{module_name}")
        except ModuleNotFoundError:
            return importlib.import_module(f"{self.common_path}.{module_name}")

    def get_prompt(self, prompt_name: str):
        prompt_path = os.path.join(os.path.dirname(__file__), "agents", self.agent_type, "prompts", prompt_name)
        if not os.path.exists(prompt_path):
            prompt_path = os.path.join(os.path.dirname(__file__), "agents", "common", "prompts", prompt_name)
        with open(prompt_path, 'r') as file:
            return file.read()

    def get_functions(self):
        return self.load_module("functions")

    def get_context(self):
        return self.load_module("context")

    def get_services(self):
        return self.load_module("services")
```

### Realtime API Endpoints

The Realtime API provides two main endpoints:

1. **Session Creation**: Creates a new realtime session with OpenAI
   ```python
   @router.post("/session/{agent_type}")
   async def create_session(agent_type: str = Path()):
       # Dynamically load agent components and create session
   ```

2. **End Call**: Processes transcript and updates data
   ```python
   @router.post("/end-call")
   async def end_call(request: EndCallRequest, current_user: Dict[str, Any]):
       # Process transcript with appropriate agent
   ```

## Agent Types

### Intake Agent

The Intake Agent is responsible for conducting intake calls with hiring managers to gather comprehensive role information.

#### Components

- **Prompt**: Located at `backend/app/services/realtime/agents/intake_agent/prompts/intake_agent`
- **Functions**: Defined in `backend/app/services/realtime/agents/intake_agent/functions.py`
  - `update_role`: Updates role details
  - `end_the_call`: Signals the end of the conversation
  - `return_json_output`: Returns structured JSON output
- **Context**: Defined in `backend/app/services/realtime/agents/intake_agent/context.py`
  - Contains role levels, job types, priorities, etc.
- **Services**: Defined in `backend/app/services/realtime/agents/intake_agent/services.py`
  - `complete_transcript`: Marks a transcript as completed
  - `get_transcript`: Retrieves a transcript
  - `update_role`: Updates a role with data from a transcript
  - `process_transcript`: Processes function calls in a transcript

#### Workflow

1. User initiates an intake call
2. Realtime API creates a session with the intake agent
3. Agent conducts the conversation, gathering role information
4. When the call ends, the transcript is processed
5. Role information is extracted and stored in the database

### Job Posting Agent

The Job Posting Agent generates professional job postings based on role information and intake transcripts.

#### Implementation

The job posting generation is implemented in the `AIService` class:

```python
async def generate_job_posting(self, role, transcript):
    # Load prompt with role and transcript context
    prompt = PromptManager.load_prompt(
        "job_posting",
        context={
            "title": role["title"],
            "summary": role["summary"],
            # ... other context fields ...
        }
    )
    
    # Define functions for OpenAI API
    functions = [
        {
            "name": "generate_job_posting",
            "description": "Generate a job posting based on role information",
            "parameters": {
                # ... parameter definitions ...
            }
        }
    ]
    
    # Generate job posting using OpenAI
    response = await ChatCompletionService.generate_completion(prompt, functions=functions)
    
    # Process response (function call or direct content)
    # ... processing logic ...
    
    return {"status": "success", "job_posting": job_posting}
```

#### Workflow

1. User requests a job posting generation
2. System retrieves role and transcript data
3. Job posting agent generates content based on the data
4. Generated content is saved to the role and job posting subcollection
5. Content is returned to the user

### Email Agent

The Email Agent processes incoming emails and generates appropriate responses.

#### Implementation

The email processing is implemented in the `AIService` class:

```python
async def process_email(
    self,
    email_content: str, 
    metadata: Dict[str, Any],
    # ... other parameters ...
) -> Dict[str, Any]:
    # Process email and generate response
    # ... implementation details ...
```

#### Workflow

1. System receives an email
2. Email agent processes the content and metadata
3. Agent determines appropriate action (direct response, function call)
4. Response is generated and returned

## Function Registry

Recruiva implements two different function registry systems:

### 1. Static Function Registry

Located at `backend/app/services/openai/function_registry.py`, this registry provides static function definitions for different agent types:

```python
class FunctionRegistry:
    realtime_functions = {
        "update_role": { /* ... */ },
        "end_the_call": { /* ... */ },
        "return_json_output": { /* ... */ }
    }

    chat_functions = {
        "generate_job_posting": { /* ... */ }
    }

    @staticmethod
    def get_functions(agent_type: str):
        if agent_type == "realtime":
            return FunctionRegistry.realtime_functions
        elif agent_type == "chat":
            return FunctionRegistry.chat_functions
        else:
            return {}
```

### 2. Processor Function Registry

Located at `backend/app/services/openai/processors/function_registry.py`, this registry provides dynamic function registration and validation:

```python
class FunctionRegistry:
    def __init__(self):
        self.functions: Dict[str, Dict[str, Any]] = {}
        self.handlers: Dict[str, Callable] = {}
    
    def register(self, name: str, description: str, parameters: Dict[str, Any]):
        # Register a function definition
        
    def register_handler(self, name: str, handler: Callable):
        # Register a handler for a function
        
    def get_functions(self) -> List[Dict[str, Any]]:
        # Get all registered functions
        
    def get_handler(self, name: str) -> Callable:
        # Get handler for a function
```

## Chat Completion Service

The Chat Completion Service handles interactions with OpenAI's API:

```python
class ChatCompletionService:
    @staticmethod
    async def generate_completion(prompt: str, functions=None, model: str = "gpt-4o", **kwargs):
        """
        Generate a completion using the OpenAI API.
        """
        try:
            logging.info(f"Generating completion with model: {model}")
            
            chat_client = ChatClient()
            
            # Validate functions format
            # ... validation logic ...
            
            response = await chat_client.process_with_function_calling(
                messages=[{"role": "system", "content": prompt}],
                functions=functions,
                model=model,
                **kwargs
            )
            
            logging.info("Successfully generated completion")
            return response
        except Exception as e:
            logging.exception(f"Error generating completion: {str(e)}")
            raise
```

## Prompt Management

Prompts are managed by the `PromptManager` class:

```python
class PromptManager:
    PROMPT_DIR = os.path.join(os.path.dirname(__file__))

    @staticmethod
    def load_prompt(prompt_name: str, context: dict) -> str:
        prompt_path = os.path.join(PromptManager.PROMPT_DIR, prompt_name)
        with open(prompt_path, 'r') as file:
            prompt_template = file.read()
        return prompt_template.format(**context)
```

Prompts are stored in the following locations:
- Agent-specific prompts: `backend/app/services/realtime/agents/{agent_type}/prompts/`
- Common prompts: `backend/app/services/openai/prompts/`

## Integration with Frontend

The frontend interacts with the agent architecture through API endpoints:

- **Realtime Sessions**: `/api/v1/realtime/session/{agent_type}`
- **End Call Processing**: `/api/v1/realtime/end-call`
- **Job Posting Generation**: `/api/v1/roles/{role_id}/generate-job-posting`

The frontend components include:
- WebRTC connection management
- Transcript handling
- UI for displaying agent interactions

## Error Handling and Logging

The system implements comprehensive error handling and logging:

- **Structured Logging**: All components use structured logging
- **Exception Handling**: Specific exception types for different error scenarios
- **Fallback Mechanisms**: Fallbacks for when AI responses don't match expected formats
- **Validation**: Input and output validation at multiple levels

## Future Improvements

1. **Configuration-Driven Agents**: Allow agents to be configured via external configuration files
2. **Dynamic Agent Discovery**: Automatically discover and register new agent types at runtime
3. **Versioned Agents**: Support multiple versions of the same agent type
4. **Agent Composition**: Allow agents to be composed of smaller, reusable components
5. **Metrics and Monitoring**: Add instrumentation to track agent performance and usage
6. **Additional Agent Types**: Implement new agent types for different use cases
7. **Enhanced Error Recovery**: Improve error recovery mechanisms for more robust operation 