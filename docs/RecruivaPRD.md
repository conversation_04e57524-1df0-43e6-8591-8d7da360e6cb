# Recruiva – PRD

---

**Product Name:** Recruiva

**Version:** 2.0

**Date:** March 2025

---

## **1. Executive Summary**

Recruiva revolutionizes recruitment through AI-driven automation that spans the entire hiring process—from role definition to final selection. The platform features Realtime Multimodal Interviews that seamlessly integrate voice, text, and visual communication; Adaptive Questioning that dynamically adjusts based on candidate responses; integrated role definition with AI-powered intake conversations; AI-based evaluation of candidates through both resume analysis and interview assessments; and a comprehensive scoring system that provides objective hiring recommendations. By automating traditionally manual recruitment processes while preserving human-like conversation, <PERSON><PERSON>ru<PERSON> reduces time-to-hire by 85% and cuts costs by 55-70%, delivering a 12:1 LTV:CAC ratio through its scalable SaaS model.

---

## **2. Objectives**

1. **Automate Manual Processes** ✅ *Partially Implemented*
   Minimize repetitive tasks around job profile creation, candidate screening, and interview loops through AI-driven solutions.
2. **Accelerate Recruitment** ✅ *Partially Implemented*
   Shorten time-to-hire via automated role definition, AI-based candidate evaluation, and real-time AI-led interviews.
3. **Enhance Decision-Making** ✅ *Partially Implemented*
   Provide data-driven insights using AI-generated transcripts, scoring, and structured evaluations.
4. **Reduce Costs** ✅ *Partially Implemented*
   Automate historically manual processes to lessen reliance on human-driven interactions while improving quality.
5. **Improve Candidate Experience** ✅ *Partially Implemented*
   Ensure fewer logistical hurdles, faster feedback, and a more transparent hiring funnel with AI-driven interfaces.

---

## **3. Scope**

### **In Scope**

- **Role Intake & Job Profile Creation** ✅ *Implemented*

  - AI-powered voice conversations with hiring managers to define roles comprehensively
  - Automated role profile generation with editable fields for refinement
  - AI-based job posting creation optimized for candidate attraction
  - Role enrichment using transcripts from intake calls
- **AI-Led Interviews** ✅ *Implemented*

  - Real-time voice and video interviews using OpenAI's latest real-time models
  - Automated, adaptive questioning based on candidate responses
  - Secure recording and transcript generation
  - Interview templating for consistent evaluation
- **Interview Templates & Evaluation Criteria** ✅ *Implemented*

  - Customizable interview templates for different interview stages
  - AI-generated interview questions based on role requirements
  - Configurable evaluation criteria with scoring rubrics
  - Automated pass/fail thresholds for objective assessment
- **Candidate Experience** ✅ *Partially Implemented*

  - Public interview links for direct candidate scheduling
  - Resume upload and evaluation
  - Instant interview capabilities for qualified candidates
  - Automated feedback generation
- **Evaluation & Reporting** ✅ *Partially Implemented*

  - AI-generated scoring and evaluations based on interviews
  - Transcript analysis and highlight extraction
  - Standardized reporting across candidates
  - Export capabilities for sharing results
- **Compliance & Security** ✅ *Implemented*

  - Secure handling of recordings, transcripts, and personal data
  - Explicit consent disclaimers for recorded sessions
  - Role-based access controls for data protection
  - Data encryption for sensitive information

### **Out of Scope**

- Direct job sourcing or external advertising services
- Onboarding workflows beyond the offer stage
- Background check integration
- Employee referral management

---

## **4. Market Analysis**

- **Industry Trend**
  High-volume hiring is increasingly turning to AI to automate and speed up candidate evaluations. According to research, 65% of enterprise companies now use some form of AI in their recruitment process, though most focus on narrow applications like resume parsing rather than end-to-end automation.
- **Competitive Landscape**
  While asynchronous video or chat-based Q&A platforms exist, **real-time voice AI** plus semantic resume analysis represents a significant gap in the market. Existing solutions like HireVue, Karat, and Filtered focus on specific stages rather than the entire recruitment pipeline.
- **Opportunity**
  By merging **AI-based role definition**, **AI-led interviews**, and **standardized evaluation**, Recruiva delivers a modern, efficient, and data-informed hiring platform that reduces time-to-hire by 85% while improving decision quality.

---

## **5. Target Users & Stakeholders**

1. **Recruiters / Talent Acquisition Specialists**

   - Overloaded by resume volume and scheduling complexity
   - Seeking advanced analytics and reduced administrative burden
   - Need standardized evaluation metrics across candidates
   - Value time-saving automation for repetitive tasks
2. **Hiring Managers**

   - Tired of delayed feedback loops and inconsistent candidate data
   - Need a unified platform for clearer, data-backed decisions
   - Want to minimize time spent defining roles and interviewing candidates
   - Require objective evaluation criteria for fair assessment
3. **Candidates**

   - Frustrated with repetitive interviews and rigid schedules
   - Looking for transparent processes and quicker decisions
   - Value convenient interview scheduling and immediate feedback
   - Appreciate fair and consistent evaluation methodology
4. **HR / Leadership**

   - Concerned with rising recruitment costs and slow time-to-hire
   - Demand streamlined funnels and robust data security
   - Need analytics on hiring effectiveness and process efficiency
   - Require compliance with hiring regulations and best practices

---

## **6. User Journeys**

### **6.1 Traditional Recruitment Process (Reference)**

Below is a typical manual process showcasing the time and cost burdens:

```mermaid
graph TB
    A["Role Definition
    ~7 hours
    ~$500 cost"] --> B["Candidate Screening
    ~9-11 hours
    ~$700 cost"]
    B --> C["Round 1 Interviews
    ~10 hours
    ~$900 cost"]
    C --> D["Round 2 Interviews
    ~8 hours
    ~$600 cost"]
    D --> E["Round 3 Interviews
    ~4 hours
    ~$300 cost"]
    E --> F["Final Evaluation
    ~3-5 hours
    ~$200 cost"]
```

- **Role Definition** - Multiple stakeholder alignment sessions and document creation
- **Candidate Screening** - Manual parsing of resumes and phone screens
- **Multiple Interview Rounds** - Technical, behavioral, and management interviews
- **Evaluation** - Subjective feedback consolidation and selection decisions

### **6.2 Optimized Journey with Recruiva**

Leveraging AI to compress timelines and reduce costs:

```mermaid
graph TB
    A["AI-Led Role Intake
    ~15 min
    ~$20 cost"] --> B["Role Enrichment
    ~5 min
    ~$5 cost"]
    B --> C["Public Job Posting
    Auto
    ~$10 cost"]
    C --> D["AI Resume Screening
    ~2 min/candidate
    ~$5/candidate cost"]
    D --> E["AI Interview
    ~45-180 min
    ~$1/min cost"]
    E --> F["AI Evaluation
    ~1 min
    ~$5 cost"]
    F --> G["Hiring Decision
    ~15 min
    ~$0 cost"]
```

1. **Role Intake** ✅ *Implemented*

   - AI-led voice conversation with hiring manager
   - Automated role profile creation
   - ~1 hour vs. ~7 hours in traditional process
2. **Role Enrichment & Job Posting** ✅ *Implemented*

   - AI-based enhancement of role data from intake transcripts
   - Automated creation of compelling job descriptions
   - ~15 minutes vs. hours of copywriting and revisions
3. **Candidate Application & Screening** ✅ *Partially Implemented*

   - Public application portal with resume upload
   - AI-based resume evaluation and matching
   - Instant qualification and interview scheduling
   - Minutes vs. days of manual screening
4. **AI Interviews** ✅ *Implemented*

   - Single adaptive interview instead of multiple rounds
   - Consistent, bias-reduced questioning
   - ~45-60 minutes vs. 3-5 hours of traditional interviews
5. **Evaluation & Decision** ✅ *Partially Implemented*

   - Automated scoring and recommendation
   - Structured evaluation based on predefined criteria
   - ~15 minutes for review vs. hours of deliberation

---

## **7. Unique Value Propositions**

1. **Real-Time Conversational Voice AI** ✅ *Implemented*
   Eliminates the need for multiple human interviewers while preserving a natural conversation experience through advanced OpenAI real-time models.
2. **End-to-End Automation** ✅ *Partially Implemented*
   From role definition to final evaluation, drastically reducing manual workload across the entire hiring pipeline.
3. **Structured Evaluation Framework** ✅ *Implemented*
   Standardized assessment criteria and scoring mechanisms ensure fair, consistent candidate evaluation.
4. **Instant Interview Capability** ✅ *Implemented*
   Qualified candidates can immediately proceed to AI interviews without scheduling delays.
5. **Customizable Interview Templates** ✅ *Implemented*
   Different interview types (technical, behavioral, etc.) with customizable questions and evaluation criteria.
6. **Comprehensive Role Definition** ✅ *Implemented*
   AI-assisted creation of detailed role profiles through natural conversation.
7. **Robust Firebase Architecture** ✅ *Implemented*
   Scalable, secure data management with future extensibility for advanced features.

---

## **8. Requirements & Features**

### **8.1 Functional Requirements**

1. **Role Definition Module** ✅ *Implemented*

   1.1 Create new roles through the dashboard interface ✅ *Implemented*

   1.2 AI-led conversation (voice/video) to gather role details ✅ *Implemented*

   1.3 Auto-generate job profile (title, responsibilities, skills) in Firestore ✅ *Implemented*

   1.4 Editable UI for refining role data with real-time updates ✅ *Implemented*

   1.5 AI-based enrichment of role details from intake transcripts ✅ *Implemented*

   1.6 Automated job posting generation for public sharing ✅ *Implemented*
2. **Interview Template Management** ✅ *Implemented*

   2.1 Create and customize interview templates for different stages ✅ *Implemented*

   2.2 Configure interview questions with purpose and evaluation criteria ✅ *Implemented*

   2.3 Set scoring rubrics and pass thresholds for objective assessment ✅ *Implemented*

   2.4 AI-assisted generation of relevant interview questions ✅ *Implemented*

   2.5 AI-assisted creation of evaluation criteria ✅ *Implemented*
3. **Candidate Application & Screening** ✅ *Partially Implemented*

   3.1 Public application portal for specific roles ✅ *Implemented*

   3.2 Resume upload and parsing ✅ *Implemented*

   3.3 AI-based resume evaluation against role requirements ✅ *Implemented*

   3.4 Application tracking and management ✅ *Partially Implemented*

   3.5 Instant interview qualification for suitable candidates ✅ *Implemented*
4. **AI-Led Interviews** ✅ *Implemented*

   4.1 Real-time voice and video interviews with AI ✅ *Implemented*

   4.2 Adaptive questioning based on candidate responses ✅ *Implemented*

   4.3 Secure recording and transcription ✅ *Implemented*

   4.4 Template-based interview structure ✅ *Implemented*

   4.5 Public interview links for direct candidate scheduling ✅ *Implemented*
5. **Evaluation & Reporting** ✅ *Partially Implemented*

   5.1 AI-generated scoring based on interview responses ✅ *Partially Implemented*

   5.2 Transcript analysis and highlight extraction ✅ *Partially Implemented*

   5.3 Structured evaluation reports ✅ *Partially Implemented*

   5.4 Candidate comparison and ranking ⬜ *Not Implemented*

   5.5 Export functionality for sharing results ✅ *Partially Implemented*
6. **Dashboard & Analytics** ✅ *Partially Implemented*

   6.1 Role management dashboard ✅ *Implemented*

   6.2 Application tracking ✅ *Partially Implemented*

   6.3 Interview schedules and status ✅ *Partially Implemented*

   6.4 Hiring pipeline visualization ⬜ *Not Implemented*

   6.5 Performance metrics and analytics ⬜ *Not Implemented*
7. **Security & Compliance** ✅ *Implemented*

   7.1 Role-based access control ✅ *Implemented*

   7.2 Data encryption for sensitive information ✅ *Implemented*

   7.3 Consent management for recordings ✅ *Implemented*

   7.4 Audit trails for key actions ✅ *Partially Implemented*

   7.5 Privacy policy and terms of service ✅ *Implemented*

### **8.2 Non-Functional Requirements**

- **Scalability** ✅ *Implemented*
  Firebase architecture with ability to scale to handle thousands of interviews and applications.
- **Performance** ✅ *Implemented*
  Sub-second response times for dashboard operations; optimized real-time interviews.
- **Reliability** ✅ *Implemented*
  99.9% uptime for core services; robust error handling for AI operations.
- **Security** ✅ *Implemented*
  End-to-end encryption for interviews; secure storage of candidate data.
- **Usability** ✅ *Implemented*
  Intuitive interfaces for both recruiters and candidates; minimal friction in user flows.
- **Accessibility** ✅ *Partially Implemented*
  Support for text-based alternatives when voice interactions aren't possible.
- **Interoperability** ⬜ *Not Implemented*
  API endpoints for potential integration with other HR systems.

---

## **9. System Architecture**

### **9.1 Technology Stack**

1. **Frontend** ✅ *Implemented*

   - **Next.js 14.2.23 (React 18)** for a dynamic, reactive UI
   - **TypeScript 5** for type safety and developer productivity
   - **TailwindCSS** for responsive, utility-first styling
   - **React Query** for efficient data fetching and state management
   - **React Hook Form** with Zod for form validation
   - **Radix UI** for accessible component primitives
2. **Backend** ✅ *Implemented*

   - **Python (FastAPI)** for AI orchestration and RESTful endpoints
   - **Pydantic** for data validation and settings management
   - **Firebase Admin SDK** for server-side Firebase operations
   - **OpenAI SDK** for AI model integrations
3. **Data Storage** ✅ *Implemented*

   - **Firebase (Cloud Firestore)** for core application data
   - **Firebase Storage** for media files (recordings, resumes)
   - **Firebase Authentication** for user management
4. **AI & Communication** ✅ *Implemented*

   - **OpenAI GPT-4o** for conversational AI and content generation
   - **OpenAI real-time models** for interactive voice interviews
   - **WebRTC** for real-time audio/video communication
5. **Deployment & Infrastructure** ✅ *Implemented*

   - **Vercel** for frontend hosting
   - **Render** for backend API hosting
   - **Firebase** for database and authentication

### **9.2 System Architecture Diagram**

```mermaid
flowchart TD
    subgraph "Frontend (Next.js)"
        A1[Authentication] --> A2[Role Management]
        A2 --> A3[Interview Templates]
        A2 --> A4[Applications]
        A3 --> A5[Real-time AI Interviews]
        A4 --> A5
    end
  
    subgraph "Backend (FastAPI)"
        B1[Auth Service] --> B2[Roles Service]
        B1 --> B3[Realtime Service]
        B2 --> B4[Template Service]
        B3 --> B5[AI Service]
        B4 --> B5
    end
  
    subgraph "Data Layer"
        C1[Firebase Auth] --> C2[Firestore]
        C2 --> C3[Firebase Storage]
    end
  
    subgraph "AI/ML Services"
        D1[OpenAI Models] --> D2[Realtime API]
        D1 --> D3[Completion API]
    end
  
    A1 <--> B1
    A2 <--> B2
    A3 <--> B4
    A5 <--> B3
    B1 <--> C1
    B2 <--> C2
    B3 <--> C3
    B5 <--> D1
    B3 <--> D2
    B5 <--> D3
```

---

## **10. Detailed User Stories**

### **Hiring Manager Journey**

1. **Role Creation** ✅ *Implemented*

   - **As a** Hiring Manager,
   - **I want** to quickly define a new role in the system,
   - **So that** I can start the hiring process efficiently.
   - **Acceptance Criteria**: Can create basic role from dashboard with key details.
2. **AI-Led Role Intake** ✅ *Implemented*

   - **As a** Hiring Manager,
   - **I want** to have a natural conversation with AI to define the role in detail,
   - **So that** I can provide comprehensive requirements without filling out forms.
   - **Acceptance Criteria**: AI captures detailed role information through voice conversation.
3. **Role Refinement** ✅ *Implemented*

   - **As a** Hiring Manager,
   - **I want** to review and edit the AI-generated role profile,
   - **So that** I can ensure all details are accurate before publishing.
   - **Acceptance Criteria**: Editable interface for all role attributes with save functionality.
4. **Interview Template Review** ✅ *Implemented*

   - **As a** Hiring Manager,
   - **I want** to review interview templates and evaluation criteria,
   - **So that** I can ensure candidates are assessed appropriately.
   - **Acceptance Criteria**: Can view and adjust interview questions and scoring criteria.
5. **Candidate Evaluation** ✅ *Partially Implemented*

   - **As a** Hiring Manager,
   - **I want** to review candidate evaluations and interview highlights,
   - **So that** I can make informed hiring decisions efficiently.
   - **Acceptance Criteria**: Dashboard showing candidate scores, evaluations, and interview highlights.

### **Recruiter Journey**

6. **Role Management** ✅ *Implemented*

   - **As a** Recruiter,
   - **I want** to manage multiple roles in the hiring pipeline,
   - **So that** I can track progress and prioritize effectively.
   - **Acceptance Criteria**: Dashboard showing all roles with status and key metrics.
7. **Interview Template Creation** ✅ *Implemented*

   - **As a** Recruiter,
   - **I want** to create and customize interview templates,
   - **So that** I can standardize the evaluation process across candidates.
   - **Acceptance Criteria**: Template editor with question management and evaluation criteria.
8. **Application Review** ✅ *Partially Implemented*

   - **As a** Recruiter,
   - **I want** to review all applications for a role,
   - **So that** I can track candidate progress and evaluations.
   - **Acceptance Criteria**: List view of all applications with status and evaluation scores.
9. **Candidate Communication** ⬜ *Not Implemented*

   - **As a** Recruiter,
   - **I want** to communicate with candidates directly from the platform,
   - **So that** I can manage the relationship efficiently.
   - **Acceptance Criteria**: Messaging interface integrated with notification system.

### **Candidate Journey**

10. **Role Application** ✅ *Implemented*

    - **As a** Candidate,
    - **I want** to apply for a role with minimal friction,
    - **So that** I can express interest quickly and efficiently.
    - **Acceptance Criteria**: Public application form with resume upload functionality.
11. **AI Interview Participation** ✅ *Implemented*

    - **As a** Candidate,
    - **I want** to participate in an AI-led interview at my convenience,
    - **So that** I can showcase my skills without scheduling constraints.
    - **Acceptance Criteria**: User-friendly interview interface with clear instructions.
12. **Application Status** ⬜ *Not Implemented*

    - **As a** Candidate,
    - **I want** to check the status of my application,
    - **So that** I can stay informed about my progress.
    - **Acceptance Criteria**: Candidate portal showing application status and next steps.

---

## **11. Implementation Status**

### **11.1 Implemented Features**

- ✅ Role creation and management dashboard
- ✅ AI-led intake conversations
- ✅ Role enrichment from intake transcripts
- ✅ Automated job posting generation
- ✅ Interview template creation and management
- ✅ AI-assisted question and criteria generation
- ✅ Real-time AI interviews with video/voice
- ✅ Public application portal for candidates
- ✅ Resume evaluation for candidate screening
- ✅ Instant interview capability for qualified candidates
- ✅ Secure authentication and authorization
- ✅ Basic application tracking

### **11.2 Partially Implemented Features**

- ⚠️ Candidate evaluation and scoring (basic implementation)
- ⚠️ Application management and filtering
- ⚠️ Transcript analysis and highlight extraction
- ⚠️ Export functionality for evaluations
- ⚠️ Audit logging for compliance

### **11.3 Planned Features**

- ⬜ Advanced analytics dashboard
- ⬜ Candidate relationship management
- ⬜ Semantic search for transcripts
- ⬜ Enhanced candidate portal with status tracking
- ⬜ Integration APIs for third-party systems
- ⬜ Multi-lingual interview support
- ⬜ Bias detection and mitigation
- ⬜ Advanced reporting and analytics

---

## **12. Product Roadmap**

### **Release 1.0: MVP (Current)** ✅ *Implemented*

- Role definition and management
- AI-led intake conversations
- Basic interview templates
- Real-time AI interviews
- Public application portal
- Resume evaluation
- Basic application tracking

### **Release 2.0: Enhanced Evaluation**

- Advanced candidate scoring
- Comprehensive evaluation reports
- Transcript analysis and highlights
- Interview insights and recommendations
- Enhanced application management
- Basic analytics dashboard

### **Release 3.0: Enterprise Features**

- Team collaboration tools
- Advanced analytics and reporting
- Integration APIs
- SSO and enterprise security
- Custom branding options
- Audit and compliance enhancements

### **Release 4.0: Advanced Intelligence**

- Semantic search for transcripts
- Multi-lingual interview support
- Bias detection and mitigation
- Predictive candidate success models
- Advanced customization options
- AI-driven hiring recommendations

---

## **13. Success Metrics**

### **13.1 Business Metrics**

- **Time-to-Hire Reduction**: Target 85% improvement over traditional methods
- **Cost-per-Hire Reduction**: Target 55-70% cost savings
- **User Adoption Rate**: Target 80% of recruiters reporting improved efficiency
- **Conversion Rate**: Target 30% of trials converting to paid subscriptions

### **13.2 Product Metrics**

- **Interview Completion Rate**: Target 90% of started interviews completed
- **User Retention**: Target 85% monthly active user retention
- **Feature Adoption**: Target 70% of users utilizing all core features
- **System Reliability**: Target 99.9% uptime for all critical services

### **13.3 User Experience Metrics**

- **Candidate Satisfaction**: Target 4.5/5 average rating in post-interview surveys
- **Recruiter Satisfaction**: Target 4.3/5 average satisfaction score
- **Onboarding Completion**: Target 95% of users completing onboarding
- **Support Ticket Volume**: Target fewer than 1 ticket per 100 active users

---

## **14. Key Risks & Mitigations**

1. **AI Reliability and Quality**

   - **Risk**: Inconsistent AI performance in interviews or evaluations
   - **Mitigation**: Continuous model training, prompt engineering, and quality monitoring
2. **User Adoption Barriers**

   - **Risk**: Resistance to AI-led recruitment processes
   - **Mitigation**: Gradual feature introduction, clear ROI demonstration, and user education
3. **Technical Performance**

   - **Risk**: Scalability challenges with increased usage
   - **Mitigation**: Cloud infrastructure with auto-scaling; performance monitoring
4. **Data Privacy & Security**

   - **Risk**: Candidate data protection concerns
   - **Mitigation**: Robust encryption, access controls, and compliance with regulations
5. **Integration Complexity**

   - **Risk**: Challenges integrating with existing HR systems
   - **Mitigation**: Well-documented APIs and dedicated integration support

---

## **15. Conclusion**

Recruiva redefines the recruitment process through comprehensive AI automation across the entire hiring pipeline. By addressing the key pain points of traditional recruitment—time consumption, inconsistency, and high costs—Recruiva delivers a solution that benefits all stakeholders in the hiring process.

With the core platform already implemented and delivering value, Recruiva is positioned to expand its capabilities through strategic feature releases that will further enhance the recruitment experience. The focus on real-time conversational AI, standardized evaluation, and end-to-end automation provides a unique value proposition in the market that addresses genuine needs of hiring managers, recruiters, and candidates alike.

By continuing to evolve based on user feedback and market demands, Recruiva will maintain its position at the forefront of recruitment innovation, helping organizations build better teams more efficiently and with greater confidence in their hiring decisions.

---
