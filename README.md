# Recruiva - AI-Driven Recruitment Platform

## Overview

Recruiva is an AI-driven recruitment platform that automates key hiring steps—from defining roles to conducting real-time AI-led interviews—while generating actionable evaluation reports. By leveraging conversational voice AI and advanced NLP, <PERSON><PERSON><PERSON><PERSON> cuts time-to-hire, improves candidate experiences, and standardizes decision-making.

## High-Level Architecture

### Frontend (Next.js + React)

- **Framework**: Next.js 14.2.23 with React 18
- **Language**: TypeScript 5
- **Styling**: TailwindCSS 3.3.0
- **State Management**: React Query 5.17.19
- **UI Components**: Radix UI Components
- **Forms**: React Hook Form 7.49.3 with Zod validation
- **Authentication**: Firebase Auth

### Backend (FastAPI + Python)

- **Framework**: FastAPI 0.103.2
- **Language**: Python 3.11.0
- **Database**: Firebase/Firestore
- **Storage**: Google Cloud Storage
- **AI Integration**: OpenAI API (GPT-4o, GPT-4o-mini, GPT-4o-mini-realtime)
- **Authentication**: Firebase Admin SDK
- **Email Processing**: IMAP/SMTP with AI-powered responses

### Infrastructure

- **Frontend Hosting**: Vercel
- **Backend Hosting**: Render with Docker
- **Database**: Firebase/Firestore
- **Real-time Communication**: WebRTC
- **Email Communication**: Gmail SMTP/IMAP

## Project Structure

```
recruiva/
├── frontend/                 # Next.js frontend application
│   ├── src/
│   │   ├── app/              # Next.js app router pages
│   │   │   ├── auth/         # Authentication pages
│   │   │   ├── dashboard/    # Dashboard views
│   │   │   ├── roles/        # Role management pages
│   │   │   ├── video-call/   # Video call interface
│   │   │   ├── settings/     # User settings
│   │   │   ├── profile/      # User profile
│   │   │   ├── instant-interview/ # Instant interview feature
│   │   │   └── ...           # Other pages
│   │   ├── components/       # React components
│   │   ├── lib/              # Utility functions and configs
│   │   ├── hooks/            # Custom React hooks
│   │   ├── types/            # TypeScript type definitions
│   │   └── styles/           # Global styles and Tailwind config
│   └── public/               # Static assets
│
├── backend/                  # FastAPI backend application
│   ├── app/
│   │   ├── api/             # API endpoints
│   │   │   └── v1/          # API version 1
│   │   │       └── endpoints/  # API endpoint implementations
│   │   │           ├── roles.py            # Role management endpoints
│   │   │           ├── realtime.py         # Realtime interview endpoints
│   │   │           ├── chat_completion.py  # Chat completion endpoints
│   │   │           ├── templates.py        # Template management endpoints
│   │   │           └── resume_evaluation.py # Resume evaluation endpoints
│   │   ├── core/            # Core application code
│   │   ├── services/        # Service layer
│   │   │   ├── ai_service.py        # AI service implementation
│   │   │   ├── email_service.py     # Email service implementation
│   │   │   ├── roles_service.py     # Roles service implementation
│   │   │   ├── firebase_service.py  # Firebase service implementation
│   │   │   ├── auth_service.py      # Auth service implementation
│   │   │   ├── openai/              # OpenAI integration
│   │   │   │   ├── models/          # OpenAI model implementations
│   │   │   │   ├── prompts/         # System prompts
│   │   │   │   └── function_registry.py  # Function registry
│   │   │   └── realtime/            # Realtime agent architecture
│   │   └── utils/           # Utility functions
│   └── tests/               # Test suite
│
├── firebase/                # Firebase configuration
├── .docs/                   # Project documentation
└── .plans/                  # Project planning documents
```

## Key Features

### Role Management

- Create and manage job roles via web interface or email
- Parse job descriptions using AI
- Record and transcribe intake calls
- AI-powered role enrichment from intake transcripts

### Email Integration

- AI-powered email processing for role management
- Natural language understanding of email requests
- Automated role creation and updates via email
- Rich, formatted email responses with Markdown
- Secure sender validation against Firebase users

### AI Function Calling

- OpenAI function calling for structured operations
- Email agent with specialized system prompt
- Automatic intent detection from user messages
- Parameter extraction and validation
- Function execution with proper error handling

### Real-time AI Interviews

- WebRTC-based video interviews
- Real-time AI evaluation
- Automated scoring system
- Interview templates and customization

### Resume Evaluation

- AI-powered resume analysis
- Skills matching with job requirements
- Candidate ranking and scoring
- Structured feedback generation

### Authentication & Security

- Firebase Authentication
- Role-based access control
- Secure file storage
- API key management

## API Endpoints

### Roles API (/api/v1/roles)

- POST / - Create role
- GET / - List roles
- GET /{role_id} - Get role details
- PUT /{role_id} - Update role
- DELETE /{role_id} - Delete role
- PATCH /{role_id}/status - Update status
- PATCH /{role_id}/priority - Update priority
- POST /{role_id}/intake-recording - Upload recording
- POST /parse-job-description - Parse description
- POST /migrate-to-team-structure - Team migration
- GET /{role_id}/intake-transcripts - Get transcripts
- POST /{role_id}/intake-transcripts - Create transcript
- PUT /{role_id}/intake-transcripts/{transcript_id} - Update transcript
- PATCH /{role_id}/intake-transcripts/{transcript_id}/complete - Complete transcript
- POST /{role_id}/intake-calls - Create intake call
- GET /{role_id}/intake-calls - List intake calls

### Realtime API (/api/v1/realtime)

- POST /session - Create realtime session

### Templates API (/api/v1/templates)

- POST / - Create template
- GET / - List templates
- GET /{template_id} - Get template details
- PUT /{template_id} - Update template
- DELETE /{template_id} - Delete template

### Resume Evaluation API (/api/v1/resume-evaluation)

- POST / - Evaluate resume
- GET /{evaluation_id} - Get evaluation results

### Chat Completion API (/api/v1/chat-completion)

- POST / - Get chat completion response

## Development Setup

### Prerequisites

- Node.js >= 22.0.0
- Python 3.11.0
- Docker (for backend development)
- Firebase project
- OpenAI API key
- Gmail account for email integration

### Frontend Setup

1. Clone the repository
   ```bash
   git clone https://github.com/yourusername/recruiva.git
   cd recruiva/frontend
   ```

2. Install dependencies
   ```bash
   npm install
   ```

3. Set up environment variables
   Create a `.env.local` file with the following variables:
   ```
   # API Configuration
   NEXT_PUBLIC_API_URL=http://localhost:8000

   # Firebase Configuration
   NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-auth-domain
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-storage-bucket
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
   NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
   NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id
   ```

4. Start the development server
   ```bash
   npm run dev
   ```

### Backend Setup

1. Navigate to the backend directory
   ```bash
   cd recruiva/backend
   ```

2. Create a virtual environment
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies
   ```bash
   pip install -r requirements.txt
   ```

4. Set up environment variables
   Create a `.env` file with the following variables:
   ```
   # Firebase Configuration
   FIREBASE_PROJECT_ID=your-project-id
   FIREBASE_PRIVATE_KEY_ID=your-private-key-id
   FIREBASE_PRIVATE_KEY="your-private-key"
   FIREBASE_CLIENT_EMAIL=your-client-email
   FIREBASE_CLIENT_ID=your-client-id
   FIREBASE_STORAGE_BUCKET=your-storage-bucket

   # OpenAI Configuration
   OPENAI_API_KEY=your-openai-api-key

   # Email Configuration
   EMAIL_USERNAME=<EMAIL>
   EMAIL_PASSWORD=your-email-password
   EMAIL_FROM="Recruiva AI"
   ```

5. Start the development server
   ```bash
   uvicorn app.main:app --reload
   ```

## OpenAI Integration

The platform uses the latest OpenAI models, including:
- GPT-4o - For comprehensive role parsing and analysis
- GPT-4o-mini - For lighter workloads and faster responses
- GPT-4o-mini-realtime-preview - For real-time interview scenarios

Default model for realtime applications:
```
"model": "gpt-4o-mini-realtime-preview-2024-12-17"
```

## Deployment

- Frontend: Deployed on Vercel (production branch: main)
- Backend: Deployed on Render using Docker (production branch: main)
- Environment variables managed via respective dashboards
