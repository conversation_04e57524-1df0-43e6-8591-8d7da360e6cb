const nextJest = require('next/jest');

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './',
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapper: {
    // Handle module aliases (this will be automatically configured for you soon)
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
  testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/.next/'],
  transformIgnorePatterns: [
    '/node_modules/(?!react-markdown|rehype-raw|rehype-sanitize|remark-parse|remark-rehype|unified|micromark|decode-named-character-reference|character-entities|property-information|hast-util-whitespace|space-separated-tokens|comma-separated-tokens|mdast-util-to-string|mdast-util-to-hast|unist-util-stringify-position|unist-util-position|unist-util-visit|unist-util-visit-parents|unist-util-is|bail|trough|vfile|vfile-message|mdast-util-from-markdown|mdast-util-to-string|micromark-util-chunked|micromark-util-classify-character|micromark-util-resolve-all|micromark-util-character|micromark-factory-space|micromark-factory-whitespace|micromark-core-commonmark|mdast-util-definitions|hast-util-sanitize|hast-util-from-parse5|hast-util-to-string|hast-util-parse-selector|hastscript|web-namespaces|zwitch|hast-util-is-element|hast-util-raw|parse5|html-void-elements|ccount|escape-string-regexp|is-plain-obj)',
  ],
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);
