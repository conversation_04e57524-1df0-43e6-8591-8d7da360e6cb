{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "react-hooks/exhaustive-deps": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-empty-object-type": "off", "@typescript-eslint/no-namespace": "off", "@typescript-eslint/no-require-imports": "off", "prefer-const": "error"}, "ignorePatterns": ["**/dist/**"]}