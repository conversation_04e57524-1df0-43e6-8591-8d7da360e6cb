# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
# Dependencies
node_modules/
.pnp/
.pnp.js

# Testing
coverage/
.nyc_output/

# Next.js
.next/
out/
build/
dist/

# Production
build/

# Logs
logs
*.log
npm-debug.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# debug
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
.env.development
.env.production
.env.*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts 

# Firebase
.firebase/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store


