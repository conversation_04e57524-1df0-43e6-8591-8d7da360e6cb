rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Base rules
    match /{document=**} {
      allow read, write: if false;
    }
    
    // Users collection
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      
      // Roles subcollection
      match /roles/{roleId} {
        allow read: if request.auth != null;
        allow write: if request.auth != null;
        
        // Intake transcripts subcollection
        match /intakeTranscripts/{transcriptId} {
          allow read: if request.auth != null;
          allow create: if request.auth != null;
          allow update: if request.auth != null;
          allow delete: if request.auth != null;
        }
        
        // Intake calls subcollection
        match /intakeCalls/{callId} {
          allow read: if request.auth != null;
          allow create: if request.auth != null;
          allow update: if request.auth != null;
          allow delete: if request.auth != null;
        }
        
        // Job postings subcollection
        match /jobPostings/{postingId} {
          allow read: if request.auth != null;
          allow create: if request.auth != null;
          allow update: if request.auth != null;
          allow delete: if request.auth != null;
        }
      }
    }
  }
} 