# Recruiva Frontend

A modern, scalable frontend application built with Next.js 14 for the Recruiva AI-driven recruitment platform.

## 🏗 Architecture Overview

### Tech Stack

- **Framework**: Next.js 14.2.23 with App Router
- **Language**: TypeScript 5
- **Styling**: TailwindCSS 3.3.0 with custom UI components
- **State Management**: React Query 5.17.19
- **Authentication**: Firebase Auth 11.1.0
- **Form Handling**: React Hook Form 7.49.3 with Zod validation 3.22.4
- **UI Components**: Radix UI (various v1.x and v2.x)
- **Charting**: Recharts 2.15.1
- **Animation**: Framer Motion 12.5.0

### Directory Structure

The `src/` directory contains the core application code. For a detailed overview, please refer to [src/README.md](src/README.md).

```
src/
├── app/                    # Next.js 14 App Router pages and layouts
│   ├── auth/              # Authentication related pages
│   ├── dashboard/         # Dashboard views
│   ├── roles/             # Role management pages
│   ├── settings/          # User settings
│   ├── profile/           # User profile
│   ├── video-call/        # Video call interface
│   ├── instant-interview/ # Instant interview feature
│   ├── competitors/       # Competitor analysis
│   ├── financials/        # Financial information
│   ├── pitch/             # Pitch deck
│   ├── contact-sales/     # Sales contact
│   └── jobs/              # Job listings
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   ├── layout/           # Layout components
│   ├── role/             # Role-specific components
│   ├── features/         # Feature-specific components
│   └── video-call/       # Video call components
├── contexts/             # React contexts
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions and shared code
│   ├── api/              # API client configuration
│   ├── firebase/         # Firebase configuration and utilities
│   └── utils/            # General utilities
├── providers/            # React providers
├── services/             # API service layers
├── styles/               # Global styles and Tailwind configuration
└── types/                # TypeScript type definitions
```

## 🔑 Key Features

### Authentication

- Firebase Authentication integration
- Protected routes and middleware
- Social authentication (Google)
- Session management

### Role Management

- Create and manage job roles
- Role status tracking
- Priority management
- Intake recording functionality
- Job description parsing

### Video Call Integration

- Real-time video communication
- WebRTC integration
- Call recording capabilities
- AI-powered interview agent
- Real-time transcription

### Instant Interview

- Quick interview setup
- Template-based questions
- AI-powered evaluation
- Candidate scoring

### Resume Evaluation

- AI-powered resume analysis
- Skills matching and scoring
- Candidate ranking
- Detailed feedback generation

### UI/UX

- Responsive design
- Dark/light mode support
- Modern component library
- Toast notifications
- Form validation
- Loading states
- Interactive charts and visualizations

## 🛠 Development Setup

### Prerequisites

- Node.js >=22.0.0
- npm (latest version)

### Environment Variables

Required environment variables (see `.env.example`):

```bash
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
# ... (other Firebase config)
```

### Installation

```bash
# Install dependencies
npm install

# Run development server
npm run dev
```

## 📦 Key Dependencies

### Core

- next: ^14.2.23
- react: ^18
- typescript: ^5

### State Management & Data Fetching

- @tanstack/react-query: ^5.17.19
- @tanstack/react-query-devtools: ^5.64.1

### UI Components

- @radix-ui/* (various components)
- @heroicons/react: ^2.2.0
- class-variance-authority: ^0.7.1
- tailwind-merge: ^2.6.0
- tailwindcss-animate: ^1.0.7
- lucide-react: ^0.312.0
- framer-motion: ^12.5.0

### Forms & Validation

- react-hook-form: ^7.49.3
- @hookform/resolvers: ^3.3.4
- zod: ^3.22.4

### Date Handling

- date-fns: ^3.2.0
- react-datepicker: ^7.6.0
- react-day-picker: ^9.5.0

### Firebase Integration

- firebase: ^11.1.0
- @firebase/auth: ^1.8.1
- firebase-admin: ^13.0.2

### Visualization

- recharts: ^2.15.1
- @tsparticles/react: ^3.0.0
- @tsparticles/slim: ^3.8.1

### HTTP Client

- axios: ^1.7.9

### Utilities

- uuid: ^11.1.0
- next-themes: ^0.4.4
- sonner: ^1.7.2
- react-markdown: ^9.1.0
- react-phone-number-input: ^3.4.12

## 🔄 State Management

### React Query

- Centralized data fetching
- Cache management
- Optimistic updates
- Real-time synchronization

### Context API

Used for:

- Theme management
- Authentication state
- Global UI state

## 🔒 Security

- Protected API routes
- Firebase Authentication
- Secure token management
- HTTPS enforcement
- Environment variable protection

## 🚀 Deployment

The application is deployed on Vercel with the following configuration:

- Production branch: main
- Preview deployments: PR branches
- Environment variables managed via Vercel dashboard
- Configured for Node.js 22+ runtime

## 📝 Code Style & Quality

### TypeScript

- Strict mode enabled
- Comprehensive type definitions
- Interface-first approach

### ESLint Configuration

- Strict rules for consistent code style
- Import sorting
- React hooks rules
- TypeScript-specific rules

### Best Practices

- Component composition
- Custom hooks for reusable logic
- Proper error handling
- Performance optimization

## 🧪 Testing

- Jest for unit testing
- React Testing Library for component testing
- Cypress for E2E testing (planned)

## 🔄 CI/CD

- Automated builds on Vercel
- ESLint checks
- Type checking
- Test execution

## 📚 Documentation

- Component documentation
- API integration docs
- Type definitions
- Storybook (planned)
