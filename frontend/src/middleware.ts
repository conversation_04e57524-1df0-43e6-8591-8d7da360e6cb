import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// List of public paths that don't require authentication
const publicPaths = [
    '/',
    '/auth/signin',
    '/auth/register',
    '/auth/forgot-password',
    '/contact-sales',
    '/jobs',  // Allow access to job board
    '/instant-interview',  // Allow access to instant interview pages
    '/api/auth',  // Allow Firebase auth endpoints
    '/api/v1/public',  // Allow public API endpoints
    '/pitch',  // Allow access to pitch deck
];

// API paths that should be publicly accessible
const publicApiPaths = [
    '/api/v1/public/jobs',  // Public role data
    '/api/v1/realtime/public-interview-session',  // Public interview session creation
    '/api/v1/realtime/update-public-transcript',  // Public transcript updates
];

export function middleware(request: NextRequest) {
    const { pathname, search } = request.nextUrl;
    
    // Allow RSC requests
    if (search?.includes('_rsc=') || search?.includes('_next')) {
        return NextResponse.next();
    }

    // Check if the path is a public API path
    const isPublicApiPath = publicApiPaths.some(path => 
        pathname.startsWith(path)
    );
    
    if (isPublicApiPath) {
        console.log(`Allowing public API access to: ${pathname}`);
        return NextResponse.next();
    }

    // Check if the path starts with any of the public paths
    const isPublicPath = publicPaths.some(path => 
        pathname === path || pathname.startsWith(`${path}/`)
    );

    // Allow access to public paths and static files
    if (isPublicPath || 
        pathname.startsWith('/_next/') || 
        pathname.startsWith('/api/') ||
        pathname.includes('.')) {
        return NextResponse.next();
    }

    // For non-public paths, check for authentication
    // This is where you would add your authentication logic
    // For now, we'll just allow all requests
    return NextResponse.next();
}

export const config = {
    matcher: [
        /*
         * Match all request paths except:
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - public folder
         */
        '/((?!_next/static|_next/image|favicon.ico|public).*)',
    ],
}; 