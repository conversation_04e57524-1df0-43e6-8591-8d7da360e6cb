/* Dark theme styles for react-datepicker */
.react-datepicker {
  @apply bg-background border border-input text-foreground shadow-lg;
}

.react-datepicker__header {
  @apply bg-muted border-b border-input;
}

.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker-year-header {
  @apply text-foreground;
}

.react-datepicker__day-name {
  @apply text-muted-foreground;
}

.react-datepicker__day {
  @apply text-foreground hover:bg-muted;
}

.react-datepicker__day--selected {
  @apply bg-primary text-primary-foreground hover:bg-primary/90;
}

.react-datepicker__day--keyboard-selected {
  @apply bg-primary/50 text-primary-foreground hover:bg-primary/90;
}

.react-datepicker__day--outside-month {
  @apply text-muted-foreground;
}

.react-datepicker__day--disabled {
  @apply text-muted-foreground/50 hover:bg-transparent cursor-not-allowed;
}

.react-datepicker__navigation-icon::before {
  @apply border-muted-foreground;
}

.react-datepicker__navigation:hover *::before {
  @apply border-foreground;
}

/* Triangle indicator */
.react-datepicker__triangle {
  @apply before:border-b-background after:border-b-background;
}

/* Today's date highlight */
.react-datepicker__day--today {
  @apply font-bold text-primary;
}

/* Weekend dates */
.react-datepicker__day--weekend {
  @apply text-muted-foreground;
}

/* Month dropdown */
.react-datepicker__month-dropdown {
  @apply bg-background border border-input;
}

.react-datepicker__month-option {
  @apply text-foreground hover:bg-muted;
}

/* Year dropdown */
.react-datepicker__year-dropdown {
  @apply bg-background border border-input;
}

.react-datepicker__year-option {
  @apply text-foreground hover:bg-muted;
}

/* Time container */
.react-datepicker__time-container {
  @apply border-l border-input;
}

.react-datepicker__time {
  @apply bg-background;
}

.react-datepicker__time-box {
  @apply border-r border-input;
}

.react-datepicker__time-list-item {
  @apply text-foreground hover:bg-muted;
}

.react-datepicker__time-list-item--selected {
  @apply bg-primary text-primary-foreground hover:bg-primary/90;
}
