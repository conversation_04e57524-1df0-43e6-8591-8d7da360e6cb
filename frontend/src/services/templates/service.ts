import { templatesA<PERSON>, InterviewTemplate, CreateTemplateRequest, UpdateTemplateRequest, TemplateStatus, Question, QuestionCreate, QuestionUpdate, Criterion, CriterionCreate, CriterionUpdate, CriterionType, ScoreCardCriterion } from './api';
import { ServiceError } from '@/lib/utils/errors';

/**
 * TemplatesService provides a centralized service layer for managing interview templates
 * with consistent error handling, data transformation, and caching capabilities.
 */
class TemplatesService {
  private templateCache: Map<string, InterviewTemplate> = new Map();
  // Track pending requests to prevent duplicates
  private pendingRequests: Map<string, Promise<any>> = new Map();

  /**
   * Get a template by ID with caching
   * @param roleId The role ID
   * @param templateId The template ID
   * @returns The template or null if not found
   */
  async getTemplate(roleId: string, templateId: string): Promise<InterviewTemplate | null> {
    try {
      console.log('TemplatesService: Getting template with ID:', templateId, 'for role:', roleId);

      // Create a composite key for caching and request tracking
      const cacheKey = `${roleId}:${templateId}`;

      // Check cache first
      if (this.templateCache.has(cacheKey)) {
        console.log('TemplatesService: Found template in cache');
        return this.templateCache.get(cacheKey)!;
      }

      // Check if we already have a pending request for this template
      if (this.pendingRequests.has(cacheKey)) {
        console.log('TemplatesService: Request already in progress, reusing promise');
        return this.pendingRequests.get(cacheKey);
      }

      console.log('TemplatesService: Fetching template from API');
      
      // Create the promise and store it
      const fetchPromise = templatesApi.getTemplate(roleId, templateId)
        .then(template => {
          if (template) {
            console.log('TemplatesService: Template fetched successfully:', template);
            this.templateCache.set(cacheKey, template);
          } else {
            console.error('TemplatesService: Template not found for ID:', templateId);
          }
          // Remove from pending requests when done
          this.pendingRequests.delete(cacheKey);
          return template;
        })
        .catch(error => {
          console.error('TemplatesService: Failed to fetch template:', error);
          // Remove from pending requests on error
          this.pendingRequests.delete(cacheKey);
          throw error;
        });
      
      // Store the promise
      this.pendingRequests.set(cacheKey, fetchPromise);
      
      return fetchPromise;
    } catch (error) {
      console.error('TemplatesService: Failed to fetch template:', error);
      throw new ServiceError('Failed to fetch template', error);
    }
  }

  /**
   * Get all templates for a role
   * @param roleId The role ID
   * @returns List of templates
   */
  async getTemplates(roleId: string): Promise<InterviewTemplate[]> {
    try {
      console.log('TemplatesService: Getting templates for role:', roleId);
      
      // Check if we already have a pending request for this role's templates
      const requestKey = `list:${roleId}`;
      if (this.pendingRequests.has(requestKey)) {
        console.log('TemplatesService: Templates request already in progress, reusing promise');
        return this.pendingRequests.get(requestKey);
      }

      // Create the promise and store it
      const fetchPromise = templatesApi.listTemplates(roleId)
        .then(templates => {
          // Ensure templates is an array before using forEach
          if (Array.isArray(templates)) {
            console.log(`TemplatesService: Successfully fetched ${templates.length} templates`);
            // Cache each template
            templates.forEach(template => {
              const cacheKey = `${roleId}:${template.id}`;
              this.templateCache.set(cacheKey, template);
            });
          } else {
            console.warn('TemplatesService: Templates is not an array:', templates);
          }
          
          // Remove from pending requests when done
          this.pendingRequests.delete(requestKey);
          return Array.isArray(templates) ? templates : [];
        })
        .catch(error => {
          console.error('TemplatesService: Failed to fetch templates:', error);
          // Remove from pending requests on error
          this.pendingRequests.delete(requestKey);
          return [];
        });
      
      // Store the promise
      this.pendingRequests.set(requestKey, fetchPromise);
      
      return fetchPromise;
    } catch (error) {
      console.error('TemplatesService: Failed to fetch templates:', error);
      return [];
    }
  }

  /**
   * Create a new template
   * @param roleId The role ID
   * @param data The template data
   * @returns The created template
   */
  async createTemplate(roleId: string, data: CreateTemplateRequest): Promise<InterviewTemplate> {
    try {
      console.log('TemplatesService: Creating template for role:', roleId);

      // Validate required fields
      if (!data.stage) {
        throw new Error('Stage is required');
      }

      if (!data.duration) {
        throw new Error('Duration is required');
      }

      // Create a copy of the data to avoid modifying the original
      const processedData = { ...data };

      // Set default values for optional fields if missing
      const finalData = {
        ...processedData,
        status: processedData.status || TemplateStatus.DRAFT,
        customInstructions: processedData.customInstructions || '',
        questions: processedData.questions || [],
        evaluationCriteria: processedData.evaluationCriteria || []
      };

      const template = await templatesApi.createTemplate(roleId, finalData);

      if (!template || !template.id) {
        throw new Error('Invalid response from create template API');
      }

      // Cache the template
      const cacheKey = `${roleId}:${template.id}`;
      this.templateCache.set(cacheKey, template);

      return template;
    } catch (error) {
      console.error('TemplatesService: Failed to create template:', error);

      // Enhance error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('422')) {
          throw new ServiceError('Invalid template data. Please check all required fields.', error);
        }
        if (error.message.includes('401') || error.message.includes('403')) {
          throw new ServiceError('Authentication error. Please sign in again.', error);
        }
      }

      throw new ServiceError('Failed to create template', error);
    }
  }

  /**
   * Update a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param data The template data to update
   * @returns The updated template
   */
  async updateTemplate(roleId: string, templateId: string, data: UpdateTemplateRequest): Promise<InterviewTemplate> {
    try {
      console.log('TemplatesService: Updating template:', templateId, 'for role:', roleId);

      const template = await templatesApi.updateTemplate(roleId, templateId, data);

      // Validate the response
      if (!template || !template.id) {
        throw new Error('Invalid response from update template API');
      }

      // Update the cache
      const cacheKey = `${roleId}:${template.id}`;
      this.templateCache.set(cacheKey, template);

      return template;
    } catch (error) {
      console.error('TemplatesService: Failed to update template:', error);

      // Enhance error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('422')) {
          throw new ServiceError('Invalid template data. Please check all required fields.', error);
        }
        if (error.message.includes('401') || error.message.includes('403')) {
          throw new ServiceError('Authentication error. Please sign in again.', error);
        }
        if (error.message.includes('404')) {
          throw new ServiceError('Template not found. It may have been deleted.', error);
        }
      }

      throw new ServiceError('Failed to update template', error);
    }
  }

  /**
   * Add a question to a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param questionData The question data
   * @returns The created question
   */
  async addQuestion(roleId: string, templateId: string, questionData: QuestionCreate): Promise<Question> {
    try {
      console.log('TemplatesService: Adding question to template:', templateId, 'for role:', roleId);

      // Validate required fields
      if (!questionData.question) {
        throw new Error('Question text is required');
      }

      const question = await templatesApi.addQuestion(roleId, templateId, questionData);

      if (!question || !question.id) {
        throw new Error('Invalid response from add question API');
      }

      // Invalidate the template cache since it's now outdated
      const cacheKey = `${roleId}:${templateId}`;
      this.templateCache.delete(cacheKey);

      return question;
    } catch (error) {
      console.error('TemplatesService: Failed to add question:', error);

      // Enhance error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('422')) {
          throw new ServiceError('Invalid question data. Please check all required fields.', error);
        }
        if (error.message.includes('401') || error.message.includes('403')) {
          throw new ServiceError('Authentication error. Please sign in again.', error);
        }
        if (error.message.includes('404')) {
          throw new ServiceError('Template not found. It may have been deleted.', error);
        }
      }

      throw new ServiceError('Failed to add question', error);
    }
  }

  /**
   * Update a question in a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param questionId The question ID
   * @param questionData The question data to update
   * @returns The updated question
   */
  async updateQuestion(roleId: string, templateId: string, questionId: string, questionData: QuestionUpdate): Promise<Question> {
    try {
      console.log('TemplatesService: Updating question:', questionId, 'in template:', templateId, 'for role:', roleId);

      // Ensure at least one field is being updated
      if (!questionData.question && !questionData.purpose && !questionData.idealAnswerCriteria) {
        throw new Error('At least one field must be provided for update');
      }

      const question = await templatesApi.updateQuestion(roleId, templateId, questionId, questionData);

      if (!question || !question.id) {
        throw new Error('Invalid response from update question API');
      }

      // Invalidate the template cache since it's now outdated
      const cacheKey = `${roleId}:${templateId}`;
      this.templateCache.delete(cacheKey);

      return question;
    } catch (error) {
      console.error('TemplatesService: Failed to update question:', error);

      // Enhance error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('422')) {
          throw new ServiceError('Invalid question data. Please check all required fields.', error);
        }
        if (error.message.includes('401') || error.message.includes('403')) {
          throw new ServiceError('Authentication error. Please sign in again.', error);
        }
        if (error.message.includes('404')) {
          throw new ServiceError('Question or template not found.', error);
        }
      }

      throw new ServiceError('Failed to update question', error);
    }
  }

  /**
   * Delete a question from a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param questionId The question ID
   */
  async deleteQuestion(roleId: string, templateId: string, questionId: string): Promise<void> {
    try {
      console.log('TemplatesService: Deleting question:', questionId, 'from template:', templateId, 'for role:', roleId);

      await templatesApi.deleteQuestion(roleId, templateId, questionId);

      // Invalidate the template cache since it's now outdated
      const cacheKey = `${roleId}:${templateId}`;
      this.templateCache.delete(cacheKey);

    } catch (error) {
      console.error('TemplatesService: Failed to delete question:', error);

      // Enhance error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('401') || error.message.includes('403')) {
          throw new ServiceError('Authentication error. Please sign in again.', error);
        }
        if (error.message.includes('404')) {
          throw new ServiceError('Question or template not found.', error);
        }
      }

      throw new ServiceError('Failed to delete question', error);
    }
  }

  /**
   * Reorder questions in a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param questionIds The ordered list of question IDs
   * @returns The updated list of questions
   */
  async reorderQuestions(roleId: string, templateId: string, questionIds: string[]): Promise<Question[]> {
    try {
      console.log('TemplatesService: Reordering questions in template:', templateId, 'for role:', roleId);

      if (!questionIds.length) {
        throw new Error('Question IDs list cannot be empty');
      }

      const questions = await templatesApi.reorderQuestions(roleId, templateId, questionIds);

      // Invalidate the template cache since it's now outdated
      const cacheKey = `${roleId}:${templateId}`;
      this.templateCache.delete(cacheKey);

      return questions;
    } catch (error) {
      console.error('TemplatesService: Failed to reorder questions:', error);

      // Enhance error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('401') || error.message.includes('403')) {
          throw new ServiceError('Authentication error. Please sign in again.', error);
        }
        if (error.message.includes('404')) {
          throw new ServiceError('Template not found.', error);
        }
      }

      throw new ServiceError('Failed to reorder questions', error);
    }
  }

  /**
   * Normalize weights of ScoreCard criteria to ensure they sum to 1.0
   * @param criteria List of criteria to normalize
   * @param newCriterion New criterion to add (optional)
   * @returns Normalized criteria list
   */
  private normalizeScoreCardWeights(criteria: Criterion[], newCriterion?: Criterion): Criterion[] {
    // Filter to get only ScoreCard criteria
    const scoreCardCriteria = criteria.filter(c => c.type === CriterionType.SCORECARD) as ScoreCardCriterion[];

    // If there's a new criterion to add and it's a ScoreCard, include it
    if (newCriterion && newCriterion.type === CriterionType.SCORECARD) {
      scoreCardCriteria.push(newCriterion as ScoreCardCriterion);
    }

    // If there are no ScoreCard criteria, return the original list
    if (scoreCardCriteria.length === 0) {
      return criteria;
    }

    // Calculate the total weight
    const totalWeight = scoreCardCriteria.reduce((sum, c) => sum + c.weight, 0);

    // If the total weight is already 1.0 (within a small margin of error), return the original list
    if (Math.abs(totalWeight - 1.0) <= 0.01) {
      return criteria;
    }

    // Normalize the weights
    scoreCardCriteria.forEach(c => {
      c.weight = c.weight / totalWeight;
    });

    // Return the updated list
    return criteria;
  }

  /**
   * Add a criterion to a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param criterionData The criterion data
   * @returns The added criterion
   */
  async addCriterion(roleId: string, templateId: string, criterionData: CriterionCreate): Promise<Criterion> {
    try {
      console.log('TemplatesService: Adding criterion to template:', templateId, 'for role:', roleId);

      // Get the current template to access existing criteria
      const template = await this.getTemplate(roleId, templateId);
      if (!template) {
        throw new ServiceError('Template not found');
      }

      // If this is a ScoreCard criterion, normalize weights
      if (criterionData.type === CriterionType.SCORECARD) {
        // Create a new criterion object with the data
        const newCriterion: Criterion = {
          id: '', // This will be assigned by the backend
          type: CriterionType.SCORECARD,
          criteria: criterionData.criteria,
          description: criterionData.description || '',
          competency: criterionData.competency || '',
          weight: criterionData.weight || 0.2
        };

        // Normalize weights including the new criterion
        const normalizedCriteria = this.normalizeScoreCardWeights(template.evaluationCriteria, newCriterion);

        // Find the normalized weight for the new criterion
        const normalizedCriterion = normalizedCriteria.find(c =>
          c.type === CriterionType.SCORECARD &&
          c.competency === newCriterion.competency &&
          c.criteria === newCriterion.criteria
        ) as ScoreCardCriterion;

        if (normalizedCriterion) {
          criterionData.weight = normalizedCriterion.weight;
        }
      }

      // Call the API to add the criterion
      const result = await templatesApi.addCriterion(roleId, templateId, criterionData);

      // Invalidate the cache for this template
      this.templateCache.delete(`${roleId}:${templateId}`);

      return result;
    } catch (error) {
      console.error('TemplatesService: Failed to add criterion:', error);
      throw new ServiceError('Failed to add criterion', error);
    }
  }

  /**
   * Update a criterion in a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param criterionId The criterion ID
   * @param criterionData The criterion data
   * @returns The updated criterion
   */
  async updateCriterion(roleId: string, templateId: string, criterionId: string, criterionData: CriterionUpdate): Promise<Criterion> {
    try {
      console.log('TemplatesService: Updating criterion:', criterionId, 'in template:', templateId, 'for role:', roleId);

      // Get the current template to access existing criteria
      const template = await this.getTemplate(roleId, templateId);
      if (!template) {
        throw new ServiceError('Template not found');
      }

      // Find the criterion being updated
      const existingCriterion = template.evaluationCriteria.find(c => c.id === criterionId);
      if (!existingCriterion) {
        throw new ServiceError('Criterion not found');
      }

      // If this is a ScoreCard criterion and weight is being updated, normalize weights
      if (existingCriterion.type === CriterionType.SCORECARD && criterionData.weight !== undefined) {
        // Create a copy of the criteria list
        const updatedCriteria = [...template.evaluationCriteria];

        // Find and update the criterion in the copy
        const criterionIndex = updatedCriteria.findIndex(c => c.id === criterionId);
        if (criterionIndex !== -1) {
          // Create an updated version of the criterion
          const updatedCriterion = {
            ...updatedCriteria[criterionIndex],
            ...criterionData
          };

          // Replace the old criterion with the updated one
          updatedCriteria[criterionIndex] = updatedCriterion;

          // Normalize weights
          const normalizedCriteria = this.normalizeScoreCardWeights(updatedCriteria);

          // Get the normalized weight for the updated criterion
          const normalizedCriterion = normalizedCriteria[criterionIndex] as ScoreCardCriterion;
          if (normalizedCriterion) {
            criterionData.weight = normalizedCriterion.weight;
          }
        }
      }

      // Call the API to update the criterion
      const result = await templatesApi.updateCriterion(roleId, templateId, criterionId, criterionData);

      // Invalidate the cache for this template
      this.templateCache.delete(`${roleId}:${templateId}`);

      return result;
    } catch (error) {
      console.error('TemplatesService: Failed to update criterion:', error);
      throw new ServiceError('Failed to update criterion', error);
    }
  }

  /**
   * Delete a criterion from a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param criterionId The criterion ID
   */
  async deleteCriterion(roleId: string, templateId: string, criterionId: string): Promise<void> {
    try {
      console.log('TemplatesService: Deleting criterion:', criterionId, 'from template:', templateId, 'for role:', roleId);

      await templatesApi.deleteCriterion(roleId, templateId, criterionId);

      // Invalidate the template cache since it's now outdated
      const cacheKey = `${roleId}:${templateId}`;
      this.templateCache.delete(cacheKey);

      console.log('TemplatesService: Criterion deleted successfully');
    } catch (error) {
      console.error('TemplatesService: Failed to delete criterion:', error);

      // Enhance error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('401') || error.message.includes('403')) {
          throw new ServiceError('Authentication error. Please sign in again.', error);
        }
        if (error.message.includes('404')) {
          throw new ServiceError('Criterion not found. It may have been deleted.', error);
        }
      }

      throw new ServiceError('Failed to delete criterion', error);
    }
  }

  /**
   * Set the pass rate for a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param passRate The pass rate value (0-1)
   */
  async setPassRate(roleId: string, templateId: string, passRate: number): Promise<void> {
    try {
      console.log('TemplatesService: Setting pass rate for template:', templateId, 'for role:', roleId, 'to:', passRate);

      // Validate pass rate
      if (passRate < 0 || passRate > 1) {
        throw new Error('Pass rate must be between 0 and 1');
      }

      await templatesApi.setPassRate(roleId, templateId, passRate);

      // Invalidate the template cache since it's now outdated
      const cacheKey = `${roleId}:${templateId}`;
      this.templateCache.delete(cacheKey);

      console.log('TemplatesService: Pass rate set successfully');
    } catch (error) {
      console.error('TemplatesService: Failed to set pass rate:', error);

      // Enhance error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('422')) {
          throw new ServiceError('Invalid pass rate. Value must be between 0 and 1.', error);
        }
        if (error.message.includes('401') || error.message.includes('403')) {
          throw new ServiceError('Authentication error. Please sign in again.', error);
        }
        if (error.message.includes('404')) {
          throw new ServiceError('Template not found. It may have been deleted.', error);
        }
      }

      throw new ServiceError('Failed to set pass rate', error);
    }
  }

  /**
   * Validate ScoreCard weights to ensure they sum to 1 (100%)
   * @param criteria List of criteria to validate
   * @returns True if valid, false otherwise
   */
  validateScoreCardWeights(criteria: Criterion[]): boolean {
    // Filter to get only ScoreCard criteria
    const scoreCardCriteria = criteria.filter(c => c.type === CriterionType.SCORECARD) as ScoreCardCriterion[];

    // If there are no ScoreCard criteria, return true
    if (scoreCardCriteria.length === 0) {
      return true;
    }

    // Calculate the total weight
    const totalWeight = scoreCardCriteria.reduce((sum, c) => sum + c.weight, 0);

    // Check if the total weight is approximately 1.0 (within a small margin of error)
    return Math.abs(totalWeight - 1.0) <= 0.01;
  }

  /**
   * Clear the template cache
   */
  clearCache(): void {
    this.templateCache.clear();
  }

  /**
   * Process a template from the API response
   * @param data The raw template data
   * @returns The processed template
   */
  processTemplate(data: InterviewTemplate): InterviewTemplate {
    // Ensure we have valid data
    if (!data || !data.id) {
      console.error('Invalid template data:', data);
      throw new Error('Invalid template data');
    }

    try {
      // Process the template data
      const processedData = {
        ...data,
        // Ensure required fields have default values
        questions: data.questions || [],
        evaluationCriteria: data.evaluationCriteria || [],
        customInstructions: data.customInstructions || '',
        statistics: data.statistics || {
          candidatesInterviewed: 0,
          passRate: 0,
          averageScore: 0,
          topScore: 0,
          topPerformerIds: []
        }
      };

      // Create the template object
      const template: InterviewTemplate = {
        id: processedData.id,
        stageIndex: processedData.stageIndex || 0,
        stage: processedData.stage || 'Unknown Stage',
        duration: processedData.duration || '30 minutes',
        customInstructions: processedData.customInstructions || '',
        questions: processedData.questions || [],
        evaluationCriteria: processedData.evaluationCriteria || [],
        status: processedData.status || TemplateStatus.DRAFT,
        statistics: processedData.statistics || {
          candidatesInterviewed: 0,
          passRate: 0,
          averageScore: 0,
          topScore: 0,
          topPerformerIds: []
        },
        createdAt: processedData.createdAt || new Date().toISOString(),
        updatedAt: processedData.updatedAt || new Date().toISOString(),
        createdBy: processedData.createdBy || ''
      };

      return template;
    } catch (error) {
      console.error('TemplatesService: Failed to process template:', error);
      throw new ServiceError('Failed to process template', error);
    }
  }

  /**
   * Generate interview questions for a template using AI
   * @param roleId The role ID
   * @param templateId The template ID
   * @returns The generated questions
   */
  async generateQuestions(roleId: string, templateId: string): Promise<Question[]> {
    try {
      console.log('TemplatesService: Generating questions for template:', templateId, 'for role:', roleId);

      // Call the API to generate questions
      const response = await templatesApi.generateQuestions(roleId, templateId);

      // Handle different response formats
      let questions;
      if (response && response.questions && Array.isArray(response.questions)) {
        questions = response.questions;
      } else if (response && typeof response === 'object') {
        // Try to extract questions from the response object
        const possibleQuestions = response.questions || [];
        questions = Array.isArray(possibleQuestions) ? possibleQuestions : [];
      } else {
        questions = [];
      }

      // Validate the questions
      if (questions.length === 0) {
        console.warn('TemplatesService: No questions were generated or invalid response format');
        // Return empty array instead of throwing - let the component handle it
        return [];
      }

      // Validate and fix any questions with missing required fields
      const validQuestions = questions.map(q => {
        // Create a new object to avoid modifying the original
        const validatedQuestion: Question = {
          id: q.id || '',
          question: q.question || (q as any).text || 'Missing question text',
          purpose: q.purpose || 'General evaluation',
          idealAnswerCriteria: q.idealAnswerCriteria || 'Clear, concise, and relevant response',
          statistics: q.statistics || {
            topScore: 0,
            topScoringCandidateId: '',
            averageScore: 0,
            totalAnswers: 0
          }
        };

        // Log warnings for any missing fields
        if (!q.question && !(q as any).text) {
          console.warn(`TemplatesService: Question ${q.id} is missing both 'question' and 'text' fields`);
        }
        if (!q.purpose) {
          console.warn(`TemplatesService: Question ${q.id} is missing 'purpose' field`);
        }
        if (!q.idealAnswerCriteria) {
          console.warn(`TemplatesService: Question ${q.id} is missing 'idealAnswerCriteria' field`);
        }

        return validatedQuestion;
      });

      // Invalidate the template cache since it's now outdated
      const cacheKey = `${roleId}:${templateId}`;
      this.templateCache.delete(cacheKey);

      console.log(`TemplatesService: Successfully validated ${validQuestions.length} questions`);
      return validQuestions;
    } catch (error) {
      console.error('TemplatesService: Failed to generate questions:', error);

      // Enhance error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('401') || error.message.includes('403')) {
          throw new ServiceError('Authentication error. Please sign in again.', error);
        }
        if (error.message.includes('404')) {
          throw new ServiceError('Template not found. It may have been deleted.', error);
        }
        if (error.message.includes('500')) {
          throw new ServiceError('Failed to generate questions. The AI service is currently unavailable. Please try again later.', error);
        }
      }

      throw new ServiceError('Failed to generate questions. Please try again.', error);
    }
  }

  /**
   * Generate evaluation criteria for a template using AI
   * @param roleId The role ID
   * @param templateId The template ID
   * @returns The generated evaluation criteria
   */
  async generateCriteria(roleId: string, templateId: string): Promise<{evaluationCriteria: Criterion[], passRate: number}> {
    try {
      console.log('TemplatesService: Generating evaluation criteria for template:', templateId, 'for role:', roleId);

      // Call the API to generate criteria
      const response = await templatesApi.generateCriteria(roleId, templateId);

      // Handle different response formats
      const result = {
        evaluationCriteria: [] as CriterionCreate[],
        passRate: 0.8 // Default pass rate
      };

      if (response && typeof response === 'object') {
        // Try to extract criteria from the response object
        if (response.evaluationCriteria && Array.isArray(response.evaluationCriteria)) {
          result.evaluationCriteria = response.evaluationCriteria;
        }

        // Extract pass rate if available
        if (typeof response.passRate === 'number') {
          result.passRate = response.passRate;
        }
      }

      // Validate the criteria
      if (result.evaluationCriteria.length === 0) {
        console.warn('TemplatesService: No evaluation criteria were generated or invalid response format');
        // Return empty criteria instead of throwing - let the component handle it
        return { evaluationCriteria: [], passRate: 0.8 };
      }

      // Validate and fix any criteria with missing required fields
      const validCriteria = result.evaluationCriteria.map(c => {
        // Create a new object to avoid modifying the original
        let validatedCriterion: Criterion;

        switch (c.type) {
          case CriterionType.SCORECARD:
            validatedCriterion = {
              id: c.id || '',
              type: CriterionType.SCORECARD,
              criteria: c.criteria || 'Missing criteria description',
              description: c.description || '',
              competency: c.competency || 'General Competency',
              weight: typeof c.weight === 'number' ? c.weight : 0.2
            };
            break;
          case CriterionType.BETWEEN_THE_LINES:
            validatedCriterion = {
              id: c.id || '',
              type: CriterionType.BETWEEN_THE_LINES,
              criteria: c.criteria || 'Missing criteria description',
              description: c.description || ''
            };
            break;
          case CriterionType.DISQUALIFIER:
            validatedCriterion = {
              id: c.id || '',
              type: CriterionType.DISQUALIFIER,
              criteria: c.criteria || 'Missing criteria description',
              description: c.description || ''
            };
            break;
          default:
            console.warn(`TemplatesService: Unknown criterion type: ${(c as any).type}`);
            // Default to Between the Lines as a fallback
            validatedCriterion = {
              id: (c as any).id || '',
              type: CriterionType.BETWEEN_THE_LINES,
              criteria: (c as any).criteria || 'Missing criteria description',
              description: (c as any).description || ''
            };
        }

        // Log warnings for any missing fields
        if (!c.criteria) {
          console.warn(`TemplatesService: Criterion is missing 'criteria' field`);
        }
        if (c.type === CriterionType.SCORECARD) {
          if (!c.competency) {
            console.warn(`TemplatesService: ScoreCard criterion is missing 'competency' field`);
          }
          if (typeof c.weight !== 'number') {
            console.warn(`TemplatesService: ScoreCard criterion is missing 'weight' field`);
          }
        }

        return validatedCriterion;
      });

      // Validate that ScoreCard weights sum to approximately 1.0
      const scoreCardCriteria = validCriteria.filter(c => c.type === CriterionType.SCORECARD) as ScoreCardCriterion[];
      if (scoreCardCriteria.length > 0) {
        const totalWeight = scoreCardCriteria.reduce((sum, c) => sum + c.weight, 0);
        // Allow for small floating-point errors (within 0.05)
        if (Math.abs(totalWeight - 1.0) > 0.05) {
          console.warn(`TemplatesService: ScoreCard weights do not sum to 1.0 (total: ${totalWeight}). Normalizing weights.`);
          // Normalize weights to sum to 1.0
          scoreCardCriteria.forEach(c => {
            c.weight = c.weight / totalWeight;
          });
        }
      }

      return {
        evaluationCriteria: validCriteria,
        passRate: result.passRate || 0.8
      };
    } catch (error) {
      console.error('TemplatesService: Failed to generate evaluation criteria:', error);
      throw new ServiceError('Failed to generate evaluation criteria', error);
    }
  }
}

// Export a singleton instance
export const templatesService = new TemplatesService();