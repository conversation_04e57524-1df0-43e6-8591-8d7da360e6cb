import { apiClient } from '@/lib/api/client/axios';
import { handleApiError } from '@/lib/utils/errors';
import { API_ENDPOINTS } from '@/lib/api/constants';

// Template types
export enum TemplateStatus {
  DRAFT = "Draft",
  ACTIVE = "Active",
  // Note: PAUSED is not supported by the backend yet, but we keep it in the frontend for future use
  // When sending to the backend, we'll convert PAUSED to ACTIVE to avoid validation errors
  PAUSED = "Paused",
  ARCHIVED = "Archived"
}

export interface TemplateStatistics {
  candidatesInterviewed: number;
  passRate: number;
  averageScore: number;
  topScore: number;
  topPerformerIds: string[];
}

// Evaluation Criteria Types
export enum CriterionType {
  SCORECARD = "ScoreCard",
  BETWEEN_THE_LINES = "BetweenTheLines",
  DISQUALIFIER = "Disqualifier"
}

export interface CriterionBase {
  id: string;
  type: CriterionType;
  criteria: string;
  description?: string;
}

export interface ScoreCardCriterion extends CriterionBase {
  type: CriterionType.SCORECARD;
  competency: string;
  weight: number;
}

export interface BetweenTheLinesCriterion extends CriterionBase {
  type: CriterionType.BETWEEN_THE_LINES;
}

export interface DisqualifierCriterion extends CriterionBase {
  type: CriterionType.DISQUALIFIER;
}

export type Criterion = ScoreCardCriterion | BetweenTheLinesCriterion | DisqualifierCriterion;

export interface CriterionCreateBase {
  type: CriterionType;
  criteria: string;
  description?: string;
  id?: string;
}

export interface ScoreCardCriterionCreate extends CriterionCreateBase {
  type: CriterionType.SCORECARD;
  competency: string;
  weight: number;
}

export interface BetweenTheLinesCriterionCreate extends CriterionCreateBase {
  type: CriterionType.BETWEEN_THE_LINES;
}

export interface DisqualifierCriterionCreate extends CriterionCreateBase {
  type: CriterionType.DISQUALIFIER;
}

export type CriterionCreate = ScoreCardCriterionCreate | BetweenTheLinesCriterionCreate | DisqualifierCriterionCreate;

export interface CriterionUpdate {
  criteria?: string;
  description?: string;
  competency?: string;
  weight?: number;
}

export interface PassRateUpdate {
  passRate: number;
}

export interface InterviewTemplate {
  id: string;
  stageIndex: number;
  stage: string;
  duration: string;
  customInstructions: string;
  questions: Question[];
  evaluationCriteria: Criterion[];
  status: TemplateStatus;
  statistics: TemplateStatistics;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface CreateTemplateRequest {
  stageIndex: number;
  stage: string;
  duration: string;
  customInstructions?: string;
  questions?: Question[];
  evaluationCriteria?: CriterionCreate[];
  status?: TemplateStatus;
}

export interface UpdateTemplateRequest {
  stageIndex?: number;
  stage?: string;
  duration?: string;
  customInstructions?: string;
  questions?: Question[];
  evaluationCriteria?: CriterionCreate[];
  status?: TemplateStatus;
}

// Question types
export interface QuestionStatistics {
  topScore: number;
  topScoringCandidateId: string;
  averageScore: number;
  totalAnswers: number;
}

export interface QuestionBase {
  question: string;
  purpose: string;
  idealAnswerCriteria: string;
}

export interface Question extends QuestionBase {
  id: string;
  statistics: QuestionStatistics;
}

export interface QuestionCreate extends QuestionBase {}

export interface QuestionUpdate {
  question?: string;
  purpose?: string;
  idealAnswerCriteria?: string;
}

export interface QuestionReorder {
  questionIds: string[];
}

// API path
const API_PATH = API_ENDPOINTS.TEMPLATES;

/**
 * Templates API service
 * Provides functions to interact with the template API endpoints
 */
export const templatesApi = {
  /**
   * List all templates for a role
   * @param roleId The role ID
   * @returns List of templates
   */
  listTemplates: async (roleId: string): Promise<InterviewTemplate[]> => {
    try {
      console.log('TemplatesApi: Fetching templates for role:', roleId);
      const response = await apiClient.get<InterviewTemplate[]>(`${API_PATH}/${roleId}`);
      console.log(`TemplatesApi: Successfully fetched ${response.data?.length || 0} templates`);
      return response.data || [];
    } catch (error) {
      console.error(`TemplatesApi: Error getting templates for role ${roleId}:`, error);
      return [];
    }
  },

  /**
   * Get a specific template
   * @param roleId The role ID
   * @param templateId The template ID
   * @returns The template or null if not found
   */
  getTemplate: async (roleId: string, templateId: string): Promise<InterviewTemplate | null> => {
    try {
      console.log('TemplatesApi: Fetching template:', templateId, 'for role:', roleId);
      const response = await apiClient.get<InterviewTemplate>(`${API_PATH}/${roleId}/${templateId}`);
      console.log('TemplatesApi: Response:', response);
      return response.data;
    } catch (error) {
      console.error(`TemplatesApi: Error getting template ${templateId} for role ${roleId}:`, error);
      return null;
    }
  },

  /**
   * Create a new template
   * @param roleId The role ID
   * @param data The template data
   * @returns The created template
   */
  createTemplate: async (roleId: string, data: CreateTemplateRequest): Promise<InterviewTemplate> => {
    try {
      console.log('TemplatesApi: Creating template for role:', roleId, 'with data:', data);
      const response = await apiClient.post<InterviewTemplate>(`${API_PATH}/${roleId}`, data);
      console.log('TemplatesApi: Template created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`TemplatesApi: Error creating template for role ${roleId}:`, error);
      return handleApiError(error);
    }
  },

  /**
   * Update a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param data The template data to update
   * @returns The updated template
   */
  updateTemplate: async (roleId: string, templateId: string, data: UpdateTemplateRequest): Promise<InterviewTemplate> => {
    try {
      console.log('TemplatesApi: Updating template:', templateId, 'for role:', roleId, 'with data:', data);

      // First, get the current template to ensure we have valid data
      const currentTemplate = await templatesApi.getTemplate(roleId, templateId);

      if (!currentTemplate) {
        throw new Error('Template not found');
      }

      // Clean the data to ensure we only send valid fields
      // This helps prevent 422 validation errors
      const cleanedData: UpdateTemplateRequest = {};

      // Only include fields that are actually changing
      if (data.status !== undefined) cleanedData.status = data.status;
      if (data.stage !== undefined) cleanedData.stage = data.stage;
      if (data.duration !== undefined) cleanedData.duration = data.duration;
      if (data.customInstructions !== undefined) cleanedData.customInstructions = data.customInstructions;

      // Only include these complex fields if they're actually provided and valid
      if (data.questions && Array.isArray(data.questions)) cleanedData.questions = data.questions;
      if (data.evaluationCriteria && Array.isArray(data.evaluationCriteria)) cleanedData.evaluationCriteria = data.evaluationCriteria;

      console.log('TemplatesApi: Sending cleaned data:', cleanedData);

      const response = await apiClient.put<InterviewTemplate>(`${API_PATH}/${roleId}/${templateId}`, cleanedData);

      if (!response.data || !response.data.id) {
        console.error('TemplatesApi: Invalid response data:', response.data);

        // If update failed but we have the current template, return it
        // This prevents UI errors while still logging the actual error
        if (currentTemplate) {
          console.warn('TemplatesApi: Returning current template as fallback');
          return currentTemplate;
        }

        throw new Error('Invalid response from API');
      }

      console.log('TemplatesApi: Template updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`TemplatesApi: Error updating template ${templateId} for role ${roleId}:`, error);

      // If it's a 422 error, try to get the current template as a fallback
      if (error instanceof Error && error.message.includes('422')) {
        console.warn('TemplatesApi: 422 error, attempting to get current template as fallback');
        const currentTemplate = await templatesApi.getTemplate(roleId, templateId);
        if (currentTemplate) {
          return currentTemplate;
        }
      }

      throw error; // Let the service layer handle the error
    }
  },

  /**
   * Add a question to a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param question The question data
   * @returns The created question
   */
  addQuestion: async (roleId: string, templateId: string, question: QuestionCreate): Promise<Question> => {
    try {
      console.log('TemplatesApi: Adding question to template:', templateId, 'for role:', roleId, 'with data:', question);
      const response = await apiClient.post<Question>(`${API_PATH}/${roleId}/${templateId}/questions`, question);
      console.log('TemplatesApi: Question added successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`TemplatesApi: Error adding question to template ${templateId} for role ${roleId}:`, error);
      return handleApiError(error);
    }
  },

  /**
   * Update a question in a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param questionId The question ID
   * @param question The question data to update
   * @returns The updated question
   */
  updateQuestion: async (roleId: string, templateId: string, questionId: string, question: QuestionUpdate): Promise<Question> => {
    try {
      console.log('TemplatesApi: Updating question:', questionId, 'in template:', templateId, 'for role:', roleId, 'with data:', question);
      const response = await apiClient.put<Question>(`${API_PATH}/${roleId}/${templateId}/questions/${questionId}`, question);
      console.log('TemplatesApi: Question updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`TemplatesApi: Error updating question ${questionId} in template ${templateId} for role ${roleId}:`, error);
      return handleApiError(error);
    }
  },

  /**
   * Delete a question from a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param questionId The question ID
   */
  deleteQuestion: async (roleId: string, templateId: string, questionId: string): Promise<void> => {
    try {
      console.log('TemplatesApi: Deleting question:', questionId, 'from template:', templateId, 'for role:', roleId);
      await apiClient.delete(`${API_PATH}/${roleId}/${templateId}/questions/${questionId}`);
      console.log('TemplatesApi: Question deleted successfully');
    } catch (error) {
      console.error(`TemplatesApi: Error deleting question ${questionId} from template ${templateId} for role ${roleId}:`, error);
      handleApiError(error);
    }
  },

  /**
   * Reorder questions in a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param questionIds The ordered list of question IDs
   * @returns The updated list of questions
   */
  reorderQuestions: async (roleId: string, templateId: string, questionIds: string[]): Promise<Question[]> => {
    try {
      console.log('TemplatesApi: Reordering questions in template:', templateId, 'for role:', roleId, 'with order:', questionIds);
      const response = await apiClient.put<Question[]>(`${API_PATH}/${roleId}/${templateId}/questions/reorder`, { questionIds });
      console.log('TemplatesApi: Questions reordered successfully');
      return response.data;
    } catch (error) {
      console.error(`TemplatesApi: Error reordering questions in template ${templateId} for role ${roleId}:`, error);
      return handleApiError(error);
    }
  },

  /**
   * Add a criterion to a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param criterion The criterion data
   * @returns The created criterion
   */
  addCriterion: async (roleId: string, templateId: string, criterion: CriterionCreate): Promise<Criterion> => {
    try {
      console.log('TemplatesApi: Adding criterion to template:', templateId, 'for role:', roleId, 'with data:', criterion);
      const response = await apiClient.post<Criterion>(`${API_PATH}/${roleId}/${templateId}/criteria`, criterion);
      console.log('TemplatesApi: Criterion added successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`TemplatesApi: Error adding criterion to template ${templateId} for role ${roleId}:`, error);
      return handleApiError(error);
    }
  },

  /**
   * Update a criterion in a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param criterionId The criterion ID
   * @param criterion The criterion data to update
   * @returns The updated criterion
   */
  updateCriterion: async (roleId: string, templateId: string, criterionId: string, criterion: CriterionUpdate): Promise<Criterion> => {
    try {
      console.log('TemplatesApi: Updating criterion:', criterionId, 'in template:', templateId, 'for role:', roleId, 'with data:', criterion);
      const response = await apiClient.put<Criterion>(`${API_PATH}/${roleId}/${templateId}/criteria/${criterionId}`, criterion);
      console.log('TemplatesApi: Criterion updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`TemplatesApi: Error updating criterion ${criterionId} in template ${templateId} for role ${roleId}:`, error);
      return handleApiError(error);
    }
  },

  /**
   * Delete a criterion from a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param criterionId The criterion ID
   */
  deleteCriterion: async (roleId: string, templateId: string, criterionId: string): Promise<void> => {
    try {
      console.log('TemplatesApi: Deleting criterion:', criterionId, 'from template:', templateId, 'for role:', roleId);
      await apiClient.delete(`${API_PATH}/${roleId}/${templateId}/criteria/${criterionId}`);
      console.log('TemplatesApi: Criterion deleted successfully');
    } catch (error) {
      console.error(`TemplatesApi: Error deleting criterion ${criterionId} from template ${templateId} for role ${roleId}:`, error);
      handleApiError(error);
    }
  },

  /**
   * Set the pass rate for a template
   * @param roleId The role ID
   * @param templateId The template ID
   * @param passRate The pass rate value (0-100)
   */
  setPassRate: async (roleId: string, templateId: string, passRate: number): Promise<void> => {
    try {
      console.log('TemplatesApi: Setting pass rate for template:', templateId, 'for role:', roleId, 'to:', passRate);
      await apiClient.put(`${API_PATH}/${roleId}/${templateId}/pass-rate`, { passRate });
      console.log('TemplatesApi: Pass rate set successfully');
    } catch (error) {
      console.error(`TemplatesApi: Error setting pass rate for template ${templateId} for role ${roleId}:`, error);
      handleApiError(error);
    }
  },

  /**
   * Generate interview questions for a template using AI
   * @param roleId The role ID
   * @param templateId The template ID
   * @returns The generated questions
   */
  generateQuestions: async (roleId: string, templateId: string): Promise<GenerateQuestionsResponse> => {
    try {
      console.log('TemplatesApi: Generating questions for template:', templateId, 'for role:', roleId);
      const response = await apiClient.post<GenerateQuestionsResponse>(`${API_PATH}/${roleId}/${templateId}/generate-questions`);
      console.log('TemplatesApi: Questions generated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`TemplatesApi: Error generating questions for template ${templateId} for role ${roleId}:`, error);
      return handleApiError(error);
    }
  },

  /**
   * Generate evaluation criteria for a template using AI
   * @param roleId The role ID
   * @param templateId The template ID
   * @returns The generated evaluation criteria and pass rate
   */
  generateCriteria: async (roleId: string, templateId: string): Promise<GenerateCriteriaResponse> => {
    try {
      console.log('TemplatesApi: Generating evaluation criteria for template:', templateId, 'for role:', roleId);
      const response = await apiClient.post<GenerateCriteriaResponse>(`${API_PATH}/${roleId}/${templateId}/generate-criteria`);
      console.log('TemplatesApi: Evaluation criteria generated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`TemplatesApi: Error generating evaluation criteria for template ${templateId} for role ${roleId}:`, error);
      return handleApiError(error);
    }
  }
};

// Update the response types for generate-questions and generate-criteria
interface GenerateQuestionsResponse {
  status: string;
  questions: QuestionCreate[];
}

interface GenerateCriteriaResponse {
  status: string;
  evaluationCriteria: CriterionCreate[];
  passRate: number;
}