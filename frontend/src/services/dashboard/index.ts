import { apiClient } from '@/lib/api/client/axios';
import { Role, ExtendedApplication } from '@/types/role';

/**
 * Dashboard service for fetching dashboard data
 */
export interface DashboardData {
  roles: Role[];
  applications: ExtendedApplication[];
  evaluations: any[];
}

export interface InterviewTranscript {
  id: string;
  status: 'in_progress' | 'completed';
  created_at?: string;
  createdAt?: string;
  updated_at?: string;
  updatedAt?: string;
  completed_at?: string;
  completedAt?: string;
  role_id?: string;
  roleId?: string;
  messages?: any[];
}

class DashboardService {
  /**
   * Get all dashboard data in a single request
   *
   * @returns Dashboard data including roles, applications, and evaluations
   */
  async getDashboardData(): Promise<DashboardData> {
    try {
      const response = await apiClient.get<DashboardData>('/dashboard/data');
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Return empty data on error
      return {
        roles: [],
        applications: [],
        evaluations: []
      };
    }
  }

  /**
   * Get interview count
   *
   * @returns Interview count data including total and this week's count
   */
  async getInterviewsCount(): Promise<{ total_count: number; this_week_count: number }> {
    try {
      // Add a timeout to the request to prevent it from hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout

      // Use a cache key to prevent multiple requests for the same data
      const cacheKey = 'interview-count-' + new Date().toISOString().split('T')[0]; // Cache by day
      const cachedData = sessionStorage.getItem(cacheKey);

      // If we have cached data and it's from today, use it
      if (cachedData) {
        try {
          const parsedData = JSON.parse(cachedData);
          console.log('Using cached interview count data');

          // Clear the timeout since we're using cached data
          clearTimeout(timeoutId);

          return {
            total_count: parsedData.total_count || 0,
            this_week_count: parsedData.this_week_count || 0
          };
        } catch (e) {
          console.error('Error parsing cached interview count data:', e);
          // Continue with the API request if parsing fails
        }
      }

      const response = await apiClient.get<{ status: string; total_count: number; this_week_count: number }>(
        '/dashboard/interviews/count',
        {
          signal: controller.signal,
          // Add a cache buster to prevent browser caching
          params: { _t: new Date().getTime() }
        }
      );

      // Clear the timeout
      clearTimeout(timeoutId);

      const result = {
        total_count: response.data.total_count || 0,
        this_week_count: response.data.this_week_count || 0
      };

      // Cache the result in sessionStorage
      try {
        sessionStorage.setItem(cacheKey, JSON.stringify(result));
      } catch (e) {
        console.error('Error caching interview count data:', e);
      }

      return result;
    } catch (error) {
      console.error('Error fetching interview count:', error);
      return {
        total_count: 0,
        this_week_count: 0
      };
    }
  }

  /**
   * Get all applications
   *
   * @returns Array of applications
   */
  async getApplications(): Promise<ExtendedApplication[]> {
    try {
      const response = await apiClient.get<ExtendedApplication[]>('/dashboard/applications');
      return response.data;
    } catch (error) {
      console.error('Error fetching applications:', error);
      return [];
    }
  }

  /**
   * Get all evaluations
   *
   * @param roleId Optional role ID to filter evaluations
   * @returns Array of evaluations
   */
  async getEvaluations(roleId?: string): Promise<any[]> {
    try {
      const url = roleId
        ? `/dashboard/evaluations?role_id=${roleId}`
        : '/dashboard/evaluations';

      const response = await apiClient.get<any[]>(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching evaluations:', error);
      return [];
    }
  }
}

export const dashboardService = new DashboardService();
