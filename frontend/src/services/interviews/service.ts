import {
  getInterviewSessions,
  getInterviewSession,
  InterviewSession
} from './api';

/**
 * InterviewService provides a higher-level interface for interview sessions
 * with integrated caching capabilities.
 */
export class InterviewService {
  private static instance: InterviewService;
  private cache: Map<string, InterviewSession> = new Map();
  private sessionListCache: InterviewSession[] | null = null;
  private readonly MAX_CACHE_SIZE = 20;
  private readonly CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes
  private lastFetchTime: number = 0;

  /**
   * Get the singleton instance of the InterviewService
   *
   * @returns The InterviewService instance
   */
  public static getInstance(): InterviewService {
    if (!InterviewService.instance) {
      InterviewService.instance = new InterviewService();
    }
    return InterviewService.instance;
  }

  /**
   * Get all interview sessions with caching
   *
   * @param forceRefresh Whether to force a refresh from the API
   * @returns Array of interview sessions
   */
  public async getInterviewSessions(forceRefresh: boolean = false): Promise<InterviewSession[]> {
    try {
      const now = Date.now();
      const cacheExpired = now - this.lastFetchTime > this.CACHE_EXPIRY;

      // Check cache first if not forcing refresh and cache hasn't expired
      if (!forceRefresh && this.sessionListCache && !cacheExpired) {
        return this.sessionListCache;
      }

      // Fetch from API
      const sessions = await getInterviewSessions();
      
      // Update cache
      this.sessionListCache = sessions;
      this.lastFetchTime = now;
      
      // Also update individual session cache
      sessions.forEach(session => {
        if (session.id) {
          this._updateCache(session.id, session);
        }
      });

      return sessions;
    } catch (error) {
      console.error('Error getting interview sessions:', error);
      // Return empty array on error if we don't have cached data
      return this.sessionListCache || [];
    }
  }

  /**
   * Get an interview session by ID with caching
   *
   * @param sessionId The interview session ID
   * @param forceRefresh Whether to force a refresh from the API
   * @returns The interview session
   */
  public async getInterviewSession(
    sessionId: string,
    forceRefresh: boolean = false
  ): Promise<InterviewSession | null> {
    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh && this.cache.has(sessionId)) {
        return this.cache.get(sessionId) || null;
      }

      // Fetch from API
      const session = await getInterviewSession(sessionId);
      
      // Update cache
      if (session) {
        this._updateCache(sessionId, session);
      }

      return session;
    } catch (error) {
      console.error('Error getting interview session:', error);
      // Return cached version on error if available
      return this.cache.get(sessionId) || null;
    }
  }

  /**
   * Clear cache for a session or all sessions
   *
   * @param sessionId Optional session ID to clear from cache
   */
  public clearCache(sessionId?: string): void {
    if (sessionId) {
      this.cache.delete(sessionId);
    } else {
      this.cache.clear();
      this.sessionListCache = null;
      this.lastFetchTime = 0;
    }
  }

  /**
   * Update the cache with a new session
   *
   * @param sessionId The session ID
   * @param session The session
   */
  private _updateCache(sessionId: string, session: InterviewSession): void {
    // Add to cache
    this.cache.set(sessionId, session);

    // Implement LRU cache - remove oldest entries if cache is too large
    if (this.cache.size > this.MAX_CACHE_SIZE) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }
  }
}

// Export singleton instance
export const interviewService = InterviewService.getInstance();
