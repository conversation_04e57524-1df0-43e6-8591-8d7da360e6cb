import { apiClient } from '@/lib/api/client/axios';

/**
 * API types for interview sessions
 */
export interface InterviewSession {
  id: string;
  roleId?: string;
  role_id?: string;
  applicationId?: string;
  candidateId?: string;
  candidateName?: string;
  roleName?: string;
  stage?: string;
  interviewStage?: string;
  templateId?: string;
  template_id?: string;
  status: string;
  created_at?: string;
  createdAt?: string;
  updated_at?: string;
  updatedAt?: string;
  completed_at?: string;
  completedAt?: string;
  messages?: any[];
  candidate?: {
    id?: string;
    name?: string;
    email?: string;
  };
  role?: {
    id?: string;
    title?: string;
  };
  template?: {
    id?: string;
    name?: string;
    url?: string;
  };
}

export interface InterviewSessionResponse {
  data: InterviewSession[];
  status: string;
  message?: string;
}

/**
 * Get all interview sessions for the current user
 *
 * @returns Array of interview sessions
 */
export async function getInterviewSessions(): Promise<InterviewSession[]> {
  try {
    const response = await apiClient.get<InterviewSessionResponse>('/dashboard/interviews');
    return response.data.data || [];
  } catch (error) {
    console.error('Error getting interview sessions:', error);
    throw error;
  }
}

/**
 * Get an interview session by ID
 *
 * @param sessionId The interview session ID
 * @returns The interview session
 */
export async function getInterviewSession(sessionId: string): Promise<InterviewSession> {
  try {
    const response = await apiClient.get<{ data: InterviewSession }>(`/dashboard/interviews/${sessionId}`);
    return response.data.data;
  } catch (error) {
    console.error('Error getting interview session:', error);
    throw error;
  }
}
