import { apiClient } from '@/lib/api/client/axios';
import { handleApiError } from '@/lib/utils/errors';

/**
 * Response from creating a realtime session
 */
export interface SessionResponse {
  session_id: string;
  client_secret: {
    value: string;
    expires_at: number;
  };
  transcript_id?: string;
  role_id?: string;
  template_id?: string;
  is_public?: boolean;
}

// Add these interfaces at the top of the file
interface PublicInterviewPayload {
  role_id: string;
  template_id?: string;
  candidate_id?: string;
  application_id?: string;
  resume_text?: string;
  job_posting?: string;
  candidate_name?: string;
}

interface ApiError extends Error {
  code?: string;
  response?: {
    status: number;
    data?: unknown;
  };
}

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

interface RoleData {
  id: string;
  title: string;
  summary?: string;
  keyResponsibilities: string[];
  [key: string]: unknown;
}

// Add this interface near the other interfaces
interface WindowWithRedirect {
  __redirectingFromCall?: boolean;
}

/**
 * Realtime API service
 * Provides functions to interact with the realtime API endpoints
 */
export const realtimeApi = {
  /**
   * Create a default realtime session (intake agent)
   * @param roleId Optional role ID to associate with the session
   * @param isEnrichment Whether this is an enrichment call for an existing role
   * @returns Session response with session ID and client secret
   */
  createSession: async (roleId?: string, isEnrichment: boolean = false): Promise<SessionResponse | { detail: string }> => {
    try {
      console.log(`RealtimeApi: Creating default session${roleId ? ` for role ${roleId}` : ''}${isEnrichment ? ' (enrichment)' : ''}`);

      // Add roleId and isEnrichment as query parameters if provided
      let queryParams = '';
      if (roleId) {
        queryParams = `?role_id=${roleId}`;
        if (isEnrichment) {
          queryParams += `&is_enrichment=true`;
        }
      } else if (isEnrichment) {
        queryParams = `?is_enrichment=true`;
      }

      const response = await apiClient.post<SessionResponse>(`/realtime/session${queryParams}`);

      console.log('RealtimeApi: Session created successfully');
      return response.data;
    } catch (error) {
      console.error('RealtimeApi: Error creating session:', error);
      return handleApiError(error, {
        defaultErrorResponse: {
          detail: 'Failed to create default session. Please try again.'
        }
      });
    }
  },

  /**
   * Create a realtime session with a specific agent type
   * @param agentType The agent type (e.g., 'intake_agent', 'interview_agent')
   * @returns Session response with session ID and client secret
   */
  createSessionWithAgent: async (agentType: string): Promise<SessionResponse | { detail: string }> => {
    try {
      console.log(`RealtimeApi: Creating session with agent type: ${agentType}`);
      const response = await apiClient.post<SessionResponse>(`/realtime/session/${agentType}`);
      console.log('RealtimeApi: Session created successfully');
      return response.data;
    } catch (error) {
      console.error(`RealtimeApi: Error creating session with agent type ${agentType}:`, error);
      return handleApiError(error, {
        defaultErrorResponse: {
          detail: `Failed to create session with agent type ${agentType}. Please try again.`
        }
      });
    }
  },

  /**
   * Create an interview session
   * @param roleId The role ID
   * @param templateId The template ID
   * @returns Session response with session ID and client secret
   */
  createInterviewSession: async (roleId: string, templateId: string): Promise<SessionResponse | { detail: string }> => {
    try {
      console.log(`RealtimeApi: Creating interview session for role ${roleId} and template ${templateId}`);
      const response = await apiClient.post<SessionResponse>(`/realtime/interview-session`, {
        role_id: roleId,
        template_id: templateId
      });
      console.log('RealtimeApi: Interview session created successfully');
      return response.data;
    } catch (error) {
      console.error(`RealtimeApi: Error creating interview session for role ${roleId} and template ${templateId}:`, error);
      return handleApiError(error, {
        defaultErrorResponse: {
          detail: 'Failed to create interview session. Please try again.'
        }
      });
    }
  },

  /**
   * Create a public interview session for candidates
   * @param roleId The role ID
   * @param templateId Optional template ID (defaults to screening template)
   * @param candidateId Optional candidate ID to link the session to a candidate
   * @param applicationId Optional application ID to link the session to an application
   * @param resumeText Optional resume text from the candidate
   * @param jobPosting Optional job posting text
   * @param candidateName Optional candidate name to display in the interview
   * @returns Session response with session ID and client secret
   */
  createPublicInterviewSession: async (
    roleId: string,
    templateId?: string,
    candidateId?: string,
    applicationId?: string,
    resumeText?: string,
    jobPosting?: string,
    candidateName?: string
  ): Promise<SessionResponse | { detail: string }> => {
    try {
      console.log(`RealtimeApi: Creating public interview session for role ${roleId}`);

      if (!roleId) {
        console.error('RealtimeApi: Role ID is required for createPublicInterviewSession');
        return { detail: 'Role ID is required' };
      }

      const payload: PublicInterviewPayload = { role_id: roleId };
      if (templateId) {
        payload.template_id = templateId;
      }

      // Add candidate and application IDs if available
      if (candidateId) {
        payload.candidate_id = candidateId;
      }
      if (applicationId) {
        payload.application_id = applicationId;
      }

      // Add resume text and job posting if available
      if (resumeText) {
        payload.resume_text = resumeText;
      }
      if (jobPosting) {
        payload.job_posting = jobPosting;
      }

      // Add candidate name if available
      // Store it in localStorage for use in the frontend even if the backend doesn't support it yet
      if (candidateName) {
        payload.candidate_name = candidateName;
        try {
          localStorage.setItem('candidateName', candidateName);
        } catch (e) {
          // Ignore localStorage errors
        }
      }

      console.log('RealtimeApi: Sending request with payload:', payload);

      // Add a unique timestamp parameter to avoid caching issues

      // Try up to 3 times with exponential backoff
      let lastError: ApiError | null = null;
      for (let attempt = 0; attempt < 3; attempt++) {
        try {
          // If not the first attempt, add backoff delay
          if (attempt > 0) {
            const delayMs = Math.min(1000 * Math.pow(2, attempt), 5000);
            console.log(`RealtimeApi: Retry attempt ${attempt+1}/3 after ${delayMs}ms delay`);
            await new Promise(resolve => setTimeout(resolve, delayMs));
          }

          // Always add a new timestamp for each attempt to prevent caching
          const requestTimestamp = Date.now();

          // Added debugging to see what's being sent and additional headers for CORS
          const response = await apiClient.post<SessionResponse>(
            `/realtime/public-interview-session?t=${requestTimestamp}`,
            payload,
            {
              timeout: 30000, // Increase timeout to 30 seconds
              headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store',
                'Pragma': 'no-cache'
              }
            }
          );

          // Log response info
          if (!response || !response.data) {
            console.error('RealtimeApi: Received empty response from public interview session API');
            continue; // Try again on empty response
          }

          // Validate required fields
          const data = response.data;
          if (!data.session_id || !data.client_secret || typeof data.client_secret !== 'object') {
            console.error('RealtimeApi: Invalid session data received', data);
            continue; // Try again on invalid data
          }

          if (!data.client_secret.value || !data.client_secret.expires_at) {
            console.error('RealtimeApi: Invalid client secret in session data', data.client_secret);
            continue; // Try again on invalid client secret
          }

          // Check if token is already expired
          const expiresAt = data.client_secret.expires_at;
          const expiresInSec = expiresAt - Math.floor(Date.now() / 1000);
          if (expiresInSec < 60) { // Token expires in less than a minute
            console.error('RealtimeApi: Session token will expire too soon', {
              expiresAt,
              expiresInSec
            });
            continue; // Try again on near-expired token
          }

          // Store creation time for future reference
          try {
            localStorage.setItem('sessionCreationTime', Date.now().toString());
          } catch (storageError) {
            console.warn('RealtimeApi: Unable to store session creation time in localStorage', storageError);
          }

          // Success - return valid session data
          console.log('RealtimeApi: Session created successfully:', {
            session_id: data.session_id,
            expires_in_sec: expiresInSec,
            transcript_id: data.transcript_id || 'none'
          });

          return data;
        } catch (error: unknown) {
          console.error('RealtimeApi: Error in session:', error);
          if (error instanceof Error) {
            const apiError = new Error(error.message) as ApiError;
            apiError.name = error.name;
            lastError = apiError;
          } else {
            const apiError = new Error('Unknown error occurred') as ApiError;
            apiError.name = 'UnknownError';
            lastError = apiError;
          }

          // Check for authentication-related errors (we can still proceed)
          if (error instanceof Error && 'response' in error && (error as ApiError).response?.status === 401 || (error as ApiError).response?.status === 403) {
            console.warn('RealtimeApi: Authentication error occurred, but proceeding with public access');
            // Don't retry on auth errors - they'll likely keep failing
            break;
          }

          // Check for 404 or invalid data (could be a temporary issue)
          if (error instanceof Error && 'response' in error && ((error as ApiError).response?.status === 400 || (error as ApiError).response?.status === 404)) {
            // Continue the retry loop
            continue;
          }
        }
      }

      // All attempts failed
      console.error('RealtimeApi: All session creation attempts failed');

      // For public interviews, provide a more helpful message
      if (lastError?.code === '401' || lastError?.code === '403') {
        return {
          detail: 'Unable to authenticate request, but proceeding with public access mode.',
          session_id: `public-${Date.now()}`,
          client_secret: {
            value: "mock-session-public",
            expires_at: Math.floor(Date.now()/1000) + 3600
          },
          transcript_id: `public-transcript-${Date.now()}`,
          is_public: true
        };
      }

      return { detail: 'Server error. Please try again later.' };
    } catch (error) {
      console.error(`RealtimeApi: Unhandled error in createPublicInterviewSession:`, error);
      return { detail: error instanceof Error ? error.message : 'An unexpected error occurred' };
    }
  },

  /**
   * End a call session and process the transcript
   * @param params Call parameters
   * @param signal Optional AbortSignal to cancel the request
   * @returns Success response or error
   */
  endCall: async (params: {
    role_id: string;
    transcript_id: string;
    user_id: string;
    is_enrichment?: boolean;
    role_data?: RoleData;
    process_method?: 'enrichment_agent' | 'direct_data';
    agent_type?: string;
  }, signal?: AbortSignal): Promise<{ status: string; message: string; detail?: string }> => {
    try {
      console.log(`[RealtimeApi] Ending call for role ${params.role_id}, transcript ${params.transcript_id}`);

      // Create request data - ensure agent_type is always in the request
      const requestData = {
        role_id: params.role_id,
        transcript_id: params.transcript_id,
        user_id: params.user_id,
        is_enrichment: params.is_enrichment !== undefined ? params.is_enrichment : false,
        role_data: params.role_data || null,
        agent_type: params.agent_type || 'intake_agent', // Use provided agent_type or default
        process_method: 'enrichment_agent' // Always force enrichment_agent regardless of input
      };

      console.log(`[RealtimeApi] Sending request with data:`, {
        role_id: requestData.role_id,
        transcript_id: requestData.transcript_id,
        is_enrichment: requestData.is_enrichment,
        process_method: requestData.process_method,
        hasRoleData: !!requestData.role_data,
        hasSignal: !!signal
      });

      // Check if we're already redirecting before even starting the request
      if ((window as WindowWithRedirect).__redirectingFromCall) {
        console.log('[RealtimeApi] Page is already redirecting, using keepalive fetch instead of axios');

        // Use fetch with keepalive for more reliable background processing during navigation
        try {
          // This will continue even if the page unloads
          fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/realtime/end-call`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              // Don't include auth token here since we're leaving the page
            },
            body: JSON.stringify(requestData),
            keepalive: true, // Critical for continuing after page unloads
          });

          return {
            status: 'success',
            message: 'Request sent with keepalive',
            detail: 'The call end request was sent with keepalive and will continue processing after navigation.'
          };
        } catch (fetchError) {
          console.warn('[RealtimeApi] Error using keepalive fetch:', fetchError);
          // Continue with normal flow as fallback
        }
      }

      // Set a timeout to avoid hanging connections
      // If a signal was provided, use it instead of creating our own
      const controller = signal ? undefined : new AbortController();
      const timeoutId = controller ? setTimeout(() => {
        try {
          controller.abort('Request timeout');
        } catch (timeoutError) {
          console.warn('[RealtimeApi] Error aborting on timeout:', timeoutError);
        }
      }, 30000) : undefined;

      // Merge signals if needed to ensure both timeouts and external aborts work
      let mergedSignal = signal;
      if (controller && signal) {
        // If both signals are provided, create a new AbortController that triggers on either signal
        const mergedController = new AbortController();

        const signalListener = () => {
          try {
            mergedController.abort('External signal aborted');
          } catch (mergeError) {
            console.warn('[RealtimeApi] Error merging abort signals:', mergeError);
          }
        };

        const controllerListener = () => {
          try {
            mergedController.abort('Timeout signal aborted');
          } catch (mergeError) {
            console.warn('[RealtimeApi] Error merging abort signals:', mergeError);
          }
        };

        signal.addEventListener('abort', signalListener);
        controller.signal.addEventListener('abort', controllerListener);

        // Clean up listeners when request completes
        setTimeout(() => {
          try {
            signal.removeEventListener('abort', signalListener);
            controller.signal.removeEventListener('abort', controllerListener);
          } catch (cleanupError) {
            console.warn('[RealtimeApi] Error cleaning up abort listeners:', cleanupError);
          }
        }, 35000);

        mergedSignal = mergedController.signal;
      } else if (controller) {
        mergedSignal = controller.signal;
      }

      try {
        const response = await apiClient.post(`/realtime/end-call`, requestData, {
          signal: mergedSignal,
          // Increase timeout for this specific request
          timeout: 30000
        });

        // Clear the timeout if we created one
        if (timeoutId) clearTimeout(timeoutId);

        console.log('[RealtimeApi] Call ended successfully with response:', response.data);

        // Handle both response formats by ensuring detail is available
        return {
          ...response.data,
          status: response.data.status || 'success',
          message: response.data.message || '',
          detail: response.data.detail || ''
        };
      } catch (apiError: unknown) {
        // Clear the timeout if we created one
        if (timeoutId) clearTimeout(timeoutId);

        // If the request was aborted, check if it was by our timeout or external signal
        if (apiError instanceof Error && (apiError.name === 'AbortError' || (apiError as ApiError).code === 'ECONNABORTED' || (apiError as ApiError).code === 'ERR_CANCELED')) {
          // If we're redirecting, this is expected
          if ((window as WindowWithRedirect).__redirectingFromCall) {
            console.log('RealtimeApi: Request aborted due to redirection');
            return {
              status: 'success',
              message: 'Request aborted due to redirection'
            };
          }
        }

        // Handle CORS or network errors gracefully
        if (apiError instanceof Error && (
          apiError.message.includes('Network Error') ||
          apiError.message.includes('CORS') ||
          !('response' in apiError)
        )) {
          // If we're redirecting, this is expected
          if ((window as WindowWithRedirect).__redirectingFromCall) {
            console.log('RealtimeApi: Network error during redirection');
            return {
              status: 'success',
              message: 'Network error during redirection'
            };
          }
        }

        // Re-throw the error
        throw apiError;
      }
    } catch (error) {
      // If we're redirecting, suppress error logs
      if ((window as WindowWithRedirect).__redirectingFromCall) {
        console.log('[RealtimeApi] Error during redirection - expected behavior:', error);

        // Try a background fetch as a last resort
        try {
          // This will continue even if the page unloads
          fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/realtime/end-call`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              role_id: params.role_id,
              transcript_id: params.transcript_id,
              user_id: params.user_id,
              is_enrichment: params.is_enrichment !== undefined ? params.is_enrichment : false,
              agent_type: params.agent_type || 'intake_agent',
              process_method: 'enrichment_agent'
            }),
            keepalive: true,
          }).catch(e => {
            // Ignore errors in the background fetch
            console.log('[RealtimeApi] Background fetch error (ignoring):', e);
          });
        } catch (fetchError) {
          // Ignore any errors from the fetch attempt
          console.warn('[RealtimeApi] Error using keepalive fetch as fallback:', fetchError);
        }

        return {
          status: 'canceled',
          message: 'Request canceled during navigation',
          detail: 'The call ended successfully but the response was canceled due to navigation. A background request was sent that should continue processing.'
        };
      }

      console.error(`[RealtimeApi] Error ending call:`, error);
      const errorResponse = handleApiError(error, {
        defaultErrorResponse: {
          detail: 'Failed to end call. The transcript may have been saved.'
        }
      });

      // Ensure we return a consistent format
      return {
        status: 'error',
        message: errorResponse.detail || 'Unknown error',
        detail: errorResponse.detail || ''
      };
    }
  },

  /**
   * End an interview session and process results
   * @param params Interview end parameters
   * @param signal Optional AbortSignal to cancel the request
   * @returns Success response or error
   */
  endInterview: async (params: {
    role_id: string;
    transcript_id: string;
    user_id: string;
  }, signal?: AbortSignal): Promise<{ status: string; message: string; results?: Record<string, unknown> } | { detail: string }> => {
    try {
      console.log(`RealtimeApi: Ending interview for role ${params.role_id} and transcript ${params.transcript_id}`);

      // Make sure we're using the OpenAI session ID (starts with 'sess_') if available
      let finalTranscriptId = params.transcript_id;

      // If we have a transcript ID that starts with 'sess_', use it
      if (params.transcript_id.startsWith('sess_')) {
        // Already using the correct format
      } else {
        // Try to get the session ID from localStorage as a fallback
        const storedSessionId = localStorage.getItem('currentSessionId');
        if (storedSessionId && storedSessionId.startsWith('sess_')) {
          finalTranscriptId = storedSessionId;
          console.log('Using stored session ID with OpenAI format:', finalTranscriptId);
        }
      }

      const response = await apiClient.post(`/realtime/end-interview`, {
        role_id: params.role_id,
        transcript_id: finalTranscriptId,
        user_id: params.user_id
      }, {
        signal: signal
      });

      console.log('RealtimeApi: Interview ended successfully');
      return response.data;
    } catch (error: any) {
      // If we're redirecting, suppress error logs
      if ((window as WindowWithRedirect).__redirectingFromCall && (error?.name === 'AbortError' || error?.code === 'ERR_CANCELED')) {
        console.log('RealtimeApi: Interview request canceled due to navigation - expected behavior');
        return {
          status: 'canceled',
          message: 'Request canceled due to navigation',
        };
      }

      console.error(`RealtimeApi: Error ending interview:`, error);
      return handleApiError(error, {
        defaultErrorResponse: {
          detail: 'Failed to end interview. The transcript may have been saved.'
        }
      });
    }
  },

  /**
   * Update a public interview transcript
   * This is a direct, unauthenticated call to ensure public transcripts are saved
   * @param sessionId The session ID
   * @param transcriptId The transcript ID
   * @param roleId The role ID
   * @param messages The conversation messages
   * @param applicationId Optional application ID
   * @returns Success response or error
   */
  updatePublicTranscript: async (
    sessionId: string,
    transcriptId: string,
    roleId: string,
    messages: Message[],
    applicationId?: string
  ): Promise<{ status: string; message: string }> => {
    try {
      // Log the session ID to help with debugging
      console.log(`RealtimeApi: Updating public transcript for session ${sessionId}`);

      // Make sure we're using the OpenAI session ID (starts with 'sess_') if available
      // But also support custom session IDs for non-OpenAI sessions
      let finalSessionId = sessionId;
      if (!sessionId.startsWith('sess_') && transcriptId && transcriptId.startsWith('sess_')) {
        console.log('Using transcript_id as session_id because it has the OpenAI format');
        finalSessionId = transcriptId;
      }

      const response = await apiClient.post('/realtime/update-public-transcript', {
        session_id: finalSessionId,
        transcript_id: transcriptId,
        role_id: roleId,
        messages,
        application_id: applicationId
      });

      console.log('RealtimeApi: Public transcript updated successfully');
      return response.data;
    } catch (error) {
      console.error(`RealtimeApi: Error updating public transcript:`, error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
};

/**
 * Realtime service for the application
 * Provides a properly typed and accessible service for realtime operations
 */
export const realtimeService = {
  /**
   * Create a default realtime session (intake agent)
   * @param roleId Optional role ID to associate with the session
   * @param isEnrichment Whether this is an enrichment call for an existing role
   * @returns Session response with session ID and client secret
   */
  createSession: async (roleId?: string, isEnrichment: boolean = false): Promise<SessionResponse | { detail: string }> => {
    return realtimeApi.createSession(roleId, isEnrichment);
  },

  /**
   * Create a realtime session with a specific agent type
   * @param agentType The agent type (e.g., 'intake_agent', 'interview_agent')
   * @returns Session response with session ID and client secret
   */
  createSessionWithAgent: async (agentType: string): Promise<SessionResponse | { detail: string }> => {
    return realtimeApi.createSessionWithAgent(agentType);
  },

  /**
   * Create an interview session
   * @param roleId The role ID
   * @param templateId The template ID
   * @returns Session response with session ID and client secret
   */
  createInterviewSession: async (roleId: string, templateId: string): Promise<SessionResponse | { detail: string }> => {
    return realtimeApi.createInterviewSession(roleId, templateId);
  },

  /**
   * Create a public interview session for candidates
   * @param roleId The role ID
   * @param templateId Optional template ID (defaults to screening template)
   * @param candidateId Optional candidate ID to link the session to a candidate
   * @param applicationId Optional application ID to link the session to an application
   * @param resumeText Optional resume text from the candidate
   * @param jobPosting Optional job posting text
   * @param candidateName Optional candidate name to display in the interview
   * @returns Session response with session ID and client secret
   */
  createPublicInterviewSession: async (
    roleId: string,
    templateId?: string,
    candidateId?: string,
    applicationId?: string,
    resumeText?: string,
    jobPosting?: string,
    candidateName?: string
  ): Promise<SessionResponse | { detail: string }> => {
    return realtimeApi.createPublicInterviewSession(roleId, templateId, candidateId, applicationId, resumeText, jobPosting, candidateName);
  },

  /**
   * End a call session and process the transcript
   * @param params Call parameters
   * @param signal Optional AbortSignal to cancel the request
   * @returns Success response or error
   */
  endCall: async (params: {
    role_id: string;
    transcript_id: string;
    user_id: string;
    is_enrichment?: boolean;
    role_data?: RoleData;
    process_method?: 'enrichment_agent' | 'direct_data';
    agent_type?: string;
  }, signal?: AbortSignal): Promise<{ status: string; message: string; detail?: string }> => {
    return realtimeApi.endCall(params, signal);
  },

  /**
   * End an interview session and process results
   * @param params Interview end parameters
   * @param signal Optional AbortSignal to cancel the request
   * @returns Success response or error
   */
  endInterview: async (params: {
    role_id: string;
    transcript_id: string;
    user_id: string;
  }, signal?: AbortSignal): Promise<{
    status: string;
    message: string;
    results?: Record<string, unknown>;
  } | { detail: string }> => {
    return realtimeApi.endInterview(params, signal);
  },

  /**
   * Update a public interview transcript
   * @param sessionId The session ID
   * @param transcriptId The transcript ID
   * @param roleId The role ID
   * @param messages The conversation messages
   * @param applicationId Optional application ID
   * @returns Success response or error
   */
  updatePublicTranscript: async (
    sessionId: string,
    transcriptId: string,
    roleId: string,
    messages: Message[],
    applicationId?: string
  ): Promise<{ status: string; message: string }> => {
    return realtimeApi.updatePublicTranscript(
      sessionId,
      transcriptId,
      roleId,
      messages,
      applicationId
    );
  }
};

// Export the API service
export default realtimeApi;