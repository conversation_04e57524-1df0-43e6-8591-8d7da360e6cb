import { AxiosProgressEvent } from 'axios';
import { apiClient } from '@/lib/api/client/axios';

/**
 * API types for resume evaluation
 */
export interface ResumeEvaluationRequest {
    resume_text: string;
    job_posting: string;
    model?: string;
    basic_evaluation?: boolean;
}

export interface SkillEvaluation {
    score: number;
    evaluation: string;
    strengths: string[];
    gaps: string[];
}

export interface EvaluationScorecard {
    technicalSkills: SkillEvaluation;
    softSkills: SkillEvaluation;
    experienceRelevance: SkillEvaluation;
    educationCertifications: SkillEvaluation;
    achievementsImpact: SkillEvaluation;
    overallScore: number;
    overallEvaluation: string;
}

export interface EvaluationRecommendation {
    decision: 'PASS' | 'FAIL';
    confidence: 'LOW' | 'MEDIUM' | 'HIGH';
    reasoning: string;
}

export interface EvaluationFeedback {
    candidateStrengths: string[];
    improvementAreas: string[];
    interviewFocus: string[];
}

export interface ResumeEvaluationResponse {
    scorecard: EvaluationScorecard;
    recommendation: EvaluationRecommendation;
    feedback: EvaluationFeedback;
    metadata: {
        timestamp: string;
        model: string;
        resumeLength: number;
        jobPostingLength: number;
        filename?: string;
        content_type?: string;
    };
    evaluation_id?: string;
    application_id?: string;
}

export interface BasicEvaluationResponse {
    overallScore: number;
    decision: 'PASS' | 'FAIL';
    confidence: 'LOW' | 'MEDIUM' | 'HIGH';
    reasoning: string;
    keyStrengths: string[];
    keyGaps: string[];
    metadata: {
        timestamp: string;
        model: string;
        resumeLength: number;
        jobPostingLength: number;
        evaluationType: 'basic';
        filename?: string;
        content_type?: string;
    };
    evaluation_id?: string;
    application_id?: string;
}

/**
 * Upload a resume file for evaluation against a job posting
 *
 * @param resumeFile The resume file to evaluate
 * @param jobPosting The job posting text to evaluate against
 * @param options Additional options
 * @returns The evaluation results
 */
export const evaluateResumeUpload = async (
    resumeFile: File,
    jobPosting?: string,
    options?: {
        jobId?: string;
        model?: string;
        basicEvaluation?: boolean;
        onProgress?: (progress: number) => void;
        candidateEmail?: string;
        candidateName?: string;
    }
): Promise<ResumeEvaluationResponse | BasicEvaluationResponse> => {
    try {
        const formData = new FormData();
        formData.append('resume_file', resumeFile);

        if (jobPosting) {
            formData.append('job_posting', jobPosting);
        }

        const params: Record<string, string | boolean> = {};

        if (options?.jobId) {
            params.job_id = options.jobId;
        }

        if (options?.model) {
            params.model = options.model;
        }

        if (options?.basicEvaluation) {
            params.basic_evaluation = options.basicEvaluation;
        }

        // Prepare headers with candidate information
        const headers: Record<string, string> = {
            'Content-Type': 'multipart/form-data',
        };

        // Add candidate information to headers if available
        if (options?.candidateEmail) {
            headers['X-Candidate-Email'] = options.candidateEmail;
            console.log(`Setting X-Candidate-Email header: ${options.candidateEmail}`);
            // Also add to form data for redundancy
            formData.append('candidate_email', options.candidateEmail);
        } else {
            // Try to get from localStorage as fallback
            const email = localStorage.getItem('lastApplicationEmail');
            if (email) {
                headers['X-Candidate-Email'] = email;
                console.log(`Setting X-Candidate-Email header from localStorage: ${email}`);
                // Also add to form data for redundancy
                formData.append('candidate_email', email);
            }
        }

        if (options?.candidateName) {
            headers['X-Candidate-Name'] = options.candidateName;
            console.log(`Setting X-Candidate-Name header: ${options.candidateName}`);
            // Also add to form data for redundancy
            formData.append('candidate_name', options.candidateName);
        } else {
            // Try to get from localStorage as fallback
            const name = localStorage.getItem('lastApplicationName');
            if (name) {
                headers['X-Candidate-Name'] = name;
                console.log(`Setting X-Candidate-Name header from localStorage: ${name}`);
                // Also add to form data for redundancy
                formData.append('candidate_name', name);
            }
        }

        const response = await apiClient.post<ResumeEvaluationResponse | BasicEvaluationResponse>(
            '/resume-evaluation/evaluate-upload',
            formData,
            {
                params,
                headers,
                onUploadProgress: (progressEvent: AxiosProgressEvent) => {
                    if (options?.onProgress && progressEvent.total) {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        options.onProgress(percentCompleted);
                    }
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error('Error evaluating resume:', error);
        throw error;
    }
};

/**
 * Evaluate resume text against a job posting
 *
 * @param resumeText The resume text to evaluate
 * @param jobPosting The job posting text to evaluate against
 * @param options Additional options
 * @returns The evaluation results
 */
export const evaluateResumeText = async (
    resumeText: string,
    jobPosting: string,
    options?: {
        model?: string;
        basicEvaluation?: boolean;
    }
): Promise<ResumeEvaluationResponse | BasicEvaluationResponse> => {
    try {
        const request: ResumeEvaluationRequest = {
            resume_text: resumeText,
            job_posting: jobPosting,
            model: options?.model,
            basic_evaluation: options?.basicEvaluation,
        };

        const response = await apiClient.post<ResumeEvaluationResponse | BasicEvaluationResponse>(
            '/resume-evaluation/evaluate',
            request
        );

        return response.data;
    } catch (error) {
        console.error('Error evaluating resume text:', error);
        throw error;
    }
};