import {
  evaluateResumeUpload,
  evaluateResumeText,
  ResumeEvaluationResponse,
  BasicEvaluationResponse
} from './api';
import { uploadResume, saveResumeEvaluation } from '@/lib/firebase';
import { trackApiPerformance } from '@/lib/analytics/resume-evaluation';
import { v4 as uuidv4 } from 'uuid';



/**
 * ResumeService provides a higher-level interface for resume processing and evaluation
 * with integrated Firebase storage and caching capabilities.
 */
class ResumeService {
  // LRU cache for evaluation results to avoid duplicate API calls
  private evaluationCache: Map<string, ResumeEvaluationResponse | BasicEvaluationResponse> = new Map();
  private readonly MAX_CACHE_SIZE = 20;

  /**
   * Upload and evaluate a resume against a job posting
   *
   * @param resumeFile The resume file to evaluate
   * @param roleId The ID of the role to evaluate against
   * @param jobPosting The job posting text (if not provided, will be fetched based on roleId)
   * @param options Additional options for evaluation and storage
   * @returns The evaluation results and applicationId
   */
  async evaluateResumeWithUpload(
    resumeFile: File,
    roleId: string,
    jobPosting?: string,
    options?: {
      applicationId?: string;
      model?: string;
      basicEvaluation?: boolean;
      onProgress?: (progress: number) => void;
      saveToFirebase?: boolean;
      storeEvaluationResults?: boolean;
    }
  ): Promise<{
    evaluation: ResumeEvaluationResponse | BasicEvaluationResponse;
    applicationId: string;
    resumeUrl: string;
  }> {
    try {
      // Generate or use provided application ID
      let applicationId = options?.applicationId || uuidv4();

      // Ensure we have the application ID in localStorage for continuity
      localStorage.setItem('applicationId', applicationId);

      // Create a cache key based on the file and job posting
      const cacheKey = `${resumeFile.name}-${resumeFile.size}-${resumeFile.lastModified}-${roleId}`;

      // Check cache first (if enabled)
      if (this.evaluationCache.has(cacheKey)) {
        const cachedResult = this.evaluationCache.get(cacheKey);
        console.log('Using cached evaluation result');
        if (cachedResult) {
          return {
            evaluation: cachedResult,
            applicationId,
            resumeUrl: 'cached-url' // This is a placeholder
          };
        }
      }

      // Track progress
      let uploadProgress = 0;
      let evaluationProgress = 0;

      const updateProgress = (progress: number, stage: 'upload' | 'evaluation') => {
        if (stage === 'upload') {
          uploadProgress = progress * 0.5; // Upload counts for 50% of total progress
        } else {
          evaluationProgress = progress * 0.5; // Evaluation counts for 50% of total progress
        }

        if (options?.onProgress) {
          options.onProgress(uploadProgress + evaluationProgress);
        }
      };

      // Upload the resume to Firebase
      let resumeUrl = '';

      if (options?.saveToFirebase !== false) {
        // Get the email from localStorage if available (to use as candidateId)
        const email = localStorage.getItem('lastApplicationEmail') || '';

        // Make sure we have a candidate ID (email) in localStorage
        if (email) {
          localStorage.setItem('candidateId', email);

          // Ensure we have synchronized application data in localStorage
          this._syncApplicationDataInLocalStorage(applicationId, email);
        }

        // First, call the evaluation API to get the parsed text
        const apiStartTime = Date.now();
        let apiSuccess = false;
        let evaluationResult: ResumeEvaluationResponse | BasicEvaluationResponse;

        try {
          // Get candidate information from localStorage
          const candidateEmail = localStorage.getItem('lastApplicationEmail') || undefined;
          const candidateName = localStorage.getItem('lastApplicationName') || undefined;

          // Call the evaluation API
          evaluationResult = await evaluateResumeUpload(
            resumeFile,
            jobPosting,
            {
              jobId: roleId,
              model: options?.model,
              basicEvaluation: options?.basicEvaluation,
              onProgress: (progress) => updateProgress(progress, 'evaluation'),
              candidateEmail,
              candidateName
            }
          );

          apiSuccess = true;
        } catch (error) {
          apiSuccess = false;
          throw error; // Re-throw to handle in outer catch
        } finally {
          // Track API performance asynchronously without awaiting
          // This now has internal checks for public context
          try {
            const apiEndTime = Date.now();
            const responseTime = apiEndTime - apiStartTime;
            const endpoint = options?.basicEvaluation
              ? '/resume-evaluation/evaluate-upload-basic'
              : '/resume-evaluation/evaluate-upload';

            // Fire and forget - don't let tracking issues affect the main flow
            trackApiPerformance(endpoint, responseTime, apiSuccess)
              .catch(() => {}); // Silently catch any errors
          } catch {
            // Ignore any error in the tracking itself
          }
        }

        // Check if the backend returned an application ID
        if (evaluationResult.application_id) {
          console.log(`Using application ID from backend: ${evaluationResult.application_id}`);
          // Update our application ID to use the one from the backend
          applicationId = evaluationResult.application_id;

          // Store the application ID in localStorage for continuity
          localStorage.setItem('applicationId', applicationId);
          localStorage.setItem('lastApplicationId', applicationId);
        }

        // Upload the resume - we can't pass parsed text directly from this API
        try {
          resumeUrl = await uploadResume(
            roleId,
            resumeFile,
            applicationId
          );

          updateProgress(100, 'upload');
        } catch (uploadError) {
          console.error('Error uploading resume:', uploadError);
          resumeUrl = `mock-resume-url-${roleId}-${applicationId}.${resumeFile.name.split('.').pop() || 'pdf'}`;
        }

        // Extract parsed resume text if available in the response metadata
        const metadata = evaluationResult.metadata as any;
        const parsedResumeText = metadata?.parsedText ||
          metadata?.resumeText ||
          '';

        // Store in localStorage for interview context and backup
        if (parsedResumeText) {
          try {
            localStorage.setItem('parsedResumeText', parsedResumeText);
            console.log('Saved parsed resume text to localStorage directly (length:', parsedResumeText.length, ')');
          } catch (e) {
            console.error('Failed to save parsed resume text to localStorage:', e);
          }
        }

        // Always try to store evaluation results in localStorage for reference
        if (options?.storeEvaluationResults !== false) {
          try {
            // Store evaluation results in a non-blocking way (don't await)
            saveResumeEvaluation(
              applicationId,
              evaluationResult,
              parsedResumeText
            ).then(evalId => {
              console.log(`Saved evaluation ${evalId} to localStorage for application ${applicationId}`);
            }).catch((error: unknown) => {
              // Just log the error and don't rethrow - the evaluation was successful even if saving failed
              console.error('Error saving evaluation to localStorage:', error);
            });
          } catch (error: unknown) {
            // Just log the error and continue - don't disrupt the flow
            console.error('Error initiating evaluation save:', error);
          }
        }

        // Store in cache
        this._addToCache(cacheKey, evaluationResult);

        return {
          evaluation: evaluationResult,
          applicationId,
          resumeUrl
        };
      } else {
        // Just evaluate without storing in Firebase
        const apiStartTime = Date.now();
        let apiSuccess = false;
        let evaluationResult: ResumeEvaluationResponse | BasicEvaluationResponse;

        try {
          // Get candidate information from localStorage
          const candidateEmail = localStorage.getItem('lastApplicationEmail') || undefined;
          const candidateName = localStorage.getItem('lastApplicationName') || undefined;

          // Call the evaluation API
          evaluationResult = await evaluateResumeUpload(
            resumeFile,
            jobPosting,
            {
              jobId: roleId,
              model: options?.model,
              basicEvaluation: options?.basicEvaluation,
              onProgress: (progress) => updateProgress(progress, 'evaluation'),
              candidateEmail,
              candidateName
            }
          );

          apiSuccess = true;
        } catch (error) {
          apiSuccess = false;
          throw error; // Re-throw to handle in outer catch
        } finally {
          // Track API performance asynchronously without awaiting
          // This now has internal checks for public context
          try {
            const apiEndTime = Date.now();
            const responseTime = apiEndTime - apiStartTime;
            const endpoint = options?.basicEvaluation
              ? '/resume-evaluation/evaluate-upload-basic'
              : '/resume-evaluation/evaluate-upload';

            // Fire and forget - don't let tracking issues affect the main flow
            trackApiPerformance(endpoint, responseTime, apiSuccess)
              .catch(() => {}); // Silently catch any errors
          } catch {
            // Ignore any error in the tracking itself
          }
        }

        // Store in cache
        this._addToCache(cacheKey, evaluationResult);

        return {
          evaluation: evaluationResult,
          applicationId,
          resumeUrl: ''
        };
      }
    } catch (error) {
      console.error('Error in evaluateResumeWithUpload:', error);
      throw error;
    }
  }

  /**
   * Evaluate resume text against a job posting
   *
   * @param resumeText The resume text to evaluate
   * @param jobPosting The job posting text
   * @param options Additional options
   * @returns The evaluation results
   */
  async evaluateResumeText(
    resumeText: string,
    jobPosting: string,
    options?: {
      model?: string;
      basicEvaluation?: boolean;
      applicationId?: string;
      roleId?: string;
      saveToFirebase?: boolean;
    }
  ): Promise<{
    evaluation: ResumeEvaluationResponse | BasicEvaluationResponse;
    applicationId?: string;
  }> {
    try {
      // Generate a cache key based on text content
      const cacheKey = `text-${Buffer.from(resumeText.slice(0, 100)).toString('base64')}-${Buffer.from(jobPosting.slice(0, 100)).toString('base64')}`;

      // Check cache first
      if (this.evaluationCache.has(cacheKey)) {
        const cachedResult = this.evaluationCache.get(cacheKey);
        console.log('Using cached evaluation result');
        if (cachedResult) {
          return { evaluation: cachedResult };
        }
      }

      // Evaluate the resume text with performance tracking
      const apiStartTime = Date.now();
      let apiSuccess = false;
      let evaluationResult: ResumeEvaluationResponse | BasicEvaluationResponse;

      try {
        // Call the evaluation API
        evaluationResult = await evaluateResumeText(
          resumeText,
          jobPosting,
          {
            model: options?.model,
            basicEvaluation: options?.basicEvaluation
          }
        );

        apiSuccess = true;
      } catch (error) {
        apiSuccess = false;
        throw error; // Re-throw to handle in outer catch
      } finally {
        // Track API performance asynchronously without awaiting
        // This now has internal checks for public context
        try {
          const apiEndTime = Date.now();
          const responseTime = apiEndTime - apiStartTime;
          const endpoint = options?.basicEvaluation
            ? '/resume-evaluation/evaluate-basic'
            : '/resume-evaluation/evaluate';

          // Fire and forget - don't let tracking issues affect the main flow
          trackApiPerformance(endpoint, responseTime, apiSuccess)
            .catch(() => {}); // Silently catch any errors
        } catch {
          // Ignore any error in the tracking itself
        }
      }

      // Store in cache
      this._addToCache(cacheKey, evaluationResult);

      // Store in Firebase if requested
      if (options?.saveToFirebase && options.applicationId && options.roleId) {
        await saveResumeEvaluation(
          options.applicationId,
          evaluationResult,
          resumeText // This parameter is already a string
        );

        return {
          evaluation: evaluationResult,
          applicationId: options.applicationId
        };
      }

      return { evaluation: evaluationResult };
    } catch (error) {
      console.error('Error in evaluateResumeText:', error);
      throw error;
    }
  }

  /**
   * Add an evaluation result to the cache, maintaining the LRU cache size limit
   */
  private _addToCache(key: string, value: ResumeEvaluationResponse | BasicEvaluationResponse): void {
    // If cache is at max size, remove the oldest entry
    if (this.evaluationCache.size >= this.MAX_CACHE_SIZE) {
      const oldestKey = this.evaluationCache.keys().next().value;
      if (oldestKey) {
        this.evaluationCache.delete(oldestKey);
      }
    }

    // Add new entry
    this.evaluationCache.set(key, value);
  }

  /**
   * Clear the evaluation cache
   */
  clearCache(): void {
    this.evaluationCache.clear();
    console.log('Resume evaluation cache cleared');
  }

  /**
   * Helper method to ensure application data is properly synchronized in localStorage
   * This addresses issues where application data is missing when needed
   */
  private _syncApplicationDataInLocalStorage(applicationId: string, candidateId: string): void {
    try {
      // Check if we have app data in localStorage
      const appDataKey = `application_${applicationId}`;
      const existingAppData = localStorage.getItem(appDataKey);

      if (!existingAppData) {
        // No application data found, check for other data elements we can use
        const fullName = localStorage.getItem('lastApplicationName') || 'Candidate';
        const email = candidateId; // Candidate ID is email for public candidates

        // Get any resume URL if available
        const resumeUrl = localStorage.getItem(`resumeUrl_${applicationId}`) || '';

        // Create a minimal application structure
        const minimalAppData = {
          id: applicationId,
          candidateId,
          email,
          fullName,
          resumeUrl,
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Save to localStorage
        localStorage.setItem(appDataKey, JSON.stringify(minimalAppData));
        console.log(`Created minimal application data in localStorage for ID: ${applicationId}`);
      } else {
        // Application data exists, ensure it has candidateId
        try {
          const parsedData = JSON.parse(existingAppData);
          if (!parsedData.candidateId && candidateId) {
            parsedData.candidateId = candidateId;
            localStorage.setItem(appDataKey, JSON.stringify(parsedData));
            console.log(`Updated application data in localStorage with candidateId: ${candidateId}`);
          }
        } catch (parseError) {
          console.error('Error parsing existing application data:', parseError);
        }
      }
    } catch (error) {
      console.error('Error synchronizing application data in localStorage:', error);
    }
  }
}

// Export singleton instance
export const resumeService = new ResumeService();