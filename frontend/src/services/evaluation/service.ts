import {
  requestEvaluation,
  getEvaluation,
  getEvaluationsByRole,
  getEvaluationsByInterview,
  requestPublicEvaluation,
  getPublicEvaluation,
  linkEvaluationToInterview,
  directLinkEvaluation,
  Evaluation,
  EvaluationResponse
} from './api';

/**
 * EvaluationService provides a higher-level interface for interview evaluation
 * with integrated caching capabilities.
 */
export class EvaluationService {
  private static instance: EvaluationService;
  private cache: Map<string, Evaluation> = new Map();
  private readonly MAX_CACHE_SIZE = 20;

  /**
   * Get the singleton instance of the EvaluationService
   *
   * @returns The EvaluationService instance
   */
  public static getInstance(): EvaluationService {
    if (!EvaluationService.instance) {
      EvaluationService.instance = new EvaluationService();
    }
    return EvaluationService.instance;
  }

  /**
   * Reset the singleton instance (for testing purposes only)
   */
  public static resetInstance(): void {
    EvaluationService.instance = undefined as unknown as EvaluationService;
  }

  /**
   * Request an evaluation for an interview (authenticated)
   *
   * @param interviewId The interview ID
   * @param roleId The role ID
   * @param applicationId Optional application ID
   * @param templateId Optional template ID
   * @param stageName Optional stage name
   * @param stageIndex Optional stage index
   * @returns The evaluation ID
   */
  public async evaluateInterview(
    interviewId: string,
    roleId: string,
    applicationId?: string,
    templateId?: string,
    stageName?: string,
    stageIndex?: number
  ): Promise<string> {
    try {
      return await requestEvaluation({
        interviewId,
        roleId,
        applicationId,
        templateId,
        stageName,
        stageIndex
      });
    } catch (error) {
      console.error('Error evaluating interview:', error);
      throw error;
    }
  }

  /**
   * Request an evaluation for a public interview
   *
   * @param interviewId The interview ID
   * @param roleId The role ID
   * @param applicationId Optional application ID
   * @param templateId Optional template ID
   * @param stageName Optional stage name
   * @param stageIndex Optional stage index
   * @returns The evaluation ID
   */
  public async evaluatePublicInterview(
    interviewId: string,
    roleId: string,
    applicationId?: string,
    templateId?: string,
    stageName?: string,
    stageIndex?: number
  ): Promise<string> {
    try {
      return await requestPublicEvaluation({
        interviewId,
        roleId,
        applicationId,
        templateId,
        stageName,
        stageIndex
      });
    } catch (error) {
      console.error('Error evaluating public interview:', error);
      throw error;
    }
  }

  /**
   * Get an evaluation by ID with caching
   *
   * @param evaluationId The evaluation ID
   * @param forceRefresh Whether to force a refresh from the API
   * @param isPublic Whether this is a public evaluation
   * @returns The evaluation
   */
  public async getEvaluation(
    evaluationId: string,
    forceRefresh: boolean = false,
    isPublic: boolean = false
  ): Promise<Evaluation> {
    try {
      console.log(`Getting evaluation ${evaluationId}, forceRefresh=${forceRefresh}, isPublic=${isPublic}`);

      // Check cache first if not forcing refresh
      if (!forceRefresh && this.cache.has(evaluationId)) {
        const cachedEvaluation = this.cache.get(evaluationId);

        // If the evaluation is completed or failed, return from cache
        // If it's pending, we should refresh to check for updates
        if (cachedEvaluation && cachedEvaluation.status !== 'pending') {
          console.log(`Returning cached evaluation for ${evaluationId}`);
          return cachedEvaluation;
        }
      }

      // Fetch from API
      console.log(`Fetching evaluation ${evaluationId} from API`);
      const evaluation = isPublic
        ? await getPublicEvaluation(evaluationId)
        : await getEvaluation(evaluationId);

      // Create a properly formatted Evaluation object
      let evaluationData: Evaluation;

      if (evaluation && evaluation.data) {
        // Map the API response to the Evaluation type
        evaluationData = {
          id: evaluationId,
          roleId: evaluation.data.roleId,
          templateId: evaluation.data.templateId,
          candidateId: evaluation.data.candidateId,
          applicationId: evaluation.data.applicationId,
          interviewId: evaluation.data.interviewId,
          scores: evaluation.data.scores || {},
          feedback: evaluation.data.feedback || '',
          recommendation: evaluation.data.recommendation,
          createdAt: new Date(),
          status: evaluation.data.status || 'completed',
          data: evaluation.data as any
        };
      } else {
        // Handle case where evaluation doesn't have the expected structure
        evaluationData = {
          id: evaluationId,
          createdAt: new Date(),
          status: 'unknown',
          data: (evaluation as any)?.evaluation?.data || evaluation?.data || {}
        };
      }

      // Update cache
      this._updateCache(evaluationId, evaluationData);

      console.log(`Successfully fetched evaluation ${evaluationId}`);
      return evaluationData;
    } catch (error) {
      console.error(`Error getting evaluation ${evaluationId}:`, error);
      throw error;
    }
  }

  /**
   * Get evaluations for a role
   *
   * @param roleId The role ID
   * @returns The evaluations
   */
  public async getEvaluationsByRole(roleId: string): Promise<Evaluation[]> {
    try {
      const evaluations = await getEvaluationsByRole(roleId);

      // Convert to Evaluation type and update cache
      const evaluationData: Evaluation[] = evaluations.map(evaluation => {
        let evalData: Evaluation;

        if ('data' in evaluation && evaluation.data) {
          // Handle EvaluationResponse type
          const evalResponse = evaluation as EvaluationResponse;
          evalData = {
            id: evalResponse.data.id || '',
            roleId: evalResponse.data.roleId,
            templateId: evalResponse.data.templateId,
            candidateId: evalResponse.data.candidateId,
            applicationId: evalResponse.data.applicationId,
            interviewId: evalResponse.data.interviewId,
            scores: evalResponse.data.scores,
            feedback: evalResponse.data.feedback,
            recommendation: evalResponse.data.recommendation,
            createdAt: new Date(),
            updatedAt: evalResponse.data.updatedAt,
            status: evalResponse.data.status,
            error: evalResponse.data.error,
            data: evalResponse.data.data
          };
        } else {
          // Handle direct Evaluation type
          // Create a safe evaluation object with default values
          evalData = {
            id: '',
            roleId: '',
            templateId: '',
            candidateId: '',
            applicationId: '',
            interviewId: '',
            scores: {},
            feedback: '',
            recommendation: undefined,
            createdAt: new Date(),
            updatedAt: '',
            status: 'unknown',
            error: '',
            data: undefined
          };

          // Copy any available properties from the evaluation object using type assertions
          const evalObj = evaluation as Record<string, any>;
          if ('id' in evalObj) evalData.id = evalObj.id as string;
          if ('roleId' in evalObj) evalData.roleId = evalObj.roleId as string;
          if ('templateId' in evalObj) evalData.templateId = evalObj.templateId as string;
          if ('candidateId' in evalObj) evalData.candidateId = evalObj.candidateId as string;
          if ('applicationId' in evalObj) evalData.applicationId = evalObj.applicationId as string;
          if ('interviewId' in evalObj) evalData.interviewId = evalObj.interviewId as string;
          if ('scores' in evalObj) evalData.scores = evalObj.scores as Record<string, number>;
          if ('feedback' in evalObj) evalData.feedback = evalObj.feedback as string;
          if ('recommendation' in evalObj && ['hire', 'reject', 'consider'].includes(evalObj.recommendation)) {
            evalData.recommendation = evalObj.recommendation as 'hire' | 'reject' | 'consider';
          }
          if ('createdAt' in evalObj) {
            evalData.createdAt = evalObj.createdAt instanceof Date ?
              evalObj.createdAt : new Date(evalObj.createdAt || Date.now());
          }
          if ('updatedAt' in evalObj) evalData.updatedAt = evalObj.updatedAt as string;
          if ('status' in evalObj) evalData.status = evalObj.status as string;
          if ('error' in evalObj) evalData.error = evalObj.error as string;
          // Skip data property as it's complex and not needed for most operations
        }

        // Update cache
        this._updateCache(evalData.id, evalData);

        return evalData;
      });

      return evaluationData;
    } catch (error) {
      console.error('Error getting evaluations by role:', error);
      throw error;
    }
  }

  /**
   * Link an evaluation to an interview
   *
   * This is used when an interview doesn't have an evaluation_id but the application has evaluation data
   *
   * @param applicationId The application ID
   * @param interviewId The interview ID
   * @returns The evaluation ID
   */
  public async linkEvaluationToInterview(applicationId: string, interviewId: string): Promise<string> {
    try {
      return await linkEvaluationToInterview(applicationId, interviewId);
    } catch (error) {
      console.error('Error linking evaluation to interview:', error);
      throw error;
    }
  }

  /**
   * Directly link a specific evaluation ID to an interview
   *
   * This is used when we know the exact evaluation ID to link
   *
   * @param interviewId The interview ID
   * @param evaluationId The evaluation ID to link
   * @returns The evaluation ID
   */
  public async directLinkEvaluation(interviewId: string, evaluationId: string): Promise<string> {
    try {
      return await directLinkEvaluation(interviewId, evaluationId);
    } catch (error) {
      console.error('Error directly linking evaluation:', error);
      throw error;
    }
  }

  /**
   * Get evaluations for an interview
   *
   * @param interviewId The interview ID
   * @returns The evaluations
   */
  public async getEvaluationsByInterview(interviewId: string): Promise<Evaluation[]> {
    try {
      const evaluations = await getEvaluationsByInterview(interviewId);

      // Convert to Evaluation type and update cache
      const evaluationData: Evaluation[] = evaluations.map(evaluation => {
        let evalData: Evaluation;

        if ('data' in evaluation && evaluation.data) {
          // Handle EvaluationResponse type
          const evalResponse = evaluation as EvaluationResponse;
          evalData = {
            id: evalResponse.data.id || '',
            roleId: evalResponse.data.roleId,
            templateId: evalResponse.data.templateId,
            candidateId: evalResponse.data.candidateId,
            applicationId: evalResponse.data.applicationId,
            interviewId: evalResponse.data.interviewId,
            scores: evalResponse.data.scores,
            feedback: evalResponse.data.feedback,
            recommendation: evalResponse.data.recommendation,
            createdAt: new Date(),
            updatedAt: evalResponse.data.updatedAt,
            status: evalResponse.data.status,
            error: evalResponse.data.error,
            data: evalResponse.data.data
          };
        } else {
          // Handle direct Evaluation type
          // Create a safe evaluation object with default values
          evalData = {
            id: '',
            roleId: '',
            templateId: '',
            candidateId: '',
            applicationId: '',
            interviewId: '',
            scores: {},
            feedback: '',
            recommendation: undefined,
            createdAt: new Date(),
            updatedAt: '',
            status: 'unknown',
            error: '',
            data: undefined
          };

          // Copy any available properties from the evaluation object using type assertions
          const evalObj = evaluation as Record<string, any>;
          if ('id' in evalObj) evalData.id = evalObj.id as string;
          if ('roleId' in evalObj) evalData.roleId = evalObj.roleId as string;
          if ('templateId' in evalObj) evalData.templateId = evalObj.templateId as string;
          if ('candidateId' in evalObj) evalData.candidateId = evalObj.candidateId as string;
          if ('applicationId' in evalObj) evalData.applicationId = evalObj.applicationId as string;
          if ('interviewId' in evalObj) evalData.interviewId = evalObj.interviewId as string;
          if ('scores' in evalObj) evalData.scores = evalObj.scores as Record<string, number>;
          if ('feedback' in evalObj) evalData.feedback = evalObj.feedback as string;
          if ('recommendation' in evalObj && ['hire', 'reject', 'consider'].includes(evalObj.recommendation)) {
            evalData.recommendation = evalObj.recommendation as 'hire' | 'reject' | 'consider';
          }
          if ('createdAt' in evalObj) {
            evalData.createdAt = evalObj.createdAt instanceof Date ?
              evalObj.createdAt : new Date(evalObj.createdAt || Date.now());
          }
          if ('updatedAt' in evalObj) evalData.updatedAt = evalObj.updatedAt as string;
          if ('status' in evalObj) evalData.status = evalObj.status as string;
          if ('error' in evalObj) evalData.error = evalObj.error as string;
          // Skip data property as it's complex and not needed for most operations
        }

        // Update cache
        this._updateCache(evalData.id, evalData);

        return evalData;
      });

      return evaluationData;
    } catch (error) {
      console.error('Error getting evaluations by interview:', error);
      throw error;
    }
  }

  /**
   * Clear cache for an evaluation or all evaluations
   *
   * @param evaluationId Optional evaluation ID to clear from cache
   */
  public clearCache(evaluationId?: string): void {
    if (!evaluationId) {
      console.log('Clearing entire evaluation cache');
      this.cache.clear();
      return;
    }

    if (this.cache.has(evaluationId)) {
      console.log(`Clearing cache for evaluation ${evaluationId}`);
      this.cache.delete(evaluationId);
    } else {
      console.log(`No cache entry found for evaluation ${evaluationId}`);
    }
  }

  /**
   * Update the cache with a new evaluation
   *
   * @param evaluationId The evaluation ID
   * @param evaluation The evaluation
   */
  private _updateCache(evaluationId: string, evaluation: Evaluation | any): void {
    if (!evaluationId) {
      console.warn('Attempted to cache evaluation with no ID');
      // Generate a temporary ID instead of returning
      const tempId = `temp-eval-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
      console.warn(`Generated temporary ID for caching: ${tempId}`);
      evaluationId = tempId;
    }

    console.log(`Updating cache for evaluation ${evaluationId}`);

    // Convert EvaluationResponse to Evaluation if needed
    const evaluationData: Evaluation = evaluation.data ? {
      ...evaluation.data,
      id: evaluationId,
      createdAt: evaluation.data.createdAt || new Date()
    } : {
      ...evaluation,
      id: evaluation.id || evaluationId,
      createdAt: evaluation.createdAt || new Date()
    };

    // Ensure the ID in the evaluation data matches the provided ID
    if (evaluationData.id !== evaluationId) {
      console.warn(`Evaluation ID mismatch: ${evaluationData.id} vs ${evaluationId}, fixing...`);
      evaluationData.id = evaluationId;
    }

    // Add to cache
    this.cache.set(evaluationId, evaluationData);
    console.log(`Cache updated for evaluation ${evaluationId}, cache size: ${this.cache.size}`);

    // Implement LRU cache - remove oldest entries if cache is too large
    if (this.cache.size > this.MAX_CACHE_SIZE) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        console.log(`Removing oldest cache entry: ${oldestKey}`);
        this.cache.delete(oldestKey);
      }
    }
  }
}

// Export singleton instance
export const evaluationService = EvaluationService.getInstance();
