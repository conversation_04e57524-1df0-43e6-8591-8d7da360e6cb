/**
 * Types for evaluation data
 */

export interface EvaluationMetadata {
  application_id?: string;
  evaluated_at?: string;
  evaluated_by?: string;
  interview_id?: string;
  role_id?: string;
}

export interface EvaluationSummary {
  candidate_name?: string;
  confidence?: string;
  decision?: string;
  minimum_pass_rate?: number;
  overall_score?: number;
  role?: string;
  summary?: string;
}

export interface QuestionAnalysis {
  question: string;
  answer: string;
  evaluation: string;
  related_competencies?: string[];
  strengths?: string[];
  weaknesses?: string[];
}

export interface ScorecardItem {
  competency: string;
  reasoning: string;
  score: number;
  weight: number;
  weighted_score: number;
}

export interface BetweenTheLinesItem {
  criteria: string;
  observation: string;
  impact: string;
}

export interface DisqualifierCheck {
  criteria: string;
  evidence: string;
  explanation: string;
  triggered: boolean;
}

export interface DecisionReasoning {
  final_recommendation?: string;
  key_factors?: string[];
  strengths?: string[];
  concerns?: string[];
}

export interface EvaluationData {
  evaluation_summary?: EvaluationSummary;
  metadata?: EvaluationMetadata;
  question_analysis?: QuestionAnalysis[];
  scorecard_evaluation?: ScorecardItem[];
  between_the_lines?: BetweenTheLinesItem[];
  disqualifier_check?: DisqualifierCheck[];
  decision_reasoning?: DecisionReasoning;
  
  // Legacy fields for backward compatibility
  decision?: string;
  overallScore?: number;
  summary?: string;
  criteria?: any[];
  questionAssessments?: any[];
  strengths?: string[];
  weaknesses?: string[];
}

export interface Evaluation {
  id: string;
  data?: EvaluationData;
  createdAt: Date;
  roleId?: string;
  applicationId?: string;
  interviewId?: string;
}
