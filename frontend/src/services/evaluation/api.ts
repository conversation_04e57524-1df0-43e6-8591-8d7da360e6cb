import { apiClient } from '@/lib/api/client/axios';

/**
 * API types for interview evaluation
 */
export interface EvaluationRequest {
  interviewId: string;
  roleId: string;
  applicationId?: string;
  templateId?: string;
  stageName?: string;
  stageIndex?: number;
}

export interface EvaluationCriterion {
  name: string;
  score: number;
  feedback: string;
}

export interface QuestionAssessment {
  questionId: string;
  question: string;
  answer: string;
  score: number;
  feedback: string;
}

export interface EvaluationData {
  overallScore: number;
  decision: 'Go' | 'No Go' | 'Maybe';
  criteria: EvaluationCriterion[];
  questionAssessments: QuestionAssessment[];
  summary: string;
  strengths: string[];
  weaknesses: string[];
  interviewerNotes: string;
}

export interface Evaluation {
  id: string;
  roleId?: string;
  templateId?: string;
  candidateId?: string;
  applicationId?: string;
  interviewId?: string;
  scores?: Record<string, number>;
  feedback?: string;
  recommendation?: 'hire' | 'reject' | 'consider';
  createdAt: Date;
  updatedAt?: string;
  status: string;
  error?: string;
  data?: {
    overallScore: number;
    decision: 'Go' | 'No Go' | 'Maybe';
    criteria?: Array<{
      name: string;
      score: number;
      feedback: string;
    }>;
    questionAssessments?: Array<{
      questionId?: string;
      question: string;
      answer: string;
      score: number;
      feedback: string;
    }>;
    summary: string;
    strengths?: string[];
    weaknesses?: string[];
    interviewerNotes?: string;
  };
}

export interface EvaluationResponse {
  data: Evaluation;
  status: string;
  message?: string;
}

/**
 * Request an evaluation for an interview (authenticated)
 *
 * @param params The evaluation request parameters
 * @returns The evaluation ID
 */
export async function requestEvaluation(params: EvaluationRequest): Promise<string> {
  try {
    // Convert camelCase to snake_case for backend compatibility
    const backendParams = {
      interview_id: params.interviewId,
      role_id: params.roleId,
      application_id: params.applicationId,
      template_id: params.templateId,
      stage_name: params.stageName,
      stage_index: params.stageIndex
    };

    console.log('Requesting evaluation with params:', backendParams);

    try {
      const response = await apiClient.post<{ evaluation_id: string, status: string, evaluation?: any }>(
        '/interview-evaluation/evaluate',
        backendParams
      );

      console.log('Evaluation response:', response.data);

      // Check if the response contains evaluation_id
      if (!response.data.evaluation_id && response.data.evaluation && response.data.evaluation.evaluation_id) {
        // Handle nested evaluation_id
        return response.data.evaluation.evaluation_id;
      } else if (!response.data.evaluation_id && response.data.status === 'success' && response.data.evaluation) {
        // If no evaluation_id but we have a successful evaluation, generate a unique ID
        const uniqueId = `eval-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        console.warn(`No evaluation_id in response, generating unique ID: ${uniqueId}`);
        return uniqueId;
      }

      return response.data.evaluation_id;
    } catch (error: any) {
      console.error('Error in requestEvaluation API call:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    }
  } catch (error) {
    console.error('Error requesting evaluation:', error);
    throw error;
  }
}

/**
 * Get an evaluation by ID
 *
 * @param evaluationId The evaluation ID
 * @returns The evaluation
 */
export async function getEvaluation(evaluationId: string): Promise<EvaluationResponse> {
  try {
    const response = await apiClient.get<EvaluationResponse>(`/interview-evaluation/evaluations/${evaluationId}`);
    return response.data;
  } catch (error) {
    console.error('Error getting evaluation:', error);
    throw error;
  }
}

/**
 * Get evaluations for a role
 *
 * @param roleId The role ID
 * @returns The evaluations
 */
export async function getEvaluationsByRole(roleId: string): Promise<EvaluationResponse[]> {
  try {
    const response = await apiClient.get<EvaluationResponse[]>(`/interview-evaluation/evaluations?role_id=${roleId}`);
    return response.data;
  } catch (error) {
    console.error('Error getting evaluations by role:', error);
    throw error;
  }
}

/**
 * Get evaluations for an interview
 *
 * @param interviewId The interview ID
 * @returns The evaluations
 */
export async function getEvaluationsByInterview(interviewId: string): Promise<EvaluationResponse[]> {
  try {
    const response = await apiClient.get<EvaluationResponse[]>(`/interview-evaluation/evaluations?interview_id=${interviewId}`);
    return response.data;
  } catch (error) {
    console.error('Error getting evaluations by interview:', error);
    throw error;
  }
}

/**
 * Request an evaluation for a public interview
 *
 * @param params The evaluation request parameters
 * @returns The evaluation ID
 */
export async function requestPublicEvaluation(params: EvaluationRequest): Promise<string> {
  try {
    // Convert camelCase to snake_case for backend compatibility
    const backendParams = {
      session_id: params.interviewId,
      role_id: params.roleId,
      application_id: params.applicationId,
      template_id: params.templateId,
      stage_name: params.stageName,
      stage_index: params.stageIndex
    };

    console.log('Requesting public evaluation with params:', backendParams);

    try {
      const response = await apiClient.post<{ evaluation_id: string, status: string, evaluation?: any }>(
        '/interview-evaluation/public/evaluate',
        backendParams
      );

      console.log('Public evaluation response:', response.data);

      // Check if the response contains evaluation_id
      if (!response.data.evaluation_id && response.data.evaluation && response.data.evaluation.evaluation_id) {
        // Handle nested evaluation_id
        return response.data.evaluation.evaluation_id;
      } else if (!response.data.evaluation_id && response.data.status === 'success' && response.data.evaluation) {
        // If no evaluation_id but we have a successful evaluation, generate a unique ID
        const uniqueId = `eval-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
        console.warn(`No evaluation_id in public response, generating unique ID: ${uniqueId}`);
        return uniqueId;
      }

      return response.data.evaluation_id;
    } catch (error: any) {
      console.error('Error in requestPublicEvaluation API call:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    }
  } catch (error) {
    console.error('Error requesting public evaluation:', error);
    throw error;
  }
}

/**
 * Get a public evaluation by ID
 *
 * @param evaluationId The evaluation ID
 * @returns The evaluation
 */
export async function getPublicEvaluation(evaluationId: string): Promise<EvaluationResponse> {
  try {
    const response = await apiClient.get<EvaluationResponse>(`/interview-evaluation/public/evaluations/${evaluationId}`);
    return response.data;
  } catch (error) {
    console.error('Error getting public evaluation:', error);
    throw error;
  }
}

/**
 * Link an evaluation to an interview
 *
 * This is used when an interview doesn't have an evaluation_id but the application has evaluation data
 *
 * @param applicationId The application ID
 * @param interviewId The interview ID
 * @returns The evaluation ID
 */
export async function linkEvaluationToInterview(applicationId: string, interviewId: string): Promise<string> {
  try {
    console.log(`Linking evaluation for application ${applicationId} to interview ${interviewId}`);

    const response = await apiClient.post<{ evaluation_id: string, status: string, message: string }>(
      '/evaluation-fix/link-evaluation-to-interview',
      {
        application_id: applicationId,
        interview_id: interviewId
      }
    );

    console.log('Link evaluation response:', response.data);
    return response.data.evaluation_id;
  } catch (error) {
    console.error('Error linking evaluation to interview:', error);
    throw error;
  }
}

/**
 * Directly link a specific evaluation ID to an interview
 *
 * This is used when we know the exact evaluation ID to link
 *
 * @param interviewId The interview ID
 * @param evaluationId The evaluation ID to link
 * @returns The evaluation ID
 */
export async function directLinkEvaluation(interviewId: string, evaluationId: string): Promise<string> {
  try {
    console.log(`Directly linking evaluation ${evaluationId} to interview ${interviewId}`);

    const payload = {
      interview_id: interviewId,
      evaluation_id: evaluationId
    };

    const response = await apiClient.post<{ evaluation_id: string, status: string, message: string }>(
      '/evaluation-fix/direct-link-evaluation',
      payload
    );

    console.log('Direct link evaluation response:', response.data);
    return response.data.evaluation_id;
  } catch (error) {
    console.error('Error directly linking evaluation:', error);
    throw error;
  }
}
