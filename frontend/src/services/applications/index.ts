import { apiClient } from '@/lib/api/client/axios';
import { ExtendedApplication, Role } from '@/types/role';
import { createApplication as apiCreateApplication, ApplicationCreateRequest, ApplicationResponse } from './api';

/**
 * Applications interface for dashboard data
 */
export interface ApplicationsData {
  applications: ExtendedApplication[];
  roles: Role[];
  evaluations: any[];
}

/**
 * Applications service for working with candidate applications
 */
class ApplicationsService {
  /**
   * Get all applications with enhanced data
   *
   * @param sort Order to sort applications (default: newest first)
   * @param limit Number of applications to return (optional)
   * @returns Array of applications with related data
   */
  async getApplications(sort: 'newest' | 'oldest' = 'newest', limit?: number): Promise<ExtendedApplication[]> {
    try {
      // Get applications with all related data in a single request
      const dashboardData = await this.getApplicationsData();
      let applications = dashboardData.applications || [];

      // Process applications - sort by creation date
      if (Array.isArray(applications)) {
        if (sort === 'newest') {
          applications = applications.sort((a, b) => {
            const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
            const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
            return dateB.getTime() - dateA.getTime();
          });
        } else {
          applications = applications.sort((a, b) => {
            const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
            const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
            return dateA.getTime() - dateB.getTime();
          });
        }

        // Apply limit if specified
        if (limit && limit > 0) {
          applications = applications.slice(0, limit);
        }
      }

      return applications;
    } catch (error) {
      console.error('Error fetching applications:', error);
      return [];
    }
  }

  /**
   * Get all applications data in a single request
   * 
   * @returns Dashboard data including applications and related data
   */
  async getApplicationsData(): Promise<ApplicationsData> {
    try {
      const response = await apiClient.get<ApplicationsData>('/dashboard/data');
      return response.data;
    } catch (error) {
      console.error('Error fetching applications data:', error);
      // Return empty data on error
      return {
        applications: [],
        roles: [],
        evaluations: []
      };
    }
  }

  /**
   * Get a single application by ID
   *
   * @param applicationId Application ID to retrieve
   * @returns Application data or null if not found
   */
  async getApplication(applicationId: string): Promise<ExtendedApplication | null> {
    try {
      if (!applicationId) {
        throw new Error('Application ID is required');
      }

      const response = await apiClient.get<ExtendedApplication>(`/dashboard/applications/${applicationId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching application ${applicationId}:`, error);
      return null;
    }
  }

  /**
   * Create a new application using the backend API
   *
   * @param applicationData The application data to create
   * @returns The created application or null if creation failed
   */
  async createApplication(applicationData: ApplicationCreateRequest): Promise<ApplicationResponse | null> {
    try {
      const application = await apiCreateApplication(applicationData);
      return application;
    } catch (error) {
      console.error('Error creating application via API:', error);
      return null;
    }
  }
}

export const applicationsService = new ApplicationsService();