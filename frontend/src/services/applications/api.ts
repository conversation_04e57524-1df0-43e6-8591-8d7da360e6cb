import { apiClient } from '@/lib/api/client/axios';

/**
 * API types for application creation
 */
export interface ApplicationCreateRequest {
    role_id: string;
    candidate_id?: string;
    email?: string;
    full_name?: string;
    resume_url?: string;
    status?: string;
    is_public?: boolean;
}

export interface ApplicationResponse {
    id: string;
    role_id: string;
    candidate_id?: string;
    email?: string;
    full_name?: string;
    resume_url?: string;
    status: string;
    is_public: boolean;
    created_at: string;
}

/**
 * Create a new application
 * 
 * @param applicationData The application data to create
 * @returns The created application
 */
export const createApplication = async (
    applicationData: ApplicationCreateRequest
): Promise<ApplicationResponse> => {
    try {
        const response = await apiClient.post<ApplicationResponse>(
            '/public/applications',
            applicationData
        );

        return response.data;
    } catch (error) {
        console.error('Error creating application:', error);
        throw error;
    }
};
