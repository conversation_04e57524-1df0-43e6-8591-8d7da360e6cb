'use client';

import { roles<PERSON><PERSON>, CreateRoleRequest, EndCallResponse } from '@/services/roles/api';
import type { Role, RoleStatus } from '@/types/role';
import { ServiceError } from '@/lib/utils/errors';
import { ApiResult } from '@/lib/api/types/api';

// Add these interfaces at the top of the file after imports
interface TranscriptData {
  messages: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
  }>;
  metadata?: {
    duration?: number;
    status?: string;
    [key: string]: unknown;
  };
}

interface TranscriptResponse {
  id: string;
  status: string;
  messages: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
  }>;
  metadata?: {
    duration?: number;
    status?: string;
    [key: string]: unknown;
  };
}

type ApiTranscriptResponse = ApiResult<TranscriptResponse>;

/**
 * RolesService provides a centralized service layer for managing roles
 * with consistent error handling, data transformation, and caching capabilities.
 */
class RolesService {
  private roleCache: Map<string, Role> = new Map();
  // Track pending requests to prevent duplicates
  private pendingRequests: Map<string, Promise<any>> = new Map();

  /**
   * Get a role by ID with caching
   */
  async getRole(roleId: string): Promise<Role | null> {
    try {
      console.log('RolesService: Getting role with ID:', roleId);

      // Check cache first
      if (this.roleCache.has(roleId)) {
        console.log('RolesService: Found role in cache');
        return this.roleCache.get(roleId)!;
      }

      // Check if we already have a pending request for this role
      if (this.pendingRequests.has(roleId)) {
        console.log('RolesService: Request already in progress, reusing promise');
        return this.pendingRequests.get(roleId);
      }

      console.log('RolesService: Fetching role from API');
      
      // Create the promise and store it
      const fetchPromise = rolesApi.getRole(roleId)
        .then(response => {
          if ('data' in response && response.data) {
            console.log('RolesService: Role fetched successfully:', response.data);
            this.roleCache.set(roleId, response.data);
            return response.data;
          } else if ('status' in response && response.status === 'error') {
            // Handle explicit error response
            console.error(`RolesService: Error fetching role: ${response.message || 'Unknown error'}`);
            return null;
          } else {
            console.error('RolesService: Role not found for ID:', roleId);
            return null;
          }
        })
        .catch(error => {
          console.error('RolesService: Error getting role:', error);
          return null;
        })
        .finally(() => {
          // Remove from pending requests when done
          this.pendingRequests.delete(roleId);
        });
      
      // Store the promise
      this.pendingRequests.set(roleId, fetchPromise);
      
      return fetchPromise;
    } catch (error) {
      console.error('RolesService: Error getting role:', error);
      return null;
    }
  }

  /**
   * Get all roles for the current user
   */
  async getRoles(): Promise<Role[]> {
    try {
      console.log('RolesService: Fetching all roles');

      // Check if we already have a pending request for all roles
      const requestKey = 'list:all';
      if (this.pendingRequests.has(requestKey)) {
        console.log('RolesService: Roles request already in progress, reusing promise');
        return this.pendingRequests.get(requestKey);
      }

      // Create the promise and store it
      const fetchPromise = rolesApi.listRoles()
        .then(response => {
          // Check if the response is an array
          if (Array.isArray(response)) {
            console.log(`RolesService: Successfully fetched ${response.length} roles`);
            
            // Cache each role
            response.forEach(role => {
              if (role && role.id) {
                this.roleCache.set(role.id, role);
              }
            });
            
            return response;
          }
          // Check if the response is an error object
          else if (response && typeof response === 'object') {
            // Type assertion to access the detail property
            const errorResponse = response as any;
            if ('detail' in errorResponse) {
              console.warn(`RolesService: API returned an error: ${errorResponse.detail}`);
              // For unauthenticated users, return an empty array rather than failing
              return [];
            }
          }

          // Something else went wrong
          console.error('RolesService: Received invalid roles data:', response);
          return [];
        })
        .catch(error => {
          console.error('RolesService: Error fetching roles:', error);
          return [];
        })
        .finally(() => {
          // Remove from pending requests when done
          this.pendingRequests.delete(requestKey);
        });
      
      // Store the promise
      this.pendingRequests.set(requestKey, fetchPromise);
      
      return fetchPromise;
    } catch (error) {
      console.error('RolesService: Error fetching roles:', error);
      return [];
    }
  }

  /**
   * Create a new role
   */
  async createRole(data: CreateRoleRequest): Promise<Role> {
    try {
      console.log('RolesService: Creating role with data:', JSON.stringify(data, null, 2));

      // Validate required fields
      if (!data.title) {
        throw new Error('Title is required');
      }

      if (!data.summary) {
        throw new Error('Summary is required');
      }

      if (!data.hiringManagerContact) {
        throw new Error('Hiring manager contact is required');
      }

      // Create a copy of the data to avoid modifying the original
      const processedData = { ...data };

      // Ensure keyResponsibilities is an array
      if (typeof processedData.keyResponsibilities === 'string') {
        processedData.keyResponsibilities = (processedData.keyResponsibilities as string).split('\n').filter((item: string) => item.trim() !== '');
      } else if (!Array.isArray(processedData.keyResponsibilities)) {
        processedData.keyResponsibilities = [];
      }

      // Ensure interviewProcess is an array
      if (!Array.isArray(processedData.interviewProcess)) {
        processedData.interviewProcess = [];
      }

      // Set default values for required fields if missing
      const finalData = {
        ...processedData,
        status: processedData.status || 'Intake',
        priority: processedData.priority || 'Normal',
        jobType: processedData.jobType || 'Full-time',
        location: processedData.location || { type: 'On-site', city: '' },
      };

      console.log('RolesService: Processed data:', JSON.stringify(finalData, null, 2));

      const role = await rolesApi.createRole(finalData);

      if (!role || !role.id) {
        throw new Error('Invalid response from create role API');
      }

      this.roleCache.set(role.id!, role);
      return role;
    } catch (error) {
      console.error('RolesService: Failed to create role:', error);

      // Enhance error message based on error type
      if (error instanceof Error) {
        if (error.message.includes('422')) {
          throw new ServiceError('Invalid role data. Please check all required fields.', error);
        }
        if (error.message.includes('401') || error.message.includes('403')) {
          throw new ServiceError('Authentication error. Please sign in again.', error);
        }
      }

      throw new ServiceError('Failed to create role', error);
    }
  }

  /**
   * Update a role
   */
  async updateRole(roleId: string, data: Partial<Role>): Promise<Role> {
    try {
      console.log('RolesService.updateRole - Input data:', JSON.stringify(data, null, 2));

      // Special case for publish/unpublish operations to ensure minimal updates
      const isPublishOperation = 'isPublished' in data && Object.keys(data).length <= 2;
      if (isPublishOperation) {
        console.log('⚠️ CRITICAL: Detected publish/unpublish operation - enforcing minimal update');

        // For publish/unpublish, we ONLY want to update isPublished and potentially status
        const safeUpdateData: Partial<CreateRoleRequest> = {
          isPublished: data.isPublished
        };

        // Only include status if it was explicitly provided
        if ('status' in data) {
          safeUpdateData.status = data.status;
        }

        console.log('⚠️ ENFORCED MINIMAL UPDATE:', JSON.stringify(safeUpdateData, null, 2));

        // Make the API call with the strictly limited data
        let role: Role;

        try {
          // CRITICAL FIX: Get current role data to ensure nothing is lost
          const currentRole = await this.getRole(roleId);
          if (!currentRole) {
            throw new Error('Failed to get current role data before publish/unpublish update');
          }

          // Store backup in memory for this service instance
          const backupKey = `role_backup_${roleId}`;
          (this as any)[backupKey] = JSON.parse(JSON.stringify(currentRole));

          // Make the API call with the strictly limited data
          role = await rolesApi.updateRole(roleId, safeUpdateData);

          // CRITICAL FIX: Verify all data was preserved by comparing with previous state
          const fieldsToCheck = ['title', 'summary', 'location', 'interviewProcess', 'status'];
          let dataLossDetected = false;

          for (const field of fieldsToCheck) {
            if (field === 'interviewProcess') {
              // Special check for interview process to ensure stages weren't lost
              const originalStages = currentRole.interviewProcess?.length || 0;
              const updatedStages = role.interviewProcess?.length || 0;

              if (updatedStages < originalStages) {
                console.error(`⚠️ DATA LOSS DETECTED! Interview stages reduced from ${originalStages} to ${updatedStages}`);
                dataLossDetected = true;

                // Restore the interview process from the saved role
                role.interviewProcess = currentRole.interviewProcess;
              }
            }
            // Skip status check if we explicitly updated it
            else if (field === 'status' && 'status' in safeUpdateData) {
              continue;
            }
            // Check if other fields were lost or emptied
            else if (
              (currentRole[field as keyof Role]) &&
              (!role[field as keyof Role] || (typeof role[field as keyof Role] === 'object' && Object.keys(role[field as keyof Role] as Record<string, unknown>).length === 0))
            ) {
              console.error(`⚠️ DATA LOSS DETECTED! Field "${field}" was lost in the update`);
              dataLossDetected = true;

              // Restore the field from the saved role using type assertion
              (role as Record<keyof Role, unknown>)[field as keyof Role] = currentRole[field as keyof Role];
            }
          }

          // If we detected data loss, attempt a server-side recovery
          if (dataLossDetected) {
            console.warn('⚠️ Some data was lost during publish/unpublish, attempting server-side recovery');

            // Create recovery object - original data but with updated isPublished status
            const recoveryData = {
              ...currentRole,
              isPublished: safeUpdateData.isPublished
            };

            // Keep the updated status if that was included
            if ('status' in safeUpdateData && safeUpdateData.status) {
              recoveryData.status = safeUpdateData.status;
            }

            // Remove fields that shouldn't be included in updates
            delete recoveryData.id;
            delete recoveryData.created_at;
            delete recoveryData.updated_at;

            try {
              console.log('⚠️ EMERGENCY RECOVERY: Attempting to restore role data on server');
              const recoveredRole = await rolesApi.updateRole(roleId, recoveryData);

              // Check if recovery was successful
              const recoverySuccessful = recoveredRole &&
                recoveredRole.interviewProcess?.length === currentRole.interviewProcess?.length;

              if (recoverySuccessful) {
                console.log('✅ Server-side recovery successful');
                role = recoveredRole;
              } else {
                console.warn('⚠️ Server-side recovery only partially successful');
                // Use the best data we have (in-memory client-side fixes)
              }
            } catch (recoveryError) {
              console.error('❌ Server-side recovery failed:', recoveryError);
              console.warn('⚠️ Using client-side data recovery instead');
              // Continue with client-side fixed data
            }
          }
        } catch (error) {
          console.error('RolesService.updateRole - Error during publish/unpublish:', error);

          // Try to use backup if available
          const backupKey = `role_backup_${roleId}`;
          const backup = (this as any)[backupKey];

          if (backup) {
            console.log('⚠️ Using in-memory backup after error');

            // Apply publish/unpublish changes to backup
            backup.isPublished = data.isPublished;
            if ('status' in data) {
              backup.status = data.status;
            }

            // Return backup as role
            role = backup;
          } else {
            throw error;
          }
        }

        // Validate the response
        if (!role || !role.id) {
          throw new Error('Invalid response from update role API');
        }

        console.log('✅ Publish/unpublish operation successful for role:', role.id);

        // Invalidate the cache to ensure fresh data
        this.roleCache.delete(roleId);

        return role;
      }

      // For other updates, proceed with the normal process
      // CRITICAL FIX: Only copy the exact fields provided in the update
      // Do NOT include any default values or transformations that could overwrite existing data
      const transformedData: Partial<CreateRoleRequest> = {};

      // Copy ONLY the fields that were explicitly provided in the input data
      Object.keys(data).forEach(key => {
        (transformedData as Record<string, unknown>)[key] = data[key as keyof typeof data];
      });

      // Ensure we're not unintentionally modifying interviewProcess
      if (data.interviewProcess !== undefined) {
        console.log('InterviewProcess data was explicitly provided - including it in the update');
        // We need to map it to the expected format, but preserve all fields
        transformedData.interviewProcess = data.interviewProcess.map(stage => ({
          ...stage, // Keep ALL original fields
          stage: stage.stage,
          duration: stage.duration,
          customInstructions: stage.customInstructions,
          state: stage.state, // Preserve the state value
        }));
      } else {
        console.log('InterviewProcess was NOT provided - it will NOT be modified');
        // Ensure interviewProcess is not included at all
        delete transformedData.interviewProcess;
      }

      console.log('RolesService.updateRole - FINAL data to send:', JSON.stringify(transformedData, null, 2));

      const role = await rolesApi.updateRole(roleId, transformedData);

      // Validate the response
      if (!role || !role.id) {
        throw new Error('Invalid response from update role API');
      }

      console.log('RolesService.updateRole - Success, updated role:', role.id);

      // Invalidate the cache to ensure fresh data
      this.roleCache.delete(roleId);

      return role;
    } catch (error) {
      console.error('Failed to update role:', error);
      if (error instanceof Error) {
        if (error.message.includes('422')) {
          throw new ServiceError('Invalid role data. Please check all required fields.', error);
        }
        if (error.message.includes('401') || error.message.includes('403')) {
          throw new ServiceError('Unauthorized to update role. Please check your permissions.', error);
        }
      }
      throw new ServiceError('Failed to update role', error);
    }
  }

  /**
   * Delete a role
   */
  async deleteRole(roleId: string): Promise<void> {
    try {
      await rolesApi.deleteRole(roleId);
      this.roleCache.delete(roleId);
    } catch (error) {
      console.error('Failed to delete role:', error);
      throw new ServiceError('Failed to delete role', error);
    }
  }

  /**
   * Update role status
   */
  async updateRoleStatus(roleId: string, status: RoleStatus): Promise<void> {
    try {
      await rolesApi.updateRoleStatus(roleId, status);
      // Invalidate cache since status changed
      this.roleCache.delete(roleId);
    } catch (error) {
      console.error('Failed to update role status:', error);
      throw new ServiceError('Failed to update role status', error);
    }
  }

  /**
   * Get intake transcripts for a role
   */
  async getIntakeTranscripts(roleId: string): Promise<any[]> {
    try {
      return await rolesApi.getIntakeTranscripts(roleId);
    } catch (error) {
      console.error('Failed to get intake transcripts:', error);
      throw new ServiceError('Failed to get intake transcripts', error);
    }
  }

  /**
   * Get interview transcripts for a role
   */
  async getInterviewTranscripts(roleId: string): Promise<any[]> {
    try {
      return await rolesApi.getInterviewTranscripts(roleId);
    } catch (error) {
      console.error('Failed to get interview transcripts:', error);
      throw new ServiceError('Failed to get interview transcripts', error);
    }
  }

  /**
   * Create an intake transcript
   */
  async createIntakeTranscript(roleId: string, transcriptData: TranscriptData): Promise<ApiTranscriptResponse> {
    try {
      console.log('RolesService: Creating intake transcript for role:', roleId);
      return await rolesApi.createIntakeTranscript(roleId, transcriptData);
    } catch (error) {
      console.error('RolesService: Failed to create intake transcript:', error);
      throw new ServiceError('Failed to create intake transcript', error);
    }
  }

  /**
   * Update an intake transcript
   */
  async updateIntakeTranscript(roleId: string, transcriptId: string, transcriptData: Partial<TranscriptData>): Promise<ApiTranscriptResponse> {
    try {
      console.log('RolesService: Updating intake transcript:', transcriptId);
      return await rolesApi.updateIntakeTranscript(roleId, transcriptId, transcriptData);
    } catch (error) {
      console.error('RolesService: Failed to update intake transcript:', error);
      throw new ServiceError('Failed to update intake transcript', error);
    }
  }

  /**
   * Complete an intake transcript
   */
  async completeIntakeTranscript(roleId: string, transcriptId: string): Promise<ApiTranscriptResponse> {
    try {
      console.log('RolesService: Completing intake transcript:', transcriptId);
      return await rolesApi.completeIntakeTranscript(roleId, transcriptId);
    } catch (error) {
      console.error('RolesService: Failed to complete intake transcript:', error);
      throw new ServiceError('Failed to complete intake transcript', error);
    }
  }

  /**
   * Create an interview transcript
   */
  async createInterviewTranscript(roleId: string, transcriptData: TranscriptData): Promise<ApiTranscriptResponse> {
    try {
      console.log('RolesService: Creating interview transcript for role:', roleId);
      return await rolesApi.createInterviewTranscript(roleId, transcriptData);
    } catch (error) {
      console.error('RolesService: Failed to create interview transcript:', error);
      throw new ServiceError('Failed to create interview transcript', error);
    }
  }

  /**
   * Update an interview transcript
   */
  async updateInterviewTranscript(roleId: string, transcriptId: string, transcriptData: Partial<TranscriptData>): Promise<ApiTranscriptResponse> {
    try {
      console.log('RolesService: Updating interview transcript:', transcriptId);
      return await rolesApi.updateInterviewTranscript(roleId, transcriptId, transcriptData);
    } catch (error) {
      console.error('RolesService: Failed to update interview transcript:', error);
      throw new ServiceError('Failed to update interview transcript', error);
    }
  }

  /**
   * Complete an interview transcript
   */
  async completeInterviewTranscript(roleId: string, transcriptId: string): Promise<ApiTranscriptResponse> {
    try {
      console.log('RolesService: Completing interview transcript:', transcriptId);
      return await rolesApi.completeInterviewTranscript(roleId, transcriptId);
    } catch (error) {
      console.error('RolesService: Failed to complete interview transcript:', error);
      throw new ServiceError('Failed to complete interview transcript', error);
    }
  }

  /**
   * Delete intake transcript
   */
  async deleteIntakeTranscript(roleId: string, transcriptId: string): Promise<ApiResult<void>> {
    try {
      return await rolesApi.deleteIntakeTranscript(roleId, transcriptId);
    } catch (error) {
      console.error('RolesService: Error deleting intake transcript:', error);
      throw error;
    }
  }

  /**
   * End an intake call and process the transcript
   */
  async endIntakeCall(roleId: string, transcriptId: string, userId: string, isEnrichment: boolean = false, roleData?: Partial<Role>): Promise<EndCallResponse> {
    return await rolesApi.endIntakeCall(roleId, transcriptId, userId, isEnrichment, roleData);
  }

  /**
   * Parse job description file
   */
  async parseJobDescription(file: File): Promise<{ content: string }> {
    try {
      return await rolesApi.parseJobDescription(file);
    } catch (error) {
      console.error('RolesService: Failed to parse job description:', error);
      throw new ServiceError('Failed to parse job description', error);
    }
  }

  /**
   * Generate a job posting from a transcript
   */
  async generateJobPosting(roleId: string, transcriptId: string): Promise<{ job_posting: string }> {
    try {
      console.log('RolesService: Generating job posting for role:', roleId, 'with transcript:', transcriptId);

      // Validate inputs
      if (!roleId || !transcriptId) {
        throw new Error('Role ID and transcript ID are required');
      }

      const result = await rolesApi.generateJobPosting(roleId, transcriptId);

      // Check if the result is successful and has data
      if ('status' in result && result.status === 'success' && 'job_posting' in result) {
        return { job_posting: String(result.job_posting) };
      } else {
        // Handle error case
        const errorMessage = 'message' in result ? result.message : 'Failed to generate job posting';
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Error in generateJobPosting:', error);
      throw error;
    }
  }

  /**
   * Get the job posting for a role
   */
  async getJobPosting(roleId: string): Promise<{ job_posting: string, generated_at?: string } | null> {
    try {
      console.log('RolesService: Getting job posting for role:', roleId);

      if (!roleId) {
        throw new Error('Role ID is required');
      }

      const result = await rolesApi.getJobPosting(roleId);

      // Check if the result has the expected structure
      if (result && typeof result === 'object') {
        // Use type assertion to access properties
        const apiResult = result as any;

        if (apiResult.status === 'success' && apiResult.job_posting) {
          console.log('RolesService: Job posting retrieved successfully');
          return {
            job_posting: apiResult.job_posting,
            generated_at: apiResult.generated_at
          };
        } else if (apiResult.status === 'not_found') {
          console.log('RolesService: No job posting found for this role');
          return null;
        } else if (apiResult.status === 'error' && apiResult.message) {
          console.error('RolesService: Error from API:', apiResult.message);
          throw new Error(apiResult.message);
        } else if (apiResult.detail) {
          // Handle FastAPI validation error format
          console.error('RolesService: API validation error:', apiResult.detail);
          throw new Error(Array.isArray(apiResult.detail)
            ? apiResult.detail.map((error: { msg: string }) => error.msg).join(', ')
            : apiResult.detail);
        }
      }

      // If we get here, the response format is unexpected
      console.error('RolesService: Unexpected response format:', result);
      throw new Error('Unexpected response format from API');
    } catch (error) {
      console.error('RolesService: Failed to get job posting:', error);
      throw new ServiceError('Failed to get job posting', error);
    }
  }

  /**
   * Clear the cache and pending requests
   */
  clearCache(): void {
    console.log('RolesService: Clearing cache');
    this.roleCache.clear();
    this.pendingRequests.clear();
  }

  /**
   * Create a placeholder role for intake calls
   */
  async createPlaceholderRole(userId: string): Promise<Role> {
    try {
      const role = await rolesApi.createPlaceholderRole(userId);

      // Cache the new role if it has an ID
      if (role && role.id) {
        this.roleCache.set(role.id, role);
      }

      return role;
    } catch (error) {
      console.error('Failed to create placeholder role:', error);
      throw new ServiceError('Failed to create placeholder role', error);
    }
  }

  async enrichRole(roleId: string, transcriptId: string): Promise<{ enriched_data: Partial<Role> }> {
    try {
      const response = await rolesApi.enrichRole(roleId, transcriptId);

      // Log the full response for debugging
      console.log('RolesService: Enrichment response:', response);

      // Handle different response formats
      if ('data' in response && response.data && typeof response.data === 'object') {
        if ('enriched_data' in response.data) {
          return { enriched_data: response.data.enriched_data as Partial<Role> };
        } else {
          // If the API returns data but in a different structure
          return { enriched_data: response.data as Partial<Role> };
        }
      } else if (response && typeof response === 'object' && 'enriched_data' in response) {
        // Direct response format (not an ApiResult)
        return { enriched_data: response.enriched_data as Partial<Role> };
      }

      throw new Error('Failed to enrich role: Invalid response format');
    } catch (error) {
      console.error('RolesService: Error enriching role:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const rolesService = new RolesService();