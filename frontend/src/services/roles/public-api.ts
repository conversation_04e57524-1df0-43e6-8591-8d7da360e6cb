'use client';

import { publicApiClient } from '@/lib/api/client/public';
import type { Role } from '@/types/role';

// Use a different API path for public endpoints
const PUBLIC_API_PATH = '/public/jobs';

export const publicRolesApi = {
    // List all public roles (no authentication required)
    listPublicRoles: async (): Promise<Role[]> => {
        try {
            // Enhanced logging for request
            console.log(`PublicRolesApi: Making request to ${PUBLIC_API_PATH}`);
            
            // Add a timestamp for debugging
            const startTime = Date.now();
            
            // Make the request with a specific timeout
            const response = await publicApiClient.get<Role[]>(PUBLIC_API_PATH, {
                timeout: 15000, // 15 seconds timeout
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Expires': '0',
                }
            });
            
            const endTime = Date.now();
            
            // Log response time
            console.log(`PublicRolesApi: Request completed in ${endTime - startTime}ms`);
            
            // Check if response data is valid
            if (!response.data) {
                console.warn('PublicRolesApi: No data returned from API');
                return [];
            }
            
            // Check if response data is an array
            if (!Array.isArray(response.data)) {
                console.warn('PublicRolesApi: Response data is not an array', typeof response.data);
                return [];
            }
            
            // Log success with data count
            console.log(`PublicRolesApi: Successfully fetched ${response.data.length} roles`);
            
            // Log first role ID for debugging if available
            if (response.data.length > 0) {
                console.log(`PublicRolesApi: First role ID: ${response.data[0].id}`);
                console.log(`PublicRolesApi: First role status: ${response.data[0].status}`);
                console.log(`PublicRolesApi: First role isPublished: ${response.data[0].isPublished}`);
            } else {
                console.warn('PublicRolesApi: No roles returned from API');
            }
            
            return response.data;
        } catch (error) {
            console.error('PublicRolesApi: Error fetching public roles:', error);
            // Return empty array on error
            return [];
        }
    },

    // Get a specific public role by ID (no authentication required)
    getPublicRole: async (roleId: string): Promise<Role | null> => {
        try {
            console.log(`PublicRolesApi: Getting public role with ID: ${roleId}`);
            
            const startTime = Date.now();
            
            const response = await publicApiClient.get<Role>(`${PUBLIC_API_PATH}/${roleId}`, {
                timeout: 10000, // 10 seconds timeout
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache',
                    'Expires': '0',
                }
            });
            
            const endTime = Date.now();
            console.log(`PublicRolesApi: Request completed in ${endTime - startTime}ms`);
            
            if (!response.data) {
                console.warn(`PublicRolesApi: No data returned for role ${roleId}`);
                return null;
            }
            
            // Check if the role is published - this is the only required condition
            if (!response.data.isPublished) {
                console.warn(`PublicRolesApi: Role ${roleId} is not published`);
                return null;
            }
            
            console.log(`PublicRolesApi: Successfully fetched role ${roleId}`);
            return response.data;
        } catch (error) {
            console.error(`PublicRolesApi: Error fetching role ${roleId}:`, error);
            return null;
        }
    },
}; 