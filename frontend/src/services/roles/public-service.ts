'use client';

import { publicRolesApi } from '@/services/roles/public-api';
import type { Role } from '@/types/role';

/**
 * PublicRolesService provides a service layer for accessing public role data
 * without authentication requirements
 */
class PublicRolesService {
  private roleCache: Map<string, Role> = new Map();
  private allRolesCache: Role[] | null = null;
  private lastFetchTimestamp: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
  private isNetworkError: boolean = false;
  private networkErrorTimestamp: number = 0;
  private readonly NETWORK_ERROR_RETRY_DELAY = 5 * 1000; // 5 seconds (reduced from 10)
  private fetchAttempts: number = 0;
  private readonly MAX_FETCH_ATTEMPTS = 2;

  /**
   * Check if cache is still valid
   */
  private isCacheValid(): boolean {
    return (
      this.allRolesCache !== null && 
      Date.now() - this.lastFetchTimestamp < this.CACHE_TTL
    );
  }

  /**
   * Check if we should retry after a network error
   */
  private shouldRetryAfterNetworkError(): boolean {
    // If we've exceeded max attempts, don't retry
    if (this.fetchAttempts >= this.MAX_FETCH_ATTEMPTS) {
      console.log(`PublicRolesService: Max fetch attempts (${this.MAX_FETCH_ATTEMPTS}) reached, not retrying`);
      return false;
    }
    
    // If there was no network error, or enough time has passed since the last error, retry
    return !this.isNetworkError || 
           (Date.now() - this.networkErrorTimestamp > this.NETWORK_ERROR_RETRY_DELAY);
  }

  /**
   * Reset network error state
   */
  private resetNetworkErrorState(): void {
    this.isNetworkError = false;
    this.fetchAttempts = 0;
  }

  /**
   * Get a public role by ID with caching
   */
  async getPublicRole(roleId: string): Promise<Role | null> {
    try {
      // Check cache first
      if (this.roleCache.has(roleId)) {
        console.log(`PublicRolesService: Using cached role data for ${roleId}`);
        return this.roleCache.get(roleId)!;
      }

      // If we had a network error recently, don't retry immediately
      if (!this.shouldRetryAfterNetworkError()) {
        console.warn('PublicRolesService: Skipping API call due to recent network error');
        return null;
      }

      this.fetchAttempts++;
      console.log(`PublicRolesService: Fetching role ${roleId} (attempt ${this.fetchAttempts})`);
      
      const role = await publicRolesApi.getPublicRole(roleId);
      
      if (role) {
        // Ensure all required fields are present
        const normalizedRole = this.normalizeRole(role);
        
        // Only check if the role is published - don't verify job posting
        if (!normalizedRole.isPublished) {
          console.warn(`PublicRolesService: Role ${roleId} is not published`);
          return null;
        }
        
        this.roleCache.set(roleId, normalizedRole);
        
        // Reset network error flag on successful request
        this.resetNetworkErrorState();
        
        return normalizedRole;
      }
      
      return null;
    } catch (error) {
      console.error('PublicRolesService: Failed to fetch role:', error);
      
      // Set network error flag and timestamp
      this.isNetworkError = true;
      this.networkErrorTimestamp = Date.now();
      
      return null;
    }
  }

  /**
   * Get all public roles with caching
   */
  async getPublicRoles(): Promise<Role[]> {
    try {
      // Return cached data if it's still valid
      if (this.isCacheValid()) {
        console.log('PublicRolesService: Using cached roles data');
        return this.allRolesCache!;
      }

      // If we had a network error recently, don't retry immediately
      // but still return cached data if available
      if (!this.shouldRetryAfterNetworkError()) {
        console.warn('PublicRolesService: Skipping API call due to recent network error');
        return this.allRolesCache || [];
      }

      this.fetchAttempts++;
      console.log(`PublicRolesService: Fetching all roles (attempt ${this.fetchAttempts})`);
      
      // Try to fetch roles with a timeout
      try {
        const roles = await publicRolesApi.listPublicRoles();
        
        console.log(`PublicRolesService: API returned ${roles?.length || 0} roles`);
        
        // If we get an empty array or null/undefined, return cached data if available
        if (!roles || !Array.isArray(roles) || roles.length === 0) {
          console.warn('PublicRolesService: No roles returned from API');
          
          // If we have cached data, use it
          if (this.allRolesCache && this.allRolesCache.length > 0) {
            console.log(`PublicRolesService: Using cached data with ${this.allRolesCache.length} roles`);
            return this.allRolesCache;
          }
          
          // Return empty array - no mock data
          console.log('PublicRolesService: No roles available, returning empty array');
          return [];
        }
        
        // Filter out any invalid roles
        const validRoles = roles.filter(role => {
          if (!role || typeof role !== 'object') {
            console.warn('PublicRolesService: Invalid role object', role);
            return false;
          }
          
          // Check for required fields
          if (!role.id || !role.title) {
            console.warn(`PublicRolesService: Role missing required fields: ${JSON.stringify({id: role.id, title: role.title})}`);
            return false;
          }
          
          // Only filter based on isPublished flag
          if (!role.isPublished) {
            console.warn(`PublicRolesService: Role is not published: ${role.id}`);
            return false;
          }
          
          return true;
        });
        
        console.log(`PublicRolesService: Filtered to ${validRoles.length} published roles out of ${roles.length} total`);
        
        // Normalize roles to ensure all required fields have valid values
        const normalizedRoles = validRoles.map(role => this.normalizeRole(role));
        
        console.log(`PublicRolesService: Successfully fetched ${normalizedRoles.length} valid roles`);
        
        // Sort roles by created_at date (newest first)
        normalizedRoles.sort((a, b) => {
          // Default dates if not available
          const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
          const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
          // Sort in descending order (newest first)
          return dateB - dateA;
        });
        
        // Update cache
        this.allRolesCache = normalizedRoles;
        this.lastFetchTimestamp = Date.now();
        
        // Also update individual role cache
        normalizedRoles.forEach(role => {
          if (role.id) {
            this.roleCache.set(role.id, role);
          }
        });
        
        // Reset network error flag on successful request
        this.resetNetworkErrorState();
        
        return normalizedRoles;
      } catch (error) {
        console.error('PublicRolesService: Error fetching roles:', error);
        
        // Set network error flag and timestamp
        this.isNetworkError = true;
        this.networkErrorTimestamp = Date.now();
        
        // Return cached data if available
        if (this.allRolesCache && this.allRolesCache.length > 0) {
          console.log('PublicRolesService: Returning cached data after error');
          return this.allRolesCache;
        }
        
        // Return empty array - no mock data
        console.log('PublicRolesService: No cached data available, returning empty array');
        return [];
      }
    } catch (error) {
      console.error('PublicRolesService: Failed to fetch public roles:', error);
      
      // Set network error flag and timestamp
      this.isNetworkError = true;
      this.networkErrorTimestamp = Date.now();
      
      // Return cached data if available
      if (this.allRolesCache && this.allRolesCache.length > 0) {
        console.log('PublicRolesService: Returning cached data after error');
        return this.allRolesCache;
      }
      
      // Return empty array - no mock data
      console.log('PublicRolesService: No cached data available, returning empty array');
      return [];
    }
  }
  
  /**
   * Normalize a role to ensure all required fields have valid values
   */
  private normalizeRole(role: any): Role {
    // Only set default values for missing fields
    return {
      ...role,
      id: role.id || '',
      title: role.title || 'Untitled Role',
      status: role.status || 'Intake',
      priority: role.priority || 'Normal',
      jobType: role.jobType || 'Full-time',
      keyResponsibilities: Array.isArray(role.keyResponsibilities) ? role.keyResponsibilities : [],
      requiredSkills: role.requiredSkills || {},
      preferredSkills: role.preferredSkills || {},
      certificates: Array.isArray(role.certificates) ? role.certificates : [],
      keyStakeholders: Array.isArray(role.keyStakeholders) ? role.keyStakeholders : [],
      interviewProcess: Array.isArray(role.interviewProcess) ? role.interviewProcess : [],
      location: role.location || { city: '', remoteStatus: 'On-site' },
      team: role.team || '',
      aboutCompany: role.aboutCompany || '',
      aboutTeam: role.aboutTeam || '',
      education: role.education || { value: '', isRequired: false },
      compensation: role.compensation || { min: 0, max: 0, currency: 'USD', equity: false },
      benefits: role.benefits || { 
        vacationDays: 0, 
        healthInsurance: false,
        dentalInsurance: false,
        visionInsurance: false,
        lifeInsurance: false,
        retirement401k: false,
        stockOptions: false,
        otherBenefits: []
      },
      startDate: role.startDate || '',
      created_at: role.createdAt || role.created_at || new Date().toISOString(),
      updated_at: role.updatedAt || role.updated_at || new Date().toISOString(),
      jobPosting: role.jobPosting || '',
      isPublished: role.isPublished || false
    };
  }
}

export const publicRolesService = new PublicRolesService(); 