'use client';

import { apiClient } from '@/lib/api/client/axios';
import type { Role, RoleStatus, InterviewStage } from '@/types/role';
import { handleApiError } from '@/lib/utils/errors';
import { API_ENDPOINTS } from '@/lib/api/constants';
import { ApiResult } from '@/lib/api/types/api';

export interface ApiResponse<T> {
  data: T;
  status: string;
  message?: string;
}

export interface CreateRoleRequest extends Omit<Role, 'id' | 'createdAt' | 'updatedAt'> {
  interviewProcess: InterviewStage[];
}

const API_PATH = API_ENDPOINTS.ROLES;

// Custom type for end-call response to match backend
export interface EndCallResponse {
  status: 'success' | 'error' | 'partial_success';
  message?: string;
}

// Add these interfaces at the top of the file after imports
interface TranscriptData {
  messages: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
  }>;
  metadata?: {
    duration?: number;
    status?: string;
    [key: string]: unknown;
  };
}

interface TranscriptResponse {
  id: string;
  status: string;
  messages: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
  }>;
  metadata?: {
    duration?: number;
    status?: string;
    [key: string]: unknown;
  };
}


// Add this interface at the top of the file
interface CreateApplicationRequest {
  name: string;
  email: string;
  phone?: string;
  resume_url?: string;
  role_id: string;
  status?: 'pending' | 'interviewing' | 'accepted' | 'rejected';
}

export const rolesApi = {
  // List all roles
  listRoles: async (): Promise<Role[]> => {
    try {
      console.log('RolesApi: Fetching all roles');
      const response = await apiClient.get<Role[]>(API_PATH);
      console.log(`RolesApi: Successfully fetched ${response.data?.length || 0} roles`);
      return response.data || [];
    } catch (error) {
      // Log detailed error information
      if (error instanceof Error) {
        console.error('API Error:', error);

        // If it's an Axios error, log more details
        if ('isAxiosError' in error && (error as any).isAxiosError) {
          const axiosError = error as any;
          console.error('API Error Details:', {
            status: axiosError.response?.status,
            statusText: axiosError.response?.statusText,
            data: axiosError.response?.data,
            headers: axiosError.response?.headers
          });

          // If it's a validation error (422 or 500 with validation details), try to extract partial data
          if (axiosError.response?.status === 422 ||
              (axiosError.response?.status === 500 &&
               axiosError.response?.data?.detail?.includes('validation'))) {

            console.error('Validation Error:', axiosError.response?.data);

            // If there's partial data in the response, try to use it
            if (axiosError.response?.data?.partial_data) {
              console.log('Using partial data from validation error response');
              return axiosError.response.data.partial_data || [];
            }
          }
        }
      } else {
        console.error('Unknown API Error:', error);
      }

      // Return empty array instead of throwing to prevent UI from breaking
      console.warn('Returning empty array due to API error');
      return [];
    }
  },

  /**
   * Get role by ID
   */
  getRole: async (roleId: string): Promise<ApiResult<Role>> => {
    try {
      console.log(`RolesApi: Getting role with ID: ${roleId}`);
      const response = await apiClient.get<ApiResult<Role> | Role>(`${API_PATH}/${roleId}`);

      // Check if the response is already in the expected format
      if (response.data && typeof response.data === 'object') {
        // If the response has a 'data' property, it's already in ApiResult format
        if ('data' in response.data) {
          console.log(`RolesApi: Role ${roleId} fetched successfully (ApiResult format)`);
          return response.data as ApiResult<Role>;
        }

        // If the response is a direct Role object, wrap it in ApiResult format
        console.log(`RolesApi: Role ${roleId} fetched successfully (direct format)`);
        return {
          status: 'success',
          data: response.data as Role
        };
      }

      // If we get here, the response is not in a valid format
      console.error(`RolesApi: Invalid response format for role ${roleId}:`, response.data);
      return {
        status: 'error',
        message: 'Invalid response format',
        detail: 'Invalid response format'
      };
    } catch (error) {
      console.error(`RolesApi: Error fetching role ${roleId}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        status: 'error',
        message: errorMessage,
        detail: errorMessage
      };
    }
  },

  // Create a new role
  createRole: async (data: CreateRoleRequest): Promise<Role> => {
    try {
      const response = await apiClient.post<Role>(API_PATH, data);
      return response.data;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Update role
  updateRole: async (roleId: string, data: Partial<CreateRoleRequest>): Promise<Role> => {
    try {
      console.log(`RolesApi.updateRole: CRITICAL UPDATE - Role ${roleId}`);
      console.log(`RolesApi.updateRole: Data keys being sent:`, Object.keys(data));
      console.log(`RolesApi.updateRole: Full data:`, JSON.stringify(data, null, 2));

      // CRITICAL FIX: Special handling for publish/unpublish operations
      // Detection: Check if this is a publish/unpublish operation (only contains isPublished with optional status)
      const isPublishUnpublishOperation = 'isPublished' in data && Object.keys(data).length <= 2
        && (!('status' in data) || (Object.keys(data).length === 2 && 'status' in data));

      if (isPublishUnpublishOperation) {
        console.log(`RolesApi.updateRole: ⚠️ DETECTED PUBLISH/UNPUBLISH OPERATION`);

        // Create a minimal update object with ONLY the required fields
        const safeUpdateData: Partial<CreateRoleRequest> = {
          isPublished: data.isPublished
        };

        // Only include status if explicitly provided
        if ('status' in data) {
          safeUpdateData.status = data.status;
        }

        console.log(`RolesApi.updateRole: ⚠️ USING STRICT MINIMAL UPDATE:`, JSON.stringify(safeUpdateData, null, 2));

        // Replace the data with our safe version
        data = safeUpdateData;
      }

      // VERIFICATION: Ensure we're not sending interview process data unless explicitly requested
      if ('interviewProcess' in data) {
        console.log(`RolesApi.updateRole: ⚠️ WARNING - interviewProcess is included in this update`);

        // Check if we're only updating isPublished/status (from publish/unpublish process)
        const keysToCheck = Object.keys(data).filter(k => k !== 'interviewProcess');
        if (keysToCheck.length <= 2 &&
            keysToCheck.every(k => k === 'isPublished' || k === 'status')) {
          console.error(`RolesApi.updateRole: ❌ ERROR - Attempted to update interviewProcess during publish/unpublish!`);
          console.error(`RolesApi.updateRole: ⚠️ REMOVING interviewProcess to prevent data loss`);

          // Create a copy without the interviewProcess
          const safeData = {...data};
          delete safeData.interviewProcess;

          // Replace data with safer version
          data = safeData;
          console.log(`RolesApi.updateRole: ✅ FIXED - Sending only:`, JSON.stringify(data, null, 2));
        }
      } else {
        console.log(`RolesApi.updateRole: ✅ GOOD - No interviewProcess in this update`);
      }

      // ADDITIONAL SAFETY: For publish/unpublish, get current role first
      let currentRoleData: Role | null = null;
      if (isPublishUnpublishOperation) {
        try {
          console.log(`RolesApi.updateRole: 🔍 Getting current role data before publish/unpublish for safety`);
          const response = await apiClient.get<Role>(`${API_PATH}/${roleId}`);
          currentRoleData = response.data;
          console.log(`RolesApi.updateRole: ✅ Got current role data successfully`);
        } catch (getError) {
          console.error(`RolesApi.updateRole: ❌ Failed to get current role data:`, getError);
          // Continue with the update even if this fails
        }
      }

      // Send the request
      const response = await apiClient.put<Role>(`${API_PATH}/${roleId}`, data);

      console.log(`RolesApi.updateRole: ✅ Success - Role ${roleId} updated successfully`);
      console.log(`RolesApi.updateRole: Response data keys:`, Object.keys(response.data));

      // FINAL SAFETY CHECK: If this was a publish/unpublish and we have the previous role data,
      // verify that critical data didn't get lost in the process
      if (isPublishUnpublishOperation && currentRoleData) {
        const updatedRole = response.data;

        // Check for potential data loss in interview process
        if (currentRoleData.interviewProcess?.length > 0 &&
           (!updatedRole.interviewProcess || updatedRole.interviewProcess.length < currentRoleData.interviewProcess.length)) {
          console.error(`RolesApi.updateRole: ❌ DETECTED DATA LOSS in interview process!`);
          console.error(`RolesApi.updateRole: Original stages: ${currentRoleData.interviewProcess.length}, Updated stages: ${updatedRole.interviewProcess?.length || 0}`);

          // Restore the interview process in the returned data
          updatedRole.interviewProcess = currentRoleData.interviewProcess;
          console.log(`RolesApi.updateRole: ✅ Restored interview process in returned data`);
        }

        // Check for other critical field loss
        const criticalFields = ['location', 'title', 'summary', 'keyResponsibilities'] as const;
        for (const field of criticalFields) {
          const currentValue = currentRoleData[field];
          const updatedValue = updatedRole[field];

          if (currentValue && !updatedValue) {
            console.error(`RolesApi.updateRole: ❌ DETECTED DATA LOSS in field: ${field}`);
            (updatedRole as any)[field] = currentValue;
            console.log(`RolesApi.updateRole: ✅ Restored ${field} in returned data`);
          }
        }
      }

      return response.data;
    } catch (error) {
      console.error(`RolesApi.updateRole: ❌ Error updating role ${roleId}:`, error);

      // Detailed error logging
      if (error instanceof Error) {
        console.error(`RolesApi.updateRole: Error message: ${error.message}`);
        console.error(`RolesApi.updateRole: Error stack: ${error.stack}`);

        // If axios error, log response data
        if ('isAxiosError' in error && (error as any).isAxiosError) {
          const axiosError = error as any;
          console.error(`RolesApi.updateRole: Response status: ${axiosError.response?.status}`);
          console.error(`RolesApi.updateRole: Response data:`, axiosError.response?.data);
        }
      }

      return handleApiError(error);
    }
  },

  // Update role status
  updateRoleStatus: async (roleId: string, status: RoleStatus): Promise<ApiResult<void>> => {
    try {
      const response = await apiClient.patch<ApiResult<void>>(
        `${API_PATH}/${roleId}/status`,
        { status }
      );
      return response.data;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Delete role
  deleteRole: async (roleId: string): Promise<ApiResult<void>> => {
    try {
      const response = await apiClient.delete<ApiResult<void>>(`${API_PATH}/${roleId}`);
      return response.data;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Get intake transcripts
  getIntakeTranscripts: async (roleId: string): Promise<any[]> => {
    try {
      const response = await apiClient.get<any[]>(`${API_PATH}/${roleId}/intake-transcripts`);
      return response.data;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Get interview transcripts
  getInterviewTranscripts: async (roleId: string): Promise<any[]> => {
    try {
      const response = await apiClient.get<any[]>(`${API_PATH}/${roleId}/interview-transcripts`);
      return response.data;
    } catch (error) {
      return handleApiError(error);
    }
  },

  /**
   * Create an intake transcript
   */
  createIntakeTranscript: async (roleId: string, transcriptData: TranscriptData): Promise<ApiResult<TranscriptResponse>> => {
    const response = await apiClient.post<ApiResult<TranscriptResponse>>(`${API_PATH}/${roleId}/intake-transcripts`, transcriptData);
    return response.data;
  },

  /**
   * Update an intake transcript
   */
  updateIntakeTranscript: async (roleId: string, transcriptId: string, transcriptData: Partial<TranscriptData>): Promise<ApiResult<TranscriptResponse>> => {
    const response = await apiClient.put<ApiResult<TranscriptResponse>>(`${API_PATH}/${roleId}/intake-transcripts/${transcriptId}`, transcriptData);
    return response.data;
  },

  /**
   * Complete an intake transcript
   */
  completeIntakeTranscript: async (roleId: string, transcriptId: string): Promise<ApiResult<TranscriptResponse>> => {
    const response = await apiClient.patch<ApiResult<TranscriptResponse>>(`${API_PATH}/${roleId}/intake-transcripts/${transcriptId}/complete`);
    return response.data;
  },

  /**
   * Create an interview transcript
   */
  createInterviewTranscript: async (roleId: string, transcriptData: TranscriptData): Promise<ApiResult<TranscriptResponse>> => {
    const response = await apiClient.post<ApiResult<TranscriptResponse>>(`${API_PATH}/${roleId}/interview-transcripts`, transcriptData);
    return response.data;
  },

  /**
   * Update an interview transcript
   */
  updateInterviewTranscript: async (roleId: string, transcriptId: string, transcriptData: Partial<TranscriptData>): Promise<ApiResult<TranscriptResponse>> => {
    const response = await apiClient.put<ApiResult<TranscriptResponse>>(`${API_PATH}/${roleId}/interview-transcripts/${transcriptId}`, transcriptData);
    return response.data;
  },

  /**
   * Complete an interview transcript
   */
  completeInterviewTranscript: async (roleId: string, transcriptId: string): Promise<ApiResult<TranscriptResponse>> => {
    const response = await apiClient.patch<ApiResult<TranscriptResponse>>(`${API_PATH}/${roleId}/interview-transcripts/${transcriptId}/complete`);
    return response.data;
  },

  // End an intake call and process the transcript
  endIntakeCall: async (roleId: string, transcriptId: string, userId: string, isEnrichment: boolean = false, roleData?: Partial<Role>): Promise<EndCallResponse> => {
    try {
      const response = await apiClient.post<EndCallResponse>(
        `/realtime/end-call`,
        {
          role_id: roleId,
          transcript_id: transcriptId,
          user_id: userId,
          is_enrichment: isEnrichment,
          role_data: roleData
        }
      );
      return response.data;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Parse job description
  parseJobDescription: async (file: File): Promise<{ content: string }> => {
    try {
      const formData = new FormData();
      formData.append('jd_file', file);

      const response = await apiClient.post<{ content: string }>(
        `${API_PATH}/parse-job-description`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          transformRequest: [(data) => data],
        }
      );
      return response.data;
    } catch (error) {
      return handleApiError(error);
    }
  },

  // Create a placeholder role for intake calls
  createPlaceholderRole: async (userId: string): Promise<Role> => {
    try {
      // Create a minimal role with just required fields
      const placeholderRole = {
        title: "New Role",
        summary: "Placeholder role created for intake call. Details will be updated after the call.",
        status: "Intake" as RoleStatus,
        user_id: userId,
        interviewProcess: [
          {
            stage: "Screening",
            duration: "30 minutes",
            customInstructions: ""
          }
        ]
      };

      const response = await apiClient.post<Role>(API_PATH, placeholderRole);
      return response.data;
    } catch (error) {
      console.error("Error creating placeholder role:", error);
      return handleApiError(error);
    }
  },

  // Generate a job posting from a transcript
  generateJobPosting: async (roleId: string, transcriptId: string): Promise<ApiResult<{ job_posting: string }>> => {
    try {
      console.log('RolesApi: Generating job posting for role:', roleId, 'with transcript:', transcriptId);
      const response = await apiClient.post<ApiResult<{ job_posting: string }>>(
        `${API_PATH}/${roleId}/generate-job-posting`,
        { transcript_id: transcriptId }
      );
      console.log('RolesApi: Job posting response:', response.data);
      return response.data;
    } catch (error) {
      console.error("Error generating job posting:", error);
      return handleApiError(error);
    }
  },

  // Enrich a role with data from a transcript
  enrichRole: async (roleId: string, transcriptId: string): Promise<ApiResult<{ enriched_data: Partial<Role> }>> => {
    try {
      console.log('RolesApi: Enriching role:', roleId, 'with transcript:', transcriptId);
      const response = await apiClient.post<any>(
        `${API_PATH}/${roleId}/enrich-role`,
        { transcript_id: transcriptId }
      );

      console.log('RolesApi: Role enrichment raw response:', response);
      console.log('RolesApi: Role enrichment data:', response.data);

      // Check if the response has the expected format
      if (response.data && response.data.enriched_data) {
        return response.data;
      } else if (response.data && response.data.status === 'success' && response.data.message) {
        // The backend returned a success message but no enriched_data
        console.error('RolesApi: Backend returned success but no enriched_data');
        throw new Error('Backend returned success but no enriched data');
      } else {
        console.error('RolesApi: Unexpected response format:', response.data);
        throw new Error('Unexpected response format from server');
      }
    } catch (error) {
      console.error("Error enriching role:", error);
      return handleApiError(error);
    }
  },

  // Get the job posting for a role
  getJobPosting: async (roleId: string): Promise<ApiResult<{ job_posting: string, generated_at?: string }>> => {
    try {
      console.log('RolesApi: Getting job posting for role:', roleId);
      const response = await apiClient.get<ApiResult<{ job_posting: string, generated_at?: string }>>(
        `${API_PATH}/${roleId}/job-posting`
      );
      console.log('RolesApi: Job posting response:', response.data);
      return response.data;
    } catch (error) {
      console.error("Error getting job posting:", error);
      return handleApiError(error);
    }
  },

  // Delete intake transcript
  deleteIntakeTranscript: async (roleId: string, transcriptId: string): Promise<ApiResult<void>> => {
    try {
      console.log('RolesApi: Deleting intake transcript:', transcriptId, 'for role:', roleId);
      const response = await apiClient.put<ApiResult<void>>(
        `${API_PATH}/${roleId}/intake-transcripts/${transcriptId}`,
        { deleted: true }
      );
      console.log('RolesApi: Delete transcript response:', response.data);
      return response.data;
    } catch (error) {
      console.error("Error deleting intake transcript:", error);
      return handleApiError(error);
    }
  },

  // Upload role intake recording
  uploadIntakeRecording: async (roleId: string, file: File): Promise<ApiResult<{ url: string }>> => {
    try {
      // Create form data to send the file
      const formData = new FormData();
      formData.append('file', file);

      const response = await apiClient.post<ApiResult<{ url: string }>>(
        `${API_PATH}/${roleId}/intake-recording`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    } catch (error) {
      return handleApiError(error);
    }
  },

  /**
   * Get public role by ID
   */
  getPublicRole: async (roleId: string): Promise<ApiResult<Role>> => {
    const response = await apiClient.get<ApiResult<Role>>(`${API_PATH}/public/${roleId}`);
    return response.data;
  },

  // Create an application for a public role
  createApplication: async (roleId: string, applicationData: CreateApplicationRequest, resumeFile?: File): Promise<ApiResult<{ id: string }>> => {
    try {
      console.log('RolesApi: Creating application for role:', roleId);

      // If a resume file is provided, upload it first
      if (resumeFile) {
        const formData = new FormData();
        formData.append('file', resumeFile);

        const uploadResponse = await apiClient.post<{ url: string }>(
          `${API_PATH}/${roleId}/upload-resume`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        );

        // Update the application data with the resume URL
        applicationData.resume_url = uploadResponse.data.url;
      }

      // Create the application
      const response = await apiClient.post<ApiResult<{ id: string }>>(
        `${API_PATH}/${roleId}/applications`,
        applicationData
      );

      return response.data;
    } catch (error) {
      console.error('RolesApi: Error creating application:', error);
      throw error;
    }
  },

  // Get public job posting (no authentication required)
  getPublicJobPosting: async (roleId: string): Promise<ApiResult<{ job_posting: string }>> => {
    try {
      console.log('RolesApi: Fetching public job posting for role:', roleId);
      const response = await apiClient.get<ApiResult<{ job_posting: string }>>(
        `${API_PATH}/public/${roleId}/job-posting`
      );
      console.log('RolesApi: Public job posting fetched');
      return response.data;
    } catch (error) {
      console.error(`RolesApi: Error getting public job posting for role ${roleId}:`, error);
      return handleApiError(error);
    }
  },

  // List public roles (no authentication required)
  listPublicRoles: async (): Promise<ApiResult<any[]>> => {
    try {
      console.log('RolesApi: Fetching public roles');
      const response = await apiClient.get<ApiResult<any[]>>(`${API_PATH}/public`);
      const successResponse = response.data;
      console.log(`RolesApi: Successfully fetched ${successResponse.status === 'success' && 'data' in successResponse ? successResponse.data.length : 0} public roles`);
      return response.data;
    } catch (error) {
      console.error('RolesApi: Error fetching public roles:', error);
      return handleApiError(error);
    }
  },
};