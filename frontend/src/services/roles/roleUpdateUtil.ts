import { ServiceError } from '@/lib/utils/errors';
import { rolesService } from './service';
import { API_URL } from '@/config/constants';
import type { Role, InterviewStage, SkillLevel, LocationType, JobType } from '@/types/role';

export const VALID_ROLE_STATUSES = [
  'Intake',
  'Sourcing',
  'Screening',
  'Deep_Dive',
  'In_Person',
  'Offer',
  'Accepted',
  'Rejected',
  'Closed',
] as const;

export type RoleStatus = typeof VALID_ROLE_STATUSES[number];

export const VALID_ROLE_PRIORITIES = ['Normal', 'Expedited'] as const;
export type RolePriority = typeof VALID_ROLE_PRIORITIES[number];

export interface RoleUpdateOptions {
  title?: string;
  summary?: string;
  status?: RoleStatus;
  priority?: RolePriority;
  keyResponsibilities?: string[];
  compensation?: {
    min: string | number;
    max: string | number;
    currency: string;
    equity: boolean;
  };
  interviewProcess?: InterviewStage[];
  hiringManagerContact?: string;
  location?: {
    type: LocationType;
    city: string;
  };
  jobType?: JobType;
  team?: string;
  aboutCompany?: string;
  aboutTeam?: string;
  requiredSkills?: Record<string, SkillLevel>;
  preferredSkills?: Record<string, SkillLevel>;
  education?: {
    value: string;
    isRequired: boolean;
  };
  certificates?: string[];
  keyStakeholders?: string[];
  benefits?: {
    vacationDays: number;
    healthInsurance: boolean;
    dentalInsurance: boolean;
    visionInsurance: boolean;
    lifeInsurance: boolean;
    retirement401k: boolean;
    stockOptions: boolean;
    otherBenefits: string[];
  };
  yearsOfExperience?: string;
  startDate?: string;
  validateFields?: boolean;
  skipCache?: boolean;
}

export const isValidRoleStatus = (status: string): status is RoleStatus => {
  return VALID_ROLE_STATUSES.includes(status as RoleStatus);
};

export const isValidRolePriority = (priority: string): priority is RolePriority => {
  return VALID_ROLE_PRIORITIES.includes(priority as RolePriority);
};

export const updateRoleStatus = async (roleId: string, status: RoleStatus): Promise<void> => {
  if (!isValidRoleStatus(status)) {
    throw new Error(`Invalid role status: ${status}`);
  }

  const response = await fetch(`${API_URL}/api/v1/roles/${roleId}/status`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ status }),
  });

  if (!response.ok) {
    throw new Error(`Failed to update role status: ${response.statusText}`);
  }
};

export class RoleUpdateUtil {
  /**
   * Standardized method to update a role
   * @param roleId - The ID of the role to update
   * @param updates - Partial role data to update
   * @param options - Update options
   */
  static async updateRole(
    roleId: string,
    updates: RoleUpdateOptions,
    options: RoleUpdateOptions = { validateFields: true }
  ): Promise<Role> {
    try {
      // Validate the role ID
      if (!roleId) {
        throw new Error('Role ID is required');
      }

      // Validate updates if required
      if (options.validateFields) {
        this.validateUpdates(updates);
      }

      // Transform updates if needed (e.g., array fields, dates)
      const transformedUpdates = this.transformUpdates(updates);

      // Perform the update via service layer
      const updatedRole = await rolesService.updateRole(roleId, transformedUpdates);
      if (!updatedRole) {
        throw new Error('Failed to update role');
      }

      return updatedRole;
    } catch (error) {
      console.error('Error updating role:', error);
      throw new ServiceError(
        error instanceof Error ? error.message : 'Failed to update role',
        error
      );
    }
  }

  /**
   * Validate required fields if present in updates
   */
  private static validateUpdates(updates: RoleUpdateOptions): void {
    if (updates.status && !isValidRoleStatus(updates.status)) {
      throw new Error(`Invalid role status: ${updates.status}`);
    }
    if (updates.priority && !isValidRolePriority(updates.priority)) {
      throw new Error(`Invalid role priority: ${updates.priority}`);
    }
    // Add more validation as needed
  }

  /**
   * Transform role updates to ensure correct data structure
   */
  private static transformUpdates(updates: RoleUpdateOptions): Partial<Role> {
    const transformed: Partial<Role> = {
      ...updates,
      keyResponsibilities: updates.keyResponsibilities || [],
    };

    if (updates.compensation) {
      transformed.compensation = {
        min: updates.compensation.min.toString(),
        max: updates.compensation.max.toString(),
        currency: updates.compensation.currency,
        equity: updates.compensation.equity,
      };
    }

    if (updates.interviewProcess) {
      transformed.interviewProcess = updates.interviewProcess;
    }

    return transformed;
  }
} 