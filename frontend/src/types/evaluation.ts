/**
 * Extended evaluation data types
 * These types extend the base evaluation types from the API
 */

import { Evaluation as BaseEvaluation } from '@/services/evaluation/api';

/**
 * Extended evaluation data interface with additional properties
 * used in the evaluation report UI
 */
export interface ExtendedEvaluationData {
  // Base properties from the API
  overallScore: number;
  decision: 'Go' | 'No Go' | 'Maybe';
  criteria?: Array<{
    name: string;
    score: number;
    feedback: string;
  }>;
  questionAssessments?: Array<{
    questionId?: string;
    question: string;
    answer: string;
    score: number;
    feedback: string;
  }>;
  summary: string;
  strengths?: string[];
  weaknesses?: string[];
  interviewerNotes?: string;

  // Extended properties for legacy data format support
  evaluation_summary?: {
    decision?: 'Go' | 'No Go' | 'Maybe';
    overall_score?: number;
    summary?: string;
  };
  metadata?: {
    role_id?: string;
    application_id?: string;
    interview_id?: string;
    evaluated_at?: string;
    evaluated_by?: string;
  };
  decision_reasoning?: {
    final_recommendation?: string;
    key_factors?: string[];
    strengths?: string[];
    concerns?: string[];
  };
  scorecard_evaluation?: Array<{
    competency: string;
    reasoning: string;
    score: number;
    weight: number;
    weighted_score: number;
  }>;
  question_analysis?: Array<{
    question: string;
    answer: string;
    evaluation: string;
    related_competencies?: string[];
    strengths?: string[];
    weaknesses?: string[];
  }>;
  between_the_lines?: Array<{
    criteria: string;
    observation: string;
    impact: string;
    aspect?: 'Strength' | 'Area for Improvement';
  }>;
  disqualifier_check?: Array<{
    disqualifier: string;
    present: boolean;
    explanation: string;
  }>;
}

/**
 * Extended evaluation interface that includes the extended data
 */
export interface ExtendedEvaluation extends BaseEvaluation {
  data?: ExtendedEvaluationData;
}

/**
 * Extended evaluation response interface
 */
export interface ExtendedEvaluationResponse {
  data: ExtendedEvaluation;
  status: string;
  message?: string;
  evaluation?: ExtendedEvaluation;
}
