export type RoleLevel = 'Entry Level' | 'Junior' | 'Mid-Level' | 'Senior' | 'Lead' | 'Principal' | 'Distinguished';

export const INTERVIEW_TYPES = [
  'Screening',
  'Cultural & Company fit',
  'Behavioral',
  'Domain Expert',
  'Technical Challenge with Code',
  'Home Assignment',
  'Experience & Role fit',
  'Advance Problem Solving',
  'Team Ethics'
] as const;

export const SKILL_LEVELS = ['Beginner', 'Intermediate', 'Advanced', 'Expert'] as const;

export type SkillLevel = 'Beginner' | 'Intermediate' | 'Expert';
export type InterviewType = typeof INTERVIEW_TYPES[number];

export interface RoleDetails {
    summary: string | null;
    preferred_skills: string[];
    required_skills: string[];
    team_dynamics: string | null;
    success_metrics: string | null;
    technical_challenges: string | null;
    stakeholder_interactions: string | null;
}

export type RoleStatus =
  | 'Intake'
  | 'Sourcing'
  | 'Screening'
  | 'Deep_Dive'
  | 'In_Person'
  | 'Offer'
  | 'Accepted'
  | 'Rejected'
  | 'Closed';

export type RolePriority = 'Normal' | 'Expedited';

export type JobType = 'Full-time' | 'Part-time' | 'Contract' | 'Internship' | 'Full-Time' | 'Part-Time';

export type LocationType = 'Remote' | 'Hybrid' | 'On-site';

export interface Location {
  type: LocationType;
  remoteStatus?: LocationType;  // For backward compatibility with backend
  city: string;
}

export interface Compensation {
  min: string | number;
  max: string | number;
  currency: string;
  equity: boolean;
}

export interface Benefits {
  vacationDays: number;
  healthInsurance: boolean;
  dentalInsurance: boolean;
  visionInsurance: boolean;
  lifeInsurance: boolean;
  retirement401k: boolean;
  stockOptions: boolean;
  otherBenefits: string[];
}

export interface Education {
  value: string;
  isRequired: boolean;
}

export interface InterviewStage {
  stage: string;
  duration: string;
  customInstructions?: string;
  state?: 'Draft' | 'Live' | 'Paused' | 'Closed';
  stageIndex?: number;
  templateId?: string;
  statistics?: {
    applicantsCount?: number;
    passedCount?: number;
    highestScore?: number;
  };
}

export interface CandidateProgression {
    total_candidates: number;
    in_review: number;
    interviewed: number;
    offered: number;
    rejected: number;
}

export enum RoleStage {
  INTAKE = 'INTAKE',
  SOURCING = 'SOURCING',
  SCREENING = 'SCREENING',
  DEEP_DIVE = 'DEEP_DIVE',
  IN_PERSON = 'IN_PERSON',
  OFFER = 'OFFER',
  DECISION = 'DECISION',
  CLOSED = 'CLOSED',
}

export type RoleDecision = 'ACCEPTED' | 'REJECTED';

export const roleStageColors: Record<RoleStage | RoleDecision, { bg: string; text: string }> = {
  [RoleStage.INTAKE]: { bg: 'bg-sky-500', text: 'text-sky-500' },
  [RoleStage.SOURCING]: { bg: 'bg-violet-500', text: 'text-violet-500' },
  [RoleStage.SCREENING]: { bg: 'bg-indigo-500', text: 'text-indigo-500' },
  [RoleStage.DEEP_DIVE]: { bg: 'bg-blue-500', text: 'text-blue-500' },
  [RoleStage.IN_PERSON]: { bg: 'bg-cyan-500', text: 'text-cyan-500' },
  [RoleStage.OFFER]: { bg: 'bg-emerald-500', text: 'text-emerald-500' },
  [RoleStage.DECISION]: { bg: 'bg-amber-500', text: 'text-amber-500' },
  'ACCEPTED': { bg: 'bg-green-500', text: 'text-green-500' },
  'REJECTED': { bg: 'bg-red-500', text: 'text-red-500' },
  [RoleStage.CLOSED]: { bg: 'bg-slate-500', text: 'text-slate-500' },
};

export interface Role {
  id?: string;
  title: string;
  summary?: string;
  keyResponsibilities: string[];
  yearsOfExperience: string;
  requiredSkills: Record<string, SkillLevel>;
  preferredSkills: Record<string, SkillLevel>;
  education: {
    value: string;
    isRequired: boolean;
  };
  certificates: string[];
  team: string;
  keyStakeholders: string[];
  location: Location;
  jobType: JobType;
  aboutCompany: string;
  aboutTeam: string;
  compensation: Compensation;
  benefits: Benefits;
  startDate: string;
  priority: RolePriority;
  hiringManagerId: string;
  hiringManagerContact: string;
  status: RoleStatus;
  level?: RoleLevel;
  interviewProcess: InterviewStage[];
  created_at?: string;
  updated_at?: string;
  jobPosting?: string; // Generated job posting in markdown format
  isPublished?: boolean; // Whether the role is published to the job board
}

export interface CreateRoleRequest extends Omit<Role, 'id'> {
  hiringManagerId: string;
  hiringManagerContact: string;
}

export const statusToStage: Record<RoleStatus, RoleStage> = {
  'Intake': RoleStage.INTAKE,
  'Sourcing': RoleStage.SOURCING,
  'Screening': RoleStage.SCREENING,
  'Deep_Dive': RoleStage.DEEP_DIVE,
  'In_Person': RoleStage.IN_PERSON,
  'Offer': RoleStage.OFFER,
  'Accepted': RoleStage.DECISION,
  'Rejected': RoleStage.DECISION,
  'Closed': RoleStage.CLOSED
};

/**
 * PublicRole interface for public role pages
 * Contains only the fields that should be publicly visible
 */
export interface PublicRole {
  id: string;
  title: string;
  summary?: string;
  keyResponsibilities: string[];
  yearsOfExperience: string;
  level?: RoleLevel;
  requiredSkills: Record<string, SkillLevel>;
  preferredSkills: Record<string, SkillLevel>;
  education: {
    value: string;
    isRequired: boolean;
  };
  certificates: string[];
  team: string;
  location: Location;
  jobType: JobType;
  interviewProcess: InterviewStage[];
  jobPosting?: string;
  is_public?: boolean;
  settings?: {
    evaluateResumes?: boolean;
    allowDirectInterview?: boolean;
    requireEmail?: boolean;
    requirePhone?: boolean;
    formCustomizations?: {
      title?: string;
      description?: string;
      theme?: 'light' | 'dark';
    };
  };
}

/**
 * CandidateApplication interface for storing candidate applications
 */
export interface CandidateApplication {
  id: string;
  roleId: string;
  fullName: string;
  email: string;
  phoneNumber?: string;
  resumeUrl?: string;
  linkedInUrl?: string;
  githubUrl?: string;
  portfolioUrl?: string;
  status: 'pending' | 'applied' | 'interviewed' | 'rejected' | 'shortlisted' | 'hired';
  created_at: string;
  updated_at: string;
  interviewTranscriptId?: string;
  evaluationId?: string;
  evaluationDecision?: string;
  evaluationScore?: number;
  candidateId?: string; // ID of the candidate (often the email address)
  is_public?: boolean; // Whether this is a public application
}

/**
 * ExtendedApplication interface for displaying application data with additional fields
 */
export interface ExtendedApplication extends CandidateApplication {
  roleName?: string;
  resumeText?: string;
  interviews?: Array<any>; // Add proper interview type if available
  evaluationData?: {
    scorecard?: {
      overallScore: number;
      technicalSkills: {
        score: number;
        evaluation: string;
        strengths: string[];
        gaps: string[];
      };
      softSkills: {
        score: number;
        evaluation: string;
        strengths: string[];
        gaps: string[];
      };
      experienceRelevance: {
        score: number;
        evaluation: string;
        strengths: string[];
        gaps: string[];
      };
      educationCertifications: {
        score: number;
        evaluation: string;
        strengths: string[];
        gaps: string[];
      };
    };
    recommendation?: {
      decision: string;
      confidence: string;
      reasoning: string;
    };
    feedback?: {
      interviewFocus: string[];
      candidateStrengths: string[];
      improvementAreas: string[];
    };
    overallScore?: number;
    decision?: string;
    confidence?: string;
    reasoning?: string;
    keyStrengths?: string[];
    keyGaps?: string[];
    // Add missing properties from the errors
    full_evaluation?: any; // Add proper type if available
    metadata?: {
      parsedText?: string;
      filename?: string;
      model?: string;
      evaluationType?: string;
      evaluation_type?: string;
      timestamp?: string | number;
    };
  };
  interviewStage?: string;
  candidateId?: string;
}