// Common Types
export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'Recruiva';
  content: string;
  timestamp: number | string;
  status: 'pending' | 'completed' | 'failed';
  isSpeaking?: boolean;
  sender?: {
    name: string;
    avatar_url?: string;
  };
  function_call?: {
    name: string;
    arguments: string;
  };
  metadata?: {
    [key: string]: unknown;
    speechId?: string;
    eventId?: string;
    speechStart?: number;
  };
} 