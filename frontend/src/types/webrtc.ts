export interface WebRTCMessage {
  id: string;
  role: 'user' | 'Recruiva' | 'system';
  content: string;
  timestamp?: number;
  status?: 'pending' | 'complete';
  type?: 'text' | 'function_call' | 'error';
  function_call?: {
    name: string;
    arguments: Record<string, unknown>;
  };
}

export interface WebRTCFunction {
  name: string;
  arguments: Record<string, unknown>;
}

// Use the global types from TypeScript's lib.dom.d.ts
export type RTCPeerConnection = globalThis.RTCPeerConnection;
export type MediaStream = globalThis.MediaStream; 