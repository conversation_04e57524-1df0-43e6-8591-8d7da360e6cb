# Frontend Source Directory Structure

## Overview

The Recruiva frontend is a modern, type-safe React application built with Next.js. It provides a seamless user experience for role management, real-time interviews, evaluation, and more. The frontend communicates with the backend via REST API endpoints, leveraging secure authentication and real-time technologies for a responsive, interactive platform.

---

## Directory Organization

```
src/
├── app/              # Next.js app directory (routing, layouts, pages, SSR)
├── components/       # Modular React components
│   ├── features/     # Feature-specific UI (roles, interviews, evaluation, etc.)
│   ├── layout/       # App-wide layouts (dashboard, sidebar, header, etc.)
│   ├── providers/    # Context provider components
│   ├── role/         # Role management UIs
│   ├── ui/           # Shared UI components (form, modal, button, etc.)
│   └── video-call/   # WebRTC video call components
├── config/           # App-wide configuration/constants
├── contexts/         # React context definitions (e.g., Auth, Sidebar)
├── hooks/            # Custom React hooks (WebRTC, toast, API, roles, etc.)
├── lib/              # Core libraries/utilities
│   ├── api/          # API client (Axios), types, error handling
│   ├── firebase/     # Firebase client, auth, Firestore, storage
│   ├── utils/        # Utility functions
│   └── auth.ts       # Auth helpers
├── providers/        # Application-level providers (React Query, etc.)
├── services/         # Business logic, API abstraction (roles, interviews, evaluation, etc.)
├── styles/           # Global styles (CSS/SCSS)
├── types/            # TypeScript type definitions (domain models, API, etc.)
└── __tests__/        # Unit/integration tests
```

---

## Architecture & Integration

- **Next.js App Router:** Uses `/app` directory for file-based routing, layouts, and server-side rendering.
- **Component-Driven UI:** All UI is composed of modular, reusable components for features, layouts, and shared UI.
- **State Management:** Uses React Context for global UI state (auth, sidebar, etc.) and React Query for server state (API data, caching, mutations).
- **Authentication:** Firebase Authentication is integrated via custom hooks and context. Protected routes and session state are managed client-side, with tokens sent to backend for secured API calls.
- **API Communication:**
  - All backend communication is via REST API endpoints (see backend/README.md).
  - Axios-based API client (`lib/api/client`) with interceptors for auth tokens and error handling.
  - TypeScript types for all API requests/responses ensure type safety.
- **Real-Time Features:**
  - WebRTC-based video interviews (see `components/video-call/`, `hooks/useWebRTC.ts`)
  - Real-time updates via Firebase/Firestore and backend events
- **Business Logic:**
  - Encapsulated in `services/` (roles, evaluation, interviews, etc.)
  - UI components are presentational; business logic and data-fetching are separated via hooks/services.
- **Testing:**
  - Tests are colocated in `__tests__/` and use Jest/React Testing Library.

---

## Folder Details

- **app/**: Next.js routing, layouts, and entry points for all pages/views.
- **components/**: All UI, separated by feature and reusability. Includes dashboard, role management, interviews, evaluation, and shared UI.
- **config/**: Application-wide configuration (constants, environment, etc.).
- **contexts/**: React Contexts for global state (authentication, sidebar, etc.).
- **hooks/**: Custom hooks for API, roles, WebRTC, evaluation, and UI utilities.
- **lib/**: Core utilities, API client, Firebase integration, and authentication helpers.
- **providers/**: Top-level React providers (React Query, context, etc.).
- **services/**: Business/domain logic, API abstraction, and state integration.
- **types/**: TypeScript type definitions for roles, users, interviews, API, etc.

---

## Integration with Backend

- All business operations (role CRUD, interviews, evaluation, etc.) are performed via secure API calls to the backend (see backend/README.md for endpoint details).
- Auth tokens are managed via Firebase and attached to API requests for protected operations.
- Real-time interview/video features are powered by WebRTC and backend signaling.
- Data consistency is ensured via React Query and real-time listeners (Firebase/Firestore).

---

## Best Practices & Security

- Use modular, reusable components and hooks
- Type all API requests/responses with TypeScript
- Store secrets and API keys in environment variables
- Use HTTPS for all API calls
- Protect routes and sensitive UI with authentication/authorization
- Validate and sanitize all user input
- Follow Firebase security rules for data access
- Mask sensitive data in logs and error messages
- Write unit/integration tests for all business logic and UI

---

## For Backend Details
See [backend/README.md](../../backend/README.md) for backend architecture, API endpoints, and integration details.

## Architecture Overview

### 1. API Layer (`lib/api/`)

- **Client Configuration**: Axios-based HTTP client with interceptors
- **Error Handling**: Centralized error handling with custom error types
- **Type Definitions**: Comprehensive TypeScript types for API requests/responses
- **Constants**: API endpoints and configuration constants

### 2. Authentication (`lib/auth.ts`)

- Firebase Authentication integration
- User session management
- Authentication state handling
- Protected route implementation

### 3. Components Layer

- **Feature Components**: Feature-specific UI components
- **Layout Components**: Page layouts and structure
- **UI Components**: Reusable UI elements
- **Providers**: Component-specific context providers
- **Video Call**: WebRTC-based video call implementation

### 4. Services Layer (`services/`)

- Business logic implementation
- Data transformation and validation
- State management integration
- API integration

### 5. Hooks Layer (`hooks/`)

- **useWebRTC**: WebRTC functionality management
- **use-toast**: Toast notification system
- Custom hooks for shared functionality

### 6. Firebase Integration (`lib/firebase/`)

- Firebase initialization
- Firestore database operations
- Storage operations
- Real-time updates

## Data Schemas

### 1. Role Schema

```
typescript
interface Role {
  id: string;
  title: string;
  description: string;
  status: RoleStatus;
  priority: RolePriority;
  createdAt: Date;
  updatedAt: Date;
  intakeTranscripts?: IntakeTranscript[];
  intakeCalls?: IntakeCall[];
}
```

### 2. User Schema

```
typescript
interface User {
  id: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  roles: string[];
  createdAt: Date;
}
```

## Navigation Flow

1. **Authentication Flow**

   - Login/Register pages
   - Protected route handling
   - Role-based access control
2. **Role Management Flow**

   - Role listing
   - Role creation/editing
   - Intake process
   - Video call integration

## Usage Guidelines

### 1. API Integration

```
typescript
// Using the API client
import { apiClient } from '@/lib/api/client';const fetchData = async () => {
  const response = await apiClient.get('/endpoint');
  return response.data;
};
```




### 2. Firebase Operations

```
typescript
// Using Firebase services
import { db } from '@/lib/firebase';const getData = async () => {
  const doc = await db.collection('collection').doc('id').get();
  return doc.data();
};
```




### 3. Component Development

```
typescript
// Creating new components
import { useToast } from '@/hooks/use-toast';const Component = () => {
  const { toast } = useToast();  return (
    // Component implementation
  );
};
```


### 4. Error Handling

```
typescript
import { APIError } from '@/lib/api/errors';try {
  // API call
} catch (error) {
  if (error instanceof APIError) {
    // Handle API error
  }
}
```


## Best Practices

1. **Code Organization**

   - Follow the established directory structure
   - Keep components focused and reusable
   - Use TypeScript for type safety
2. **State Management**

   - Use React Query for server state
   - Use context for global UI state
   - Keep component state local when possible
3. **Error Handling**

   - Use the centralized error handling system
   - Provide meaningful error messages
   - Log errors appropriately
4. **Performance**

   - Implement proper memoization
   - Use lazy loading for components
   - Optimize API calls and caching
5. **Testing**

   - Write unit tests for services
   - Test components in isolation
   - Use mock data for testing

## Security Guidelines

1. **Authentication**

   - Always use protected routes
   - Implement proper token management
   - Handle session expiration
2. **API Calls**

   - Use HTTPS endpoints
   - Implement proper error handling
   - Validate input data
3. **Firebase Security**

   - Follow Firebase security rules
   - Implement proper data access controls
   - Use environment variables for keys
