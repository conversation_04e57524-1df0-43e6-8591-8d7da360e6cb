'use client';

import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
import { getAuth, Auth } from 'firebase/auth';
import { getFirestore, Firestore } from 'firebase/firestore';
import { getStorage, FirebaseStorage } from 'firebase/storage';

const firebaseConfig = {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
    measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
let app: FirebaseApp;
let auth: Auth;
let db: Firestore;
let storage: FirebaseStorage;

try {
    if (!getApps().length) {
        console.log('Initializing Firebase with config:', {
            ...firebaseConfig,
            apiKey: firebaseConfig.apiKey ? '**********' : undefined
        });
        app = initializeApp(firebaseConfig);
    } else {
        app = getApps()[0];
    }

    auth = getAuth(app);
    db = getFirestore(app);
    storage = getStorage(app);

    console.log('Firebase initialized successfully:', {
        isInitialized: !!app,
        hasAuth: !!auth,
        hasDb: !!db,
        hasStorage: !!storage,
        projectId: firebaseConfig.projectId
    });
} catch (error) {
    console.error('Error initializing Firebase:', error);
    throw error; // Re-throw to prevent usage if initialization fails
}

export { app, auth, db, storage };

// Re-export transcript functions
export { saveTranscript, updateTranscript, completeTranscript } from './transcript';

// Re-export applications functions
export {
  saveApplication,
  getApplication,
  updateApplication,
  updateApplicationWithTranscript
} from './applications';

// Export simplified functions
export {
  uploadResume,
  saveResumeEvaluation,
  checkExistingApplication
} from './simplified-evaluations';