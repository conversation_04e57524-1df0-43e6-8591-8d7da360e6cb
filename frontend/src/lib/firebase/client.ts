'use client';

import { initializeApp, getApp, FirebaseApp } from 'firebase/app';
import { 
    getAuth, 
    setPersistence, 
    browserLocalPersistence, 
    onAuthStateChanged, 
    GoogleAuthProvider 
} from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { firebaseConfig, validateConfig } from './config';

// Validate configuration
validateConfig();

// Initialize Firebase
let app;
try {
    app = getApp();
    console.log('[Firebase] Using existing Firebase app');
} catch {
    app = initializeApp(firebaseConfig);
    console.log('[Firebase] Created new Firebase app');
}

// Initialize services
const auth = getAuth(app);
auth.useDeviceLanguage();

// Configure default settings
const db = getFirestore(app);
const storage = getStorage(app);

// Create a reusable Google provider
const googleProvider = new GoogleAuthProvider();
googleProvider.addScope('profile');
googleProvider.addScope('email');

// Set persistence to LOCAL and verify auth state
let persistencePromise: Promise<void>;
if (typeof window !== 'undefined') {
    console.log('[Firebase] Setting up auth persistence...');
    persistencePromise = setPersistence(auth, browserLocalPersistence)
        .then(() => {
            console.log('[Firebase] Auth persistence set to LOCAL');
            return new Promise<void>((resolve) => {
                const unsubscribe = onAuthStateChanged(auth, (user) => {
                    console.log('[Firebase] Initial auth state verified:', {
                        isAuthenticated: !!user,
                        email: user?.email,
                        emailVerified: user?.emailVerified,
                        uid: user?.uid,
                        isAnonymous: user?.isAnonymous,
                        metadata: user?.metadata
                    });
                    unsubscribe();
                    resolve();
                });
            });
        })
        .catch((error) => {
            console.error('[Firebase] Error setting auth persistence:', error);
            throw error;
        });
} else {
    persistencePromise = Promise.resolve();
}

// Initialize Firebase and wait for persistence
const initializeFirebase = () => {
    return {
        app: app as FirebaseApp,
        auth,
        db,
        storage,
        persistencePromise,
        googleProvider
    };
};

// Export initialized Firebase
const firebase = initializeFirebase();
export default firebase;
export { auth, db, storage, persistencePromise, googleProvider }; 