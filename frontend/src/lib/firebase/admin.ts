import { getApps, initializeApp, cert, getApp } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { getStorage } from 'firebase-admin/storage';
import { adminConfig } from './config';

function initAdmin() {
    try {
        // Return existing app if already initialized
        if (getApps().length > 0) {
            return getApp();
        }

        // Initialize the app with the admin config
        return initializeApp({
            credential: cert(adminConfig.credential),
            projectId: adminConfig.credential.projectId,
            storageBucket: adminConfig.storageBucket
        });
    } catch (error) {
        console.error('Error initializing Firebase Admin:', error);
        
        // In development, try to initialize with just project ID if service account fails
        if (process.env.NODE_ENV === 'development') {
            const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID;
            if (projectId) {
                console.warn('Using development fallback for Firebase Admin');
                return getApps().length ? getApp() : initializeApp({
                    projectId: projectId,
                    storageBucket: adminConfig.storageBucket
                });
            }
        }
        throw error;
    }
}

// Initialize the app
const app = initAdmin();

// Export the admin services
export const adminAuth = getAuth(app);
export const adminDb = getFirestore(app);
export const adminStorage = getStorage(app);
export default app; 