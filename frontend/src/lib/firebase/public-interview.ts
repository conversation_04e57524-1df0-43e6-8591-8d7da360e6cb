// No direct Firestore imports needed as we're using the backend API

// Define a type that matches what the API expects
type ApiMessage = {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
};

/**
 * Utility functions for handling public interview sessions
 * These functions help ensure consistent handling of public interview sessions
 * and proper saving of transcripts to Firestore
 */

/**
 * Checks if we're in a public interview flow
 * @returns boolean indicating if this is a public interview
 */
export const isPublicInterviewFlow = (): boolean => {
  try {
    return typeof window !== 'undefined' && localStorage.getItem('isPublicInterview') === 'true';
  } catch (e) {
    return false;
  }
};

/**
 * Gets the OpenAI-style session ID (sess_*) if available, otherwise returns the provided ID
 * @param sessionId The session ID to check
 * @param transcriptId Optional transcript ID to check as fallback
 * @returns The OpenAI-style session ID if available, otherwise the original ID
 */
export const getOpenAISessionId = (sessionId: string, transcriptId?: string): string => {
  // If sessionId is already in OpenAI format, use it
  if (sessionId && sessionId.startsWith('sess_')) {
    return sessionId;
  }

  // If transcriptId is in OpenAI format, use it instead
  if (transcriptId && transcriptId.startsWith('sess_')) {
    return transcriptId;
  }

  // Check localStorage for a stored OpenAI session ID
  try {
    const storedSessionId = localStorage.getItem('openai_session_id');
    if (storedSessionId && storedSessionId.startsWith('sess_')) {
      return storedSessionId;
    }
  } catch (e) {
    // Ignore localStorage errors
  }

  // Return the original session ID if no OpenAI format ID is found
  return sessionId;
};

/**
 * Saves a public interview transcript to Firestore
 * @param sessionId The session ID
 * @param messages The messages to save
 * @param roleId The role ID
 * @param applicationId Optional application ID
 * @returns Promise resolving to success status
 */
export const savePublicInterviewTranscript = async (
  sessionId: string,
  messages: Array<{
    id: string;
    role: 'user' | 'assistant' | 'system' | 'Recruiva';
    content: string;
    timestamp: number | string;
    status?: 'pending' | 'completed' | 'failed';
    isSpeaking?: boolean;
  }>,
  roleId: string,
  applicationId?: string
): Promise<boolean> => {
  try {
    // Only log when there are significant changes to reduce noise
    if (messages.length % 5 === 0 || messages.length < 5) {
      console.log(`Saving public interview transcript for session ${sessionId} (${messages.length} messages)`);
    }

    // First save to localStorage as a backup
    try {
      localStorage.setItem(`public_interview_transcript_${sessionId}`, JSON.stringify(messages));
    } catch (localStorageError) {
      console.error('Error saving to localStorage:', localStorageError);
      // Continue with API save attempt
    }

    // Get application ID from parameters or localStorage
    const appId = applicationId || localStorage.getItem('applicationId') || localStorage.getItem('lastApplicationId');

    // Use a single approach to save the transcript
    let saveSuccess = false;

    // Use the backend API to save the transcript (most reliable method)
    try {
      // Dynamically import to avoid circular dependencies
      const { realtimeApi } = await import('@/services/realtime');

      // Convert messages to the format expected by the API
      const apiMessages: ApiMessage[] = messages.map(msg => ({
        id: msg.id,
        role: msg.role === 'Recruiva' || msg.role === 'system' ? 'assistant' : 'user',
        content: msg.content,
        timestamp: typeof msg.timestamp === 'number' ? msg.timestamp : Date.now()
      }));

      // Use our utility function to get the OpenAI-style session ID if available
      const finalSessionId = getOpenAISessionId(sessionId);

      // Store the OpenAI session ID in localStorage for future use
      if (finalSessionId.startsWith('sess_')) {
        try {
          localStorage.setItem('openai_session_id', finalSessionId);
        } catch (e) {
          // Ignore localStorage errors
        }
      }

      const response = await realtimeApi.updatePublicTranscript(
        finalSessionId,
        finalSessionId, // Use the same ID for both session and transcript
        roleId,
        apiMessages,
        appId || undefined
      );

      if (response && response.status === 'success') {
        console.log('Successfully saved transcript via backend API');
        saveSuccess = true;
        return true;
      }
    } catch (apiError) {
      console.warn('Error saving transcript via API:', apiError);
      // Don't attempt direct Firestore access to avoid permission errors
    }

    return saveSuccess;
  } catch (error) {
    console.error('Error saving public interview transcript to Firestore:', error);
    return false;
  }
};

/**
 * Completes a public interview transcript via the backend API
 * @param sessionId The session ID
 * @param roleId The role ID
 * @returns Promise resolving to success status
 */
export const completePublicInterviewTranscript = async (
  sessionId: string,
  roleId: string
): Promise<boolean> => {
  try {
    console.log(`Completing public interview transcript for session ${sessionId}`);

    // Mark as completed in localStorage
    try {
      localStorage.setItem(`public_interview_transcript_${sessionId}_completed`, 'true');
      localStorage.setItem(`public_interview_completed_at_${sessionId}`, new Date().toISOString());
    } catch (localStorageError) {
      console.error('Error updating localStorage:', localStorageError);
    }

    // Use the backend API to complete the transcript
    try {
      const { realtimeApi } = await import('@/services/realtime');

      // Use our utility function to get the OpenAI-style session ID if available
      const finalSessionId = getOpenAISessionId(sessionId);

      // Store the OpenAI session ID in localStorage for future use
      if (finalSessionId.startsWith('sess_')) {
        try {
          localStorage.setItem('openai_session_id', finalSessionId);
        } catch (e) {
          // Ignore localStorage errors
        }
      }

      const result = await realtimeApi.endInterview({
        role_id: roleId,
        transcript_id: finalSessionId,
        user_id: 'anonymous'
      });

      console.log('Public interview completion API result:', result);
      return true;
    } catch (apiError) {
      console.error('Error calling completion API for public interview:', apiError);
      return false;
    }
  } catch (error) {
    console.error('Error completing public interview transcript:', error);
    return false;
  }
};

/**
 * Gets the current session ID from localStorage or creates a new one
 * @returns The session ID
 */
export const getOrCreateSessionId = (): string => {
  try {
    // Try to get existing session ID from localStorage
    const existingSessionId = localStorage.getItem('currentSessionId');
    if (existingSessionId) {
      return existingSessionId;
    }

    // Create a new session ID if none exists
    const newSessionId = `public-interview-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    localStorage.setItem('currentSessionId', newSessionId);
    return newSessionId;
  } catch (e) {
    // Generate a fallback session ID if localStorage is not available
    return `public-interview-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }
};
