'use client';

/**
 * Applications module for interacting with application data in Firebase
 */

import { collection, doc, setDoc, getDoc, updateDoc, serverTimestamp, query, where, getDocs } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { db, storage } from './index';
import { CandidateApplication, ExtendedApplication } from '@/types/role';
import { v4 as uuidv4 } from 'uuid';
import { getAuth } from 'firebase/auth';

/**
 * Check if we're in a public interview flow
 * @returns true if this is a public interview flow
 */
export const isPublicInterviewFlow = (): boolean => {
  // Check URL for interview path
  const isInInterviewPath = typeof window !== 'undefined' &&
    window.location.pathname.includes('/instant-interview/');

  // Check authentication state
  const auth = getAuth();
  const isUnauthenticated = !auth.currentUser;

  return isInInterviewPath && isUnauthenticated;
};

/**
 * Save a new application to Firebase
 *
 * @param roleId Role ID this application is for
 * @param application Application data to save
 * @returns The application ID
 */
export const saveApplication = async (
  roleId: string,
  application: Omit<CandidateApplication, 'id' | 'roleId' | 'created_at' | 'updated_at'>
): Promise<string> => {
  try {
    // Generate a unique ID for the application
    const applicationId = uuidv4();

    // Create the application document
    const applicationData: CandidateApplication = {
      ...application,
      id: applicationId,
      roleId,
      status: 'applied', // Using 'applied' as the initial status instead of 'pending'
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Store minimal tracking information in localStorage
    try {
      localStorage.setItem('applicationId', applicationId);
      localStorage.setItem('lastApplicationId', applicationId);
      localStorage.setItem('roleId', roleId);

      // Store these for reference in other operations but not the full application
      if (application.email) {
        localStorage.setItem('candidateId', application.email);
        localStorage.setItem('lastApplicationEmail', application.email);
      }
      if (application.fullName) {
        localStorage.setItem('lastApplicationName', application.fullName);
      }

      // Store full application data in localStorage as backup
      localStorage.setItem(`application_${applicationId}`, JSON.stringify(applicationData));
    } catch (storageError) {
      console.error('Error storing application IDs in localStorage:', storageError);
      // Continue anyway - this is just for tracking
    }

    // Note: We no longer directly save to Firestore collections from the frontend
    // The backend will handle creating the application in Firestore when the resume is evaluated
    // This avoids permission errors and ensures a single source of truth

    return applicationId;
  } catch (error) {
    console.error('Error saving application:', error);

    // Generate and return an application ID anyway to not disrupt the flow
    // The application data should be in localStorage
    const fallbackId = uuidv4();
    return fallbackId;
  }
};

/**
 * Save a resume evaluation to Firebase
 *
 * @param applicationId Application ID to save evaluation for
 * @param evaluationData Evaluation data from the API
 * @param parsedResumeText Optional parsed resume text to store
 * @returns The evaluation ID
 */
export const saveResumeEvaluation = async (
  applicationId: string,
  evaluationData: any,
  parsedResumeText?: string
): Promise<string> => {
  try {
    // Generate an ID for the evaluation
    const evaluationId = uuidv4();

    // Add metadata to the evaluation
    const evaluationWithMetadata = {
      ...evaluationData,
      id: evaluationId,
      applicationId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Save to localStorage as a backup
    try {
      localStorage.setItem(`evaluation_${evaluationId}`, JSON.stringify(evaluationWithMetadata));

      // Save parsed resume text if provided
      if (parsedResumeText) {
        localStorage.setItem('parsedResumeText', parsedResumeText);
        console.log('Saved parsed resume text to localStorage for interview context');
      }

      // Also save as application evaluation for easier retrieval
      localStorage.setItem(`application_evaluation_${applicationId}`, JSON.stringify({
        evaluationId,
        applicationId,
        evaluation: evaluationWithMetadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      console.log(`Evaluation data saved to localStorage with ID: ${evaluationId}`);
    } catch (localStorageError) {
      console.warn('Failed to save evaluation to localStorage:', localStorageError);
    }

    // Note: We no longer directly save to Firestore collections from the frontend
    // The backend handles creating the evaluation in Firestore when the resume is evaluated
    // This avoids permission errors and ensures a single source of truth

    return evaluationId;
  } catch (error) {
    console.error('Error saving resume evaluation:', error);
    // Generate an ID and return it to prevent disrupting the main flow
    const fallbackId = uuidv4();
    return fallbackId;
  }
};

/**
 * Upload a resume file to Firebase Storage
 *
 * @param _roleId Role ID this resume is for
 * @param file Resume file to upload
 * @param applicationId Optional application ID
 * @param parsedResumeText Optional parsed resume text
 * @returns URL to the uploaded resume
 */
export const uploadResume = async (
  _roleId: string,
  file: File,
  applicationId?: string,
  parsedResumeText?: string
): Promise<string> => {
  try {
    // Generate an application ID if not provided
    if (!applicationId) {
      applicationId = uuidv4();
      console.log(`Generated new application ID: ${applicationId}`);
    }

    // Determine the file extension
    const fileExtension = file.name.split('.').pop() || 'pdf';

    // Create a unique filename
    const fileName = `resume.${fileExtension}`;

    // Use a consistent storage path for all resumes
    const filePath = `applications/${applicationId}/${fileName}`;

    console.log(`Uploading resume to path: ${filePath}`);
    const storageRef = ref(storage, filePath);
    const snapshot = await uploadBytes(storageRef, file);

    // Get the download URL
    const downloadURL = await getDownloadURL(snapshot.ref);

    // Store minimal data in localStorage for continuity
    try {
      localStorage.setItem('resumeUrl', downloadURL);
      localStorage.setItem(`resumeUrl_${applicationId}`, downloadURL);

      // If we have parsed resume text, save that too
      if (parsedResumeText) {
        localStorage.setItem('parsedResumeText', parsedResumeText);
      }
    } catch (storageError) {
      console.error('Error storing resume data in localStorage:', storageError);
      // Continue anyway - this is just for UI continuity
    }

    // Note: We no longer update the application document directly from the frontend
    // The backend will handle updating the application with the resume URL
    // This avoids permission errors and ensures a single source of truth

    return downloadURL;
  } catch (error: unknown) {
    console.error('Error uploading resume:', error);

    // For public flows, generate a mock URL to allow the flow to continue
    if (isPublicInterviewFlow()) {
      console.log('Generating mock URL for public flow to continue');
      const mockUrl = `https://firebasestorage.googleapis.com/mock-resume/${_roleId}/${applicationId || uuidv4()}/resume.pdf`;
      return mockUrl;
    }

    throw error;
  }
};

/**
 * Get an application from Firebase
 *
 * @param applicationId Application ID to retrieve
 * @returns The application data or null if not found
 */
export const getApplication = async (applicationId: string): Promise<CandidateApplication | null> => {
  try {
    if (!applicationId) {
      console.warn('No application ID provided to getApplication');
      return null;
    }

    // Try to get application data from localStorage first
    try {
      const appDataStr = localStorage.getItem(`application_${applicationId}`);
      if (appDataStr) {
        const appData = JSON.parse(appDataStr);
        console.log(`Found application in localStorage with ID: ${applicationId}`);
        return appData as CandidateApplication;
      }
    } catch (localStorageError) {
      console.error('Error checking application in localStorage:', localStorageError);
    }

    // For public interview flows, we don't need to fetch from backend
    // as all necessary data should be in localStorage
    if (isPublicInterviewFlow()) {
      console.log('Public interview flow - using only localStorage data');
      return null;
    }

    // Use the backend API to get the application instead of direct Firestore access
    // This avoids permission errors and follows the proper architecture
    try {
      const response = await fetch(`/api/v1/applications/${applicationId}`);
      if (response.ok) {
        const applicationData = await response.json();
        console.log(`Application ${applicationId} retrieved from backend API`);

        // Cache in localStorage for future use
        localStorage.setItem(`application_${applicationId}`, JSON.stringify(applicationData));

        return applicationData;
      }
    } catch (apiError) {
      console.error('Error retrieving application from API:', apiError);
    }

    console.warn(`Application with ID ${applicationId} not found`);
    return null;
  } catch (error) {
    console.error('Error getting application:', error);
    return null;
  }
};

/**
 * Update an application in Firebase or localStorage
 *
 * @param applicationId Application ID to update
 * @param applicationData Updated application data
 */
export const updateApplication = async (
  applicationId: string,
  applicationData: Partial<CandidateApplication>
): Promise<void> => {
  try {
    // Check if this application exists in localStorage
    const localData = localStorage.getItem(`application_${applicationId}`);
    if (localData) {
      try {
        // Parse the existing data
        const existingData = JSON.parse(localData) as CandidateApplication;

        // Update the data
        const updatedData = {
          ...existingData,
          ...applicationData,
          updated_at: new Date().toISOString()
        };

        // Save back to localStorage
        localStorage.setItem(`application_${applicationId}`, JSON.stringify(updatedData));
        console.log(`Application ${applicationId} updated in localStorage`);
        return;
      } catch (localStorageError) {
        console.error('Error updating application in localStorage:', localStorageError);
      }
    }

    // For public interview flows, we only update localStorage
    if (isPublicInterviewFlow()) {
      console.log('Public interview flow - skipping backend update');
      return;
    }

    // Use the backend API to update the application instead of direct Firestore access
    try {
      const response = await fetch(`/api/v1/applications/${applicationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...applicationData,
          updated_at: new Date().toISOString()
        })
      });

      if (response.ok) {
        console.log(`Application ${applicationId} updated via backend API`);
      } else {
        console.warn(`Failed to update application via API: ${response.status}`);
      }
    } catch (apiError) {
      console.error('Error updating application via API:', apiError);
    }
  } catch (error) {
    console.error('Error updating application:', error);
    // Don't throw error to not break the flow
    console.warn('Update application failed, continuing process');
  }
};

/**
 * Update an application with interview transcript ID
 *
 * @param applicationId Application ID to update
 * @param transcriptId Interview transcript ID
 * @param stageIndex Optional stage index, defaults to 0 (screening)
 * @param stageName Optional stage name, defaults to "Screening"
 */
export const updateApplicationWithTranscript = async (
  applicationId: string,
  transcriptId: string,
  stageIndex: number = 0,
  stageName: string = "Screening"
): Promise<void> => {
  try {
    if (!applicationId || !transcriptId) {
      console.warn('Missing required ID(s) for updateApplicationWithTranscript:',
        { applicationId, transcriptId });
      return;
    }

    const sessionId = localStorage.getItem('currentSessionId');

    // Update the application in the main applications collection
    try {
      const appRef = doc(db, 'applications', applicationId);
      const appDoc = await getDoc(appRef);

      if (appDoc.exists()) {
        // Update the application status and transcript ID
        await updateDoc(appRef, {
          interviewTranscriptId: transcriptId,
          status: 'interviewed',
          updated_at: serverTimestamp()
        });

        console.log(`Application ${applicationId} updated with transcript ID: ${transcriptId}`);

        // Create an interview entry if session ID is available
        if (sessionId) {
          // Check if interview already exists for this transcript
          const interviewsRef = collection(db, 'applications', applicationId, 'interviews');
          const q = query(interviewsRef, where("transcript_id", "==", transcriptId));
          const querySnapshot = await getDocs(q);

          if (querySnapshot.empty) {
            // Create a interviews subcollection entry
            const interviewRef = doc(db, 'applications', applicationId, 'interviews', transcriptId);
            await setDoc(interviewRef, {
              transcript_id: transcriptId,
              session_id: sessionId,
              stage_index: stageIndex,
              stage_name: stageName,
              status: 'completed',
              created_at: serverTimestamp(),
              updated_at: serverTimestamp()
            });

            console.log(`Interview session created with transcript ID: ${transcriptId}, stage: ${stageName} (${stageIndex})`);
          } else {
            console.log(`Interview with transcript ID ${transcriptId} already exists, skipping creation`);
          }
        }

        return;
      }

      console.warn(`Application ${applicationId} not found in applications collection`);
    } catch (error) {
      console.error('Error updating application with transcript:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error in updateApplicationWithTranscript:', error);
    // Don't throw to not break the flow
  }
};

/**
 * Get a resume evaluation from Firebase
 *
 * @param applicationId Application ID to get evaluation for
 * @param evaluationId Optional specific evaluation ID
 * @returns The evaluation data or null if not found
 */
export const getResumeEvaluation = async (
  applicationId: string,
  evaluationId?: string
): Promise<any | null> => {
  try {
    if (!applicationId) {
      console.warn('No application ID provided to getResumeEvaluation');
      return null;
    }

    // Check localStorage first for standard evaluation storage format
    if (evaluationId) {
      const localData = localStorage.getItem(`evaluation_${evaluationId}`);
      if (localData) {
        try {
          const evaluationData = JSON.parse(localData);
          console.log(`Evaluation ${evaluationId} found in localStorage`);
          return evaluationData;
        } catch (parseError) {
          console.error('Error parsing localStorage evaluation data:', parseError);
        }
      }
    }

    // Check for application-specific evaluation backup in localStorage
    const applicationEvaluation = localStorage.getItem(`application_evaluation_${applicationId}`);
    if (applicationEvaluation) {
      try {
        const backupData = JSON.parse(applicationEvaluation);
        console.log(`Found application evaluation backup in localStorage for ${applicationId}`);

        // Return the evaluation from the backup
        if (backupData.evaluation) {
          return backupData.evaluation;
        }
      } catch (parseError) {
        console.error('Error parsing application evaluation backup:', parseError);
      }
    }

    // Check for full evaluation backup in localStorage
    const fullEvaluation = localStorage.getItem(`full_evaluation_${applicationId}`);
    if (fullEvaluation) {
      try {
        const evaluationData = JSON.parse(fullEvaluation);
        console.log(`Full evaluation backup found in localStorage for application ${applicationId}`);
        return evaluationData;
      } catch (parseError) {
        console.error('Error parsing full evaluation backup:', parseError);
      }
    }

    // Try to get candidateId from localStorage
    const candidateId = localStorage.getItem('candidateId');

    // If we have a candidateId, check the candidates collection first
    if (candidateId) {
      try {
        if (evaluationId) {
          // Get specific evaluation
          const evaluationRef = doc(
            db,
            'candidates',
            candidateId,
            'applications',
            applicationId,
            'evaluations',
            evaluationId
          );

          try {
            const evaluationDoc = await getDoc(evaluationRef);

            if (evaluationDoc.exists()) {
              const evaluationData = evaluationDoc.data();
              console.log(`Evaluation ${evaluationId} found for application ${applicationId}`);
              return evaluationData;
            }
          } catch (error) {
            console.error('Error retrieving specific evaluation:', error);
            // Continue to try other methods
          }
        } else {
          // Get most recent evaluation
          try {
            const evaluationsRef = collection(
              db,
              'candidates',
              candidateId,
              'applications',
              applicationId,
              'evaluations'
            );

            const evaluationsSnapshot = await getDocs(evaluationsRef);

            if (!evaluationsSnapshot.empty) {
              // Convert to array and sort by created_at (newest first)
              const evaluations = evaluationsSnapshot.docs.map(doc => doc.data());
              evaluations.sort((a, b) => {
                const dateA = new Date(a.created_at || 0);
                const dateB = new Date(b.created_at || 0);
                return dateB.getTime() - dateA.getTime();
              });

              // Return the most recent evaluation
              console.log(`Found ${evaluations.length} evaluations for application ${applicationId}`);
              return evaluations[0];
            }
          } catch (error) {
            console.error('Error retrieving evaluations collection:', error);
            // Continue to try other methods
          }
        }
      } catch (candidateError) {
        console.error('Error getting evaluation from candidates collection:', candidateError);
      }
    }

    // If not found in candidates collection, check the applications collection
    try {
      if (evaluationId) {
        // Get specific evaluation
        const evaluationRef = doc(
          db,
          'applications',
          applicationId,
          'evaluations',
          evaluationId
        );

        const evaluationDoc = await getDoc(evaluationRef);

        if (evaluationDoc.exists()) {
          const evaluationData = evaluationDoc.data();
          console.log(`Evaluation ${evaluationId} found for application ${applicationId}`);
          return evaluationData;
        }
      } else {
        // Get most recent evaluation
        const evaluationsRef = collection(
          db,
          'applications',
          applicationId,
          'evaluations'
        );

        const evaluationsSnapshot = await getDocs(evaluationsRef);

        if (!evaluationsSnapshot.empty) {
          // Convert to array and sort by created_at (newest first)
          const evaluations = evaluationsSnapshot.docs.map(doc => doc.data());
          evaluations.sort((a, b) => {
            const dateA = new Date(a.created_at || 0);
            const dateB = new Date(b.created_at || 0);
            return dateB.getTime() - dateA.getTime();
          });

          // Return the most recent evaluation
          console.log(`Found ${evaluations.length} evaluations for application ${applicationId}`);
          return evaluations[0];
        }
      }
    } catch (appError) {
      console.error('Error getting evaluation from applications collection:', appError);
    }

    console.warn(`No evaluation found for application ${applicationId}`);
    return null;
  } catch (error) {
    console.error('Error getting resume evaluation:', error);
    return null;
  }
};

/**
 * Check if a candidate already has an active application for a role
 *
 * @param roleId Role ID to check
 * @param email Candidate email to check
 * @returns An object with exists flag and application data if found
 */
export const checkExistingApplication = async (
  roleId: string,
  email: string
): Promise<{ exists: boolean, application?: CandidateApplication }> => {
  try {
    if (!email || !roleId) {
      console.warn('Missing email or roleId for checking existing application');
      return { exists: false };
    }

    // First check localStorage for basic application tracking only
    const lastApplicationId = localStorage.getItem('lastApplicationId');
    const applicationId = localStorage.getItem('applicationId');
    const storedRoleId = localStorage.getItem('roleId');

    // If we have application tracking info in localStorage, check if it's for the current role
    if ((lastApplicationId || applicationId) && storedRoleId === roleId) {
      const appId = applicationId || lastApplicationId;
      console.log(`Found application ID in localStorage for role ${roleId}: ${appId}`);

      // Try to get full application data from localStorage first
      try {
        const appDataStr = localStorage.getItem(`application_${appId}`);
        if (appDataStr) {
          const appData = JSON.parse(appDataStr);
          if (appData.email === email && appData.roleId === roleId) {
            console.log(`Found matching application in localStorage with ID: ${appId}`);
            return {
              exists: true,
              application: appData as CandidateApplication
            };
          }
        }
      } catch (localStorageError) {
        console.error('Error checking application in localStorage:', localStorageError);
      }

      // Query the application in Firestore to get full details
      try {
        const applicationRef = doc(db, 'applications', appId as string);
        const applicationSnap = await getDoc(applicationRef);

        if (applicationSnap.exists() && applicationSnap.data().email === email) {
          const applicationData = applicationSnap.data() as CandidateApplication;
          console.log(`Verified application exists in Firestore with ID: ${appId}`);
          return {
            exists: true,
            application: applicationData
          };
        }
      } catch (firestoreError) {
        console.error('Error checking application in Firestore:', firestoreError);
      }
    }

    // Check if we're in a public flow
    const isPublic = isPublicInterviewFlow();

    // Check collections in order of likelihood for public applications
    const collectionsToCheck = isPublic
      ? ['public_applications', 'applications']
      : ['applications', 'public_applications'];

    // Check in main applications collections first
    for (const collectionName of collectionsToCheck) {
      try {
        const applicationsRef = collection(db, collectionName);
        const q = query(applicationsRef,
          where('roleId', '==', roleId),
          where('email', '==', email)
        );

        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          const applicationData = querySnapshot.docs[0].data() as CandidateApplication;
          console.log(`Found existing application in ${collectionName} collection for role ${roleId}:`, querySnapshot.docs[0].id);

          return {
            exists: true,
            application: {
              ...applicationData,
              id: querySnapshot.docs[0].id
            }
          };
        }
      } catch (collectionError) {
        console.error(`Error checking ${collectionName} collection:`, collectionError);
      }
    }

    // Check the candidate's applications subcollection as a fallback
    try {
      const candidateId = email;
      const candidateRef = doc(db, 'candidates', candidateId);
      const candidateSnap = await getDoc(candidateRef);

      if (candidateSnap.exists()) {
        const applicationsRef = collection(db, 'candidates', candidateId, 'applications');
        const q = query(applicationsRef, where('roleId', '==', roleId));
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          const applicationData = querySnapshot.docs[0].data() as CandidateApplication;
          console.log(`Found existing application in candidate subcollection for role ${roleId}:`, applicationData.id);

          return {
            exists: true,
            application: {
              ...applicationData,
              id: querySnapshot.docs[0].id
            }
          };
        }
      }
    } catch (candidateError) {
      console.error('Error checking candidate subcollection:', candidateError);
    }

    // No existing application found
    return { exists: false };
  } catch (error) {
    console.error('Error checking for existing application:', error);
    return { exists: false };
  }
};

/**
 * Get all applications across all candidates
 *
 * @returns Array of applications with candidate and role information
 */
export const getAllApplications = async (): Promise<ExtendedApplication[]> => {
  try {
    const applications: ExtendedApplication[] = [];

    // First, check the main applications collection
    try {
      console.log('Fetching applications from main applications collection');
      const mainApplicationsRef = collection(db, 'applications');
      const mainApplicationsSnapshot = await getDocs(mainApplicationsRef);

      // Process each application from the main collection
      const mainApps = await Promise.all(mainApplicationsSnapshot.docs.map(async (appDoc) => {
        const applicationData = appDoc.data() as CandidateApplication;
        const app: ExtendedApplication = {
          ...applicationData,
          id: appDoc.id,
          candidateId: applicationData.candidateId || applicationData.email || ''
        };

        // Get role name if roleId exists
        if (app.roleId) {
          try {
            const roleRef = doc(db, 'roles', app.roleId);
            const roleSnap = await getDoc(roleRef);
            if (roleSnap.exists()) {
              app.roleName = roleSnap.data().title;
            }
          } catch (roleError) {
            console.error(`Error fetching role data for application ${app.id}:`, roleError);
          }
        }

        // Get the most recent evaluation for scoring info
        try {
          const evaluationData = await getResumeEvaluation(app.id || '');
          if (evaluationData) {
            app.evaluationData = evaluationData;
          }
        } catch (evalError) {
          console.error(`Error fetching evaluation for application ${app.id}:`, evalError);
        }

        return app;
      }));

      console.log(`Found ${mainApps.length} applications in main applications collection`);
      applications.push(...mainApps);
    } catch (mainError) {
      console.error('Error fetching from main applications collection:', mainError);
    }

    // Then, check the public_applications collection
    try {
      console.log('Fetching applications from public_applications collection');
      const publicApplicationsRef = collection(db, 'public_applications');
      const publicApplicationsSnapshot = await getDocs(publicApplicationsRef);

      // Process each application from the public collection
      const publicApps = await Promise.all(publicApplicationsSnapshot.docs.map(async (appDoc) => {
        const applicationData = appDoc.data() as CandidateApplication;
        const app: ExtendedApplication = {
          ...applicationData,
          id: appDoc.id,
          candidateId: applicationData.candidateId || applicationData.email || ''
        };

        // Get role name if roleId exists
        if (app.roleId) {
          try {
            const roleRef = doc(db, 'roles', app.roleId);
            const roleSnap = await getDoc(roleRef);
            if (roleSnap.exists()) {
              app.roleName = roleSnap.data().title;
            }
          } catch (roleError) {
            console.error(`Error fetching role data for application ${app.id}:`, roleError);
          }
        }

        return app;
      }));

      console.log(`Found ${publicApps.length} applications in public_applications collection`);
      applications.push(...publicApps);
    } catch (publicError) {
      console.error('Error fetching from public_applications collection:', publicError);
    }

    // Finally, check applications in candidate subcollections
    try {
      console.log('Fetching applications from candidate subcollections');
      const candidatesRef = collection(db, 'candidates');
      const candidatesSnapshot = await getDocs(candidatesRef);

      // For each candidate, get their applications
      const fetchPromises = candidatesSnapshot.docs.map(async (candidateDoc) => {
        const candidateId = candidateDoc.id;
        const applicationsRef = collection(db, 'candidates', candidateId, 'applications');
        const applicationsSnapshot = await getDocs(applicationsRef);

        // Process each application
        const candidateApps = await Promise.all(applicationsSnapshot.docs.map(async (appDoc) => {
          const applicationData = appDoc.data() as CandidateApplication;
          const app: ExtendedApplication = {
            ...applicationData,
            id: appDoc.id,
            candidateId
          };

          // Get role name if roleId exists
          if (app.roleId) {
            try {
              const roleRef = doc(db, 'roles', app.roleId);
              const roleSnap = await getDoc(roleRef);
              if (roleSnap.exists()) {
                app.roleName = roleSnap.data().title;
              }
            } catch (roleError) {
              console.error(`Error fetching role data for application ${app.id}:`, roleError);
            }
          }

          // Get the most recent evaluation for scoring info
          try {
            const evaluationData = await getResumeEvaluation(app.id || '');
            if (evaluationData) {
              app.evaluationData = evaluationData;
            }
          } catch (evalError) {
            console.error(`Error fetching evaluation for application ${app.id}:`, evalError);
          }

          // Get the interview stage if transcript exists
          if (app.interviewTranscriptId) {
            try {
              const interviewsRef = collection(db, 'candidates', candidateId, 'applications', app.id || '', 'interviews');
              const interviewsSnap = await getDocs(interviewsRef);

              if (!interviewsSnap.empty) {
                // Just get the stage name from the first interview
                app.interviewStage = interviewsSnap.docs[0].data().stage_name || 'Interview';
              }
            } catch (interviewError) {
              console.error(`Error fetching interview data for application ${app.id}:`, interviewError);
            }
          }

          return app;
        }));

        return candidateApps;
      });

      const candidateApplications = await Promise.all(fetchPromises);
      const flattenedCandidateApps = candidateApplications.flat();
      console.log(`Found ${flattenedCandidateApps.length} applications in candidate subcollections`);
      applications.push(...flattenedCandidateApps);
    } catch (candidateError) {
      console.error('Error fetching from candidate subcollections:', candidateError);
    }

    // Remove duplicates based on application ID
    const uniqueApplications = applications.filter((app, index, self) =>
      app.id && self.findIndex(a => a.id === app.id) === index
    );

    // Sort applications by creation date (newest first)
    uniqueApplications.sort((a, b) => {
      const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
      const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
      return dateB.getTime() - dateA.getTime();
    });

    console.log(`Returning ${uniqueApplications.length} unique applications`);
    return uniqueApplications;
  } catch (error) {
    console.error('Error fetching all applications:', error);
    return [];
  }
};