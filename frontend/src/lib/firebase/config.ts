// Firebase Configuration
export const firebaseConfig = {
    // Client Config (Public)
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

// Admin Configuration (Server-side only)
export const adminConfig = {
    credential: {
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    },
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
};

// Validation function for required config
export const validateConfig = () => {
    const requiredClientConfig = ['apiKey', 'projectId', 'appId', 'authDomain', 'storageBucket'] as const;
    for (const key of requiredClientConfig) {
        if (!firebaseConfig[key]) {
            throw new Error(`Missing required Firebase config: ${key}`);
        }
    }
}; 