import { rolesService } from '@/services/roles';
import { getAuth } from 'firebase/auth';
import { isApiError } from '@/lib/api/types/api';
import { isPublicInterviewFlow } from './applications';

// Define a type that matches what the API expects
type ApiMessage = {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
};

interface TranscriptMessage {
  id: string;
  role: 'user' | 'Recruiva';
  content: string;
  timestamp: number;
  status: 'pending' | 'completed' | 'failed';
  isSpeaking?: boolean;
}

interface Transcript {
  type: 'intake' | 'screening' | 'interview';
  status: 'in_progress' | 'completed';
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
  messages: TranscriptMessage[];
}

// This interface matches what the rolesService API expects
interface TranscriptData {
  messages: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
  }>;
  updatedAt?: string;
  messageCount?: number;
  metadata?: {
    duration?: number;
    status?: string;
    [key: string]: unknown;
  };
}

export const saveTranscript = async (
  roleId: string,
  sessionId: string,
  messages: Array<{
    id: string;
    role: 'user' | 'assistant' | 'system' | 'Recruiva';
    content: string;
    timestamp: number | string;
    status?: 'pending' | 'completed' | 'failed';
    isSpeaking?: boolean;
  }>,
  type: 'intake' | 'screening' | 'interview'
): Promise<void> => {
  try {
    // Check if this is a public interview flow
    const isPublic = isPublicInterviewFlow();

    // Get current user (only if not in public flow)
    const auth = getAuth();
    const user = auth.currentUser;
    if (!isPublic && !user) {
      throw new Error('No authenticated user found');
    }

    // Validate inputs
    if (!roleId || !sessionId || !messages || !Array.isArray(messages)) {
      throw new Error('Invalid input parameters for saveTranscript');
    }

    // Ensure messages array is not empty and has valid structure
    const validMessages = messages.filter(msg =>
      msg && typeof msg === 'object' &&
      'content' in msg &&
      'role' in msg &&
      'timestamp' in msg &&
      msg.role !== 'system' // Filter out system messages
    );

    // Create transcript data
    const transcript: Transcript = {
      type,
      status: 'in_progress',
      createdAt: new Date(),
      updatedAt: new Date(),
      messageCount: validMessages.length,
      messages: validMessages.map(msg => ({
        id: msg.id,
        role: msg.role === 'assistant' || msg.role === 'Recruiva' ? 'Recruiva' : 'user',
        content: msg.content || '',
        timestamp: typeof msg.timestamp === 'string' ? Date.parse(msg.timestamp) : (msg.timestamp || Date.now()),
        status: msg.status || 'completed',
        isSpeaking: false
      }))
    };

    // For public interviews, always save to localStorage first as a backup
    if (isPublic) {
      try {
        localStorage.setItem(`public_interview_transcript_${sessionId}`, JSON.stringify({
          ...transcript,
          messages: transcript.messages,
          createdAt: transcript.createdAt.toISOString(),
          updatedAt: transcript.updatedAt.toISOString()
        }));
        console.log(`Saved public interview transcript to localStorage as backup: ${sessionId}`);
      } catch (localStorageError) {
        console.error('Error saving public interview transcript to localStorage:', localStorageError);
        // Continue execution - this is just a backup
      }
    }

    // Use the appropriate API method based on transcript type
    let response;
    if (type === 'interview') {
      // For interview transcripts
      console.log('Saving interview transcript...');
      // Convert transcript to the format expected by the API
      const apiTranscriptData: TranscriptData = {
        messages: transcript.messages.map(msg => ({
          role: msg.role === 'Recruiva' ? 'assistant' : 'user',
          content: msg.content,
          timestamp: typeof msg.timestamp === 'number' ? msg.timestamp : Date.now()
        })),
        metadata: {
          status: transcript.status,
          duration: 0
        }
      };
      response = await rolesService.createInterviewTranscript(roleId, apiTranscriptData);
    } else {
      // For intake or screening transcripts
      console.log('Saving intake transcript...');
      // Convert transcript to the format expected by the API
      const apiTranscriptData: TranscriptData = {
        messages: transcript.messages.map(msg => ({
          role: msg.role === 'Recruiva' ? 'assistant' : 'user',
          content: msg.content,
          timestamp: typeof msg.timestamp === 'number' ? msg.timestamp : Date.now()
        })),
        metadata: {
          status: transcript.status,
          duration: 0
        }
      };
      response = await rolesService.createIntakeTranscript(roleId, apiTranscriptData);
    }

    if (isApiError(response)) {
      throw new Error(response.detail || 'Failed to save transcript');
    }

    console.log('Successfully saved transcript:', {
      roleId,
      sessionId,
      messageCount: validMessages.length,
      type
    });
  } catch (error) {
    console.error('Error saving transcript:', error);

    // For public interviews, don't propagate the error if we have a localStorage backup
    if (isPublicInterviewFlow()) {
      console.log('Using localStorage backup for public interview transcript');
      return; // Return without throwing to prevent disrupting the user flow
    }

    throw error;
  }
};

export const updateTranscript = async (
  roleId: string,
  transcriptId: string,
  messages: Array<{
    id: string;
    role: 'user' | 'assistant' | 'system' | 'Recruiva';
    content: string;
    timestamp: number | string;
    status?: 'pending' | 'completed' | 'failed';
    isSpeaking?: boolean;
  }>,
  type: 'intake' | 'screening' | 'interview' = 'intake',
  applicationId?: string
): Promise<void> => {
  try {
    console.log(`Updating ${type} transcript ${transcriptId} for role ${roleId} with ${messages.length} messages`);

    // Check if this is a public interview flow
    const isPublic = isPublicInterviewFlow();

    // Validate inputs
    if (!roleId || !transcriptId || !messages || !Array.isArray(messages)) {
      throw new Error('Invalid input parameters for updateTranscript');
    }

    // Ensure messages array has valid structure
    const validMessages = messages.filter(msg =>
      msg && typeof msg === 'object' &&
      'content' in msg &&
      'role' in msg &&
      'timestamp' in msg &&
      msg.role !== 'system' // Filter out system messages
    );

    // Format the transcript data
    const transcriptData = {
      updatedAt: new Date(),
      messageCount: validMessages.length,
      messages: validMessages.map(msg => ({
        id: msg.id,
        role: msg.role === 'assistant' || msg.role === 'Recruiva' ? 'Recruiva' : 'user',
        content: msg.content || '',
        timestamp: typeof msg.timestamp === 'string' ? Date.parse(msg.timestamp) : (msg.timestamp || Date.now()),
        status: msg.status || 'completed',
        isSpeaking: false
      }))
    };

    // For public interviews, always save to localStorage first as a backup
    if (isPublic) {
      try {
        localStorage.setItem(`public_interview_transcript_${transcriptId}`, JSON.stringify({
          ...transcriptData,
          messages: transcriptData.messages,
          updatedAt: transcriptData.updatedAt.toISOString()
        }));
        console.log(`Saved updated public interview transcript to localStorage: ${transcriptId}`);

        // For public interviews, also try to update via the realtime API
        if (isPublic) {
          try {
            // Import the realtime service dynamically to avoid circular dependencies
            const { realtimeApi } = await import('@/services/realtime');

            // IMPROVEMENT: Also try to write directly to Firestore for public interviews
            try {
              // Import Firebase modules dynamically to avoid circular dependencies
              const { getFirestore, doc, setDoc, updateDoc, serverTimestamp } = await import('firebase/firestore');
              const db = getFirestore();

              // Check if document exists first
              const docRef = doc(db, "public_interview_sessions", transcriptId);

              try {
                // Try to update the existing document
                await updateDoc(docRef, {
                  messages: validMessages.map(msg => ({
                    id: msg.id,
                    role: msg.role === 'assistant' || msg.role === 'Recruiva' ? 'Recruiva' : 'user',
                    content: msg.content || '',
                    timestamp: typeof msg.timestamp === 'string' ? Date.parse(msg.timestamp) : (msg.timestamp || Date.now()),
                    status: msg.status || 'completed',
                    isSpeaking: false
                  })),
                  updated_at: serverTimestamp(),
                  message_count: validMessages.length
                });
                console.log('Successfully updated public interview transcript directly in Firestore');
              } catch (updateError) {
                // If update fails, try to create a new document
                console.log('Document may not exist, creating new document:', updateError);
                await setDoc(docRef, {
                  session_id: transcriptId,
                  transcript_id: transcriptId,
                  role_id: roleId,
                  created_at: serverTimestamp(),
                  updated_at: serverTimestamp(),
                  status: 'in_progress',
                  is_public: true,
                  messages: validMessages.map(msg => ({
                    id: msg.id,
                    role: msg.role === 'assistant' || msg.role === 'Recruiva' ? 'Recruiva' : 'user',
                    content: msg.content || '',
                    timestamp: typeof msg.timestamp === 'string' ? Date.parse(msg.timestamp) : (msg.timestamp || Date.now()),
                    status: msg.status || 'completed',
                    isSpeaking: false
                  })),
                  message_count: validMessages.length,
                  applicationId: applicationId || localStorage.getItem('applicationId') || localStorage.getItem('lastApplicationId')
                });
                console.log('Successfully created public interview transcript directly in Firestore');
              }
            } catch (firestoreError) {
              console.error('Error writing to Firestore directly:', firestoreError);
              // Continue with API call as fallback
            }

            // Convert messages to the format expected by the API
            const apiMessages: ApiMessage[] = validMessages.map(msg => ({
              id: msg.id,
              role: msg.role === 'Recruiva' || msg.role === 'system' ? 'assistant' : 'user',
              content: msg.content,
              timestamp: typeof msg.timestamp === 'number' ? msg.timestamp : Date.now()
            }));

            // Make sure we're using the OpenAI session ID (starts with 'sess_') if available
            let finalSessionId = transcriptId;
            let finalTranscriptId = transcriptId;

            // If we have a session ID that starts with 'sess_', use it
            if (transcriptId.startsWith('sess_')) {
              finalSessionId = transcriptId;
              finalTranscriptId = transcriptId;
            }

            // Call the updatePublicTranscript method
            const result = await realtimeApi.updatePublicTranscript(
              finalSessionId,
              finalTranscriptId,
              roleId,
              apiMessages,
              applicationId
            );

            console.log('Public transcript update result:', result);

            // If successful, we can return early
            if (result && result.status === 'success') {
              console.log('Successfully updated public transcript via realtime API');
              return;
            }
          } catch (realtimeError) {
            console.error('Error updating public transcript via realtime API:', realtimeError);
            console.log('Continuing with localStorage backup only');
            // Continue execution - we already have a localStorage backup
            return;
          }
        }
      } catch (localStorageError) {
        console.error('Error saving public interview transcript to localStorage:', localStorageError);
        // For public interviews, we'll try the API anyway
      }
    }

    // If not public or if we want to try the API for authenticated users
    if (!isPublic) {
      // Implement retry logic for transcript updates
      const MAX_RETRIES = 3;
      const RETRY_DELAY = 2000; // 2 seconds

      let retryCount = 0;
      let success = false;

      while (retryCount < MAX_RETRIES && !success) {
        try {
          if (retryCount > 0) {
            console.log(`Retry attempt ${retryCount}/${MAX_RETRIES} for updating transcript`);
          }

          // Convert transcript data to the format expected by the API
          const apiTranscriptData: TranscriptData = {
            messages: transcriptData.messages.map(msg => ({
              role: msg.role === 'Recruiva' ? 'assistant' : 'user',
              content: msg.content,
              timestamp: typeof msg.timestamp === 'number' ? msg.timestamp : Date.now()
            })),
            updatedAt: transcriptData.updatedAt.toISOString(),
            messageCount: transcriptData.messageCount,
            metadata: {
              status: 'in_progress',
              duration: 0
            }
          };

          // Use the appropriate API method based on transcript type
          if (type === 'interview') {
            await rolesService.updateInterviewTranscript(roleId, transcriptId, apiTranscriptData);
          } else {
            await rolesService.updateIntakeTranscript(roleId, transcriptId, apiTranscriptData);
          }

          success = true;
          console.log(`Successfully updated ${type} transcript ${transcriptId}`);
        } catch (error) {
          retryCount++;
          console.error(`Error updating transcript (attempt ${retryCount}/${MAX_RETRIES}):`, error);

          if (retryCount < MAX_RETRIES) {
            console.log(`Retrying in ${RETRY_DELAY}ms...`);
            await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
          } else {
            // If all retries fail, throw the error
            throw error;
          }
        }
      }
    }
  } catch (error) {
    console.error('Error updating transcript:', error);

    // For public interviews, don't propagate the error if we have a localStorage backup
    if (isPublicInterviewFlow()) {
      console.log('Using localStorage backup for public interview transcript update');
      return; // Return without throwing to prevent disrupting the user flow
    }

    throw new Error('Failed to update transcript');
  }
};

export const completeTranscript = async (
  roleId: string,
  sessionId: string,
  type: 'intake' | 'screening' | 'interview' = 'intake'
): Promise<void> => {
  try {
    console.log(`Completing ${type} transcript ${sessionId} for role ${roleId}`);

    // Check if this is a public interview flow
    const isPublic = isPublicInterviewFlow();

    // For public interviews, mark as completed in localStorage and return
    if (isPublic) {
      try {
        // Get existing transcript from localStorage
        const transcriptData = localStorage.getItem(`public_interview_transcript_${sessionId}`);
        if (transcriptData) {
          // Mark as completed by adding a flag
          localStorage.setItem(`public_interview_transcript_${sessionId}_completed`, 'true');
          localStorage.setItem(`public_interview_completed_at_${sessionId}`, new Date().toISOString());
          console.log(`Marked public interview transcript as completed in localStorage: ${sessionId}`);
        }

        // We no longer directly update Firestore to avoid permission errors
        // Instead, we rely on the backend API to handle this

        // Call the backend API to ensure the transcript is processed
        try {
          const { realtimeApi } = await import('@/services/realtime');

          // Make sure we're using the OpenAI session ID (starts with 'sess_') if available
          let finalSessionId = sessionId;

          // If we have a session ID that starts with 'sess_', use it
          if (sessionId.startsWith('sess_')) {
            finalSessionId = sessionId;
          }

          const result = await realtimeApi.endInterview({
            role_id: roleId,
            transcript_id: finalSessionId,
            user_id: 'anonymous'
          });
          console.log('Public interview completion API result:', result);
        } catch (apiError) {
          console.error('Error calling completion API for public interview:', apiError);
          // Continue execution - don't throw error for public interviews
        }

        return;
      } catch (error) {
        console.error('Error completing public interview transcript in localStorage:', error);
        // Continue execution - don't throw error for public interviews
        return;
      }
    }

    // Get current user
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Implement retry logic for completing transcript
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 2000; // 2 seconds

    let retryCount = 0;
    let success = false;

    while (retryCount < MAX_RETRIES && !success) {
      try {
        if (retryCount > 0) {
          console.log(`Retry attempt ${retryCount}/${MAX_RETRIES} for completing transcript`);
        }

        // Use the appropriate API method based on transcript type
        let response;
        if (type === 'interview') {
          response = await rolesService.completeInterviewTranscript(roleId, sessionId);
        } else {
          response = await rolesService.completeIntakeTranscript(roleId, sessionId);
        }

        if (isApiError(response)) {
          throw new Error(response.detail || `Failed to complete ${type} transcript`);
        }

        success = true;
        console.log(`Successfully completed ${type} transcript ${sessionId}`);
      } catch (error) {
        retryCount++;
        console.error(`Error completing transcript (attempt ${retryCount}/${MAX_RETRIES}):`, error);

        if (retryCount < MAX_RETRIES) {
          console.log(`Retrying in ${RETRY_DELAY}ms...`);
          await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
        } else {
          // If all retries fail, throw the error
          throw error;
        }
      }
    }
  } catch (error) {
    console.error('Error completing transcript:', error);
    throw error;
  }
};