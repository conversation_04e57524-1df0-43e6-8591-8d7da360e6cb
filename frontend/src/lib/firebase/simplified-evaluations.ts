import { v4 as uuidv4 } from 'uuid';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { isPublicInterviewFlow } from '@/lib/firebase/public-interview';

/**
 * Upload a resume file to Firebase Storage
 *
 * This simplified version doesn't try to save to Firestore since the backend handles that
 *
 * @param roleId Role ID to associate with the resume
 * @param file Resume file to upload
 * @param applicationId Optional application ID to associate with the resume
 * @returns URL to the uploaded resume
 */
export const uploadResume = async (
  roleId: string,
  file: File,
  applicationId?: string
): Promise<string> => {
  try {
    // Generate an application ID if not provided
    const appId = applicationId || uuidv4();

    // Create a reference to the storage location
    const storage = getStorage();
    const storageRef = ref(storage, `resumes/${roleId}/${appId}/${file.name}`);

    // Upload the file
    const snapshot = await uploadBytes(storageRef, file);
    console.log('Resume uploaded successfully');

    // Get the download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    console.log('Resume download URL:', downloadURL);

    // Save the URL to localStorage
    try {
      localStorage.setItem(`resumeUrl_${appId}`, downloadURL);

      // Update application data in localStorage if it exists
      const appDataStr = localStorage.getItem(`application_${appId}`);
      if (appDataStr) {
        const appData = JSON.parse(appDataStr);
        appData.resumeUrl = downloadURL;
        appData.updated_at = new Date().toISOString();
        localStorage.setItem(`application_${appId}`, JSON.stringify(appData));
      }

      // Save application ID for future reference
      localStorage.setItem('lastApplicationId', appId);
    } catch (localStorageError) {
      console.error('Error saving resume URL to localStorage:', localStorageError);
    }

    return downloadURL;
  } catch (error: unknown) {
    console.error('Error uploading resume:', error);

    // For public candidates, don't throw the error to prevent disrupting the flow
    if (isPublicInterviewFlow()) {
      console.log('Using mock URL due to upload error');
      // Generate a mock URL so the flow can continue
      const mockUrl = `https://storage.googleapis.com/mock-resume-url/${roleId}/${applicationId || uuidv4()}/resume.pdf`;
      return mockUrl;
    }

    throw error;
  }
};

/**
 * Check if an application already exists for a role and candidate
 *
 * This simplified version only checks localStorage and doesn't try to access Firestore
 *
 * @param roleId Role ID to check
 * @param email Candidate email to check
 * @returns An object with exists flag and application data if found
 */
export const checkExistingApplication = async (
  roleId: string,
  email: string
): Promise<{ exists: boolean, application?: any }> => {
  try {
    if (!email || !roleId) {
      console.warn('Missing email or roleId for checking existing application');
      return { exists: false };
    }

    // First check localStorage for existing application IDs
    const lastApplicationId = localStorage.getItem('lastApplicationId');
    const applicationId = localStorage.getItem('applicationId');

    // If we have an application ID in localStorage, check if we have the application data
    if (lastApplicationId || applicationId) {
      const appId = applicationId || lastApplicationId;
      const appDataStr = localStorage.getItem(`application_${appId}`);

      if (appDataStr) {
        try {
          const appData = JSON.parse(appDataStr);

          // Check if this application is for the current role
          if (appData.roleId === roleId) {
            console.log(`Found existing application in localStorage for role ${roleId}:`, appData.id);
            return {
              exists: true,
              application: appData
            };
          }
        } catch (parseError) {
          console.error('Error parsing application data from localStorage:', parseError);
        }
      }
    }

    // No existing application found
    return { exists: false };
  } catch (error) {
    console.error('Error checking for existing application:', error);
    return { exists: false };
  }
};

/**
 * Save a resume evaluation to localStorage only
 *
 * This simplified version doesn't try to save to Firestore since the backend handles that
 *
 * @param applicationId Application ID to save evaluation for
 * @param evaluationData Evaluation data from the API
 * @param parsedResumeText Optional parsed resume text to store
 * @returns The evaluation ID
 */
export const saveResumeEvaluation = async (
  applicationId: string,
  evaluationData: any,
  parsedResumeText?: string
): Promise<string> => {
  try {
    // Use the evaluation ID from the backend if available, otherwise generate one
    const evaluationId = evaluationData.evaluation_id || uuidv4();

    // Add metadata to the evaluation
    const evaluationWithMetadata = {
      ...evaluationData,
      id: evaluationId,
      applicationId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Save to localStorage only
    try {
      localStorage.setItem(`evaluation_${evaluationId}`, JSON.stringify(evaluationWithMetadata));

      // Save parsed resume text if provided
      if (parsedResumeText) {
        localStorage.setItem('parsedResumeText', parsedResumeText);
        console.log('Saved parsed resume text to localStorage for interview context');
      }

      // Also save as application evaluation for easier lookup
      localStorage.setItem(`application_evaluation_${applicationId}`, JSON.stringify({
        evaluationId,
        applicationId,
        evaluation: evaluationWithMetadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      console.log(`Evaluation saved to localStorage with ID: ${evaluationId}`);
    } catch (localStorageError) {
      console.warn('Failed to save evaluation to localStorage:', localStorageError);
    }

    // Return the evaluation ID
    return evaluationId;
  } catch (error) {
    console.error('Error saving resume evaluation:', error);
    // Generate and return a fallback ID to not break the flow
    return uuidv4();
  }
};
