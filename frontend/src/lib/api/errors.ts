/**
 * Custom API Error class for handling API-specific errors
 */
export class APIError extends <PERSON>rror {
  constructor(
    public status: number,
    public message: string,
    public data?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'APIError';
    Object.setPrototypeOf(this, APIError.prototype);
  }

  static fromResponse(response: Response, data?: Record<string, unknown>): APIError {
    return new APIError(
      response.status,
      data?.detail as string || response.statusText || 'An error occurred',
      data
    );
  }
}

/**
 * Error handler utility for API errors
 */
export const handleAPIError = (error: unknown) => {
  if (error instanceof APIError) {
    // Handle known API errors
    return {
      message: error.message,
      status: error.status,
      data: error.data,
    };
  }

  if (error instanceof Error) {
    // Handle standard JS errors
    return {
      message: error.message,
      status: 500,
      data: null,
    };
  }

  // Handle unknown errors
  return {
    message: 'An unexpected error occurred',
    status: 500,
    data: null,
  };
};

/**
 * Error codes and messages
 */
export const ErrorCodes = {
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

export const ErrorMessages = {
  [ErrorCodes.BAD_REQUEST]: 'Invalid request',
  [ErrorCodes.UNAUTHORIZED]: 'Authentication required',
  [ErrorCodes.FORBIDDEN]: 'Access denied',
  [ErrorCodes.NOT_FOUND]: 'Resource not found',
  [ErrorCodes.INTERNAL_SERVER_ERROR]: 'Internal server error',
} as const; 