# API Integration

This directory contains all API integration code for the Recruiva frontend application.

## Directory Structure

```
api/
├── client.ts        # Base API client implementation
├── constants.ts     # API-related constants
├── errors.ts        # Error handling utilities
├── types.ts         # Type definitions
├── services/        # Service implementations
│   └── roles.ts     # Roles API service
└── README.md        # This file
```

## Usage

### Base API Client

The base API client provides common functionality for making HTTP requests:

```typescript
import { APIClient } from './client';

class MyService extends APIClient {
  async getData() {
    return this.get('/endpoint');
  }
}
```

### Error Handling

All API errors are handled consistently using the `APIError` class:

```typescript
try {
  await api.getData();
} catch (error) {
  if (error instanceof APIError) {
    // Handle API-specific error
  }
}
```

### Type Safety

All API requests and responses are fully typed:

```typescript
import { API } from './types';

async function createRole(data: API.DTO.CreateRoleRequest): Promise<API.DTO.RoleResponse> {
  // Implementation
}
```

### Constants

API-related constants are centralized:

```typescript
import { API_ENDPOINTS, ROLE_STATUS } from './constants';

const endpoint = API_ENDPOINTS.ROLES;
const status = ROLE_STATUS.INTAKE;
```

## Services

### RolesAPI

Handles all role-related operations:

- Create role
- Get roles
- Update role
- Delete role
- Update role status
- Update role priority

Example usage:

```typescript
import { RolesAPI } from './services/roles';

const rolesApi = new RolesAPI();

// Create a role
const role = await rolesApi.createRole({
  title: 'Software Engineer',
  // ... other fields
});

// Get all roles
const roles = await rolesApi.getRoles();
```

## Error Codes

Common error codes are defined in `errors.ts`:

- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Internal Server Error

## Environment Configuration

The API client uses the following environment variables:

- `NEXT_PUBLIC_API_URL`: Base URL for API requests (default: 'http://localhost:8000')

## Authentication

All authenticated requests should include the `requiresAuth: true` option:

```typescript
await this.get('/endpoint', { requiresAuth: true });
```

## File Uploads

File uploads are handled through Firebase Storage:

```typescript
const storageRef = ref(storage, `path/to/file/${filename}`);
const snapshot = await uploadBytes(storageRef, file);
const url = await getDownloadURL(snapshot.ref);
``` 