'use client';

import { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { auth } from '@/lib/firebase/client';

interface RetryConfig {
  retry?: number;
  retryDelay?: (retryCount: number) => number;
  _retryCount?: number;
}

interface AxiosRequestConfigWithRetry extends InternalAxiosRequestConfig, RetryConfig {}

export const addRetryInterceptor = (client: AxiosInstance) => {
  client.interceptors.response.use(undefined, async (err: AxiosError) => {
    const config = err.config as AxiosRequestConfigWithRetry;
    if (!config || !config.retry || config._retryCount! >= config.retry!) {
      return Promise.reject(err);
    }

    config._retryCount = (config._retryCount || 0) + 1;
    const delay = typeof config.retryDelay === 'function' 
      ? config.retryDelay(config._retryCount)
      : 1000;

    // Enhanced logging for retry attempts
    console.log(`Retrying request (${config._retryCount}/${config.retry}) after ${delay}ms for URL: ${config.baseURL}${config.url}`);
    
    await new Promise(resolve => setTimeout(resolve, delay));
    return client(config);
  });
};

export const addAuthInterceptor = (client: AxiosInstance) => {
  // Token cache to prevent excessive refresh requests
  let cachedToken: string | null = null;
  let tokenExpiryTime: number = 0;
  let tokenRefreshPromise: Promise<string> | null = null;
  
  // Minimum time between token refresh attempts (in milliseconds)
  const MIN_TOKEN_REFRESH_INTERVAL = 10000; // 10 seconds
  let lastTokenRefreshTime = 0;

  client.interceptors.request.use(async (config: InternalAxiosRequestConfig) => {
    try {
      // Skip auth for public endpoints
      if (config.url && (
          config.url.includes('/interview-evaluation/public/') ||
          config.url.includes('/public-interview') ||
          config.url.includes('/public/')
        )) {
        console.log(`[Auth Interceptor] Skipping auth for public endpoint: ${config.url}`);
        return config;
      }
      
      // Wait for Firebase to initialize and persistence to be set
      await new Promise<void>((resolve) => {
        let attempts = 0;
        const maxAttempts = 5;
        const checkAuth = () => {
          attempts++;
          if (auth.currentUser || attempts >= maxAttempts) {
            resolve();
          } else {
            setTimeout(checkAuth, 1000); // Wait 1 second before next attempt
          }
        };
        
        const unsubscribe = auth.onAuthStateChanged((user) => {
          unsubscribe();
          if (user) {
            resolve();
          } else {
            checkAuth();
          }
        });
      });

      const user = auth.currentUser;
      
      if (user) {
        const currentTime = Date.now();
        
        // Function to get a fresh token
        const getNewToken = async (): Promise<string> => {
          try {
            // Update the last refresh time
            lastTokenRefreshTime = Date.now();
            
            // Force refresh only if token is expired or about to expire
            const forceRefresh = !cachedToken || currentTime >= tokenExpiryTime - 5 * 60 * 1000; // Refresh 5 minutes before expiry
            
            console.log(`[Auth Interceptor] Getting ${forceRefresh ? 'new' : 'cached'} token`);
            const token = await user.getIdToken(forceRefresh);
            
            // Cache the token and set expiry time (tokens typically last 1 hour)
            cachedToken = token;
            
            // Parse the token to get the expiry time
            try {
              const tokenParts = token.split('.');
              if (tokenParts.length === 3) {
                const payload = JSON.parse(atob(tokenParts[1]));
                if (payload.exp) {
                  tokenExpiryTime = payload.exp * 1000; // Convert to milliseconds
                  console.log(`[Auth Interceptor] Token will expire at ${new Date(tokenExpiryTime).toISOString()}`);
                }
              }
            } catch (parseError) {
              console.error('[Auth Interceptor] Error parsing token:', parseError);
              // Default to 55 minutes if we can't parse the token
              tokenExpiryTime = currentTime + 55 * 60 * 1000;
            }
            
            return token;
          } catch (error) {
            console.error('[Auth Interceptor] Error getting token:', error);
            throw error;
          }
        };
        
        // Adjust token refresh logic to be more efficient
        const needsRefresh = !cachedToken || currentTime >= tokenExpiryTime - 5 * 60 * 1000;
        
        if (needsRefresh) {
          // Check if we're within the rate limit
          const timeSinceLastRefresh = currentTime - lastTokenRefreshTime;
          
          if (timeSinceLastRefresh < MIN_TOKEN_REFRESH_INTERVAL) {
            // Only log once per minute to reduce console noise
            if (timeSinceLastRefresh > 60000 || Math.random() < 0.1) {
              console.warn(`[Auth Interceptor] Token refresh rate limited. Last refresh was ${Math.round(timeSinceLastRefresh)}ms ago.`);
            }
            
            // If we have a cached token, use it even if it's close to expiry
            if (cachedToken) {
              config.headers.Authorization = `Bearer ${cachedToken}`;
              return config;
            }
            
            // If we don't have a cached token, wait for the rate limit
            await new Promise(resolve => setTimeout(resolve, MIN_TOKEN_REFRESH_INTERVAL - timeSinceLastRefresh));
          }
          
          // Use a shared promise for concurrent requests
          if (!tokenRefreshPromise) {
            tokenRefreshPromise = getNewToken();
            
            // Clear the promise after it resolves or rejects
            tokenRefreshPromise.finally(() => {
              tokenRefreshPromise = null;
            });
          }
          
          try {
            const token = await tokenRefreshPromise;
            config.headers.Authorization = `Bearer ${token}`;
          } catch (error) {
            console.error('[Auth Interceptor] Failed to refresh token:', error);
            
            // If we have a cached token, use it as a fallback
            if (cachedToken) {
              console.log('[Auth Interceptor] Using cached token as fallback after refresh error');
              config.headers.Authorization = `Bearer ${cachedToken}`;
            } else {
              throw error;
            }
          }
        } else {
          // Use cached token
          config.headers.Authorization = `Bearer ${cachedToken}`;
        }
      }

      return config;
    } catch (error) {
      console.error('[Auth Interceptor] Error:', error);
      return Promise.reject(error);
    }
  });
};

export const addResponseInterceptor = (client: AxiosInstance) => {
  // Add request interceptor for logging
  client.interceptors.request.use(
    (config) => {
      // Enhanced request logging
      console.log(`Making request to: ${config.baseURL}${config.url}`);
      return config;
    },
    (error) => {
      console.error('Request error:', error);
      return Promise.reject(error);
    }
  );

  // Add response interceptor for handling responses and errors
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      // Only log in development and only for non-GET requests to reduce noise
      if (process.env.NODE_ENV !== 'production' || response.config.method !== 'get') {
        console.log('API Response:', {
          url: response.config.url,
          status: response.status,
          method: response.config.method,
          dataSize: response.data ? (Array.isArray(response.data) ? response.data.length : 'object') : 'none'
        });
      }
      return response;
    },
    (error: AxiosError) => {
      // Check if the error is a network error
      if (!error.response) {
        // Enhanced network error logging
        console.error('Network Error:', {
          url: error.config?.url,
          message: error.message,
          baseURL: error.config?.baseURL,
          code: error.code,
          name: error.name
        });
        
        // Create a more descriptive error message
        const errorMessage = 'Network error: Unable to connect to the server. Please check your connection.';
        const enhancedError = new Error(errorMessage);
        
        // Add original error properties
        Object.assign(enhancedError, {
          isNetworkError: true,
          originalError: error,
          config: error.config,
          // Add a timestamp to track when the error occurred
          timestamp: Date.now()
        });
        
        return Promise.reject(enhancedError);
      }

      if (error.response) {
        console.error('API Error:', {
          url: error.config?.url,
          status: error.response.status,
          data: error.response.data,
          method: error.config?.method
        });

        // Create a more descriptive error message based on status code
        let errorMessage = '';
        switch (error.response.status) {
          case 401: errorMessage = 'Authentication failed. Please sign in again.'; break;
          case 403: errorMessage = 'You do not have permission to perform this action.'; break;
          case 404: errorMessage = 'The requested resource was not found.'; break;
          case 429: errorMessage = 'Too many requests. Please try again later.'; break;
          case 500: errorMessage = 'Server error. Please try again later.'; break;
          case 502: errorMessage = 'Bad gateway. Please try again later.'; break;
          case 503: errorMessage = 'Service unavailable. Please try again later.'; break;
          case 504: errorMessage = 'Gateway timeout. Please try again later.'; break;
          default: errorMessage = error.response.data && typeof error.response.data === 'object' && 'message' in error.response.data 
            ? String(error.response.data.message) 
            : 'An error occurred. Please try again.';
        }

        const enhancedError = new Error(errorMessage);
        
        // Add original error properties
        Object.assign(enhancedError, {
          isNetworkError: false,
          originalError: error,
          response: error.response,
          status: error.response.status,
          // Add a timestamp to track when the error occurred
          timestamp: Date.now()
        });
        
        return Promise.reject(enhancedError);
      }

      return Promise.reject(error);
    }
  );
}; 