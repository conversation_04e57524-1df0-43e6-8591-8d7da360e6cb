import axios, { CreateAxiosDefaults } from 'axios';
import { addAuthInterceptor, addResponseInterceptor, addRetryInterceptor } from '@/lib/api/client/interceptors';

// Configuration
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const API_PREFIX = '/api/v1';
const IS_PRODUCTION = process.env.NODE_ENV === 'production';

// Define custom config type that includes retry properties
interface CustomAxiosConfig extends CreateAxiosDefaults {
  retry?: number;
  retryDelay?: (retryCount: number) => number;
}

// Increased timeout and added retry configuration
const createBaseConfig = (timeout: number = 120000): CustomAxiosConfig => {
  const config: CustomAxiosConfig = {
    baseURL: `${API_URL}${API_PREFIX}`,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    withCredentials: false, // Set to false to avoid CORS issues with credentials
    maxRedirects: 5,
    timeout, // Increased default timeout to 2 minutes
    validateStatus: (status) => status >= 200 && status < 500,
    httpsAgent: IS_PRODUCTION ? { rejectUnauthorized: true } : undefined,
  };

  // Add retry configuration as custom properties
  config.retry = 3;
  config.retryDelay = (retryCount: number) => Math.min(1000 * 2 ** retryCount, 10000); // Exponential backoff with max 10s

  return config;
};

// Create API clients
export const apiClient = axios.create(createBaseConfig());
export const webRTCClient = axios.create(createBaseConfig(180000)); // 3 minutes for WebRTC operations

// Apply interceptors
[apiClient, webRTCClient].forEach(client => {
  addRetryInterceptor(client);
  addAuthInterceptor(client);
  addResponseInterceptor(client);
});