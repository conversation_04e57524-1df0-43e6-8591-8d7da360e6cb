'use client';

import axios, { CreateAxiosDefaults } from 'axios';
import { addResponseInterceptor, addRetryInterceptor } from '@/lib/api/client/interceptors';

// Configuration
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const API_PREFIX = '/api/v1';

// Log the API URL for debugging
console.log('Public API Client: Using API URL:', API_URL);

const createBaseConfig = (timeout: number = 30000): CreateAxiosDefaults => {
    const config = {
        baseURL: `${API_URL}${API_PREFIX}`,
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            // Add cache control headers to enable browser caching
            'Cache-Control': 'max-age=300' // Cache for 5 minutes
        },
        withCredentials: false, // Set to false to avoid CORS issues with credentials
        maxRedirects: 5,
        timeout,
        validateStatus: (status: number) => status >= 200 && status < 500,
        // Add retry configuration
        retry: 3,
        retryDelay: (retryCount: number) => retryCount * 1000, // 1s, 2s, 3s
    };
    
    // Log the full configuration for debugging
    console.log('Public API Client: Created config with baseURL:', config.baseURL);
    console.log('Public API Client: Timeout set to:', timeout);
    console.log('Public API Client: withCredentials:', config.withCredentials);
    
    return config;
};

// Create public API client without auth interceptor
export const publicApiClient = axios.create(createBaseConfig());

// Log when the client is created
console.log('Public API Client: Created public API client');

// Apply only non-auth interceptors
addRetryInterceptor(publicApiClient);
addResponseInterceptor(publicApiClient);

// Add a specific error handler for the public API client
publicApiClient.interceptors.request.use(
    (config) => {
        // Add a timestamp to prevent caching issues, but only if not already present
        if (!config.url?.includes('_t=')) {
            const timestamp = new Date().getTime();
            const separator = config.url?.includes('?') ? '&' : '?';
            config.url = `${config.url}${separator}_t=${timestamp}`;
        }
        return config;
    },
    (error) => {
        console.error('Public API Client: Request error:', error);
        return Promise.reject(error);
    }
);

// Add a specific response handler for the public API client
publicApiClient.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        // Enhanced error handling for public API client
        console.error('Public API Client: Response error:', {
            url: error.config?.url,
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
        });

        // Create a more descriptive error
        const enhancedError = new Error(
            error.response 
                ? `API Error: ${error.response.status} - ${error.response.statusText}` 
                : 'Network error: Unable to connect to the server. Please check your connection.'
        );
        
        // Add original error properties
        Object.assign(enhancedError, {
            isNetworkError: !error.response,
            originalError: error,
            config: error.config
        });
        
        return Promise.reject(enhancedError);
    }
); 