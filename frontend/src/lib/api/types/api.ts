// Base response interface
export interface BaseApiResponse {
  status: 'success' | 'error';
  message?: string;
}

// Success response type
export interface ApiResponse<T> extends BaseApiResponse {
  status: 'success';
  data: T;
}

// Error response type
export interface ApiError {
  detail: string;
  code?: string;
  errors?: Record<string, string[]>;
}

// Combined error response type
export interface ApiErrorResponse extends ApiError, BaseApiResponse {
  status: 'error';
}

// Union type for all possible API responses
export type ApiResult<T> = ApiResponse<T> | ApiErrorResponse;

// Type guard for error responses
export const isApiError = (response: ApiResult<unknown>): response is ApiErrorResponse => {
  return response.status === 'error';
}; 