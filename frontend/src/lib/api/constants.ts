// File: frontend/src/lib/api/constants.ts

/**
 * API Endpoints
 */
export const API_ENDPOINTS = {
  ROLES: '/roles',  // Base endpoint without prefix and trailing slash
  REALTIME: '/realtime',  // Base endpoint without prefix and trailing slash
  TEMPLATES: '/templates',  // Base endpoint for interview templates
} as const;

/**
 * HTTP Methods
 */
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
} as const;

/**
 * Role Status
 */
export const ROLE_STATUS = {
  INTAKE: 'Intake',
  OPEN: 'Open',
  CLOSED: 'Closed',
  ON_HOLD: 'On-Hold',
} as const;

/**
 * Role Priority
 */
export const ROLE_PRIORITY = {
  NORMAL: 'Normal',
  EXPEDITED: 'Expedited',
} as const;

/**
 * Job Types
 */
export const JOB_TYPE = {
  FULL_TIME: 'Full-time',
  PART_TIME: 'Part-time',
  CONTRACT: 'Contract',
  INTERNSHIP: 'Internship',
} as const;

/**
 * Location Types
 */
export const LOCATION_TYPE = {
  REMOTE: 'Remote',
  HYBRID: 'Hybrid',
  ON_SITE: 'On-site',
} as const;

/**
 * API Version
 */
export const API_VERSION = 'v1';

/**
 * Default API Configuration
 */
export const API_CONFIG = {
  BASE_URL: (process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000') + '/api/v1',
  TIMEOUT: 60000, // 60 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const; 