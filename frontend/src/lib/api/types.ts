// File: frontend/src/lib/api/types.ts

import { Role as AppRole } from '@/types/role';

/**
 * Base API response type for consistent error handling
 */
export namespace API {
  export interface APIResponse<T> {
    data: T;
    status: string;
    message?: string;
  }

  /**
   * Role related types
   */
  export interface Role extends AppRole {}

  /**
   * Request/Response DTOs
   */
  export namespace DTO {
    export interface CreateRoleRequest extends Omit<AppRole, 'id' | 'created_at' | 'updated_at'> {
      intake_form_url?: string;
      job_description_file?: File;
    }

    export interface RoleResponse {
      data: Role;
    }

    export interface RoleListResponse {
      data: Role[];
      total: number;
      page: number;
      size: number;
      pages: number;
    }
  }

  /**
   * Common types used across the API
   */
  export interface PaginatedResponse<T> {
    items: T[];
    total: number;
    page: number;
    size: number;
    pages: number;
  }

  /**
   * Message type for realtime communication
   */
  export interface Message {
    id?: string;
    role: 'user' | 'Recruiva';
    content: string;
    timestamp: number;
    status?: 'pending' | 'completed' | 'failed';
    isSpeaking?: boolean;
  }
}

// Candidate Types
export interface Candidate {
  id: string;
  name: string;
  email: string;
  phone?: string;
  resume_url?: string;
  status: 'pending' | 'interviewing' | 'accepted' | 'rejected';
  role_id: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCandidateDTO {
  name: string;
  email: string;
  phone?: string;
  resume_url?: string;
  role_id: string;
}

// Evaluation Types
export interface Evaluation {
  id: string;
  interview_id: string;
  scores: Record<string, number>;
  feedback: string;
  recommendation: 'hire' | 'reject' | 'consider';
  created_at: string;
  updated_at: string;
}

export interface CreateEvaluationDTO {
  interview_id: string;
  scores: Record<string, number>;
  feedback: string;
  recommendation: 'hire' | 'reject' | 'consider';
}

// Type aliases for easier imports
export type Role = API.Role;
export type CreateRoleDTO = API.DTO.CreateRoleRequest;
export type RoleResponse = API.DTO.RoleResponse;
export type RoleListResponse = API.DTO.RoleListResponse;
export type PaginatedResponse<T> = API.PaginatedResponse<T>; 