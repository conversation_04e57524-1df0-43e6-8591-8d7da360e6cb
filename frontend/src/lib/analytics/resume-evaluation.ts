/**
 * Analytics module for resume evaluation tracking
 */

import { ResumeEvaluationResponse, BasicEvaluationResponse } from '@/services/resume';
import { doc, setDoc, updateDoc, increment, serverTimestamp, runTransaction } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

/**
 * Check if we're in a public interview flow
 * @returns true if this is a public interview flow
 */
const isPublicInterviewFlow = (): boolean => {
  // Check URL for interview path
  const isInInterviewPath = typeof window !== 'undefined' && 
    window.location.pathname.includes('/instant-interview/');
  
  // Check authentication state
  const auth = getAuth();
  const isUnauthenticated = !auth.currentUser;
  
  return isInInterviewPath && isUnauthenticated;
};

/**
 * Track a resume evaluation result
 * 
 * @param roleId The role ID
 * @param evaluationData The evaluation data
 * @param applicationId The application ID
 */
export const trackResumeEvaluation = async (
  roleId: string,
  evaluationData: ResumeEvaluationResponse | BasicEvaluationResponse,
  applicationId: string
): Promise<void> => {
  try {
    // Skip tracking in public interview flow
    if (isPublicInterviewFlow()) {
      console.log('Analytics tracking disabled in public interview flow');
      return;
    }

    // Determine if the evaluation passed
    const isPassed = 'scorecard' in evaluationData 
      ? evaluationData.recommendation.decision === 'PASS'
      : evaluationData.decision === 'PASS';
    
    // Determine the confidence level
    const confidence = 'scorecard' in evaluationData
      ? evaluationData.recommendation.confidence
      : evaluationData.confidence;
    
    // Get the overall score
    const score = 'scorecard' in evaluationData
      ? evaluationData.scorecard.overallScore
      : evaluationData.overallScore;
    
    // Anonymized evaluation record for analytics
    const evaluationRecord = {
      roleId,
      timestamp: serverTimestamp(),
      result: isPassed ? 'PASS' : 'FAIL',
      confidence,
      score,
      evaluationType: 'scorecard' in evaluationData ? 'detailed' : 'basic',
      resumeLength: evaluationData.metadata.resumeLength,
      processingTime: evaluationData.metadata.timestamp ? new Date().getTime() - new Date(evaluationData.metadata.timestamp).getTime() : 0,
      modelUsed: evaluationData.metadata.model
    };
    
    // Store the evaluation record in the analytics collection
    await setDoc(
      doc(db, 'analytics', 'resume_evaluations', 'evaluations', applicationId),
      evaluationRecord
    );
    
    // Update role-specific statistics
    const roleStatsRef = doc(db, 'analytics', 'resume_evaluations', 'role_stats', roleId);
    
    // Use a transaction to safely update counters
    await runTransaction(db, async (transaction) => {
      const roleStatsDoc = await transaction.get(roleStatsRef);
      
      if (!roleStatsDoc.exists()) {
        // Create new stats document
        transaction.set(roleStatsRef, {
          totalEvaluations: 1,
          passedEvaluations: isPassed ? 1 : 0,
          failedEvaluations: isPassed ? 0 : 1,
          totalScore: score,
          averageScore: score,
          highConfidenceCount: confidence === 'HIGH' ? 1 : 0,
          mediumConfidenceCount: confidence === 'MEDIUM' ? 1 : 0,
          lowConfidenceCount: confidence === 'LOW' ? 1 : 0,
          lastUpdated: serverTimestamp()
        });
      } else {
        // Update existing stats
        const data = roleStatsDoc.data();
        const newTotalEvaluations = data.totalEvaluations + 1;
        const newTotalScore = data.totalScore + score;
        
        transaction.update(roleStatsRef, {
          totalEvaluations: increment(1),
          passedEvaluations: increment(isPassed ? 1 : 0),
          failedEvaluations: increment(isPassed ? 0 : 1),
          totalScore: newTotalScore,
          averageScore: newTotalScore / newTotalEvaluations,
          highConfidenceCount: increment(confidence === 'HIGH' ? 1 : 0),
          mediumConfidenceCount: increment(confidence === 'MEDIUM' ? 1 : 0),
          lowConfidenceCount: increment(confidence === 'LOW' ? 1 : 0),
          lastUpdated: serverTimestamp()
        });
      }
    });
    
    // Update global statistics
    const globalStatsRef = doc(db, 'analytics', 'resume_evaluations', 'global_stats', 'overall');
    
    await runTransaction(db, async (transaction) => {
      const globalStatsDoc = await transaction.get(globalStatsRef);
      
      if (!globalStatsDoc.exists()) {
        // Create new global stats document
        transaction.set(globalStatsRef, {
          totalEvaluations: 1,
          passedEvaluations: isPassed ? 1 : 0,
          failedEvaluations: isPassed ? 0 : 1,
          totalScore: score,
          averageScore: score,
          detailedEvaluationCount: 'scorecard' in evaluationData ? 1 : 0,
          basicEvaluationCount: 'scorecard' in evaluationData ? 0 : 1,
          lastUpdated: serverTimestamp()
        });
      } else {
        // Update existing global stats
        const data = globalStatsDoc.data();
        const newTotalEvaluations = data.totalEvaluations + 1;
        const newTotalScore = data.totalScore + score;
        
        transaction.update(globalStatsRef, {
          totalEvaluations: increment(1),
          passedEvaluations: increment(isPassed ? 1 : 0),
          failedEvaluations: increment(isPassed ? 0 : 1),
          totalScore: newTotalScore,
          averageScore: newTotalScore / newTotalEvaluations,
          detailedEvaluationCount: increment('scorecard' in evaluationData ? 1 : 0),
          basicEvaluationCount: increment('scorecard' in evaluationData ? 0 : 1),
          lastUpdated: serverTimestamp()
        });
      }
    });
  } catch (error) {
    console.error('Error tracking resume evaluation:', error);
    // Don't throw to prevent disrupting user flow
  }
};

/**
 * Track when a user continues to the interview after evaluation
 * 
 * @param roleId The role ID
 * @param applicationId The application ID
 * @param evaluationResult Whether the evaluation passed or failed
 */
export const trackInterviewContinuation = async (
  roleId: string,
  applicationId: string,
  evaluationResult: 'PASS' | 'FAIL'
): Promise<void> => {
  try {
    // Skip tracking in public interview flow
    if (isPublicInterviewFlow()) {
      console.log('Interview continuation tracking disabled in public interview flow');
      return;
    }

    // Update the evaluation record with continuation data
    const evalRef = doc(db, 'analytics', 'resume_evaluations', 'evaluations', applicationId);
    
    await updateDoc(evalRef, {
      continuedToInterview: true,
      continuationTimestamp: serverTimestamp()
    });
    
    // Update role-specific continuation statistics
    const roleStatsRef = doc(db, 'analytics', 'resume_evaluations', 'role_stats', roleId);
    
    await updateDoc(roleStatsRef, {
      interviewContinuations: increment(1),
      passedContinuations: increment(evaluationResult === 'PASS' ? 1 : 0),
      failedContinuations: increment(evaluationResult === 'FAIL' ? 1 : 0),
      lastUpdated: serverTimestamp()
    });
    
    // Update global continuation statistics
    const globalStatsRef = doc(db, 'analytics', 'resume_evaluations', 'global_stats', 'overall');
    
    await updateDoc(globalStatsRef, {
      interviewContinuations: increment(1),
      passedContinuations: increment(evaluationResult === 'PASS' ? 1 : 0),
      failedContinuations: increment(evaluationResult === 'FAIL' ? 1 : 0),
      lastUpdated: serverTimestamp()
    });
  } catch (error) {
    console.error('Error tracking interview continuation:', error);
    // Don't throw to prevent disrupting user flow
  }
};

/**
 * Track API performance metrics
 * 
 * @param endpoint The API endpoint
 * @param responseTime Response time in milliseconds
 * @param success Whether the API call was successful
 * @param errorMessage Optional error message
 */
export const trackApiPerformance = async (
  endpoint: string,
  responseTime: number,
  success: boolean): Promise<void> => {
  try {
    // Skip tracking in public interview flow
    if (isPublicInterviewFlow()) {
      console.log('API performance tracking disabled in public interview flow');
      return;
    }

    // Check if analytics tracking is enabled via localStorage flag
    // This allows users to disable performance tracking if desired
    const analyticsDisabled = localStorage.getItem('disableAnalytics') === 'true';
    if (analyticsDisabled) {
      return; // Skip tracking if analytics are disabled
    }
    
    // Get Firestore reference
    const db = getFirestore();
    const now = new Date();
    const dateKey = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
    
    // Sanitize endpoint name for Firestore key
    const sanitizedEndpoint = endpoint.replace(/\//g, '_').replace(/\./g, '_').replace(/\[/g, '_').replace(/\]/g, '_');
    
    // Guard against running in strict CSP environments where Firebase might be restricted
    if (typeof window === 'undefined' || !db) {
      console.warn('Firebase not available for API performance tracking');
      return;
    }
    
    // Create a transaction to ensure atomic operations
    runTransaction(db, async (transaction) => {
      // Try to get the document first to check if we have access
      // This provides an early fail without attempting writes if permissions are insufficient
      try {
        const apiStatsRef = doc(db, 'analytics', 'api_performance', 'endpoints', sanitizedEndpoint);
        const apiStatsSnapshot = await transaction.get(apiStatsRef);
        
        // If we can't read the document, we likely can't write either
        // This is a sign of permission issues
        if (!apiStatsSnapshot.exists()) {
          // Only try to create the document if we were able to read successfully
          transaction.set(apiStatsRef, {
            lastCalled: serverTimestamp(),
            totalCalls: 1,
            successfulCalls: success ? 1 : 0,
            failedCalls: success ? 0 : 1,
            totalResponseTime: responseTime,
            averageResponseTime: responseTime,
            endpoints: {
              [dateKey]: {
                totalCalls: 1,
                successfulCalls: success ? 1 : 0,
                failedCalls: success ? 0 : 1,
                totalResponseTime: responseTime,
                averageResponseTime: responseTime
              }
            }
          }, { merge: true });
        } else {
          // Document exists, update it
          const currentData = apiStatsSnapshot.data();
          const currentEndpointData = currentData.endpoints?.[dateKey] || {
            totalCalls: 0,
            successfulCalls: 0,
            failedCalls: 0,
            totalResponseTime: 0
          };
          
          // Calculate new values
          const newTotalCalls = (currentData.totalCalls || 0) + 1;
          const newTotalResponseTime = (currentData.totalResponseTime || 0) + responseTime;
          const newEndpointTotalCalls = currentEndpointData.totalCalls + 1;
          const newEndpointTotalResponseTime = currentEndpointData.totalResponseTime + responseTime;
          
          // Update daily stats
          transaction.update(apiStatsRef, {
            lastCalled: serverTimestamp(),
            totalCalls: increment(1),
            successfulCalls: increment(success ? 1 : 0),
            failedCalls: increment(success ? 0 : 1),
            totalResponseTime: newTotalResponseTime,
            averageResponseTime: newTotalResponseTime / newTotalCalls,
            [`endpoints.${dateKey}`]: {
              totalCalls: newEndpointTotalCalls,
              successfulCalls: (currentEndpointData.successfulCalls || 0) + (success ? 1 : 0),
              failedCalls: (currentEndpointData.failedCalls || 0) + (success ? 0 : 1),
              totalResponseTime: newEndpointTotalResponseTime,
              averageResponseTime: newEndpointTotalResponseTime / newEndpointTotalCalls,
              lastUpdated: serverTimestamp()
            }
          });
        }
      } catch (permissionError) {
        // Likely a permission error when trying to read
        console.warn('Unable to access API analytics document - permission denied:', permissionError);
        return; // Exit the transaction without throwing, so the outer try/catch doesn't log again
      }
    }).catch((transactionError) => {
      // Handle transaction failures silently
      if (transactionError.code === 'permission-denied') {
        console.warn('Performance tracking disabled due to insufficient permissions');
      } else {
        console.warn('API performance tracking failed:', transactionError.message);
      }
    });
  } catch (error) {
    // Don't throw or log loudly to avoid disrupting the main application flow
    console.warn('API performance tracking skipped:', error instanceof Error ? error.message : 'Unknown error');
  }
}; 