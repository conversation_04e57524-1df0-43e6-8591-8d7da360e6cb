// File: frontend/src/lib/utils.ts

import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error | null = null;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      if (attempt === maxAttempts) break;
      
      // Wait before retrying, with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError;
}

/**
 * Preprocesses markdown content to ensure proper rendering
 * - Removes markdown code fence markers if present
 * - Removes "Job Posting:" header if present
 * - Handles other common formatting issues
 */
export function preprocessMarkdown(markdown: string | null | undefined): string {
  if (!markdown) return '';
  
  // Log the original markdown for debugging
  if (process.env.NODE_ENV === 'development') {
    console.log('Original markdown:', markdown.substring(0, 100) + (markdown.length > 100 ? '...' : ''));
  }
  
  // Remove markdown code fence markers if present (case insensitive)
  let processed = markdown.replace(/^```markdown\s*/i, '').replace(/\s*```\s*$/, '');
  
  // Remove any "Job Posting:" header if present
  processed = processed.replace(/^# Job Posting:.*\n/i, '');
  
  // Handle triple backticks with other language specifiers (e.g., ```json, ```yaml)
  processed = processed.replace(/^```[a-z]*\s*/i, '').replace(/\s*```\s*$/, '');
  
  // Ensure proper spacing around headers
  processed = processed.replace(/^(#+)([^\s#])/gm, '$1 $2');
  
  // Ensure proper spacing for list items
  processed = processed.replace(/^([*-])([^\s])/gm, '$1 $2');
  
  // Log the processed markdown for debugging
  if (process.env.NODE_ENV === 'development' && processed !== markdown) {
    console.log('Processed markdown:', processed.substring(0, 100) + (processed.length > 100 ? '...' : ''));
  }
  
  return processed;
}

/**
 * Formats a date string to display how long ago it was posted
 * - Returns 'Today' for today's date
 * - Returns 'Yesterday' for yesterday's date
 * - Returns 'X days ago' for dates less than a week ago
 * - Returns 'X weeks ago' for dates less than a month ago
 * - Returns 'X months ago' for dates less than a year ago
 * - Returns 'X years ago' for dates more than a year ago
 */
export function formatPostedDate(dateString?: string): string {
  if (!dateString) return 'Recently posted';
  
  try {
    const postedDate = new Date(dateString);
    const now = new Date();
    
    // Reset hours to compare just the dates
    const postedDateOnly = new Date(postedDate.getFullYear(), postedDate.getMonth(), postedDate.getDate());
    const nowDateOnly = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    // Calculate difference in days
    const diffTime = Math.abs(nowDateOnly.getTime() - postedDateOnly.getTime());
    const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
    
    if (postedDateOnly.getTime() === nowDateOnly.getTime()) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Recently posted';
  }
} 