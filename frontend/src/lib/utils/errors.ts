import { AxiosError } from 'axios';

export interface ApiError {
  detail: string;
  code?: string;
  errors?: Record<string, string[]>;
}

export class ServiceError extends Error {
  constructor(message: string, public originalError?: unknown) {
    super(message);
    this.name = 'ServiceError';
  }
}

export const handleApiError = <T>(error: unknown, options?: { defaultErrorResponse?: T }): never | T => {
  if (options?.defaultErrorResponse) {
    if (error instanceof AxiosError) {
      const axiosError = error as AxiosError<ApiError>;
      if (axiosError.response?.data) {
        const apiError = axiosError.response.data;
        if (apiError.detail) {
          return {
            ...options.defaultErrorResponse,
            message: apiError.detail
          } as T;
        }
        if (apiError.errors) {
          return {
            ...options.defaultErrorResponse,
            message: Object.entries(apiError.errors)
              .map(([field, msgs]) => `${field}: ${msgs.join(', ')}`)
              .join('; ')
          } as T;
        }
      }
      return {
        ...options.defaultErrorResponse,
        message: axiosError.message
      } as T;
    }
    return {
      ...options.defaultErrorResponse,
      message: 'An unexpected error occurred. Please try again.'
    } as T;
  }

  if (error instanceof AxiosError) {
    const axiosError = error as AxiosError<ApiError>;
    if (axiosError.response?.data) {
      const apiError = axiosError.response.data;
      if (apiError.detail) {
        throw new Error(apiError.detail);
      }
      if (apiError.errors) {
        throw new Error(Object.entries(apiError.errors)
          .map(([field, msgs]) => `${field}: ${msgs.join(', ')}`)
          .join('; '));
      }
      throw new Error(JSON.stringify(apiError));
    }
    throw new Error(axiosError.message);
  }
  throw new Error('An unexpected error occurred. Please try again.');
}; 