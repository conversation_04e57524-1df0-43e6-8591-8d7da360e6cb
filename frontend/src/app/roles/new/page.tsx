'use client';

import { useRouter } from 'next/navigation';
import { RoleForm } from '@/components/features/roles/RoleForm';
import { rolesService } from '@/services/roles';
import { useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { Montserrat } from 'next/font/google';
import { useToast } from '@/hooks/use-toast';

const montserrat = Montserrat({ 
  subsets: ['latin'],
  weight: ['500', '600', '700'],
});

export default function NewRolePage() {
  const router = useRouter();
  const { user } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [currentTitle, setCurrentTitle] = useState('New Role');

  const handleSubmit = async (data: any) => {
    try {
      setLoading(true);
      
      // Log the data being submitted for debugging
      console.log('Submitting role data:', JSON.stringify(data, null, 2));
      
      // Ensure keyResponsibilities is an array
      if (typeof data.keyResponsibilities === 'string') {
        data.keyResponsibilities = data.keyResponsibilities.split('\n').filter((item: string) => item.trim() !== '');
      } else if (!Array.isArray(data.keyResponsibilities)) {
        data.keyResponsibilities = [];
      }
      
      // Ensure required fields are present
      if (!data.title) {
        throw new Error('Title is required');
      }
      
      if (!data.summary) {
        throw new Error('Summary is required');
      }
      
      if (!data.hiringManagerContact) {
        data.hiringManagerContact = user?.email || '';
      }
      
      const role = await rolesService.createRole(data);
      
      console.log('Role created successfully:', role);
      
      toast({
        title: 'Success',
        description: 'Role created successfully',
        variant: 'default',
      });

      router.push(`/roles/${role.id}`);
    } catch (error) {
      console.error('Error creating role:', error);
      
      // More detailed error handling
      let errorMessage = 'Failed to create role. Please try again.';
      
      if (error instanceof Error) {
        errorMessage = error.message;
        
        // Check for specific error types
        if (errorMessage.includes('422')) {
          errorMessage = 'Invalid role data. Please check all required fields.';
        } else if (errorMessage.includes('401') || errorMessage.includes('403')) {
          errorMessage = 'Authentication error. Please sign in again.';
        }
      }
      
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <main className="min-h-screen">
      <div className="container mx-auto px-6 py-12 animate-fade-in">
        <div className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h1 className={`text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 ${montserrat.className}`}>
              {currentTitle}
            </h1>
            <button
              onClick={() => router.back()}
              className="px-4 py-2 text-sm font-medium text-slate-400 hover:text-slate-300 transition-colors"
            >
              Cancel
            </button>
          </div>
          <p className="text-sm text-slate-400">
            Start the role intake process by providing initial details. Required fields are marked with an asterisk (*).
          </p>
        </div>

        <RoleForm 
          onSubmit={handleSubmit} 
          userEmail={user?.email || ''} 
          isSubmitting={loading}
          isEditing={false}
          onTitleChange={setCurrentTitle}
        />
      </div>
    </main>
  );
}
