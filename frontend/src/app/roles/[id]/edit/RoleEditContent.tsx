'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { RoleForm, ProcessedRoleFormData } from '@/components/features/roles/RoleForm';
import { useRole } from '@/hooks/useRole';
import { useRoleUpdate } from '@/hooks/useRoleUpdate';
import { RoleUpdateOptions } from '@/services/roles/roleUpdateUtil';
import { rolesService } from '@/services/roles/service';
import { useState } from 'react';
import { SkillLevel } from '@/types/role';

interface RoleEditContentProps {
    roleId: string;
}

export function RoleEditContent({ roleId }: RoleEditContentProps) {
    const router = useRouter();
    const { role, loading, error } = useRole(roleId);
    const { updateRole, isUpdating } = useRoleUpdate();
    const [currentTitle, setCurrentTitle] = useState<string>('');

    console.log('RoleEditContent:', { roleId, role, loading, error });

    const handleSubmit = async (formData: ProcessedRoleFormData) => {
        try {
            console.log('Submitting form data:', formData);
            
            // Transform form data to RoleUpdateOptions
            const roleUpdate: RoleUpdateOptions = {
                title: formData.title,
                summary: formData.summary,
                status: formData.status,
                priority: formData.priority,
                // keyResponsibilities is already an array from ProcessedRoleFormData
                keyResponsibilities: formData.keyResponsibilities,
                compensation: formData.compensation
                    ? {
                        min: formData.compensation.min,
                        max: formData.compensation.max,
                        currency: formData.compensation.currency,
                        equity: formData.compensation.equity,
                    }
                    : undefined,
                interviewProcess: formData.interviewProcess,
                hiringManagerContact: formData.hiringManagerContact,
                location: formData.location,
                jobType: formData.jobType,
                team: formData.team,
                aboutCompany: formData.aboutCompany,
                aboutTeam: formData.aboutTeam,
                // Convert string skills to SkillLevel type
                requiredSkills: formData.requiredSkills ? 
                    Object.entries(formData.requiredSkills).reduce((acc, [key, value]) => {
                        acc[key] = value as SkillLevel;
                        return acc;
                    }, {} as Record<string, SkillLevel>) : 
                    {},
                preferredSkills: formData.preferredSkills ? 
                    Object.entries(formData.preferredSkills).reduce((acc, [key, value]) => {
                        acc[key] = value as SkillLevel;
                        return acc;
                    }, {} as Record<string, SkillLevel>) : 
                    {},
                education: formData.education,
                certificates: formData.certificates,
                keyStakeholders: formData.keyStakeholders,
                benefits: formData.benefits,
            };

            console.log('Role update data:', roleUpdate);

            const updatedRole = await updateRole(roleId, roleUpdate);
            if (!updatedRole) {
                throw new Error('Failed to update role');
            }

            // Wait for a brief moment to ensure the update is propagated
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Verify the update was successful by fetching the role again
            const verifiedRole = await rolesService.getRole(roleId);
            if (verifiedRole) {
                toast.success('Role updated successfully');
                router.push(`/roles/${roleId}`);
            } else {
                throw new Error('Failed to verify role update');
            }
        } catch (error) {
            console.error('Failed to update role:', error);
            if (error instanceof Error && error.message.includes('422')) {
                toast.error('Invalid role data. Please check all required fields.');
            } else {
                toast.error('Failed to update role. Please try again.');
            }
        }
    };

    if (loading) {
        return <div>Loading...</div>;
    }

    if (!role) {
        console.error('Role not found:', { roleId, error });
        return <div>Role not found. Error: {error || 'Unknown error'}</div>;
    }

    return (
        <>
            <div className="mb-12">
                <div className="flex items-center justify-between mb-6">
                    <h1 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400">
                        {currentTitle || role.title}
                    </h1>
                    <button
                        onClick={() => router.back()}
                        className="px-4 py-2 text-sm font-medium text-slate-400 hover:text-slate-300 transition-colors"
                    >
                        Cancel
                    </button>
                </div>
                <p className="text-sm text-slate-400">
                    Edit role details. Required fields are marked with an asterisk (*).
                </p>
            </div>
            <RoleForm
                initialData={role}
                onSubmit={handleSubmit}
                isEditing={true}
                isSubmitting={isUpdating}
                onTitleChange={setCurrentTitle}
            />
        </>
    );
}
