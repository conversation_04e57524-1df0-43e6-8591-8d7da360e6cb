'use client';

import { useParams } from 'next/navigation';
import { RoleEditContent } from './RoleEditContent';
import { Montserrat } from 'next/font/google';

const montserrat = Montserrat({
  subsets: ['latin'],
  weight: ['500', '600', '700'],
});

export default function RoleEditPage() {
  const params = useParams();
  const roleId = params?.id as string;

  return (
    <main className="min-h-screen">
      <div className={`container mx-auto px-6 py-12 animate-fade-in ${montserrat.className}`}>
        <RoleEditContent roleId={roleId} />
      </div>
    </main>
  );
}
