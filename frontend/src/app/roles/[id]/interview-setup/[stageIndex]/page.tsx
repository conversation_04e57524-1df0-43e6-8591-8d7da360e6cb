'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { CollapsibleSection } from '@/components/ui/CollapsibleSection';
import { Button } from '@/components/ui/Button';
import { ArrowLeft, Users, CheckCircle, Award, Clock, Edit, Plus, ChevronDown, Sparkles, Play, Copy, Check, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { rolesService } from '@/services/roles';
import { templatesService, InterviewTemplate, TemplateStatus, UpdateTemplateRequest } from '@/services/templates';
import { Role, InterviewStage } from '@/types/role';
import { Badge } from '@/components/ui';
import { cn } from '@/lib/utils';
import { TemplateEditForm } from '@/components/templates/TemplateEditForm';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropdownMenu';
import { toast } from 'sonner';
import { QuestionsList } from '@/components/templates/QuestionsList';
import { QuestionCreate, QuestionUpdate } from '@/services/templates/api';
import { CriteriaList } from '@/components/templates/CriteriaList';
import { PassRateForm } from '@/components/templates/PassRateForm';
import { CriterionCreate, CriterionUpdate } from '@/services/templates/api';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/client';
import { evaluationService } from '@/services/evaluation';
import { useEvaluationDialog } from '@/components/evaluation/EvaluationReportDialog';

// Define interfaces for placeholder data
interface InterviewQuestion {
  id: string;
  question: string;
  type: 'technical' | 'behavioral' | 'situational';
  difficulty: 'easy' | 'medium' | 'hard';
}

interface EvaluationCriterion {
  id: string;
  name: string;
  description: string;
  maxScore: number;
}

interface CompletedInterview {
  id: string;
  candidateName: string;
  candidateId?: string;
  applicationId?: string;
  status: 'passed' | 'failed' | 'pending';
  score?: number;
  date: Date;
  transcriptId?: string;
  evaluationId?: string;
  evaluation_id?: string; // Add snake_case version for consistency
  hasTranscript: boolean;
  hasEvaluation?: boolean; // Flag to indicate if the interview has a valid evaluation
}

interface StageStatistics {
  candidatesCount: number;
  passedCount: number;
  highestScore: number;
  openDuration: string;
}

// Define a utility function for consistent Card styling
const getCardClasses = (extraClasses = '') => {
  return cn(
    "transition-all duration-300",
    "backdrop-blur-[6px] backdrop-saturate-[1.4]", // Glass effect matching other components
    "bg-white/70 border-slate-200/70", // Light mode with more transparency
    "dark:bg-slate-800/60 dark:border-slate-700/40", // Dark mode consistent with other components
    "shadow-sm shadow-slate-200/30 dark:shadow-xl dark:shadow-slate-950/20", // Consistent shadow
    "hover:shadow-md hover:shadow-slate-200/40 dark:hover:shadow-lg dark:hover:shadow-slate-950/30", // Hover effects matching other components
    extraClasses
  );
};

/**
 * InterviewSetupPage component
 * A page for setting up and managing an interview stage
 */
export default function InterviewSetupPage() {
  const params = useParams();
  const router = useRouter();
  const roleId = params?.id as string;
  const stageIndex = parseInt(params?.stageIndex as string, 10);

  // State for role and template data
  const [role, setRole] = useState<Role | null>(null);
  const [template, setTemplate] = useState<InterviewTemplate | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for loading indicators
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [questionsLoading, setQuestionsLoading] = useState(false);
  const [criteriaLoading, setCriteriaLoading] = useState(false);

  // State for preview interview
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);

  // Available states for the dropdown
  const availableStates: TemplateStatus[] = [
    TemplateStatus.DRAFT,
    TemplateStatus.ACTIVE,
    TemplateStatus.PAUSED,
    TemplateStatus.ARCHIVED
  ];

  // Placeholder data - would be fetched from backend in real implementation
  const [statistics] = useState<StageStatistics>({
    candidatesCount: 12,
    passedCount: 8,
    highestScore: 92,
    openDuration: '24 days'
  });

  const [] = useState<InterviewQuestion[]>([
    {
      id: '1',
      question: 'Describe a situation where you had to solve a complex problem under pressure.',
      type: 'behavioral',
      difficulty: 'medium'
    },
    {
      id: '2',
      question: 'Explain the difference between REST and GraphQL APIs.',
      type: 'technical',
      difficulty: 'hard'
    },
    {
      id: '3',
      question: 'How would you handle a conflict with a team member?',
      type: 'situational',
      difficulty: 'medium'
    }
  ]);

  const [] = useState<EvaluationCriterion[]>([
    {
      id: '1',
      name: 'Technical Knowledge',
      description: 'Understanding of technical concepts and ability to explain them clearly',
      maxScore: 10
    },
    {
      id: '2',
      name: 'Problem Solving',
      description: 'Ability to analyze problems and develop effective solutions',
      maxScore: 10
    },
    {
      id: '3',
      name: 'Communication',
      description: 'Clarity of expression and ability to articulate thoughts',
      maxScore: 10
    }
  ]);

  // State for completed interviews
  const [completedInterviews, setCompletedInterviews] = useState<CompletedInterview[]>([]);
  const [interviewsLoading, setInterviewsLoading] = useState(true);
  const [evaluating, setEvaluating] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  const [stage, setStage] = useState<InterviewStage | null>(null);
  const [stageState, setStageState] = useState<TemplateStatus>(TemplateStatus.DRAFT);

  const { showEvaluation, dialog } = useEvaluationDialog();

  useEffect(() => {
    const fetchRoleAndStage = async () => {
      try {
        setIsLoading(true);
        setIsSaving(false);
        setError(null);

        if (!roleId) {
          setError('Role ID is missing');
          setIsLoading(false);
          return;
        }

        const roleData = await rolesService.getRole(roleId);

        if (!roleData) {
          setError('Role not found');
          setIsLoading(false);
          return;
        }

        setRole(roleData);

        // Get the interview stage
        if (
          !roleData.interviewProcess ||
          !Array.isArray(roleData.interviewProcess) ||
          stageIndex < 0 ||
          stageIndex >= roleData.interviewProcess.length
        ) {
          setError('Interview stage not found');
          setIsLoading(false);
          return;
        }

        const currentStage = roleData.interviewProcess[stageIndex];
        setStage(currentStage);

        // Fetch templates for this role
        const templates = await templatesService.getTemplates(roleId);

        // Find template for this stage
        const stageTemplate = templates.find(t => t.stageIndex === stageIndex);

        if (stageTemplate) {
          setTemplate(stageTemplate);
          setStageState(stageTemplate.status);
        } else {
          // If no template exists, create one
          try {
            const newTemplate = await templatesService.createTemplate(roleId, {
              stageIndex,
              stage: currentStage.stage,
              duration: currentStage.duration,
              customInstructions: currentStage.customInstructions || '',
              status: TemplateStatus.DRAFT
            });

            setTemplate(newTemplate);
            setStageState(newTemplate.status);
          } catch (error) {
            console.error('Failed to create template:', error);
            toast.error('Failed to create interview template');
          }
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching role and stage:', error);
        setError('Failed to load interview stage details. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchRoleAndStage();
  }, [roleId, stageIndex]);

  useEffect(() => {
    if (role?.id && stageIndex !== undefined) {
      setInterviewsLoading(true);
      fetchCompletedInterviews(role.id, stageIndex)
        .then(interviews => {
          setCompletedInterviews(interviews);
          setInterviewsLoading(false);
        })
        .catch(error => {
          console.error("Error loading interviews:", error);
          setInterviewsLoading(false);
        });
    }
  }, [role?.id, stageIndex]);

  /**
   * Fetch completed interviews from Firestore
   *
   * @param roleId The role ID
   * @param stageIndex The stage index
   * @returns Array of completed interviews
   */
  /**
   * Try to get the candidate name from an application
   * @param applicationId The application ID
   * @returns The candidate name or null if not found
   */
  async function getCandidateNameFromApplication(applicationId: string): Promise<string | null> {
    try {
      if (!applicationId) return null;

      console.log(`Trying to get candidate name from application ${applicationId}`);

      // Import Firebase modules
      const { doc, getDoc } = await import('firebase/firestore');

      // Try to get the application document
      const applicationRef = doc(db, "applications", applicationId);
      const applicationDoc = await getDoc(applicationRef);

      if (applicationDoc.exists()) {
        const applicationData = applicationDoc.data();
        if (applicationData.fullName) {
          console.log(`Found candidate name ${applicationData.fullName} from application ${applicationId}`);
          return applicationData.fullName;
        }
      }

      return null;
    } catch (error) {
      console.error(`Error getting candidate name from application ${applicationId}:`, error);
      return null;
    }
  }

  async function fetchCompletedInterviews(roleId: string, stageIndex: number): Promise<CompletedInterview[]> {
    try {
      console.log(`Fetching interviews for role ${roleId} and stage ${stageIndex}`);

      // Fetch interviews from Firestore
      const interviewsRef = collection(db, "public_interview_sessions");

      // Create a query that handles both camelCase and snake_case field names
      // First try with roleId (camelCase)
      let q = query(
        interviewsRef,
        where("roleId", "==", roleId)
      );

      let snapshot = await getDocs(q);

      // If no results, try with role_id (snake_case)
      if (snapshot.empty) {
        console.log("No results with roleId, trying role_id");
        q = query(
          interviewsRef,
          where("role_id", "==", roleId)
        );
        snapshot = await getDocs(q);
      }

      const interviews: CompletedInterview[] = [];

      console.log(`Found ${snapshot.size} interview sessions`);

      // Convert to array to use Promise.all with async operations
      const docs = snapshot.docs;

      // Process each document with async operations
      for (const doc of docs) {
        const data = doc.data();
        console.log(`Processing document ${doc.id}:`, data);

        // Check if this interview has a transcript
        const hasTranscript = !!data.transcriptId || !!data.transcript_id ||
                             (data.messages && data.messages.length > 0) ||
                             (data.transcript && data.transcript.messages && data.transcript.messages.length > 0);

        // Skip interviews without transcripts if we're looking for completed interviews
        if (!hasTranscript) {
          console.log(`Skipping interview ${doc.id} - no transcript found`);
          continue;
        }

        // Check if this interview belongs to the current stage
        // Handle both camelCase and snake_case field names
        const interviewStageIndex = data.stageIndex !== undefined ? data.stageIndex : data.stage_index;

        // If stageIndex is defined for this interview and doesn't match our target, skip it
        if (interviewStageIndex !== undefined && interviewStageIndex !== stageIndex) {
          console.log(`Skipping interview ${doc.id} - stage index ${interviewStageIndex} doesn't match ${stageIndex}`);
          continue;
        }

        // Get transcript ID (handle both camelCase and snake_case)
        const transcriptId = data.transcriptId || data.transcript_id || doc.id;

        // Get application ID (handle different field names)
        const applicationId = data.applicationId || data.application_id;

        // Get candidate name (handle different field names)
        let candidateName = data.candidateName || data.candidate_name || 'Anonymous Candidate';

        // If we have an application ID but no candidate name, try to get it from the application
        if (candidateName === 'Anonymous Candidate' && applicationId) {
          const appCandidateName = await getCandidateNameFromApplication(applicationId);
          if (appCandidateName) {
            candidateName = appCandidateName;
          }
        }

        // Get candidate ID (handle different field names)
        const candidateId = data.candidateId || data.candidate_id;

        // Get evaluation result
        const evaluationResult = data.evaluationResult || data.evaluation_result;
        let evaluationId = data.evaluationId || data.evaluation_id;

        // Check if the interview has an evaluation
        const hasEvaluation = !!evaluationId && evaluationId !== '4kpMAFwDX5U65eEyfqtU';

        // Log evaluation ID for debugging
        if (hasEvaluation) {
          console.log(`Interview ${doc.id} has evaluation ID: ${evaluationId}`);
        } else if (evaluationId === '4kpMAFwDX5U65eEyfqtU') {
          console.log(`Interview ${doc.id} has hardcoded evaluation ID, treating as no evaluation`);
          evaluationId = undefined;
        } else {
          console.log(`Interview ${doc.id} has NO evaluation ID`);
        }

        // Determine status
        let status: 'passed' | 'failed' | 'pending' = 'pending';
        let score = undefined;

        if (evaluationResult) {
          status = evaluationResult.decision === 'Go' ? 'passed' :
                  evaluationResult.decision === 'No Go' ? 'failed' : 'pending';
          score = evaluationResult.overallScore;
        }

        // Get date (handle different field names and formats)
        let date: Date;
        if (data.completedAt && typeof data.completedAt.toDate === 'function') {
          date = data.completedAt.toDate();
        } else if (data.completed_at && typeof data.completed_at.toDate === 'function') {
          date = data.completed_at.toDate();
        } else if (data.updatedAt && typeof data.updatedAt.toDate === 'function') {
          date = data.updatedAt.toDate();
        } else if (data.updated_at && typeof data.updated_at.toDate === 'function') {
          date = data.updated_at.toDate();
        } else if (data.createdAt && typeof data.createdAt.toDate === 'function') {
          date = data.createdAt.toDate();
        } else if (data.created_at && typeof data.created_at.toDate === 'function') {
          date = data.created_at.toDate();
        } else {
          // Fallback to string dates or current date
          date = new Date(data.updatedAt || data.updated_at || data.createdAt || data.created_at || Date.now());
        }

        // Transform to CompletedInterview format
        interviews.push({
          id: doc.id,
          candidateName,
          candidateId,
          applicationId,
          status,
          score,
          date,
          transcriptId,
          evaluationId,
          evaluation_id: evaluationId, // Include both camelCase and snake_case versions
          hasTranscript,
          hasEvaluation // Include flag indicating if the interview has a valid evaluation
        });
      }

      console.log(`Returning ${interviews.length} interviews`);

      // Sort by date (newest first)
      return interviews.sort((a, b) => b.date.getTime() - a.date.getTime());
    } catch (error) {
      console.error("Error fetching completed interviews:", error);
      return [];
    }
  }

  const handleBack = () => {
    router.back();
  };

  const handleStateChange = async (newState: TemplateStatus) => {
    if (!template) return;

    try {
      setIsSaving(true);

      // Store the previous state in case we need to revert
      const previousState = stageState;

      // Update UI state immediately for better user experience
      setStageState(newState);

      // Only send the status field to minimize validation errors
      const updatedTemplate = await templatesService.updateTemplate(roleId, template.id, {
        status: newState
      });

      // Update local template state with the response from the API
      if (updatedTemplate) {
        // Only update the template if the status actually changed
        if (updatedTemplate.status === newState) {
          setTemplate(updatedTemplate);
          toast.success(`Template status updated to ${newState}`);
        } else {
          // If the status didn't change, revert to the previous state
          setStageState(previousState);
          toast.error('Failed to update template status');
        }
      } else {
        // If no template was returned, revert to previous state
        setStageState(previousState);
        toast.error('Failed to update template status');
      }
    } catch (error) {
      console.error(`Error updating template status:`, error);
      toast.error('Failed to update template status');
      // Revert to previous state on error
      setStageState(template.status);
    } finally {
      setIsSaving(false);
    }
  };

  const handleEditTemplate = () => {
    setEditMode(true);
  };

  const handleCancelEdit = () => {
    setEditMode(false);
  };

  const handleSaveTemplate = async (data: UpdateTemplateRequest) => {
    if (!template) return;

    try {
      // Update template in the API
      const updatedTemplate = await templatesService.updateTemplate(roleId, template.id, data);

      // Update local template state
      setTemplate(updatedTemplate);

      // Update stage state to match template data for UI consistency
      if (stage && data.stage) {
        const updatedStage = {
          ...stage,
          stage: data.stage,
          duration: data.duration || stage.duration,
          customInstructions: data.customInstructions
        };
        setStage(updatedStage);
      }

      // Exit edit mode
      setEditMode(false);
    } catch (error) {
      console.error('Error updating template:', error);
      throw error; // Let the form component handle the error
    }
  };

  const getStateColor = (state: TemplateStatus) => {
    switch (state) {
      case TemplateStatus.DRAFT:
        return 'bg-yellow-500/20 text-yellow-600 dark:bg-yellow-500/10 dark:text-yellow-400 ring-1 ring-yellow-600/30 dark:ring-yellow-400/20';
      case TemplateStatus.ACTIVE:
        return 'bg-green-500/20 text-green-600 dark:bg-green-500/10 dark:text-green-400 ring-1 ring-green-600/30 dark:ring-green-400/20';
      case TemplateStatus.PAUSED:
        return 'bg-red-500/20 text-red-600 dark:bg-red-500/10 dark:text-red-400 ring-1 ring-red-600/30 dark:ring-red-400/20';
      case TemplateStatus.ARCHIVED:
        return 'bg-gray-500/20 text-gray-600 dark:bg-gray-500/10 dark:text-gray-400 ring-1 ring-gray-600/30 dark:ring-gray-400/20';
      default:
        return '';
    }
  };

  // Handle adding a new question
  const handleAddQuestion = async (questionData: QuestionCreate) => {
    if (!template) return;

    try {
      setQuestionsLoading(true);

      // Add the question to the template
      await templatesService.addQuestion(roleId, template.id, questionData);

      // Refresh the template to get the updated questions list
      const updatedTemplate = await templatesService.getTemplate(roleId, template.id);
      if (updatedTemplate) {
        setTemplate(updatedTemplate);
      }

      toast.success('Question added successfully');
    } catch (error) {
      console.error('Error adding question:', error);
      toast.error('Failed to add question');
    } finally {
      setQuestionsLoading(false);
    }
  };

  // Handle updating a question
  const handleUpdateQuestion = async (questionId: string, questionData: QuestionUpdate) => {
    if (!template) return;

    try {
      setQuestionsLoading(true);

      // Update the question
      await templatesService.updateQuestion(roleId, template.id, questionId, questionData);

      // Refresh the template to get the updated questions list
      const updatedTemplate = await templatesService.getTemplate(roleId, template.id);
      if (updatedTemplate) {
        setTemplate(updatedTemplate);
      }

      toast.success('Question updated successfully');
    } catch (error) {
      console.error('Error updating question:', error);
      toast.error('Failed to update question');
    } finally {
      setQuestionsLoading(false);
    }
  };

  // Handle deleting a question
  const handleDeleteQuestion = async (questionId: string) => {
    if (!template) return;

    try {
      setQuestionsLoading(true);

      // Delete the question
      await templatesService.deleteQuestion(roleId, template.id, questionId);

      // Refresh the template to get the updated questions list
      const updatedTemplate = await templatesService.getTemplate(roleId, template.id);
      if (updatedTemplate) {
        setTemplate(updatedTemplate);
      }

      toast.success('Question deleted successfully');
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error('Failed to delete question');
    } finally {
      setQuestionsLoading(false);
    }
  };

  // Handle reordering questions
  const handleReorderQuestions = async (questionIds: string[]) => {
    if (!template) return;

    try {
      setQuestionsLoading(true);

      // Reorder the questions
      await templatesService.reorderQuestions(roleId, template.id, questionIds);

      // Refresh the template to get the updated questions list
      const updatedTemplate = await templatesService.getTemplate(roleId, template.id);
      if (updatedTemplate) {
        setTemplate(updatedTemplate);
      }

      toast.success('Questions reordered successfully');
    } catch (error) {
      console.error('Error reordering questions:', error);
      toast.error('Failed to reorder questions');
    } finally {
      setQuestionsLoading(false);
    }
  };

  // Handle generating questions with AI
  const handleGenerateQuestions = async () => {
    if (!template) return;

    try {
      setQuestionsLoading(true);

      // Show a toast to indicate generation has started
      toast.info('Generating interview questions with AI...', {
        duration: 3000,
      });

      // Generate questions using AI
      const questions = await templatesService.generateQuestions(roleId, template.id);

      if (!questions || questions.length === 0) {
        toast.error('No questions were generated. Please try again.');
        setQuestionsLoading(false);
        return;
      }

      // Refresh the template to get the updated questions list
      const updatedTemplate = await templatesService.getTemplate(roleId, template.id);
      if (updatedTemplate) {
        setTemplate(updatedTemplate);
        toast.success(`${questions.length} questions generated successfully`);
      } else {
        // If we couldn't refresh the template, but we have questions, show a partial success
        toast.warning('Questions were generated but we couldn&apos;t refresh the view. Please reload the page.');
      }
    } catch (error) {
      console.error('Error generating questions:', error);

      // Provide more specific error messages based on the error
      if (error instanceof Error) {
        if (error.message.includes('404')) {
          toast.error('Template or role not found. Please check if they still exist.');
        } else if (error.message.includes('401') || error.message.includes('403')) {
          toast.error('Authentication error. Please sign in again.');
        } else if (error.message.includes('500')) {
          toast.error('Server error while generating questions. Please try again later.');
        } else {
          toast.error(`Failed to generate questions: ${error.message}`);
        }
      } else {
        toast.error('Failed to generate questions. Please try again.');
      }
    } finally {
      setQuestionsLoading(false);
    }
  };

  // Handle generating evaluation criteria with AI
  const handleGenerateCriteria = async () => {
    if (!template) return;

    try {
      setCriteriaLoading(true);

      // Show a toast to indicate generation has started
      toast.info('Generating evaluation criteria with AI...', {
        duration: 3000,
      });

      // Generate criteria using AI
      const result = await templatesService.generateCriteria(roleId, template.id);

      if (!result || !result.evaluationCriteria || result.evaluationCriteria.length === 0) {
        toast.error('No evaluation criteria were generated. Please try again.');
        setCriteriaLoading(false);
        return;
      }

      // Refresh the template to get the updated criteria
      const updatedTemplate = await templatesService.getTemplate(roleId, template.id);
      if (updatedTemplate) {
        setTemplate(updatedTemplate);
        toast.success(`${result.evaluationCriteria.length} evaluation criteria generated successfully`);
      } else {
        // If we couldn't refresh the template, but we have criteria, show a partial success
        toast.warning('Criteria were generated but we couldn&apos;t refresh the view. Please reload the page.');
      }
    } catch (error) {
      console.error('Error generating evaluation criteria:', error);

      // Provide more specific error messages based on the error
      if (error instanceof Error) {
        if (error.message.includes('404')) {
          toast.error('Template or role not found. Please check if they still exist.');
        } else if (error.message.includes('401') || error.message.includes('403')) {
          toast.error('Authentication error. Please sign in again.');
        } else if (error.message.includes('500')) {
          toast.error('Server error while generating criteria. Please try again later.');
        } else {
          toast.error(`Failed to generate criteria: ${error.message}`);
        }
      } else {
        toast.error('Failed to generate evaluation criteria. Please try again.');
      }
    } finally {
      setCriteriaLoading(false);
    }
  };

  // Handle adding a criterion
  const handleAddCriterion = async (criterionData: CriterionCreate) => {
    if (!template) return;

    try {
      setCriteriaLoading(true);
      await templatesService.addCriterion(roleId, template.id, criterionData);

      // Refresh the template to get the updated criteria
      const updatedTemplate = await templatesService.getTemplate(roleId, template.id);
      if (updatedTemplate) {
        setTemplate(updatedTemplate);
      }

      toast.success('Criterion added successfully');
    } catch (error) {
      console.error('Error adding criterion:', error);
      toast.error('Failed to add criterion');
    } finally {
      setCriteriaLoading(false);
    }
  };

  // Handle updating a criterion
  const handleUpdateCriterion = async (criterionId: string, criterionData: CriterionUpdate) => {
    if (!template) return;

    try {
      setCriteriaLoading(true);
      await templatesService.updateCriterion(roleId, template.id, criterionId, criterionData);

      // Refresh the template to get the updated criteria
      const updatedTemplate = await templatesService.getTemplate(roleId, template.id);
      if (updatedTemplate) {
        setTemplate(updatedTemplate);
      }

      toast.success('Criterion updated successfully');
    } catch (error) {
      console.error('Error updating criterion:', error);
      toast.error('Failed to update criterion');
    } finally {
      setCriteriaLoading(false);
    }
  };

  // Handle updating min score directly from the criterion item
  const handleUpdateImportance = async (criterionId: string, weight: number) => {
    if (!template) return Promise.reject(new Error('Template not found'));

    try {
      // Update the criterion with the new weight
      await templatesService.updateCriterion(roleId, template.id, criterionId, { weight });

      // Refresh the template to get the updated criteria
      const updatedTemplate = await templatesService.getTemplate(roleId, template.id);
      if (updatedTemplate) {
        setTemplate(updatedTemplate);
      }

      return Promise.resolve();
    } catch (error) {
      console.error('Error updating weight:', error);
      toast.error('Failed to update weight');
      return Promise.reject(error);
    }
  };

  // Handle deleting a criterion
  const handleDeleteCriterion = async (criterionId: string) => {
    if (!template) return;

    try {
      setCriteriaLoading(true);
      await templatesService.deleteCriterion(roleId, template.id, criterionId);

      // Refresh the template to get the updated criteria
      const updatedTemplate = await templatesService.getTemplate(roleId, template.id);
      if (updatedTemplate) {
        setTemplate(updatedTemplate);
      }

      toast.success('Criterion deleted successfully');
    } catch (error) {
      console.error('Error deleting criterion:', error);
      toast.error('Failed to delete criterion');
    } finally {
      setCriteriaLoading(false);
    }
  };

  // Function to refresh the template data
  const refreshTemplate = async () => {
    if (!template) return;
    try {
      const updatedTemplate = await templatesService.getTemplate(roleId, template.id);
      if (updatedTemplate) {
        setTemplate(updatedTemplate);
      }
    } catch (error) {
      console.error('Error refreshing template:', error);
    }
  };

  // Handle setting the pass rate
  const handleSetPassRate = async (passRate: number) => {
    if (!template) return;

    try {
      await templatesService.setPassRate(roleId, template.id, passRate);
      toast.success('Pass rate updated successfully');
      refreshTemplate();
    } catch (error) {
      console.error('Error updating pass rate:', error);
      toast.error('Failed to update pass rate');
    }
  };

  // Handle preview interview
  const handlePreviewInterview = async () => {
    if (!template || !roleId) return;

    try {
      setIsPreviewLoading(true);

      // Show a toast to indicate the preview is being prepared
      toast.info('Preparing interview preview...', {
        duration: 3000,
      });

      // First, check if the interview agent is available by making a test request
      try {
        // We'll make a simple HEAD request to check if the endpoint is available
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/realtime/check-agent/interview_agent`, {
          method: 'HEAD',
        });

        if (!response.ok) {
          // If the response is not OK, show an error message
          toast.error('Interview agent is not available. Please contact support.', {
            duration: 5000,
          });
          setIsPreviewLoading(false);
          return;
        }
      } catch (checkError) {
        console.error('Error checking interview agent availability:', checkError);
        toast.error('Failed to verify interview agent availability. Please try again later.', {
          duration: 5000,
        });
        setIsPreviewLoading(false);
        return;
      }

      // If we get here, the interview agent is available, so navigate to the video call page
      router.push(`/video-call?type=interview&roleId=${roleId}&templateId=${template.id}`);
    } catch (error) {
      console.error('Error starting interview preview:', error);

      // Provide more specific error messages based on the error
      if (error instanceof Error) {
        if (error.message.includes('404')) {
          toast.error('Template or role not found. Please check if they still exist.');
        } else if (error.message.includes('401') || error.message.includes('403')) {
          toast.error('Authentication error. Please sign in again.');
        } else if (error.message.includes('500')) {
          toast.error('Server error while preparing interview. Please try again later.');
        } else {
          toast.error(`Failed to start interview preview: ${error.message}`);
        }
      } else {
        toast.error('Failed to start interview preview. Please try again.');
      }

      setIsPreviewLoading(false);
    }
  };

  // Handle interview evaluation
  const handleEvaluateInterview = async (interview: CompletedInterview) => {
    if (!role?.id || !interview.id || !interview.transcriptId) {
      toast.error("Missing required information for evaluation");
      return;
    }

    // Skip if the interview already has a valid evaluation
    if (interview.hasEvaluation) {
      console.log(`Interview ${interview.id} already has an evaluation`);
      toast.info("This interview already has an evaluation");
      return;
    }

    try {
      setEvaluating(interview.id);

      // Get template ID and stage information
      const templateId = template?.id;
      const stageName = stage?.stage || "Screening";

      console.log(`Evaluating interview ${interview.id} with template ${templateId} for stage ${stageName} (index: ${stageIndex})`);

      // Request evaluation using the evaluation service
      const evaluationId = await evaluationService.evaluateInterview(
        interview.id,
        role.id,
        interview.applicationId,
        templateId,
        stageName,
        stageIndex
      );

      console.log(`Received new evaluation ID ${evaluationId} for interview ${interview.id}`);

      // Verify that we received a valid evaluation ID
      if (!evaluationId) {
        console.error(`Received empty evaluation ID for interview ${interview.id}`);
        toast.error("Received invalid evaluation ID");
        setEvaluating(null);
        return;
      }

      // Update local state
      setCompletedInterviews(prevInterviews => {
        return prevInterviews.map(item => {
          if (item.id === interview.id) {
            console.log(`Updating interview ${item.id} with evaluation ID ${evaluationId}`);
            return {
              ...item,
              evaluationId,
              evaluation_id: evaluationId, // Set both camelCase and snake_case versions
              hasEvaluation: true // Mark as having a valid evaluation
            };
          }
          return item;
        });
      });

      console.log(`Set evaluation ID ${evaluationId} for interview ${interview.id}`);

      toast.success("Evaluation requested successfully");

      // Poll for evaluation completion
      pollEvaluationStatus(evaluationId, interview.id);
    } catch (error) {
      console.error("Error requesting evaluation:", error);
      toast.error("Failed to evaluate interview");
    } finally {
      setEvaluating(null);
    }
  };

  // Poll for evaluation status updates
  const pollEvaluationStatus = (evaluationId: string, interviewId: string) => {
    console.log(`Starting polling for evaluation ${evaluationId} for interview ${interviewId}`);

    // Skip polling for invalid evaluation ID
    if (!evaluationId) {
      console.error(`Attempted to poll with invalid evaluation ID`);
      return;
    }

    const checkStatus = async () => {
      try {
        console.log(`Checking status of evaluation ${evaluationId}`);

        // Always force a refresh to avoid caching issues
        const evaluation = await evaluationService.getEvaluation(evaluationId, true);
        console.log(`Evaluation ${evaluationId} status: ${evaluation.status}`);

        if (evaluation.status === 'completed') {
          console.log(`Evaluation ${evaluationId} completed, updating interview ${interviewId}`);

          // Update the interview with evaluation results
          setCompletedInterviews(prevInterviews => {
            return prevInterviews.map(item => {
              if (item.id === interviewId) {
                console.log(`Updating interview ${item.id} with completed evaluation ${evaluationId}`);
                console.log(`Decision: ${evaluation.data?.decision}, Score: ${evaluation.data?.overallScore}`);

                return {
                  ...item,
                  evaluationId,
                  evaluation_id: evaluationId, // Set both camelCase and snake_case versions
                  hasEvaluation: true, // Mark as having a valid evaluation
                  status: evaluation.data?.decision === 'Go' ? 'passed' :
                         evaluation.data?.decision === 'No Go' ? 'failed' : 'pending',
                  score: evaluation.data?.overallScore
                };
              }
              return item;
            });
          });
          return true; // Stop polling
        }

        return false; // Continue polling
      } catch (error) {
        console.error(`Error checking evaluation ${evaluationId} status:`, error);
        return true; // Stop polling on error
      }
    };

    // Poll every 5 seconds until complete
    const poll = async () => {
      const complete = await checkStatus();
      if (!complete) {
        console.log(`Scheduling next poll for evaluation ${evaluationId} in 5 seconds`);
        setTimeout(poll, 5000);
      } else {
        console.log(`Polling complete for evaluation ${evaluationId}`);
      }
    };

    poll();
  };

  // Handle viewing an evaluation
  const handleViewEvaluation = (interview: CompletedInterview) => {
    // Check if the interview has a valid evaluation
    if (!interview.hasEvaluation) {
      console.log('Interview has no evaluation:', interview);
      toast.error("Evaluation not found");
      return;
    }

    const evaluationId = interview.evaluationId || interview.evaluation_id;

    if (!evaluationId) {
      console.log('Interview data:', interview);
      toast.error("Evaluation ID not found");
      return;
    }

    console.log(`Viewing evaluation ${evaluationId} for interview ${interview.id}`);

    // Clear any cached evaluation data first
    evaluationService.clearCache(evaluationId);

    // Show the evaluation dialog
    showEvaluation(evaluationId);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !role || !stage || !template) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error || 'Interview stage not found'}</p>
            <Button className="mt-4" onClick={handleBack}>
              Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="py-8 space-y-8">
      {/* Error message */}
      {error && (
        <div className="bg-red-500/10 border border-red-500/50 text-red-500 p-4 rounded-md">
          {error}
        </div>
      )}

      {/* Header with back button and status */}
      <div className="flex flex-col">
        <div className="flex justify-between items-start">
          <div className="flex flex-col">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold tracking-tight gradient-text">
                {stage.stage} <span className="gradient-text">Interview</span>
              </h1>
            </div>
            <div className="space-y-0.5">
              <p className="text-slate-600 dark:text-slate-400">{role.title}</p>
              <div className="flex items-center gap-1">
                <p className="text-xs text-slate-500 dark:text-slate-500">Role ID: {role.id}</p>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(role.id || '');
                    setCopied(true);
                    setTimeout(() => setCopied(false), 2000);
                  }}
                  className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                  title="Copy role ID to clipboard"
                >
                  {copied ? (
                    <Check className="h-3 w-3 text-green-500" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </button>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {/* Preview Interview Button */}
            <Button
              variant="default"
              size="sm"
              className="gap-2 bg-purple-600 hover:bg-purple-700 text-white"
              onClick={handlePreviewInterview}
              disabled={isPreviewLoading || stageState === TemplateStatus.DRAFT || stageState === TemplateStatus.ARCHIVED}
            >
              {isPreviewLoading ? (
                <LoadingSpinner className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              Preview Interview
            </Button>

            {/* Status Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="secondary"
                  size="sm"
                  className={cn(
                    "gap-2",
                    getStateColor(stageState)
                  )}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <LoadingSpinner className="h-4 w-4" />
                  ) : (
                    <div className={cn(
                      "h-2 w-2 rounded-full",
                      stageState === TemplateStatus.DRAFT && "bg-yellow-500 dark:bg-yellow-400",
                      stageState === TemplateStatus.ACTIVE && "bg-green-600 dark:bg-green-400",
                      stageState === TemplateStatus.PAUSED && "bg-red-600 dark:bg-red-400",
                      stageState === TemplateStatus.ARCHIVED && "bg-gray-600 dark:bg-gray-400"
                    )} />
                  )}
                  {stageState}
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {availableStates.map((state) => (
                  <DropdownMenuItem
                    key={state}
                    onClick={() => handleStateChange(state)}
                    className={cn(
                      "cursor-pointer",
                      state === stageState && "bg-slate-700"
                    )}
                  >
                    <div className="flex items-center gap-2">
                      <div className={cn(
                        "h-2 w-2 rounded-full",
                        state === TemplateStatus.DRAFT && "bg-yellow-500 dark:bg-yellow-400",
                        state === TemplateStatus.ACTIVE && "bg-green-600 dark:bg-green-400",
                        state === TemplateStatus.PAUSED && "bg-red-600 dark:bg-red-400",
                        state === TemplateStatus.ARCHIVED && "bg-gray-600 dark:bg-gray-400"
                      )} />
                      {state}
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="secondary" className="gap-2" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4" />
              Back to Role
            </Button>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className={getCardClasses()}>
          <CardContent className="p-6 flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-500 dark:text-slate-400">Candidates</p>
              <p className="text-2xl font-bold text-slate-800 dark:text-slate-100">{statistics.candidatesCount}</p>
            </div>
            <Users className="h-8 w-8 text-indigo-500 dark:text-indigo-400" />
          </CardContent>
        </Card>

        <Card className={getCardClasses()}>
          <CardContent className="p-6 flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-500 dark:text-slate-400">Pass Rate</p>
              <p className="text-2xl font-bold text-slate-800 dark:text-slate-100">{Math.round((statistics.passedCount / statistics.candidatesCount) * 100)}%</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500 dark:text-green-400" />
          </CardContent>
        </Card>

        <Card className={getCardClasses()}>
          <CardContent className="p-6 flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-500 dark:text-slate-400">Highest Score</p>
              <p className="text-2xl font-bold text-slate-800 dark:text-slate-100">{statistics.highestScore}</p>
            </div>
            <Award className="h-8 w-8 text-purple-500 dark:text-purple-400" />
          </CardContent>
        </Card>

        <Card className={getCardClasses()}>
          <CardContent className="p-6 flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-500 dark:text-slate-400">Open Duration</p>
              <p className="text-2xl font-bold text-slate-800 dark:text-slate-100">{statistics.openDuration}</p>
            </div>
            <Clock className="h-8 w-8 text-blue-500 dark:text-blue-400" />
          </CardContent>
        </Card>
      </div>

      {/* Stage Details Card */}
      {editMode ? (
        <div className="mt-8">
          <TemplateEditForm
            template={template}
            onSave={handleSaveTemplate}
            onCancel={handleCancelEdit}
          />
        </div>
      ) : (
        <CollapsibleSection
          title={<CardTitle className="text-slate-900 dark:text-slate-100">Stage Details</CardTitle>}
          headerRight={
            <Button variant="secondary" size="sm" className="gap-2" onClick={handleEditTemplate}>
              <Edit className="h-4 w-4" />
              Edit
            </Button>
          }
          className={getCardClasses()}
        >
          <div className="space-y-4">
            {/* Stage Name and Duration in one row */}
            <div className="flex flex-wrap md:flex-nowrap gap-6">
              <div className="w-full md:w-1/2">
                <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Stage Name</h3>
                <p className="text-slate-600 dark:text-slate-400">{template.stage}</p>
              </div>
              <div className="w-full md:w-1/2">
                <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Duration</h3>
                <p className="text-slate-600 dark:text-slate-400">{template.duration}</p>
              </div>
            </div>

            {/* Custom Instructions */}
            {template.customInstructions && (
              <div className="pt-2 border-t border-slate-300/50 dark:border-slate-700/50">
                <div className="mb-4">
                  <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Custom Instructions</h3>
                  <p className="text-slate-600 dark:text-slate-400 text-sm">{template.customInstructions}</p>
                </div>
              </div>
            )}
          </div>
        </CollapsibleSection>
      )}

      {/* Interview Questions */}
      <CollapsibleSection
        title={<CardTitle className="text-slate-900 dark:text-slate-100">Interview Questions</CardTitle>}
        headerRight={
          <div className="flex items-center gap-2">
            <button
              className="text-purple-500 hover:text-purple-600 dark:text-purple-300 dark:hover:text-purple-200 flex items-center justify-center h-8 w-8 rounded-full bg-purple-200/50 hover:bg-purple-200/70 dark:bg-purple-600/20 dark:hover:bg-purple-600/30 transition-all border border-purple-300/50 dark:border-purple-500/30 shadow-sm hover:shadow-purple-300/30 dark:hover:shadow-purple-500/20"
              onClick={handleGenerateQuestions}
              disabled={questionsLoading}
              title="Generate questions with AI"
            >
              {questionsLoading ? (
                <LoadingSpinner className="h-4 w-4" />
              ) : (
                <Sparkles className="h-4 w-4 animate-pulse" />
              )}
            </button>
            <button
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1 text-sm font-medium transition-colors"
              onClick={() => {
                // Find the QuestionsList component instance and trigger add
                const questionsList = document.getElementById('questions-list');
                if (questionsList) {
                  const event = new CustomEvent('add-question');
                  questionsList.dispatchEvent(event);
                }
              }}
            >
              <Plus className="h-4 w-4" />
              Add Question
            </button>
          </div>
        }
        className={getCardClasses()}
      >
        <QuestionsList
          id="questions-list"
          questions={template.questions}
          onAddQuestion={handleAddQuestion}
          onUpdateQuestion={handleUpdateQuestion}
          onDeleteQuestion={handleDeleteQuestion}
          onReorderQuestions={handleReorderQuestions}
          onGenerateQuestions={handleGenerateQuestions}
          isLoading={questionsLoading}
          showActionButtons={false}
        />
      </CollapsibleSection>

      {/* Evaluation Criteria */}
      <CollapsibleSection
        title={<CardTitle className="text-slate-900 dark:text-slate-100">Evaluation Criteria</CardTitle>}
        headerRight={
          <div className="flex items-center gap-2">
            <button
              className="text-purple-500 hover:text-purple-600 dark:text-purple-300 dark:hover:text-purple-200 flex items-center justify-center h-8 w-8 rounded-full bg-purple-200/50 hover:bg-purple-200/70 dark:bg-purple-600/20 dark:hover:bg-purple-600/30 transition-all border border-purple-300/50 dark:border-purple-500/30 shadow-sm hover:shadow-purple-300/30 dark:hover:shadow-purple-500/20"
              onClick={handleGenerateCriteria}
              disabled={criteriaLoading}
              title="Generate evaluation criteria with AI"
            >
              {criteriaLoading ? (
                <LoadingSpinner className="h-4 w-4" />
              ) : (
                <Sparkles className="h-4 w-4 animate-pulse" />
              )}
            </button>
            <div className="flex items-center gap-2 bg-slate-200/50 dark:bg-slate-800/30 border border-slate-300/50 dark:border-slate-700/50 rounded-md px-3 py-1.5">
              <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
              <PassRateForm
                currentPassRate={template?.statistics?.passRate || 0.7}
                onSave={handleSetPassRate}
                className="bg-transparent"
              />
            </div>
          </div>
        }
        className={getCardClasses()}
      >
        <div className="space-y-8">
          {/* Criteria Management */}
          <CriteriaList
            criteria={template?.evaluationCriteria || []}
            onAddCriterion={handleAddCriterion}
            onUpdateCriterion={handleUpdateCriterion}
            onDeleteCriterion={handleDeleteCriterion}
            onUpdateImportance={handleUpdateImportance}
            isLoading={criteriaLoading}
            id="criteria-list"
          />
        </div>
      </CollapsibleSection>

      {/* Completed Interviews */}
      <CollapsibleSection
        title={<CardTitle className="text-slate-900 dark:text-slate-100">Completed Interviews</CardTitle>}
        className={getCardClasses()}
      >
        {interviewsLoading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : completedInterviews.length > 0 ? (
          <div className="space-y-4">
            {completedInterviews.map((interview) => (
              <div key={interview.id} className="p-4 rounded-md bg-slate-100/80 dark:bg-slate-800/50 border border-slate-300 dark:border-slate-700">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium text-slate-700 dark:text-slate-300">{interview.candidateName}</h3>
                  <div className="flex gap-2">
                    <Badge className={cn(
                      "flex items-center px-2 py-1",
                      interview.status === 'passed' && "bg-green-500/20 text-green-600 dark:bg-green-500/10 dark:text-green-400 ring-1 ring-green-600/30 dark:ring-green-400/20",
                      interview.status === 'failed' && "bg-red-500/20 text-red-600 dark:bg-red-500/10 dark:text-red-400 ring-1 ring-red-600/30 dark:ring-red-400/20",
                      interview.status === 'pending' && "bg-yellow-500/20 text-yellow-600 dark:bg-yellow-500/10 dark:text-yellow-400 ring-1 ring-yellow-600/30 dark:ring-yellow-400/20"
                    )}>
                      {interview.status}
                    </Badge>
                    {interview.score !== undefined && (
                      <Badge className="flex items-center px-2 py-1 bg-blue-500/20 text-blue-600 dark:bg-blue-500/10 dark:text-blue-400 ring-1 ring-blue-600/30 dark:ring-blue-400/20">
                        Score: {interview.score.toFixed(1)}
                      </Badge>
                    )}
                  </div>
                </div>
                <p className="text-sm text-slate-600 dark:text-slate-400">Date: {interview.date.toLocaleDateString()}</p>

                {/* Evaluation action button */}
                <div className="mt-3 flex justify-end">
                  {/* Debug info - only show in development */}
                  {process.env.NODE_ENV === 'development' && (
                    <div className="text-xs text-slate-400 mr-auto">
                      {interview.hasEvaluation ? `Eval ID: ${interview.evaluationId?.substring(0, 6)}...` : 'No Eval ID'}
                    </div>
                  )}

                  {/* Show evaluation buttons if the interview has a valid evaluation */}
                  {interview.hasEvaluation ? (
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => handleViewEvaluation(interview)}
                      >
                        <Award className="h-4 w-4 mr-2" />
                        Quick View
                      </Button>
                      <Button
                        size="sm"
                        variant="secondary"
                        className="bg-white dark:bg-slate-900"
                        asChild
                      >
                        <Link href={`/evaluation/${interview.evaluationId}`}>
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Full Report
                        </Link>
                      </Button>
                    </div>
                  ) : interview.hasTranscript ? (
                    <Button
                      size="sm"
                      variant="default"
                      onClick={() => handleEvaluateInterview(interview)}
                      disabled={evaluating === interview.id}
                    >
                      {evaluating === interview.id ? (
                        <>
                          <LoadingSpinner className="mr-2" />
                          Evaluating...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-2" />
                          Evaluate with AI
                        </>
                      )}
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      variant="secondary"
                      disabled={true}
                    >
                      No Transcript Available
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-slate-600 dark:text-slate-400">No completed interviews yet.</p>
        )}
      </CollapsibleSection>

      {/* Evaluation Dialog */}
      {dialog}
    </div>
  );
}