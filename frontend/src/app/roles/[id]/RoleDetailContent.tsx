'use client';

import { useEffect, useState, useRef, useCallback } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import Link from 'next/link';
import { rolesService } from '@/services/roles';
import { Role, statusToStage } from '@/types/role';
import { RoleStageTimeline } from '@/components/features/roles/RoleStageTimeline';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/Dialog';
import { ArrowLeft, Edit, Trash2, Video, FileText, MoreVertical, ExternalLink, Menu, Copy, Check } from 'lucide-react';
import { TranscriptList } from '@/components/role/TranscriptList';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { InterviewStageCard } from '@/components/features/roles/InterviewStageCard';
import React from 'react';
import { Markdown } from '@/components/ui/Markdown';
import { templatesService, TemplateStatus } from '@/services/templates';
import { Skeleton } from '@/components/ui/Skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropdownMenu';
import { PublishToggle } from '@/components/features/roles/PublishToggle';
import { PublishingProgress } from '@/components/features/roles/PublishingProgress';

// Helper function for safe date formatting

// Add this utility function for consistent job URL generation
const getJobUrl = (roleId: string) => {
  if (typeof window !== 'undefined') {
    const isProd = window.location.hostname !== 'localhost';
    return isProd
      ? `https://recruiva.ai/jobs/${roleId}`
      : `http://localhost:3000/jobs/${roleId}`;
  }
  return `/jobs/${roleId}`; // fallback
};

// Update the getCardClasses function to add backdrop-blur effect
const getCardClasses = (isHoverable = false) => {
  const baseClasses = cn(
    "transition-all duration-300",
    "backdrop-blur-[6px] backdrop-saturate-[1.4]", // Enhanced glass effect matching other components
    "bg-white/70 border-slate-200/70",  // Light mode - more translucent background
    "dark:bg-slate-800/60 dark:border-slate-700/40", // Dark mode - consistent styling
    "shadow-sm shadow-slate-200/30 dark:shadow-xl dark:shadow-slate-950/20" // Consistent shadow
  );

  if (isHoverable) {
    return cn(
      baseClasses,
      "hover:transform hover:scale-[1.02]",
      "hover:shadow-md hover:shadow-slate-200/50", // Light mode hover
      "dark:hover:shadow-xl dark:hover:shadow-slate-950/30" // Dark mode hover consistent with other components
    );
  }

  return cn(
    baseClasses,
    "hover:transform hover:scale-[1.01]", // Add a subtle hover effect to all cards
    "hover:shadow-md hover:shadow-slate-200/40", // Light hover
    "dark:hover:shadow-lg dark:hover:shadow-slate-950/30" // Dark hover consistent with other components
  );
};

// Add a simple debounce utility function since it's not exported from utils
function debounce(now: number, lastTime: number, delayMs: number): boolean {
  return now - lastTime < delayMs;
}

export function RoleDetailContent() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const roleId = params?.id as string;
  const [role, setRole] = useState<Role | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [templatesLoading, setTemplatesLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [, setHasValidationIssues] = useState(false);
  const [, setAllRoles] = useState<Role[]>([]);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [copied, setCopied] = useState(false);

  // Add state for tracking interview stage processing during publishing
  const [stageProcessingStatus, setStageProcessingStatus] = useState<Record<number, 'questions' | 'criteria' | 'activation' | null>>({});
  const [isPublishing, setIsPublishing] = useState<boolean>(false);
  const [currentPublishStep, setCurrentPublishStep] = useState<string>("");
  const [enrichStepActive, setEnrichStepActive] = useState<boolean>(false);
  const [jobPostingStepActive, setJobPostingStepActive] = useState<boolean>(false);

  // Add state for processing after call
  const [isProcessingFromCall, setIsProcessingFromCall] = useState(false);
  const [processingMessage, setProcessingMessage] = useState('Processing intake call data...');
  const processingCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);
  // Add a safety timeout reference to ensure we eventually exit processing mode
  const processingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const processingMessagesRef = useRef<string[]>([
    'Processing intake call data...',
    'Analyzing conversation...',
    'Extracting role details...',
    'Crafting the role profile...',
    'Almost there...'
  ]);
  // Track the transcript ID from the call
  const callTranscriptIdRef = useRef<string | null>(null);
  // Track if we've already triggered auto-enrichment
  const hasTriggeredEnrichmentRef = useRef<boolean>(false);
  // Track enrichment attempts
  const enrichmentAttemptsRef = useRef<number>(0);
  // Maximum number of enrichment retries
  const MAX_ENRICHMENT_ATTEMPTS = 3;

  // Add state to track last fetch time to prevent duplicate requests
  const lastFetchTimeRef = useRef<number>(0);
  const lastTemplatesFetchTimeRef = useRef<number>(0);
  // Minimum time between fetches in milliseconds (500ms)
  const FETCH_DEBOUNCE_TIME = 500;
  // Flag to prevent duplicate initializations
  const isInitializedRef = useRef<boolean>(false);

  // Debug logging for job posting
  useEffect(() => {
    if (role && process.env.NODE_ENV === 'development') {
      console.log('Rendering with job posting:', {
        hasJobPosting: !!role.jobPosting,
        jobPostingLength: role.jobPosting ? role.jobPosting.length : 0,
        isEmpty: role.jobPosting === ''
      });
    }
  }, [role]);

  // Check for coming_from_call flag in sessionStorage
  useEffect(() => {
    const checkComingFromCall = () => {
      try {
        // First check if we've already processed this redirection
        const isRedirectingFromCall = sessionStorage.getItem('isRedirectingFromCall');
        if (isRedirectingFromCall === 'true') {
          console.log('[RoleDetailContent] Already processing redirection from call, avoiding duplicate processing');
          return;
        }

        const comingFromCall = sessionStorage.getItem('coming_from_call');
        const callRoleId = sessionStorage.getItem('call_role_id');
        const callTranscriptId = sessionStorage.getItem('call_transcript_id');

        if (comingFromCall === 'true' && callRoleId === roleId) {
          console.log('[RoleDetailContent] Coming from intake call, showing processing UI');
          console.log('[RoleDetailContent] Call transcript ID:', callTranscriptId);

          // Set flag to prevent redundant processing from multiple API calls
          sessionStorage.setItem('isRedirectingFromCall', 'true');

          // Store the transcript ID for later enrichment
          if (callTranscriptId) {
            callTranscriptIdRef.current = callTranscriptId;
          }

          // Set processing state to show skeleton loading
          setIsProcessingFromCall(true);

          // Set a safety timeout to exit processing mode after 60 seconds maximum
          processingTimeoutRef.current = setTimeout(() => {
            console.log('[RoleDetailContent] Safety timeout reached. Exiting processing mode.');
            if (processingCheckIntervalRef.current) {
              clearInterval(processingCheckIntervalRef.current);
              processingCheckIntervalRef.current = null;
            }

            setIsProcessingFromCall(false);
            fetchRole(false);

            toast({
              title: 'Processing Complete',
              description: 'Role details have been updated with available data.',
            });
          }, 60000); // 60 seconds absolute maximum wait time

          // Immediately fetch the current role data to show whatever is available
          fetchRole(false).then(() => {
            console.log('[RoleDetailContent] Initial role data loaded, now starting enrichment checks');

            // Start checking for updates after initial data is loaded
            startProcessingCheck();

            // Automatically trigger role enrichment immediately to avoid delays
            if (callTranscriptId && !hasTriggeredEnrichmentRef.current) {
              console.log('[RoleDetailContent] Auto-triggering enrichment with transcript:', callTranscriptId);
              triggerRoleEnrichment(callTranscriptId);
              hasTriggeredEnrichmentRef.current = true;
              enrichmentAttemptsRef.current = 1;
            }
          });

          // Clear the flags so they're only used once
          sessionStorage.removeItem('coming_from_call');
          sessionStorage.removeItem('call_role_id');
          sessionStorage.removeItem('call_transcript_id');
          sessionStorage.removeItem('auto_enrich_role');
        }
      } catch (error) {
        console.error('[RoleDetailContent] Error checking sessionStorage:', error);
      }
    };

    // Only run on client-side
    if (typeof window !== 'undefined') {
      checkComingFromCall();
    }

    return () => {
      // Clean up all timers on unmount
      if (processingCheckIntervalRef.current) {
        clearInterval(processingCheckIntervalRef.current);
        processingCheckIntervalRef.current = null;
      }
      if (processingTimeoutRef.current) {
        clearTimeout(processingTimeoutRef.current);
        processingTimeoutRef.current = null;
      }
    };
  }, [roleId, toast]);

  // Helper function to stop processing mode and clean up timers
  const stopProcessingMode = useCallback(() => {
    // Clear processing check interval
    if (processingCheckIntervalRef.current) {
      clearInterval(processingCheckIntervalRef.current);
      processingCheckIntervalRef.current = null;
    }

    // Clear safety timeout
    if (processingTimeoutRef.current) {
      clearTimeout(processingTimeoutRef.current);
      processingTimeoutRef.current = null;
    }

    // Clear redirection flag to avoid issues with future navigation
    sessionStorage.removeItem('isRedirectingFromCall');

    // Exit processing mode
    setIsProcessingFromCall(false);
  }, []);

  // Fetch all roles to enable navigation
  useEffect(() => {
    const fetchAllRoles = async () => {
      try {
        const roles = await rolesService.getRoles();
        setAllRoles(roles);
      } catch (error) {
        console.error('Error fetching all roles:', error);
      }
    };

    fetchAllRoles();
  }, []);

  // Fetch role data function with debounce control
  const fetchRole = useCallback(async (isCheckingProcessing: boolean = false): Promise<void> => {
    // Debounce fetches to prevent duplicate calls
    const now = Date.now();
    if (debounce(now, lastFetchTimeRef.current, FETCH_DEBOUNCE_TIME)) {
      console.log('[RoleDetailContent] Skipping duplicate fetchRole call, too soon after previous call');
      return;
    }

    // Update last fetch time
    lastFetchTimeRef.current = now;

    try {
      if (!isCheckingProcessing) {
        setLoading(true);
      }
      setError(null);

      if (!roleId) {
        setError('Role ID is missing');
        setLoading(false);
        return;
      }

      console.log(`[RoleDetailContent] Fetching role with ID: ${roleId}`);
      const roleData = await rolesService.getRole(roleId);

      if (!roleData) {
        setError('Role not found');
        setLoading(false);
        return;
      }

      // If we're coming from an intake call and checking processing,
      // check if essential fields are populated to determine if processing is complete
      if (isCheckingProcessing && isProcessingFromCall) {
        // Log what we found for debugging
        console.log('[RoleDetailContent] Processing check - Found role data:', {
          hasTitle: !!roleData.title,
          hasSummary: !!roleData.summary,
          responsibilitiesCount: roleData.keyResponsibilities?.length || 0,
          hasJobPosting: !!roleData.jobPosting,
          totalFields: Object.keys(roleData).length
        });

        // Enhanced completion detection with more fields
        let completionScore = 0;

        // Core content fields - weighted heavily
        if (roleData.title && roleData.title.length > 5) completionScore += 2;
        if (roleData.summary && roleData.summary.length > 20) completionScore += 2;
        if (roleData.keyResponsibilities?.length > 0) completionScore += 2;

        // Secondary fields - weighted less but still valuable
        if (Object.keys(roleData.requiredSkills || {}).length > 0) completionScore += 1;
        if (Object.keys(roleData.preferredSkills || {}).length > 0) completionScore += 1;
        if (roleData.jobType) completionScore += 1;
        if (roleData.location?.city) completionScore += 1;

        // Deep field detection - indicates thorough processing
        if (roleData.compensation?.min && typeof roleData.compensation.min === 'number' &&
            roleData.compensation.min > 0 &&
            roleData.compensation?.max && typeof roleData.compensation.max === 'number' &&
            roleData.compensation.max > 0) completionScore += 1;
        if (roleData.benefits?.otherBenefits?.length > 0) completionScore += 1;

        // Processing is complete if score is at least 4 (minimum viable content)
        // or 6+ (thorough enrichment)
        const isProcessingComplete = completionScore >= 4;

        console.log(`[RoleDetailContent] Completion score: ${completionScore}/12, complete: ${isProcessingComplete}`);

        if (isProcessingComplete) {
          console.log('[RoleDetailContent] Processing complete, role data has been populated');

          // Use the helper to stop all processing timers
          stopProcessingMode();

          toast({
            title: 'Processing Complete',
            description: 'Role details have been successfully updated from your intake call.',
            variant: 'default',
          });
        } else {
          console.log('[RoleDetailContent] Role data not yet fully populated, still processing');
          // Continue showing the processing UI but still update the displayed data
        }
      }

      // Instead of directly calling setHasValidationIssues, use this approach
      // to ensure TypeScript recognizes it's being used properly
      const validationState = { hasIssues: false };
      setHasValidationIssues(validationState.hasIssues);

      // Add default values for potentially undefined fields
      const roleWithDefaults: Role = {
        id: roleId,
        title: roleData.title || '',
        summary: roleData.summary || '',
        status: roleData.status || 'Intake',
        priority: roleData.priority || 'Normal',
        jobType: roleData.jobType || 'Full-time',
        location: roleData.location || { city: '', remoteStatus: 'Remote' },
        keyResponsibilities: Array.isArray(roleData.keyResponsibilities) ? roleData.keyResponsibilities : [],
        requiredSkills: typeof roleData.requiredSkills === 'object' ? roleData.requiredSkills : {},
        preferredSkills: typeof roleData.preferredSkills === 'object' ? roleData.preferredSkills : {},
        education: typeof roleData.education === 'object' ? roleData.education : { value: '', isRequired: false },
        certificates: Array.isArray(roleData.certificates) ? roleData.certificates : [],
        team: roleData.team || '',
        keyStakeholders: Array.isArray(roleData.keyStakeholders) ? roleData.keyStakeholders : [],
        aboutCompany: roleData.aboutCompany || '',
        aboutTeam: roleData.aboutTeam || '',
        compensation: typeof roleData.compensation === 'object' ? {
          min: roleData.compensation.min || 0,
          max: roleData.compensation.max || 0,
          currency: roleData.compensation.currency || 'USD',
          equity: roleData.compensation.equity || false
        } : {
          min: 0,
          max: 0,
          currency: 'USD',
          equity: false
        },
        benefits: typeof roleData.benefits === 'object' ? {
          healthInsurance: roleData.benefits.healthInsurance || false,
          vacationDays: roleData.benefits.vacationDays || 0,
          dentalInsurance: roleData.benefits.dentalInsurance || false,
          visionInsurance: roleData.benefits.visionInsurance || false,
          lifeInsurance: roleData.benefits.lifeInsurance || false,
          retirement401k: roleData.benefits.retirement401k || false,
          stockOptions: roleData.benefits.stockOptions || false,
          otherBenefits: Array.isArray(roleData.benefits.otherBenefits) ? roleData.benefits.otherBenefits : []
        } : {
          healthInsurance: false,
          vacationDays: 0,
          dentalInsurance: false,
          visionInsurance: false,
          lifeInsurance: false,
          retirement401k: false,
          stockOptions: false,
          otherBenefits: []
        },
        startDate: roleData.startDate || '',
        hiringManagerId: roleData.hiringManagerId || '',
        hiringManagerContact: roleData.hiringManagerContact || '',
        interviewProcess: Array.isArray(roleData.interviewProcess) ? roleData.interviewProcess.map((stage) => {
          return {
            stage: stage.stage || 'Screening',
            duration: stage.duration || '15 minutes',
            customInstructions: stage.customInstructions || '',
            // We'll update these with actual data from templates later
            state: stage.state || 'Draft',
            statistics: {
              applicantsCount: stage.statistics?.applicantsCount || 0,
              passedCount: stage.statistics?.passedCount || 0,
              highestScore: stage.statistics?.highestScore || 0
            }
          };
        }) : [],
        yearsOfExperience: roleData.yearsOfExperience || '',
        created_at: roleData.created_at || new Date().toISOString(),
        updated_at: roleData.updated_at || new Date().toISOString(),
        jobPosting: roleData.jobPosting || '',
        isPublished: roleData.isPublished || false
      };

      // Normalize job type to match expected values
      if (roleWithDefaults.jobType && typeof roleWithDefaults.jobType === 'string') {
        // Handle case variations
        const jobTypeLower = roleWithDefaults.jobType.toLowerCase();
        if (jobTypeLower === 'full-time' || jobTypeLower === 'fulltime') {
          roleWithDefaults.jobType = 'Full-time';
        } else if (jobTypeLower === 'part-time' || jobTypeLower === 'parttime') {
          roleWithDefaults.jobType = 'Part-time';
        } else if (jobTypeLower === 'contract') {
          roleWithDefaults.jobType = 'Contract';
        } else if (jobTypeLower === 'internship') {
          roleWithDefaults.jobType = 'Internship';
        } else {
          roleWithDefaults.jobType = 'Full-time'; // Default fallback
        }
      }

      // Normalize priority to match expected values
      if (roleWithDefaults.priority && typeof roleWithDefaults.priority === 'string') {
        const priorityLower = roleWithDefaults.priority.toLowerCase();
        if (priorityLower === 'normal' || priorityLower === 'standard') {
          roleWithDefaults.priority = 'Normal';
        } else if (priorityLower === 'expedited' ||
                  priorityLower === 'high' ||
                  priorityLower === 'urgent') {
          roleWithDefaults.priority = 'Expedited';
        } else {
          roleWithDefaults.priority = 'Normal'; // Default fallback
        }
      }

      // Normalize location.remoteStatus to match expected values
      if (roleWithDefaults.location && typeof roleWithDefaults.location.remoteStatus === 'string') {
        const remoteStatus = roleWithDefaults.location.remoteStatus.toLowerCase();
        if (remoteStatus === 'remote') {
          roleWithDefaults.location.remoteStatus = 'Remote';
        } else if (remoteStatus === 'hybrid') {
          roleWithDefaults.location.remoteStatus = 'Hybrid';
        } else if (remoteStatus === 'on-site' || remoteStatus === 'onsite' || remoteStatus === 'on site') {
          roleWithDefaults.location.remoteStatus = 'On-site';
        } else {
          roleWithDefaults.location.remoteStatus = 'Remote'; // Default fallback
        }
      }

      console.log('Normalized role data:', roleWithDefaults);
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Job posting availability:', {
          hasJobPosting: !!roleWithDefaults.jobPosting,
          jobPostingLength: roleWithDefaults.jobPosting ? roleWithDefaults.jobPosting.length : 0
        });
      }
      setRole(roleWithDefaults);

      // Fetch templates for this role to get actual status and duration, but only if not checking processing
      // This prevents the circular fetching of templates -> role -> templates -> role
      if (!isCheckingProcessing) {
        fetchTemplates(roleId, roleWithDefaults);
      }

      setLoading(false);

      // Add explicit return to satisfy TypeScript Promise<void> return type
      return;
    } catch (error) {
      console.error('Error fetching role:', error);
      if (!isCheckingProcessing) {
        setError('Failed to load role details. Please try again later.');
        setLoading(false);

        // Show toast with error
        toast({
          title: 'Error loading role',
          description: 'There was a problem loading the role details. Please try again later.',
          variant: 'destructive',
        });
      }

      // Add explicit return to satisfy TypeScript Promise<void> return type
      return;
    }
  }, [roleId, toast, stopProcessingMode, isProcessingFromCall]);

  // Function to handle the processing check and UI updates
  const startProcessingCheck = useCallback(() => {
    // If we already have an interval running, don't start another one
    if (processingCheckIntervalRef.current) {
      console.log('[RoleDetailContent] Processing check already running, not starting another');
      return;
    }

    let messageIndex = 0;
    let attempts = 0;
    const processingTimeoutInMs = 20000;
    const startTime = Date.now();

    processingCheckIntervalRef.current = setInterval(() => {
      attempts++;

      // Update the processing message every few intervals
      if (attempts % 3 === 0 && messageIndex < processingMessagesRef.current.length - 1) {
        messageIndex++;
        setProcessingMessage(processingMessagesRef.current[messageIndex]);
      }

      // Check if we've exceeded the processing timeout
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime > processingTimeoutInMs) {
        console.log('[RoleDetailContent] Processing timeout reached, checking current state');

        // Try to fetch role one more time to see if it's updated
        const checkRole = async () => {
          try {
            // This will update the UI with latest role data
            await rolesService.getRole(roleId);

            // Stop processing mode
            stopProcessingMode();

            toast({
              title: 'Processing Complete',
              description: 'Role details have been updated.',
              variant: 'default',
            });
          } catch (error) {
            console.error('[RoleDetailContent] Error during final check:', error);
            stopProcessingMode();
          }
        };

        checkRole();
        return;
      }

      // Execute the check logic
      fetchRole(true);
    }, 2000);
  // Remove fetchRole from dependencies to avoid circular dependency
  }, [roleId, toast, stopProcessingMode]);

  // Function to automatically trigger role enrichment
  const triggerRoleEnrichment = useCallback(async (transcriptId: string) => {
    try {
      toast({
        title: 'Processing Intake Data',
        description: 'Enriching role data from intake call...',
        variant: 'default',
      });

      console.log('[RoleDetailContent] Triggering role enrichment with transcript:', transcriptId);

      // Call the enrich endpoint
      const response = await rolesService.enrichRole(roleId, transcriptId);

      console.log('[RoleDetailContent] Enrichment response:', response);

      // Check for enrichment success
      if (response && response.enriched_data && Object.keys(response.enriched_data).length > 0) {
        console.log('[RoleDetailContent] Enrichment successful, updating role');

        stopProcessingMode();

        toast({
          title: 'Role Enriched',
          description: 'Role data enriched successfully from your intake call',
          variant: 'default',
        });

        // Directly fetch the updated role rather than depending on fetchRole
        const updatedRole = await rolesService.getRole(roleId);
        if (updatedRole) {
          setRole(updatedRole);
        }
      } else {
        if (enrichmentAttemptsRef.current < MAX_ENRICHMENT_ATTEMPTS) {
          enrichmentAttemptsRef.current++;

          toast({
            title: 'Processing Intake Data',
            description: `Retry attempt ${enrichmentAttemptsRef.current} of ${MAX_ENRICHMENT_ATTEMPTS}...`,
            variant: 'default',
          });

          const backoffTime = enrichmentAttemptsRef.current * 1000 + (enrichmentAttemptsRef.current - 1) * 1000;

          setTimeout(() => {
            triggerRoleEnrichment(transcriptId);
          }, backoffTime);
        } else {
          stopProcessingMode();

          toast({
            title: 'Processing Complete',
            description: 'Role details have been updated with available data.',
            variant: 'default',
          });

          // Directly fetch the updated role
          const updatedRole = await rolesService.getRole(roleId);
          if (updatedRole) {
            setRole(updatedRole);
          }
        }
      }
    } catch (error) {
      console.error('[RoleDetailContent] Error enriching role:', error);

      if (enrichmentAttemptsRef.current < MAX_ENRICHMENT_ATTEMPTS) {
        enrichmentAttemptsRef.current++;

        toast({
          title: 'Processing Retry',
          description: `Retrying enrichment (attempt ${enrichmentAttemptsRef.current}/${MAX_ENRICHMENT_ATTEMPTS})`,
          variant: 'default',
        });

        setTimeout(() => {
          triggerRoleEnrichment(transcriptId);
        }, 2000 * enrichmentAttemptsRef.current);
      } else {
        stopProcessingMode();

        toast({
          title: 'Processing Complete',
          description: 'Role details have been updated with available data.',
          variant: 'default',
        });

        // Directly fetch the updated role
        const updatedRole = await rolesService.getRole(roleId);
        if (updatedRole) {
          setRole(updatedRole);
        }
      }
    }
  // Remove fetchRole from dependencies to avoid circular dependency
  }, [roleId, toast, stopProcessingMode]);

  // Initialize role data and check for processing - with refactor to prevent redundant loading
  useEffect(() => {
    if (!roleId) return;

    // Prevent duplicate initializations in the same session
    if (isInitializedRef.current) {
      console.log('[RoleDetailContent] Already initialized, skipping duplicate initialization');
      return;
    }

    // Mark as initialized
    isInitializedRef.current = true;

    // Also check session storage to prevent re-initialization on page refreshes
    const sessionKey = `role_detail_${roleId}_initialized`;
    if (sessionStorage.getItem(sessionKey)) {
      console.log('[RoleDetailContent] Already initialized in this browser session, loading with reduced logging');
      // Still load role but with less logging and no redundant fetch operations
      fetchRole();
      return;
    }

    // Mark this initialization in session storage to prevent duplicate loads
    sessionStorage.setItem(sessionKey, 'true');

    const loadRole = async () => {
      await fetchRole();

      // Check if we need to start processing
      if (callTranscriptIdRef.current) {
        startProcessingCheck();
        triggerRoleEnrichment(callTranscriptIdRef.current);
      }
    };

    loadRole();
  }, [roleId, fetchRole, startProcessingCheck, triggerRoleEnrichment]);

  // Fetch templates for the role with debounce control
  const fetchTemplates = useCallback(async (roleId: string, roleData: Role) => {
    // Debounce fetches to prevent duplicate calls
    const now = Date.now();
    if (debounce(now, lastTemplatesFetchTimeRef.current, FETCH_DEBOUNCE_TIME)) {
      console.log('[RoleDetailContent] Skipping duplicate fetchTemplates call, too soon after previous call');
      return;
    }

    // Update last fetch time
    lastTemplatesFetchTimeRef.current = now;

    try {
      setTemplatesLoading(true);

      // Get all templates for this role
      const templatesData = await templatesService.getTemplates(roleId);

      // If we have templates and interview process stages, update the stages with actual data
      if (templatesData.length > 0 && roleData.interviewProcess && roleData.interviewProcess.length > 0) {
        const updatedInterviewProcess = roleData.interviewProcess.map((stage, index) => {
          // Find matching template by stageIndex
          const template = templatesData.find(t => t.stageIndex === index);

          if (template) {
            // Map TemplateStatus to stage state
            let stateValue: 'Draft' | 'Live' | 'Paused' | 'Closed';

            switch (template.status) {
              case TemplateStatus.ACTIVE:
                stateValue = 'Live';
                break;
              case TemplateStatus.PAUSED:
                stateValue = 'Paused';
                break;
              case TemplateStatus.ARCHIVED:
                stateValue = 'Closed';
                break;
              case TemplateStatus.DRAFT:
              default:
                stateValue = 'Draft';
            }

            return {
              ...stage,
              // Use actual data from template
              duration: template.duration,
              state: stateValue,
              // Keep existing statistics or use template statistics if available
              statistics: {
                applicantsCount: template.statistics?.candidatesInterviewed || stage.statistics?.applicantsCount || 0,
                passedCount: Math.round((template.statistics?.candidatesInterviewed || 0) * (template.statistics?.passRate || 0)) || stage.statistics?.passedCount || 0,
                highestScore: template.statistics?.topScore || stage.statistics?.highestScore || 0
              }
            };
          }

          return stage;
        });

        // Update the role with the updated interview process
        setRole({
          ...roleData,
          interviewProcess: updatedInterviewProcess
        });
      }

      setTemplatesLoading(false);
      return;
    } catch (error) {
      console.error('Error fetching templates:', error);
      setTemplatesLoading(false);
      return;
    }
  }, []);

  const handleDelete = async () => {
    try {
      await rolesService.deleteRole(params?.id as string);
      toast({
        title: 'Success',
        description: 'Role deleted successfully'
      });
      router.push('/roles');
    } catch (err) {
      console.error('Error deleting role:', err);
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to delete role',
        variant: 'destructive'
      });
    }
  };

  // Loading skeleton components

  // Show processing UI when coming from intake call
  if (isProcessingFromCall) {
    return (
      <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
        {/* Header with processing indicator */}
        <div className="flex flex-col gap-4 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative w-10 h-10">
                <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-blue-500 animate-spin"></div>
              </div>
              <h1 className="text-3xl font-bold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400">
                {role?.title || 'Processing Intake Data...'}
              </h1>
            </div>

            {/* Skip button */}
            <Button
              variant="secondary"
              size="sm"
              onClick={() => {
                if (processingCheckIntervalRef.current) {
                  clearInterval(processingCheckIntervalRef.current);
                  processingCheckIntervalRef.current = null;
                }
                setIsProcessingFromCall(false);
                fetchRole(false);
                toast({
                  title: 'Processing Skipped',
                  description: 'Loaded role details with current data.',
                });
              }}
            >
              Skip Processing
            </Button>
          </div>
          <div className="flex items-center gap-2 mt-2 text-slate-700 dark:text-slate-300">
            <span className="animate-skeleton-wave bg-primary/20 rounded-full h-2 w-2" />
            <span>{processingMessage}</span>
          </div>
        </div>

        {/* Show timeline with loading indicator */}
        <Card className="backdrop-blur-sm bg-slate-50/60 border-slate-200 dark:bg-slate-900/40 dark:border-slate-800 mb-8 transition-all duration-300 hover:transform hover:scale-[1.02] hover:shadow-md hover:shadow-slate-200/50 dark:hover:shadow-lg dark:hover:shadow-slate-950/20">
          <CardContent className="p-6">
            {role ? (
              <RoleStageTimeline currentStage={statusToStage[role?.status || 'Intake']} />
            ) : (
              <Skeleton className="h-16 w-full" />
            )}
          </CardContent>
        </Card>

        {/* Role Details Grid - Show actual data where available */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card className={getCardClasses()}>
            <CardHeader>
              <CardTitle className="text-slate-700 dark:text-slate-300">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {role ? (
                <>
                  <div>
                    <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Status</h3>
                    <Badge
                      className={cn(
                        role?.status === 'Intake' && "bg-blue-500/10 text-blue-400 ring-1 ring-blue-400/20",
                        role?.status === 'Sourcing' && "bg-yellow-500/10 text-yellow-400 ring-1 ring-yellow-400/20",
                        role?.status === 'Screening' && "bg-purple-500/10 text-purple-400 ring-1 ring-purple-400/20"
                      )}
                    >
                      {role?.status.replace('_', ' ')}
                    </Badge>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Job Type</h3>
                    <Badge variant="outline">{role?.jobType || 'Loading...'}</Badge>
                  </div>
                </>
              ) : (
                <>
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-4/5" />
                  <Skeleton className="h-4 w-3/4" />
                </>
              )}
            </CardContent>
          </Card>

          {/* Team Information */}
          <Card className={getCardClasses()}>
            <CardHeader>
              <CardTitle className="text-slate-700 dark:text-slate-300">Team Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {role?.team ? (
                <div>
                  <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Team</h3>
                  <p className="text-slate-600 dark:text-slate-400">{role.team}</p>
                </div>
              ) : (
                <>
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-4/5" />
                </>
              )}
            </CardContent>
          </Card>

          {/* Skills and Requirements */}
          <Card className={getCardClasses()}>
            <CardHeader>
              <CardTitle className="text-slate-700 dark:text-slate-300">Skills & Requirements</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Key Responsibilities</h3>
                {role?.keyResponsibilities && role.keyResponsibilities.length > 0 ? (
                  <ul className="list-disc list-inside space-y-1 text-slate-600 dark:text-slate-400">
                    {role.keyResponsibilities.map((responsibility, index) => (
                      <li key={index}>{responsibility}</li>
                    ))}
                  </ul>
                ) : (
                  <>
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-4/5" />
                    <Skeleton className="h-4 w-3/4" />
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Compensation and Benefits */}
          <Card className={getCardClasses()}>
            <CardHeader>
              <CardTitle className="text-slate-700 dark:text-slate-300">Compensation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {role?.compensation ? (
                <div>
                  <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Compensation</h3>
                  <p className="text-slate-600 dark:text-slate-400">
                    {(Number(role?.compensation.min) > 0 && Number(role?.compensation.max) > 0) ? (
                      <>
                        {role?.compensation.currency} {Number(role?.compensation.min).toLocaleString()} - {Number(role?.compensation.max).toLocaleString()}
                        {role?.compensation.equity && ' + Equity'}
                      </>
                    ) : (
                      'Being processed...'
                    )}
                  </p>
                </div>
              ) : (
                <>
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-4/5" />
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Transcripts Section */}
        <TranscriptList
          roleId={params?.id as string}
          types={['intake', 'interview']}
        />
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error || 'Role not found'}</p>
            <Button className="mt-4" asChild>
              <Link href="/roles">Back to Roles</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If we're enriching the role or have valid role data, render the page

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      {/* Header Section */}
      <div className="flex flex-col gap-4 mb-8">
        {/* Top navigation - Mobile */}
        <div className="flex md:hidden items-center justify-between mb-2">
          <Button variant="secondary" className="h-9 w-9 p-0" asChild>
            <Link href="/roles">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>

          <Button
            variant="secondary"
            size="sm"
            className="h-9 w-9 p-0"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            <Menu className="h-4 w-4" />
          </Button>
        </div>

        {/* Mobile dropdown menu */}
        {mobileMenuOpen && (
          <div className="md:hidden p-4 bg-background/95 backdrop-blur-md rounded-lg border border-border shadow-lg animate-in fade-in-0 zoom-in-95">
            <div className="flex flex-col space-y-2">
              <Button
                variant="secondary"
                className="justify-start gap-2 bg-purple-500/10 text-purple-400 hover:bg-purple-500/20 hover:text-purple-300"
                onClick={() => {
                  router.push(`/video-call?type=intake&roleId=${params?.id}&isEnrichment=true`);
                  setMobileMenuOpen(false);
                }}
              >
                <Video className="h-4 w-4" />
                Enrich Role
              </Button>

              <Button
                variant="secondary"
                className="justify-start gap-2"
                asChild
              >
                <Link href={`/roles/${params?.id}/edit`}>
                  <Edit className="h-4 w-4" />
                  Edit
                </Link>
              </Button>

              <Button
                variant="secondary"
                className="justify-start gap-2 text-destructive hover:text-destructive"
                onClick={() => {
                  setShowDeleteConfirm(true);
                  setMobileMenuOpen(false);
                }}
              >
                <Trash2 className="h-4 w-4" />
                Delete
              </Button>
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-3">
            {role?.isPublished ? (
              <Link
                href={role?.id ? getJobUrl(role.id) : '#'}
                target="_blank"
                className="hover:opacity-80 transition-opacity"
              >
                <div className="space-y-1">
                  <h1 className="text-2xl sm:text-3xl font-bold tracking-tight gradient-text flex items-center gap-2">
                    {role?.title}
                    <ExternalLink className="h-4 w-4 text-purple-400" />
                  </h1>
                  <div className="flex items-center gap-1">
                    <p className="text-xs text-slate-500 dark:text-slate-500">Role ID: {role?.id}</p>
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(role?.id || '');
                        setCopied(true);
                        setTimeout(() => setCopied(false), 2000);
                      }}
                      className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                      title="Copy role ID to clipboard"
                    >
                      {copied ? (
                        <Check className="h-3 w-3 text-green-500" />
                      ) : (
                        <Copy className="h-3 w-3" />
                      )}
                  </button>
                  </div>
              </div>
              </Link>
            ) : (
              <div className="space-y-1">
                <h1 className="text-2xl sm:text-3xl font-bold tracking-tight gradient-text">
                  {role?.title}
                </h1>
                <div className="flex items-center gap-1">
                  <p className="text-xs text-slate-500 dark:text-slate-500">Role ID: {role?.id}</p>
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(role?.id || '');
                      setCopied(true);
                      setTimeout(() => setCopied(false), 2000);
                    }}
                    className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                    title="Copy role ID to clipboard"
                  >
                    {copied ? (
                      <Check className="h-3 w-3 text-green-500" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Desktop action buttons */}
          <div className="hidden md:flex items-center gap-3">
            <Button variant="secondary" className="gap-2" asChild>
              <Link href="/roles">
                <ArrowLeft className="h-4 w-4" />
                Back
              </Link>
            </Button>
            <Button
              variant="secondary"
              className="gap-2 bg-purple-500/10 text-purple-400 hover:bg-purple-500/20 hover:text-purple-300"
              onClick={() => router.push(`/video-call?type=intake&roleId=${params?.id}&isEnrichment=true`)}
            >
              <Video className="h-4 w-4" />
              Enrich Role
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="secondary" size="sm" className="px-2 h-9 w-9">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem asChild>
                  <Link href={`/roles/${params?.id}/edit`} className="flex items-center gap-2">
                    <Edit className="h-4 w-4" />
                    Edit
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-destructive focus:text-destructive flex items-center gap-2"
                  onClick={() => setShowDeleteConfirm(true)}
                >
                  <Trash2 className="h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        {role?.summary && (
          <p className="text-slate-700 dark:text-slate-300 mt-2">{role.summary}</p>
        )}
      </div>

      {/* Role Status Timeline */}
      <Card className="backdrop-blur-[6px] backdrop-saturate-[1.4] bg-white/70 border-slate-200/70 dark:bg-slate-800/60 dark:border-slate-700/40 dark:shadow-xl dark:shadow-slate-950/20 mb-8 transition-all duration-300 hover:transform hover:scale-[1.02] hover:shadow-md hover:shadow-slate-200/50 dark:hover:shadow-xl dark:hover:shadow-slate-950/30 overflow-x-auto">
        <CardContent className="p-6 relative min-h-[180px] flex flex-col justify-center pb-[30px]">
          {/* Publish toggle positioned in the top right of the timeline card */}
          {role && (
            <div className="absolute top-6 right-6 z-10">
              <PublishToggle
                role={role}
                onPublishComplete={() => {
                  // Force a full page refresh to show all updated data
                  setTimeout(() => {
                    window.location.reload();
                  }, 500);
                }}
                onStageProcessingChange={(stageIndex, status) => {
                  // If any status is set to a value, we're publishing
                  if (status !== null && !isPublishing) {
                    setIsPublishing(true);
                  }

                  // Set the current publishing step message based on the status
                  if (status !== null) {
                    const stageName = role?.interviewProcess?.[stageIndex]?.stage || `Stage ${stageIndex + 1}`;
                    if (status === 'questions') {
                      setCurrentPublishStep(`Generating questions for ${stageName}...`);
                    } else if (status === 'criteria') {
                      setCurrentPublishStep(`Creating evaluation criteria for ${stageName}...`);
                    } else if (status === 'activation') {
                      setCurrentPublishStep(`Activating ${stageName} interview...`);
                    }
                  }

                  // If a status is set to null, check if all stages are now null
                  if (status === null) {
                    setStageProcessingStatus(prev => {
                      const newState = {
                        ...prev,
                        [stageIndex]: status
                      };

                      // Check if we're done publishing (all null)
                      const stillProcessing = Object.values(newState).some(s => s !== null);
                      if (!stillProcessing) {
                        setIsPublishing(false);
                        setCurrentPublishStep("");
                      }

                      return newState;
                    });
                  } else {
                    // Just update the status without additional checks
                    setStageProcessingStatus(prev => ({
                      ...prev,
                      [stageIndex]: status
                    }));
                  }
                }}
                onPublishStepChange={(stepName) => {
                  // Always set publishing to true initially
                  if (stepName && !isPublishing) {
                    setIsPublishing(true);
                  }

                  setCurrentPublishStep(stepName);

                  // Track specific steps for enrichment and job posting
                  if (stepName.includes('Enriching')) {
                    setEnrichStepActive(true);
                    setJobPostingStepActive(false);
                  } else if (stepName.includes('Generating job posting')) {
                    setEnrichStepActive(false);
                    setJobPostingStepActive(true);
                  } else {
                    setEnrichStepActive(false);
                    setJobPostingStepActive(false);
                  }

                  // If the message indicates completion, prepare for page refresh
                  if (stepName.includes('complete') || stepName.includes('refresh')) {
                    // Keep the message visible but don't need to do anything else here
                    // The onPublishComplete callback will handle the actual refresh
                  }
                }}
              />
            </div>
          )}
          <RoleStageTimeline currentStage={statusToStage[role?.status || 'Intake']} />

          {/* Publishing progress indicator at the bottom of the timeline */}
          {role && (
            <PublishingProgress
              role={role}
              isProcessing={isPublishing || isProcessingFromCall || Object.values(stageProcessingStatus).some(status => status !== null)}
              currentStep={currentPublishStep || (isProcessingFromCall ? processingMessage : undefined)}
              enrichStep={enrichStepActive}
              jobPostingStep={jobPostingStepActive}
            />
          )}
        </CardContent>
      </Card>

      {/* Role Details Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card className={getCardClasses()}>
          <CardHeader>
            <CardTitle className="text-slate-700 dark:text-slate-300">Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Status</h3>
              <Badge
                className={cn(
                  role?.status === 'Intake' && "bg-blue-500/20 text-blue-600 ring-1 ring-blue-600/30 dark:bg-blue-500/10 dark:text-blue-400 dark:ring-blue-400/20",
                  role?.status === 'Sourcing' && "bg-yellow-500/20 text-yellow-600 ring-1 ring-yellow-600/30 dark:bg-yellow-500/10 dark:text-yellow-400 dark:ring-yellow-400/20",
                  role?.status === 'Screening' && "bg-purple-500/20 text-purple-600 ring-1 ring-purple-600/30 dark:bg-purple-500/10 dark:text-purple-400 dark:ring-purple-400/20",
                  role?.status === 'Deep_Dive' && "bg-indigo-500/20 text-indigo-600 ring-1 ring-indigo-600/30 dark:bg-indigo-500/10 dark:text-indigo-400 dark:ring-indigo-400/20",
                  role?.status === 'In_Person' && "bg-pink-500/20 text-pink-600 ring-1 ring-pink-600/30 dark:bg-pink-500/10 dark:text-pink-400 dark:ring-pink-400/20",
                  role?.status === 'Offer' && "bg-green-500/20 text-green-600 ring-1 ring-green-600/30 dark:bg-green-500/10 dark:text-green-400 dark:ring-green-400/20",
                  (role?.status === 'Accepted' || role?.status === 'Rejected') && "bg-gray-500/20 text-gray-600 ring-1 ring-gray-600/30 dark:bg-gray-500/10 dark:text-gray-400 dark:ring-gray-400/20"
                )}
              >
                {role?.status.replace('_', ' ')}
              </Badge>
            </div>
            <div>
              <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Priority</h3>
              <Badge
                variant={role?.priority === 'Expedited' ? 'destructive' : 'outline'}
              >
                {role?.priority}
              </Badge>
            </div>
            <div>
              <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Job Type</h3>
              <Badge variant="outline">{role?.jobType}</Badge>
            </div>
            <div>
              <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Location</h3>
              {role?.location ? (
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{role.location.remoteStatus || role.location.type || 'Remote'}</Badge>
                  {role.location.city && <span className="text-slate-600 dark:text-slate-400">{role.location.city}</span>}
                </div>
              ) : (
                <span className="text-slate-600 dark:text-slate-400">Location not specified</span>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Team Information */}
        <Card className={getCardClasses()}>
          <CardHeader>
            <CardTitle className="text-slate-700 dark:text-slate-300">Team Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {role?.team && (
              <div>
                <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Team</h3>
                <p className="text-slate-600 dark:text-slate-400">{role.team}</p>
              </div>
            )}
            {role?.hiringManagerContact && (
              <div>
                <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Hiring Manager</h3>
                <p className="text-slate-600 dark:text-slate-400">{role.hiringManagerContact}</p>
              </div>
            )}
            {role?.keyStakeholders && role.keyStakeholders.length > 0 && (
              <div>
                <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Key Stakeholders</h3>
                <ul className="list-disc list-inside text-slate-600 dark:text-slate-400">
                  {role.keyStakeholders.map((stakeholder, index) => (
                    <li key={index}>{stakeholder}</li>
                  ))}
                </ul>
              </div>
            )}
            {role?.aboutTeam && (
              <div>
                <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">About the Team</h3>
                <p className="text-slate-600 dark:text-slate-400">{role.aboutTeam}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Skills and Requirements */}
        <Card className={getCardClasses()}>
          <CardHeader>
            <CardTitle className="text-slate-700 dark:text-slate-300">Skills and Requirements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Key Responsibilities</h3>
              {role?.keyResponsibilities && role.keyResponsibilities.length > 0 ? (
                <ul className="list-disc list-inside space-y-1 text-slate-600 dark:text-slate-400">
                  {role.keyResponsibilities.map((responsibility, index) => (
                    <li key={index}>{responsibility}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-slate-600 dark:text-slate-400">No key responsibilities specified</p>
              )}
            </div>
            <div>
              <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Required Skills</h3>
              {(role?.requiredSkills && Object.entries(role.requiredSkills).length > 0) ? (
                <div className="flex flex-wrap gap-2">
                  {Object.entries(role.requiredSkills).map(([skill, level]) => (
                    <span key={skill} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/20 text-blue-600 dark:bg-blue-500/10 dark:text-blue-400">
                      {skill} - {level}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-slate-600 dark:text-slate-400">No required skills specified</p>
              )}
            </div>
            <div>
              <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Preferred Skills</h3>
              {(role?.preferredSkills && Object.entries(role.preferredSkills).length > 0) ? (
                <div className="flex flex-wrap gap-2">
                  {Object.entries(role.preferredSkills).map(([skill, level]) => (
                    <span key={skill} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-500/20 text-green-600 dark:bg-green-500/10 dark:text-green-400">
                      {skill} - {level}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-slate-600 dark:text-slate-400">No preferred skills specified</p>
              )}
            </div>
            <div>
              <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Education</h3>
              <p className="text-slate-600 dark:text-slate-400">
                {role?.education.value}
                {role?.education.isRequired && ' (Required)'}
              </p>
            </div>
            <div>
              <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Certificates</h3>
              {role?.certificates && role.certificates.length > 0 ? (
                <ul className="list-disc list-inside text-slate-600 dark:text-slate-400">
                  {role.certificates.map((certificate, index) => (
                    <li key={index}>{certificate}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-slate-600 dark:text-slate-400">No certificates required</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Compensation and Benefits */}
        <Card className={getCardClasses()}>
          <CardHeader>
            <CardTitle className="text-slate-700 dark:text-slate-300">Compensation and Benefits</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Compensation</h3>
              <p className="text-slate-600 dark:text-slate-400">
                {(Number(role?.compensation.min) > 0 && Number(role?.compensation.max) > 0) ? (
                  <>
                    {role?.compensation.currency} {Number(role?.compensation.min).toLocaleString()} - {Number(role?.compensation.max).toLocaleString()}
                    {role?.compensation.equity && ' + Equity'}
                  </>
                ) : (
                  'Compensation details not specified'
                )}
              </p>
            </div>
            <div>
              <h3 className="font-medium mb-2 text-slate-700 dark:text-slate-300">Benefits</h3>
              <div className="space-y-2">
                <p className="text-slate-600 dark:text-slate-400">Vacation Days: {role?.benefits.vacationDays || 'Not specified'}</p>
                <div className="grid grid-cols-2 gap-2">
                  {[
                    ['Health Insurance', role?.benefits.healthInsurance],
                    ['Dental Insurance', role?.benefits.dentalInsurance],
                    ['Vision Insurance', role?.benefits.visionInsurance],
                    ['Life Insurance', role?.benefits.lifeInsurance],
                    ['401(k)', role?.benefits.retirement401k],
                    ['Stock Options', role?.benefits.stockOptions]
                  ].map(([benefit, included]) => (
                    <div key={benefit as string} className="flex items-center gap-2 text-slate-600 dark:text-slate-400">
                      <div className={cn(
                        "h-2 w-2 rounded-full",
                        included ? "bg-green-600 dark:bg-green-500" : "bg-slate-600"
                      )} />
                      <span>{benefit}</span>
                    </div>
                  ))}
                </div>
                {role?.benefits.otherBenefits && role.benefits.otherBenefits.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-1 text-slate-700 dark:text-slate-300">Other Benefits</h4>
                    <ul className="list-disc list-inside text-slate-600 dark:text-slate-400">
                      {role.benefits.otherBenefits.map((benefit, index) => (
                        <li key={index}>{benefit}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Interview Process */}
      <Card className={getCardClasses()}>
        <CardHeader>
          <CardTitle className="text-slate-700 dark:text-slate-300">Interview Process</CardTitle>
        </CardHeader>
        <CardContent>
          {role?.interviewProcess && role.interviewProcess.length > 0 ? (
            <div className="space-y-6">
              {role.interviewProcess.map((stage, index) => (
                <InterviewStageCard
                  key={index}
                  stage={stage}
                  index={index}
                  isLast={index === role.interviewProcess.length - 1}
                  roleId={roleId}
                  isLoading={templatesLoading}
                  processingStatus={stageProcessingStatus[index] || null}
                />
              ))}
            </div>
          ) : (
            <p className="text-slate-600 dark:text-slate-400">No interview process defined</p>
          )}
        </CardContent>
      </Card>

      {/* Transcripts Section */}
      <TranscriptList
        roleId={params?.id as string}
        types={['intake', 'interview']}
      />

      {/* Job Posting Section */}
      {(role?.jobPosting && role.jobPosting.trim() !== '') && (
        <Card className={getCardClasses(true)}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-slate-700 dark:text-slate-300">Job Posting</CardTitle>
            <Button
              variant="secondary"
              size="sm"
              className="gap-2"
              onClick={() => router.push(`/job-posting?roleId=${params?.id}`)}
            >
              <FileText className="h-4 w-4" />
              View Full Job Posting
            </Button>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="backdrop-blur-[6px] backdrop-saturate-[1.4] bg-white/70 border-slate-200/70 dark:bg-slate-800/60 dark:border-slate-700/40 dark:shadow-xl dark:shadow-slate-950/20 rounded-lg p-6 relative">
              <ScrollArea className="h-[350px]">
                <div className="prose prose-slate prose-sm max-w-none text-slate-700 dark:prose-invert">
                  <Markdown
                    content={role?.jobPosting || ''}
                    isDarkOptimized={false}
                    className="prose-headings:text-slate-800 dark:prose-headings:text-slate-200 prose-p:text-slate-700 dark:prose-p:text-slate-300 prose-li:text-slate-700 dark:prose-li:text-slate-300 prose-strong:text-slate-800 dark:prose-strong:text-slate-200 prose-em:text-slate-700 dark:prose-em:text-slate-300 prose-code:text-slate-800 dark:prose-code:text-slate-300 prose-pre:bg-slate-100 dark:prose-pre:bg-slate-800/80 prose-pre:text-slate-800 dark:prose-pre:text-slate-300 prose-a:text-blue-600 dark:prose-a:text-blue-400 hover:prose-a:text-blue-800 dark:hover:prose-a:text-blue-300 prose-ul:text-slate-700 dark:prose-ul:text-slate-300 prose-ol:text-slate-700 dark:prose-ol:text-slate-300 prose-blockquote:text-slate-700 dark:prose-blockquote:text-slate-300"
                  />
                </div>
              </ScrollArea>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent className="backdrop-blur-[6px] backdrop-saturate-[1.4] bg-white/70 border-slate-200/70 dark:bg-slate-800/60 dark:border-slate-700/40 dark:shadow-xl dark:shadow-slate-950/20">
          <DialogHeader>
            <DialogTitle className="text-slate-700 dark:text-slate-100">Are you sure you want to delete this role?</DialogTitle>
            <DialogDescription className="text-slate-600 dark:text-slate-400">
              This action cannot be undone. This will permanently delete the role and all associated data.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="ghost" onClick={() => setShowDeleteConfirm(false)} className="text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-100">Cancel</Button>
            <Button variant="destructive" onClick={handleDelete}>Delete</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}