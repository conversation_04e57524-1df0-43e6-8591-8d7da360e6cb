import { Montser<PERSON> } from 'next/font/google';

const montserrat = Montserrat({ 
  subsets: ['latin'],
  weight: ['500', '600', '700'],
});

export default function RoleDetailLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <main className="min-h-screen">
      <div className={`container px-0 sm:px-4 mx-auto ${montserrat.className}`}>
        {children}
      </div>
    </main>
  );
}
