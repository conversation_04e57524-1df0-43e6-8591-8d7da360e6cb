'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/Table';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { 
    ChevronDown, 
    ChevronRight, 
    ArrowUpRight, 
    Calendar, 
    FileText,
    Award,

    UserRound
} from 'lucide-react';
import { applicationsService } from '@/services/applications';
import { ExtendedApplication } from '@/types/role';

import { cn } from '@/lib/utils/styles';

// Enhanced glass effect for cards, matching dashboard style
const dashboardCardClass = "backdrop-blur-[6px] backdrop-saturate-[1.4] bg-white/80 border-slate-200/70 dark:bg-slate-800/60 dark:border-slate-700/40 shadow-sm hover:shadow-md hover:shadow-slate-200/30 dark:shadow-xl dark:shadow-slate-950/20 dark:hover:shadow-lg dark:hover:shadow-slate-950/30 transition-all duration-300";

/**
 * ApplicationsPage component displays all candidate applications
 * with a similar UI to the Recent Applications section on the dashboard
 */
export default function ApplicationsPage() {
    const { user, loading: authLoading } = useAuth();
    const router = useRouter();
    const [applications, setApplications] = useState<ExtendedApplication[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [expandedApplications, setExpandedApplications] = useState<Record<string, boolean>>({});

    // Check authentication
    useEffect(() => {
        if (!authLoading && !user) {
            router.push('/signin');
            return;
        }
    }, [user, router, authLoading]);

    // Load applications data using the optimized method
    useEffect(() => {
        const loadApplications = async () => {
            if (!user) return;
            
            try {
                setLoading(true);
                setError(null);
                
                // Get all applications sorted by newest first
                const allApplications = await applicationsService.getApplications('newest');
                
                setApplications(allApplications);
            } catch (error) {
                console.error('Error loading applications:', error);
                setError('Failed to load applications data');
                setApplications([]);
            } finally {
                setLoading(false);
            }
        };

        if (user) {
            loadApplications();
        }
    }, [user]);

    // Toggle expanded state for an application
    const toggleApplicationExpanded = (applicationId: string) => {
        setExpandedApplications(prev => ({
            ...prev,
            [applicationId]: !prev[applicationId]
        }));
    };

    // Navigate to application details page
    const navigateToApplication = (applicationId: string) => {
        router.push(`/applications/${applicationId}`);
    };

    // Show loading state while auth is being checked
    if (authLoading) {
        return (
            <DashboardLayout>
                <div className="flex justify-center items-center h-screen">
                    <LoadingSpinner size="lg" />
                </div>
            </DashboardLayout>
        );
    }

    // Don't render anything if user is not authenticated
    if (!user) {
        return null;
    }

    return (
        <DashboardLayout>
            <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0 mb-6">
                <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Applications</h2>
                <Button
                    onClick={() => router.push('/applications/new')}
                    className="justify-center"
                >
                    <UserRound className="mr-2 h-4 w-4" />
                    Add New Application
                </Button>
            </div>
            
            <div className="grid gap-4 grid-cols-1">
                <Card className={`${dashboardCardClass}`}>
                    <CardHeader>
                        <CardTitle>All Applications</CardTitle>
                    </CardHeader>
                    <CardContent className="overflow-auto p-0">
                        <div className="w-full overflow-x-auto rounded-b-lg">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-slate-50/50 dark:bg-slate-800/30">
                                        <TableHead>Candidate</TableHead>
                                        <TableHead>Role</TableHead>
                                        <TableHead className="hidden sm:table-cell">Date</TableHead>
                                        <TableHead>Resume</TableHead>
                                        <TableHead>Interview</TableHead>
                                        <TableHead>Decision</TableHead>
                                        <TableHead>Stage</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {loading ? (
                                        <TableRow>
                                            <TableCell colSpan={7} className="text-center">
                                                <LoadingSpinner />
                                            </TableCell>
                                        </TableRow>
                                    ) : error ? (
                                        <TableRow>
                                            <TableCell colSpan={7} className="text-center text-red-500">
                                                {error}
                                            </TableCell>
                                        </TableRow>
                                    ) : applications.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={7} className="text-center text-slate-500">
                                                No applications found
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        applications.map((application) => (
                                            <React.Fragment key={application.id || 'unknown'}>
                                                <TableRow
                                                    className={cn(
                                                        "group cursor-pointer",
                                                        expandedApplications[application.id || '']
                                                            ? "bg-purple-50/10 dark:bg-purple-900/5 border-l-4 border-l-purple-200 dark:border-l-purple-800/30"
                                                            : "hover:bg-slate-50/50 dark:hover:bg-slate-800/30 hover:shadow-sm dark:hover:shadow-slate-900/10 hover:scale-[1.01] transition-all duration-200"
                                                    )}
                                                    onClick={() => application.id && navigateToApplication(application.id)}
                                                >
                                                    <TableCell className="font-medium">
                                                        <div className="flex items-center space-x-2">
                                                            <div
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    if (application.id) {
                                                                        setTimeout(() => {
                                                                            toggleApplicationExpanded(application.id);
                                                                        }, 0);
                                                                    }
                                                                }}
                                                                className={cn(
                                                                    "p-1 rounded-full cursor-pointer transition-all duration-200",
                                                                    expandedApplications[application.id || '']
                                                                        ? "bg-purple-100 dark:bg-purple-900/20 hover:bg-purple-200 dark:hover:bg-purple-800/30"
                                                                        : "hover:bg-slate-200/70 dark:hover:bg-slate-700/70"
                                                                )}
                                                            >
                                                                {expandedApplications[application.id || ''] ? (
                                                                    <ChevronDown className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                                                                ) : (
                                                                    <ChevronRight className="h-4 w-4 text-slate-500 dark:text-slate-400" />
                                                                )}
                                                            </div>
                                                            <div
                                                                className="group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-200 flex items-center"
                                                                onClick={() => application.id && navigateToApplication(application.id)}
                                                            >
                                                                {application.fullName || 'Unknown Candidate'}
                                                                <ArrowUpRight className="ml-1 h-3.5 w-3.5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                                                            </div>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>{application.roleName || 'N/A'}</TableCell>
                                                    <TableCell className="hidden sm:table-cell">
                                                        {application.created_at ? new Date(application.created_at).toLocaleDateString() : 'N/A'}
                                                    </TableCell>

                                                    {/* Resume Score */}
                                                    <TableCell>
                                                        {application.evaluationData &&
                                                         application.evaluationData.overallScore ? (
                                                            <div className="flex items-center">
                                                                <div className="relative w-7 h-7 mr-1">
                                                                    {(() => {
                                                                        const score = parseFloat(String(application.evaluationData?.overallScore || 0));
                                                                        let textColor = "text-slate-800 dark:text-slate-200";

                                                                        // Determine color based on decision or score
                                                                        if (application.evaluationData?.decision === 'Go' || application.evaluationData?.decision === 'PASS') {
                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                        } else if (application.evaluationData?.decision === 'No Go' || application.evaluationData?.decision === 'FAIL') {
                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                        } else if (score >= 4) {
                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                        } else if (score >= 3) {
                                                                            textColor = "text-amber-800 dark:text-amber-200";
                                                                        } else if (score < 3) {
                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                        }

                                                                        return (
                                                                            <div className="flex flex-col items-center">
                                                                                <span className={`text-xs font-bold ${textColor}`}>
                                                                                    {application.evaluationData?.overallScore}
                                                                                </span>
                                                                                <span className="text-[10px] text-slate-500">/ 5</span>
                                                                            </div>
                                                                        );
                                                                    })()}
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <span className="text-xs text-slate-500">N/A</span>
                                                        )}
                                                    </TableCell>

                                                    {/* Interview Score */}
                                                    <TableCell>
                                                        {application.interviews && application.interviews.length > 0 &&
                                                         (application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore) ? (
                                                            <div className="flex items-center">
                                                                <div className="relative w-7 h-7 mr-1">
                                                                    {(() => {
                                                                        const score = parseFloat(String(application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore || 0));
                                                                        let textColor = "text-slate-800 dark:text-slate-200";

                                                                        // Determine color based on decision or score
                                                                        if (application.interviews[0].decision === 'Go' || application.interviews[0].decision === 'PASS') {
                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                        } else if (application.interviews[0].decision === 'No Go' || application.interviews[0].decision === 'FAIL') {
                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                        } else if (score >= 80) {
                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                        } else if (score >= 60) {
                                                                            textColor = "text-amber-800 dark:text-amber-200";
                                                                        } else if (score < 60) {
                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                        }

                                                                        return (
                                                                            <div className="flex flex-col items-center">
                                                                                <span className={`text-xs font-bold ${textColor}`}>
                                                                                    {application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore}
                                                                                </span>
                                                                                <span className="text-[10px] text-slate-500">/ 100</span>
                                                                            </div>
                                                                        );
                                                                    })()}
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <span className="text-xs text-slate-500">N/A</span>
                                                        )}
                                                    </TableCell>

                                                    {/* Decision */}
                                                    <TableCell>
                                                        {application.interviews && application.interviews.length > 0 && application.interviews[0].decision ? (
                                                            <div className={cn(
                                                                "inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",
                                                                application.interviews[0].decision === 'Go' || application.interviews[0].decision === 'PASS' ?
                                                                "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" :
                                                                application.interviews[0].decision === 'No Go' || application.interviews[0].decision === 'FAIL' ?
                                                                "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300" :
                                                                "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"
                                                            )}>
                                                                {application.interviews[0].decision}
                                                            </div>
                                                        ) : application.evaluationData && application.evaluationData.decision ? (
                                                            <div className={cn(
                                                                "inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",
                                                                application.evaluationData.decision === 'Go' || application.evaluationData.decision === 'PASS' ?
                                                                "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" :
                                                                application.evaluationData.decision === 'No Go' || application.evaluationData.decision === 'FAIL' ?
                                                                "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300" :
                                                                "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"
                                                            )}>
                                                                {application.evaluationData.decision}
                                                            </div>
                                                        ) : (
                                                            <span className="text-xs text-slate-500">Pending</span>
                                                        )}
                                                    </TableCell>

                                                    {/* Stage */}
                                                    <TableCell>
                                                        {application.interviews && application.interviews.length > 0 ? (
                                                            <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                                                                {application.interviews[0].stage_name || application.interviews[0].stageName ||
                                                                 (application.interviews[0].stageIndex === 0 ? 'Screening' : 'Interview')}
                                                            </div>
                                                        ) : (
                                                            <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300">
                                                                Applied
                                                            </div>
                                                        )}
                                                    </TableCell>
                                                </TableRow>

                                                {/* Expanded content - rendered as a separate row */}
                                                {expandedApplications[application.id || ''] && (
                                                    <TableRow className="bg-purple-50/20 dark:bg-purple-900/10 border-t border-slate-200/50 dark:border-slate-700/20 border-l-4 border-l-purple-200 dark:border-l-purple-800/30 hover:bg-purple-50/20 dark:hover:bg-purple-900/10">
                                                        <TableCell colSpan={7} className="p-0">
                                                            <div className="px-4 py-3">
                                                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                                    {/* Left column - Contact Info */}
                                                                    <div className="space-y-2">
                                                                        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Contact Info</h4>
                                                                        {/* Email */}
                                                                        {application.email && (
                                                                            <div className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                                                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                                                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                                                                </svg>
                                                                                <span>{application.email}</span>
                                                                            </div>
                                                                        )}

                                                                        {/* Phone */}
                                                                        {application.phoneNumber && (
                                                                            <div className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                                                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                                                                </svg>
                                                                                <span>{application.phoneNumber}</span>
                                                                            </div>
                                                                        )}

                                                                        {/* Created Date */}
                                                                        {application.created_at && (
                                                                            <div className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                                                                                <Calendar className="h-4 w-4 text-slate-400 flex-shrink-0" />
                                                                                <span>Applied {new Date(application.created_at).toLocaleDateString()}</span>
                                                                            </div>
                                                                        )}
                                                                    </div>

                                                                    {/* Middle column - Resume Evaluation */}
                                                                    <div className="space-y-2">
                                                                        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Resume Evaluation</h4>

                                                                        {application.evaluationData &&
                                                                         (application.evaluationData.overallScore !== undefined) ? (
                                                                            <div className="flex items-center gap-3 mb-2">
                                                                                <div className="relative w-10 h-10">
                                                                                    {/* Calculate score for color determination */}
                                                                                    {(() => {
                                                                                        const score = parseFloat(String(application.evaluationData.overallScore || 0));
                                                                                        let scoreColor = "bg-blue-500";
                                                                                        let borderColor = "border-slate-200 dark:border-slate-700";
                                                                                        let textColor = "text-slate-800 dark:text-slate-200";

                                                                                        // Determine color based on decision or score
                                                                                        if (application.evaluationData.decision === 'Go' || application.evaluationData.decision === 'PASS') {
                                                                                            scoreColor = "bg-green-500";
                                                                                            borderColor = "border-green-200 dark:border-green-700";
                                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                                        } else if (application.evaluationData.decision === 'No Go' || application.evaluationData.decision === 'FAIL') {
                                                                                            scoreColor = "bg-red-500";
                                                                                            borderColor = "border-red-200 dark:border-red-700";
                                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                                        } else if (score >= 80) {
                                                                                            scoreColor = "bg-green-500";
                                                                                            borderColor = "border-green-200 dark:border-green-700";
                                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                                        } else if (score >= 60) {
                                                                                            scoreColor = "bg-amber-500";
                                                                                            borderColor = "border-amber-200 dark:border-amber-700";
                                                                                            textColor = "text-amber-800 dark:text-amber-200";
                                                                                        } else if (score < 60) {
                                                                                            scoreColor = "bg-red-500";
                                                                                            borderColor = "border-red-200 dark:border-red-700";
                                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                                        }

                                                                                        return (
                                                                                            <div className={`w-10 h-10 rounded-full border-3 ${borderColor} flex items-center justify-center relative`}>
                                                                                                <div className="flex flex-col items-center">
                                                                                                    <span className={`text-sm font-bold ${textColor}`}>
                                                                                                        {application.evaluationData.overallScore}
                                                                                                    </span>
                                                                                                    <span className="text-[10px] text-slate-500">/ 5</span>
                                                                                                </div>
                                                                                                <div className="absolute inset-0 rounded-full overflow-hidden">
                                                                                                    <div
                                                                                                        className={`absolute bottom-0 left-0 right-0 ${scoreColor}`}
                                                                                                        style={{
                                                                                                            height: `${Math.min(100, Math.max(0, (score / 100) * 100))}%`,
                                                                                                            opacity: '0.3'
                                                                                                        }}
                                                                                                    ></div>
                                                                                                </div>
                                                                                            </div>
                                                                                        );
                                                                                    })()}
                                                                                </div>

                                                                                <div>
                                                                                    <div className={cn(
                                                                                        "inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mb-1",
                                                                                        application.evaluationData.decision === 'Go' || application.evaluationData.decision === 'PASS' ?
                                                                                        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" :
                                                                                        application.evaluationData.decision === 'No Go' || application.evaluationData.decision === 'FAIL' ?
                                                                                        "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300" :
                                                                                        "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"
                                                                                    )}>
                                                                                        {application.evaluationData.decision}
                                                                                    </div>
                                                                                    <p className="text-xs text-slate-500">Resume Score</p>
                                                                                </div>
                                                                            </div>
                                                                        ) : (
                                                                            <div className="text-sm text-slate-500">No resume evaluation available</div>
                                                                        )}

                                                                        {/* Resume Link */}
                                                                        {application.resumeUrl && (
                                                                            <div className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                                                                <FileText className="h-4 w-4 flex-shrink-0" />
                                                                                <a href={application.resumeUrl} target="_blank" rel="noopener noreferrer" onClick={(e) => e.stopPropagation()}>View Resume</a>
                                                                            </div>
                                                                        )}
                                                                    </div>

                                                                    {/* Right column - Interview Evaluation */}
                                                                    <div className="space-y-2">
                                                                        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Interview Evaluation</h4>

                                                                        {application.interviews && application.interviews.length > 0 &&
                                                                         (application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore) ? (
                                                                            <div className="flex items-center gap-3 mb-2">
                                                                                <div className="flex flex-col items-center justify-center">
                                                                                    {/* Calculate score for color determination */}
                                                                                    {(() => {
                                                                                        const score = parseFloat(String(application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore || 0));
                                                                                        let textColor = "text-slate-800 dark:text-slate-200";

                                                                                        // Determine color based on decision or score
                                                                                        if (application.interviews[0].decision === 'Go' || application.interviews[0].decision === 'PASS') {
                                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                                        } else if (application.interviews[0].decision === 'No Go' || application.interviews[0].decision === 'FAIL') {
                                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                                        } else if (score >= 80) {
                                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                                        } else if (score >= 60) {
                                                                                            textColor = "text-amber-800 dark:text-amber-200";
                                                                                        } else if (score < 60) {
                                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                                        }

                                                                                        return (
                                                                                            <div className="text-center">
                                                                                                <span className={`text-2xl font-bold ${textColor}`}>
                                                                                                    {application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore}
                                                                                                </span>
                                                                                                <div className="text-xs text-slate-500">out of 100</div>
                                                                                            </div>
                                                                                        );
                                                                                    })()}
                                                                                </div>
                                                                            </div>
                                                                        ) : (
                                                                            <div className="text-sm text-slate-500">No interview evaluation available</div>
                                                                        )}

                                                                        {/* View Evaluation Link */}
                                                                        {application.interviews && application.interviews.length > 0 &&
                                                                         application.interviews[0] && (application.interviews[0].evaluation_id || application.interviews[0].evaluationId) && (
                                                                            <div className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                                                                <Award className="h-4 w-4 flex-shrink-0" />
                                                                                <a
                                                                                    href={`/evaluation/${application.interviews && application.interviews[0] ? (application.interviews[0].evaluation_id || application.interviews[0].evaluationId) : ''}`}
                                                                                    onClick={(e) => {
                                                                                        e.stopPropagation();
                                                                                        router.push(`/evaluation/${application.interviews && application.interviews[0] ? (application.interviews[0].evaluation_id || application.interviews[0].evaluationId) : ''}`);
                                                                                    }}
                                                                                >
                                                                                    View Interview Evaluation
                                                                                </a>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>

                                                                {/* View Details Button - Moved to the left */}
                                                                <div className="mt-3 flex justify-start">
                                                                    <Button
                                                                        variant="secondary"
                                                                        size="sm"
                                                                        onClick={() => application.id && navigateToApplication(application.id)}
                                                                        className="text-xs"
                                                                    >
                                                                        View Application Details
                                                                        <ArrowUpRight className="ml-1 h-3 w-3" />
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                        </TableCell>
                                                    </TableRow>
                                                )}
                                            </React.Fragment>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </DashboardLayout>
    );
}