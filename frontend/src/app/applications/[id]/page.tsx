'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ArrowLeft, FileText, User, Briefcase, BarChart, Award, MessageSquare } from 'lucide-react';
import Link from 'next/link';
import { applicationsService } from '@/services/applications';
import { ExtendedApplication } from '@/types/role';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils/styles';
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/Tabs";
import { useEvaluationDialog } from '@/components/evaluation/EvaluationReportDialog';
import { useTranscriptDialog } from '@/components/interview/TranscriptDialog';
import { templatesService } from '@/services/templates';
// Removed unused imports

/**
 * ApplicationDetailPage component displays details of a single application
 */
export default function ApplicationDetailPage({ params }: { params: { id: string } }) {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [application, setApplication] = useState<ExtendedApplication | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Add dialog functionality
  const { showEvaluation, dialog: evaluationDialog } = useEvaluationDialog();
  const { showTranscript, dialog: transcriptDialog } = useTranscriptDialog();

  // Check authentication
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/signin');
      return;
    }
  }, [user, router, authLoading]);

  // Load application data
  useEffect(() => {
    const loadApplication = async () => {
      if (!user || !params.id) return;

      try {
        setLoading(true);
        setError(null);
        const app = await applicationsService.getApplication(params.id);

        if (!app) {
          setError('Application not found');
          setApplication(null);
          return;
        }

        // Debug the application data
        console.log('Application data:', app);
        console.log('Evaluation data:', app.evaluationData);

        // Add more detailed logging for debugging
        if (app.interviews && app.interviews.length > 0) {
          console.log('Interview data structure:', Object.keys(app.interviews[0]));

          // Check if evaluation data is available in the interview
          if (app.interviews[0].evaluation) {
            console.log('Interview evaluation data:', app.interviews[0].evaluation);
          }

          // Log evaluation ID
          const evalId = app.interviews[0].evaluation_id || app.interviews[0].evaluationId;
          console.log('First interview evaluation ID:', evalId || 'None');
        }

        // Ensure evaluationData is properly structured
        if (app.evaluationData) {
          // Log the structure to help debug
          console.log('Evaluation data structure:', Object.keys(app.evaluationData));

          // If evaluationData is nested inside full_evaluation, extract it
          if (app.evaluationData && 'full_evaluation' in app.evaluationData &&
              app.evaluationData.full_evaluation &&
              (!app.evaluationData.keyStrengths || app.evaluationData.keyStrengths.length === 0)) {
            console.log('Extracting data from full_evaluation');
            app.evaluationData = {
              ...app.evaluationData,
              ...(app.evaluationData.full_evaluation as any)
            };
          }
        }

        // Log interview data if available
        if (app.interviews && app.interviews.length > 0) {
          console.log(`Found ${app.interviews.length} interviews for application ${params.id}`);
          console.log('First interview data:', app.interviews[0]);

          // Ensure each interview has the necessary fields and extract evaluation data
          app.interviews = app.interviews.map(interview => {
            // If transcript is missing but transcript_id is present, create a placeholder
            if (!interview.transcript && interview.transcript_id) {
              interview.transcript = { id: interview.transcript_id, status: 'pending' };
            }

            // Ensure stage_name is set (handle camelCase/snake_case inconsistency)
            if (!interview.stage_name && interview.stageName) {
              interview.stage_name = interview.stageName;
            }

            // Ensure evaluation_id is set (handle camelCase/snake_case inconsistency)
            if (!interview.evaluation_id && interview.evaluationId) {
              interview.evaluation_id = interview.evaluationId;
              console.log(`Found evaluationId (camelCase): ${interview.evaluationId}`);
            }

            // Debug log for evaluation IDs
            if (interview.evaluation_id) {
              console.log(`Interview ${interview.id} has evaluation_id: ${interview.evaluation_id}`);
            } else if (interview.evaluationId) {
              console.log(`Interview ${interview.id} has evaluationId: ${interview.evaluationId}`);
            } else {
              console.log(`Interview ${interview.id} has NO evaluation ID`);
            }

            // Extract evaluation data if available
            if (interview.evaluation) {
              console.log(`Interview ${interview.id} has embedded evaluation data`);

              // Extract score
              if (!interview.score && !interview.overall_score && !interview.overallScore) {
                if (interview.evaluation.overall_score) {
                  interview.overall_score = interview.evaluation.overall_score;
                } else if (interview.evaluation.overallScore) {
                  interview.overallScore = interview.evaluation.overallScore;
                } else if (interview.evaluation.score) {
                  interview.score = interview.evaluation.score;
                }
              }

              // Extract decision
              if (!interview.decision) {
                interview.decision = interview.evaluation.decision;
              }

              // Extract confidence
              if (!interview.confidence) {
                interview.confidence = interview.evaluation.confidence;
              }
            }

            return interview;
          });

          // Fetch stage names from templates if needed
          const fetchStageNamesFromTemplates = async () => {
            if (!app.interviews || app.interviews.length === 0) return;

            // Get the role ID from the application
            const roleId = app.roleId;
            if (!roleId) return;

            // Process each interview that has a template_id but no stage_name
            for (let i = 0; i < app.interviews.length; i++) {
              const interview = app.interviews[i];

              // For the first interview (index 0), always use 'Screening' as the stage name if no stage name is available
              if (i === 0 && (!interview.stage_name && !interview.stageName)) {
                console.log('Setting stage name to Screening for first interview');
                interview.stage_name = 'Screening';
                interview.stageName = 'Screening';
                continue; // Skip template fetching for this interview
              }

              const templateId = interview.template_id || interview.templateId;
              if (templateId && !interview.stage_name && !interview.stageName) {
                try {
                  console.log(`Fetching template ${templateId} for role ${roleId} to get stage name`);
                  const template = await templatesService.getTemplate(roleId, templateId);

                  if (template && template.stage) {
                    console.log(`Found stage name in template: ${template.stage}`);
                    interview.stage_name = template.stage;
                    interview.stageName = template.stage;
                  }
                } catch (error) {
                  console.error('Error fetching template for stage name:', error);
                }
              }
            }
          };

          // Call the function to fetch stage names
          fetchStageNamesFromTemplates().then(() => {
            console.log('Finished fetching stage names from templates');
          }).catch(error => {
            console.error('Error in fetchStageNamesFromTemplates:', error);
          });
        } else {
          console.log('No interviews found for application', params.id);
        }

        setApplication(app);
      } catch (error) {
        console.error('Error loading application:', error);
        setError('Failed to load application data');
        setApplication(null);
      } finally {
        setLoading(false);
      }
    };

    if (user && params.id) {
      loadApplication();
    }
  }, [user, params.id]);

  const formatCreatedDate = (dateString: string | undefined): string => {
    if (!dateString) return 'Unknown';

    try {
      // Check if it's a Firebase timestamp object (has seconds and nanoseconds)
      if (typeof dateString === 'object' && dateString !== null) {
        // @ts-expect-error - Handle Firestore Timestamp objects
        const seconds = dateString.seconds || 0;
        return format(new Date(seconds * 1000), 'PPP');
      }

      // Try to parse the date string
      const date = new Date(dateString);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return 'Unknown';
      }

      return format(date, 'PPP');
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Unknown';
    }
  };

  // Show loading state while auth is being checked
  if (authLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-screen">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  // Don't render anything if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <DashboardLayout>
      {/* Render the dialogs */}
      {evaluationDialog}
      {transcriptDialog}

      <div>
        <Button
          variant="ghost"
          className="mb-4"
          onClick={() => router.push('/applications')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Applications
        </Button>

        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-bold tracking-tight">
            {application ? application.fullName : 'Application Details'}
          </h2>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center my-8">
          <LoadingSpinner size="lg" />
        </div>
      ) : error ? (
        <Card className="mt-4">
          <CardContent className="pt-6">
            <div className="text-red-500 p-4">{error}</div>
          </CardContent>
        </Card>
      ) : application ? (
        <div className="mt-4 space-y-6">
          {/* Application header with summary */}
          <Card>
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex flex-col">
                  <div className="flex items-center mb-2">
                    <User className="h-5 w-5 mr-2 text-slate-500" />
                    <h3 className="font-medium">Candidate</h3>
                  </div>
                  <p>{application.fullName}</p>
                  <a href={`mailto:${application.email}`} className="text-blue-600 hover:underline">
                    {application.email}
                  </a>
                  {application.phoneNumber && (
                    <p className="text-slate-600">{application.phoneNumber}</p>
                  )}
                </div>

                <div className="flex flex-col">
                  <div className="flex items-center mb-2">
                    <Briefcase className="h-5 w-5 mr-2 text-slate-500" />
                    <h3 className="font-medium">Role</h3>
                  </div>
                  <p>{application.roleName || 'Unknown Role'}</p>
                  <p className="text-slate-600">
                    Applied on: {formatCreatedDate(application.created_at)}
                  </p>
                  <div className="mt-1">
                    <Badge variant="outline">{application.status || 'pending'}</Badge>
                  </div>
                </div>

                <div className="flex flex-col">
                  <div className="flex items-center mb-3">
                    <BarChart className="h-5 w-5 mr-2 text-slate-500" />
                    <h3 className="font-medium">Evaluation</h3>
                  </div>

                  {application.evaluationData ? (
                    <div>
                      {/* Interview Evaluation Section */}
                      {application.interviews && application.interviews.length > 0 && (
                        <div className="mb-3 pb-3 border-b border-slate-200 dark:border-slate-700">
                          <div className="flex items-center justify-between mb-1">
                            <p className="text-sm font-medium text-slate-700 dark:text-slate-300">
                              {application.interviews[0].stage_name || application.interviews[0].stageName || (application.interviews[0].stageIndex === 0 ? 'Screening' : application.interviewStage) || 'Interview'} Evaluation
                            </p>
                            {application.interviews[0].decision ? (
                              <Badge
                                className={cn(
                                  "text-white text-xs",
                                  application.interviews[0].decision === 'Go' || application.interviews[0].decision === 'PASS' ?
                                  "bg-green-500" :
                                  application.interviews[0].decision === 'No Go' || application.interviews[0].decision === 'FAIL' ?
                                  "bg-red-500" :
                                  "bg-amber-500"
                                )}
                              >
                                {application.interviews[0].decision}
                              </Badge>
                            ) : (
                              <Badge
                                className="bg-slate-400 text-white text-xs"
                              >
                                Pending
                              </Badge>
                            )}
                          </div>

                          {/* Score visualization for interview evaluation */}
                          {application.interviews[0].score || application.interviews[0].overall_score || application.interviews[0].overallScore ? (
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                {/* Score circle visualization */}
                                <div className="relative">
                                  <div className="w-12 h-12 rounded-full border-4 border-slate-200 dark:border-slate-700 flex items-center justify-center relative">
                                    {/* Score value */}
                                    <span className="text-sm font-bold text-slate-800 dark:text-slate-200">
                                      {application.interviews[0].score ||
                                       application.interviews[0].overall_score ||
                                       application.interviews[0].overallScore}
                                    </span>

                                    {/* Score fill background */}
                                    <div className="absolute inset-0 rounded-full overflow-hidden">
                                      <div
                                        className={cn(
                                          "absolute bottom-0 left-0 right-0",
                                          application.interviews[0].decision === 'Go' || application.interviews[0].decision === 'PASS' ?
                                          "bg-green-500" :
                                          application.interviews[0].decision === 'No Go' || application.interviews[0].decision === 'FAIL' ?
                                          "bg-red-500" :
                                          "bg-blue-500"
                                        )}
                                        style={{
                                          height: `${Math.min(100, Math.max(0,
                                            (parseFloat(String(application.interviews[0].score ||
                                                        application.interviews[0].overall_score ||
                                                        application.interviews[0].overallScore || 0)) / 100) * 100
                                          ))}%`,
                                          opacity: '0.3'
                                        }}
                                      ></div>
                                    </div>
                                  </div>

                                  {/* Confidence indicator */}
                                  {application.interviews[0].confidence && (
                                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
                                      <span className="text-xs text-slate-500 bg-white dark:bg-slate-800 px-1 rounded-sm">
                                        {application.interviews[0].confidence}
                                      </span>
                                    </div>
                                  )}
                                </div>

                                {/* Score description */}
                                <div>
                                  <p className="text-sm text-slate-700 dark:text-slate-300">
                                    {application.interviews[0].decision === 'Go' || application.interviews[0].decision === 'PASS' ?
                                      'Strong performance' :
                                      application.interviews[0].decision === 'No Go' || application.interviews[0].decision === 'FAIL' ?
                                      'Needs improvement' :
                                      'Mixed results'
                                    }
                                  </p>
                                  <p className="text-xs text-slate-500">
                                    out of 100
                                  </p>
                                </div>
                              </div>

                              {/* View report button */}
                              {application.interviews[0].evaluation_id || application.interviews[0].evaluationId ? (
                                <Link
                                  href={`/evaluation/${application.interviews[0].evaluation_id || application.interviews[0].evaluationId}`}
                                  className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1 transition-colors"
                                >
                                  <Award className="h-3.5 w-3.5" />
                                  View Report
                                </Link>
                              ) : (
                                <span className="text-xs text-slate-500 flex items-center gap-1">
                                  <Award className="h-3.5 w-3.5" />
                                  No Evaluation
                                </span>
                              )}
                            </div>
                          ) : (
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-slate-500">Score not available</span>

                              <span className="text-xs text-slate-500 flex items-center gap-1">
                                <Award className="h-3.5 w-3.5" />
                                No Evaluation
                              </span>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Resume Evaluation Section */}
                      <div>
                        <p className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                          Resume Evaluation
                        </p>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            {application.evaluationData.overallScore !== undefined && (
                              <span className="text-sm font-medium mr-1">
                                {typeof application.evaluationData.overallScore === 'number'
                                  ? `${application.evaluationData.overallScore.toFixed(1)}/5`
                                  : application.evaluationData.overallScore}
                              </span>
                            )}
                            {application.evaluationData.confidence && (
                              <span className="text-xs text-slate-500">
                                ({application.evaluationData.confidence} confidence)
                              </span>
                            )}
                          </div>

                          {application.evaluationData.decision && (
                            <Badge
                              className={cn(
                                "text-white text-xs",
                                application.evaluationData.decision === 'PASS' ? 'bg-green-500' : 'bg-red-500'
                              )}
                            >
                              {application.evaluationData.decision}
                            </Badge>
                          )}
                        </div>

                        {application.evaluationData.keyStrengths && Array.isArray(application.evaluationData.keyStrengths) &&
                         application.evaluationData.keyStrengths.length > 0 && (
                          <p className="text-xs text-slate-600 mt-2">
                            <span className="font-medium">Key strength:</span> {application.evaluationData.keyStrengths[0]}
                          </p>
                        )}

                        {application.evaluationData.keyGaps && Array.isArray(application.evaluationData.keyGaps) &&
                         application.evaluationData.keyGaps.length > 0 && (
                          <p className="text-xs text-slate-600 mt-1">
                            <span className="font-medium">Key gap:</span> {application.evaluationData.keyGaps[0]}
                          </p>
                        )}
                      </div>
                    </div>
                  ) : (
                    <p className="text-slate-600">Evaluation pending</p>
                  )}
                </div>
              </div>

              {application.resumeUrl && (
                <div className="mt-6 pt-6 border-t">
                  <a
                    href={application.resumeUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 hover:text-blue-800"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    <span>View Resume</span>
                  </a>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tabs for different sections */}
          <Tabs defaultValue="evaluation" className="w-full">
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="evaluation">Resume Evaluation</TabsTrigger>
              <TabsTrigger value="interviews">Interviews</TabsTrigger>
            </TabsList>

            {/* Resume Evaluation Tab */}
            <TabsContent value="evaluation">
              {application.evaluationData ? (
                <Card>
                  <CardHeader>
                    <CardTitle>Resume Evaluation</CardTitle>
                    <CardDescription>
                      Evaluation of candidate&apos;s resume against role requirements
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Overall Assessment */}
                      <div className="bg-white dark:bg-slate-800 rounded-lg p-6 border shadow-sm">
                        <h3 className="font-medium text-lg mb-5 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                            <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                          </svg>
                          Overall Assessment
                        </h3>

                        <div className="bg-slate-50 dark:bg-slate-900 rounded-lg p-5 border border-slate-200 dark:border-slate-700">
                          <div className="grid grid-cols-1 md:grid-cols-12 gap-6 items-start">
                            {/* Score Visualization */}
                            <div className="relative flex items-center justify-center md:col-span-3">
                              {application.evaluationData.confidence && (
                                <div className="absolute -top-2 z-10 text-center w-full">
                                  <p className="text-xs text-slate-500 dark:text-slate-400 bg-white dark:bg-slate-800 px-2 py-0.5 inline-block rounded-full shadow-sm">
                                    with <span className={cn(
                                      "font-medium",
                                      application.evaluationData.confidence === 'HIGH'
                                        ? 'text-green-600 dark:text-green-400'
                                        : application.evaluationData.confidence === 'MEDIUM'
                                          ? 'text-blue-600 dark:text-blue-400'
                                          : 'text-amber-600 dark:text-amber-400'
                                    )}>{application.evaluationData.confidence}</span> confidence
                                  </p>
                                </div>
                              )}
                              <div className="w-28 h-28 rounded-full border-8 border-slate-200 dark:border-slate-700 flex items-center justify-center relative mt-6">
                                {application.evaluationData.overallScore !== undefined && (
                                  <>
                                    <div
                                      className={cn(
                                        "absolute inset-0 rounded-full",
                                        typeof application.evaluationData.overallScore === 'number' && application.evaluationData.overallScore >= 4
                                          ? 'bg-green-100 dark:bg-green-900/30 border-8 border-green-200 dark:border-green-800/30'
                                          : typeof application.evaluationData.overallScore === 'number' && application.evaluationData.overallScore >= 3
                                            ? 'bg-blue-100 dark:bg-blue-900/30 border-8 border-blue-200 dark:border-blue-800/30'
                                            : 'bg-amber-100 dark:bg-amber-900/30 border-8 border-amber-200 dark:border-amber-800/30'
                                      )}
                                      style={{ opacity: 0.7 }}
                                    ></div>
                                    <div className="z-10 text-center">
                                      <p className="text-3xl font-bold text-slate-800 dark:text-slate-200">
                                        {typeof application.evaluationData.overallScore === 'number'
                                          ? application.evaluationData.overallScore.toFixed(1)
                                          : application.evaluationData.overallScore}
                                      </p>
                                      <p className="text-xs text-slate-500">out of 5</p>
                                    </div>
                                  </>
                                )}
                              </div>
                              {application.evaluationData.decision && (
                                <div className="absolute -bottom-2">
                                  <Badge
                                    className={cn(
                                      "text-white px-3 py-0.5 text-xs shadow-sm",
                                      application.evaluationData.decision === 'PASS'
                                        ? 'bg-green-500 hover:bg-green-500'
                                        : 'bg-red-500 hover:bg-red-500'
                                    )}
                                  >
                                    {application.evaluationData.decision}
                                  </Badge>
                                </div>
                              )}
                            </div>

                            {/* Reasoning Summary */}
                            <div className="text-center md:text-left md:col-span-4">
                              <p className="text-slate-500 mb-2 text-sm font-medium">Summary</p>
                              {application.evaluationData.reasoning ? (
                                <p className="text-sm text-slate-700 dark:text-slate-300 line-clamp-4">
                                  {application.evaluationData.reasoning.split('.')[0]}.
                                </p>
                              ) : (
                                <p className="text-sm text-slate-500 italic">
                                  No summary available
                                </p>
                              )}
                            </div>

                            {/* Strengths and Gaps */}
                            <div className="text-center md:text-left md:col-span-5">
                              {application.evaluationData.keyStrengths && Array.isArray(application.evaluationData.keyStrengths) &&
                               application.evaluationData.keyStrengths.length > 0 && (
                                <div className="mb-4">
                                  <p className="text-slate-500 mb-1 text-sm font-medium">Top Strength</p>
                                  <p className="text-sm text-slate-700 dark:text-slate-300 flex items-start">
                                    <span className="text-green-500 mr-1.5 mt-0.5">•</span>
                                    <span className="line-clamp-2">{application.evaluationData.keyStrengths[0]}</span>
                                  </p>
                                </div>
                              )}

                              {application.evaluationData.keyGaps && Array.isArray(application.evaluationData.keyGaps) &&
                               application.evaluationData.keyGaps.length > 0 && (
                                <div>
                                  <p className="text-slate-500 mb-1 text-sm font-medium">Top Gap</p>
                                  <p className="text-sm text-slate-700 dark:text-slate-300 flex items-start">
                                    <span className="text-amber-500 mr-1.5 mt-0.5">•</span>
                                    <span className="line-clamp-2">{application.evaluationData.keyGaps[0]}</span>
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {application.evaluationData.reasoning && (
                          <div className="mt-5 p-4 bg-slate-50 dark:bg-slate-900 rounded-md border border-slate-200 dark:border-slate-700">
                            <p className="text-sm text-slate-700 dark:text-slate-300 leading-relaxed">
                              {application.evaluationData.reasoning}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Strengths and Gaps */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-white dark:bg-slate-800 border rounded-lg p-6 shadow-sm">
                          <h3 className="font-medium text-lg mb-4 flex items-center text-green-600 dark:text-green-500">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            Key Strengths
                          </h3>
                          {application.evaluationData.keyStrengths && Array.isArray(application.evaluationData.keyStrengths) && application.evaluationData.keyStrengths.length > 0 ? (
                            <ul className="space-y-3 bg-slate-50 dark:bg-slate-900 p-4 rounded-md border border-slate-200 dark:border-slate-700">
                              {application.evaluationData.keyStrengths.map((strength: string, i: number) => (
                                <li key={i} className="flex items-start">
                                  <span className="text-green-500 mr-2 mt-0.5">•</span>
                                  <span className="text-slate-700 dark:text-slate-300">{strength}</span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <div className="bg-slate-50 dark:bg-slate-900 p-4 rounded-md border border-slate-200 dark:border-slate-700">
                              <p className="text-slate-500 italic">No strengths identified</p>
                            </div>
                          )}
                        </div>
                        <div className="bg-white dark:bg-slate-800 border rounded-lg p-6 shadow-sm">
                          <h3 className="font-medium text-lg mb-4 flex items-center text-amber-600 dark:text-amber-500">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                            Gaps
                          </h3>
                          {application.evaluationData.keyGaps && Array.isArray(application.evaluationData.keyGaps) && application.evaluationData.keyGaps.length > 0 ? (
                            <ul className="space-y-3 bg-slate-50 dark:bg-slate-900 p-4 rounded-md border border-slate-200 dark:border-slate-700">
                              {application.evaluationData.keyGaps.map((gap: string, i: number) => (
                                <li key={i} className="flex items-start">
                                  <span className="text-amber-500 mr-2 mt-0.5">•</span>
                                  <span className="text-slate-700 dark:text-slate-300">{gap}</span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <div className="bg-slate-50 dark:bg-slate-900 p-4 rounded-md border border-slate-200 dark:border-slate-700">
                              <p className="text-slate-500 italic">No gaps identified</p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Resume Text - Moved to the end */}
                      {application.evaluationData?.metadata?.parsedText ? (
                        <div className="bg-white dark:bg-slate-800 border rounded-lg p-6 mt-6 shadow-sm">
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="font-medium text-lg flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                              </svg>
                              Resume Content
                            </h3>
                            {application.evaluationData?.metadata?.filename && (
                              <Badge variant="outline" className="ml-2 bg-slate-100 dark:bg-slate-800">
                                {application.evaluationData?.metadata?.filename}
                              </Badge>
                            )}
                          </div>
                          <div className="max-h-80 overflow-y-auto bg-slate-50 dark:bg-slate-900 p-4 rounded-md border border-slate-200 dark:border-slate-700 text-sm">
                            <p className="whitespace-pre-wrap text-slate-700 dark:text-slate-300 leading-relaxed">{application.evaluationData?.metadata?.parsedText}</p>
                          </div>
                        </div>
                      ) : application?.resumeText ? (
                        <div className="bg-white dark:bg-slate-800 border rounded-lg p-6 mt-6 shadow-sm">
                          <div className="flex justify-between items-center mb-4">
                            <h3 className="font-medium text-lg flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                              </svg>
                              Resume Content
                            </h3>
                          </div>
                          <div className="max-h-80 overflow-y-auto bg-slate-50 dark:bg-slate-900 p-4 rounded-md border border-slate-200 dark:border-slate-700 text-sm">
                            <p className="whitespace-pre-wrap text-slate-700 dark:text-slate-300 leading-relaxed">{application?.resumeText}</p>
                          </div>
                        </div>
                      ) : null}

                      {/* Evaluation Metadata */}
                      {application.evaluationData?.metadata && (
                        <div className="bg-white dark:bg-slate-800 border rounded-lg p-4 mt-6 shadow-sm">
                          <h3 className="text-sm font-medium text-slate-500 mb-3 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                            </svg>
                            Evaluation Details
                          </h3>
                          <div className="flex flex-wrap gap-2">
                            {application.evaluationData?.metadata?.model && (
                              <Badge variant="outline" className="text-xs bg-slate-100 dark:bg-slate-800">
                                Model: {application.evaluationData?.metadata?.model}
                              </Badge>
                            )}
                            {application.evaluationData?.metadata?.evaluationType && (
                              <Badge variant="outline" className="text-xs bg-slate-100 dark:bg-slate-800">
                                Type: {application.evaluationData?.metadata?.evaluationType}
                              </Badge>
                            )}
                            {application.evaluationData?.metadata?.evaluation_type && (
                              <Badge variant="outline" className="text-xs bg-slate-100 dark:bg-slate-800">
                                Type: {application.evaluationData?.metadata?.evaluation_type}
                              </Badge>
                            )}
                            {application.evaluationData?.metadata?.timestamp && (
                              <Badge variant="outline" className="text-xs bg-slate-100 dark:bg-slate-800">
                                {new Date(application.evaluationData?.metadata?.timestamp || 0).toLocaleString()}
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card className="border shadow-sm">
                  <CardContent className="pt-6">
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <div className="bg-slate-50 dark:bg-slate-900 p-4 rounded-full mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-2">
                        No Evaluation Data Available
                      </h3>
                      <p className="text-slate-500 mb-4 max-w-md">
                        Resume evaluations are generated when candidates submit their applications.
                      </p>
                      <div className="flex justify-center">
                        <Badge variant="outline" className="bg-slate-100 dark:bg-slate-800">
                          Application ID: {params.id}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Interviews Tab */}
            <TabsContent value="interviews">
              <Card>
                <CardHeader>
                  <CardTitle>Interview Sessions</CardTitle>
                  <CardDescription>
                    Interview sessions conducted with this candidate
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {application?.interviews && application.interviews.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-1 gap-4">
                      {application?.interviews?.map((interview, index) => {
                        // Calculate interview duration if start and end times are available
                        let duration: string | null = null;
                        if (interview.start_time && interview.end_time) {
                          const start = new Date(interview.start_time);
                          const end = new Date(interview.end_time);
                          if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
                            const durationMs = end.getTime() - start.getTime();
                            const minutes = Math.floor(durationMs / 60000);
                            duration = `${minutes} minutes`;
                          }
                        }

                        // Count number of questions if transcript is available
                        let questionCount = 0;
                        if (interview.transcript && interview.transcript.messages) {
                          // Count messages from the system/assistant that appear to be questions
                          questionCount = interview.transcript.messages.filter((msg: any) =>
                            (msg.role === 'assistant' || msg.role === 'system') &&
                            (msg.content.trim().endsWith('?') ||
                             msg.content.includes('?\n') ||
                             msg.type === 'question')
                          ).length;
                        }

                        // Get template information
                        const templateName = interview.template_name || interview.templateName || 'Interview';
                        const templateId = interview.template_id || interview.templateId;

                        // For the first interview (index 0), always use 'Screening' as the stage name if no stage name is available
                        // This is a direct fix for the specific case mentioned by the user
                        let stageName: string;
                        if (index === 0 && (!interview.stage_name && !interview.stageName)) {
                          stageName = 'Screening';
                          // Update the interview object for future reference
                          interview.stage_name = 'Screening';
                          interview.stageName = 'Screening';
                          console.log('Setting stage name to Screening for first interview');
                        } else {
                          stageName = interview.stage_name || interview.stageName || application.interviewStage || templateName || 'Interview';
                        }

                        // Get the evaluation ID from the interview data without any fallback
                        const evaluationId = interview.evaluation_id || interview.evaluationId;
                        // Log for debugging purposes
                        console.log(`Interview ${interview.id} evaluation ID: ${evaluationId || 'None'}`);
                        // All necessary variables have been defined

                        return (
                          <Card key={interview.id || index} className="relative h-full flex flex-col bg-white/80 backdrop-blur-sm border-slate-200/70 shadow-lg shadow-slate-200/30 dark:bg-slate-800/60 dark:border-slate-700/40 dark:backdrop-blur-[6px] dark:backdrop-saturate-[1.4] dark:shadow-xl dark:shadow-slate-950/20 transition-all duration-300 ease-out hover:bg-white hover:border-slate-300 hover:shadow-xl hover:shadow-slate-300/40 dark:hover:bg-slate-800/70 dark:hover:border-slate-700/60 dark:hover:shadow-xl dark:hover:shadow-slate-950/30 hover:translate-y-[-2px]">
                            <CardHeader className="pb-3 sm:pb-4 flex flex-row items-center justify-between">
                              <div className="space-y-1">
                                <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 flex items-center gap-1 group cursor-pointer">
                                  {stageName}
                                </h3>
                                <p className="text-xs text-slate-500">
                                  {interview.created_at ? formatCreatedDate(interview.created_at) : 'No date'}
                                </p>
                              </div>
                              {interview.decision ? (
                                <Badge
                                  className={`text-white text-xs px-2 py-1 rounded-md ${
                                    interview.decision === 'Go' || interview.decision === 'PASS' ?
                                    'bg-green-500' :
                                    interview.decision === 'No Go' || interview.decision === 'FAIL' ?
                                    'bg-red-500' :
                                    'bg-amber-500'
                                  }`}
                                >
                                  {interview.decision}
                                </Badge>
                              ) : (
                                <Badge
                                  className="bg-slate-400 text-white text-xs px-2 py-1 rounded-md"
                                >
                                  Pending
                                </Badge>
                              )}
                            </CardHeader>

                            <CardContent className="pt-0 flex-1 flex flex-col">
                              {/* Interview Details */}
                              <div className="grid grid-cols-2 gap-4 mb-4 text-sm text-slate-700 dark:text-slate-300">
                                {/* Template */}
                                <div className="flex items-center gap-1.5">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                                  </svg>
                                  <span className="truncate" title={templateName}>{templateName}</span>
                                </div>

                                {/* Duration */}
                                <div className="flex items-center gap-1.5">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                  </svg>
                                  <span className="truncate">{duration || 'Not available'}</span>
                                </div>
                              </div>

                              <div className="grid grid-cols-2 gap-4 mb-4 text-sm text-slate-700 dark:text-slate-300">
                                {/* Score visualization */}
                                <div className="flex items-center gap-2">
                                  {interview.score || interview.overall_score || interview.overallScore ? (
                                    <>
                                      {/* Score circle visualization */}
                                      <div className="relative">
                                        <div className="w-8 h-8 rounded-full border-3 border-slate-200 dark:border-slate-700 flex items-center justify-center relative">
                                          {/* Score value */}
                                          <span className="text-xs font-bold text-slate-800 dark:text-slate-200">
                                            {interview.score || interview.overall_score || interview.overallScore}
                                          </span>

                                          {/* Score fill background */}
                                          <div className="absolute inset-0 rounded-full overflow-hidden">
                                            <div
                                              className={cn(
                                                "absolute bottom-0 left-0 right-0",
                                                interview.decision === 'Go' || interview.decision === 'PASS' ?
                                                "bg-green-500" :
                                                interview.decision === 'No Go' || interview.decision === 'FAIL' ?
                                                "bg-red-500" :
                                                "bg-blue-500"
                                              )}
                                              style={{
                                                height: `${Math.min(100, Math.max(0,
                                                  (parseFloat(String(interview.score ||
                                                              interview.overall_score ||
                                                              interview.overallScore || 0)) / 100) * 100
                                                ))}%`,
                                                opacity: '0.3'
                                              }}
                                            ></div>
                                          </div>
                                        </div>
                                      </div>
                                      <span className="truncate">
                                        Score: {interview.score || interview.overall_score || interview.overallScore}/100
                                      </span>
                                    </>
                                  ) : (
                                    <>
                                      <Award className="h-4 w-4 text-slate-400 flex-shrink-0" />
                                      <span className="truncate">Score: N/A</span>
                                    </>
                                  )}
                                </div>

                                {/* Questions */}
                                <div className="flex items-center gap-1.5">
                                  <MessageSquare className="h-4 w-4 text-slate-400 flex-shrink-0" />
                                  <span className="truncate">{questionCount > 0 ? `${questionCount} questions` : 'Not available'}</span>
                                </div>
                              </div>

                              {/* Status */}
                              <div className="flex items-center gap-1.5 mb-4 text-sm text-slate-700 dark:text-slate-300">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                </svg>
                                <span className="truncate">{interview.status || 'Completed'}</span>
                              </div>

                              {/* Action Buttons */}
                              <div className="mt-auto pt-4 border-t border-slate-200 dark:border-slate-700/40 flex flex-wrap gap-2">
                                {/* Evaluation Buttons - Only show when evaluation ID exists */}
                                {evaluationId ? (
                                  <>
                                    <Button
                                      variant="secondary"
                                      size="sm"
                                      className="text-xs flex items-center gap-1 shadow-sm hover:shadow-md transition-all duration-300 flex-1"
                                      onClick={() => {
                                        console.log('Showing evaluation:', evaluationId);
                                        showEvaluation(evaluationId);
                                      }}
                                    >
                                      <Award className="h-3.5 w-3.5 mr-1" />
                                      Quick View
                                    </Button>

                                    <Button
                                      variant="secondary"
                                      size="sm"
                                      className="text-xs flex items-center gap-1 shadow-sm hover:shadow-md transition-all duration-300 bg-white dark:bg-slate-900 flex-1"
                                      asChild
                                    >
                                      <Link href={`/evaluation/${evaluationId}`}>
                                        <Award className="h-3.5 w-3.5 mr-1" />
                                        Full Report
                                      </Link>
                                    </Button>
                                  </>
                                ) : (
                                  <Button
                                    variant="secondary"
                                    size="sm"
                                    className="text-xs flex items-center gap-1 shadow-sm transition-all duration-300 flex-1"
                                    disabled
                                  >
                                    <Award className="h-3.5 w-3.5 mr-1" />
                                    No Evaluation Available
                                  </Button>
                                )}
                              </div>

                              <div className="flex flex-wrap gap-2 mt-2">
                                {/* Transcript Button */}
                                <Button
                                  variant="secondary"
                                  size="sm"
                                  className="text-xs flex items-center gap-1 shadow-sm hover:shadow-md transition-all duration-300 flex-1"
                                  onClick={() => showTranscript(
                                    interview.transcript || { id: interview.transcript_id },
                                    interview.id || `interview-${index}`,
                                    stageName
                                  )}
                                >
                                  <MessageSquare className="h-3.5 w-3.5 mr-1" />
                                  View Transcript
                                </Button>

                                {/* Template Link */}
                                {templateId && (
                                  <Button
                                    variant="secondary"
                                    size="sm"
                                    className="text-xs flex items-center gap-1 shadow-sm hover:shadow-md transition-all duration-300 flex-1"
                                    asChild
                                  >
                                    <Link href={`/roles/templates/${templateId}`}>
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                                      </svg>
                                      View Template
                                    </Link>
                                  </Button>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  ) : application.interviewStage ? (
                    <div className="bg-white/80 dark:bg-slate-800/60 backdrop-blur-[2px] border border-slate-200/80 dark:border-slate-700/60 rounded-lg shadow-sm p-8 transition-all duration-300 hover:shadow-md">
                      <div className="flex flex-col items-center justify-center text-center">
                        <div className="bg-slate-50 dark:bg-slate-900/80 p-5 rounded-full mb-5 shadow-inner">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                        </div>
                        <h3 className="font-medium text-lg mb-2 text-slate-800 dark:text-slate-200">{application.interviewStage}</h3>
                        <p className="text-slate-600 dark:text-slate-400 mb-5 max-w-md">
                          This candidate is in the <span className="font-medium text-slate-700 dark:text-slate-300">{application.interviewStage}</span> stage, but no interview sessions have been conducted yet.
                        </p>
                        {application.interviewTranscriptId && (
                          <Badge variant="outline" className="bg-slate-100/80 dark:bg-slate-800/80 shadow-sm">
                            Transcript ID: {application.interviewTranscriptId}
                          </Badge>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="bg-white/80 dark:bg-slate-800/60 backdrop-blur-[2px] border border-slate-200/80 dark:border-slate-700/60 rounded-lg shadow-sm p-8 transition-all duration-300 hover:shadow-md">
                      <div className="flex flex-col items-center justify-center text-center">
                        <div className="bg-slate-50 dark:bg-slate-900/80 p-5 rounded-full mb-5 shadow-inner">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <h3 className="font-medium text-lg mb-2 text-slate-800 dark:text-slate-200">No Interview Data</h3>
                        <p className="text-slate-600 dark:text-slate-400 max-w-md">
                          No interview sessions have been conducted with this candidate yet. Interview data will appear here once sessions are completed.
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      ) : null}
    </DashboardLayout>
  );
}