'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ArrowLeft, FileText } from 'lucide-react';
import { evaluationService } from '@/services/evaluation';
import { ExtendedEvaluation } from '@/types/evaluation';
import { useToast } from '@/components/ui/use-toast';

import {
  SummarySection,
  ScorecardSection,
  QuestionAnalysisSection,
  BetweenTheLinesSection,
  DisqualifierSection,
  DecisionReasoningSection
} from '@/components/evaluation/EvaluationReportSections';

// Import types from the types file instead of defining them here

/**
 * EvaluationReportPage component displays a full evaluation report
 */
export default function EvaluationReportPage({ params }: { params: { id: string } }) {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [evaluation, setEvaluation] = useState<ExtendedEvaluation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  // Check authentication
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/signin');
      return;
    }
  }, [user, router, authLoading]);

  // Load evaluation data
  useEffect(() => {
    const loadEvaluation = async () => {
      if (!user || !params.id) return;

      try {
        setLoading(true);
        setError(null);

        // Clear any cached evaluation data first
        evaluationService.clearCache(params.id);

        // Always force a refresh to avoid caching issues
        console.log(`Loading evaluation ${params.id} for full report page`);
        const data = await evaluationService.getEvaluation(params.id, true);

        if (!data) {
          throw new Error('Evaluation not found');
        }

        // Map the API response to the ExtendedEvaluation type
        const evaluationData: ExtendedEvaluation = {
          id: params.id,
          createdAt: new Date(),
          status: data.status || 'completed',
          // Use the correct data path based on the API response
          data: (data as any).evaluation?.data || (data as any).evaluation || data.data || data
        };

        console.log(`Evaluation data loaded for ${params.id}:`, evaluationData);
        setEvaluation(evaluationData);
      } catch (err) {
        console.error(`Error loading evaluation ${params.id}:`, err);
        setError(err instanceof Error ? err : new Error(String(err)));
        toast({
          title: 'Error',
          description: 'Failed to load evaluation data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    if (user && params.id) {
      loadEvaluation();
    }
  }, [user, params.id, toast]);

  // Show loading state while auth is being checked
  if (authLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-screen">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  // Don't render anything if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <DashboardLayout>
      <div>
        <Button
          variant="ghost"
          className="mb-4"
          onClick={() => router.back()}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center my-8">
          <LoadingSpinner size="lg" />
        </div>
      ) : error ? (
        <Card className="mt-4">
          <CardContent className="pt-6">
            <div className="text-red-500 p-4">{error.message}</div>
          </CardContent>
        </Card>
      ) : evaluation ? (
        <div className="space-y-6">
          {/* Summary Section */}
          <Card>
            <CardContent className="p-6">
              <SummarySection summary={evaluation.data?.evaluation_summary || {
                candidate_name: 'Candidate',
                role: evaluation.data?.metadata?.role_id,
                decision: evaluation.data?.decision || evaluation.data?.evaluation_summary?.decision,
                overall_score: evaluation.data?.overallScore || evaluation.data?.evaluation_summary?.overall_score,
                summary: evaluation.data?.summary || evaluation.data?.evaluation_summary?.summary,
              }} />
            </CardContent>
          </Card>

          {/* Decision Reasoning Section */}
          {(evaluation.data?.decision_reasoning || evaluation.data?.decision) && (
            <Card>
              <CardContent className="p-6">
                <DecisionReasoningSection data={evaluation.data?.decision_reasoning || {
                  final_recommendation: evaluation.data?.summary,
                  key_factors: [],
                  strengths: evaluation.data?.strengths || [],
                  concerns: evaluation.data?.weaknesses || []
                }} />
              </CardContent>
            </Card>
          )}

          {/* Scorecard Section */}
          {(evaluation.data?.scorecard_evaluation || evaluation.data?.criteria) && (
            <Card>
              <CardContent className="p-6">
                <ScorecardSection items={evaluation.data?.scorecard_evaluation ||
                  (evaluation.data?.criteria?.map(c => ({
                    competency: c.name || 'Unnamed Criterion',
                    reasoning: c.feedback || 'No feedback provided',
                    score: typeof c.score === 'number' ? c.score : 0,
                    weight: 1 / (evaluation.data?.criteria?.length || 1),
                    weighted_score: (typeof c.score === 'number' ? c.score : 0) * (1 / (evaluation.data?.criteria?.length || 1))
                  })) || [])
                } />
              </CardContent>
            </Card>
          )}

          {/* Question Analysis Section */}
          {(evaluation.data?.question_analysis || evaluation.data?.questionAssessments) && (
            <Card>
              <CardContent className="p-6">
                <QuestionAnalysisSection items={evaluation.data?.question_analysis ||
                  (evaluation.data?.questionAssessments?.map(qa => ({
                    question: qa.question || 'Unnamed Question',
                    answer: qa.answer || 'No answer provided',
                    evaluation: qa.feedback || 'No feedback provided',
                    related_competencies: [],
                    strengths: [],
                    weaknesses: []
                  })) || [])
                } />
              </CardContent>
            </Card>
          )}

          {/* Between the Lines Section */}
          {(evaluation.data?.between_the_lines || (evaluation.data?.strengths && evaluation.data?.strengths.length > 0)) && (
            <Card>
              <CardContent className="p-6">
                <BetweenTheLinesSection items={evaluation.data?.between_the_lines ||
                  [
                    ...(evaluation.data?.strengths?.map(s => ({
                      criteria: 'Strength',
                      observation: s,
                      impact: 'Positive impact on candidate evaluation'
                    })) || []),
                    ...(evaluation.data?.weaknesses?.map(w => ({
                      criteria: 'Area for Improvement',
                      observation: w,
                      impact: 'Area that could be improved'
                    })) || [])
                  ]
                } />
              </CardContent>
            </Card>
          )}

          {/* Disqualifier Check Section */}
          {evaluation.data?.disqualifier_check && evaluation.data.disqualifier_check.length > 0 && (
            <Card>
              <CardContent className="p-6">
                <DisqualifierSection items={evaluation.data.disqualifier_check.map(item => ({
                  criteria: item.disqualifier || 'Disqualifier',
                  evidence: item.explanation || '',
                  explanation: item.explanation || '',
                  triggered: item.present || false
                }))} />
              </CardContent>
            </Card>
          )}

          {/* Metadata */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center text-slate-500 mb-3">
                <FileText className="h-5 w-5 mr-2" />
                <h3 className="font-medium">Metadata</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div className="flex">
                  <span className="text-slate-500 w-32">Evaluation ID:</span>
                  <span className="text-slate-700 dark:text-slate-300 font-mono">{evaluation.id}</span>
                </div>
                {evaluation.data?.metadata?.role_id && (
                  <div className="flex">
                    <span className="text-slate-500 w-32">Role ID:</span>
                    <span className="text-slate-700 dark:text-slate-300 font-mono">{evaluation.data.metadata.role_id}</span>
                  </div>
                )}
                {evaluation.data?.metadata?.application_id && (
                  <div className="flex">
                    <span className="text-slate-500 w-32">Application ID:</span>
                    <span className="text-slate-700 dark:text-slate-300 font-mono">{evaluation.data.metadata.application_id}</span>
                  </div>
                )}
                {evaluation.data?.metadata?.interview_id && (
                  <div className="flex">
                    <span className="text-slate-500 w-32">Interview ID:</span>
                    <span className="text-slate-700 dark:text-slate-300 font-mono">{evaluation.data.metadata.interview_id}</span>
                  </div>
                )}
                {evaluation.data?.metadata?.evaluated_at && (
                  <div className="flex">
                    <span className="text-slate-500 w-32">Evaluated At:</span>
                    <span className="text-slate-700 dark:text-slate-300">{new Date(evaluation.data.metadata.evaluated_at).toLocaleString()}</span>
                  </div>
                )}
                {evaluation.data?.metadata?.evaluated_by && (
                  <div className="flex">
                    <span className="text-slate-500 w-32">Evaluated By:</span>
                    <span className="text-slate-700 dark:text-slate-300">{evaluation.data.metadata.evaluated_by}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      ) : null}
    </DashboardLayout>
  );
}

// Components moved to EvaluationReportSections.tsx
