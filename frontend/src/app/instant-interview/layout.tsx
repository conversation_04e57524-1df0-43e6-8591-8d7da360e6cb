'use client';

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils/styles';
import { ThemeToggle } from '@/components/ui/ThemeToggle';

/**
 * InstantInterview layout component
 * Provides a consistent background for all instant interview pages
 * Matches the style of the job board with a dark gray mixed background
 */
export default function InstantInterviewLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Determine if we're in dark mode - use resolved theme to account for system preference
  const isDarkMode = mounted && (resolvedTheme === 'dark' || theme === 'dark');

  if (!mounted) {
    // During SSR or before hydration, default to no specific classes to avoid flicker
    return <div className="min-h-screen">{children}</div>;
  }

  return (
    <div className={cn(
      "min-h-screen transition-colors duration-300 relative",
      // Apply theme-specific backgrounds
      isDarkMode 
        ? "bg-background" 
        : "bg-gradient-to-br from-orange-50/80 via-pink-50/70 to-purple-50/80"
    )}>
      {children}
      
      {/* Fixed theme toggle button in bottom right */}
      <div className="fixed bottom-6 right-6 z-50">
        <div className="bg-card shadow-lg rounded-full p-2">
          <ThemeToggle />
        </div>
      </div>
    </div>
  );
} 