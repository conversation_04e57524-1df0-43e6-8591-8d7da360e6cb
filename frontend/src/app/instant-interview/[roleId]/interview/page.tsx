'use client';

import { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { toast } from 'sonner';
import { getApplication, updateApplicationWithTranscript } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/client';
import { realtimeApi } from '@/services/realtime';
import { Button } from '@/components/ui/Button';
import { Textarea } from '@/components/ui/Textarea';
import { useRouter } from 'next/navigation';
import { publicRolesService } from '@/services/roles/public-service';
import { DarkModeVideoCall } from '@/components/video-call/DarkModeVideoCall';
import { PermissionCheck } from '@/components/video-call/PermissionCheck';

interface InterviewPageProps {
  params: {
    roleId: string;
  };
}

// Add a TextBased Interview fallback component
const TextBasedInterview = ({ roleId, onSubmit }: { roleId: string, onSubmit: (answers: string[]) => void }) => {
  const [role, setRole] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [answers, setAnswers] = useState<{[key: string]: string}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [questions] = useState([
    { id: 'q1', question: 'Please introduce yourself and tell us about your relevant experience.' },
    { id: 'q2', question: 'What are your key strengths that make you suitable for this position?' },
    { id: 'q3', question: 'Why are you interested in this role?' },
    { id: 'q4', question: 'Describe a challenge you\'ve faced in your previous experience and how you overcame it.' },
    { id: 'q5', question: 'What are your expectations for this role and what would you hope to achieve if selected?' }
  ]);

  // Try to get the role info
  useEffect(() => {
    const fetchRole = async () => {
      try {
        const response = await fetch(`/api/v1/public/jobs/${roleId}?t=${Date.now()}`);
        if (response.ok) {
          const data = await response.json();
          setRole(data);
        }
      } catch (error) {
        console.error('Error fetching role info:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRole();
  }, [roleId]);

  const handleChange = (id: string, value: string) => {
    setAnswers(prev => ({ ...prev, [id]: value }));
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleSubmit = () => {
    setIsSubmitting(true);
    const allAnswers = questions.map(q => answers[q.id] || '');
    onSubmit(allAnswers);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner className="h-8 w-8" />
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const isComplete = Object.keys(answers).length >= questions.length;

  return (
    <Card className="max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="flex flex-col gap-1">
          <span className="text-2xl font-bold">{role?.title || 'Interview Questions'}</span>
          <span className="text-sm text-muted-foreground">
            Please answer the following questions to complete your interview
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-6">
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4 dark:bg-gray-700">
            <div className="bg-primary h-2.5 rounded-full" style={{ width: `${(currentQuestionIndex + 1) / questions.length * 100}%` }}></div>
          </div>
          <p className="text-sm text-center text-muted-foreground">Question {currentQuestionIndex + 1} of {questions.length}</p>
        </div>

        <div className="space-y-6">
          <div className="space-y-2">
            <h3 className="text-lg font-medium">{currentQuestion.question}</h3>
            <Textarea
              placeholder="Your answer..."
              className="min-h-[150px]"
              value={answers[currentQuestion.id] || ''}
              onChange={(e) => handleChange(currentQuestion.id, e.target.value)}
            />
          </div>

          <div className="flex justify-between mt-6">
            <Button
              variant="secondary"
              onClick={handlePreviousQuestion}
              disabled={currentQuestionIndex === 0}
            >
              Previous
            </Button>

            {currentQuestionIndex < questions.length - 1 ? (
              <Button
                onClick={handleNextQuestion}
                disabled={!answers[currentQuestion.id]}
              >
                Next
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || !isComplete}
              >
                {isSubmitting ? (
                  <>
                    <LoadingSpinner className="mr-2 h-4 w-4" />
                    Submitting...
                  </>
                ) : 'Submit Answers'}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * PublicInterviewPage component
 * Page for candidates to take their interview
 */
export default function PublicInterviewPage({ params }: InterviewPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const applicationId = searchParams?.get('applicationId');
  const roleId = params?.roleId;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionData, setSessionData] = useState<any>(null);
  const [connectionFailed, setConnectionFailed] = useState(false);
  const [connectionAttempts, setConnectionAttempts] = useState(0);
  const [permissionsGranted, setPermissionsGranted] = useState(false);

  // Add a function to verify the session token
  const verifySessionToken = async (sessionId: string, clientSecret: any) => {
    try {
      // Log the session data for debugging
      console.log("Verifying session token:", {
        sessionId: sessionId,
        secretLength: clientSecret ? clientSecret.length : 0,
        hasSecretValue: !!clientSecret
      });

      // Check if the token is expired
      if (typeof clientSecret === 'object' && clientSecret.expires_at) {
        const expiresAt = clientSecret.expires_at;
        const nowSeconds = Math.floor(Date.now() / 1000);
        const timeRemaining = expiresAt - nowSeconds;

        console.log(`Session token expires in ${timeRemaining} seconds`);

        if (timeRemaining < 30) {
          console.error('Session token will expire too soon:', {
            expiresAt,
            timeRemaining
          });
          return false;
        }
      } else if (typeof clientSecret === 'string' && clientSecret.length > 0) {
        // If it's just a string, make basic validation
        if (clientSecret.length < 10) {
          console.error('Session token appears too short');
          return false;
        }
      } else {
        console.error('Invalid client secret format');
        return false;
      }

      // We can't directly validate the session token with OpenAI's API
      // But we can check if it's a valid format (should have a valid session ID and non-empty client secret)
      if (!sessionId || sessionId.length < 10) {
        console.error('Session ID appears invalid');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error validating session token:', error);
      return false;
    }
  };

  // Modify the session creation process to include retries and check for existing sessions
  // Use a ref to track if a session creation is in progress to prevent duplicates
  const sessionCreationInProgress = useRef(false);

  const createPublicInterviewSession = async (
    retryCount = 0,
    templateId?: string,
    candidateId?: string,
    applicationId?: string,
    resumeText?: string,
    jobPosting?: string,
    candidateName?: string
  ): Promise<any> => {
    // Prevent duplicate session creation
    if (sessionCreationInProgress.current) {
      console.log('Session creation already in progress, waiting...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // If we already have session data, return it
      if (sessionData) {
        console.log('Using existing session data');
        return sessionData;
      }
    }

    // Set the lock
    sessionCreationInProgress.current = true;

    try {
      console.log(`Creating public interview session for role: ${roleId} (attempt ${retryCount + 1})`);

      // Use the applicationId passed to the function - don't override it
      // Only get candidateId from localStorage if not provided
      if (!candidateId) {
        const storedCandidateId = localStorage.getItem('candidateEmail') || localStorage.getItem('userEmail');
        // Convert null to undefined to match the expected type
        candidateId = storedCandidateId || undefined;
      }

      // First, check if we already have an active session stored in localStorage
      const existingSessionId = localStorage.getItem('currentSessionId');
      const existingTranscriptId = localStorage.getItem('currentTranscriptId');
      const sessionTimestamp = localStorage.getItem('sessionCreationTime');

      // If we have existing session data that's less than 5 minutes old, reuse it instead of creating a new one
      if (existingSessionId && existingTranscriptId && sessionTimestamp) {
        const creationTime = parseInt(sessionTimestamp);
        const now = Date.now();
        const sessionAge = now - creationTime;

        // Session is less than 5 minutes old (300000ms)
        if (sessionAge < 300000) {
          console.log(`Reusing existing session created ${Math.round(sessionAge/1000)} seconds ago:`, {
            session_id: existingSessionId,
            transcript_id: existingTranscriptId
          });

          // Create a mock session response with the existing data
          return {
            session_id: existingSessionId,
            transcript_id: existingTranscriptId,
            role_id: roleId,
            // We're missing client_secret, but that's ok for this purpose
            client_secret: { value: "reused-session", expires_at: Math.floor(Date.now()/1000) + 3600 },
            reused: true
          };
        } else {
          console.log('Existing session is too old, creating a new one');
        }
      }

      // Only get candidateName from localStorage if not provided
      if (!candidateName) {
        const storedCandidateName = localStorage.getItem('candidateName') || localStorage.getItem('lastApplicationName');
        // Convert null to undefined to match the expected type
        candidateName = storedCandidateName || undefined;
      }

      // Store the candidate name in localStorage for later use
      if (candidateName) {
        try {
          localStorage.setItem('candidateName', candidateName);
        } catch (e) {
          // Ignore localStorage errors
        }
      }

      // Add a timestamp to the URL in the API call
      const sessionResponse = await realtimeApi.createPublicInterviewSession(
        roleId,
        templateId, // Use the templateId passed to the function
        candidateId || undefined, // candidateId
        applicationId, // Use the applicationId passed to the function
        resumeText,
        jobPosting,
        candidateName || undefined // candidateName
      );

      // Log detailed session response for debugging
      console.log('Session creation response received:', sessionResponse);

      // Check if we got a valid session response (not an error response)
      const isValidSession = !('detail' in sessionResponse);
      if (isValidSession) {
        console.log('Session creation successful, detailed data:', {
          hasSessionId: !!sessionResponse.session_id,
          sessionIdLength: sessionResponse.session_id?.length || 0,
          hasClientSecret: !!sessionResponse.client_secret?.value,
          clientSecretLength: sessionResponse.client_secret?.value?.length || 0,
          expiresIn: sessionResponse.client_secret?.expires_at
            ? Math.floor(sessionResponse.client_secret.expires_at - Date.now()/1000)
            : 'N/A',
          hasTranscriptId: !!sessionResponse.transcript_id
        });

        // Store creation timestamp to help with future deduplication
        localStorage.setItem('sessionCreationTime', Date.now().toString());
      } else {
        console.error('Error response received:', sessionResponse.detail);
      }

      // Enhanced error checking for debugging
      if (!sessionResponse) {
        console.error('Error: Empty response from public interview session API');
        if (retryCount < 2) {
          console.log(`Retrying (${retryCount + 1}/3)...`);
          await new Promise(resolve => setTimeout(resolve, 2000));
          return createPublicInterviewSession(retryCount + 1, templateId, candidateId, applicationId, resumeText, jobPosting, candidateName);
        }
        throw new Error('No response received');
      }

      // Check if we got an error response
      if ('detail' in sessionResponse) {
        console.error('Error creating public interview session:', sessionResponse.detail);
        if (retryCount < 2) {
          console.log(`Retrying (${retryCount + 1}/3) after error: ${sessionResponse.detail}`);
          await new Promise(resolve => setTimeout(resolve, 2000));
          return createPublicInterviewSession(retryCount + 1, templateId, candidateId, applicationId, resumeText, jobPosting, candidateName);
        }
        throw new Error(sessionResponse.detail);
      }

      // Verify we have the essential session data
      if (!sessionResponse.session_id || !sessionResponse.client_secret || !sessionResponse.client_secret.value) {
        console.error('Invalid session data received:', JSON.stringify(sessionResponse, null, 2));
        if (retryCount < 2) {
          console.log(`Retrying (${retryCount + 1}/3) after invalid session data`);
          await new Promise(resolve => setTimeout(resolve, 2000));
          return createPublicInterviewSession(retryCount + 1, templateId, candidateId, applicationId, resumeText, jobPosting, candidateName);
        }
        throw new Error('Missing required session data');
      }

      // Verify the token is valid
      const isValid = await verifySessionToken(
        sessionResponse.session_id,
        sessionResponse.client_secret
      );

      if (!isValid) {
        console.error('Session token validation failed');
        if (retryCount < 2) {
          console.log(`Retrying (${retryCount + 1}/3) after token validation failure`);
          await new Promise(resolve => setTimeout(resolve, 3000));
          return createPublicInterviewSession(retryCount + 1, templateId, candidateId, applicationId, resumeText, jobPosting, candidateName);
        }
        throw new Error('Invalid session token');
      }

      console.log('Successfully created public interview session:', {
        session_id: sessionResponse.session_id,
        has_client_secret: !!sessionResponse.client_secret,
        transcript_id: sessionResponse.transcript_id,
        expires_at: new Date(sessionResponse.client_secret.expires_at * 1000).toLocaleString()
      });

      return sessionResponse;
    } catch (error) {
      if (retryCount < 2) {
        console.log(`Retrying (${retryCount + 1}/3) after exception:`, error);
        await new Promise(resolve => setTimeout(resolve, 3000));
        return createPublicInterviewSession(retryCount + 1, templateId, candidateId, applicationId, resumeText, jobPosting, candidateName);
      }
      throw error;
    } finally {
      // Release the lock
      sessionCreationInProgress.current = false;
    }
  };

  // Function to handle text-based interview submission
  const handleTextSubmission = async (answers: string[]) => {
    try {
      // Get application ID from URL params only - single source of truth
      const appId = applicationId;

      // Store the text answers in localStorage
      localStorage.setItem('textInterviewAnswers', JSON.stringify(answers));

      // Redirect to thank you page
      router.push(`/instant-interview/${roleId}/thank-you${appId ? `?applicationId=${appId}` : ''}`);

      toast.success('Your answers have been submitted successfully!');
    } catch (error) {
      console.error('Error submitting text answers:', error);
      toast.error('Failed to submit your answers. Please try again.');
    }
  };

  // Function to handle WebRTC connection failures
  const handleConnectionFailure = () => {
    setConnectionAttempts(prev => prev + 1);

    console.log(`Connection failure handler called. Attempt ${connectionAttempts + 1} of 3`);

    // After 2 failed attempts, show the text-based fallback immediately
    if (connectionAttempts >= 2) {
      console.log("Max reconnect attempts reached, switching to text-based interview immediately");
      setConnectionFailed(true);
      toast.error(
        'Video interview connection failed',
        { description: 'The system has switched to a text-based interview instead.' }
      );
    } else {
      // Toast notification on first failure
      toast.warning(
        'Connection issue detected',
        { description: 'Attempting to reconnect... If this persists, a text-based alternative will be provided.' }
      );

      // Try to use the existing session first before creating a new one
      if (sessionData && sessionData.session_id) {
        // Don't create a new session, just try to reconnect with the existing one
        console.log("Attempting to reconnect with existing session:", sessionData.session_id);

        // Trigger a re-render with the same session data but force a new connection
        const updatedSessionData = {...sessionData, reconnectAttempt: connectionAttempts};
        setSessionData(updatedSessionData);
      } else {
        // If there's no session data for some reason, create a new one
        try {
          // Get candidate name from localStorage
          const storedCandidateName = localStorage.getItem('candidateName') || localStorage.getItem('lastApplicationName');
          // Convert null to undefined to match the expected type
          const candidateName = storedCandidateName || undefined;

          // Store the candidate name in localStorage for later use
          if (candidateName) {
            try {
              localStorage.setItem('candidateName', candidateName);
            } catch (e) {
              // Ignore localStorage errors
            }
          }

          createPublicInterviewSession(0, undefined, undefined, undefined, undefined, undefined, candidateName).then(session => {
            setSessionData(session);
            setIsLoading(false);
          }).catch(err => {
            console.error("Failed to create new session:", err);
            setConnectionFailed(true);

            toast.error(
              'Unable to establish a video connection',
              { description: 'Switching to text-based interview mode.' }
            );
          });
        } catch (error) {
          console.error("Error creating new session:", error);
          setConnectionFailed(true);

          toast.error(
            'Connection error',
            { description: 'Please continue with the text-based interview.' }
          );
        }
      }
    }
  };

  useEffect(() => {
    const initializeInterview = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get the application ID from URL params only - this is the single source of truth
        // We don't fall back to localStorage to avoid using stale or incorrect application IDs
        const appId = applicationId;

        if (!appId) {
          console.warn('Application ID is missing from URL params, continuing with anonymous interview');
        }

        if (!roleId) {
          setError('Role ID is missing');
          setIsLoading(false);
          return;
        }

        // Try to get the role data to find the screening template
        let templateId: string | undefined;
        try {
          const roleData = await publicRolesService.getPublicRole(roleId);
          if (roleData && roleData.interviewProcess && Array.isArray(roleData.interviewProcess)) {
            // Find the screening stage (usually the first stage)
            const screeningStage = roleData.interviewProcess.find(stage =>
              stage.stage?.toLowerCase() === 'screening' || stage.stageIndex === 0);

            if (screeningStage && screeningStage.templateId) {
              templateId = screeningStage.templateId;
              console.log(`Found template ID ${templateId} for screening stage`);
            }
          }
        } catch (roleError) {
          console.warn('Error fetching role data for template:', roleError);
          // Continue without template ID
        }

        // Get application data if available
        let application: any = null;
        let resumeText: string | null = null;
        let jobPosting: string | null = null;

        if (appId) {
          application = await getApplication(appId);

          // Try to get resume text from localStorage first
          resumeText = localStorage.getItem('parsedResumeText');

          // If no resume text in localStorage, check the application data
          if (!resumeText && application) {
            // Access resume information through application properties if available
            if (application.resumeUrl) {
              console.log('Resume available, but text not parsed');
            }

            // Try to get from evaluations if they exist
            // Access safely with optional chaining
            const evaluations = (application as any).evaluations;
            if (evaluations && Array.isArray(evaluations) && evaluations.length > 0) {
              const latestEvaluation = evaluations[0];
              resumeText = latestEvaluation.parsedResumeText || null;
            }
          }

          console.log('Resume text available for interview:', resumeText ? 'Yes' : 'No');

          // If no application found but we have basic info in localStorage, use that
          if (!application) {
            const candidateName = localStorage.getItem('lastApplicationName');
            const candidateEmail = localStorage.getItem('lastApplicationEmail');

            if (candidateName || candidateEmail) {
              console.log('Using basic candidate info from localStorage:', { candidateName, candidateEmail });
              application = {
                id: appId,
                roleId,
                fullName: candidateName || 'Candidate',
                email: candidateEmail || '<EMAIL>',
                status: 'pending',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              };
            }
          }
        }

        // Try to get the job posting for the role using multiple approaches
        try {
          // Approach 1: Try the backend API endpoint
          try {
            const apiUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/v1/public/jobs/${roleId}/job-posting?t=${Date.now()}`;
            console.log('Fetching job posting from:', apiUrl);
            const response = await fetch(apiUrl, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Expires': '0'
              }
            });

            if (response.ok) {
              const roleData = await response.json();
              jobPosting = roleData.job_posting || roleData.summary || null;
              console.log('Job posting available from API:', jobPosting ? `Yes (length: ${jobPosting.length} chars)` : 'No');
            } else {
              console.warn(`Failed to fetch job posting from API: ${response.status} ${response.statusText}`);
            }
          } catch (apiError) {
            console.error('Error fetching job posting from API:', apiError);
          }

          // Approach 2: If job posting is still null, try the publicRolesService
          if (!jobPosting) {
            try {
              console.log('Trying to fetch job posting from publicRolesService');
              const roleData = await publicRolesService.getPublicRole(roleId);
              jobPosting = roleData?.jobPosting || roleData?.summary || null;
              console.log('Job posting from publicRolesService:', jobPosting ? `Available (length: ${jobPosting.length} chars)` : 'Not available');
            } catch (serviceError) {
              console.error('Error with publicRolesService job posting fetch:', serviceError);
            }
          }

          // Approach 3: If still null, try to get the role directly from Firestore
          if (!jobPosting) {
            try {
              console.log('Trying to fetch job posting from Firestore');
              const roleRef = doc(db, 'roles', roleId);
              const roleSnap = await getDoc(roleRef);

              if (roleSnap.exists()) {
                const roleData = roleSnap.data();
                jobPosting = roleData?.jobPosting || roleData?.summary || null;
                console.log('Job posting from Firestore:', jobPosting ? `Available (length: ${jobPosting.length} chars)` : 'Not available');
              } else {
                console.warn('Role not found in Firestore');
              }
            } catch (firestoreError) {
              console.error('Error fetching job posting from Firestore:', firestoreError);
            }
          }
        } catch (error) {
          console.error('Error in job posting fetch process:', error);
          // We'll continue without the job posting
        }

        console.log('Application data for interview:', application);

        // Get candidate ID from localStorage
        const storedCandidateId = localStorage.getItem('candidateEmail') || localStorage.getItem('userEmail');
        // Convert null to undefined to match the expected type
        const candidateId = storedCandidateId || undefined;

        // Create a public interview session
        try {
          // Get candidate name from localStorage
          const storedCandidateName = localStorage.getItem('candidateName') || localStorage.getItem('lastApplicationName');
          // Convert null to undefined to match the expected type
          const candidateName = storedCandidateName || undefined;

          // Store the candidate name in localStorage for later use
          if (candidateName) {
            try {
              localStorage.setItem('candidateName', candidateName);
            } catch (e) {
              // Ignore localStorage errors
            }
          }

          const sessionResponse = await createPublicInterviewSession(
            0, // retryCount
            templateId, // templateId - now we pass the template ID we found
            candidateId || undefined, // candidateId
            appId || undefined, // applicationId - convert null to undefined
            resumeText || undefined,
            jobPosting || undefined,
            candidateName || undefined // candidateName
          );

          // Store session data
          setSessionData(sessionResponse);

          // If the session has a transcript ID and we have an application, update it
          // Only update transcript if this is not a reused session to avoid duplicate updates
          if (sessionResponse.transcript_id && appId && !sessionResponse.reused) {
            try {
              // For public interviews, default to stage 0 (Screening)
              const stageIndex = 0;
              const stageName = "Screening";

              // Update the application with the transcript ID using our utility function
              // This will update the application in the main applications collection
              await updateApplicationWithTranscript(appId, sessionResponse.transcript_id, stageIndex, stageName);
              console.log(`Application ${appId} updated with transcript ID: ${sessionResponse.transcript_id}`);

              // Store session info in the public_interview_sessions collection for reference
              try {
                // Import additional Firebase modules dynamically
                const { setDoc, updateDoc, serverTimestamp } = await import('firebase/firestore');

                // Try to update the existing document first
                try {
                  // Get candidate name from localStorage - try multiple sources
                  const candidateName = localStorage.getItem('candidateName') ||
                                       localStorage.getItem('lastApplicationName') ||
                                       localStorage.getItem('fullName') ||
                                       'Anonymous Candidate';

                  // Always store the candidate name in localStorage for later use
                  try {
                    localStorage.setItem('candidateName', candidateName);
                  } catch (e) {
                    // Ignore localStorage errors
                  }

                  const docRef = doc(db, "public_interview_sessions", sessionResponse.transcript_id);
                  await updateDoc(docRef, {
                    applicationId: appId,
                    candidateId: candidateId,
                    // Always include candidate name
                    candidateName: candidateName,
                    candidate_name: candidateName,
                    updated_at: serverTimestamp()
                  });
                  console.log('Successfully updated public interview session with application ID in Firestore');
                } catch (updateError) {
                  // If update fails, document might not exist, so create it
                  console.log('Document may not exist, creating new document');

                  // Get candidate name from localStorage - try multiple sources
                  const candidateName = localStorage.getItem('candidateName') ||
                                       localStorage.getItem('lastApplicationName') ||
                                       localStorage.getItem('fullName') ||
                                       'Anonymous Candidate';

                  // Always store the candidate name in localStorage for later use
                  try {
                    localStorage.setItem('candidateName', candidateName);
                  } catch (e) {
                    // Ignore localStorage errors
                  }

                  const docRef = doc(db, "public_interview_sessions", sessionResponse.transcript_id);
                  await setDoc(docRef, {
                    session_id: sessionResponse.session_id,
                    transcript_id: sessionResponse.transcript_id,
                    role_id: roleId,
                    created_at: serverTimestamp(),
                    updated_at: serverTimestamp(),
                    status: 'in_progress',
                    is_public: true,
                    applicationId: appId,
                    candidateId: candidateId,
                    // Also add snake_case versions for consistency
                    application_id: appId,
                    candidate_id: candidateId,
                    // Always include candidate name
                    candidateName: candidateName,
                    candidate_name: candidateName,
                    messages: [] // Initialize with empty messages array
                  });
                  console.log('Successfully created public interview session document in Firestore');
                }
              } catch (firestoreError) {
                console.error('Error updating session document in Firestore:', firestoreError);
                // Continue despite error - we'll try again during the interview
              }
            } catch (updateError) {
              console.error('Error updating application with transcript:', updateError);
              // Continue despite error - non-critical
            }
          }

          // Store session info in localStorage as fallback
          try {
            localStorage.setItem('currentSessionId', sessionResponse.session_id);
            if (sessionResponse.transcript_id) {
              localStorage.setItem('currentTranscriptId', sessionResponse.transcript_id);
            }

            // Store additional session metadata to help with debugging and recovery
            localStorage.setItem('sessionCreationTime', Date.now().toString());
            localStorage.setItem('sessionRoleId', roleId);

            // If we have an application ID, store that association as well
            if (appId) {
              localStorage.setItem('sessionApplicationId', appId);
            }
          } catch (storageError) {
            console.error('Error storing session info in localStorage:', storageError);
          }

          setIsLoading(false);
        } catch (sessionError) {
          console.error('Exception creating public interview session:', sessionError);
          setError('Failed to create interview session. Please try again later.');
          setIsLoading(false);

          toast.error('Failed to start interview', {
            description: sessionError instanceof Error ? sessionError.message : 'There was an error setting up your interview. Please try again later.',
          });
        }
      } catch (error) {
        console.error('Error in initializeInterview:', error);
        setError('An unexpected error occurred. Please try again later.');
        setIsLoading(false);
      }
    };

    initializeInterview();
  }, [applicationId, roleId]);

  // Set up public interview session with immediate execution to ensure flag is set early
  useEffect(() => {
    console.log('PublicInterviewPage: Setting public interview flags');

    // Mark this as a public interview in localStorage (critical for routing)
    localStorage.setItem('isPublicInterview', 'true');

    // Reset the completion flag when starting a new interview
    localStorage.removeItem('publicInterviewCompleted');

    // Clear ALL sessionStorage that might trigger authenticated API calls
    sessionStorage.clear();

    // Verify no authenticated redirects will happen
    const checkAndClearStorage = () => {
      // These are the main flags that would trigger a redirect to authenticated pages
      const keysToCheck = [
        'coming_from_call',
        'call_role_id',
        'call_transcript_id',
        'auto_enrich_role'
      ];

      // Check if any of these keys exist and clear them
      keysToCheck.forEach(key => {
        if (sessionStorage.getItem(key)) {
          console.log(`PublicInterviewPage: Clearing ${key} from sessionStorage`);
          sessionStorage.removeItem(key);
        }
      });
    };

    // Run immediately
    checkAndClearStorage();

    // Also set up interval to check throughout the session
    const intervalId = setInterval(checkAndClearStorage, 5000);

    return () => {
      // Don't clean up the isPublicInterview flag here since it needs to persist
      // to the thank you page - it will be cleaned up by the VideoCall component
      clearInterval(intervalId);
    };
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <LoadingSpinner className="mx-auto" />
          <p className="mt-4 text-muted-foreground">Setting up your interview...</p>
        </div>
      </div>
    );
  }

  if (error || (!sessionData && !connectionFailed)) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error || 'Failed to initialize interview'}</p>
            <p className="text-sm text-muted-foreground mt-2">
              Please try again later or contact support.
            </p>
            <div className="mt-4">
              <Button onClick={() => setConnectionFailed(true)}>
                Continue with Text-based Interview
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show the text-based interview if connections keep failing
  if (connectionFailed) {
    return (
      <div className="container mx-auto py-8 px-4">
        <TextBasedInterview roleId={roleId} onSubmit={handleTextSubmission} />
      </div>
    );
  }

  // If permissions haven't been granted yet, show the permission check screen
  if (!permissionsGranted) {
    return (
      <div className="flex justify-center items-center min-h-screen p-4">
        <PermissionCheck
          onPermissionsGranted={() => setPermissionsGranted(true)}
          className="max-w-lg"
        />
      </div>
    );
  }

  // Once permissions are granted, show the video call
  return (
    <div className="h-screen w-full flex flex-col overflow-hidden dark bg-black"
         data-theme="dark"
         data-force-dark="true"
         data-call-type="interview"
         data-role-id={roleId}
         data-template-id={sessionData.template_id || undefined}
         data-is-public-interview="true">
      <DarkModeVideoCall
        type="interview"
        roleId={roleId}
        templateId={sessionData.template_id}
        isEnrichment={false}
        isPublicInterview={true}
        sessionData={sessionData}
        onConnectionFailure={handleConnectionFailure}
        reconnectAttempt={connectionAttempts}
      />
    </div>
  );
}