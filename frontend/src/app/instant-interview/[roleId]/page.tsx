'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { publicRolesService } from '@/services/roles/public-service';
import { PublicRole } from '@/types/role';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { CandidateApplicationForm } from '@/components/application/CandidateApplicationForm';
import { ResumeEvaluationResponse, BasicEvaluationResponse } from '@/services/resume';
import { trackResumeEvaluation, trackInterviewContinuation } from '@/lib/analytics/resume-evaluation';
import { toast } from 'sonner';
import { ArrowLeft, Building, MapPin, Clock, Briefcase } from 'lucide-react';
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils/styles';

/**
 * InstantInterviewPage component
 * Page for candidates to apply for an instant interview
 * Supports both light and dark themes
 */
export default function InstantInterviewPage() {
  const params = useParams();
  const router = useRouter();
  const roleId = params?.roleId as string;

  const [role, setRole] = useState<PublicRole | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [applicationComplete, setApplicationComplete] = useState(false);
  const [evaluateResumes, setEvaluateResumes] = useState(true); // Feature flag for resume evaluation

  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  const isDarkMode = mounted && (resolvedTheme === 'dark' || theme === 'dark');

  // Fetch role data
  useEffect(() => {
    const fetchRoleData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!roleId) {
          setError('Role ID is missing');
          setIsLoading(false);
          return;
        }

        // Fetch the public role data
        const data = await publicRolesService.getPublicRole(roleId);

        if (!data) {
          setError('Role not found or not publicly available.');
          setIsLoading(false);
          return;
        }

        // Cast to PublicRole before accessing properties
        const publicRole = data as PublicRole;
        setRole(publicRole);

        // Check if this role has resume evaluation enabled
        // Read from role data if the property exists, otherwise use default (true)
        const shouldEvaluateResumes = publicRole.settings?.evaluateResumes !== undefined
          ? Boolean(publicRole.settings.evaluateResumes)
          : true;

        setEvaluateResumes(shouldEvaluateResumes);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching role data:', error);
        setError('Failed to load role data. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchRoleData();
  }, [roleId]);

  // Handle application submission
  const handleApplicationComplete = async (applicationData: {
    applicationId: string;
    evaluation?: ResumeEvaluationResponse | BasicEvaluationResponse;
    isPassed?: boolean;
  }) => {
    const { applicationId, evaluation, isPassed } = applicationData;
    setApplicationComplete(true);

    // Store application ID in localStorage for later reference
    localStorage.setItem('applicationId', applicationId);
    localStorage.setItem('roleId', roleId);

    // Store evaluation data if available
    if (evaluation) {
      try {
        localStorage.setItem('resumeEvaluation', JSON.stringify(evaluation));
        localStorage.setItem('resumeEvaluationResult', isPassed ? 'PASS' : 'FAIL');

        // The tracking function now has its own internal checks for public context
        // so we can call it safely and it will skip tracking if in public flow
        await trackResumeEvaluation(roleId, evaluation, applicationId);
      } catch (e) {
        console.error('Failed to store evaluation data in localStorage:', e);
      }
    }

    // If there's an evaluation and it failed, we might want to show a different message or redirect elsewhere
    // For now, we'll always proceed to the interview but could implement different paths based on evaluation

    // Show success message based on evaluation
    if (evaluation && !isPassed) {
      toast.info('Application submitted! Proceeding to interview.', {
        description: 'While your resume doesn\'t fully match the requirements, we\'ll continue with the interview.',
        duration: 3000,
      });
    } else {
      toast.success('Application submitted successfully!', {
        description: 'You will now be redirected to the interview.',
        duration: 3000,
      });
    }

    // Redirect to the interview page with evaluation result in query params if available
    setTimeout(async () => {
      const queryParams = new URLSearchParams({
        applicationId
      });

      if (evaluation) {
        const result = isPassed ? 'PASS' : 'FAIL';
        queryParams.append('evaluationResult', result);

        // The tracking function now has its own internal checks for public context
        // so we can call it safely and it will skip tracking if in public flow
        await trackInterviewContinuation(roleId, applicationId, result);
      }

      router.push(`/instant-interview/${roleId}/interview?${queryParams.toString()}`);
    }, 2000);
  };

  // Handle back button
  const handleBack = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !role) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error || 'Role not found'}</p>
            <Button className="mt-4" onClick={handleBack}>
              Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Role information */}
        <div className="md:col-span-1">
          <Card className={cn(
            "backdrop-blur-sm",
            isDarkMode
              ? "bg-slate-800/50 border-slate-700/60"
              : "bg-white/70 border-slate-200"
          )}>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className={cn(
                  "text-xl text-transparent bg-clip-text bg-gradient-to-r",
                  isDarkMode
                    ? "from-indigo-400 via-purple-400 to-pink-400"
                    : "from-indigo-600 via-purple-600 to-pink-600"
                )}>
                  {role.title}
                </CardTitle>
                <CardDescription>{role.level}</CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="text-muted-foreground"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
            </CardHeader>
            <CardContent className="space-y-5">
              {/* Summary - Moved to top */}
              {role.summary && (
                <div>
                  <h3 className={cn(
                    "text-sm font-medium mb-2",
                    isDarkMode ? "text-slate-200" : "text-slate-800"
                  )}>
                    Summary
                  </h3>
                  <p className={cn(
                    "text-sm",
                    isDarkMode ? "text-muted-foreground" : "text-slate-600"
                  )}>
                    {role.summary}
                  </p>
                </div>
              )}

              {/* Key Responsibilities */}
              {role.keyResponsibilities && role.keyResponsibilities.length > 0 && (
                <div>
                  <h3 className={cn(
                    "text-sm font-medium mb-2",
                    isDarkMode ? "text-slate-200" : "text-slate-800"
                  )}>
                    Key Responsibilities
                  </h3>
                  <ul className={cn(
                    "text-sm list-disc list-inside space-y-1",
                    isDarkMode ? "text-muted-foreground" : "text-slate-600"
                  )}>
                    {role.keyResponsibilities.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Required Skills */}
              {role.requiredSkills && Object.keys(role.requiredSkills).length > 0 && (
                <div>
                  <h3 className={cn(
                    "text-sm font-medium mb-2",
                    isDarkMode ? "text-slate-200" : "text-slate-800"
                  )}>
                    Required Skills
                  </h3>
                  <div className="flex flex-wrap gap-1.5">
                    {Object.entries(role.requiredSkills).map(([skill, _level], index: number) => (
                      <span
                        key={`req-${index}`}
                        className={cn(
                          "inline-flex items-center rounded-md bg-transparent px-2 py-1 text-xs font-medium ring-1 ring-inset",
                          isDarkMode
                            ? "text-emerald-400 ring-emerald-500/30"
                            : "text-emerald-700 ring-emerald-600/30"
                        )}
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Preferred Skills */}
              {role.preferredSkills && Object.keys(role.preferredSkills).length > 0 && (
                <div>
                  <h3 className={cn(
                    "text-sm font-medium mb-2",
                    isDarkMode ? "text-slate-200" : "text-slate-800"
                  )}>
                    Preferred Skills
                  </h3>
                  <div className="flex flex-wrap gap-1.5">
                    {Object.entries(role.preferredSkills).map(([skill, _level], index: number) => (
                      <span
                        key={`pref-${index}`}
                        className={cn(
                          "inline-flex items-center rounded-md bg-transparent px-2 py-1 text-xs font-medium ring-1 ring-inset",
                          isDarkMode
                            ? "text-purple-400 ring-purple-500/30"
                            : "text-purple-700 ring-purple-600/30"
                        )}
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Company and team */}
              <div className="flex items-start space-x-2">
                <Building className={cn(
                  "h-5 w-5 shrink-0 mt-0.5",
                  isDarkMode ? "text-muted-foreground" : "text-slate-500"
                )} />
                <div>
                  <div className={cn(
                    "font-medium",
                    isDarkMode ? "text-slate-200" : "text-slate-800"
                  )}>
                    Company & Team
                  </div>
                  <div className={cn(
                    "text-sm",
                    isDarkMode ? "text-muted-foreground" : "text-slate-600"
                  )}>
                    {role.team}
                  </div>
                </div>
              </div>

              {/* Location */}
              <div className="flex items-start space-x-2">
                <MapPin className={cn(
                  "h-5 w-5 shrink-0 mt-0.5",
                  isDarkMode ? "text-muted-foreground" : "text-slate-500"
                )} />
                <div>
                  <div className={cn(
                    "font-medium",
                    isDarkMode ? "text-slate-200" : "text-slate-800"
                  )}>
                    Location
                  </div>
                  <div className={cn(
                    "text-sm",
                    isDarkMode ? "text-muted-foreground" : "text-slate-600"
                  )}>
                    {role.location.city ? `${role.location.city}, ` : ''}
                    {role.location.type} {role.location.remoteStatus ? `· ${role.location.remoteStatus}` : ''}
                  </div>
                </div>
              </div>

              {/* Job Type */}
              <div className="flex items-start space-x-2">
                <Briefcase className={cn(
                  "h-5 w-5 shrink-0 mt-0.5",
                  isDarkMode ? "text-muted-foreground" : "text-slate-500"
                )} />
                <div>
                  <div className={cn(
                    "font-medium",
                    isDarkMode ? "text-slate-200" : "text-slate-800"
                  )}>
                    Job Type
                  </div>
                  <div className={cn(
                    "text-sm",
                    isDarkMode ? "text-muted-foreground" : "text-slate-600"
                  )}>
                    {role.jobType}
                  </div>
                </div>
              </div>

              {/* Experience */}
              <div className="flex items-start space-x-2">
                <Clock className={cn(
                  "h-5 w-5 shrink-0 mt-0.5",
                  isDarkMode ? "text-muted-foreground" : "text-slate-500"
                )} />
                <div>
                  <div className={cn(
                    "font-medium",
                    isDarkMode ? "text-slate-200" : "text-slate-800"
                  )}>
                    Experience
                  </div>
                  <div className={cn(
                    "text-sm",
                    isDarkMode ? "text-muted-foreground" : "text-slate-600"
                  )}>
                    {role.yearsOfExperience}
                  </div>
                </div>
              </div>

              <div className="mt-4 flex justify-center">
                <Button
                  variant="secondary"
                  className="w-full font-medium"
                  onClick={handleBack}
                >
                  View Full Job Description
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Application form */}
        <div className="md:col-span-2">
          {!applicationComplete ? (
            <Card className={cn(
              "max-w-[110%] mx-auto bg-transparent border-0 shadow-none",
              isDarkMode
                ? "bg-transparent border-0 shadow-none"
                : "bg-transparent border-0 shadow-none"
            )}>
              <CardHeader>
                <CardTitle>Apply for Instant Interview</CardTitle>
                <CardDescription>
                  Please fill out the form below to apply for this position.
                  All fields marked with * are required.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CandidateApplicationForm
                  roleId={roleId}
                  jobPosting={role.jobPosting}
                  roleTitle={role.title}
                  onComplete={handleApplicationComplete}
                  evaluateResume={evaluateResumes}
                  useBasicEvaluation={true}
                  noCard={true}
                />
              </CardContent>
            </Card>
          ) : (
            <Card className={cn(
              "max-w-[110%] mx-auto bg-transparent border-0 shadow-none",
              isDarkMode
                ? "bg-transparent border-0 shadow-none"
                : "bg-transparent border-0 shadow-none"
            )}>
              <CardHeader>
                <CardTitle>Processing Your Application</CardTitle>
                <CardDescription>
                  Please wait while we prepare your interview...
                </CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center py-6">
                <LoadingSpinner />
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}