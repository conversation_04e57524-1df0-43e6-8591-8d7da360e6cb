'use client';

import { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { publicRolesService } from '@/services/roles/public-service';
import { PublicRole } from '@/types/role';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/Card';
import { CheckCircle2, Home } from 'lucide-react';
import { toast } from 'sonner';

/**
 * ThankYouPage component
 * Page shown after a candidate completes their interview
 */
export default function ThankYouPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const roleId = params?.roleId as string;
  const applicationId = searchParams?.get('applicationId');

  const [role, setRole] = useState<PublicRole | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [, setError] = useState<string | null>(null);
  const [evaluationStatus, setEvaluationStatus] = useState<'idle' | 'triggered' | 'error' | 'completed'>('idle');

  // Verify we came from a public interview and clear any authenticated session data
  useEffect(() => {
    // Multiple ways to detect a public interview flow
    const fromLocalStorage = localStorage.getItem('isPublicInterview') === 'true';
    const completionFlag = localStorage.getItem('publicInterviewCompleted') === 'true';
    const userEndedInterview = localStorage.getItem('userEndedInterview') === 'true';
    const hasCompletedSessionId = !!localStorage.getItem('completedSessionId');
    const hasApplicationId = !!localStorage.getItem('applicationId') || !!localStorage.getItem('lastApplicationId');
    const directAccess = localStorage.getItem('directAccess') === 'true';

    // Check URL path - if we're in /instant-interview/ path, assume public flow
    const urlIsPublic = typeof window !== 'undefined' && window.location.pathname.includes('/instant-interview/');

    // Combine all signals - any one of these indicates a public flow
    const isPublicFlow = fromLocalStorage || completionFlag || userEndedInterview || urlIsPublic ||
                        directAccess || (hasCompletedSessionId && hasApplicationId);

    console.log('ThankYouPage: Initializing with detection results:', {
      fromLocalStorage,
      completionFlag,
      userEndedInterview,
      urlIsPublic,
      directAccess,
      hasCompletedSessionId,
      hasApplicationId,
      isPublicFlow
    });

    // Since this is the thank-you page specifically for public interviews,
    // we can safely assume we're in a public flow even if flags aren't set
    if (!isPublicFlow) {
      console.log('ThankYouPage: No public flow flags detected, but assuming public flow based on URL');
      // Set the flag to ensure proper cleanup
      localStorage.setItem('isPublicInterview', 'true');
    }

    // Always clear ALL sessionStorage to prevent any authenticated API calls
    sessionStorage.clear();

    // Log the current storage state for debugging
    console.log('ThankYouPage: Current localStorage state:', {
      isPublicInterview: localStorage.getItem('isPublicInterview'),
      publicInterviewCompleted: localStorage.getItem('publicInterviewCompleted'),
      directAccess: localStorage.getItem('directAccess'),
      applicationId: localStorage.getItem('applicationId'),
      roleId: localStorage.getItem('roleId'),
      completedSessionId: localStorage.getItem('completedSessionId')
    });

    // When this component unmounts, clean up the flags
    return () => {
      // Clean up all the public interview flags
      localStorage.removeItem('isPublicInterview');
      localStorage.removeItem('publicInterviewCompleted');
      localStorage.removeItem('userEndedInterview');
      localStorage.removeItem('directAccess');
      localStorage.removeItem('currentSessionId');
      localStorage.removeItem('currentTranscriptId');
      localStorage.removeItem('completedSessionId');
      localStorage.removeItem('evaluation_triggered');
    };
  }, []);

  // Fetch role data
  useEffect(() => {
    const fetchRoleData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!roleId) {
          setError('Role ID is missing');
          setIsLoading(false);
          return;
        }

        // Fetch the public role data
        const data = await publicRolesService.getPublicRole(roleId);

        if (!data) {
          console.warn('Role not found or not publicly available');
          // Don't set error here, we can still show a generic thank you
          setIsLoading(false);
          return;
        }

        setRole(data as PublicRole);
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching role data:', error);
        // Don't set error here, we can still show a generic thank you
        setIsLoading(false);
      }
    };

    fetchRoleData();
  }, [roleId]);

  // Trigger automatic interview evaluation
  useEffect(() => {
    const triggerAutomaticEvaluation = async () => {
      try {
        // Check if evaluation was already triggered for this session
        const evaluationTriggeredKey = 'evaluation_triggered';
        if (localStorage.getItem(evaluationTriggeredKey) === 'true') {
          console.log('ThankYouPage: Evaluation already triggered for this session, skipping');
          setEvaluationStatus('completed');
          return;
        }
        
        // CRITICAL: Get session ID from various possible sources - prioritize OpenAI format IDs
        const openaiSessionId = localStorage.getItem('openai_session_id');
        const completedSessionId = localStorage.getItem('completedSessionId');
        const currentSessionId = localStorage.getItem('currentSessionId');
        const lastTranscriptId = localStorage.getItem('lastTranscriptId');
        
        // Log ALL possible session IDs for debugging
        console.log('ThankYouPage: Available session IDs:', {
          openaiSessionId,
          completedSessionId,
          currentSessionId,
          lastTranscriptId,
          applicationId,
          roleId
        });
        
        // Use the best session ID available, prioritizing OpenAI format
        const sessionId = 
          (openaiSessionId?.startsWith('sess_') ? openaiSessionId : null) || 
          (completedSessionId?.startsWith('sess_') ? completedSessionId : null) ||
          (currentSessionId?.startsWith('sess_') ? currentSessionId : null) ||
          (lastTranscriptId?.startsWith('sess_') ? lastTranscriptId : null) ||
          completedSessionId || 
          currentSessionId ||
          lastTranscriptId;
        
        if (!sessionId) {
          console.error('ThankYouPage: No valid session ID found for automatic evaluation');
          setEvaluationStatus('error');
          return;
        }

        if (!roleId) {
          console.error('ThankYouPage: No role ID found for automatic evaluation');
          setEvaluationStatus('error');
          return;
        }

        console.log(`ThankYouPage: Triggering automatic evaluation for session ${sessionId} and role ${roleId}`);
        setEvaluationStatus('triggered');

        // Make direct fetch request to avoid any middleware issues
        try {
          const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://recruiva-backend.onrender.com';
          const endpoint = `/api/v1/interview-evaluation/public/auto-evaluate/${sessionId}`;
          const fullUrl = `${apiUrl}${endpoint}`;
            
          console.log(`ThankYouPage: Making fetch request to ${fullUrl}`);
            
          // Extract stage information from localStorage if available
          const stageIndex = localStorage.getItem('interviewStageIndex');
          const stageName = localStorage.getItem('interviewStageName');
          const templateId = localStorage.getItem('interviewTemplateId');
            
          // Try to get template data and resume text from localStorage
          let templateData = null;
          let resumeText: string | null = null;
          try {
            const templateDataStr = localStorage.getItem('templateData');
            if (templateDataStr) {
              templateData = JSON.parse(templateDataStr);
              console.log('ThankYouPage: Found template data in localStorage for fetch');
            }
              
            // Try to get resume text if available
            resumeText = localStorage.getItem('resumeText');
            if (resumeText) {
              console.log('ThankYouPage: Found resume text in localStorage for fetch');
            }
          } catch (parseError) {
            console.warn('ThankYouPage: Error parsing data from localStorage for fetch:', parseError);
          }
              
          const response = await fetch(fullUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache',
            },
            body: JSON.stringify({
              role_id: roleId,
              application_id: applicationId || undefined,
              template_id: templateId || undefined,
              stage_name: stageName || undefined,
              stage_index: stageIndex ? parseInt(stageIndex) : undefined,
              template_data: templateData || undefined,
              resume_text: resumeText || undefined
            })
          });
          
          const responseData = await response.json();
          console.log('ThankYouPage: Automatic evaluation API response:', responseData);
          
          if (response.ok) {
            // Mark as triggered so we don't run this again if component remounts
            localStorage.setItem(evaluationTriggeredKey, 'true');
            setEvaluationStatus('completed');
            
            // Add visual feedback for development only
            if (process.env.NODE_ENV !== 'production') {
              toast.success('Evaluation triggered successfully (dev only message)');
            }
          } else {
            console.error('ThankYouPage: Error response from evaluation API:', responseData);
            setEvaluationStatus('error');
          }
        } catch (fetchError) {
          console.error('ThankYouPage: Fetch error triggering evaluation:', fetchError);
          
          // Fallback to axios if fetch fails
          try {
            console.log('ThankYouPage: Trying axios as fallback');
            const { apiClient } = await import('@/lib/api/client/axios');
            
            // Extract stage information from localStorage if available
            const stageIndex = localStorage.getItem('interviewStageIndex');
            const stageName = localStorage.getItem('interviewStageName');
            const templateId = localStorage.getItem('interviewTemplateId');
            
            // Try to get template data and resume text from localStorage
            let templateData = null;
            let resumeText: string | null = null;
            try {
              const templateDataStr = localStorage.getItem('templateData');
              if (templateDataStr) {
                templateData = JSON.parse(templateDataStr);
                console.log('ThankYouPage: Found template data in localStorage');
              }
              
              // Try to get resume text if available
              resumeText = localStorage.getItem('resumeText');
              if (resumeText) {
                console.log('ThankYouPage: Found resume text in localStorage');
              }
            } catch (parseError) {
              console.warn('ThankYouPage: Error parsing data from localStorage:', parseError);
            }
            
            const response = await apiClient.post(
              `/api/v1/interview-evaluation/public/auto-evaluate/${sessionId}`,
              {
                role_id: roleId,
                application_id: applicationId || undefined,
                template_id: templateId || undefined,
                stage_name: stageName || undefined,
                stage_index: stageIndex ? parseInt(stageIndex) : undefined,
                template_data: templateData || undefined,
                resume_text: resumeText || undefined
              },
              {
                timeout: 15000, // Increased timeout
              }
            );
            
            console.log('ThankYouPage: Axios fallback succeeded:', response.data);
            localStorage.setItem(evaluationTriggeredKey, 'true');
            setEvaluationStatus('completed');
          } catch (axiosError) {
            console.error('ThankYouPage: Axios fallback also failed:', axiosError);
            setEvaluationStatus('error');
          }
        }
      } catch (error) {
        console.error('ThankYouPage: Error triggering automatic evaluation:', error);
        setEvaluationStatus('error');
        // Don't show error to candidate - this is a background process
      }
    };

    // Only trigger if this is a completed public interview
    const isPublicInterview = localStorage.getItem('isPublicInterview') === 'true';
    const isCompleted = localStorage.getItem('publicInterviewCompleted') === 'true';
    const userEndedInterview = localStorage.getItem('userEndedInterview') === 'true';
    
    // Log all key values for debugging
    console.log('ThankYouPage: Interview flags:', {
      isPublicInterview,
      isCompleted,
      userEndedInterview,
      roleId,
      applicationId,
      evaluationStatus
    });
    
    if ((isPublicInterview && (isCompleted || userEndedInterview)) && roleId) {
      // Small delay to ensure this doesn't compete with page rendering
      setTimeout(triggerAutomaticEvaluation, 1000);
    }
  }, [roleId, applicationId, evaluationStatus]);

  // Handle direct access to thank-you page
  useEffect(() => {
    // Check if the user navigated directly to this page without completing an interview
    // This will be true if none of the expected flags are set
    const hasInterviewFlags = localStorage.getItem('isPublicInterview') === 'true' ||
                             localStorage.getItem('publicInterviewCompleted') === 'true' ||
                             localStorage.getItem('userEndedInterview') === 'true' ||
                             !!localStorage.getItem('completedSessionId');

    // Check if we have referrer information
    const hasReferrer = document.referrer && (
      document.referrer.includes('/instant-interview/') ||
      document.referrer.includes('/interview')
    );

    // If we have no flags and no proper referrer, the user might have directly accessed this page
    const isDirectAccess = !hasInterviewFlags && !hasReferrer && roleId;

    if (isDirectAccess) {
      console.log('ThankYouPage: Detected direct access, setting minimal flags for proper operation');

      // Set minimal flags needed for this page to function correctly
      localStorage.setItem('isPublicInterview', 'true');
      localStorage.setItem('directAccess', 'true');

      // Track this as a direct access for analytics
      console.log('ThankYouPage: User directly accessed thank you page without completing interview');
    }
  }, [roleId]);

  const handleGoHome = () => {
    // Clear all storage related to public interview flow
    const keysToRemove = [
      'applicationId',
      'roleId',
      'lastApplicationId',
      'isPublicInterview',
      'publicInterviewCompleted',
      'userEndedInterview',
      'directAccess',
      'currentSessionId',
      'currentTranscriptId',
      'completedSessionId',
      'lastTranscriptId',
      'evaluation_triggered',
      'openai_session_id',
      'interviewStageIndex',
      'interviewStageName',
      'interviewTemplateId',
      'templateData',
      'resumeText',
      'sessionCreationTime',
      'parsedResumeText',
      'resumeEvaluation',
      'resumeEvaluationResult',
      'textInterviewAnswers'
    ];

    // Remove all keys
    keysToRemove.forEach(key => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.error(`Error removing ${key} from localStorage:`, error);
      }
    });

    // Clear any session storage as well
    try {
      sessionStorage.clear();
    } catch (error) {
      console.error('Error clearing sessionStorage:', error);
    }

    console.log('ThankYouPage: Cleared all interview-related data');

    // Redirect to homepage - use window.location to ensure a full page reload
    window.location.href = '/';
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <LoadingSpinner />
      </div>
    );
  }

  // Show evaluation status in development mode only
  const renderDebugInfo = () => {
    if (process.env.NODE_ENV !== 'production') {
      return (
        <div className="fixed bottom-2 right-2 text-xs bg-black/70 text-white p-2 rounded z-50">
          <div>Status: <span className={
            evaluationStatus === 'triggered' ? 'text-yellow-400' :
            evaluationStatus === 'completed' ? 'text-green-400' :
            evaluationStatus === 'error' ? 'text-red-400' : 'text-blue-400'
          }>{evaluationStatus}</span></div>
          <div>RoleID: {roleId?.substring(0, 8)}...</div>
          <div>AppID: {applicationId?.substring(0, 8)}...</div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="container mx-auto py-12 px-4 min-h-[80vh] flex items-center justify-center">
      {renderDebugInfo()}
      <Card className="max-w-2xl mx-auto w-full">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 bg-primary/10 p-4 rounded-full w-16 h-16 flex items-center justify-center">
            <CheckCircle2 className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl sm:text-3xl">Thank You for Your Interview!</CardTitle>
          <CardDescription className="text-lg">
            Your interview has been successfully completed
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 text-center">
          <p>
            Thank you for taking the time to complete an interview for
            {role ? ` the ${role.title} position` : ' this position'}.
          </p>

          <div className="bg-muted/50 p-4 rounded-md text-left space-y-2">
            <h3 className="font-medium">What happens next?</h3>
            <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
              <li>Our hiring team will review your interview</li>
              <li>If you&apos;re a good fit, we&apos;ll contact you for the next steps</li>
              <li>You may be asked to participate in additional interviews</li>
              <li>The hiring process typically takes 1-2 weeks</li>
            </ul>
          </div>

          <p className="text-sm text-muted-foreground">
            If you have any questions about your application, please contact us at
            <a href="mailto:<EMAIL>" className="text-primary"> <EMAIL></a>
          </p>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            className="w-full sm:w-auto"
            onClick={handleGoHome}
          >
            <Home className="mr-2 h-4 w-4" />
            Return to Home
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}