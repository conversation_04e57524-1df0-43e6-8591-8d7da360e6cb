'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/Card';
import { Mail } from 'lucide-react';
import { RocketLaunchIcon, ClockIcon, BuildingOffice2Icon } from '@heroicons/react/24/outline';

export default function ContactSalesPage() {
    return (
        <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0 bg-[#2563eb]">
            <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
                <div className="absolute inset-0 bg-[#020817]" />
                <div className="relative z-20 flex flex-col h-full">
                    <div className="flex items-center text-lg font-medium">
                        <Link href="/" className="flex items-center">
                            <span className="logo-text text-3xl font-bold tracking-tight hover:text-gray-200 transition-colors">Recruiva</span>
                        </Link>
                    </div>
                    <div className="mt-auto space-y-8">
                        <div className="space-y-4 opacity-0 translate-y-8 animate-slide-up [animation-delay:200ms]">
                            <div className="flex items-center gap-3">
                                <RocketLaunchIcon className="w-8 h-8 text-blue-400" />
                                <h2 className="text-2xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                                    Transform Your Hiring
                                </h2>
                            </div>
                            <p className="bg-gradient-to-r from-gray-400 to-gray-300 bg-clip-text text-transparent text-lg leading-relaxed">
                                Streamline your recruitment process with AI-powered interviews and intelligent candidate matching.
                            </p>
                        </div>
                        <div className="space-y-4 opacity-0 translate-y-8 animate-slide-up [animation-delay:400ms]">
                            <div className="flex items-center gap-3">
                                <ClockIcon className="w-8 h-8 text-purple-400" />
                                <h2 className="text-2xl font-semibold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                                    Save Engineering Hours
                                </h2>
                            </div>
                            <p className="bg-gradient-to-r from-gray-400 to-gray-300 bg-clip-text text-transparent text-lg leading-relaxed">
                                Automate technical interviews and reduce screening time by up to 80% with AI-powered assessments.
                            </p>
                        </div>
                        <div className="space-y-4 opacity-0 translate-y-8 animate-slide-up [animation-delay:600ms]">
                            <div className="flex items-center gap-3">
                                <BuildingOffice2Icon className="w-8 h-8 text-blue-400" />
                                <h2 className="text-2xl font-semibold bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">
                                    Scale With Confidence
                                </h2>
                            </div>
                            <p className="bg-gradient-to-r from-gray-400 to-gray-300 bg-clip-text text-transparent text-lg leading-relaxed">
                                Built for teams of all sizes, from startups to enterprises.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div className="lg:p-8 bg-[#2563eb] min-h-screen flex items-center justify-center">
                <div className="mx-auto flex w-full flex-col justify-center sm:w-[350px]">
                    <Card className="bg-[#020817]/80 hover:bg-[#020817]/85 border-none shadow-xl backdrop-blur-sm transition-all duration-300">
                        <CardHeader>
                            <CardTitle className="text-white">Contact Sales</CardTitle>
                            <CardDescription className="text-gray-400">Ready to transform your recruitment process?</CardDescription>
                        </CardHeader>
                        <CardContent className="grid gap-4">
                            <div className="p-6 rounded-lg bg-[#1a1d24]/90 border border-gray-700/50">
                                <div className="flex flex-col items-center space-y-4">
                                    <Mail className="h-10 w-10 text-blue-400" />
                                    <div className="space-y-2">
                                        <h3 className="text-xl font-semibold text-white">Get in Touch</h3>
                                        <p className="text-gray-400">
                                            Contact us directly for personalized assistance with your recruitment needs.
                                        </p>
                                        <p className="text-blue-400 font-medium text-lg mt-4">
                                            <EMAIL>
                                        </p>
                                        <p className="text-gray-500 text-sm mt-1">
                                            Siavash Fahimi, Founder and CEO
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter className="flex flex-col gap-4">
                            <Button 
                                className="w-full bg-white/90 text-[#020817] hover:bg-white/95 active:bg-white/100 transition-all duration-200" 
                                size="lg" 
                                onClick={() => window.location.href = 'mailto:<EMAIL>?subject=Recruiva Sales Inquiry'}
                            >
                                Email Us Now
                            </Button>
                        </CardFooter>
                    </Card>
                </div>
            </div>
        </div>
    );
} 