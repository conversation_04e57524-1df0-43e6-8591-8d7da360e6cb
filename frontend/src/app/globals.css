@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@500;600;700&display=swap');

@layer base {
  :root {
    --font-montserrat: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    /* Light theme (default for :root) */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Updated for glassy effect */
    --card: 0 0% 100% / 0.85; /* Slightly transparent for glassy effect */
    --card-foreground: 222.2 84% 4.9%;

    /* Updated for glassy effect */
    --popover: 0 0% 100% / 0.9; /* Slightly transparent for glassy effect */
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    /* Updated for glassy effect */
    --secondary: 210 40% 96.1% / 0.85;
    --secondary-foreground: 222.2 47.4% 11.2%;

    /* Updated for glassy effect */
    --muted: 210 40% 96.1% / 0.75;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Updated for glassy effect */
    --accent: 210 40% 96.1% / 0.85;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4% / 0.8;
    --input: 214.3 31.8% 91.4% / 0.8;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;
  }

  /* Dark theme */
  .dark {
    /* Updated to a more sophisticated gray tone inspired by Spotify's dark theme */
    --background: 220 13% 14%; /* Changed from 222.2 84% 4.9% to a lighter gray tone */
    --foreground: 210 40% 98%;

    /* Updated for glassy effect with new background color */
    --card: 220 13% 14% / 0.75; /* More transparent for dark mode glassy effect */
    --card-foreground: 210 40% 98%;

    /* Updated for glassy effect with new background color */
    --popover: 220 13% 14% / 0.85; /* Slightly transparent for glassy effect */
    --popover-foreground: 210 40% 98%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    /* Updated for glassy effect */
    --secondary: 215 14% 21% / 0.8; /* Adjusted to match new background */
    --secondary-foreground: 210 40% 98%;

    /* Updated for glassy effect */
    --muted: 215 14% 21% / 0.7; /* Adjusted to match new background */
    --muted-foreground: 215 20.2% 65.1%;

    /* Updated for glassy effect */
    --accent: 215 14% 21% / 0.8; /* Adjusted to match new background */
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 215 14% 21% / 0.6; /* Adjusted to match new background */
    --input: 215 14% 21% / 0.6; /* Adjusted to match new background */
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Glass effect utilities */
  .glass-effect {
    @apply backdrop-blur-sm backdrop-saturate-[1.8] border border-opacity-40;
    @apply shadow-lg shadow-slate-200/20 dark:shadow-slate-900/30;
  }

  /* Light theme glass effect */
  .light-glass {
    @apply bg-white/85 border-white/30 backdrop-blur-[6px] backdrop-saturate-[1.8];
    @apply shadow-xl shadow-slate-200/30;
  }

  /* Dark theme glass effect */
  .dark-glass {
    @apply dark:bg-slate-800/60 dark:border-slate-700/40 dark:backdrop-blur-[6px] dark:backdrop-saturate-[1.4];
    @apply dark:shadow-xl dark:shadow-slate-950/20;
  }

  /* Light and dark mode compatible components */
  .logo-text {
    @apply text-2xl font-bold tracking-tight;
    font-family: var(--font-montserrat);
    background-image: linear-gradient(135deg, #2563EB 0%, #6D28D9 65%, #93185E 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Dark mode override for logo-text */
  .dark .logo-text {
    background-image: linear-gradient(135deg, #60A5FA 0%, #8B5CF7 65%, #C084FC 100%);
  }

  .active-nav-item {
    @apply bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-purple-400/20;
    color: #8B5CF7;
  }

  /* Interactive elements base styles */
  .interactive-base {
    @apply rounded-lg transition-all duration-200;
    @apply border-slate-200/80 bg-white/90 text-slate-700 dark:border-slate-700/40 dark:bg-slate-800/60 dark:text-slate-300;
    @apply backdrop-blur-[2px] backdrop-saturate-150 dark:backdrop-blur-[6px] dark:backdrop-saturate-[1.4];
    @apply dark:shadow-xl dark:shadow-slate-950/20;
  }

  .interactive-hover {
    @apply hover:border-slate-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20 dark:hover:border-slate-700/60;
    @apply hover:bg-gradient-to-r hover:from-blue-100/30 hover:via-purple-100/30 hover:to-purple-100/30 hover:text-slate-900;
    @apply dark:hover:bg-slate-800/70 dark:hover:text-white dark:focus:border-purple-500 dark:focus:ring-purple-500/20;
  }

  /* Buttons */
  .btn {
    @apply interactive-base px-4 py-2 font-semibold tracking-wide;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 via-blue-500 to-purple-500 text-white font-medium
    hover:from-blue-700 hover:via-blue-600 hover:to-purple-600
    shadow-[0_2px_8px_rgba(59,130,246,0.25)]
    hover:shadow-[0_4px_12px_rgba(59,130,246,0.35)]
    hover:translate-y-[-1px] transition-all;
    color: rgba(255, 255, 255, 1); /* Force fully opaque bright white text */
    text-shadow: 0 0 1px rgba(255, 255, 255, 0.5); /* Add subtle text shadow to enhance visibility */
  }

  .btn-secondary {
    @apply border border-blue-500/50 bg-white/10 text-blue-600
    hover:border-purple-500/50 hover:text-purple-600 hover:bg-white/20
    transition-all hover:translate-y-[-1px]
    shadow-[0_2px_4px_rgba(0,0,0,0.05)] translate-y-[-1px]
    hover:shadow-[0_3px_6px_rgba(0,0,0,0.08)]
    dark:border-slate-700/40 dark:bg-slate-800/60 dark:text-blue-400
    dark:hover:bg-slate-800/70 dark:hover:border-slate-700/60 dark:hover:text-purple-400
    dark:backdrop-blur-[6px] dark:backdrop-saturate-[1.4]
    dark:shadow-[0_2px_4px_rgba(0,0,0,0.2)] dark:hover:shadow-[0_3px_6px_rgba(0,0,0,0.25)];
  }

  .btn-ghost {
    @apply bg-transparent hover:bg-blue-100/50
    dark:text-slate-300 dark:hover:bg-slate-800/70 dark:hover:border-slate-700/60 dark:hover:text-white
    dark:backdrop-blur-[6px] dark:backdrop-saturate-[1.4];
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg;
  }

  /* Inputs and Textareas */
  .input-base {
    @apply interactive-base interactive-hover px-3 py-2;
    @apply placeholder:text-slate-400 dark:placeholder:text-slate-500 focus:outline-none;
  }

  /* Select/Dropdown */
  .select-base {
    @apply interactive-base interactive-hover appearance-none px-3 py-2 pr-8;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
  }

  /* Form inputs */
  .form-input {
    @apply input-base w-full;
  }

  .form-select {
    @apply select-base w-full;
  }

  .form-textarea {
    @apply input-base w-full min-h-[100px] resize-y;
  }

  .form-checkbox {
    @apply h-4 w-4 rounded text-indigo-500 transition-colors;
    @apply border-slate-300 bg-white/90 hover:border-slate-400 focus:ring-2 focus:ring-indigo-500/20;
    @apply dark:border-slate-700 dark:bg-slate-900/70 dark:text-purple-500 dark:hover:border-slate-600 dark:focus:ring-purple-500/20;
  }

  .form-radio {
    @apply h-4 w-4 rounded-full text-indigo-500 transition-colors;
    @apply border-slate-300 bg-white/90 hover:border-slate-400 focus:ring-2 focus:ring-indigo-500/20;
    @apply dark:border-slate-700 dark:bg-slate-900/70 dark:text-purple-500 dark:hover:border-slate-600 dark:focus:ring-purple-500/20;
  }

  /* Dropdown menu items */
  .dropdown-item {
    @apply interactive-base interactive-hover flex w-full items-center px-3 py-2 text-sm;
  }

  .gradient-heading {
    @apply text-transparent bg-clip-text;
    background-image: linear-gradient(to right, #4F46E5, #8B5CF6, #DB2777); /* Darker colors for light mode */
  }

  /* Dark mode override for gradient-heading */
  .dark .gradient-heading {
    background-image: linear-gradient(to right, #818CF8, #A78BFA, #F472B6); /* Original lighter colors for dark mode */
  }

  /* Utility class for gradient text styling */
  .gradient-text {
    @apply text-transparent bg-clip-text;
    background-image: linear-gradient(to right, #3730A3, #6D28D9, #9D174D); /* Darker colors for light mode */
  }

  /* Dark mode override for gradient-text */
  .dark .gradient-text {
    background-image: linear-gradient(to right, #818CF8, #A78BFA, #F472B6); /* Original lighter colors for dark mode */
  }

  .card-hover {
    @apply hover:bg-slate-100/80 transition-all duration-200 dark:hover:bg-slate-800/60;
    @apply backdrop-blur-sm backdrop-saturate-150;
  }

  .nav-link {
    @apply text-slate-700 hover:text-slate-900 transition-colors dark:text-slate-300 dark:hover:text-white;
  }

  /* Primary button with improved hover effect */
  .primary-button {
    @apply bg-indigo-500 hover:bg-indigo-600 text-white font-medium shadow-sm transition-all
    hover:translate-y-[-1px] hover:shadow-md;
  }

  /* Special button with gradient background */
  .gradient-button {
    @apply relative text-white font-medium shadow-md transition-all hover:translate-y-[-1px];
    background: linear-gradient(to right, #4F46E5, #8B5CF6, #DB2777);
  }

  .gradient-button:hover {
    filter: brightness(110%);
    box-shadow: 0 6px 15px rgba(79, 70, 229, 0.25);
  }

  /* Dark mode optimization for gradient button */
  .dark .gradient-button {
    filter: brightness(105%);
  }
  .dark .gradient-button:hover {
    filter: brightness(115%);
    box-shadow: 0 6px 15px rgba(129, 140, 248, 0.25);
  }

  /* Secondary button with consistent height and raised effect */
  .secondary-button {
    @apply text-slate-900 border border-slate-800 hover:bg-slate-100 transition-all
    dark:text-white dark:border-white dark:hover:bg-white/10
    shadow-[0_2px_4px_rgba(0,0,0,0.05)] translate-y-[-1px]
    hover:shadow-[0_3px_6px_rgba(0,0,0,0.08)] hover:translate-y-[-1px]
    dark:shadow-[0_2px_4px_rgba(0,0,0,0.2)] dark:hover:shadow-[0_3px_6px_rgba(0,0,0,0.25)];
  }

  .feature-card {
    @apply p-6 rounded-lg transition-all;
    @apply border border-slate-200/80 bg-white/85 hover:bg-slate-50/90;
    @apply dark:border-slate-700/60 dark:bg-slate-800/60 dark:hover:bg-slate-800/70;
    @apply backdrop-blur-[4px] backdrop-saturate-[1.5] shadow-lg shadow-slate-200/20 dark:shadow-slate-900/20;
  }

  /* Card styles for light and dark mode - updated for glassy effect */
  .card {
    @apply rounded-lg border shadow-sm transition-all duration-200;
    @apply border-slate-200/80 bg-white/70 text-slate-900; /* Light mode with transparency */
    @apply dark:border-slate-700/60 dark:bg-slate-800/80 dark:text-slate-300; /* Dark mode with transparency */
    @apply hover:shadow-md hover:shadow-slate-200/50; /* Light mode hover */
    @apply dark:hover:shadow-lg dark:hover:shadow-slate-900/20; /* Dark mode hover */
    /* Glass effect */
    @apply backdrop-blur-[4px] backdrop-saturate-[1.5];
  }

  /* Make inputs and form elements light mode friendly with glass effect */
  .form-input, .form-select, .form-textarea {
    @apply bg-white/85 dark:bg-slate-900/70; /* Semi-transparent backgrounds */
    @apply border-slate-200/80 dark:border-slate-700/60; /* Semi-transparent borders */
    @apply text-slate-900 dark:text-slate-300;
    @apply placeholder:text-slate-400 dark:placeholder:text-slate-500;
    @apply focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20;
    @apply dark:focus:border-purple-500 dark:focus:ring-2 dark:focus:ring-purple-500/20;
    @apply backdrop-blur-[2px] backdrop-saturate-[1.3]; /* Subtle glass effect */
  }

  /* Dropdown styles for light and dark mode with glass effect */
  .dropdown-menu {
    @apply bg-white/90 dark:bg-slate-900/80; /* Semi-transparent backgrounds */
    @apply border-slate-200/80 dark:border-slate-700/60; /* Semi-transparent borders */
    @apply text-slate-900 dark:text-slate-300;
    @apply shadow-lg shadow-slate-200/50 dark:shadow-slate-900/50;
    @apply backdrop-blur-[5px] backdrop-saturate-[1.5]; /* Glass effect */
  }

  /* Badge styles for light and dark mode */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors;
    @apply border-slate-200 bg-slate-100 text-slate-900; /* Light mode */
    @apply dark:border-slate-700 dark:bg-slate-800 dark:text-slate-300; /* Dark mode */
  }

  .badge-primary {
    @apply bg-indigo-100 text-indigo-700; /* Light mode */
    @apply dark:bg-indigo-900/50 dark:text-indigo-300; /* Dark mode */
  }
}

/* Custom scrollbar styles */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.gray.300') theme('colors.gray.100');
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: theme('colors.gray.100');
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: theme('colors.gray.300');
    border-radius: 4px;
    border: 2px solid theme('colors.gray.100');
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: theme('colors.gray.400');
  }

  /* Enhanced skeleton loading animation */
  .animate-skeleton-wave {
    background: linear-gradient(
      90deg,
      rgba(71, 85, 105, 0.3) 0%,
      rgba(51, 65, 85, 0.6) 50%,
      rgba(71, 85, 105, 0.3) 100%
    );
    background-size: 200% 100%;
    animation: skeleton-wave 4s ease-in-out infinite;
  }
}

/* Custom animations */
@layer utilities {
  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  .animate-slide-in {
    animation: slide-in 0.3s ease-out;
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slide-in {
    from {
      transform: translateY(10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(2rem);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%) rotate(45deg);
    }
    100% {
      transform: translateX(100%) rotate(45deg);
    }
  }

  @keyframes skeleton-wave {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .animate-slide-up {
    animation: slide-up 0.8s ease-out forwards;
  }
}

/* Custom focus styles */
@layer base {
  :focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2;
  }
}

/* Custom form styles */
@layer components {
  .form-input {
    @apply block w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .form-select {
    @apply block w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .form-checkbox {
    @apply h-4 w-4 rounded border border-input ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .form-radio {
    @apply h-4 w-4 rounded-full border border-input ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
}

/* Custom card styles */
@layer components {
  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-2xl font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
}

/* Custom badge styles */
@layer components {
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  }

  .badge-primary {
    @apply border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
  }

  .badge-secondary {
    @apply border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .badge-destructive {
    @apply border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80;
  }

  .badge-outline {
    @apply border-border bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground;
  }
}

/* Custom dark mode optimized prose styling for browser compatibility */
.dark-optimized-prose {
  /* Base text color for all content */
  color: #e2e8f0; /* slate-200 */

  /* Headings */
  & h1, & h2, & h3, & h4, & h5, & h6 {
    color: #f8fafc; /* slate-50 */
    font-weight: 600;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
  }

  & h1 {
    font-size: 2.25em;
    line-height: 1.2;
  }

  & h2 {
    font-size: 1.875em;
    line-height: 1.3;
  }

  & h3 {
    font-size: 1.5em;
    line-height: 1.4;
  }

  /* Paragraphs and spacing */
  & p {
    margin-top: 1em;
    margin-bottom: 1em;
    color: #e2e8f0; /* slate-200 */
  }

  /* Links */
  & a {
    color: #93c5fd; /* blue-300 */
    text-decoration: underline;
    text-underline-offset: 2px;
  }

  & a:hover {
    color: #bfdbfe; /* blue-200 */
  }

  /* Lists */
  & ul, & ol {
    color: #e2e8f0; /* slate-200 */
    margin-top: 1em;
    margin-bottom: 1em;
    padding-left: 1.5em;
  }

  & li {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
  }

  /* Blockquotes */
  & blockquote {
    border-left-width: 4px;
    border-left-color: #475569; /* slate-600 */
    padding-left: 1em;
    font-style: italic;
    color: #cbd5e1; /* slate-300 */
  }

  /* Code blocks */
  & pre {
    background-color: #1e293b; /* slate-800 */
    color: #e2e8f0; /* slate-200 */
    overflow-x: auto;
    font-size: 0.875em;
    line-height: 1.7;
    margin-top: 1.6em;
    margin-bottom: 1.6em;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
  }

  & code {
    background-color: #334155; /* slate-700 */
    color: #e2e8f0; /* slate-200 */
    padding: 0.25rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
  }

  & pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
    font-size: inherit;
  }

  /* Tables */
  & table {
    width: 100%;
    table-layout: auto;
    text-align: left;
    margin-top: 2em;
    margin-bottom: 2em;
    border-collapse: collapse;
  }

  & th {
    font-weight: 600;
    border-bottom-width: 1px;
    border-bottom-color: #475569; /* slate-600 */
    padding: 0.5rem;
    color: #f8fafc; /* slate-50 */
  }

  & td {
    padding: 0.5rem;
    border-bottom-width: 1px;
    border-bottom-color: #334155; /* slate-700 */
  }

  /* Horizontal Rule */
  & hr {
    border-color: #475569; /* slate-600 */
    margin-top: 2em;
    margin-bottom: 2em;
  }
}

/* Custom light mode optimized prose styling for browser compatibility */
.light-optimized-prose {
  /* Base text color for all content */
  color: #1e293b; /* slate-800 */

  /* Headings */
  & h1, & h2, & h3, & h4, & h5, & h6 {
    color: #0f172a; /* slate-900 */
    font-weight: 600;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
  }

  & h1 {
    font-size: 2.25em;
    line-height: 1.2;
  }

  & h2 {
    font-size: 1.875em;
    line-height: 1.3;
  }

  & h3 {
    font-size: 1.5em;
    line-height: 1.4;
  }

  /* Paragraphs and spacing */
  & p {
    margin-top: 1em;
    margin-bottom: 1em;
    color: #1e293b; /* slate-800 */
  }

  /* Links */
  & a {
    color: #2563eb; /* blue-600 */
    text-decoration: underline;
    text-underline-offset: 2px;
  }

  & a:hover {
    color: #1d4ed8; /* blue-700 */
  }

  /* Lists */
  & ul, & ol {
    color: #1e293b; /* slate-800 */
    margin-top: 1em;
    margin-bottom: 1em;
    padding-left: 1.5em;
  }

  & li {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
  }

  /* Blockquotes */
  & blockquote {
    border-left-width: 4px;
    border-left-color: #cbd5e1; /* slate-300 */
    padding-left: 1em;
    font-style: italic;
    color: #475569; /* slate-600 */
  }

  /* Code blocks */
  & pre {
    background-color: #f8fafc; /* slate-50 */
    color: #0f172a; /* slate-900 */
    overflow-x: auto;
    font-size: 0.875em;
    line-height: 1.7;
    margin-top: 1.6em;
    margin-bottom: 1.6em;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0; /* slate-200 */
  }

  & code {
    background-color: #f1f5f9; /* slate-100 */
    color: #0f172a; /* slate-900 */
    padding: 0.25rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
  }

  & pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
    font-size: inherit;
  }

  /* Tables */
  & table {
    width: 100%;
    table-layout: auto;
    text-align: left;
    margin-top: 2em;
    margin-bottom: 2em;
    border-collapse: collapse;
  }

  & th {
    font-weight: 600;
    border-bottom-width: 1px;
    border-bottom-color: #cbd5e1; /* slate-300 */
    padding: 0.5rem;
    color: #0f172a; /* slate-900 */
  }

  & td {
    padding: 0.5rem;
    border-bottom-width: 1px;
    border-bottom-color: #e2e8f0; /* slate-200 */
  }

  /* Horizontal Rule */
  & hr {
    border-color: #cbd5e1; /* slate-300 */
    margin-top: 2em;
    margin-bottom: 2em;
  }
}

/* Phone Input Styling */
.PhoneInput {
  display: flex;
  align-items: center;
  border-radius: 0.375rem;
  border: 1px solid hsl(var(--input-border));
  background-color: hsl(var(--input));
  color: hsl(var(--input-foreground));
  min-height: 2.5rem;
  padding: 0 0.75rem;
}

.PhoneInput:focus-within {
  outline: 2px solid transparent;
  outline-offset: 2px;
  ring-offset-width: 2px;
  ring-offset-color: hsl(var(--background));
  ring-color: hsl(var(--ring));
  ring-width: 2px;
}

.PhoneInputCountry {
  display: flex;
  align-items: center;
  margin-right: 0.5rem;
}

.PhoneInputInput {
  flex: 1;
  border: none;
  background: transparent;
  padding: 0.5rem 0;
  color: hsl(var(--input-foreground));
  font-size: 0.875rem;
  outline: none;
}

.PhoneInputCountryIcon {
  width: 1.5rem;
  height: 1rem;
  border-radius: 0.125rem;
  margin-right: 0.5rem;
}

.PhoneInputCountrySelectArrow {
  border-style: solid;
  border-width: 0.25rem 0.25rem 0 0.25rem;
  border-color: hsl(var(--muted-foreground)) transparent transparent transparent;
  margin-left: 0.5rem;
  opacity: 0.7;
}
