// File: frontend/src/app/page.tsx
'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { ArrowRight, Bot, Clock, Target, Briefcase, Menu, X, Video, <PERSON><PERSON>hart, Zap, Award } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { Montserrat } from 'next/font/google';
import { useTheme } from 'next-themes';
import { useState, useEffect } from 'react';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { cn } from '@/lib/utils/styles';
import ROICalculator from '@/components/ROICalculator';
import { FlippingBanner } from '@/components/ui/FlippingBanner';

const montserrat = Montserrat({ subsets: ['latin'] });

export default function LandingPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0); // 0 to 1 for smooth transition

  // Prevent hydration mismatch and handle scroll events
  useEffect(() => {
    setMounted(true);

    // Add scroll event listener with throttling for smoother transition
    let lastScrollY = window.scrollY;
    let ticking = false;

    const handleScroll = () => {
      lastScrollY = window.scrollY;

      if (!ticking) {
        window.requestAnimationFrame(() => {
          // Calculate a smooth progress value between 0 and 1
          // Transition occurs between 0px and 50px of scroll
          const progress = Math.min(1, Math.max(0, lastScrollY / 50));
          setScrollProgress(progress);
          ticking = false;
        });

        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll);

    // Initial check
    handleScroll();

    // Clean up
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isDarkMode = mounted && (resolvedTheme === 'dark' || theme === 'dark');

  const handleSignInClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (user) {
      router.push('/dashboard');
    } else {
      router.push('/auth/signin');
    }
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className={cn(
      "min-h-screen transition-colors duration-300 relative",
      // Theme-specific backgrounds
      isDarkMode
        ? "bg-background"
        : "bg-gradient-to-br from-orange-50/80 via-pink-50/70 to-purple-50/80",
      montserrat.className
    )}>
      {/* Navigation - Sticky Header */}
      <nav
        className={cn(
          "sticky top-0 z-50 w-full",
          // Use longer duration for smoother transition
          "transition-all duration-500 ease-in-out",
          // Dynamic padding based on scroll progress
          "transition-padding duration-500 ease-in-out",
          scrollProgress > 0.5 ? "py-2" : "py-4",
          // No background color in the className to ensure transparency
          "bg-transparent"
        )}
        style={{
          // Apply a dynamic backdrop blur based on scroll progress
          backdropFilter: `blur(${scrollProgress * 5}px)`,
          WebkitBackdropFilter: `blur(${scrollProgress * 5}px)`, // For Safari support
          // Keep the background transparent to maintain the same appearance
          backgroundColor: 'transparent'
        }}
      >
        <div className="container mx-auto px-4 sm:px-6 flex justify-between items-center transition-all duration-500 ease-in-out">
        <Link
          href="/pitch"
          className="logo-text text-2xl sm:text-3xl md:text-4xl font-bold transition-all duration-500 ease-in-out"
          style={{
            // Use CSS transform to smoothly scale the logo based on scroll progress
            transform: `scale(${1 - (scrollProgress * 0.15)})`,
            transformOrigin: 'left center'
          }}
        >Recruiva</Link>

        {/* Mobile menu button */}
        <div className="md:hidden">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
            className={cn(
              isDarkMode
                ? "text-white hover:text-white hover:bg-slate-800/70 hover:border-slate-700/60"
                : "text-slate-700 hover:text-slate-900 hover:bg-slate-200/50"
            )}
          >
            {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>

        {/* Desktop navigation */}
        <div className="hidden md:flex md:space-x-4 md:items-center">
          <Link href="/jobs">
            <Button
              variant="ghost"
              className={cn(
                "flex items-center gap-2",
                isDarkMode
                  ? "text-white hover:text-white hover:bg-slate-800/70 hover:border-slate-700/60 hover:translate-y-[-1px] hover:shadow-md hover:shadow-slate-950/20"
                  : "text-slate-700 hover:text-slate-900 hover:bg-slate-200/50 hover:translate-y-[-1px] hover:shadow-sm"
              )}
            >
              <Briefcase className="h-4 w-4" />
              Job Board
            </Button>
          </Link>
          <Button
            variant="ghost"
            className={cn(
              isDarkMode
                ? "text-white hover:text-white hover:bg-slate-800/70 hover:border-slate-700/60 hover:translate-y-[-1px] hover:shadow-md hover:shadow-slate-950/20"
                : "text-slate-700 hover:text-slate-900 hover:bg-slate-200/50 hover:translate-y-[-1px] hover:shadow-sm"
            )}
            onClick={handleSignInClick}
            disabled={loading}
          >
            {user ? 'Go to Dashboard' : 'Sign In'}
          </Button>
          <Link href="/contact-sales">
            <Button variant="default" className="gap-2 font-medium [color:white]">
              Contact Sales
            </Button>
          </Link>
        </div>
        </div>
      </nav>

      {/* Mobile menu - positioned below the sticky header */}
      {mobileMenuOpen && (
        <div
          className="md:hidden fixed left-0 right-0 z-40 bg-background border-b dark:border-slate-800 transition-all duration-500 ease-in-out animate-in slide-in-from-top-5"
          style={{
            // Smoothly adjust top position based on scroll progress
            top: `${60 - (scrollProgress * 8)}px`
          }}
        >
          <div className="container mx-auto px-4 py-4 flex flex-col space-y-4">
            <Link href="/jobs" onClick={() => setMobileMenuOpen(false)}>
              <Button
                variant="ghost"
                className="w-full justify-start hover:translate-y-[-1px] hover:shadow-sm dark:hover:shadow-md dark:hover:shadow-slate-950/20"
              >
                <Briefcase className="h-4 w-4 mr-2" />
                Job Board
              </Button>
            </Link>
            <Button
              variant="ghost"
              className="w-full justify-start hover:translate-y-[-1px] hover:shadow-sm dark:hover:shadow-md dark:hover:shadow-slate-950/20"
              onClick={(e) => {
                handleSignInClick(e);
                setMobileMenuOpen(false);
              }}
              disabled={loading}
            >
              {user ? 'Go to Dashboard' : 'Sign In'}
            </Button>
            <Link href="/contact-sales" onClick={() => setMobileMenuOpen(false)}>
              <Button variant="default" className="w-full justify-center gap-2 font-medium [color:rgba(255,255,255,1)] [text-shadow:0_0_1px_rgba(255,255,255,0.5)]">
                Schedule A Demo
              </Button>
            </Link>
          </div>
        </div>
      )}

      {/* Hero Section with Flipping Banner */}
      <section className="container mx-auto px-4 sm:px-6 pt-2 pb-10 sm:pt-4 sm:pb-16">
        <FlippingBanner
          features={[
            {
              title: "Transform Your Hiring Process with AI",
              description: "Experience the future of recruitment where AI conducts intelligent interviews, evaluates candidates holistically, and autonomously advances the best candidates through the funnel.",
              icon: <Zap className={cn("w-12 h-12", isDarkMode ? "text-indigo-400" : "text-indigo-600")} />
            },
            {
              title: "80% Faster Hiring Process",
              description: "Cut your time-to-hire dramatically with AI-powered interviews available 24/7, eliminating scheduling delays and accelerating candidate evaluation.",
              icon: <Clock className={cn("w-12 h-12", isDarkMode ? "text-purple-400" : "text-purple-600")} />
            },
            {
              title: "Smart Adaptive Interviews",
              description: "Our AI conducts human-like conversations with adaptive questioning based on candidate responses, ensuring a thorough and personalized assessment.",
              icon: <Video className={cn("w-12 h-12", isDarkMode ? "text-pink-400" : "text-pink-600")} />
            },
            {
              title: "Holistic Candidate Evaluation",
              description: "Comprehensive assessment of technical skills, soft skills, and cultural fit through multi-dimensional analysis of candidate responses.",
              icon: <BarChart className={cn("w-12 h-12", isDarkMode ? "text-indigo-400" : "text-indigo-600")} />
            },
            {
              title: "Autonomous Candidate Advancement",
              description: "AI automatically identifies and advances qualified candidates through your recruitment funnel, saving time and reducing manual screening efforts.",
              icon: <Award className={cn("w-12 h-12", isDarkMode ? "text-purple-400" : "text-purple-600")} />
            }
          ]}
          interval={6000}
          className="mb-8"
        />

        <div className="flex flex-col sm:flex-row justify-center gap-4 sm:gap-8 mt-8 text-center">
          <div className="flex flex-col items-center gap-2">
            <span className={cn(
              "text-sm",
              isDarkMode ? "text-slate-400" : "text-slate-600"
            )}>
              See Recruiva in Action
            </span>
            {user ? (
              <Button
                variant="secondary"
                size="lg"
                className="w-full sm:w-auto gap-2"
                onClick={() => router.push('/dashboard')}
              >
                Go to Dashboard <ArrowRight className="h-4 w-4" />
              </Button>
            ) : (
              <Link href="https://calendar.app.google/8xKkS5tMBb2MA8bR9" className="w-full sm:w-auto">
                <Button
                  variant="default"
                  size="lg"
                  className="w-full sm:w-auto gap-2 font-medium [color:rgba(255,255,255,1)] [text-shadow:0_0_1px_rgba(255,255,255,0.5)]"
                >
                  Schedule A Demo <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
            )}
          </div>
          {!user && (
            <div className="flex flex-col items-center gap-2 mt-4 sm:mt-0">
              <span className={cn(
                "text-sm",
                isDarkMode ? "text-slate-400" : "text-slate-600"
              )}>
                Already have an account?
              </span>
              <Button
                size="lg"
                variant="secondary"
                className="w-full sm:w-auto gap-2"
                onClick={handleSignInClick}
                disabled={loading}
              >
                Sign In to Dashboard <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </section>

      {/* ROI Calculator Section */}
      <section className="container mx-auto px-4 sm:px-6 pt-0 pb-6 sm:pt-0 sm:pb-10">
        <h2 className={cn(
          "text-xl sm:text-2xl font-bold mb-2 text-center",
          "text-transparent bg-clip-text bg-gradient-to-r",
          isDarkMode
            ? "from-indigo-400 via-purple-400 to-pink-400"
            : "from-indigo-600 via-purple-600 to-pink-600"
        )}>
          See How Much You Can Save
        </h2>
        <ROICalculator isDarkMode={isDarkMode} />
      </section>

      {/* Features Grid */}
      <section className="container mx-auto px-4 sm:px-6 py-10 sm:py-20">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
          <FeatureCard
            icon={<Clock className={cn("w-8 h-8", isDarkMode ? "text-indigo-400" : "text-indigo-600")} />}
            title="80% Faster Hiring"
            description="Cut your time-to-hire dramatically with AI-powered interviews available 24/7"
            isDarkMode={isDarkMode}
          />
          <FeatureCard
            icon={<Video className={cn("w-8 h-8", isDarkMode ? "text-purple-400" : "text-purple-600")} />}
            title="Smart Interviews"
            description="Advanced AI conducts adaptive interviews, ensuring consistent evaluation"
            isDarkMode={isDarkMode}
          />
          <FeatureCard
            icon={<Target className={cn("w-8 h-8", isDarkMode ? "text-pink-400" : "text-pink-600")} />}
            title="Precise Matching"
            description="AI analyzes responses in real-time to identify the perfect candidates"
            isDarkMode={isDarkMode}
          />
          <FeatureCard
            icon={<Bot className={cn("w-8 h-8", isDarkMode ? "text-indigo-400" : "text-indigo-600")} />}
            title="Autonomous ATS"
            description="End-To-End automation, from role definition to hire"
            isDarkMode={isDarkMode}
          />
        </div>
      </section>

      {/* Contact Sales Section */}
      <section className="container mx-auto px-4 sm:px-6 py-10 sm:py-20 text-center">
        <h2 className={cn(
          "text-2xl sm:text-3xl font-bold mb-4 sm:mb-8",
          isDarkMode ? "text-white" : "text-slate-800"
        )}>
          Ready to Transform Your Hiring?
        </h2>
        <p className={cn(
          "mb-6 sm:mb-8",
          isDarkMode ? "text-slate-300" : "text-slate-600"
        )}>
          Contact our team to learn how Recruiva can streamline your recruitment process.
        </p>
        <Link href={`mailto:<EMAIL>`}>
          <Button size="lg" className="bg-indigo-500 hover:bg-indigo-600 text-white font-medium shadow-sm [color:rgba(255,255,255,1)] [text-shadow:0_0_1px_rgba(255,255,255,0.5)]">
            Contact Sales Team
          </Button>
        </Link>
      </section>

      {/* Fixed theme toggle button in bottom right */}
      <div className="fixed bottom-6 right-6 z-50">
        <div className="bg-card shadow-lg rounded-full p-2">
          <ThemeToggle />
        </div>
      </div>
    </div>
  );
}

function FeatureCard({
  icon,
  title,
  description,
  isDarkMode
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  isDarkMode: boolean;
}) {
  return (
    <div className={cn(
      "p-6 rounded-lg border transition-all",
      isDarkMode
        ? "border-slate-700/40 bg-slate-800/60 hover:bg-slate-800/70 shadow-xl shadow-slate-950/20 backdrop-blur-[6px] backdrop-saturate-[1.4]"
        : "border-slate-200 bg-white/85 hover:bg-white shadow-sm"
    )}>
      <div className="mb-4">{icon}</div>
      <h3 className={cn(
        "text-xl font-semibold mb-2",
        isDarkMode ? "text-white" : "text-slate-800"
      )}>
        {title}
      </h3>
      <p className={isDarkMode ? "text-slate-300" : "text-slate-600"}>
        {description}
      </p>
    </div>
  );
}
