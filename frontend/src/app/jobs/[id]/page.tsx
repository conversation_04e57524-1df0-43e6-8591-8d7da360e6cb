'use client';

import { useEffect, useState } from 'react';
import { Role } from '@/types/role';
import { publicRolesService } from '@/services/roles/public-service';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ArrowLeft, MapPin, Briefcase, DollarSign, Calendar, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React from 'react';
import { preprocessMarkdown, formatPostedDate } from '@/lib/utils';
import { Markdown } from '@/components/ui/Markdown';
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils/styles';

/**
 * Public job details page that displays job posting information
 * without requiring authentication
 * Supports both light and dark themes
 */
export default function JobDetailsPage({ params }: { params: { id: string } }) {
  const [role, setRole] = useState<Role | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const isDarkMode = mounted && (resolvedTheme === 'dark' || theme === 'dark');

  useEffect(() => {
    const fetchRole = async () => {
      try {
        console.log(`JobDetailsPage: Fetching role with ID: ${params?.id}`);
        setLoading(true);
        const data = await publicRolesService.getPublicRole(params?.id);

        // If role doesn't exist, redirect to jobs page
        if (!data) {
          console.warn('JobDetailsPage: Role not found, redirecting to jobs page');
          router.push('/jobs');
          return;
        }

        // Check if role is published - this is the only required condition
        if (!data.isPublished) {
          console.warn(`JobDetailsPage: Role is not published, redirecting to jobs page`);
          router.push('/jobs');
          return;
        }

        // Log if there's no job posting but still show the page
        if (!data.jobPosting) {
          console.warn('JobDetailsPage: Role does not have a job posting, showing placeholder content');
        }

        console.log('JobDetailsPage: Successfully fetched role:', {
          id: data.id,
          title: data.title,
          status: data.status,
          isPublished: data.isPublished,
          jobPostingLength: data.jobPosting ? data.jobPosting.length : 0
        });

        setRole(data);
      } catch (err) {
        console.error('Error fetching role:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchRole();
  }, [params.id, router]);

  // Format salary range
  const formatSalary = (min: string | number, max: string | number, currency: string = 'USD') => {
    try {
      const formatter = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency,
        maximumFractionDigits: 0
      });
      return `${formatter.format(Number(min))} - ${formatter.format(Number(max))}`;
    } catch (error) {
      console.error('Error formatting salary:', error);
      return `${min} - ${max} ${currency}`;
    }
  };

  // Use the utility function for date formatting
  const getPostedDate = (dateString?: string) => {
    return formatPostedDate(dateString);
  };

  // Process job posting to handle markdown code fence markers and other formatting issues
  const sanitizedJobPosting = React.useMemo(() => {
    return preprocessMarkdown(role?.jobPosting);
  }, [role?.jobPosting]);

  if (loading) {
    return (
      <div className={cn(
        "flex items-center justify-center min-h-screen",
        isDarkMode
          ? "bg-background"
          : "bg-gradient-to-br from-orange-50/80 via-pink-50/70 to-purple-50/80"
      )}>
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !role) {
    return (
      <div className={cn(
        "flex items-center justify-center min-h-screen",
        isDarkMode
          ? "bg-background"
          : "bg-gradient-to-br from-orange-50/80 via-pink-50/70 to-purple-50/80"
      )}>
        <div className="text-destructive">
          <h2 className="text-xl font-semibold mb-2">Error Loading Job</h2>
          <p>{error || 'Job not found'}</p>
          <Link href="/jobs">
            <Button
              className="mt-4"
              variant="secondary"
            >
              Back to Jobs
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "min-h-screen transition-colors duration-300",
      isDarkMode
        ? "bg-background"
        : "bg-gradient-to-br from-orange-50/80 via-pink-50/70 to-purple-50/80"
    )}>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link href="/jobs">
            <Button variant="ghost" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Jobs
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main content */}
          <div className="lg:col-span-2">
            <Card className={cn(
              "backdrop-blur-[6px] backdrop-saturate-[1.4]",
              isDarkMode
                ? "bg-slate-800/60 border-slate-700/40 shadow-xl shadow-slate-950/20"
                : "bg-white/70 border-slate-200/70 shadow-sm shadow-slate-200/30"
            )}>
              <CardHeader>
                <div className="space-y-2">
                  <h2 className={cn(
                    "text-[2.6rem] font-bold text-transparent bg-clip-text bg-gradient-to-r",
                    isDarkMode
                      ? "from-indigo-400 via-purple-400 to-pink-400"
                      : "from-indigo-600 via-purple-600 to-pink-600"
                  )}>
                    {role.title}
                  </h2>
                  {role.team && (
                    <p className={cn(
                      "text-lg font-medium",
                      isDarkMode ? "text-slate-300" : "text-slate-700"
                    )}>
                      {role.team}
                    </p>
                  )}
                  <div className="flex flex-wrap gap-4 text-sm pt-2">
                    {/* Location */}
                    {role.location && (
                      <div className={cn(
                        "flex items-center gap-2",
                        isDarkMode ? "text-slate-300" : "text-slate-700"
                      )}>
                        <MapPin className={cn(
                          "h-4 w-4 shrink-0",
                          isDarkMode ? "text-slate-400" : "text-slate-500"
                        )} />
                        <span>
                          {role.location.city ? `${role.location.city} · ` : ''}
                          {role.location.remoteStatus || role.location.type || 'Remote'}
                        </span>
                      </div>
                    )}

                    {/* Job Type */}
                    {role.jobType && (
                      <div className={cn(
                        "flex items-center gap-2",
                        isDarkMode ? "text-slate-300" : "text-slate-700"
                      )}>
                        <Briefcase className={cn(
                          "h-4 w-4 shrink-0",
                          isDarkMode ? "text-slate-400" : "text-slate-500"
                        )} />
                        <span>{role.jobType}</span>
                      </div>
                    )}

                    {/* Compensation */}
                    {role.compensation && role.compensation.min && role.compensation.max &&
                     Number(role.compensation.min) > 0 && Number(role.compensation.max) > 0 && (
                      <div className={cn(
                        "flex items-center gap-2",
                        isDarkMode ? "text-slate-300" : "text-slate-700"
                      )}>
                        <DollarSign className={cn(
                          "h-4 w-4 shrink-0",
                          isDarkMode ? "text-slate-400" : "text-slate-500"
                        )} />
                        <span>
                          {formatSalary(role.compensation.min, role.compensation.max, role.compensation.currency)}
                        </span>
                      </div>
                    )}

                    {/* Posted date */}
                    <div className={cn(
                      "flex items-center gap-2",
                      isDarkMode ? "text-slate-300" : "text-slate-700"
                    )}>
                      <Calendar className={cn(
                        "h-4 w-4 shrink-0",
                        isDarkMode ? "text-slate-400" : "text-slate-500"
                      )} />
                      <span>
                        Posted {getPostedDate(role.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {role.jobPosting ? (
                  <div className="prose prose-slate prose-sm max-w-none text-slate-700 dark:prose-invert">
                    <Markdown
                      content={sanitizedJobPosting}
                      isDarkOptimized={isDarkMode}
                      className="prose-headings:text-slate-800 dark:prose-headings:text-slate-200 prose-p:text-slate-700 dark:prose-p:text-slate-300 prose-li:text-slate-700 dark:prose-li:text-slate-300 prose-strong:text-slate-800 dark:prose-strong:text-slate-200 prose-em:text-slate-700 dark:prose-em:text-slate-300 prose-code:text-slate-800 dark:prose-code:text-slate-300 prose-pre:bg-slate-100 dark:prose-pre:bg-slate-800/80 prose-pre:text-slate-800 dark:prose-pre:text-slate-300 prose-a:text-blue-600 dark:prose-a:text-blue-400 hover:prose-a:text-blue-800 dark:hover:prose-a:text-blue-300 prose-ul:text-slate-700 dark:prose-ul:text-slate-300 prose-ol:text-slate-700 dark:prose-ol:text-slate-300 prose-blockquote:text-slate-700 dark:prose-blockquote:text-slate-300"
                    />
                  </div>
                ) : (
                  <div className="py-8 text-center">
                    <h3 className={cn(
                      "text-xl font-medium mb-4",
                      isDarkMode ? "text-slate-300" : "text-slate-700"
                    )}>
                      Job Description
                    </h3>
                    <p className={isDarkMode ? "text-slate-400" : "text-slate-600"}>
                      We&apos;re looking for talented professionals to join our team. Please apply to learn more about this role.
                    </p>
                    <div className={cn(
                      "mt-6 pt-6",
                      isDarkMode ? "border-t border-slate-700/40" : "border-t border-slate-200/70"
                    )}>
                      <p className={isDarkMode ? "text-slate-400 text-sm" : "text-slate-500 text-sm"}>
                        A complete job description will be available soon.
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div>
            <Card className={cn(
              "backdrop-blur-[6px] backdrop-saturate-[1.4] sticky top-6",
              isDarkMode
                ? "bg-slate-800/60 border-slate-700/40 shadow-xl shadow-slate-950/20"
                : "bg-white/70 border-slate-200/70 shadow-sm shadow-slate-200/30"
            )}>
              <CardHeader>
                <CardTitle className="text-xl">Apply for this position</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className={isDarkMode ? "text-slate-300" : "text-slate-700"}>
                  Interested in this role? Submit your application now.
                </p>
                <Button
                  className="w-full gap-2"
                  variant="default"
                  onClick={() => router.push(`/instant-interview/${role.id}`)}
                >
                  Instant Interview <ExternalLink className="h-4 w-4" />
                </Button>
                <p className={cn(
                  "mt-2 text-sm",
                  isDarkMode ? "text-slate-400" : "text-slate-600"
                )}>
                  Submit your application and proceed directly to an AI-powered interview. No account required.
                </p>

                {role.aboutCompany && (
                  <div className={cn(
                    "pt-6 mt-6",
                    isDarkMode ? "border-t border-slate-700/40" : "border-t border-slate-200/70"
                  )}>
                    <h3 className="text-lg font-semibold mb-2">About the Company</h3>
                    <p className={cn(
                      "text-sm",
                      isDarkMode ? "text-slate-300" : "text-slate-700"
                    )}>
                      {role.aboutCompany}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}