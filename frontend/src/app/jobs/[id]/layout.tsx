'use client';

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils/styles';

export default function JobDetailsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Determine if we're in dark mode - use resolved theme to account for system preference
  const isDarkMode = mounted && (resolvedTheme === 'dark' || theme === 'dark');

  if (!mounted) {
    // During SSR or before hydration, default to no specific classes to avoid flicker
    return <div className="min-h-screen">{children}</div>;
  }

  return (
    <div className={cn(
      "min-h-screen transition-colors duration-300 relative",
      // Apply theme-specific backgrounds
      isDarkMode ? "bg-background" : "bg-gradient-to-br from-orange-50/80 via-pink-50/70 to-purple-50/80"
    )}>
      {children}
    </div>
  );
} 