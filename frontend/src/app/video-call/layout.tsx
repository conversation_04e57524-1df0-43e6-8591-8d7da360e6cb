'use client';

import React, { useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { useRouter } from 'next/navigation';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { VideoCallThemeProvider } from '@/components/providers/video-call-theme-provider';

// Extend Window interface to include our custom property
declare global {
  interface Window {
    __FORCED_DARK_MODE?: boolean;
  }
}

export default function VideoCallLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();

  // Force dark mode at layout level
  useEffect(() => {
    // Ensure dark mode is applied at the document level
    document.documentElement.classList.add('dark');
    document.documentElement.setAttribute('data-force-theme', 'dark');
    
    return () => {
      // We don't remove the attributes on cleanup to avoid theme flashing
    };
  }, []);

  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black dark">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    router.push('/signin');
    return null;
  }

  return (
    <VideoCallThemeProvider>
      <div className="h-screen w-full bg-background dark" data-theme="dark" data-force-dark="true">
        {children}
        <style jsx global>{`
          html, body {
            height: 100%;
            overflow: hidden;
            margin: 0;
            padding: 0;
            background: hsl(220, 13%, 14%); /* Match our new dark theme background */
            color-scheme: dark;
          }
          
          html[data-force-theme="dark"] {
            --background: hsl(220, 13%, 14%); /* Match our new dark theme background */
            --foreground: #ffffff;
            --muted: hsl(215, 14%, 21%); /* Match our new muted color */
            --muted-foreground: #a3a3a3;
            color-scheme: dark;
          }
          
          /* Ensure all color schemes are overridden */
          [data-force-dark="true"] * {
            color-scheme: dark !important;
          }
        `}</style>
      </div>
    </VideoCallThemeProvider>
  );
} 