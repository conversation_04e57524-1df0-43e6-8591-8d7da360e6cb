'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { DarkModeVideoCall } from '@/components/video-call/DarkModeVideoCall';

export default function VideoCallPage() {
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);

  // Get call type and role ID from URL parameters
  const type = searchParams?.get('type') || 'intake';
  const roleId = searchParams?.get('roleId');
  const templateId = searchParams?.get('templateId');
  const isEnrichment = searchParams?.get('enrichment') === 'true';

  // Add debug logging
  useEffect(() => {
    console.log('VideoCallPage mounted with params:', {
      type,
      roleId,
      templateId,
      isEnrichment
    });

    // Force dark mode at page level
    document.documentElement.classList.add('dark');
  }, [type, roleId, templateId, isEnrichment]);

  useEffect(() => {
    // Short delay to allow components to initialize
    const loadingTimer = setTimeout(() => {
      setLoading(false);
      console.log('VideoCallPage finished loading');
    }, 1000);

    return () => clearTimeout(loadingTimer);
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-black dark">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Allow the page to be reused for different call types
  let pageTitle = 'Video Call';
  if (type === 'intake') {
    pageTitle = 'Role Intake Call';
  } else if (type === 'interview') {
    pageTitle = 'Candidate Interview';
  } else if (type === 'screening') {
    pageTitle = 'Candidate Screening';
  }

  return (
    <>
      <title>{pageTitle} | Recruiva</title>
      <meta name="description" content="Recruiva AI-powered interview session" />

      <div className="h-screen w-full flex flex-col overflow-hidden dark bg-black"
           data-theme="dark"
           data-force-dark="true"
           data-call-type={type}
           data-role-id={roleId || undefined}
           data-template-id={templateId || undefined}
           data-is-enrichment={isEnrichment}>
        <DarkModeVideoCall
          type={type}
          roleId={roleId || null}
          templateId={templateId || null}
          isEnrichment={isEnrichment}
        />
      </div>
    </>
  );
}