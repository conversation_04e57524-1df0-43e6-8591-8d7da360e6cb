'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/Card';
import { Label } from '@/components/ui/Label';
import { useAuth } from '@/contexts/auth-context';
import { FirebaseError } from 'firebase/app';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { RocketLaunchIcon, ClockIcon, BuildingOffice2Icon } from '@heroicons/react/24/outline';

export default function SignInPage() {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [localError, setLocalError] = useState<string | null>(null);
    const { user, error: authError, signInWithEmail, signInWithGoogle } = useAuth();
    const router = useRouter();
    const searchParams = useSearchParams();
    const callbackUrl = searchParams?.get('callbackUrl') || '/dashboard';

    // Redirect if user is already authenticated
    useEffect(() => {
        if (user) {
            console.log('[SignInPage] User already signed in, redirecting to dashboard', {
                email: user.email,
                callbackUrl
            });
            router.push(callbackUrl);
        }
    }, [user, router, callbackUrl]);

    // Update local error state when auth error changes
    useEffect(() => {
        if (authError) {
            console.log('[SignInPage] Auth error received', { authError });
            setLocalError(authError);
            setIsLoading(false);
        }
    }, [authError]);

    // If user is already signed in, don't render the sign-in form
    if (user) {
        return (
            <div className="flex h-screen items-center justify-center">
                <LoadingSpinner />
            </div>
        );
    }

    const getErrorMessage = (error: FirebaseError | Error) => {
        if (error instanceof FirebaseError) {
            switch (error.code) {
                case 'auth/invalid-email':
                    return 'Invalid email address format';
                case 'auth/user-disabled':
                    return 'This account has been disabled';
                case 'auth/user-not-found':
                    return 'No account found with this email';
                case 'auth/wrong-password':
                    return 'Invalid password';
                case 'auth/invalid-credential':
                    return 'Invalid email or password';
                case 'auth/too-many-requests':
                    return 'Too many failed attempts. Please try again later';
                default:
                    return error.message || 'An error occurred during sign in';
            }
        }
        return error.message || 'An error occurred during sign in';
    };

    const handleEmailSignIn = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        setLocalError(null);
        try {
            await signInWithEmail(email, password);
        } catch (error) {
            const errorMessage = getErrorMessage(error as Error);
            setLocalError(errorMessage);
            setIsLoading(false);
        }
    };

    const handleGoogleSignIn = async () => {
        setIsLoading(true);
        setLocalError(null);
        try {
            await signInWithGoogle();
            // Let the auth context handle success
        } catch (error) {
            const errorMessage = getErrorMessage(error as Error);
            setLocalError(errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0 bg-[#2563eb]">
            <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
                <div className="absolute inset-0 bg-[#020817]" />
                <div className="relative z-20 flex flex-col h-full">
                    <div className="flex items-center text-lg font-medium">
                        <Link href="/" className="flex items-center">
                            <span className="logo-text text-3xl font-bold tracking-tight hover:text-gray-200 transition-colors">Recruiva</span>
                        </Link>
                    </div>
                    <div className="mt-auto space-y-8">
                        <div className="space-y-4 opacity-0 translate-y-8 animate-slide-up [animation-delay:200ms]">
                            <div className="flex items-center gap-3">
                                <RocketLaunchIcon className="w-8 h-8 text-blue-400" />
                                <h2 className="text-2xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                                    Transform Your Hiring
                                </h2>
                            </div>
                            <p className="bg-gradient-to-r from-gray-400 to-gray-300 bg-clip-text text-transparent text-lg leading-relaxed">
                                Streamline your recruitment process with AI-powered interviews and intelligent candidate matching.
                            </p>
                        </div>
                        <div className="space-y-4 opacity-0 translate-y-8 animate-slide-up [animation-delay:400ms]">
                            <div className="flex items-center gap-3">
                                <ClockIcon className="w-8 h-8 text-purple-400" />
                                <h2 className="text-2xl font-semibold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                                    Save Engineering Hours
                                </h2>
                            </div>
                            <p className="bg-gradient-to-r from-gray-400 to-gray-300 bg-clip-text text-transparent text-lg leading-relaxed">
                                Automate technical interviews and reduce screening time by up to 80% with AI-powered assessments.
                            </p>
                        </div>
                        <div className="space-y-4 opacity-0 translate-y-8 animate-slide-up [animation-delay:600ms]">
                            <div className="flex items-center gap-3">
                                <BuildingOffice2Icon className="w-8 h-8 text-blue-400" />
                                <h2 className="text-2xl font-semibold bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">
                                    Scale With Confidence
                                </h2>
                            </div>
                            <p className="bg-gradient-to-r from-gray-400 to-gray-300 bg-clip-text text-transparent text-lg leading-relaxed">
                                Built for teams of all sizes, from startups to enterprises.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div className="lg:p-8 bg-[#2563eb] min-h-screen flex items-center justify-center">
                <div className="mx-auto flex w-full flex-col justify-center sm:w-[350px]">
                    <Card className="bg-[#020817]/80 hover:bg-[#020817]/85 border-none shadow-xl backdrop-blur-sm transition-all duration-300">
                        <CardHeader>
                            <CardTitle className="text-white">Welcome back</CardTitle>
                            <CardDescription className="text-gray-400">Sign in to your account to continue</CardDescription>
                        </CardHeader>
                        <form onSubmit={handleEmailSignIn}>
                            <CardContent className="grid gap-4">
                                {localError && (
                                    <Alert variant="destructive">
                                        <AlertDescription>
                                            <div dangerouslySetInnerHTML={{ __html: localError }} />
                                        </AlertDescription>
                                    </Alert>
                                )}
                                <div className="grid gap-2">
                                    <Label htmlFor="email" className="text-gray-300">Email</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="<EMAIL>"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        required
                                        disabled={isLoading}
                                        className="bg-[#1a1d24]/90 border-gray-700/50 text-white placeholder:text-gray-500 focus:border-gray-600/50 focus:ring-1 focus:ring-gray-600/30 hover:border-gray-600/70 transition-all duration-200"
                                    />
                                </div>
                                <div className="grid gap-2">
                                    <Label htmlFor="password" className="text-gray-300">Password</Label>
                                    <Input
                                        id="password"
                                        type="password"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        required
                                        disabled={isLoading}
                                        className="bg-[#1a1d24]/90 border-gray-700/50 text-white focus:border-gray-600/50 focus:ring-1 focus:ring-gray-600/30 hover:border-gray-600/70 transition-all duration-200"
                                    />
                                </div>
                                <div className="flex items-center justify-between">
                                    <Link href="/auth/forgot-password" className="text-sm text-gray-400 hover:text-gray-300 transition-colors duration-200">
                                        Forgot password?
                                    </Link>
                                </div>
                            </CardContent>
                            <CardFooter className="flex flex-col gap-4">
                                <Button 
                                    className="w-full bg-white/90 text-[#020817] hover:bg-white/95 active:bg-white/100 transition-all duration-200" 
                                    size="lg" 
                                    type="submit" 
                                    disabled={isLoading}
                                >
                                    {isLoading ? (
                                        <div className="flex items-center gap-2">
                                            <LoadingSpinner className="h-4 w-4" />
                                            <span>Signing in...</span>
                                        </div>
                                    ) : (
                                        'Sign In'
                                    )}
                                </Button>
                                <div className="relative">
                                    <div className="absolute inset-0 flex items-center">
                                        <span className="w-full border-t border-gray-700/50" />
                                    </div>
                                    <div className="relative flex justify-center text-xs uppercase">
                                        <span className="bg-[#020817]/80 px-2 text-gray-400">Or continue with</span>
                                    </div>
                                </div>
                                <Button
                                    variant="secondary"
                                    className="w-full bg-[#1a1d24]/90 text-white hover:bg-[#1a1d24]/95 active:bg-[#1a1d24]/100 border border-gray-700/50 hover:border-gray-700/70 transition-all duration-200"
                                    size="lg"
                                    onClick={handleGoogleSignIn}
                                    disabled={isLoading}
                                    type="button"
                                >
                                    {isLoading ? (
                                        <div className="flex items-center gap-2">
                                            <LoadingSpinner className="h-4 w-4" />
                                            <span>Signing in with Google...</span>
                                        </div>
                                    ) : (
                                        'Sign in with Google'
                                    )}
                                </Button>
                            </CardFooter>
                        </form>
                    </Card>
                </div>
            </div>
        </div>
    );
} 