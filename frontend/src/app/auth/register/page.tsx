'use client';

import { But<PERSON> } from "@/components/ui/Button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { useAuth } from "@/contexts/auth-context";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import Link from 'next/link';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { RocketLaunchIcon, ClockIcon, BuildingOffice2Icon } from '@heroicons/react/24/outline';
import { FcGoogle } from "react-icons/fc";
import { FirebaseError } from 'firebase/app';

export default function RegisterPage() {
    const { registerWithGoogle, registerWithEmail } = useAuth();
    const router = useRouter();
    const [loading, setLoading] = useState(false);
    const [googleLoading, setGoogleLoading] = useState(false);
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [fullName, setFullName] = useState('');
    const [localError, setLocalError] = useState<string | null>(null);

    const handleGoogleSignIn = async () => {
        try {
            setGoogleLoading(true);
            await registerWithGoogle(false);
            toast.success("Registration successful!");
            router.push("/dashboard");
        } catch (error: unknown) {
            console.error("Registration error:", error);
            let errorMessage = "Failed to register";

            if (error instanceof FirebaseError) {
                errorMessage = error.message || "Failed to register";
                if (error.message?.includes('work email')) {
                    errorMessage = 'Please use your work email address. Personal Gmail accounts are not allowed.';
                }
            }

            toast.error(errorMessage);
        } finally {
            setGoogleLoading(false);
        }
    };

    const handleEmailRegistration = async (e: React.FormEvent) => {
        e.preventDefault();
        setLocalError(null);

        // Basic validation
        if (!fullName || fullName.trim().length < 2) {
            setLocalError('Full name must be at least 2 characters');
            return;
        }

        if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
            setLocalError('Please enter a valid email address');
            return;
        }

        if (!password || password.length < 6) {
            setLocalError('Password must be at least 6 characters');
            return;
        }

        if (password !== confirmPassword) {
            setLocalError('Passwords do not match');
            return;
        }

        try {
            setLoading(true);
            await registerWithEmail(email, password, fullName);
            toast.success("Registration successful!");
            router.push("/dashboard");
        } catch (error: unknown) {
            console.error("Registration error:", error);
            let errorMessage = "Failed to register";

            if (error instanceof FirebaseError) {
                errorMessage = error.message || "Failed to register";

                // Handle specific Firebase errors
                if (error.code === 'auth/email-already-in-use') {
                    errorMessage = 'An account with this email already exists';
                } else if (error.code === 'auth/invalid-email') {
                    errorMessage = 'Invalid email format';
                } else if (error.code === 'auth/weak-password') {
                    errorMessage = 'Password is too weak';
                }
            }

            setLocalError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0 bg-[#2563eb]">
            <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
                <div className="absolute inset-0 bg-[#020817]" />
                <div className="relative z-20 flex flex-col h-full">
                    <div className="flex items-center text-lg font-medium">
                        <Link href="/" className="flex items-center">
                            <span className="logo-text text-3xl font-bold tracking-tight hover:text-gray-200 transition-colors">Recruiva</span>
                        </Link>
                    </div>
                    <div className="mt-auto space-y-8">
                        <div className="space-y-4 opacity-0 translate-y-8 animate-slide-up [animation-delay:200ms]">
                            <div className="flex items-center gap-3">
                                <RocketLaunchIcon className="w-8 h-8 text-blue-400" />
                                <h2 className="text-2xl font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                                    Transform Your Hiring
                                </h2>
                            </div>
                            <p className="bg-gradient-to-r from-gray-400 to-gray-300 bg-clip-text text-transparent text-lg leading-relaxed">
                                Streamline your recruitment process with AI-powered interviews and intelligent candidate matching.
                            </p>
                        </div>
                        <div className="space-y-4 opacity-0 translate-y-8 animate-slide-up [animation-delay:400ms]">
                            <div className="flex items-center gap-3">
                                <ClockIcon className="w-8 h-8 text-purple-400" />
                                <h2 className="text-2xl font-semibold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                                    Save Engineering Hours
                                </h2>
                            </div>
                            <p className="bg-gradient-to-r from-gray-400 to-gray-300 bg-clip-text text-transparent text-lg leading-relaxed">
                                Automate technical interviews and reduce screening time by up to 80% with AI-powered assessments.
                            </p>
                        </div>
                        <div className="space-y-4 opacity-0 translate-y-8 animate-slide-up [animation-delay:600ms]">
                            <div className="flex items-center gap-3">
                                <BuildingOffice2Icon className="w-8 h-8 text-blue-400" />
                                <h2 className="text-2xl font-semibold bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">
                                    Scale With Confidence
                                </h2>
                            </div>
                            <p className="bg-gradient-to-r from-gray-400 to-gray-300 bg-clip-text text-transparent text-lg leading-relaxed">
                                Built for teams of all sizes, from startups to enterprises.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div className="lg:p-8 bg-[#2563eb] min-h-screen flex items-center justify-center">
                <div className="mx-auto flex w-full flex-col justify-center sm:w-[350px]">
                    <Card className="bg-[#020817]/80 hover:bg-[#020817]/85 border-none shadow-xl backdrop-blur-sm transition-all duration-300">
                        <CardHeader>
                            <CardTitle className="text-white">Create an account</CardTitle>
                            <CardDescription className="text-gray-400">Sign up to get started with Recruiva</CardDescription>
                        </CardHeader>
                        <CardContent className="grid gap-4">
                            <Button
                                variant="secondary"
                                className="w-full bg-[#1a1d24]/90 text-white hover:bg-[#1a1d24]/95 active:bg-[#1a1d24]/100 border border-gray-700/50 hover:border-gray-700/70 transition-all duration-200 flex items-center gap-2"
                                size="lg"
                                onClick={handleGoogleSignIn}
                                disabled={loading || googleLoading}
                            >
                                {googleLoading ? (
                                    <div className="flex items-center gap-2">
                                        <LoadingSpinner className="h-4 w-4" />
                                        <span>Registering with Google...</span>
                                    </div>
                                ) : (
                                    <div className="flex items-center gap-2">
                                        <FcGoogle className="h-5 w-5" />
                                        <span>Continue with Google</span>
                                    </div>
                                )}
                            </Button>

                            <div className="relative my-2">
                                <div className="absolute inset-0 flex items-center">
                                    <span className="w-full border-t border-gray-700/50"></span>
                                </div>
                                <div className="relative flex justify-center text-xs">
                                    <span className="bg-[#020817] px-2 text-gray-400">OR</span>
                                </div>
                            </div>

                            {localError && (
                                <Alert variant="destructive" className="bg-red-900/20 border-red-900/50 text-red-300">
                                    <AlertDescription>{localError}</AlertDescription>
                                </Alert>
                            )}

                            <form onSubmit={handleEmailRegistration} className="space-y-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="fullName" className="text-gray-300">Full Name</Label>
                                    <Input
                                        id="fullName"
                                        type="text"
                                        placeholder="John Doe"
                                        value={fullName}
                                        onChange={(e) => setFullName(e.target.value)}
                                        required
                                        disabled={loading}
                                        className="bg-[#1a1d24]/90 border-gray-700/50 text-white placeholder:text-gray-500 focus:border-gray-600/50 focus:ring-1 focus:ring-gray-600/30 hover:border-gray-600/70 transition-all duration-200"
                                    />
                                </div>
                                <div className="grid gap-2">
                                    <Label htmlFor="email" className="text-gray-300">Email</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="<EMAIL>"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                        required
                                        disabled={loading}
                                        className="bg-[#1a1d24]/90 border-gray-700/50 text-white placeholder:text-gray-500 focus:border-gray-600/50 focus:ring-1 focus:ring-gray-600/30 hover:border-gray-600/70 transition-all duration-200"
                                    />
                                </div>
                                <div className="grid gap-2">
                                    <Label htmlFor="password" className="text-gray-300">Password</Label>
                                    <Input
                                        id="password"
                                        type="password"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        required
                                        disabled={loading}
                                        className="bg-[#1a1d24]/90 border-gray-700/50 text-white focus:border-gray-600/50 focus:ring-1 focus:ring-gray-600/30 hover:border-gray-600/70 transition-all duration-200"
                                    />
                                </div>
                                <div className="grid gap-2">
                                    <Label htmlFor="confirmPassword" className="text-gray-300">Confirm Password</Label>
                                    <Input
                                        id="confirmPassword"
                                        type="password"
                                        value={confirmPassword}
                                        onChange={(e) => setConfirmPassword(e.target.value)}
                                        required
                                        disabled={loading}
                                        className="bg-[#1a1d24]/90 border-gray-700/50 text-white focus:border-gray-600/50 focus:ring-1 focus:ring-gray-600/30 hover:border-gray-600/70 transition-all duration-200"
                                    />
                                </div>
                                <Button
                                    className="w-full bg-white/90 text-[#020817] hover:bg-white/95 active:bg-white/100 transition-all duration-200"
                                    size="lg"
                                    type="submit"
                                    disabled={loading || googleLoading}
                                >
                                    {loading ? (
                                        <div className="flex items-center gap-2">
                                            <LoadingSpinner className="h-4 w-4" />
                                            <span>Creating account...</span>
                                        </div>
                                    ) : (
                                        'Create Account'
                                    )}
                                </Button>
                            </form>
                        </CardContent>
                        <CardFooter className="flex flex-col space-y-4">
                            <div className="text-sm text-gray-400 text-center">
                                By continuing, you agree to our{" "}
                                <Link href="/terms" className="text-blue-400 hover:text-blue-300 underline underline-offset-4">
                                    Terms of Service
                                </Link>{" "}
                                and{" "}
                                <Link href="/privacy" className="text-blue-400 hover:text-blue-300 underline underline-offset-4">
                                    Privacy Policy
                                </Link>
                                .
                            </div>
                            <div className="text-sm text-gray-400 text-center">
                                Already have an account?{" "}
                                <Link href="/auth/signin" className="text-blue-400 hover:text-blue-300 underline underline-offset-4">
                                    Sign in
                                </Link>
                            </div>
                        </CardFooter>
                    </Card>
                </div>
            </div>
        </div>
    );
}