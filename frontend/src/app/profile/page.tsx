'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { But<PERSON> } from '@/components/ui/Button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar'
import { Camera } from 'lucide-react'
import { useAuth } from '@/contexts/auth-context'
import { useToast } from '@/hooks/use-toast'
import { doc, updateDoc, onSnapshot, Timestamp, getDoc, setDoc } from 'firebase/firestore'
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage'
import { db, storage, auth } from '@/lib/firebase'
import { sendPasswordResetEmail } from 'firebase/auth'
import { Select } from '@/components/ui/Select'

// Default avatar from UI library or a placeholder service
const DEFAULT_AVATAR = 'https://ui-avatars.com/api/?background=random';

type UserRole = 'recruiter' | 'hiring_manager';

interface UserFormData {
  fullName: string;
  phone: string;
  photoURL: string;
  role: UserRole;
}

export default function ProfilePage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [userData, setUserData] = useState<UserFormData>({
    fullName: '',
    phone: '',
    photoURL: user?.photoURL || '',
    role: 'recruiter',
  });

  useEffect(() => {
    if (!user) return;

    // Subscribe to user profile changes
    const unsubscribe = onSnapshot(doc(db, 'users', user.uid), (doc) => {
      if (doc.exists()) {
        const data = doc.data();
        setUserData({
          fullName: data.fullName || '',
          phone: data.phone || '',
          photoURL: data.photoURL || user?.photoURL || '',
          role: data.role || 'recruiter',
        });
      }
    });

    return () => unsubscribe();
  }, [user]);

  const getDefaultAvatarUrl = (name: string) => {
    return `${DEFAULT_AVATAR}&name=${encodeURIComponent(name)}`;
  };

  const handlePhotoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!user || !e.target.files || !e.target.files[0]) return;

    const file = e.target.files[0];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (file.size > maxSize) {
      toast({
        title: 'Error',
        description: 'Image size should be less than 5MB',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      // Upload to Firebase Storage
      const storageRef = ref(storage, `users/${user.uid}/profile-photo`);
      await uploadBytes(storageRef, file);
      const photoURL = await getDownloadURL(storageRef);

      setUserData(prev => ({ ...prev, photoURL }));
      toast({
        title: 'Success',
        description: 'Profile photo uploaded successfully. Click Save Changes to update your profile.',
      });
    } catch (error) {
      console.error('Error uploading photo:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload profile photo. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveChanges = async () => {
    if (!user) return;

    if (!userData.fullName?.trim()) {
      toast({
        title: 'Error',
        description: 'Full name is required',
        variant: 'destructive',
      });
      return;
    }

    if (!userData.role) {
      toast({
        title: 'Error',
        description: 'Please select a role',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      const userDocRef = doc(db, 'users', user.uid);
      
      // First check if the document exists
      const docSnap = await getDoc(userDocRef);
      
      const userDoc = {
        fullName: userData.fullName.trim(),
        email: user.email,
        phone: userData.phone?.trim() || '',
        photoURL: userData.photoURL || getDefaultAvatarUrl(userData.fullName),
        role: userData.role,
        updatedAt: Timestamp.fromDate(new Date())
      };

      if (!docSnap.exists()) {
        console.log('Creating new user document...');
        await setDoc(userDocRef, {
          ...userDoc,
          createdAt: Timestamp.fromDate(new Date())
        });
      } else {
        console.log('Updating existing user document...');
        await updateDoc(userDocRef, userDoc);
      }

      toast({
        title: 'Success',
        description: 'Profile updated successfully',
      });
    } catch (error: any) {
      console.error('Error updating profile:', {
        error,
        errorCode: error.code,
        errorMessage: error.message,
        userData: {
          ...userData,
          email: user.email
        }
      });
      
      toast({
        title: 'Error',
        description: `Failed to update profile: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordReset = async () => {
    if (!user?.email) return;

    try {
      await sendPasswordResetEmail(auth, user.email);
      toast({
        title: 'Success',
        description: 'Password reset email sent. Please check your inbox.',
      });
    } catch (error) {
      console.error('Error sending password reset:', error);
      toast({
        title: 'Error',
        description: 'Failed to send password reset email. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (!user) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Profile</h2>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-4">
              <Avatar className="h-24 w-24">
                <AvatarImage src={userData.photoURL || getDefaultAvatarUrl(userData.fullName)} alt="Profile" />
                <AvatarFallback>{userData.fullName?.charAt(0) || user.email?.charAt(0) || 'U'}</AvatarFallback>
              </Avatar>
              <div>
                <input
                  type="file"
                  id="photo-upload"
                  className="hidden"
                  accept="image/*"
                  onChange={handlePhotoChange}
                />
                <Button
                  variant="secondary"
                  onClick={() => document.getElementById('photo-upload')?.click()}
                  disabled={isLoading}
                >
                  <Camera className="mr-2 h-4 w-4" />
                  Change Photo
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Full Name</label>
              <Input
                value={userData.fullName}
                onChange={(e) => setUserData(prev => ({ ...prev, fullName: e.target.value }))}
                placeholder="Enter your full name"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Email</label>
              <Input
                value={user.email || ''}
                disabled
                className="bg-muted"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Phone</label>
              <Input
                value={userData.phone}
                onChange={(e) => setUserData(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="Enter your phone number"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Role</label>
              <Select
                value={userData.role}
                onValueChange={(value: UserRole) => setUserData(prev => ({ ...prev, role: value }))}
                options={[
                  { label: 'Recruiter', value: 'recruiter' },
                  { label: 'Hiring Manager', value: 'hiring_manager' }
                ]}
              />
            </div>
            <Button onClick={handleSaveChanges} disabled={isLoading}>
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Security</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              To change your password, click the button below. You will receive an email with instructions to reset your password.
            </p>
            <Button onClick={handlePasswordReset}>Send Password Reset Email</Button>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
} 