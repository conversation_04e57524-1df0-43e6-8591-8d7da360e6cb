import { useState, useEffect } from 'react';
import { evaluationService } from '@/services/evaluation';
import { ExtendedEvaluation } from '@/types/evaluation';

/**
 * Hook to fetch and manage a single evaluation
 *
 * @param evaluationId The ID of the evaluation to fetch
 * @param isPublic Whether this is a public evaluation
 * @param pollingInterval Optional interval (ms) to poll for updates if status is pending
 * @returns Object containing evaluation, loading state, and error
 */
export function useEvaluation(
  evaluationId: string | undefined,
  isPublic: boolean = false,
  pollingInterval: number = 5000
) {
  const [evaluation, setEvaluation] = useState<ExtendedEvaluation | null>(null);
  const [loading, setLoading] = useState<boolean>(!!evaluationId);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;
    let pollingTimer: NodeJS.Timeout | null = null;

    console.log(`useEvaluation hook called with evaluationId=${evaluationId}, isPublic=${isPublic}, pollingInterval=${pollingInterval}`);

    const fetchEvaluation = async () => {
      if (!evaluationId) {
        console.log('No evaluationId provided, skipping fetch');
        if (isMounted) {
          setLoading(false);
        }
        return;
      }

      console.log(`Fetching evaluation ${evaluationId}`);
      setLoading(true);
      setError(null);

      // Clear previous evaluation when ID changes
      setEvaluation(null);

      try {
        // Always force a refresh when the evaluation ID changes to avoid caching issues
        console.log(`Calling evaluationService.getEvaluation(${evaluationId}, true, ${isPublic})`);
        const data = await evaluationService.getEvaluation(evaluationId, true, isPublic);
        console.log(`Received evaluation data for ${evaluationId}:`, data);

        if (isMounted) {
          console.log(`Setting evaluation data for ${evaluationId}`);
          setEvaluation(data);
          setLoading(false);

          // If the evaluation is still pending, set up polling
          if (data.status === 'pending' && pollingInterval > 0) {
            console.log(`Setting up polling for ${evaluationId} with interval ${pollingInterval}ms`);
            pollingTimer = setTimeout(fetchEvaluation, pollingInterval);
          }
        }
      } catch (err) {
        if (isMounted) {
          console.error(`Error in useEvaluation for ${evaluationId}:`, err);
          setError(err instanceof Error ? err : new Error(String(err)));
          setLoading(false);
        }
      }
    };

    // Clear the cache for this evaluation ID to ensure we get fresh data
    console.log(`Clearing cache for evaluation ${evaluationId}`);
    evaluationService.clearCache(evaluationId);

    fetchEvaluation();

    return () => {
      console.log(`Cleaning up useEvaluation hook for ${evaluationId}`);
      isMounted = false;
      if (pollingTimer) {
        clearTimeout(pollingTimer);
      }
    };
  }, [evaluationId, isPublic, pollingInterval]);

  // Function to manually refresh the evaluation
  const refreshEvaluation = async () => {
    if (!evaluationId) return;

    setLoading(true);
    setError(null);

    try {
      const data = await evaluationService.getEvaluation(evaluationId, true, isPublic);
      setEvaluation(data);
      setLoading(false);
    } catch (err) {
      console.error('Error refreshing evaluation:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      setLoading(false);
    }
  };

  return { evaluation, loading, error, refreshEvaluation };
}

/**
 * Hook to fetch and manage evaluations for a role
 *
 * @param roleId The ID of the role to fetch evaluations for
 * @returns Object containing evaluations, loading state, and error
 */
export function useRoleEvaluations(roleId: string | undefined) {
  const [evaluations, setEvaluations] = useState<ExtendedEvaluation[]>([]);
  const [loading, setLoading] = useState<boolean>(!!roleId);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;

    const fetchEvaluations = async () => {
      if (!roleId) {
        if (isMounted) {
          setLoading(false);
        }
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const data = await evaluationService.getEvaluationsByRole(roleId);

        if (isMounted) {
          setEvaluations(data);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error in useRoleEvaluations:', err);
          setError(err instanceof Error ? err : new Error(String(err)));
          setLoading(false);
        }
      }
    };

    fetchEvaluations();

    return () => {
      isMounted = false;
    };
  }, [roleId]);

  // Function to manually refresh the evaluations
  const refreshEvaluations = async () => {
    if (!roleId) return;

    setLoading(true);
    setError(null);

    try {
      const data = await evaluationService.getEvaluationsByRole(roleId);
      setEvaluations(data);
      setLoading(false);
    } catch (err) {
      console.error('Error refreshing evaluations:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      setLoading(false);
    }
  };

  return { evaluations, loading, error, refreshEvaluations };
}

/**
 * Hook to fetch and manage evaluations for an interview
 *
 * @param interviewId The ID of the interview to fetch evaluations for
 * @returns Object containing evaluations, loading state, and error
 */
export function useInterviewEvaluations(interviewId: string | undefined) {
  const [evaluations, setEvaluations] = useState<ExtendedEvaluation[]>([]);
  const [loading, setLoading] = useState<boolean>(!!interviewId);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;

    const fetchEvaluations = async () => {
      if (!interviewId) {
        if (isMounted) {
          setLoading(false);
        }
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const data = await evaluationService.getEvaluationsByInterview(interviewId);

        if (isMounted) {
          setEvaluations(data);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error in useInterviewEvaluations:', err);
          setError(err instanceof Error ? err : new Error(String(err)));
          setLoading(false);
        }
      }
    };

    fetchEvaluations();

    return () => {
      isMounted = false;
    };
  }, [interviewId]);

  // Function to manually refresh the evaluations
  const refreshEvaluations = async () => {
    if (!interviewId) return;

    setLoading(true);
    setError(null);

    try {
      const data = await evaluationService.getEvaluationsByInterview(interviewId);
      setEvaluations(data);
      setLoading(false);
    } catch (err) {
      console.error('Error refreshing evaluations:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      setLoading(false);
    }
  };

  return { evaluations, loading, error, refreshEvaluations };
}

/**
 * Hook to request an evaluation and track its status
 *
 * @param isPublic Whether to use the public API
 * @returns Object containing request function, evaluation ID, loading state, and error
 */
export function useEvaluationRequest(isPublic: boolean = false) {
  const [evaluationId, setEvaluationId] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const requestEvaluation = async (
    interviewId: string,
    roleId: string,
    applicationId?: string
  ) => {
    setLoading(true);
    setError(null);

    try {
      const id = isPublic
        ? await evaluationService.evaluatePublicInterview(interviewId, roleId, applicationId)
        : await evaluationService.evaluateInterview(interviewId, roleId, applicationId);

      setEvaluationId(id);
      setLoading(false);
      return id;
    } catch (err) {
      console.error('Error requesting evaluation:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      setLoading(false);
      throw err;
    }
  };

  return { requestEvaluation, evaluationId, loading, error };
}
