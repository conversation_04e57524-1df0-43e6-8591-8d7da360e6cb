import { useState } from 'react';
import { RoleUpdateUtil, RoleUpdateOptions } from '@/services/roles/roleUpdateUtil';
import type { Role } from '@/types/role';

export const useRoleUpdate = () => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const updateRole = async (roleId: string, updates: RoleUpdateOptions): Promise<Role | null> => {
    setIsUpdating(true);
    setError(null);

    try {
      const updatedRole = await RoleUpdateUtil.updateRole(roleId, updates);
      return updatedRole;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update role'));
      return null;
    } finally {
      setIsUpdating(false);
    }
  };

  return {
    updateRole,
    isUpdating,
    error,
  };
}; 