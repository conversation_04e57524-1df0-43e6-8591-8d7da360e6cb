import { useState, useEffect } from 'react';
import { Role } from '@/types/role';
import { rolesService } from '@/services/roles';

export function useRole(roleId: string | null) {
  const [role, setRole] = useState<Role | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRole = async () => {
      if (!roleId) {
        console.error('useRole: roleId is null');
        setError('Role ID is missing');
        setLoading(false);
        return;
      }

      try {
        console.log('useRole: Fetching role with ID:', roleId);
        setLoading(true);
        setError(null);
        
        const data = await rolesService.getRole(roleId);
        console.log('useRole: Fetched role data:', data);
        
        if (!data) {
          console.error('useRole: Role not found for ID:', roleId);
          setError('Role not found');
          setRole(null);
        } else {
          setRole(data);
          setError(null);
        }
      } catch (err) {
        console.error('useRole: Error fetching role:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch role');
        setRole(null);
      } finally {
        setLoading(false);
      }
    };

    fetchRole();
  }, [roleId]);

  return {
    role,
    loading,
    error,
    setRole,
  };
} 