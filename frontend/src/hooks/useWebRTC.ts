// File: frontend/src/hooks/useWebRTC.ts

import { useEffect, useRef, useState, useCallback } from 'react';

interface UseWebRTCProps {
    onMessage?: (event: any) => void;
    onTrack?: (stream: MediaStream) => void;
    onConnectionStateChange?: (state: RTCPeerConnectionState) => void;
    sessionData?: {
        session_id: string;
        client_secret: {
            value: string;
            expires_at: number;
        };
        transcript_id?: string;
        role_id?: string;
        template_id?: string;
    };
    muted?: boolean;
}

export const useWebRTC = ({ 
    onMessage, 
    onTrack, 
    onConnectionStateChange, 
    sessionData,
    muted = false
}: UseWebRTCProps = {}) => {
    const [isConnected, setIsConnected] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
    const dataChannelRef = useRef<RTCDataChannel | null>(null);
    const audioStreamRef = useRef<MediaStream | null>(null);
    const connectionTimeoutIdRef = useRef<NodeJS.Timeout | null>(null);
    const reconnectAttemptRef = useRef<number>(0);

    // Stop any audio tracks that might be active
    const stopAudioTracks = useCallback(() => {
        if (audioStreamRef.current) {
            try {
                audioStreamRef.current.getTracks().forEach(track => {
                    try {
                        track.stop();
                    } catch (e) {
                        console.warn("Error stopping audio track:", e);
                    }
                });
                audioStreamRef.current = null;
            } catch (e) {
                console.warn("Error stopping audio stream:", e);
            }
        }
    }, []);

    // Clean up resources
    const cleanup = useCallback(() => {
        console.log("Cleaning up WebRTC connection resources");
        
        // Clear any timeouts
        if (connectionTimeoutIdRef.current) {
            clearTimeout(connectionTimeoutIdRef.current);
            connectionTimeoutIdRef.current = null;
        }
        
        // Stop any audio tracks
        stopAudioTracks();
        
        // Close peer connection
        if (peerConnectionRef.current) {
            try {
                // Close data channel
                if (dataChannelRef.current) {
                    try {
                        dataChannelRef.current.close();
                    } catch (e) {
                        console.error("Error closing data channel:", e);
                    }
                    dataChannelRef.current = null;
                }
                
                // Close the peer connection
                peerConnectionRef.current.close();
            } catch (e) {
                console.error("Error closing peer connection:", e);
            }
            peerConnectionRef.current = null;
        }
        
        setIsConnected(false);
    }, [stopAudioTracks]);

    const initializeConnection = async () => {
        // Reset state
        setIsConnected(false);
        setError(null);
        
        // Close any existing peer connection
        if (peerConnectionRef.current) {
            try {
                // Close data channel
                if (dataChannelRef.current) {
                    try {
                        dataChannelRef.current.close();
                    } catch (e) {
                        console.error("Error closing data channel:", e);
                    }
                    dataChannelRef.current = null;
                }
                
                // Close the peer connection
                peerConnectionRef.current.close();
                peerConnectionRef.current = null;
            } catch (e) {
                console.error("Error closing peer connection:", e);
            }
        }
        
        // Stop any existing audio tracks
        stopAudioTracks();
        
        try {
            // Validate session data
            if (!sessionData || !sessionData.client_secret || !sessionData.client_secret.value) {
                throw new Error('Missing or invalid session data');
            }
            
            console.log('useWebRTC: Using provided session data:', {
                session_id: sessionData.session_id,
                has_client_secret: !!sessionData.client_secret,
                transcript_id: sessionData.transcript_id,
                role_id: sessionData.role_id,
                template_id: sessionData.template_id
            });
            
            // Check session token expiration
            const expiresAt = sessionData.client_secret.expires_at;
            const currentTime = Math.floor(Date.now() / 1000);
            if (expiresAt && currentTime > expiresAt) {
                throw new Error('Session token has expired. Please refresh the page to get a new token.');
            }
            
            const EPHEMERAL_KEY = sessionData.client_secret.value;
            
            // Get microphone stream BEFORE creating the peer connection
            console.log('Requesting microphone access...');
            let ms: MediaStream;
            try {
                ms = await navigator.mediaDevices.getUserMedia({
                    audio: true
                });
                audioStreamRef.current = ms;
                console.log('Successfully obtained microphone stream');
            } catch (mediaError) {
                console.error('Error accessing microphone:', mediaError);
                throw new Error('Failed to access microphone. Please check your permissions and try again.');
            }
            
            // Create a fresh peer connection after getting microphone access
            console.log('Creating new RTCPeerConnection');
            const pc = new RTCPeerConnection({
                iceServers: [
                    { urls: "stun:stun.l.google.com:19302" },
                    { urls: "stun:stun1.l.google.com:19302" },
                    { urls: "stun:stun2.l.google.com:19302" }
                ]
            });
            peerConnectionRef.current = pc;

            // Set up to play remote audio from the model
            pc.ontrack = (e) => {
                if (onTrack && e.streams && e.streams[0]) {
                    console.log('Received audio track from remote peer');
                    onTrack(e.streams[0]);
                }
            };

            // IMPORTANT: Add local audio track for microphone input
            const audioTrack = ms.getTracks()[0];
            try {
                // Apply muted state to the audio track before adding it
                if (audioTrack) {
                    audioTrack.enabled = !muted;
                    console.log(`Audio track initial state: ${muted ? 'muted' : 'unmuted'}`);
                }
                
                pc.addTrack(audioTrack, ms);
                console.log('Successfully added audio track to WebRTC connection');
            } catch (trackError) {
                console.error('Error adding audio track:', trackError);
                throw new Error('Failed to add audio track to connection');
            }

            // Add connection state change handler
            pc.addEventListener('connectionstatechange', () => {
                console.log('WebRTC Connection State:', pc.connectionState);
                setIsConnected(pc.connectionState === 'connected');
                
                if (pc.connectionState === 'connected') {
                    console.log("WebRTC connection established successfully");
                    
                    // Clear timeout since we're connected successfully
                    if (connectionTimeoutIdRef.current) {
                        clearTimeout(connectionTimeoutIdRef.current);
                        connectionTimeoutIdRef.current = null;
                    }
                    
                    // Reset reconnect attempts counter on successful connection
                    reconnectAttemptRef.current = 0;
                }
                else if (pc.connectionState === 'failed' || pc.connectionState === 'disconnected' || pc.connectionState === 'closed') {
                    const errorMsg = `Connection ${pc.connectionState}. Please try again.`;
                    setError(errorMsg);
                    
                    // If onMessage is defined, pass an error event
                    if (onMessage) {
                        onMessage({
                            type: 'error',
                            error: errorMsg,
                            connection_state: pc.connectionState
                        });
                    }
                    
                    // Clean up resources if we're completely failed/closed
                    if (pc.connectionState === 'failed' || pc.connectionState === 'closed') {
                        console.log(`WebRTC connection ${pc.connectionState}, cleaning up resources`);
                        cleanup();
                    }
                }
                
                // Call the onConnectionStateChange callback if provided
                if (onConnectionStateChange) {
                    onConnectionStateChange(pc.connectionState);
                }
            });
            
            // Also listen for ICE connection state changes for more detailed info
            pc.addEventListener('iceconnectionstatechange', () => {
                console.log('ICE Connection State:', pc.iceConnectionState);
                
                // If ICE connection fails, this can be more specific than connectionstate
                if (pc.iceConnectionState === 'failed') {
                    console.error('ICE connection failed - possible network issues');
                    
                    if (onMessage) {
                        onMessage({
                            type: 'error',
                            error: 'Network connection issue detected',
                            detail: 'ICE negotiation failed'
                        });
                    }
                }
            });
            
            // Create data channel for sending and receiving events
            try {
                const dc = pc.createDataChannel("oai-events");
                dataChannelRef.current = dc;
                
                dc.addEventListener("message", (e) => {
                    try {
                        const eventData = JSON.parse(e.data);
                        
                        // Log detailed error information if present
                        if (eventData.type === 'response.done' && eventData.response?.status === 'failed') {
                            console.error('WebRTC Response Error:', eventData.response?.status_details);
                            
                            if (onMessage) {
                                onMessage({
                                    type: 'error',
                                    error: eventData.response?.status_details || 'Unknown error',
                                    original_event: eventData
                                });
                            }
                        }
                        
                        if (onMessage) {
                            onMessage(eventData);
                        }
                    } catch (err) {
                        console.error("Error processing data channel message:", err);
                    }
                });
                
                // Add additional event handlers for data channel
                dc.onopen = () => {
                    console.log('Data channel opened');
                    
                    // Send session.update to configure the session with input_audio_transcription
                    try {
                        const sessionConfig = {
                            type: 'session.update',
                            session: {
                                modalities: ['text', 'audio'],
                                input_audio_transcription: {
                                    model: 'whisper-1',
                                    language: 'en'
                                }
                            }
                        };
                        
                        dc.send(JSON.stringify(sessionConfig));
                        console.log('Sent session.update with input_audio_transcription configuration');
                    } catch (configError) {
                        console.error('Error sending session configuration:', configError);
                    }
                };
                
                dc.onclose = () => {
                    console.log('Data channel closed');
                };
                
                dc.onerror = (e) => {
                    console.error('Data channel error:', e);
                };
            } catch (channelError) {
                console.error('Error creating data channel:', channelError);
                throw new Error('Failed to create data channel for communication');
            }

            // Set connection timeout
            connectionTimeoutIdRef.current = setTimeout(() => {
                if (pc.connectionState !== 'connected') {
                    const timeoutError = 'Connection timed out. Please try again.';
                    console.error(timeoutError);
                    setError(timeoutError);
                    
                    if (onMessage) {
                        onMessage({
                            type: 'error',
                            error: timeoutError
                        });
                    }
                    
                    // Clean up
                    cleanup();
                }
            }, 30000); // 30 second timeout

            // Log SDP offer creation
            console.log("Creating offer for WebRTC connection...");
            
            // Start the session using SDP
            const offer = await pc.createOffer();
            await pc.setLocalDescription(offer);
            
            // Wait a short time for ICE candidates to be gathered
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const baseUrl = "https://api.openai.com/v1/realtime";
            const model = "gpt-4o-mini-realtime-preview-2024-12-17";
            
            // Use the model parameter for the API URL
            const apiUrl = `${baseUrl}?model=${model}`;
            console.log(`useWebRTC: Connecting to OpenAI API: ${apiUrl}`);
            
            const sdpResponse = await fetch(apiUrl, {
                method: "POST",
                body: pc.localDescription?.sdp,
                headers: {
                    Authorization: `Bearer ${EPHEMERAL_KEY}`,
                    "Content-Type": "application/sdp"
                },
            });
            
            if (!sdpResponse.ok) {
                const errorText = await sdpResponse.text();
                throw new Error(`Failed to establish WebRTC connection: ${errorText}`);
            }

            const answer: RTCSessionDescriptionInit = {
                type: 'answer' as RTCSdpType,
                sdp: await sdpResponse.text(),
            };
            
            await pc.setRemoteDescription(answer);
            console.log("Remote description set successfully");
            
            // Connection is being established, we'll wait for the 'connected' event
            console.log("Waiting for WebRTC connection to establish...");
            
            return pc;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to initialize WebRTC connection';
            console.error('WebRTC connection error:', errorMessage);
            setError(errorMessage);
            setIsConnected(false);
            
            // Clean up any resources
            cleanup();
            
            // If onMessage is defined, pass an error event
            if (onMessage) {
                onMessage({
                    type: 'error',
                    error: errorMessage
                });
            }
            
            throw err;
        }
    };

    const sendMessage = (message: any) => {
        if (dataChannelRef.current?.readyState === 'open') {
            try {
                dataChannelRef.current.send(JSON.stringify(message));
            } catch (err) {
                console.error('Error sending message:', err);
            }
        } else {
            console.warn('Cannot send message: data channel not open');
        }
    };

    const disconnect = useCallback(() => {
        cleanup();
    }, [cleanup]);

    useEffect(() => {
        return () => {
            disconnect();
        };
    }, [disconnect]);

    // Apply muted state effect
    useEffect(() => {
        // Find and disable all audio tracks when muted
        if (audioStreamRef.current) {
            audioStreamRef.current.getAudioTracks().forEach(track => {
                track.enabled = !muted;
                console.log(`Local audio track ${track.label} ${muted ? 'muted' : 'unmuted'}`);
            });
        }
        
        // Update peer connection tracks directly - this is crucial for WebRTC muting
        if (peerConnectionRef.current) {
            const senders = peerConnectionRef.current.getSenders();
            let trackCount = 0;
            senders.forEach(sender => {
                if (sender.track && sender.track.kind === 'audio') {
                    sender.track.enabled = !muted;
                    console.log(`WebRTC sender track ${sender.track.label} ${muted ? 'muted' : 'unmuted'}`);
                    trackCount++;
                }
            });
            console.log(`Updated ${trackCount} sender tracks mute state to: ${muted}`);
        }
    }, [muted]);

    return {
        isConnected,
        error,
        connect: initializeConnection,
        disconnect,
        sendMessage,
        peerConnectionRef
    };
}; 