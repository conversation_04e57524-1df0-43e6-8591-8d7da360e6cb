'use client';

import { createContext, useContext, useState, ReactNode, useEffect } from 'react';

interface SidebarContextType {
    isCollapsed: boolean;
    toggleCollapse: () => void;
    setCollapsed: (collapsed: boolean) => void;
    isMobileOpen: boolean;
    setMobileOpen: (open: boolean) => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: ReactNode }) {
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [isMobileOpen, setIsMobileOpen] = useState(false);

    // <PERSON>le closing the sidebar when clicking outside on mobile
    useEffect(() => {
        const handleClickOutside = () => {
            if (isMobileOpen) {
                setIsMobileOpen(false);
            }
        };

        // Only add the event listener when the mobile sidebar is open
        if (isMobileOpen) {
            document.addEventListener('click', handleClickOutside);
        }

        return () => {
            document.removeEventListener('click', handleClickOutside);
        };
    }, [isMobileOpen]);

    // Close mobile menu on route change
    useEffect(() => {
        const handleRouteChange = () => {
            setIsMobileOpen(false);
        };

        window.addEventListener('popstate', handleRouteChange);

        return () => {
            window.removeEventListener('popstate', handleRouteChange);
        };
    }, []);

    const toggleCollapse = () => setIsCollapsed(prev => !prev);
    const setCollapsed = (collapsed: boolean) => setIsCollapsed(collapsed);
    const setMobileOpen = (open: boolean) => setIsMobileOpen(open);

    return (
        <SidebarContext.Provider value={{ 
            isCollapsed, 
            toggleCollapse, 
            setCollapsed,
            isMobileOpen,
            setMobileOpen 
        }}>
            {children}
        </SidebarContext.Provider>
    );
}

export function useSidebar() {
    const context = useContext(SidebarContext);
    if (context === undefined) {
        throw new Error('useSidebar must be used within a SidebarProvider');
    }
    return context;
} 