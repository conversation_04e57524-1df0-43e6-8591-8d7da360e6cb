'use client';

import { createContext, useContext, useEffect, useState, useCallback } from 'react';
import {
    User,
    GoogleAuthProvider,
    signInWithPopup,
    signOut,
    createUserWithEmailAndPassword,
    signInWithEmailAndPassword,
    onAuthStateChanged,
    getAdditionalUserInfo,
    setPersistence,
    browserLocalPersistence
} from 'firebase/auth';
import { auth, db } from '@/lib/firebase';
import { doc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import { useRouter, usePathname } from 'next/navigation';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

// Constants
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

// Types
export type UserRole = 'recruiter' | 'hiring_manager' | 'admin';

interface FirestoreUser {
    uid: string;
    email: string;
    fullName: string;
    role: UserRole;
    createdAt: Date;
    updatedAt: Date;
    photoURL?: string;
    isWorkspaceUser: boolean;
    authProvider: 'google' | 'email';
    status: 'active' | 'inactive';
    emailVerified: boolean;
    lastSignInTime: string;
    accountType: 'personal' | 'workspace';
}

interface AuthState {
    user: User | null;
    firestoreUser: FirestoreUser | null;
    loading: boolean;
    error: string | null;
    isInitialized: boolean;
}

interface AuthContextType extends AuthState {
    signInWithGoogle: () => Promise<void>;
    signInWithEmail: (email: string, password: string) => Promise<void>;
    registerWithGoogle: (requireWorkEmail?: boolean) => Promise<void>;
    registerWithEmail: (email: string, password: string, name: string) => Promise<void>;
    logout: () => Promise<void>;
    clearError: () => void;
}

const AuthContext = createContext<AuthContextType | null>(null);

// Custom error class for auth errors
class AuthError extends Error {
    constructor(message: string, public code?: string) {
        super(message);
        this.name = 'AuthError';
    }
}

// Helper function for retry logic
async function withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = MAX_RETRIES,
    delay: number = RETRY_DELAY
): Promise<T> {
    let lastError: Error;

    for (let i = 0; i < maxRetries; i++) {
        try {
            return await operation();
        } catch (error: unknown) {
            if (error instanceof Error) {
                lastError = error;
                console.error(`Attempt ${i + 1} failed:`, error);

                // Don't retry for certain errors
                if ('code' in error && (
                    error.code === 'auth/popup-closed-by-user' ||
                    error.code === 'auth/cancelled-popup-request' ||
                    error.code === 'auth/email-already-in-use'
                )) {
                    throw error;
                }

                if (i < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i))); // Exponential backoff
                }
            } else {
                throw error;
            }
        }
    }

    throw lastError!;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
    const [state, setState] = useState<AuthState>({
        user: null,
        firestoreUser: null,
        loading: true,
        error: null,
        isInitialized: false
    });

    const router = useRouter();
    const pathname = usePathname();

    // Helper function to update state
    const updateState = useCallback((newState: Partial<AuthState>) => {
        setState(prev => ({ ...prev, ...newState }));
    }, []);

    // Helper function to handle auth errors
    const handleAuthError = useCallback((error: Error | { code?: string; message?: string }) => {
        console.error('Auth error:', error);
        let message = 'An unexpected error occurred';

        if (error instanceof AuthError) {
            message = error.message;
        } else if ('code' in error && error.code) {
            switch (error.code) {
                case 'auth/invalid-email':
                    message = 'Invalid email address format';
                    break;
                case 'auth/user-disabled':
                    message = 'This account has been disabled';
                    break;
                case 'auth/user-not-found':
                    message = 'No account found with this email';
                    break;
                case 'auth/wrong-password':
                    message = 'Invalid password';
                    break;
                case 'auth/popup-closed-by-user':
                    message = 'Sign in cancelled. Please try again.';
                    break;
                case 'auth/email-already-in-use':
                    message = 'An account already exists with this email';
                    break;
                case 'auth/network-request-failed':
                    message = 'Network error. Please check your connection.';
                    break;
                case 'auth/too-many-requests':
                    message = 'Too many attempts. Please try again later.';
                    break;
                case 'auth/operation-not-allowed':
                    message = 'This sign-in method is not enabled.';
                    break;
                default:
                    message = error.message || 'Authentication failed';
            }
        }

        updateState({ error: message, loading: false });
    }, [updateState]);

    // Helper function to fetch Firestore user data
    const fetchFirestoreUser = useCallback(async (uid: string, isFirstSignIn: boolean = false): Promise<FirestoreUser | null> => {
        const maxRetries = isFirstSignIn ? 5 : MAX_RETRIES; // More retries for first sign-in
        const retryDelay = isFirstSignIn ? 500 : RETRY_DELAY; // Shorter delay for first sign-in

        return await withRetry(async () => {
            const userDoc = await getDoc(doc(db, 'users', uid));
            return userDoc.exists() ? userDoc.data() as FirestoreUser : null;
        }, maxRetries, retryDelay);
    }, []);

    // Initialize auth state
    useEffect(() => {
        let mounted = true;

        const initializeAuth = async () => {
            try {
                // Set up persistence if in browser environment
                if (typeof window !== 'undefined') {
                    try {
                        await setPersistence(auth, browserLocalPersistence);
                        console.log('Auth persistence set to LOCAL');
                    } catch (error) {
                        console.error('Error setting auth persistence:', error);
                        // Continue even if persistence setup fails
                    }
                }

                // Set up auth state listener
                const unsubscribe = onAuthStateChanged(auth, async (user) => {
                    if (!mounted) return;

                    if (user) {
                        // Get user's profile from Firestore
                        const userDoc = await getDoc(doc(db, 'users', user.uid));
                        const userData = userDoc.data();

                        updateState({
                            user,
                            firestoreUser: userData as FirestoreUser,
                            loading: false,
                            error: null,
                            isInitialized: true
                        });

                        // Redirect to dashboard if on auth pages
                        if (pathname?.startsWith('/auth/')) {
                            router.push('/dashboard');
                        }
                    } else {
                        updateState({
                            user: null,
                            firestoreUser: null,
                            loading: false,
                            error: null,
                            isInitialized: true
                        });

                        // Redirect to sign in if not on public paths
                        if (pathname &&
                            !pathname.startsWith('/auth/') &&
                            pathname !== '/' &&
                            pathname !== '/contact-sales' &&
                            !pathname.startsWith('/jobs') &&
                            !pathname.startsWith('/instant-interview') &&
                            !pathname.startsWith('/pitch')) {
                            router.push('/auth/signin');
                        }
                    }
                });

                return unsubscribe;
            } catch (error) {
                console.error('Error initializing auth:', error);
                updateState({
                    loading: false,
                    error: 'Failed to initialize authentication',
                    isInitialized: true
                });
                return () => {}; // Return empty cleanup function in case of error
            }
        };

        initializeAuth();

        return () => {
            mounted = false;
        };
    }, [router, pathname, updateState]);

    const signInWithGoogle = useCallback(async () => {
        updateState({ loading: true, error: null });

        try {
            const result = await withRetry(async () => {
                const provider = new GoogleAuthProvider();
                provider.setCustomParameters({ prompt: 'select_account' });
                return await signInWithPopup(auth, provider);
            });

            const firestoreUser = await fetchFirestoreUser(result.user.uid, true);
            if (!firestoreUser) {
                await signOut(auth);
                throw new AuthError('Account not found. Please register first.');
            }

            updateState({
                user: result.user,
                firestoreUser,
                loading: false,
                error: null
            });

            router.push('/dashboard');
        } catch (error) {
            handleAuthError(error as Error | { code?: string; message?: string });
        }
    }, [router, fetchFirestoreUser, handleAuthError, updateState]);

    const signInWithEmail = useCallback(async (email: string, password: string) => {
        updateState({ loading: true, error: null });

        try {
            const result = await withRetry(async () => {
                return await signInWithEmailAndPassword(auth, email, password);
            });

            const firestoreUser = await fetchFirestoreUser(result.user.uid, true); // Pass true for first sign-in
            if (!firestoreUser) {
                await signOut(auth);
                throw new AuthError('Account not found. Please register first.');
            }

            updateState({
                user: result.user,
                firestoreUser,
                loading: false,
                error: null
            });

            router.push('/dashboard');
        } catch (error) {
            handleAuthError(error as Error | { code?: string; message?: string });
        }
    }, [router, fetchFirestoreUser, handleAuthError, updateState]);

    const registerWithGoogle = useCallback(async (requireWorkEmail: boolean = false) => {
        updateState({ loading: true, error: null });

        try {
            const result = await withRetry(async () => {
                const provider = new GoogleAuthProvider();
                provider.setCustomParameters({ prompt: 'select_account' });
                return await signInWithPopup(auth, provider);
            });

            const additionalInfo = getAdditionalUserInfo(result);
            if (!additionalInfo?.isNewUser) {
                throw new AuthError('An account already exists with this email');
            }

            const isGmailUser = result.user.email?.endsWith('@gmail.com');
            if (requireWorkEmail && isGmailUser) {
                await signOut(auth);
                throw new AuthError('Please use your work email address');
            }

            // Create Firestore user document
            const userData: Omit<FirestoreUser, 'uid'> = {
                email: result.user.email!,
                fullName: result.user.displayName || result.user.email!.split('@')[0],
                role: 'recruiter',
                createdAt: new Date(),
                updatedAt: new Date(),
                photoURL: result.user.photoURL || undefined,
                isWorkspaceUser: !isGmailUser,
                authProvider: 'google',
                status: 'active',
                emailVerified: result.user.emailVerified,
                lastSignInTime: result.user.metadata.lastSignInTime || new Date().toISOString(),
                accountType: isGmailUser ? 'personal' : 'workspace'
            };

            await setDoc(doc(db, 'users', result.user.uid), {
                ...userData,
                createdAt: serverTimestamp(),
                updatedAt: serverTimestamp()
            });

            const firestoreUser = await fetchFirestoreUser(result.user.uid);
            updateState({
                user: result.user,
                firestoreUser,
                loading: false,
                error: null
            });

            router.push('/dashboard');
        } catch (error) {
            if (auth.currentUser) {
                await signOut(auth);
            }
            handleAuthError(error as Error | { code?: string; message?: string });
        }
    }, [router, fetchFirestoreUser, handleAuthError, updateState]);

    const registerWithEmail = useCallback(async (email: string, password: string, name: string) => {
        updateState({ loading: true, error: null });

        try {
            const result = await withRetry(async () => {
                return await createUserWithEmailAndPassword(auth, email, password);
            });

            // Create Firestore user document
            const userData: Omit<FirestoreUser, 'uid'> = {
                email: result.user.email!,
                fullName: name,
                role: 'recruiter',
                createdAt: new Date(),
                updatedAt: new Date(),
                isWorkspaceUser: !email.endsWith('@gmail.com'),
                authProvider: 'email',
                status: 'active',
                emailVerified: result.user.emailVerified,
                lastSignInTime: result.user.metadata.lastSignInTime || new Date().toISOString(),
                accountType: email.endsWith('@gmail.com') ? 'personal' : 'workspace'
            };

            await setDoc(doc(db, 'users', result.user.uid), {
                ...userData,
                createdAt: serverTimestamp(),
                updatedAt: serverTimestamp()
            });

            const firestoreUser = await fetchFirestoreUser(result.user.uid);
            updateState({
                user: result.user,
                firestoreUser,
                loading: false,
                error: null
            });

            router.push('/dashboard');
        } catch (error) {
            if (auth.currentUser) {
                await signOut(auth);
            }
            handleAuthError(error as Error | { code?: string; message?: string });
        }
    }, [router, fetchFirestoreUser, handleAuthError, updateState]);

    const logout = useCallback(async () => {
        try {
            await signOut(auth);
            updateState({
                user: null,
                firestoreUser: null,
                loading: false,
                error: null
            });
            router.push('/auth/signin');
        } catch (error) {
            handleAuthError(error as Error | { code?: string; message?: string });
        }
    }, [router, handleAuthError, updateState]);

    const clearError = useCallback(() => {
        updateState({ error: null });
    }, [updateState]);

    if (state.loading && !state.isInitialized) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    return (
        <AuthContext.Provider value={{
            ...state,
            signInWithGoogle,
            signInWithEmail,
            registerWithGoogle,
            registerWithEmail,
            logout,
            clearError
        }}>
            {children}
        </AuthContext.Provider>
    );
}

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};