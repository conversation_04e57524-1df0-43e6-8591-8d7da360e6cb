import { renderHook, act } from '@testing-library/react';
import { useEvaluation, useRoleEvaluations, useInterviewEvaluations, useEvaluationRequest } from '@/hooks/useEvaluation';
import { evaluationService } from '@/services/evaluation';

// Mock the evaluation service
jest.mock('@/services/evaluation', () => ({
  evaluationService: {
    getEvaluation: jest.fn(),
    getEvaluationsByRole: jest.fn(),
    getEvaluationsByInterview: jest.fn(),
    evaluateInterview: jest.fn(),
    evaluatePublicInterview: jest.fn()
  }
}));

describe('Evaluation Hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('useEvaluation', () => {
    it('should fetch evaluation data and return it', async () => {
      const mockEvaluation = {
        id: 'eval-123',
        status: 'completed',
        data: { overallScore: 85 },
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-123',
        roleId: 'role-123'
      };

      (evaluationService.getEvaluation as jest.Mock).mockResolvedValueOnce(mockEvaluation);

      const { result, rerender } = renderHook(
        (props) => useEvaluation(props.evaluationId, props.isPublic),
        { initialProps: { evaluationId: 'eval-123', isPublic: false } }
      );

      // Initially should be loading
      expect(result.current.loading).toBe(true);
      expect(result.current.evaluation).toBe(null);
      expect(result.current.error).toBe(null);

      // Wait for the async operation to complete
      await act(async () => {
        await Promise.resolve();
      });

      // Should have loaded the evaluation
      expect(result.current.loading).toBe(false);
      expect(result.current.evaluation).toBe(mockEvaluation);
      expect(result.current.error).toBe(null);
      expect(evaluationService.getEvaluation).toHaveBeenCalledWith('eval-123', false, false);

      // Test with isPublic = true
      (evaluationService.getEvaluation as jest.Mock).mockResolvedValueOnce(mockEvaluation);

      rerender({ evaluationId: 'eval-123', isPublic: true });

      await act(async () => {
        await Promise.resolve();
      });

      expect(evaluationService.getEvaluation).toHaveBeenCalledWith('eval-123', false, true);
    });

    it('should poll for updates if evaluation status is pending', async () => {
      const pendingEvaluation = {
        id: 'eval-123',
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-123',
        roleId: 'role-123'
      };

      const completedEvaluation = {
        ...pendingEvaluation,
        status: 'completed',
        data: { overallScore: 85 }
      };

      // First call returns pending, second call returns completed
      (evaluationService.getEvaluation as jest.Mock)
        .mockResolvedValueOnce(pendingEvaluation)
        .mockResolvedValueOnce(completedEvaluation);

      const { result } = renderHook(() => useEvaluation('eval-123', false, 1000));

      // Initially should be loading
      expect(result.current.loading).toBe(true);

      // Wait for the first async operation to complete
      await act(async () => {
        await Promise.resolve();
      });

      // Should have loaded the pending evaluation
      expect(result.current.loading).toBe(false);
      expect(result.current.evaluation).toBe(pendingEvaluation);

      // Fast-forward timer to trigger polling
      await act(async () => {
        jest.advanceTimersByTime(1000);
        await Promise.resolve();
      });

      // Should have updated to the completed evaluation
      expect(result.current.evaluation).toBe(completedEvaluation);
      expect(evaluationService.getEvaluation).toHaveBeenCalledTimes(2);
    });

    it('should handle errors correctly', async () => {
      const mockError = new Error('API error');
      (evaluationService.getEvaluation as jest.Mock).mockRejectedValueOnce(mockError);

      const { result } = renderHook(() => useEvaluation('eval-123'));

      // Wait for the async operation to complete
      await act(async () => {
        await Promise.resolve();
      });

      // Should have set the error
      expect(result.current.loading).toBe(false);
      expect(result.current.evaluation).toBe(null);
      expect(result.current.error).toEqual(mockError);
    });

    it('should provide a refreshEvaluation function that works correctly', async () => {
      const initialEvaluation = {
        id: 'eval-123',
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-123',
        roleId: 'role-123'
      };

      const updatedEvaluation = {
        ...initialEvaluation,
        status: 'completed',
        data: { overallScore: 85 }
      };

      (evaluationService.getEvaluation as jest.Mock)
        .mockResolvedValueOnce(initialEvaluation)
        .mockResolvedValueOnce(updatedEvaluation);

      const { result } = renderHook(() => useEvaluation('eval-123'));

      // Wait for the initial load
      await act(async () => {
        await Promise.resolve();
      });

      // Should have loaded the initial evaluation
      expect(result.current.evaluation).toBe(initialEvaluation);

      // Call refreshEvaluation
      await act(async () => {
        await result.current.refreshEvaluation();
      });

      // Should have updated to the new evaluation
      expect(result.current.evaluation).toBe(updatedEvaluation);
      expect(evaluationService.getEvaluation).toHaveBeenCalledTimes(2);
      expect(evaluationService.getEvaluation).toHaveBeenLastCalledWith('eval-123', true, false);
    });
  });

  describe('useRoleEvaluations', () => {
    it('should fetch evaluations for a role and return them', async () => {
      const mockEvaluations = [
        {
          id: 'eval-123',
          status: 'completed',
          data: { overallScore: 85 },
          createdAt: new Date(),
          updatedAt: new Date(),
          interviewId: 'interview-123',
          roleId: 'role-123'
        }
      ];

      (evaluationService.getEvaluationsByRole as jest.Mock).mockResolvedValueOnce(mockEvaluations);

      const { result } = renderHook(() => useRoleEvaluations('role-123'));

      // Initially should be loading
      expect(result.current.loading).toBe(true);
      expect(result.current.evaluations).toEqual([]);
      expect(result.current.error).toBe(null);

      // Wait for the async operation to complete
      await act(async () => {
        await Promise.resolve();
      });

      // Should have loaded the evaluations
      expect(result.current.loading).toBe(false);
      expect(result.current.evaluations).toBe(mockEvaluations);
      expect(result.current.error).toBe(null);
      expect(evaluationService.getEvaluationsByRole).toHaveBeenCalledWith('role-123');
    });

    it('should provide a refreshEvaluations function that works correctly', async () => {
      const initialEvaluations = [
        {
          id: 'eval-123',
          status: 'completed',
          data: { overallScore: 85 },
          createdAt: new Date(),
          updatedAt: new Date(),
          interviewId: 'interview-123',
          roleId: 'role-123'
        }
      ];

      const updatedEvaluations = [
        ...initialEvaluations,
        {
          id: 'eval-456',
          status: 'completed',
          data: { overallScore: 90 },
          createdAt: new Date(),
          updatedAt: new Date(),
          interviewId: 'interview-456',
          roleId: 'role-123'
        }
      ];

      (evaluationService.getEvaluationsByRole as jest.Mock)
        .mockResolvedValueOnce(initialEvaluations)
        .mockResolvedValueOnce(updatedEvaluations);

      const { result } = renderHook(() => useRoleEvaluations('role-123'));

      // Wait for the initial load
      await act(async () => {
        await Promise.resolve();
      });

      // Should have loaded the initial evaluations
      expect(result.current.evaluations).toBe(initialEvaluations);

      // Call refreshEvaluations
      await act(async () => {
        await result.current.refreshEvaluations();
      });

      // Should have updated to the new evaluations
      expect(result.current.evaluations).toBe(updatedEvaluations);
      expect(evaluationService.getEvaluationsByRole).toHaveBeenCalledTimes(2);
    });
  });

  describe('useInterviewEvaluations', () => {
    it('should fetch evaluations for an interview and return them', async () => {
      const mockEvaluations = [
        {
          id: 'eval-123',
          status: 'completed',
          data: { overallScore: 85 },
          createdAt: new Date(),
          updatedAt: new Date(),
          interviewId: 'interview-123',
          roleId: 'role-123'
        }
      ];

      (evaluationService.getEvaluationsByInterview as jest.Mock).mockResolvedValueOnce(mockEvaluations);

      const { result } = renderHook(() => useInterviewEvaluations('interview-123'));

      // Wait for the async operation to complete
      await act(async () => {
        await Promise.resolve();
      });

      // Should have loaded the evaluations
      expect(result.current.loading).toBe(false);
      expect(result.current.evaluations).toBe(mockEvaluations);
      expect(result.current.error).toBe(null);
      expect(evaluationService.getEvaluationsByInterview).toHaveBeenCalledWith('interview-123');
    });
  });

  describe('useEvaluationRequest', () => {
    it('should request an authenticated evaluation correctly', async () => {
      const mockEvaluationId = 'eval-123';
      (evaluationService.evaluateInterview as jest.Mock).mockResolvedValueOnce(mockEvaluationId);

      const { result } = renderHook(() => useEvaluationRequest());

      // Initially should not be loading
      expect(result.current.loading).toBe(false);
      expect(result.current.evaluationId).toBe(null);
      expect(result.current.error).toBe(null);

      // Request an evaluation
      let returnedId: string | undefined;
      await act(async () => {
        returnedId = await result.current.requestEvaluation('interview-123', 'role-123', 'app-123');
      });

      // Should have set the evaluation ID and called the service
      expect(result.current.loading).toBe(false);
      expect(result.current.evaluationId).toBe(mockEvaluationId);
      expect(result.current.error).toBe(null);
      expect(returnedId).toBe(mockEvaluationId);
      expect(evaluationService.evaluateInterview).toHaveBeenCalledWith('interview-123', 'role-123', 'app-123');
    });

    it('should request a public evaluation correctly', async () => {
      const mockEvaluationId = 'eval-123';
      (evaluationService.evaluatePublicInterview as jest.Mock).mockResolvedValueOnce(mockEvaluationId);

      const { result } = renderHook(() => useEvaluationRequest(true));

      // Request an evaluation
      await act(async () => {
        await result.current.requestEvaluation('interview-123', 'role-123');
      });

      // Should have called the public service
      expect(evaluationService.evaluatePublicInterview).toHaveBeenCalledWith('interview-123', 'role-123', undefined);
      expect(evaluationService.evaluateInterview).not.toHaveBeenCalled();
    });

    it('should handle errors correctly', async () => {
      const mockError = new Error('API error');
      (evaluationService.evaluateInterview as jest.Mock).mockRejectedValueOnce(mockError);

      const { result } = renderHook(() => useEvaluationRequest());

      // Request an evaluation that will fail
      await act(async () => {
        try {
          await result.current.requestEvaluation('interview-123', 'role-123');
        } catch (_error) {
          // Expected error
        }
      });

      // Should have set the error
      expect(result.current.loading).toBe(false);
      expect(result.current.evaluationId).toBe(null);
      expect(result.current.error).toEqual(mockError);
    });
  });
});
