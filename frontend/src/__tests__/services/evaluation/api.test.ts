import { 
  requestEvaluation, 
  getEvaluation, 
  getEvaluationsByRole, 
  getEvaluationsByInterview,
  requestPublicEvaluation,
  getPublicEvaluation
} from '@/services/evaluation/api';
import { apiClient } from '@/lib/api/client/axios';

// Mock the axios client
jest.mock('@/lib/api/client/axios', () => ({
  apiClient: {
    post: jest.fn(),
    get: jest.fn()
  }
}));

describe('Evaluation API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('requestEvaluation', () => {
    it('should call the correct endpoint with the right parameters', async () => {
      const mockResponse = { data: { evaluation_id: 'eval-123' } };
      (apiClient.post as jest.Mock).mockResolvedValueOnce(mockResponse);

      const params = {
        interviewId: 'interview-123',
        roleId: 'role-123',
        applicationId: 'app-123'
      };

      const result = await requestEvaluation(params);

      expect(apiClient.post).toHaveBeenCalledWith('/evaluations', params);
      expect(result).toBe('eval-123');
    });

    it('should throw an error when the API call fails', async () => {
      const mockError = new Error('API error');
      (apiClient.post as jest.Mock).mockRejectedValueOnce(mockError);

      const params = {
        interviewId: 'interview-123',
        roleId: 'role-123'
      };

      await expect(requestEvaluation(params)).rejects.toThrow('API error');
    });
  });

  describe('getEvaluation', () => {
    it('should call the correct endpoint and transform the response', async () => {
      const mockApiResponse = {
        data: {
          id: 'eval-123',
          status: 'completed',
          data: { overallScore: 85 },
          created_at: '2025-03-28T12:00:00Z',
          updated_at: '2025-03-28T12:30:00Z',
          interview_id: 'interview-123',
          role_id: 'role-123',
          application_id: 'app-123'
        }
      };
      (apiClient.get as jest.Mock).mockResolvedValueOnce(mockApiResponse);

      const result = await getEvaluation('eval-123');

      expect(apiClient.get).toHaveBeenCalledWith('/evaluations/eval-123');
      expect(result).toEqual({
        id: 'eval-123',
        status: 'completed',
        data: { overallScore: 85 },
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
        interviewId: 'interview-123',
        roleId: 'role-123',
        applicationId: 'app-123',
        error: undefined
      });
    });
  });

  describe('getEvaluationsByRole', () => {
    it('should call the correct endpoint and transform the response', async () => {
      const mockApiResponse = {
        data: [
          {
            id: 'eval-123',
            status: 'completed',
            data: { overallScore: 85 },
            created_at: '2025-03-28T12:00:00Z',
            updated_at: '2025-03-28T12:30:00Z',
            interview_id: 'interview-123',
            role_id: 'role-123',
            application_id: 'app-123'
          }
        ]
      };
      (apiClient.get as jest.Mock).mockResolvedValueOnce(mockApiResponse);

      const result = await getEvaluationsByRole('role-123');

      expect(apiClient.get).toHaveBeenCalledWith('/evaluations?roleId=role-123');
      expect(result).toEqual([
        {
          id: 'eval-123',
          status: 'completed',
          data: { overallScore: 85 },
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
          interviewId: 'interview-123',
          roleId: 'role-123',
          applicationId: 'app-123',
          error: undefined
        }
      ]);
    });
  });

  describe('getEvaluationsByInterview', () => {
    it('should call the correct endpoint and transform the response', async () => {
      const mockApiResponse = {
        data: [
          {
            id: 'eval-123',
            status: 'completed',
            data: { overallScore: 85 },
            created_at: '2025-03-28T12:00:00Z',
            updated_at: '2025-03-28T12:30:00Z',
            interview_id: 'interview-123',
            role_id: 'role-123',
            application_id: 'app-123'
          }
        ]
      };
      (apiClient.get as jest.Mock).mockResolvedValueOnce(mockApiResponse);

      const result = await getEvaluationsByInterview('interview-123');

      expect(apiClient.get).toHaveBeenCalledWith('/evaluations?interviewId=interview-123');
      expect(result).toEqual([
        {
          id: 'eval-123',
          status: 'completed',
          data: { overallScore: 85 },
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
          interviewId: 'interview-123',
          roleId: 'role-123',
          applicationId: 'app-123',
          error: undefined
        }
      ]);
    });
  });

  describe('requestPublicEvaluation', () => {
    it('should call the correct endpoint with the right parameters', async () => {
      const mockResponse = { data: { evaluation_id: 'eval-123' } };
      (apiClient.post as jest.Mock).mockResolvedValueOnce(mockResponse);

      const params = {
        interviewId: 'interview-123',
        roleId: 'role-123',
        applicationId: 'app-123'
      };

      const result = await requestPublicEvaluation(params);

      expect(apiClient.post).toHaveBeenCalledWith('/public/evaluations', params);
      expect(result).toBe('eval-123');
    });
  });

  describe('getPublicEvaluation', () => {
    it('should call the correct endpoint and transform the response', async () => {
      const mockApiResponse = {
        data: {
          id: 'eval-123',
          status: 'completed',
          data: { overallScore: 85 },
          created_at: '2025-03-28T12:00:00Z',
          updated_at: '2025-03-28T12:30:00Z',
          interview_id: 'interview-123',
          role_id: 'role-123',
          application_id: 'app-123'
        }
      };
      (apiClient.get as jest.Mock).mockResolvedValueOnce(mockApiResponse);

      const result = await getPublicEvaluation('eval-123');

      expect(apiClient.get).toHaveBeenCalledWith('/public/evaluations/eval-123');
      expect(result).toEqual({
        id: 'eval-123',
        status: 'completed',
        data: { overallScore: 85 },
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
        interviewId: 'interview-123',
        roleId: 'role-123',
        applicationId: 'app-123',
        error: undefined
      });
    });
  });
});
