import { EvaluationService } from '@/services/evaluation/service';
import * as api from '@/services/evaluation/api';

// Mock the API module
jest.mock('@/services/evaluation/api', () => ({
  requestEvaluation: jest.fn(),
  getEvaluation: jest.fn(),
  getEvaluationsByRole: jest.fn(),
  getEvaluationsByInterview: jest.fn(),
  requestPublicEvaluation: jest.fn(),
  getPublicEvaluation: jest.fn()
}));

describe('EvaluationService', () => {
  let service: EvaluationService;
  
  beforeEach(() => {
    jest.clearAllMocks();
    // Get a fresh instance for testing
    service = new EvaluationService();
    
    // Clear the cache for each test
    service.clearCache();
  });

  describe('getInstance', () => {
    it('should return the same instance when called multiple times', () => {
      const instance1 = new EvaluationService();
      const instance2 = new EvaluationService();
      
      expect(instance1).not.toBe(instance2);
    });
  });

  describe('evaluateInterview', () => {
    it('should call requestEvaluation with the correct parameters', async () => {
      const mockEvaluationId = 'eval-123';
      (api.requestEvaluation as jest.Mock).mockResolvedValueOnce(mockEvaluationId);
      
      const result = await service.evaluateInterview('interview-123', 'role-123', 'app-123');
      
      expect(api.requestEvaluation).toHaveBeenCalledWith({
        interviewId: 'interview-123',
        roleId: 'role-123',
        applicationId: 'app-123'
      });
      expect(result).toBe(mockEvaluationId);
    });
    
    it('should handle errors from the API', async () => {
      const mockError = new Error('API error');
      (api.requestEvaluation as jest.Mock).mockRejectedValueOnce(mockError);
      
      await expect(service.evaluateInterview('interview-123', 'role-123'))
        .rejects.toThrow('API error');
    });
  });

  describe('evaluatePublicInterview', () => {
    it('should call requestPublicEvaluation with the correct parameters', async () => {
      const mockEvaluationId = 'eval-123';
      (api.requestPublicEvaluation as jest.Mock).mockResolvedValueOnce(mockEvaluationId);
      
      const result = await service.evaluatePublicInterview('interview-123', 'role-123', 'app-123');
      
      expect(api.requestPublicEvaluation).toHaveBeenCalledWith({
        interviewId: 'interview-123',
        roleId: 'role-123',
        applicationId: 'app-123'
      });
      expect(result).toBe(mockEvaluationId);
    });
  });

  describe('getEvaluation', () => {
    it('should return cached evaluation if available and not pending', async () => {
      // Setup a completed evaluation in the cache
      const mockEvaluation = {
        id: 'eval-123',
        status: 'completed' as const,
        data: { overallScore: 85 },
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-123',
        roleId: 'role-123'
      };
      
      // Manually add to cache
      await service.getEvaluation('eval-123', false, false);
      
      // Mock the API response for the first call
      (api.getEvaluation as jest.Mock).mockResolvedValueOnce(mockEvaluation);
      
      // First call should hit the API
      await service.getEvaluation('eval-123');
      
      // Reset the mock to verify it's not called again
      (api.getEvaluation as jest.Mock).mockClear();
      
      // Second call should use the cache
      const result = await service.getEvaluation('eval-123');
      
      expect(api.getEvaluation).not.toHaveBeenCalled();
      expect(result).toEqual(mockEvaluation);
    });
    
    it('should fetch from API if evaluation is not in cache', async () => {
      const mockEvaluation = {
        id: 'eval-123',
        status: 'completed' as const,
        data: { overallScore: 85 },
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-123',
        roleId: 'role-123'
      };
      
      (api.getEvaluation as jest.Mock).mockResolvedValueOnce(mockEvaluation);
      
      const result = await service.getEvaluation('eval-123');
      
      expect(api.getEvaluation).toHaveBeenCalledWith('eval-123');
      expect(result).toEqual(mockEvaluation);
    });
    
    it('should fetch from API if evaluation is in cache but status is pending', async () => {
      // Setup a pending evaluation
      const pendingEvaluation = {
        id: 'eval-123',
        status: 'pending' as const,
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-123',
        roleId: 'role-123'
      };
      
      const completedEvaluation = {
        ...pendingEvaluation,
        status: 'completed' as const,
        data: { overallScore: 85 }
      };
      
      // First call returns pending
      (api.getEvaluation as jest.Mock).mockResolvedValueOnce(pendingEvaluation);
      
      // First call to populate the cache
      await service.getEvaluation('eval-123');
      
      // Reset the mock
      (api.getEvaluation as jest.Mock).mockClear();
      
      // Second call should return completed
      (api.getEvaluation as jest.Mock).mockResolvedValueOnce(completedEvaluation);
      
      // Second call should still hit the API because status is pending
      const result = await service.getEvaluation('eval-123');
      
      expect(api.getEvaluation).toHaveBeenCalledWith('eval-123');
      expect(result).toEqual(completedEvaluation);
    });
    
    it('should fetch from API if forceRefresh is true', async () => {
      // Setup a completed evaluation
      const initialEvaluation = {
        id: 'eval-123',
        status: 'completed' as const,
        data: { overallScore: 85 },
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-123',
        roleId: 'role-123'
      };
      
      const updatedEvaluation = {
        ...initialEvaluation,
        data: { overallScore: 90 }
      };
      
      // First call returns initial
      (api.getEvaluation as jest.Mock).mockResolvedValueOnce(initialEvaluation);
      
      // First call to populate the cache
      await service.getEvaluation('eval-123');
      
      // Reset the mock
      (api.getEvaluation as jest.Mock).mockClear();
      
      // Second call should return updated
      (api.getEvaluation as jest.Mock).mockResolvedValueOnce(updatedEvaluation);
      
      // Second call with forceRefresh should hit the API
      const result = await service.getEvaluation('eval-123', true);
      
      expect(api.getEvaluation).toHaveBeenCalledWith('eval-123');
      expect(result).toEqual(updatedEvaluation);
    });
    
    it('should call getPublicEvaluation if isPublic is true', async () => {
      const mockEvaluation = {
        id: 'eval-123',
        status: 'completed' as const,
        data: { overallScore: 85 },
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-123',
        roleId: 'role-123'
      };
      
      (api.getPublicEvaluation as jest.Mock).mockResolvedValueOnce(mockEvaluation);
      
      const result = await service.getEvaluation('eval-123', false, true);
      
      expect(api.getPublicEvaluation).toHaveBeenCalledWith('eval-123');
      expect(api.getEvaluation).not.toHaveBeenCalled();
      expect(result).toEqual(mockEvaluation);
    });
  });

  describe('getEvaluationsByRole', () => {
    it('should call getEvaluationsByRole with the correct parameter', async () => {
      const mockEvaluations = [
        {
          id: 'eval-123',
          status: 'completed' as const,
          data: { overallScore: 85 },
          createdAt: new Date(),
          updatedAt: new Date(),
          interviewId: 'interview-123',
          roleId: 'role-123'
        }
      ];
      
      (api.getEvaluationsByRole as jest.Mock).mockResolvedValueOnce(mockEvaluations);
      
      const result = await service.getEvaluationsByRole('role-123');
      
      expect(api.getEvaluationsByRole).toHaveBeenCalledWith('role-123');
      expect(result).toEqual(mockEvaluations);
    });
    
    it('should update the cache with fetched evaluations', async () => {
      const mockEvaluations = [
        {
          id: 'eval-123',
          status: 'completed' as const,
          data: { overallScore: 85 },
          createdAt: new Date(),
          updatedAt: new Date(),
          interviewId: 'interview-123',
          roleId: 'role-123'
        },
        {
          id: 'eval-456',
          status: 'completed' as const,
          data: { overallScore: 90 },
          createdAt: new Date(),
          updatedAt: new Date(),
          interviewId: 'interview-456',
          roleId: 'role-123'
        }
      ];
      
      (api.getEvaluationsByRole as jest.Mock).mockResolvedValueOnce(mockEvaluations);
      
      // First call to populate the cache
      await service.getEvaluationsByRole('role-123');
      
      // Reset the mocks
      (api.getEvaluation as jest.Mock).mockClear();
      (api.getEvaluationsByRole as jest.Mock).mockClear();
      
      // Now try to get one of the evaluations from the cache
      await service.getEvaluation('eval-123');
      
      // Should not call the API again
      expect(api.getEvaluation).not.toHaveBeenCalled();
    });
  });

  describe('getEvaluationsByInterview', () => {
    it('should call getEvaluationsByInterview with the correct parameter', async () => {
      const mockEvaluations = [
        {
          id: 'eval-123',
          status: 'completed' as const,
          data: { overallScore: 85 },
          createdAt: new Date(),
          updatedAt: new Date(),
          interviewId: 'interview-123',
          roleId: 'role-123'
        }
      ];
      
      (api.getEvaluationsByInterview as jest.Mock).mockResolvedValueOnce(mockEvaluations);
      
      const result = await service.getEvaluationsByInterview('interview-123');
      
      expect(api.getEvaluationsByInterview).toHaveBeenCalledWith('interview-123');
      expect(result).toEqual(mockEvaluations);
    });
  });

  describe('clearCache', () => {
    it('should clear a specific evaluation from the cache', async () => {
      // Setup evaluations in the cache
      const mockEvaluation1 = {
        id: 'eval-123',
        status: 'completed' as const,
        data: { overallScore: 85 },
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-123',
        roleId: 'role-123'
      };
      
      const mockEvaluation2 = {
        id: 'eval-456',
        status: 'completed' as const,
        data: { overallScore: 90 },
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-456',
        roleId: 'role-123'
      };
      
      // Mock API responses
      (api.getEvaluation as jest.Mock)
        .mockResolvedValueOnce(mockEvaluation1)
        .mockResolvedValueOnce(mockEvaluation2)
        .mockResolvedValueOnce(mockEvaluation1);
      
      // Populate the cache
      await service.getEvaluation('eval-123');
      await service.getEvaluation('eval-456');
      
      // Reset the mock
      (api.getEvaluation as jest.Mock).mockClear();
      
      // Verify both are in cache
      await service.getEvaluation('eval-123');
      await service.getEvaluation('eval-456');
      
      // Should not have called the API
      expect(api.getEvaluation).not.toHaveBeenCalled();
      
      // Clear one evaluation
      service.clearCache('eval-123');
      
      // Reset the mock
      (api.getEvaluation as jest.Mock).mockClear();
      
      // Try to get both evaluations
      await service.getEvaluation('eval-123');
      await service.getEvaluation('eval-456');
      
      // Should have called the API for the cleared evaluation but not for the other
      expect(api.getEvaluation).toHaveBeenCalledTimes(1);
      expect(api.getEvaluation).toHaveBeenCalledWith('eval-123');
    });
    
    it('should clear all evaluations from the cache when no ID is provided', async () => {
      // Setup evaluations in the cache
      const mockEvaluation1 = {
        id: 'eval-123',
        status: 'completed' as const,
        data: { overallScore: 85 },
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-123',
        roleId: 'role-123'
      };
      
      const mockEvaluation2 = {
        id: 'eval-456',
        status: 'completed' as const,
        data: { overallScore: 90 },
        createdAt: new Date(),
        updatedAt: new Date(),
        interviewId: 'interview-456',
        roleId: 'role-123'
      };
      
      // Mock API responses
      (api.getEvaluation as jest.Mock)
        .mockResolvedValueOnce(mockEvaluation1)
        .mockResolvedValueOnce(mockEvaluation2)
        .mockResolvedValueOnce(mockEvaluation1)
        .mockResolvedValueOnce(mockEvaluation2);
      
      // Populate the cache
      await service.getEvaluation('eval-123');
      await service.getEvaluation('eval-456');
      
      // Reset the mock
      (api.getEvaluation as jest.Mock).mockClear();
      
      // Verify both are in cache
      await service.getEvaluation('eval-123');
      await service.getEvaluation('eval-456');
      
      // Should not have called the API
      expect(api.getEvaluation).not.toHaveBeenCalled();
      
      // Clear all evaluations
      service.clearCache();
      
      // Reset the mock
      (api.getEvaluation as jest.Mock).mockClear();
      
      // Try to get both evaluations
      await service.getEvaluation('eval-123');
      await service.getEvaluation('eval-456');
      
      // Should have called the API for both evaluations
      expect(api.getEvaluation).toHaveBeenCalledTimes(2);
    });
  });
});
