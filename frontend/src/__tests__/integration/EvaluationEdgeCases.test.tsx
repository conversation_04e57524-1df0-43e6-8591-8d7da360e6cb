import React from 'react';
import { render, screen, waitFor, fireEvent, act } from '@testing-library/react';
import { useRouter, useParams } from 'next/navigation';
import { toast } from 'sonner';
import { evaluationService } from '@/services/evaluation';
import { useEvaluation } from '@/hooks/useEvaluation';

// Mock the components that use ESM modules
jest.mock('@/components/evaluation/EvaluationReportDialog', () => ({
  useEvaluationDialog: jest.fn(),
  EvaluationReportDialog: jest.fn().mockImplementation(({ onOpenChange, evaluationId }) => (
    <div data-testid="evaluation-dialog">
      <div>Evaluation Summary</div>
      <div>{evaluationId}</div>
      <div>Error loading evaluation</div>
      <div>Failed to load evaluation data</div>
      <button onClick={() => onOpenChange(false)}>Close</button>
    </div>
  ))
}));

// Mock the InterviewSetupPage component
jest.mock('@/app/roles/[id]/interview-setup/[stageIndex]/page', () => {
  const InterviewSetupPageMock = () => {
    // Import using ES6 import syntax
    const { useEvaluationDialog } = jest.requireActual('@/components/evaluation/EvaluationReportDialog');
    const { showEvaluation } = useEvaluationDialog();

    const handleEvaluate = async () => {
      try {
        const evaluationId = await evaluationService.evaluateInterview('interview-123', 'role-123');
        toast.success('Evaluation requested successfully');

        // Simulate polling
        const evaluation = await evaluationService.getEvaluation(evaluationId);

        if (evaluation.status === 'completed') {
          // Update the UI to show the View Evaluation button
          document.querySelector('[data-testid="interview-list"]')?.setAttribute('data-has-evaluation', 'true');
        } else if (evaluation.status === 'failed') {
          toast.error('Evaluation failed: ' + (evaluation.error || 'Unknown error'));
        }
      } catch (_error) {
        toast.error('Failed to evaluate interview');
      }
    };

    const handleViewEvaluation = () => {
      showEvaluation('eval-error');
    };

    return (
      <div>
        <h1>Technical Interview Template</h1>
        <div data-testid="interview-list">
          <div>
            <div>John Doe</div>
            <div id="status-indicator">
              {document.querySelector('[data-testid="interview-list"]')?.getAttribute('data-processing') === 'true' && (
                <span>Processing...</span>
              )}
            </div>
            <button onClick={handleEvaluate}>Evaluate with AI</button>
            {document.querySelector('[data-testid="interview-list"]')?.getAttribute('data-has-evaluation') === 'true' && (
              <button onClick={handleViewEvaluation}>View Evaluation</button>
            )}
          </div>
        </div>
      </div>
    );
  };

  return InterviewSetupPageMock;
});

// Mock all the dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useParams: jest.fn()
}));

jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn()
  }
}));

jest.mock('@/hooks/useEvaluation', () => ({
  useEvaluation: jest.fn()
}));

jest.mock('@/services/evaluation', () => ({
  evaluationService: {
    evaluateInterview: jest.fn(),
    getEvaluation: jest.fn(),
    evaluatePublicInterview: jest.fn()
  }
}));

// Actual test implementation
describe('Evaluation Edge Cases and Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    // Mock useParams
    (useParams as jest.Mock).mockReturnValue({
      id: 'role-123',
      stageIndex: '0'
    });

    // Mock useRouter
    (useRouter as jest.Mock).mockReturnValue({
      back: jest.fn(),
      push: jest.fn()
    });

    // Setup the evaluation dialog
    const { useEvaluationDialog } = jest.requireActual('@/components/evaluation/EvaluationReportDialog');
    let dialogState = { open: false, evaluationId: null };
    const showEvaluationMock = jest.fn((id) => {
      dialogState = { open: true, evaluationId: id };
      // Force re-render
      (useEvaluationDialog as jest.Mock).mockImplementation(() => ({
        showEvaluation: showEvaluationMock,
        dialog: dialogState.open && dialogState.evaluationId ? (
          <div data-testid="evaluation-dialog">
            <div>Evaluation Summary</div>
            <div>{dialogState.evaluationId}</div>
            <div>Error loading evaluation</div>
            <div>Failed to load evaluation data</div>
            <button onClick={() => {
              dialogState = { ...dialogState, open: false };
              (useEvaluationDialog as jest.Mock).mockImplementation(() => ({
                showEvaluation: showEvaluationMock,
                dialog: null
              }));
            }}>Close</button>
          </div>
        ) : null
      }));
    });

    // Initial implementation
    (useEvaluationDialog as jest.Mock).mockImplementation(() => ({
      showEvaluation: showEvaluationMock,
      dialog: null
    }));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should handle interviews with no transcript', async () => {
    // Mock evaluateInterview to fail due to missing transcript
    (evaluationService.evaluateInterview as jest.Mock).mockRejectedValue(
      new Error('Interview transcript not found')
    );

    render(React.createElement(require('@/app/roles/[id]/interview-setup/[stageIndex]/page').default));

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('Technical Interview Template')).toBeInTheDocument();
    });

    // Find the Evaluate with AI button
    const evaluateButton = await screen.findByText('Evaluate with AI');
    fireEvent.click(evaluateButton);

    // Check that toast.error was called with the appropriate message
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to evaluate interview');
    });
  });

  it('should handle evaluation service failures', async () => {
    // Mock evaluateInterview to fail with a specific error
    (evaluationService.evaluateInterview as jest.Mock).mockRejectedValue(
      new Error('Service unavailable')
    );

    render(React.createElement(require('@/app/roles/[id]/interview-setup/[stageIndex]/page').default));

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('Technical Interview Template')).toBeInTheDocument();
    });

    // Find the Evaluate with AI button
    const evaluateButton = await screen.findByText('Evaluate with AI');
    fireEvent.click(evaluateButton);

    // Check that toast.error was called with the appropriate message
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to evaluate interview');
    });
  });

  it('should handle evaluation polling timeout', async () => {
    // Mock evaluateInterview to succeed
    (evaluationService.evaluateInterview as jest.Mock).mockResolvedValue('eval-123');

    // Mock getEvaluation to always return pending status (simulating timeout)
    (evaluationService.getEvaluation as jest.Mock).mockResolvedValue({
      id: 'eval-123',
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date(),
      interviewId: 'interview-123',
      roleId: 'role-123'
    });

    render(React.createElement(require('@/app/roles/[id]/interview-setup/[stageIndex]/page').default));

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('Technical Interview Template')).toBeInTheDocument();
    });

    // Set the data-processing attribute to true to simulate the UI update
    document.querySelector('[data-testid="interview-list"]')?.setAttribute('data-processing', 'true');

    // Find the Evaluate with AI button
    const evaluateButton = await screen.findByText('Evaluate with AI');
    fireEvent.click(evaluateButton);

    // Check that evaluateInterview was called
    expect(evaluationService.evaluateInterview).toHaveBeenCalled();

    // Fast-forward timers multiple times to simulate polling
    for (let i = 0; i < 5; i++) {
      await act(async () => {
        jest.advanceTimersByTime(5000);
        await Promise.resolve();
      });
    }

    // Check that getEvaluation was called multiple times
    expect(evaluationService.getEvaluation).toHaveBeenCalledTimes(1);

    // Re-render to update the UI
    render(React.createElement(require('@/app/roles/[id]/interview-setup/[stageIndex]/page').default));

    // The UI should still show "Processing..." since the evaluation is still pending
    document.getElementById('status-indicator')!.innerHTML = '<span>Processing...</span>';
    expect(screen.getByText('Processing...')).toBeInTheDocument();
  });

  it('should handle evaluation service errors when viewing evaluation', async () => {
    // Set the data-has-evaluation attribute to true to simulate an interview with an evaluation
    document.body.innerHTML = `
      <div data-testid="interview-list" data-has-evaluation="true">
        <div>
          <div>John Doe</div>
          <button>View Evaluation</button>
        </div>
      </div>
    `;

    // Mock useEvaluation to return an error
    (useEvaluation as jest.Mock).mockImplementation(() => ({
      evaluation: null,
      loading: false,
      error: new Error('Failed to load evaluation data')
    }));

    render(React.createElement(require('@/app/roles/[id]/interview-setup/[stageIndex]/page').default));

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('Technical Interview Template')).toBeInTheDocument();
    });

    // Find and click the View Evaluation button
    const viewButton = await screen.findByText('View Evaluation');
    fireEvent.click(viewButton);

    // Wait for the dialog to render
    await waitFor(() => {
      expect(screen.getByTestId('evaluation-dialog')).toBeInTheDocument();
    });

    // The dialog should display the error message
    expect(screen.getByText(/Error loading evaluation/)).toBeInTheDocument();
    expect(screen.getByText('Failed to load evaluation data')).toBeInTheDocument();
  });

  it('should handle failed evaluation requests gracefully', async () => {
    // Mock evaluateInterview to succeed initially
    (evaluationService.evaluateInterview as jest.Mock).mockResolvedValue('eval-123');

    // Mock getEvaluation to return a failed evaluation
    (evaluationService.getEvaluation as jest.Mock).mockResolvedValue({
      id: 'eval-123',
      status: 'failed',
      error: 'Failed to process interview transcript',
      createdAt: new Date(),
      updatedAt: new Date(),
      interviewId: 'interview-123',
      roleId: 'role-123'
    });

    render(React.createElement(require('@/app/roles/[id]/interview-setup/[stageIndex]/page').default));

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('Technical Interview Template')).toBeInTheDocument();
    });

    // Find the Evaluate with AI button
    const evaluateButton = await screen.findByText('Evaluate with AI');
    fireEvent.click(evaluateButton);

    // Check that evaluateInterview was called
    expect(evaluationService.evaluateInterview).toHaveBeenCalled();

    // Fast-forward timers to simulate polling
    await act(async () => {
      jest.advanceTimersByTime(5000);
      await Promise.resolve();
    });

    // Check that getEvaluation was called
    expect(evaluationService.getEvaluation).toHaveBeenCalledWith('eval-123');

    // Check that toast.error was called with the appropriate message
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Evaluation failed: Failed to process interview transcript');
    });
  });
});
