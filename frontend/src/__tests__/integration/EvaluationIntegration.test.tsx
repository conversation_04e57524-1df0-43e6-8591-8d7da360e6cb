import React from 'react';
import { render, screen, waitFor, fireEvent, act } from '@testing-library/react';
import { toast } from 'sonner';
import { evaluationService } from '@/services/evaluation';
import { useEvaluation } from '@/hooks/useEvaluation';

// Mock the dependencies
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn()
  }
}));

jest.mock('@/hooks/useEvaluation', () => ({
  useEvaluation: jest.fn()
}));

jest.mock('@/services/evaluation', () => ({
  evaluationService: {
    evaluateInterview: jest.fn(),
    getEvaluation: jest.fn(),
    evaluatePublicInterview: jest.fn()
  }
}));

// Sample evaluation data
const mockEvaluation = {
  id: 'eval-123',
  status: 'completed',
  data: {
    overallScore: 4.2,
    decision: 'Go',
    criteria: [
      {
        name: 'Technical Skills',
        score: 4.5,
        feedback: 'Strong technical knowledge demonstrated.'
      },
      {
        name: 'Communication',
        score: 4.0,
        feedback: 'Good communication skills, articulates thoughts clearly.'
      }
    ],
    questionAssessments: [
      {
        questionId: 'q1',
        question: 'Describe your experience with React.',
        answer: 'I have 3 years of experience with React, building complex applications.',
        score: 4.5,
        feedback: 'Demonstrated deep knowledge of React concepts.'
      },
      {
        questionId: 'q2',
        question: 'How do you handle state management?',
        answer: 'I use Redux for complex state and React Context for simpler cases.',
        score: 4.0,
        feedback: 'Good understanding of state management approaches.'
      }
    ],
    summary: 'The candidate demonstrates strong technical skills and good communication abilities.',
    strengths: ['Technical expertise', 'Clear communication'],
    weaknesses: ['Could provide more specific examples'],
    interviewerNotes: 'Candidate would be a good fit for the team.'
  },
  createdAt: new Date('2025-03-28T12:00:00Z'),
  updatedAt: new Date('2025-03-28T12:30:00Z'),
  interviewId: 'interview-123',
  roleId: 'role-123',
  applicationId: 'app-123'
};

// Simple mock components for testing
interface EvaluationButtonProps {
  onEvaluate: () => Promise<void>;
  isProcessing: boolean;
  hasEvaluation: boolean;
  onViewEvaluation: () => void;
}

const EvaluationButton = ({ onEvaluate, isProcessing, hasEvaluation, onViewEvaluation }: EvaluationButtonProps) => (
  <div>
    {isProcessing ? (
      <div>Processing...</div>
    ) : (
      <>
        <button onClick={onEvaluate}>Evaluate with AI</button>
        {hasEvaluation && (
          <button onClick={onViewEvaluation}>View Evaluation</button>
        )}
      </>
    )}
  </div>
);

interface EvaluationDialogProps {
  evaluation: typeof mockEvaluation | null;
  loading: boolean;
  error: Error | null;
  onClose: () => void;
}

const EvaluationDialog = ({ evaluation, loading, error, onClose }: EvaluationDialogProps) => (
  <div data-testid="evaluation-dialog">
    {loading && <div>Loading evaluation...</div>}
    {error && (
      <div>
        <div>Error loading evaluation</div>
        <div>{error.message}</div>
      </div>
    )}
    {!loading && !error && evaluation && (
      <>
        <div>Evaluation Summary</div>
        <div>{evaluation.data.overallScore}</div>
        <div>{evaluation.data.decision}</div>
        <div>Technical Skills</div>
        <div>Communication</div>
      </>
    )}
    <button onClick={onClose}>Close</button>
  </div>
);

describe('Evaluation Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should complete the full evaluation flow from request to display', async () => {
    // Mock the evaluation service
    (evaluationService.evaluateInterview as jest.Mock).mockResolvedValue('eval-123');
    (evaluationService.getEvaluation as jest.Mock).mockResolvedValue({
      ...mockEvaluation,
      status: 'completed'
    });

    // Mock the useEvaluation hook
    (useEvaluation as jest.Mock).mockReturnValue({
      evaluation: mockEvaluation,
      loading: false,
      error: null
    });

    // Initial state
    let isProcessing = false;
    let hasEvaluation = false;
    let showDialog = false;

    // Create a handler function that we can await
    const handleEvaluate = async () => {
      try {
        const evaluationId = await evaluationService.evaluateInterview('interview-123', 'role-123');
        await toast.success('Evaluation requested successfully');
        isProcessing = true;
        rerender(
          <EvaluationButton
            onEvaluate={handleEvaluate}
            isProcessing={isProcessing}
            hasEvaluation={hasEvaluation}
            onViewEvaluation={handleViewEvaluation}
          />
        );

        // Simulate polling
        const evaluation = await evaluationService.getEvaluation(evaluationId);
        if (evaluation.status === 'completed') {
          isProcessing = false;
          hasEvaluation = true;
          rerender(
            <EvaluationButton
              onEvaluate={handleEvaluate}
              isProcessing={isProcessing}
              hasEvaluation={hasEvaluation}
              onViewEvaluation={handleViewEvaluation}
            />
          );
        }
      } catch (_error) {
        toast.error('Failed to evaluate interview');
      }
    };

    const handleViewEvaluation = () => {
      showDialog = true;
      rerender(
        <>
          <EvaluationButton
            onEvaluate={handleEvaluate}
            isProcessing={isProcessing}
            hasEvaluation={hasEvaluation}
            onViewEvaluation={handleViewEvaluation}
          />
          {showDialog && (
            <EvaluationDialog
              evaluation={mockEvaluation}
              loading={false}
              error={null}
              onClose={() => {
                showDialog = false;
                rerender(
                  <EvaluationButton
                    onEvaluate={handleEvaluate}
                    isProcessing={isProcessing}
                    hasEvaluation={hasEvaluation}
                    onViewEvaluation={handleViewEvaluation}
                  />
                );
              }}
            />
          )}
        </>
      );
    };

    // Render the evaluation button
    const { rerender } = render(
      <EvaluationButton
        onEvaluate={handleEvaluate}
        isProcessing={isProcessing}
        hasEvaluation={hasEvaluation}
        onViewEvaluation={handleViewEvaluation}
      />
    );

    // Find and click the Evaluate with AI button
    const evaluateButton = screen.getByText('Evaluate with AI');

    // Use act to properly handle the async click handler
    await act(async () => {
      fireEvent.click(evaluateButton);
      // Wait for all promises to resolve
      await Promise.resolve();
    });

    // Check that evaluateInterview was called
    expect(evaluationService.evaluateInterview).toHaveBeenCalledWith(
      'interview-123',
      'role-123'
    );

    // Check that toast.success was called
    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Evaluation requested successfully');
    });

    // Fast-forward timers to simulate polling completion
    await act(async () => {
      jest.advanceTimersByTime(5000);
      await Promise.resolve();
    });

    // Check that getEvaluation was called
    expect(evaluationService.getEvaluation).toHaveBeenCalledWith('eval-123');

    // Check that the View Evaluation button is displayed
    expect(screen.getByText('View Evaluation')).toBeInTheDocument();

    // Click the View Evaluation button
    const viewButton = screen.getByText('View Evaluation');
    fireEvent.click(viewButton);

    // Check that the evaluation dialog is displayed
    expect(screen.getByTestId('evaluation-dialog')).toBeInTheDocument();

    // Check that the evaluation data is displayed
    expect(screen.getByText('Evaluation Summary')).toBeInTheDocument();
    expect(screen.getByText('4.2')).toBeInTheDocument(); // Overall score
    expect(screen.getByText('Go')).toBeInTheDocument(); // Decision
    expect(screen.getByText('Technical Skills')).toBeInTheDocument();
    expect(screen.getByText('Communication')).toBeInTheDocument();

    // Close the dialog
    const closeButton = screen.getByText('Close');
    fireEvent.click(closeButton);

    // Check that the dialog is closed
    expect(screen.queryByTestId('evaluation-dialog')).not.toBeInTheDocument();
  });

  it('should handle errors in the evaluation flow', async () => {
    // Mock the evaluation service to fail
    (evaluationService.evaluateInterview as jest.Mock).mockRejectedValue(
      new Error('Failed to evaluate interview')
    );

    // Initial state
    const isProcessing = false;
    const hasEvaluation = false;

    // Render the evaluation button
    render(
      <EvaluationButton
        onEvaluate={async () => {
          try {
            await evaluationService.evaluateInterview('interview-123', 'role-123');
            toast.success('Evaluation requested successfully');
          } catch (_error) {
            toast.error('Failed to evaluate interview');
          }
        }}
        isProcessing={isProcessing}
        hasEvaluation={hasEvaluation}
        onViewEvaluation={() => {}}
      />
    );

    // Find and click the Evaluate with AI button
    const evaluateButton = screen.getByText('Evaluate with AI');

    // Use act to properly handle the async click handler
    await act(async () => {
      fireEvent.click(evaluateButton);
      // Wait for all promises to resolve
      await Promise.resolve();
    });

    // Check that evaluateInterview was called
    expect(evaluationService.evaluateInterview).toHaveBeenCalledWith(
      'interview-123',
      'role-123'
    );

    // Check that toast.error was called
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to evaluate interview');
    });
  });

  it('should handle evaluation service errors when viewing evaluation', async () => {
    // Mock the useEvaluation hook to return an error
    (useEvaluation as jest.Mock).mockReturnValue({
      evaluation: null,
      loading: false,
      error: new Error('Failed to load evaluation data')
    });

    // Render the evaluation dialog with an error
    render(
      <EvaluationDialog
        evaluation={null}
        loading={false}
        error={new Error('Failed to load evaluation data')}
        onClose={() => {}}
      />
    );

    // Check that the error message is displayed
    expect(screen.getByText('Error loading evaluation')).toBeInTheDocument();
    expect(screen.getByText('Failed to load evaluation data')).toBeInTheDocument();
  });

  it('should handle public interview evaluations', async () => {
    // Mock the evaluation service for public evaluations
    (evaluationService.evaluatePublicInterview as jest.Mock).mockResolvedValue('eval-456');

    // Call the public evaluation service directly
    const evaluationId = await evaluationService.evaluatePublicInterview(
      'interview-123',
      'role-123',
      'app-123'
    );

    // Check that the service was called correctly
    expect(evaluationId).toBe('eval-456');
    expect(evaluationService.evaluatePublicInterview).toHaveBeenCalledWith(
      'interview-123',
      'role-123',
      'app-123'
    );
  });
});
