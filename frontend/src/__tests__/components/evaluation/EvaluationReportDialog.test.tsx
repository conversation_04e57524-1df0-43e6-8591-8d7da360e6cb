import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { renderHook, act } from '@testing-library/react-hooks';
import { EvaluationReportDialog, useEvaluationDialog } from '@/components/evaluation/EvaluationReportDialog';
import { useEvaluation } from '@/hooks/useEvaluation';
import { Evaluation } from '@/services/evaluation';

// Mock the useEvaluation hook
jest.mock('@/hooks/useEvaluation', () => ({
  useEvaluation: jest.fn()
}));

// Sample evaluation data for testing
const mockEvaluation: Evaluation = {
  id: 'eval-123',
  status: 'completed',
  data: {
    overallScore: 4.2,
    decision: 'Go',
    criteria: [
      {
        name: 'Technical Skills',
        score: 4.5,
        feedback: 'Strong technical knowledge demonstrated.'
      },
      {
        name: 'Communication',
        score: 4.0,
        feedback: 'Good communication skills, articulates thoughts clearly.'
      }
    ],
    questionAssessments: [
      {
        questionId: 'q1',
        question: 'Describe your experience with React.',
        answer: 'I have 3 years of experience with React, building complex applications.',
        score: 4.5,
        feedback: 'Demonstrated deep knowledge of React concepts.'
      },
      {
        questionId: 'q2',
        question: 'How do you handle state management?',
        answer: 'I use Redux for complex state and React Context for simpler cases.',
        score: 4.0,
        feedback: 'Good understanding of state management approaches.'
      }
    ],
    summary: 'The candidate demonstrates strong technical skills and good communication abilities.',
    strengths: ['Technical expertise', 'Clear communication'],
    weaknesses: ['Could provide more specific examples'],
    interviewerNotes: 'Candidate would be a good fit for the team.'
  },
  createdAt: new Date('2025-03-28T12:00:00Z'),
  updatedAt: '2025-03-28T12:30:00Z',
  interviewId: 'interview-123',
  roleId: 'role-123',
  applicationId: 'app-123'
};

describe('EvaluationReportDialog', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render loading state initially', () => {
    // Mock the hook to return loading state
    (useEvaluation as jest.Mock).mockReturnValue({
      evaluation: null,
      loading: true,
      error: null
    });

    render(
      <EvaluationReportDialog
        open={true}
        onOpenChange={() => {}}
        evaluationId="eval-123"
      />
    );

    // Check that loading state is displayed
    expect(screen.getByText('Loading evaluation...')).toBeInTheDocument();
  });

  it('should render error state when there is an error', () => {
    // Mock the hook to return error state
    (useEvaluation as jest.Mock).mockReturnValue({
      evaluation: null,
      loading: false,
      error: new Error('Failed to load evaluation')
    });

    render(
      <EvaluationReportDialog
        open={true}
        onOpenChange={() => {}}
        evaluationId="eval-123"
      />
    );

    // Check that error state is displayed
    expect(screen.getByText(/Error loading evaluation/)).toBeInTheDocument();
  });

  it('should render "not found" state when evaluation is null', () => {
    // Mock the hook to return null evaluation
    (useEvaluation as jest.Mock).mockReturnValue({
      evaluation: null,
      loading: false,
      error: null
    });

    render(
      <EvaluationReportDialog
        open={true}
        onOpenChange={() => {}}
        evaluationId="eval-123"
      />
    );

    // Check that not found state is displayed
    expect(screen.getByText('Evaluation not found')).toBeInTheDocument();
  });

  it('should render evaluation data correctly', () => {
    // Mock the hook to return evaluation data
    (useEvaluation as jest.Mock).mockReturnValue({
      evaluation: mockEvaluation,
      loading: false,
      error: null
    });

    render(
      <EvaluationReportDialog
        open={true}
        onOpenChange={() => {}}
        evaluationId="eval-123"
      />
    );

    // Check that evaluation data is displayed
    expect(screen.getByText('Evaluation Summary')).toBeInTheDocument();
    expect(screen.getByText('4.2')).toBeInTheDocument(); // Overall score
    expect(screen.getByText('Go')).toBeInTheDocument(); // Decision
    expect(screen.getByText('Competency Scorecard')).toBeInTheDocument();
    expect(screen.getByText('Technical Skills')).toBeInTheDocument();
    expect(screen.getByText('Communication')).toBeInTheDocument();
    expect(screen.getByText('Question-by-Question Assessment')).toBeInTheDocument();
    expect(screen.getByText(/Describe your experience with React/)).toBeInTheDocument();
    expect(screen.getByText(/How do you handle state management/)).toBeInTheDocument();
    expect(screen.getByText('Between the Lines')).toBeInTheDocument();
    expect(screen.getByText('Technical expertise')).toBeInTheDocument();
    expect(screen.getByText('Could provide more specific examples')).toBeInTheDocument();
    expect(screen.getByText('Final Decision')).toBeInTheDocument();
  });

  it('should call onOpenChange when Close button is clicked', () => {
    // Mock the hook to return evaluation data
    (useEvaluation as jest.Mock).mockReturnValue({
      evaluation: mockEvaluation,
      loading: false,
      error: null
    });

    const onOpenChangeMock = jest.fn();

    render(
      <EvaluationReportDialog
        open={true}
        onOpenChange={onOpenChangeMock}
        evaluationId="eval-123"
      />
    );

    // Find and click the close button by role instead of text
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    // Check that onOpenChange was called with false
    expect(onOpenChangeMock).toHaveBeenCalledWith(false);
  });
});

describe('useEvaluationDialog', () => {
  it('should return showEvaluation function and dialog component', () => {
    // Mock implementation of useEvaluation for this test
    (useEvaluation as jest.Mock).mockReturnValue({
      evaluation: null,
      loading: true,
      error: null
    });

    // Use React Testing Library's renderHook to test our custom hook
    const { result } = renderHook(() => useEvaluationDialog());

    // Check that the hook returns the expected properties
    expect(result.current).toHaveProperty('showEvaluation');
    expect(result.current).toHaveProperty('dialog');
    expect(typeof result.current.showEvaluation).toBe('function');
    expect(result.current.dialog).toBeNull(); // Initially null

    // Call showEvaluation
    act(() => {
      result.current.showEvaluation('eval-123');
    });

    // Check that dialog is now set
    expect(result.current.dialog).not.toBeNull();
    // Use non-null assertion since we've already checked it's not null
    expect(result.current.dialog!.props.evaluationId).toBe('eval-123');
  });
});
