import React from 'react';
import { render, screen, waitFor, fireEvent, act } from '@testing-library/react';
import { useRouter, useParams } from 'next/navigation';
import { toast } from 'sonner';
import { evaluationService } from '@/services/evaluation';
import { useEvaluationDialog } from '@/components/evaluation/EvaluationReportDialog';
import InterviewSetupPage from '@/app/roles/[id]/interview-setup/[stageIndex]/page';
import { rolesService } from '@/services/roles';
import { templatesService, TemplateStatus } from '@/services/templates';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db as _db } from '@/lib/firebase/client';

// Mock all the dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useParams: jest.fn()
}));

jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn()
  }
}));

jest.mock('@/services/evaluation', () => ({
  evaluationService: {
    evaluateInterview: jest.fn(),
    getEvaluation: jest.fn()
  }
}));

jest.mock('@/components/evaluation/EvaluationReportDialog', () => ({
  useEvaluationDialog: jest.fn()
}));

jest.mock('@/services/roles', () => ({
  rolesService: {
    getRole: jest.fn()
  }
}));

jest.mock('@/services/templates', () => ({
  templatesService: {
    getTemplates: jest.fn(),
    getTemplate: jest.fn(),
    createTemplate: jest.fn(),
    updateTemplate: jest.fn()
  },
  TemplateStatus: {
    DRAFT: 'DRAFT',
    ACTIVE: 'ACTIVE',
    PAUSED: 'PAUSED',
    ARCHIVED: 'ARCHIVED'
  }
}));

jest.mock('firebase/firestore', () => ({
  collection: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  getDocs: jest.fn()
}));

jest.mock('@/lib/firebase/client', () => ({
  db: {}
}));

// Sample test data
const mockRoleId = 'role-123';
const mockStageIndex = '0';
const mockRole = {
  id: mockRoleId,
  title: 'Software Engineer',
  interviewProcess: [
    {
      stage: 'Technical Interview',
      duration: '45 minutes',
      customInstructions: 'Focus on technical skills'
    }
  ]
};

const mockTemplate = {
  id: 'template-123',
  stageIndex: 0,
  stage: 'Technical Interview',
  duration: '45 minutes',
  customInstructions: 'Focus on technical skills',
  status: TemplateStatus.ACTIVE,
  questions: [],
  evaluationCriteria: []
};

const mockCompletedInterviews = [
  {
    id: 'interview-123',
    candidateName: 'John Doe',
    status: 'pending',
    date: new Date(),
    hasTranscript: true,
    evaluationId: null
  },
  {
    id: 'interview-456',
    candidateName: 'Jane Smith',
    status: 'passed',
    score: 4.5,
    date: new Date(),
    hasTranscript: true,
    evaluationId: 'eval-456'
  }
];

const mockShowEvaluation = jest.fn();
const mockRouter = { back: jest.fn(), push: jest.fn() };

describe('InterviewSetupPage Integration with Evaluation Dialog', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock useParams
    (useParams as jest.Mock).mockReturnValue({
      id: mockRoleId,
      stageIndex: mockStageIndex
    });

    // Mock useRouter
    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    // Mock rolesService
    (rolesService.getRole as jest.Mock).mockResolvedValue(mockRole);

    // Mock templatesService
    (templatesService.getTemplates as jest.Mock).mockResolvedValue([mockTemplate]);
    (templatesService.getTemplate as jest.Mock).mockResolvedValue(mockTemplate);

    // Mock Firebase query
    (collection as jest.Mock).mockReturnValue({});
    (query as jest.Mock).mockReturnValue({});
    (where as jest.Mock).mockReturnValue({});
    (getDocs as jest.Mock).mockResolvedValue({
      forEach: (callback: (doc: any) => void) => {
        mockCompletedInterviews.forEach((interview) => {
          callback({
            id: interview.id,
            data: () => ({
              candidateName: interview.candidateName,
              evaluationId: interview.evaluationId,
              evaluationResult: interview.status === 'passed' ? { decision: 'Go', overallScore: interview.score } : null,
              completedAt: { toDate: () => interview.date },
              transcriptId: interview.hasTranscript ? 'transcript-123' : null
            })
          });
        });
      }
    });

    // Mock useEvaluationDialog
    (useEvaluationDialog as jest.Mock).mockReturnValue({
      showEvaluation: mockShowEvaluation,
      dialog: <div data-testid="evaluation-dialog" />
    });
  });

  it('should load completed interviews and display them', async () => {
    render(<InterviewSetupPage />);

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('Technical Interview Template')).toBeInTheDocument();
    });

    // Check that completed interviews are displayed
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });
  });

  it('should show evaluation dialog when View Evaluation button is clicked', async () => {
    render(<InterviewSetupPage />);

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('Technical Interview Template')).toBeInTheDocument();
    });

    // Find and click the View Evaluation button for the second interview (which has an evaluationId)
    const viewButtons = await screen.findAllByText('View Evaluation');
    fireEvent.click(viewButtons[0]); // Assuming the first "View Evaluation" button is for Jane Smith

    // Check that showEvaluation was called with the correct evaluationId
    expect(mockShowEvaluation).toHaveBeenCalledWith('eval-456');

    // Check that the evaluation dialog is rendered
    expect(screen.getByTestId('evaluation-dialog')).toBeInTheDocument();
  });

  it('should show error toast when trying to view evaluation without evaluationId', async () => {
    // Modify the mock to make the first interview have a "View Evaluation" button but no evaluationId
    mockCompletedInterviews[0].status = 'passed';
    mockCompletedInterviews[0].score = 3.5;

    render(<InterviewSetupPage />);

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('Technical Interview Template')).toBeInTheDocument();
    });

    // Find and click the View Evaluation button for the first interview (which has no evaluationId)
    const viewButtons = await screen.findAllByText('View Evaluation');
    fireEvent.click(viewButtons[0]);

    // Check that toast.error was called
    expect(toast.error).toHaveBeenCalledWith('Evaluation not found');

    // Check that showEvaluation was not called
    expect(mockShowEvaluation).not.toHaveBeenCalled();
  });

  it('should request evaluation when Evaluate with AI button is clicked', async () => {
    // Mock evaluateInterview to return an evaluationId
    (evaluationService.evaluateInterview as jest.Mock).mockResolvedValue('new-eval-123');

    // Mock getEvaluation to simulate a completed evaluation after polling
    (evaluationService.getEvaluation as jest.Mock).mockResolvedValueOnce({
      id: 'new-eval-123',
      status: 'pending'
    }).mockResolvedValueOnce({
      id: 'new-eval-123',
      status: 'completed',
      data: {
        decision: 'Go',
        overallScore: 4.2
      }
    });

    render(<InterviewSetupPage />);

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('Technical Interview Template')).toBeInTheDocument();
    });

    // Find and click the Evaluate with AI button for the first interview
    const evaluateButtons = await screen.findAllByText('Evaluate with AI');
    fireEvent.click(evaluateButtons[0]);

    // Check that evaluateInterview was called with the correct parameters
    expect(evaluationService.evaluateInterview).toHaveBeenCalledWith(
      'interview-123',
      mockRoleId,
      undefined // applicationId is undefined in our mock
    );

    // Check that toast.success was called
    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Evaluation requested successfully');
    });

    // Fast-forward timers to simulate polling completion
    await act(async () => {
      jest.advanceTimersByTime(5000);
      await Promise.resolve();
    });

    // Check that getEvaluation was called for polling
    expect(evaluationService.getEvaluation).toHaveBeenCalledWith('new-eval-123');
  });
});
