/* Custom styling for range input */
input[type=range] {
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  border-radius: 9999px;
  outline: none;
  transition: all 0.3s ease;
}

input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  cursor: pointer;
  background: var(--thumb-color, #7c3aed);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

input[type=range]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  background: var(--thumb-color, #7c3aed);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

input[type=range]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  background: var(--thumb-hover-color, #6d28d9);
}

input[type=range]::-moz-range-thumb:hover {
  transform: scale(1.1);
  background: var(--thumb-hover-color, #6d28d9);
}

/* Dark mode styles */
.dark-mode input[type=range]::-webkit-slider-thumb {
  background: var(--thumb-color, #a78bfa);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dark-mode input[type=range]::-moz-range-thumb {
  background: var(--thumb-color, #a78bfa);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dark-mode input[type=range]::-webkit-slider-thumb:hover {
  background: var(--thumb-hover-color, #8b5cf6);
}

.dark-mode input[type=range]::-moz-range-thumb:hover {
  background: var(--thumb-hover-color, #8b5cf6);
}
