'use client';

import { useEffect, useState, useCallback } from 'react';
import { Role } from '@/types/role';
import { publicRolesService } from '@/services/roles/public-service';
import { JobBoard } from '@/components/features/jobs/JobBoard';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { ArrowLeft, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTheme } from 'next-themes';

// Skeleton loader for the job board
const JobBoardSkeleton = () => (
  <div className="animate-pulse">
    <div className="h-12 bg-slate-800/50 rounded-md mb-6"></div>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array(6).fill(0).map((_, i) => (
        <div key={i} className="bg-slate-800/50 rounded-md h-64"></div>
      ))}
    </div>
  </div>
);

export function JobsWrapper() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Determine if we're in dark mode
  const isDarkMode = mounted && (resolvedTheme === 'dark' || theme === 'dark');

  // Fetch roles with retry logic
  const fetchRoles = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await publicRolesService.getPublicRoles();
      
      if (!data || !Array.isArray(data)) {
        throw new Error('Invalid response format');
      }
      
      // Filter for only published roles
      const publishedRoles = data.filter(role => role.isPublished === true);
      
      setRoles(publishedRoles);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching roles:', err);
      
      // Increment retry count and try again if under max retries
      if (retryCount < 3) {
        setRetryCount(prev => prev + 1);
        setTimeout(() => fetchRoles(), 2000); // Retry after 2 seconds
      } else {
        setError('Failed to load jobs. Please try again later.');
        setLoading(false);
      }
    }
  }, [retryCount]);

  useEffect(() => {
    fetchRoles();
  }, [fetchRoles]);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className={cn(
          "text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r",
          isDarkMode
            ? "from-indigo-400 via-purple-400 to-pink-400"
            : "from-indigo-600 via-purple-600 to-pink-600"
        )}>
          Job Board
        </h1>
        <div className="flex items-center gap-4">
          <Link href="/">
            <Button variant="secondary" size="sm" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Home
            </Button>
          </Link>
          <Button 
            variant="secondary" 
            size="sm" 
            className="gap-2"
            onClick={() => {
              setRetryCount(0);
              fetchRoles();
            }}
            disabled={loading}
          >
            <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
            Refresh
          </Button>
        </div>
      </div>

      {loading && retryCount === 0 ? (
        <JobBoardSkeleton />
      ) : error ? (
        <div className="bg-slate-900/50 border border-slate-800 rounded-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-red-400 mb-2">Error Loading Jobs</h2>
          <p className="text-slate-400 mb-4">{error}</p>
          <Button 
            variant="default" 
            onClick={() => {
              setRetryCount(0);
              fetchRoles();
            }}
            className="font-medium"
          >
            Try Again
          </Button>
        </div>
      ) : (
        <JobBoard roles={roles} />
      )}
    </div>
  );
} 