import { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/Card';
import { 
  MapPin, 
  Briefcase,
  DollarSign,
  Calendar,
  ArrowRight
} from 'lucide-react';
import { Role } from '@/types/role';
import { cn } from '@/lib/utils/styles';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import { formatPostedDate } from '@/lib/utils';

// Dynamically import icons to reduce initial bundle size
const DynamicMapPin = dynamic(() => Promise.resolve(MapPin), { ssr: false });
const DynamicBriefcase = dynamic(() => Promise.resolve(Briefcase), { ssr: false });
const DynamicDollarSign = dynamic(() => Promise.resolve(DollarSign), { ssr: false });
const DynamicCalendar = dynamic(() => Promise.resolve(Calendar), { ssr: false });
const DynamicArrowRight = dynamic(() => Promise.resolve(ArrowRight), { ssr: false });

interface JobCardProps {
  role: Role;
  isDarkMode: boolean;
}

/**
 * JobCard component displays a job listing with key information
 * and a button to view details or apply
 * Supports both light and dark themes
 */
export function JobCard({ role, isDarkMode }: JobCardProps) {
  // Format salary range - memoized to avoid recalculation
  const formattedSalary = useMemo(() => {
    if (!role.compensation || !role.compensation.min || !role.compensation.max) {
      return null;
    }
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: role.compensation.currency || 'USD',
      maximumFractionDigits: 0
    });
    
    return `${formatter.format(Number(role.compensation.min))} - ${formatter.format(Number(role.compensation.max))}`;
  }, [role.compensation]);

  // Get first two lines of summary - memoized to avoid recalculation
  const summaryPreview = useMemo(() => {
    if (!role.summary) return '';
    const lines = role.summary.split('\n');
    const preview = lines.slice(0, 2).join('\n');
    return preview.length > 150 ? preview.substring(0, 147) + '...' : preview;
  }, [role.summary]);

  // Format date to display how long ago the job was posted - memoized to avoid recalculation
  const postedDate = useMemo(() => {
    return formatPostedDate(role.created_at);
  }, [role.created_at]);

  // Memoize the card class to avoid recalculation
  const cardClass = useMemo(() => {
    return cn(
      "relative h-full flex flex-col",
      isDarkMode 
        ? cn(
            "bg-slate-800/60 backdrop-blur-[6px] backdrop-saturate-[1.4] border-slate-700/40",
            "shadow-xl shadow-slate-950/20",
            "hover:bg-slate-800/70 hover:border-slate-700/60",
            "hover:shadow-xl hover:shadow-slate-950/30"
          )
        : cn(
            "bg-white/80 backdrop-blur-sm border-slate-200/70",
            "shadow-lg shadow-slate-200/30",
            "hover:bg-white hover:border-slate-300",
            "hover:shadow-xl hover:shadow-slate-300/40"
          ),
      "transition-all duration-300 ease-out",
      "hover:translate-y-[-2px]",
    );
  }, [isDarkMode]);

  return (
    <Card className={cardClass}>
      <CardHeader className="pb-4 flex-grow">
        <div className="space-y-2">
          <CardTitle className={cn(
            "text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r",
            isDarkMode
              ? "from-indigo-400 via-purple-400 to-pink-400"
              : "from-indigo-600 via-purple-600 to-pink-600"
          )}>
            {role.title}
          </CardTitle>
          {role.team && (
            <p className={cn(
              "text-sm font-medium",
              isDarkMode ? "text-slate-300" : "text-slate-700"
            )}>
              {role.team}
            </p>
          )}
          {role.summary && (
            <p className={cn(
              "text-sm line-clamp-3",
              isDarkMode ? "text-slate-400" : "text-slate-600"
            )}>
              {summaryPreview}
            </p>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0 space-y-4">
        {/* Job details grid */}
        <div className={cn(
          "grid grid-cols-2 gap-4 text-sm",
          isDarkMode ? "text-slate-300" : "text-slate-700"
        )}>
          {/* Location */}
          {role.location && (
            <div className="flex items-center gap-2">
              <DynamicMapPin className={cn(
                "h-4 w-4 shrink-0",
                isDarkMode ? "text-slate-400" : "text-slate-500"
              )} />
              <span className="truncate">
                {role.location.city ? `${role.location.city} · ` : ''}
                {role.location.remoteStatus || role.location.type || 'Remote'}
              </span>
            </div>
          )}
          
          {/* Job Type */}
          {role.jobType && (
            <div className="flex items-center gap-2">
              <DynamicBriefcase className={cn(
                "h-4 w-4 shrink-0",
                isDarkMode ? "text-slate-400" : "text-slate-500"
              )} />
              <span className="truncate">{role.jobType}</span>
            </div>
          )}
          
          {/* Compensation */}
          {formattedSalary && (
            <div className="flex items-center gap-2">
              <DynamicDollarSign className={cn(
                "h-4 w-4 shrink-0",
                isDarkMode ? "text-slate-400" : "text-slate-500"
              )} />
              <span className="truncate">
                {formattedSalary}
              </span>
            </div>
          )}
          
          {/* Posted date */}
          <div className="flex items-center gap-2">
            <DynamicCalendar className={cn(
              "h-4 w-4 shrink-0",
              isDarkMode ? "text-slate-400" : "text-slate-500"
            )} />
            <span className="truncate">
              {postedDate}
            </span>
          </div>
        </div>
        
        {/* Apply button */}
        <div className={cn(
          "pt-4 border-t",
          isDarkMode ? "border-slate-700/50" : "border-slate-200/70"
        )}>
          <Link href={`/jobs/${role.id}`} passHref prefetch={false}>
            <Button 
              variant="secondary" 
              className={cn(
                "w-full gap-2",
                isDarkMode
                  ? "bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-pink-500/20 hover:from-indigo-500/30 hover:via-purple-500/30 hover:to-pink-500/30"
                  : "bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-pink-500/10 hover:from-indigo-500/20 hover:via-purple-500/20 hover:to-pink-500/20"
              )}
            >
              View Details <DynamicArrowRight className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
} 