import { useState, useMemo, memo, useEffect } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui';
import { JobCard } from './JobCard';
import { Role } from '@/types/role';
import { cn } from '@/lib/utils/styles';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { useTheme } from 'next-themes';

const JOBS_PER_PAGE = 12;

interface JobBoardProps {
  roles: Role[];
}

// Memoized JobCard component to prevent unnecessary re-renders
const MemoizedJobCard = memo(JobCard);

/**
 * JobBoard component displays all open roles as job cards
 * with filtering and pagination capabilities
 * Supports both light and dark themes
 */
export function JobBoard({ roles }: JobBoardProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [locationFilter, setLocationFilter] = useState<string>('all');
  const [jobTypeFilter, setJobTypeFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Determine if we're in dark mode
  const isDarkMode = mounted && (resolvedTheme === 'dark' || theme === 'dark');

  // Sort roles by created_at date (newest first) and memoize to prevent recalculation
  const sortedRoles = useMemo(() => {
    return [...roles].sort((a, b) => {
      // Default dates if not available
      const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
      const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
      // Sort in descending order (newest first)
      return dateB - dateA;
    });
  }, [roles]);

  // Memoize filtered roles to prevent recalculation on every render
  const filteredRoles = useMemo(() => {
    return sortedRoles.filter((role) => {
      const matchesSearch = searchQuery === '' ||
        role.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (role.team?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false) ||
        (role.location?.city?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false) ||
        (role.summary?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false);

      const matchesLocation =
        locationFilter === 'all' ||
        role.location?.type === locationFilter ||
        role.location?.remoteStatus === locationFilter;

      const matchesJobType =
        jobTypeFilter === 'all' ||
        role.jobType === jobTypeFilter;

      return matchesSearch && matchesLocation && matchesJobType;
    });
  }, [sortedRoles, searchQuery, locationFilter, jobTypeFilter]);

  // Memoize pagination calculations
  const { totalPages, paginatedRoles, startIndex } = useMemo(() => {
    const totalPages = Math.ceil(filteredRoles.length / JOBS_PER_PAGE);
    const startIndex = (currentPage - 1) * JOBS_PER_PAGE;
    const paginatedRoles = filteredRoles.slice(startIndex, startIndex + JOBS_PER_PAGE);

    return { totalPages, paginatedRoles, startIndex };
  }, [filteredRoles, currentPage]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, locationFilter, jobTypeFilter]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Generate pagination controls
  const renderPaginationControls = () => {
    const pages: (number | string)[] = [];

    // Always show first page
    pages.push(1);

    // Add ellipsis and surrounding pages
    if (currentPage > 3) pages.push('...');

    // Add pages around current page
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      if (i === 1 || i === totalPages) continue;
      pages.push(i);
    }

    // Add ellipsis and last page
    if (currentPage < totalPages - 2) pages.push('...');
    if (totalPages > 1) pages.push(totalPages);

    return (
      <div className="flex items-center justify-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1}
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {pages.map((page, index) => (
          typeof page === 'number' ? (
            <Button
              key={index}
              variant="ghost"
              size="sm"
              className={cn(
                "h-8 w-8 p-0",
                currentPage === page && cn(
                  isDarkMode ? "bg-slate-800/70 text-slate-100 border-slate-700/40" : "bg-slate-200 text-slate-900"
                )
              )}
              onClick={() => handlePageChange(page)}
              disabled={page === currentPage}
            >
              {page}
            </Button>
          ) : (
            <span key={index} className={cn(
              "px-2",
              isDarkMode ? "text-slate-400" : "text-slate-500"
            )}>...</span>
          )
        ))}

        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  if (!mounted) {
    return null;
  }

  return (
    <Card className={cn(
      "backdrop-blur-sm",
      isDarkMode
        ? "bg-slate-900/50 border-slate-800"
        : "bg-white/70 border-slate-200"
    )}>
      <CardHeader className={cn(
        "border-b",
        isDarkMode ? "border-slate-800/50" : "border-slate-200/50"
      )}>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-4 w-full">
            <Input
              placeholder="Search jobs..."
              className={cn(
                "w-full md:w-64",
                isDarkMode
                  ? "bg-slate-800/50 border-slate-700 focus:bg-slate-800/70"
                  : "bg-white/80 border-slate-300 focus:bg-white"
              )}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <div className="flex gap-2 w-full md:w-auto">
              <Select
                value={locationFilter}
                onValueChange={setLocationFilter}
                options={[
                  { label: 'All Locations', value: 'all' },
                  { label: 'Remote', value: 'Remote' },
                  { label: 'Hybrid', value: 'Hybrid' },
                  { label: 'On-site', value: 'On-site' }
                ]}
                placeholder="Location"
                className={cn(
                  "w-full md:w-[150px]",
                  isDarkMode
                    ? "bg-slate-800/50 border-slate-700"
                    : "bg-white/80 border-slate-300"
                )}
              />
              <Select
                value={jobTypeFilter}
                onValueChange={setJobTypeFilter}
                options={[
                  { label: 'All Types', value: 'all' },
                  { label: 'Full-time', value: 'Full-time' },
                  { label: 'Part-time', value: 'Part-time' },
                  { label: 'Contract', value: 'Contract' },
                  { label: 'Internship', value: 'Internship' }
                ]}
                placeholder="Job Type"
                className={cn(
                  "w-full md:w-[150px]",
                  isDarkMode
                    ? "bg-slate-800/50 border-slate-700"
                    : "bg-white/80 border-slate-300"
                )}
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        {filteredRoles.length === 0 ? (
          <div className={cn(
            "text-center py-12",
            isDarkMode ? "text-slate-400" : "text-slate-600"
          )}>
            <h3 className="text-xl font-semibold mb-2">No open positions found</h3>
            {roles.length === 0 ? (
              <p>There are currently no open positions available.</p>
            ) : (
              <p>Try adjusting your search criteria</p>
            )}
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {paginatedRoles.map((role) => (
                <MemoizedJobCard key={role.id} role={role} isDarkMode={isDarkMode} />
              ))}
            </div>
            {totalPages > 1 && (
              <div className={cn(
                "mt-8 border-t pt-6",
                isDarkMode ? "border-slate-800/50" : "border-slate-200/50"
              )}>
                {renderPaginationControls()}
                <div className={cn(
                  "mt-2 text-center text-sm",
                  isDarkMode ? "text-slate-400" : "text-slate-600"
                )}>
                  Showing {startIndex + 1}-{Math.min(startIndex + JOBS_PER_PAGE, filteredRoles.length)} of {filteredRoles.length} jobs
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}