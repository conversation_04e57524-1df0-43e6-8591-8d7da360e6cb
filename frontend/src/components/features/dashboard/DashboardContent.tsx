'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/Table';
import { Button } from '@/components/ui/Button';
import { Plus, Phone, ChevronDown, ChevronRight, MapPin, Briefcase, Users2, DollarSign, Calendar, ArrowUpRight, UserRound, FileText, UserCheck, Video, Trophy, Award } from 'lucide-react';
import { rolesService } from '@/services/roles';
import { Role, CandidateProgression, ExtendedApplication } from '@/types/role';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { StatusBadge } from '@/components/ui/StatusBadge';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils/styles';
import { dashboardService } from '@/services/dashboard';

interface RoleWithProgression extends Role {
    candidate_progression?: CandidateProgression;
    applicantCount?: number;
}

// Enhanced glass effect for dashboard cards
const dashboardCardClass = "backdrop-blur-[6px] backdrop-saturate-[1.4] bg-white/80 border-slate-200/70 dark:bg-slate-800/60 dark:border-slate-700/40 shadow-sm hover:shadow-md hover:shadow-slate-200/30 dark:shadow-xl dark:shadow-slate-950/20 dark:hover:shadow-lg dark:hover:shadow-slate-950/30 transition-all duration-300";

export function DashboardContent() {
    const { user, loading: authLoading } = useAuth();
    const router = useRouter();
    const { toast } = useToast();
    const [roles, setRoles] = useState<RoleWithProgression[]>([]);
    const [applications, setApplications] = useState<ExtendedApplication[]>([]);
    const [openRolesCount, setOpenRolesCount] = useState<number>(0);
    const [activeApplicationsCount, setActiveApplicationsCount] = useState<number>(0);
    const [interviewsThisWeekCount, setInterviewsThisWeekCount] = useState<number>(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isCreatingRole, setIsCreatingRole] = useState(false);
    const [expandedRoles, setExpandedRoles] = useState<Record<string, boolean>>({});
    const [expandedApplications, setExpandedApplications] = useState<Record<string, boolean>>({});

    // Helper function to format salary range
    const formatSalary = (min: string | number, max: string | number, currency: string = 'USD') => {
        const formatter = new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
            maximumFractionDigits: 0
        });
        return `${formatter.format(Number(min))} - ${formatter.format(Number(max))}`;
    };

    // Toggle expanded state for a role
    const toggleRoleExpanded = (roleId: string) => {
        setExpandedRoles(prev => ({
            ...prev,
            [roleId]: !prev[roleId]
        }));
    };

    // Toggle expanded state for an application
    const toggleApplicationExpanded = (applicationId: string) => {
        setExpandedApplications(prev => ({
            ...prev,
            [applicationId]: !prev[applicationId]
        }));
    };

    // Navigate to role details page
    const navigateToRole = (roleId: string) => {
        router.push(`/roles/${roleId}`);
    };

    // Navigate to application details page
    const navigateToApplication = (applicationId: string) => {
        router.push(`/applications/${applicationId}`);
    };

    useEffect(() => {
        if (!authLoading && !user) {
            router.push('/signin');
            return;
        }
    }, [user, router, authLoading]);

    useEffect(() => {
        const loadData = async () => {
            if (!user) return;

            try {
                setLoading(true);
                setError(null);

                // Load all dashboard data in a single request
                const dashboardData = await dashboardService.getDashboardData();
                const { roles: allRoles, applications: allApplications } = dashboardData;

                // Process applications - sort by creation date (newest first)
                if (Array.isArray(allApplications)) {
                    const sortedApplications = allApplications.sort((a, b) => {
                        const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
                        const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
                        return dateB.getTime() - dateA.getTime();
                    });

                    // Take the first 5 applications
                    const recentApplications = sortedApplications.slice(0, 5);
                    setApplications(recentApplications);

                    // Count active applications (not rejected or hired)
                    const activeApps = allApplications.filter(app =>
                        app.status !== 'rejected' && app.status !== 'hired'
                    );
                    setActiveApplicationsCount(activeApps.length);

                    // Count interviews created this week
                    const now = new Date();
                    const startOfWeek = new Date(now);
                    startOfWeek.setHours(0, 0, 0, 0);
                    startOfWeek.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)

                    const endOfWeek = new Date(startOfWeek);
                    endOfWeek.setDate(startOfWeek.getDate() + 7); // End of week (next Sunday)

                    // Fetch interview count separately, but do it in a non-blocking way
                    // This prevents the dashboard from being blocked if the interview count endpoint fails
                    // Use a separate state variable to track if we're loading the interview count
                    let isFetchingInterviewCount = false;

                    // First check if we have a cached value in sessionStorage
                    const cacheKey = 'interview-count-' + new Date().toISOString().split('T')[0]; // Cache by day
                    const cachedData = sessionStorage.getItem(cacheKey);

                    if (cachedData) {
                        try {
                            const parsedData = JSON.parse(cachedData);
                            setInterviewsThisWeekCount(parsedData.this_week_count || 0);
                            console.log('Using cached interview count data');
                            isFetchingInterviewCount = true; // Mark as fetching so we don't do it again below

                            // Still refresh in the background after a delay
                            setTimeout(async () => {
                                try {
                                    const freshData = await dashboardService.getInterviewsCount();
                                    setInterviewsThisWeekCount(freshData.this_week_count);
                                } catch (refreshError) {
                                    console.error('Error refreshing interview count:', refreshError);
                                    // Keep using the cached value
                                }
                            }, 5000); // Refresh after 5 seconds
                        } catch (e) {
                            console.error('Error parsing cached interview count data:', e);
                            // Continue with the normal fetch below
                        }
                    }

                    // Only fetch if we don't have cached data
                    if (!isFetchingInterviewCount) {
                        setTimeout(async () => {
                            try {
                                const interviewCountData = await dashboardService.getInterviewsCount();
                                setInterviewsThisWeekCount(interviewCountData.this_week_count);
                            } catch (countError) {
                                console.error('Error fetching interview count:', countError);
                                setInterviewsThisWeekCount(0);
                            }
                        }, 100);
                    }
                } else {
                    setApplications([]);
                    setActiveApplicationsCount(0);
                    setInterviewsThisWeekCount(0);
                }

                if (Array.isArray(allRoles)) {
                    // Sort roles by creation date (newest first) and take the first 5
                    const sortedRoles = allRoles.sort((a, b) => {
                        const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
                        const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
                        return dateB.getTime() - dateA.getTime();
                    });

                    // Add applicant count to each role
                    const rolesWithApplicantCount = sortedRoles.map(role => {
                        // Count applications for this role
                        const applicantCount = allApplications.filter(app => app.roleId === role.id).length;
                        return { ...role, applicantCount };
                    });

                    const recentRoles = rolesWithApplicantCount.slice(0, 5);
                    setRoles(recentRoles);

                    // Count open roles
                    const openCount = allRoles.filter(role =>
                        role.status !== 'Accepted' &&
                        role.status !== 'Rejected'
                    ).length;
                    setOpenRolesCount(openCount);
                } else {
                    setRoles([]);
                    setOpenRolesCount(0);
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                setError('Failed to load dashboard data');
                setRoles([]);
                setOpenRolesCount(0);
            } finally {
                setLoading(false);
            }
        };

        if (user) {
            loadData();
        }
    }, [user]);

    const handleStartIntakeCall = async () => {
        if (!user) {
            toast({
                title: "Authentication Required",
                description: "Please sign in to start an intake call.",
                variant: "destructive"
            });
            return;
        }

        try {
            setIsCreatingRole(true);
            // Create a placeholder role first
            const newRole = await rolesService.createPlaceholderRole(user.uid);

            // Navigate to video call page with role ID and intake type
            router.push(`/video-call?type=intake&roleId=${newRole.id}`);
        } catch (error) {
            console.error("Error creating placeholder role:", error);
            toast({
                title: "Error",
                description: "Failed to create role for intake call. Please try again.",
                variant: "destructive"
            });
        } finally {
            setIsCreatingRole(false);
        }
    };

    // Show loading state while auth is being checked
    if (authLoading) {
        return (
            <div className="flex justify-center items-center h-screen">
                <LoadingSpinner size="lg" />
            </div>
        );
    }

    // Don't render anything if user is not authenticated
    if (!user) {
        return null;
    }

    return (
        <>
            <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">Dashboard</h2>
                <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
                    <Button
                        onClick={handleStartIntakeCall}
                        disabled={isCreatingRole}
                        className="justify-center"
                    >
                        <Phone className="mr-2 h-4 w-4" />
                        {isCreatingRole ? "Creating..." : "Start Intake Call"}
                    </Button>
                    <Button
                        variant="secondary"
                        onClick={() => router.push('/roles/new')}
                        className="justify-center"
                    >
                        <Plus className="mr-2 h-4 w-4" />
                        Add New Role
                    </Button>
                </div>
            </div>
            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                <Card className={dashboardCardClass}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                            Open Roles
                        </CardTitle>
                        <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                            <Briefcase className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        </div>
                    </CardHeader>
                    <CardContent>
                        {loading ? (
                            <LoadingSpinner />
                        ) : error ? (
                            <div className="text-sm text-red-500">Failed to load count</div>
                        ) : (
                            <>
                                <div className="text-2xl font-bold">{openRolesCount}</div>
                                <p className="text-xs text-muted-foreground">
                                    Active hiring positions
                                </p>
                            </>
                        )}
                    </CardContent>
                </Card>
                <Card className={dashboardCardClass}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                            Active Applications
                        </CardTitle>
                        <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-full">
                            <UserCheck className="h-4 w-4 text-green-600 dark:text-green-400" />
                        </div>
                    </CardHeader>
                    <CardContent>
                        {loading ? (
                            <LoadingSpinner />
                        ) : error ? (
                            <div className="text-sm text-red-500">Failed to load count</div>
                        ) : (
                            <>
                                <div className="text-2xl font-bold">{activeApplicationsCount}</div>
                                <p className="text-xs text-muted-foreground">
                                    Applications in progress
                                </p>
                            </>
                        )}
                    </CardContent>
                </Card>
                <Card className={dashboardCardClass}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                            Interviews This Week
                        </CardTitle>
                        <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                            <Video className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                        </div>
                    </CardHeader>
                    <CardContent>
                        {loading ? (
                            <LoadingSpinner />
                        ) : error ? (
                            <div className="text-sm text-red-500">Failed to load count</div>
                        ) : (
                            <>
                                <div className="text-2xl font-bold">{interviewsThisWeekCount}</div>
                                <p className="text-xs text-muted-foreground">
                                    Created this week
                                </p>
                            </>
                        )}
                    </CardContent>
                </Card>
                <Card className={dashboardCardClass}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                            Finalists
                        </CardTitle>
                        <div className="p-2 bg-amber-100 dark:bg-amber-900/30 rounded-full">
                            <Trophy className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">--</div>
                        <p className="text-xs text-muted-foreground">
                            Candidates in final stages
                        </p>
                    </CardContent>
                </Card>
            </div>

            {/* Enhanced Recent Roles section with expandable rows */}
            <div className="grid gap-4 grid-cols-1">
                <Card className={`${dashboardCardClass}`}>
                    <CardHeader>
                        <CardTitle>Recent Roles</CardTitle>
                    </CardHeader>
                    <CardContent className="overflow-auto p-0">
                        <div className="w-full overflow-x-auto rounded-b-lg">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-slate-50/50 dark:bg-slate-800/30">
                                        <TableHead>Title</TableHead>
                                        <TableHead>Team</TableHead>
                                        <TableHead className="hidden sm:table-cell">Date</TableHead>
                                        <TableHead>Applicants</TableHead>
                                        <TableHead>Status</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {loading ? (
                                        <TableRow>
                                            <TableCell colSpan={5} className="text-center">
                                                <LoadingSpinner />
                                            </TableCell>
                                        </TableRow>
                                    ) : error ? (
                                        <TableRow>
                                            <TableCell colSpan={5} className="text-center text-red-500">
                                                {error}
                                            </TableCell>
                                        </TableRow>
                                    ) : roles.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={5} className="text-center text-muted-foreground">
                                                No roles found
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        roles.map((role) => (
                                            <React.Fragment key={role.id}>
                                                {/* Main row (always visible) */}
                                                <TableRow
                                                    className={cn(
                                                        "group cursor-pointer",
                                                        expandedRoles[role.id || '']
                                                            ? "bg-purple-50/10 dark:bg-purple-900/5 border-l-4 border-l-purple-200 dark:border-l-purple-800/30"
                                                            : "hover:bg-slate-50/50 dark:hover:bg-slate-800/30 hover:shadow-sm dark:hover:shadow-slate-900/10 hover:scale-[1.01] transition-all duration-200"
                                                    )}
                                                    onClick={() => role.id && navigateToRole(role.id)}
                                                >
                                                    <TableCell className="font-medium">
                                                        <div className="flex items-center space-x-2">
                                                            <div
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    // Use a callback to avoid setState in render warning
                                                                    if (role.id) {
                                                                        // Use setTimeout to move state update out of render cycle
                                                                        setTimeout(() => {
                                                                            toggleRoleExpanded(role.id || '');
                                                                        }, 0);
                                                                    }
                                                                }}
                                                                className={cn(
                                                                    "p-1 rounded-full cursor-pointer transition-all duration-200",
                                                                    expandedRoles[role.id || '']
                                                                        ? "bg-purple-100 dark:bg-purple-900/20 hover:bg-purple-200 dark:hover:bg-purple-800/30"
                                                                        : "hover:bg-slate-200/70 dark:hover:bg-slate-700/70"
                                                                )}
                                                            >
                                                                {expandedRoles[role.id || ''] ? (
                                                                    <ChevronDown className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                                                                ) : (
                                                                    <ChevronRight className="h-4 w-4 text-slate-500 dark:text-slate-400" />
                                                                )}
                                                            </div>
                                                            <div
                                                                className="group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-200 flex items-center"
                                                                onClick={() => role.id && navigateToRole(role.id)}
                                                            >
                                                                {role.title || 'Untitled Role'}
                                                                <ArrowUpRight className="ml-1 h-3.5 w-3.5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                                                            </div>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>{role.team || 'N/A'}</TableCell>
                                                    <TableCell className="hidden sm:table-cell">
                                                        {role.created_at ? new Date(role.created_at).toLocaleDateString() : 'N/A'}
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="flex items-center space-x-1">
                                                            <UserRound className="h-4 w-4 text-slate-400" />
                                                            <span>{role.applicantCount || 0}</span>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <StatusBadge status={role.status} />
                                                    </TableCell>
                                                </TableRow>

                                                {/* Expanded content - rendered as a separate row */}
                                                {expandedRoles[role.id || ''] && (
                                                    <TableRow className="bg-purple-50/20 dark:bg-purple-900/10 border-t border-slate-200/50 dark:border-slate-700/20 border-l-4 border-l-purple-200 dark:border-l-purple-800/30 hover:bg-purple-50/20 dark:hover:bg-purple-900/10">
                                                        <TableCell colSpan={5} className="p-0">
                                                            <div className="px-4 py-3">
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            {/* Left column */}
                                                            <div className="space-y-2">
                                                                {/* Location */}
                                                                {role.location && (
                                                                    <div className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                                                                        <MapPin className="h-4 w-4 text-slate-400 flex-shrink-0" />
                                                                        <span>
                                                                            {role.location.city ? role.location.city : role.location.type}
                                                                        </span>
                                                                    </div>
                                                                )}

                                                                {/* Job Type */}
                                                                <div className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                                                                    <Briefcase className="h-4 w-4 text-slate-400 flex-shrink-0" />
                                                                    <span>{role.jobType || 'Full-time'}</span>
                                                                </div>

                                                                {/* Team */}
                                                                {role.team && (
                                                                    <div className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                                                                        <Users2 className="h-4 w-4 text-slate-400 flex-shrink-0" />
                                                                        <span>{role.team}</span>
                                                                    </div>
                                                                )}
                                                            </div>

                                                            {/* Right column */}
                                                            <div className="space-y-2">
                                                                {/* Salary Range */}
                                                                {role.compensation && Number(role.compensation.min) > 0 && Number(role.compensation.max) > 0 && (
                                                                    <div className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                                                                        <DollarSign className="h-4 w-4 text-slate-400 flex-shrink-0" />
                                                                        <span>
                                                                            {formatSalary(
                                                                                role.compensation.min,
                                                                                role.compensation.max,
                                                                                role.compensation.currency
                                                                            )}
                                                                        </span>
                                                                    </div>
                                                                )}

                                                                {/* Created Date */}
                                                                {role.created_at && (
                                                                    <div className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                                                                        <Calendar className="h-4 w-4 text-slate-400 flex-shrink-0" />
                                                                        <span>Created {new Date(role.created_at).toLocaleDateString()}</span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>

                                                        {/* Summary */}
                                                        {role.summary && (
                                                            <div className="mt-3 pt-3 border-t border-slate-200/70 dark:border-slate-700/40">
                                                                <p className="text-sm text-slate-700 dark:text-slate-300">
                                                                    {role.summary.length > 200 ? `${role.summary.substring(0, 200)}...` : role.summary}
                                                                </p>
                                                            </div>
                                                        )}

                                                        {/* View Details Button */}
                                                        <div className="mt-3 flex justify-end">
                                                            <Button
                                                                variant="secondary"
                                                                size="sm"
                                                                onClick={() => role.id && navigateToRole(role.id)}
                                                                className="text-xs"
                                                            >
                                                                View Details
                                                                <ArrowUpRight className="ml-1 h-3 w-3" />
                                                            </Button>
                                                        </div>
                                                            </div>
                                                        </TableCell>
                                                    </TableRow>
                                                )}
                                            </React.Fragment>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>

                {/* Recent Applications section with expandable rows */}
                <Card className={`${dashboardCardClass} mt-6`}>
                    <CardHeader>
                        <CardTitle>Recent Applications</CardTitle>
                    </CardHeader>
                    <CardContent className="overflow-auto p-0">
                        <div className="w-full overflow-x-auto rounded-b-lg">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-slate-50/50 dark:bg-slate-800/30">
                                        <TableHead>Candidate</TableHead>
                                        <TableHead>Role</TableHead>
                                        <TableHead className="hidden sm:table-cell">Date</TableHead>
                                        <TableHead>Resume</TableHead>
                                        <TableHead>Interview</TableHead>
                                        <TableHead>Decision</TableHead>
                                        <TableHead>Stage</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {loading ? (
                                        <TableRow>
                                            <TableCell colSpan={7} className="text-center">
                                                <LoadingSpinner />
                                            </TableCell>
                                        </TableRow>
                                    ) : error ? (
                                        <TableRow>
                                            <TableCell colSpan={7} className="text-center text-red-500">
                                                {error}
                                            </TableCell>
                                        </TableRow>
                                    ) : applications.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={7} className="text-center text-slate-500">
                                                No applications found
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        applications.map((application) => (
                                            <React.Fragment key={application.id || 'unknown'}>
                                                <TableRow
                                                    className={cn(
                                                        "group cursor-pointer",
                                                        expandedApplications[application.id || '']
                                                            ? "bg-purple-50/10 dark:bg-purple-900/5 border-l-4 border-l-purple-200 dark:border-l-purple-800/30"
                                                            : "hover:bg-slate-50/50 dark:hover:bg-slate-800/30 hover:shadow-sm dark:hover:shadow-slate-900/10 hover:scale-[1.01] transition-all duration-200"
                                                    )}
                                                    onClick={() => application.id && navigateToApplication(application.id)}
                                                >
                                                    <TableCell className="font-medium">
                                                        <div className="flex items-center space-x-2">
                                                            <div
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    // Use a callback to avoid setState in render warning
                                                                    if (application.id) {
                                                                        // Use setTimeout to move state update out of render cycle
                                                                        setTimeout(() => {
                                                                            toggleApplicationExpanded(application.id);
                                                                        }, 0);
                                                                    }
                                                                }}
                                                                className={cn(
                                                                    "p-1 rounded-full cursor-pointer transition-all duration-200",
                                                                    expandedApplications[application.id || '']
                                                                        ? "bg-purple-100 dark:bg-purple-900/20 hover:bg-purple-200 dark:hover:bg-purple-800/30"
                                                                        : "hover:bg-slate-200/70 dark:hover:bg-slate-700/70"
                                                                )}
                                                            >
                                                                {expandedApplications[application.id || ''] ? (
                                                                    <ChevronDown className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                                                                ) : (
                                                                    <ChevronRight className="h-4 w-4 text-slate-500 dark:text-slate-400" />
                                                                )}
                                                            </div>
                                                            <div
                                                                className="group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-200 flex items-center"
                                                                onClick={() => application.id && navigateToApplication(application.id)}
                                                            >
                                                                {application.fullName || 'Unknown Candidate'}
                                                                <ArrowUpRight className="ml-1 h-3.5 w-3.5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                                                            </div>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>{application.roleName || 'N/A'}</TableCell>
                                                    <TableCell className="hidden sm:table-cell">
                                                        {application.created_at ? new Date(application.created_at).toLocaleDateString() : 'N/A'}
                                                    </TableCell>

                                                    {/* Resume Score */}
                                                    <TableCell>
                                                        {application.evaluationData &&
                                                         application.evaluationData.overallScore ? (
                                                            <div className="flex items-center">
                                                                <div className="relative w-7 h-7 mr-1">
                                                                    {/* Calculate score for color determination */}
                                                                    {(() => {
                                                                        const score = parseFloat(String(application.evaluationData?.overallScore || 0));
                                                                        let textColor = "text-slate-800 dark:text-slate-200";

                                                                        // Determine color based on decision or score
                                                                        if (application.evaluationData?.decision === 'Go' || application.evaluationData?.decision === 'PASS') {
                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                        } else if (application.evaluationData?.decision === 'No Go' || application.evaluationData?.decision === 'FAIL') {
                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                        } else if (score >= 4) {
                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                        } else if (score >= 3) {
                                                                            textColor = "text-amber-800 dark:text-amber-200";
                                                                        } else if (score < 3) {
                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                        }

                                                                        return (
                                                                            <div className="flex flex-col items-center">
                                                                                <span className={`text-xs font-bold ${textColor}`}>
                                                                                    {application.evaluationData?.overallScore}
                                                                                </span>
                                                                                <span className="text-[10px] text-slate-500">/ 5</span>
                                                                            </div>
                                                                        );
                                                                    })()}
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <span className="text-xs text-slate-500">N/A</span>
                                                        )}
                                                    </TableCell>

                                                    {/* Interview Score */}
                                                    <TableCell>
                                                        {application.interviews && application.interviews.length > 0 &&
                                                         (application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore) ? (
                                                            <div className="flex items-center">
                                                                <div className="relative w-7 h-7 mr-1">
                                                                    {/* Calculate score for color determination */}
                                                                    {(() => {
                                                                        const score = parseFloat(String(application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore || 0));
                                                                        let textColor = "text-slate-800 dark:text-slate-200";

                                                                        // Determine color based on decision or score
                                                                        if (application.interviews[0].decision === 'Go' || application.interviews[0].decision === 'PASS') {
                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                        } else if (application.interviews[0].decision === 'No Go' || application.interviews[0].decision === 'FAIL') {
                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                        } else if (score >= 80) {
                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                        } else if (score >= 60) {
                                                                            textColor = "text-amber-800 dark:text-amber-200";
                                                                        } else if (score < 60) {
                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                        }

                                                                        return (
                                                                            <div className="flex flex-col items-center">
                                                                                <span className={`text-xs font-bold ${textColor}`}>
                                                                                    {application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore}
                                                                                </span>
                                                                                <span className="text-[10px] text-slate-500">/ 100</span>
                                                                            </div>
                                                                        );
                                                                    })()}
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <span className="text-xs text-slate-500">N/A</span>
                                                        )}
                                                    </TableCell>

                                                    {/* Decision */}
                                                    <TableCell>
                                                        {application.interviews && application.interviews.length > 0 && application.interviews[0].decision ? (
                                                            <div className={cn(
                                                                "inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",
                                                                application.interviews[0].decision === 'Go' || application.interviews[0].decision === 'PASS' ?
                                                                "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" :
                                                                application.interviews[0].decision === 'No Go' || application.interviews[0].decision === 'FAIL' ?
                                                                "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300" :
                                                                "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"
                                                            )}>
                                                                {application.interviews[0].decision}
                                                            </div>
                                                        ) : application.evaluationData && application.evaluationData.decision ? (
                                                            <div className={cn(
                                                                "inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",
                                                                application.evaluationData.decision === 'Go' || application.evaluationData.decision === 'PASS' ?
                                                                "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" :
                                                                application.evaluationData.decision === 'No Go' || application.evaluationData.decision === 'FAIL' ?
                                                                "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300" :
                                                                "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"
                                                            )}>
                                                                {application.evaluationData.decision}
                                                            </div>
                                                        ) : (
                                                            <span className="text-xs text-slate-500">Pending</span>
                                                        )}
                                                    </TableCell>

                                                    {/* Stage */}
                                                    <TableCell>
                                                        {application.interviews && application.interviews.length > 0 ? (
                                                            <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                                                                {application.interviews[0].stage_name || application.interviews[0].stageName ||
                                                                 (application.interviews[0].stageIndex === 0 ? 'Screening' : 'Interview')}
                                                            </div>
                                                        ) : (
                                                            <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300">
                                                                Applied
                                                            </div>
                                                        )}
                                                    </TableCell>
                                                </TableRow>

                                                {/* Expanded content - rendered as a separate row */}
                                                {expandedApplications[application.id || ''] && (
                                                    <TableRow className="bg-purple-50/20 dark:bg-purple-900/10 border-t border-slate-200/50 dark:border-slate-700/20 border-l-4 border-l-purple-200 dark:border-l-purple-800/30 hover:bg-purple-50/20 dark:hover:bg-purple-900/10">
                                                        <TableCell colSpan={7} className="p-0">
                                                            <div className="px-4 py-3">
                                                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                                    {/* Left column - Contact Info */}
                                                                    <div className="space-y-2">
                                                                        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Contact Info</h4>
                                                                        {/* Email */}
                                                                        {application.email && (
                                                                            <div className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                                                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                                                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                                                                </svg>
                                                                                <span>{application.email}</span>
                                                                            </div>
                                                                        )}

                                                                        {/* Phone */}
                                                                        {application.phoneNumber && (
                                                                            <div className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                                                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                                                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                                                                </svg>
                                                                                <span>{application.phoneNumber}</span>
                                                                            </div>
                                                                        )}

                                                                        {/* Created Date */}
                                                                        {application.created_at && (
                                                                            <div className="flex items-center gap-2 text-sm text-slate-700 dark:text-slate-300">
                                                                                <Calendar className="h-4 w-4 text-slate-400 flex-shrink-0" />
                                                                                <span>Applied {new Date(application.created_at).toLocaleDateString()}</span>
                                                                            </div>
                                                                        )}
                                                                    </div>

                                                                    {/* Middle column - Resume Evaluation */}
                                                                    <div className="space-y-2">
                                                                        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Resume Evaluation</h4>

                                                                        {application.evaluationData &&
                                                                         (application.evaluationData.overallScore !== undefined) ? (
                                                                            <div className="flex items-center gap-3 mb-2">
                                                                                <div className="relative w-10 h-10">
                                                                                    {/* Calculate score for color determination */}
                                                                                    {(() => {
                                                                                        const score = parseFloat(String(application.evaluationData.overallScore || 0));
                                                                                        let scoreColor = "bg-blue-500";
                                                                                        let borderColor = "border-slate-200 dark:border-slate-700";
                                                                                        let textColor = "text-slate-800 dark:text-slate-200";

                                                                                        // Determine color based on decision or score
                                                                                        if (application.evaluationData.decision === 'Go' || application.evaluationData.decision === 'PASS') {
                                                                                            scoreColor = "bg-green-500";
                                                                                            borderColor = "border-green-200 dark:border-green-700";
                                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                                        } else if (application.evaluationData.decision === 'No Go' || application.evaluationData.decision === 'FAIL') {
                                                                                            scoreColor = "bg-red-500";
                                                                                            borderColor = "border-red-200 dark:border-red-700";
                                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                                        } else if (score >= 80) {
                                                                                            scoreColor = "bg-green-500";
                                                                                            borderColor = "border-green-200 dark:border-green-700";
                                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                                        } else if (score >= 60) {
                                                                                            scoreColor = "bg-amber-500";
                                                                                            borderColor = "border-amber-200 dark:border-amber-700";
                                                                                            textColor = "text-amber-800 dark:text-amber-200";
                                                                                        } else if (score < 60) {
                                                                                            scoreColor = "bg-red-500";
                                                                                            borderColor = "border-red-200 dark:border-red-700";
                                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                                        }

                                                                                        return (
                                                                                            <div className={`w-10 h-10 rounded-full border-3 ${borderColor} flex items-center justify-center relative`}>
                                                                                                <div className="flex flex-col items-center">
                                                                                                    <span className={`text-sm font-bold ${textColor}`}>
                                                                                                        {application.evaluationData.overallScore}
                                                                                                    </span>
                                                                                                    <span className="text-[10px] text-slate-500">/ 5</span>
                                                                                                </div>
                                                                                                <div className="absolute inset-0 rounded-full overflow-hidden">
                                                                                                    <div
                                                                                                        className={`absolute bottom-0 left-0 right-0 ${scoreColor}`}
                                                                                                        style={{
                                                                                                            height: `${Math.min(100, Math.max(0, (score / 100) * 100))}%`,
                                                                                                            opacity: '0.3'
                                                                                                        }}
                                                                                                    ></div>
                                                                                                </div>
                                                                                            </div>
                                                                                        );
                                                                                    })()}
                                                                                </div>

                                                                                <div>
                                                                                    <div className={cn(
                                                                                        "inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mb-1",
                                                                                        application.evaluationData.decision === 'Go' || application.evaluationData.decision === 'PASS' ?
                                                                                        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" :
                                                                                        application.evaluationData.decision === 'No Go' || application.evaluationData.decision === 'FAIL' ?
                                                                                        "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300" :
                                                                                        "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"
                                                                                    )}>
                                                                                        {application.evaluationData.decision}
                                                                                    </div>
                                                                                    <p className="text-xs text-slate-500">Resume Score</p>
                                                                                </div>
                                                                            </div>
                                                                        ) : (
                                                                            <div className="text-sm text-slate-500">No resume evaluation available</div>
                                                                        )}

                                                                        {/* Resume Link */}
                                                                        {application.resumeUrl && (
                                                                            <div className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                                                                <FileText className="h-4 w-4 flex-shrink-0" />
                                                                                <a href={application.resumeUrl} target="_blank" rel="noopener noreferrer" onClick={(e) => e.stopPropagation()}>View Resume</a>
                                                                            </div>
                                                                        )}
                                                                    </div>

                                                                    {/* Right column - Interview Evaluation */}
                                                                    <div className="space-y-2">
                                                                        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Interview Evaluation</h4>

                                                                        {application.interviews && application.interviews.length > 0 &&
                                                                         (application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore) ? (
                                                                            <div className="flex items-center gap-3 mb-2">
                                                                                <div className="flex flex-col items-center justify-center">
                                                                                    {/* Calculate score for color determination */}
                                                                                    {(() => {
                                                                                        const score = parseFloat(String(application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore || 0));
                                                                                        let textColor = "text-slate-800 dark:text-slate-200";

                                                                                        // Determine color based on decision or score
                                                                                        if (application.interviews[0].decision === 'Go' || application.interviews[0].decision === 'PASS') {
                                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                                        } else if (application.interviews[0].decision === 'No Go' || application.interviews[0].decision === 'FAIL') {
                                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                                        } else if (score >= 80) {
                                                                                            textColor = "text-green-800 dark:text-green-200";
                                                                                        } else if (score >= 60) {
                                                                                            textColor = "text-amber-800 dark:text-amber-200";
                                                                                        } else if (score < 60) {
                                                                                            textColor = "text-red-800 dark:text-red-200";
                                                                                        }

                                                                                        return (
                                                                                            <div className="text-center">
                                                                                                <span className={`text-2xl font-bold ${textColor}`}>
                                                                                                    {application.interviews[0].overall_score || application.interviews[0].score || application.interviews[0].overallScore}
                                                                                                </span>
                                                                                                <div className="text-xs text-slate-500">out of 100</div>
                                                                                            </div>
                                                                                        );
                                                                                    })()}
                                                                                </div>

                                                                                {/* Removed the Interview Score text */}
                                                                            </div>
                                                                        ) : (
                                                                            <div className="text-sm text-slate-500">No interview evaluation available</div>
                                                                        )}

                                                                        {/* View Evaluation Link */}
                                                                        {application.interviews && application.interviews.length > 0 &&
                                                                         application.interviews[0] && (application.interviews[0].evaluation_id || application.interviews[0].evaluationId) && (
                                                                            <div className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                                                                <Award className="h-4 w-4 flex-shrink-0" />
                                                                                <a
                                                                                    href={`/evaluation/${application.interviews && application.interviews[0] ? (application.interviews[0].evaluation_id || application.interviews[0].evaluationId) : ''}`}
                                                                                    onClick={(e) => {
                                                                                        e.stopPropagation();
                                                                                        router.push(`/evaluation/${application.interviews && application.interviews[0] ? (application.interviews[0].evaluation_id || application.interviews[0].evaluationId) : ''}`);
                                                                                    }}
                                                                                >
                                                                                    View Interview Evaluation
                                                                                </a>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>

                                                                {/* View Details Button - Moved to the left */}
                                                                <div className="mt-3 flex justify-start">
                                                                    <Button
                                                                        variant="secondary"
                                                                        size="sm"
                                                                        onClick={() => application.id && navigateToApplication(application.id)}
                                                                        className="text-xs"
                                                                    >
                                                                        View Application Details
                                                                        <ArrowUpRight className="ml-1 h-3 w-3" />
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                        </TableCell>
                                                    </TableRow>
                                                )}
                                            </React.Fragment>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </>
    );
}