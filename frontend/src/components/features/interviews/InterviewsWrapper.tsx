'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { PageHeader } from '@/components/ui/PageHeader';
import { InterviewList } from '@/components/interviews/InterviewList';
import { interviewService, InterviewSession } from '@/services/interviews';
import { useAuth } from '@/contexts/auth-context';
import { Button } from '@/components/ui/Button';
import { RefreshCw } from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export function InterviewsWrapper() {
  const { user } = useAuth();
  const router = useRouter();
  const [interviews, setInterviews] = useState<InterviewSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadInterviews();
  }, [user]);

  const loadInterviews = async (forceRefresh = false) => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      if (forceRefresh) {
        setIsRefreshing(true);
      }

      const data = await interviewService.getInterviewSessions(forceRefresh);

      // Sort interviews by creation date (newest first)
      const sortedInterviews = [...data].sort((a, b) => {
        const dateA = a.created_at ? new Date(a.created_at) :
                     a.createdAt ? new Date(a.createdAt) : new Date(0);
        const dateB = b.created_at ? new Date(b.created_at) :
                     b.createdAt ? new Date(b.createdAt) : new Date(0);
        return dateB.getTime() - dateA.getTime();
      });

      setInterviews(sortedInterviews);
    } catch (error) {
      console.error('Error loading interviews:', error);
      setError('Failed to load interviews. Please try again.');
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    loadInterviews(true);
  };

  const handleViewInterview = (interviewId: string) => {
    router.push(`/interviews/${interviewId}`);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <PageHeader
          title="Interviews"
          description="View and manage your interview sessions"
        >
          <Button
            variant="secondary"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <LoadingSpinner className="h-4 w-4 mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </PageHeader>

        {error ? (
          <div className="bg-red-500/10 text-red-400 p-4 rounded-md border border-red-400/20">
            {error}
          </div>
        ) : (
          <InterviewList
            interviews={interviews}
            isLoading={loading}
            onViewInterview={handleViewInterview}
          />
        )}
      </div>
    </DashboardLayout>
  );
}
