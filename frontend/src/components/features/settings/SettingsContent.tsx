'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Select } from '@/components/ui/Select';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/Tabs';
import { useTheme } from 'next-themes';
import { ThemeToggle } from '@/components/ui/ThemeToggle';

interface Settings {
  theme: string;
  language: string;
  timezone: string;
  dateFormat: string;
  notifications: string;
  emailFrequency: string;
}

export function SettingsContent() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [settings, setLocalSettings] = useState<Settings>({
    theme: 'dark',
    language: 'en',
    timezone: 'America/Los_Angeles',
    dateFormat: 'MM/DD/YYYY',
    notifications: 'all',
    emailFrequency: 'realtime',
  });

  // Prevent hydration mismatch by syncing with theme system after mount
  useEffect(() => {
    setMounted(true);
    if (theme) {
      setLocalSettings(prev => ({ ...prev, theme }));
    }
  }, [theme]);

  const setSettings = (newSettings: Partial<Settings>) => {
    setLocalSettings({ ...settings, ...newSettings });
    
    // Update theme if theme setting changes
    if (newSettings.theme && newSettings.theme !== settings.theme) {
      setTheme(newSettings.theme);
    }
  }

  // Avoid rendering with wrong theme
  if (!mounted) {
    return null;
  }

  return (
    <>
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="appearance">Appearance</TabsTrigger>
        </TabsList>
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Company Name</label>
                <Input placeholder="Enter company name" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Time Zone</label>
                <Select
                  value={settings.timezone}
                  onValueChange={(value) => setSettings({ timezone: value })}
                  options={[
                    { label: 'UTC-8 (PST)', value: 'America/Los_Angeles' },
                    { label: 'UTC-5 (EST)', value: 'America/New_York' },
                    { label: 'UTC+0 (GMT)', value: 'UTC' },
                    { label: 'UTC+1 (CET)', value: 'Europe/Paris' },
                    { label: 'UTC+8 (CST)', value: 'Asia/Shanghai' }
                  ]}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Language</label>
                <Select
                  value={settings.language}
                  onValueChange={(value) => setSettings({ language: value })}
                  options={[
                    { label: 'English', value: 'en' },
                    { label: 'Spanish', value: 'es' },
                    { label: 'French', value: 'fr' }
                  ]}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Date Format</label>
                <Select
                  value={settings.dateFormat}
                  onValueChange={(value) => setSettings({ dateFormat: value })}
                  options={[
                    { label: 'MM/DD/YYYY', value: 'MM/DD/YYYY' },
                    { label: 'DD/MM/YYYY', value: 'DD/MM/YYYY' },
                    { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' }
                  ]}
                />
              </div>
              <Button>Save Changes</Button>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Email Notifications</label>
                <Select
                  value={settings.notifications}
                  onValueChange={(value) => setSettings({ notifications: value })}
                  options={[
                    { label: 'All', value: 'all' },
                    { label: 'Important Only', value: 'important' },
                    { label: 'None', value: 'none' }
                  ]}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Email Frequency</label>
                <Select
                  value={settings.emailFrequency}
                  onValueChange={(value) => setSettings({ emailFrequency: value })}
                  options={[
                    { label: 'Real-time', value: 'realtime' },
                    { label: 'Daily Digest', value: 'daily' },
                    { label: 'Weekly Summary', value: 'weekly' }
                  ]}
                />
              </div>
              <Button>Save Changes</Button>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="integrations">
          <Card>
            <CardHeader>
              <CardTitle>Integration Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Google Calendar API Key</label>
                <Input type="password" placeholder="Enter API key" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Zoom API Key</label>
                <Input type="password" placeholder="Enter API key" />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Slack Webhook URL</label>
                <Input placeholder="Enter webhook URL" />
              </div>
              <Button>Save Changes</Button>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle>Appearance Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Theme</label>
                <div className="flex items-center space-x-4 mb-2">
                  <ThemeToggle />
                  <span className="text-sm text-muted-foreground">Quick toggle between light and dark</span>
                </div>
                <Select
                  value={settings.theme}
                  onValueChange={(value) => setSettings({ theme: value })}
                  options={[
                    { label: 'Light', value: 'light' },
                    { label: 'Dark', value: 'dark' },
                    { label: 'System', value: 'system' }
                  ]}
                />
              </div>
              <Button onClick={() => setTheme(settings.theme)}>Save Changes</Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </>
  );
} 