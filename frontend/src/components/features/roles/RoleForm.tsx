import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { ControllerRenderProps } from 'react-hook-form'
import { Role, RoleStatus, INTERVIEW_TYPES, SkillLevel } from '@/types/role'
import React from 'react'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Button,
  Card,
  CardContent,
  Select,
  Textarea,
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent,
  FormDescription,
} from '@/components/ui'
import { ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'

const JOB_TYPES = ['Full-time', 'Part-time', 'Contract', 'Internship', 'Full-Time', 'Part-Time'] as const;
const REMOTE_STATUS = ['Remote', 'Hybrid', 'On-site'] as const;
const ROLE_PRIORITIES = ['Normal', 'Expedited'] as const;
const ROLE_STATUSES: RoleStatus[] = [
  'Intake',
  'Sourcing',
  'Screening',
  'Deep_Dive',
  'In_Person',
  'Offer',
  'Accepted',
  'Rejected',
  'Closed'
];
const SKILL_LEVELS = ['Beginner', 'Intermediate', 'Expert'] as const;

// Define a single schema for both create and edit
const roleSchema = z.object({
  // Only title and summary are required
  title: z.string().min(1, 'Title is required'),
  summary: z.string().min(1, 'Summary is required'),
  // All other fields are optional
  keyResponsibilities: z.string().optional().default(''),
  requiredSkills: z.record(z.string()).optional().default({}),
  preferredSkills: z.record(z.string()).optional().default({}),
  education: z.object({
    value: z.string().optional().default(''),
    isRequired: z.boolean().optional().default(false)
  }).optional().default({ value: '', isRequired: false }),
  certificates: z.array(z.string()).optional().default([]),
  team: z.string().optional().default(''),
  keyStakeholders: z.array(z.string()).optional().default([]),
  location: z.object({
    type: z.enum(REMOTE_STATUS),
    city: z.string().optional().default('')
  }).optional().default({ type: 'On-site', city: '' }),
  jobType: z.enum(JOB_TYPES).optional().default('Full-time'),
  aboutCompany: z.string().optional().default(''),
  aboutTeam: z.string().optional().default(''),
  compensation: z.object({
    min: z.union([z.string(), z.number()]).optional().default('0'),
    max: z.union([z.string(), z.number()]).optional().default('0'),
    currency: z.string().optional().default('USD'),
    equity: z.boolean().optional().default(false)
  }).optional().default({ min: '0', max: '0', currency: 'USD', equity: false }),
  benefits: z.object({
    vacationDays: z.number().optional().default(0),
    healthInsurance: z.boolean().optional().default(false),
    dentalInsurance: z.boolean().optional().default(false),
    visionInsurance: z.boolean().optional().default(false),
    lifeInsurance: z.boolean().optional().default(false),
    retirement401k: z.boolean().optional().default(false),
    stockOptions: z.boolean().optional().default(false),
    otherBenefits: z.array(z.string()).optional().default([])
  }).optional().default({
    vacationDays: 0,
    healthInsurance: false,
    dentalInsurance: false,
    visionInsurance: false,
    lifeInsurance: false,
    retirement401k: false,
    stockOptions: false,
    otherBenefits: []
  }),
  priority: z.enum(ROLE_PRIORITIES).optional().default('Normal'),
  hiringManagerId: z.string().optional().default(''),
  hiringManagerContact: z.string().email('Invalid email address').optional().default(''),
  status: z.union([
    z.literal('Intake'),
    z.literal('Sourcing'),
    z.literal('Screening'),
    z.literal('Deep_Dive'),
    z.literal('In_Person'),
    z.literal('Offer'),
    z.literal('Accepted'),
    z.literal('Rejected'),
    z.literal('Closed')
  ]).optional().default('Intake'),
  interviewProcess: z.array(z.object({
    stage: z.string(),
    duration: z.string(),
    customInstructions: z.string().optional()
  })).optional().default([])
});

// Base form data type from the schema
export type RoleFormData = z.infer<typeof roleSchema>;

// Extended type for the processed form data with keyResponsibilities as string[]
export type ProcessedRoleFormData = Omit<RoleFormData, 'keyResponsibilities'> & {
  keyResponsibilities: string[];
};

interface RoleFormProps {
  initialData?: Role
  onSubmit: (data: ProcessedRoleFormData) => void
  isEditing: boolean
  isSubmitting?: boolean
  userEmail?: string
  onTitleChange?: (title: string) => void
}

interface CollapsibleSectionProps {
  title: string
  defaultOpen?: boolean
  children: React.ReactNode
  hasError?: boolean
}

function CollapsibleSection({ title, defaultOpen = false, children, hasError = false }: CollapsibleSectionProps) {
  const [isOpen, setIsOpen] = React.useState(defaultOpen);

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen} className="space-y-2">
      <CollapsibleTrigger 
        className="flex w-full items-center justify-between px-1 py-2 font-semibold transition-all"
      >
        <div 
          className={cn(
            "flex items-center space-x-2",
            hasError 
              ? "text-red-500 hover:text-red-600" 
              : isOpen
                ? "bg-gradient-to-r from-[#6366F1] to-[#8B5CF6] bg-clip-text text-transparent"
                : "hover:bg-gradient-to-r hover:from-[#6366F1] hover:to-[#8B5CF6] hover:bg-clip-text hover:text-transparent"
          )}
        >
          <h3 className="text-lg">{title}</h3>
          {hasError && (
            <span className="text-sm text-red-500">(Required fields missing)</span>
          )}
        </div>
        <ChevronDown 
          className={cn(
            "h-5 w-5 transition-transform duration-200",
            isOpen && "transform rotate-180",
            hasError 
              ? "text-red-500" 
              : isOpen
                ? "text-[#6366F1]"
                : "text-current hover:text-[#6366F1]"
          )} 
        />
      </CollapsibleTrigger>
      <CollapsibleContent className="space-y-4 px-1">
        {children}
      </CollapsibleContent>
    </Collapsible>
  );
}

interface SkillInputProps {
  skills: Record<string, string>;
  onChange: (skills: Record<string, string>) => void;
  placeholder?: string;
}

function SkillInput({ skills, onChange, placeholder }: SkillInputProps) {
  const [newSkill, setNewSkill] = React.useState('');
  const [newLevel, setNewLevel] = React.useState<string>(SKILL_LEVELS[0]);

  const handleAddSkill = () => {
    if (newSkill.trim()) {
      onChange({
        ...skills,
        [newSkill.trim()]: newLevel,
      });
      setNewSkill('');
      setNewLevel(SKILL_LEVELS[0]);
    }
  };

  const handleRemoveSkill = (skillToRemove: string) => {
    const newSkills = { ...skills };
    delete newSkills[skillToRemove];
    onChange(newSkills);
  };

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        <Input
          value={newSkill}
          onChange={(e) => setNewSkill(e.target.value)}
          placeholder={placeholder}
          className="flex-1"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleAddSkill();
            }
          }}
        />
        <Select
          value={newLevel}
          onValueChange={setNewLevel}
          options={SKILL_LEVELS.map((level) => ({
            label: level,
            value: level,
          }))}
        />
        <Button
          type="button"
          variant="secondary"
          onClick={handleAddSkill}
        >
          Add
        </Button>
      </div>
      <div className="grid grid-cols-1 gap-2">
        {Object.entries(skills || {}).map(([skill, level]) => (
          <div
            key={skill}
            className="flex items-center justify-between p-2 rounded-md bg-slate-800"
          >
            <div className="flex items-center gap-2">
              <span className="text-slate-200">{skill}</span>
              <span className="text-sm text-slate-400">({level})</span>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => handleRemoveSkill(skill)}
              className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
            >
              Remove
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}

export function RoleForm({ initialData, onSubmit, isSubmitting, isEditing, userEmail = '', onTitleChange }: RoleFormProps) {
  // Convert Role skills (which are Record<string, SkillLevel>) to Record<string, string> for the form
  const convertSkills = (skills: Record<string, SkillLevel> | undefined): Record<string, string> => {
    if (!skills) return {};
    return Object.entries(skills).reduce((acc, [key, value]) => {
      acc[key] = value as string;
      return acc;
    }, {} as Record<string, string>);
  };

  const form = useForm<RoleFormData>({
    resolver: zodResolver(roleSchema),
    defaultValues: {
      title: initialData?.title || '',
      summary: initialData?.summary || '',
      keyResponsibilities: Array.isArray(initialData?.keyResponsibilities) 
        ? initialData.keyResponsibilities.join('\n') 
        : initialData?.keyResponsibilities || '',
      requiredSkills: convertSkills(initialData?.requiredSkills),
      preferredSkills: convertSkills(initialData?.preferredSkills),
      education: {
        value: initialData?.education?.value || '',
        isRequired: initialData?.education?.isRequired || false,
      },
      certificates: initialData?.certificates || [],
      team: initialData?.team || '',
      keyStakeholders: initialData?.keyStakeholders || [],
      location: {
        type: initialData?.location?.type || 'On-site',
        city: initialData?.location?.city || '',
      },
      jobType: initialData?.jobType || 'Full-time',
      aboutCompany: initialData?.aboutCompany || '',
      aboutTeam: initialData?.aboutTeam || '',
      compensation: {
        min: initialData?.compensation?.min || '0',
        max: initialData?.compensation?.max || '0',
        currency: initialData?.compensation?.currency || 'USD',
        equity: initialData?.compensation?.equity || false,
      },
      benefits: {
        vacationDays: initialData?.benefits?.vacationDays || 0,
        healthInsurance: initialData?.benefits?.healthInsurance || false,
        dentalInsurance: initialData?.benefits?.dentalInsurance || false,
        visionInsurance: initialData?.benefits?.visionInsurance || false,
        lifeInsurance: initialData?.benefits?.lifeInsurance || false,
        retirement401k: initialData?.benefits?.retirement401k || false,
        stockOptions: initialData?.benefits?.stockOptions || false,
        otherBenefits: initialData?.benefits?.otherBenefits || [],
      },
      priority: initialData?.priority || 'Normal',
      hiringManagerId: initialData?.hiringManagerId || '',
      hiringManagerContact: initialData?.hiringManagerContact || userEmail,
      status: initialData?.status || 'Intake',
      interviewProcess: initialData?.interviewProcess || [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'interviewProcess',
  });

  const { errors } = form.formState;

  // Helper function to check if a section has errors
  const getSectionErrors = (fields: string[]): boolean => {
    if (!fields.length) return false;
    
    return fields.some(field => {
      const fieldPath = field.split('.');
      let current: any = errors;
      for (const path of fieldPath) {
        if (!current || !current[path]) return false;
        current = current[path];
      }
      return true;
    });
  };

  const handleAddInterviewStage = () => {
    append({
      stage: 'Initial Screening',
      duration: '30 minutes',
      customInstructions: ''
    });
  };

  // Update hiring manager email when user email changes
  React.useEffect(() => {
    if (userEmail && !form.getValues('hiringManagerContact')) {
      form.setValue('hiringManagerContact', userEmail);
    }
  }, [userEmail, form]);

  // Watch for title changes
  React.useEffect(() => {
    if (!onTitleChange) {
      return;
    }
    
    const subscription = form.watch((value) => {
      if (value.title !== undefined) {
        onTitleChange(value.title || '');
      }
    });

    return () => subscription.unsubscribe();
  }, [form, onTitleChange]);

  const handleSubmit = (data: RoleFormData) => {
    try {
      // Format the data before submitting
      const formattedData: ProcessedRoleFormData = {
        ...data,
        // Convert keyResponsibilities from string to array
        keyResponsibilities: data.keyResponsibilities ? data.keyResponsibilities.split('\n').filter(item => item.trim() !== '') : [],
        // Ensure skills are properly formatted
        requiredSkills: data.requiredSkills || {},
        preferredSkills: data.preferredSkills || {},
      };
      
      onSubmit(formattedData);
    } catch (error) {
      console.error('Error formatting form data:', error);
    }
  };

  return (
    <Card className="max-w-4xl mx-auto">
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6 pt-4">
            {/* Basic Information */}
            <CollapsibleSection 
              title="Basic Information" 
              defaultOpen={true}
              hasError={getSectionErrors(['title', 'summary'])}
            >
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }: { field: ControllerRenderProps<RoleFormData, 'title'> }) => (
                    <FormItem>
                      <FormLabel>Title *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter role title" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="summary"
                  render={({ field }: { field: ControllerRenderProps<RoleFormData, 'summary'> }) => (
                    <FormItem>
                      <FormLabel>Summary *</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter role summary"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="keyResponsibilities"
                  render={({ field }: { field: ControllerRenderProps<RoleFormData, 'keyResponsibilities'> }) => (
                    <FormItem>
                      <FormLabel>Key Responsibilities</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter key responsibilities"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="jobType"
                    render={({ field }: { field: ControllerRenderProps<RoleFormData, 'jobType'> }) => (
                      <FormItem>
                        <FormLabel>Job Type</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                            options={JOB_TYPES.map((type) => ({
                              label: type,
                              value: type,
                            }))}
                            placeholder="Select job type"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }: { field: ControllerRenderProps<RoleFormData, 'priority'> }) => (
                      <FormItem>
                        <FormLabel>Priority</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                            options={ROLE_PRIORITIES.map((priority) => ({
                              label: priority,
                              value: priority,
                            }))}
                            placeholder="Select priority"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="location.city"
                    render={({ field }: { field: ControllerRenderProps<RoleFormData, 'location.city'> }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter city" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="location.type"
                    render={({ field }: { field: ControllerRenderProps<RoleFormData, 'location.type'> }) => (
                      <FormItem>
                        <FormLabel>Work Type</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                            options={REMOTE_STATUS.map((status) => ({
                              label: status,
                              value: status,
                            }))}
                            placeholder="Select work type"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="hiringManagerContact"
                    render={({ field }: { field: ControllerRenderProps<RoleFormData, 'hiringManagerContact'> }) => (
                      <FormItem>
                        <FormLabel>Hiring Manager Contact</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Enter hiring manager email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }: { field: ControllerRenderProps<RoleFormData, 'status'> }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value}
                            onValueChange={(value: RoleStatus) => field.onChange(value)}
                            options={ROLE_STATUSES.map((status) => ({
                              label: status.replace('_', ' '),
                              value: status,
                            }))}
                            placeholder="Select status"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </CollapsibleSection>

            {/* Skills and Education */}
            <CollapsibleSection 
              title="Skills and Education"
              hasError={getSectionErrors([])}
            >
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="requiredSkills"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Required Skills</FormLabel>
                      <FormControl>
                        <SkillInput
                          skills={field.value}
                          onChange={field.onChange}
                          placeholder="Enter a required skill"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="preferredSkills"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preferred Skills</FormLabel>
                      <FormControl>
                        <SkillInput
                          skills={field.value}
                          onChange={field.onChange}
                          placeholder="Enter a preferred skill"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="education.value"
                  render={({ field }: { field: ControllerRenderProps<RoleFormData, 'education.value'> }) => (
                    <FormItem>
                      <FormLabel>Education</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Bachelor's in Computer Science" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="education.isRequired"
                  render={({ field }: { field: ControllerRenderProps<RoleFormData, 'education.isRequired'> }) => (
                    <FormItem>
                      <FormLabel>Education Requirement</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ? 'required' : 'preferred'}
                          onValueChange={(value) => field.onChange(value === 'required')}
                          options={[
                            { label: 'Required', value: 'required' },
                            { label: 'Preferred', value: 'preferred' },
                          ]}
                          placeholder="Select requirement type"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="certificates"
                render={({ field }: { field: ControllerRenderProps<RoleFormData, 'certificates'> }) => (
                  <FormItem>
                    <FormLabel>Certificates</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter certificates (comma-separated)"
                        value={field.value?.join(', ') || ''}
                        onChange={(e) => {
                          const value = e.target.value.split(',').map(s => s.trim()).filter(Boolean);
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CollapsibleSection>

            {/* Team Information */}
            <CollapsibleSection 
              title="Team Information"
              hasError={getSectionErrors([])}
            >
              <FormField
                control={form.control}
                name="team"
                render={({ field }: { field: ControllerRenderProps<RoleFormData, 'team'> }) => (
                  <FormItem>
                    <FormLabel>Team</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter team name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="keyStakeholders"
                render={({ field }: { field: ControllerRenderProps<RoleFormData, 'keyStakeholders'> }) => (
                  <FormItem>
                    <FormLabel>Key Stakeholders</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter stakeholders (comma-separated)"
                        value={field.value?.join(', ') || ''}
                        onChange={(e) => {
                          const value = e.target.value.split(',').map(s => s.trim()).filter(Boolean);
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="aboutTeam"
                render={({ field }: { field: ControllerRenderProps<RoleFormData, 'aboutTeam'> }) => (
                  <FormItem>
                    <FormLabel>About Team</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the team"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CollapsibleSection>

            {/* Company Information */}
            <CollapsibleSection 
              title="Company Information"
              hasError={getSectionErrors([])}
            >
              <FormField
                control={form.control}
                name="aboutCompany"
                render={({ field }: { field: ControllerRenderProps<RoleFormData, 'aboutCompany'> }) => (
                  <FormItem>
                    <FormLabel>About Company</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the company"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CollapsibleSection>

            {/* Compensation and Benefits */}
            <CollapsibleSection 
              title="Compensation and Benefits"
              hasError={getSectionErrors([])}
            >
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="compensation.min"
                  render={({ field }: { field: ControllerRenderProps<RoleFormData, 'compensation.min'> }) => (
                    <FormItem>
                      <FormLabel>Minimum Compensation</FormLabel>
                      <FormControl>
                        <Input 
                          type="text"
                          placeholder="e.g. 80000"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => {
                            const value = e.target.value.replace(/[^0-9]/g, '');
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormDescription>Enter numbers only, without currency symbols or commas</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="compensation.max"
                  render={({ field }: { field: ControllerRenderProps<RoleFormData, 'compensation.max'> }) => (
                    <FormItem>
                      <FormLabel>Maximum Compensation</FormLabel>
                      <FormControl>
                        <Input 
                          type="text"
                          placeholder="e.g. 100000"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => {
                            const value = e.target.value.replace(/[^0-9]/g, '');
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormDescription>Enter numbers only, without currency symbols or commas</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="compensation.currency"
                  render={({ field }: { field: ControllerRenderProps<RoleFormData, 'compensation.currency'> }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. USD" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="compensation.equity"
                  render={({ field }: { field: ControllerRenderProps<RoleFormData, 'compensation.equity'> }) => (
                    <FormItem>
                      <FormLabel>Equity</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ? 'true' : 'false'}
                          onValueChange={(value) => field.onChange(value === 'true')}
                          options={[
                            { label: 'Yes', value: 'true' },
                            { label: 'No', value: 'false' },
                          ]}
                          placeholder="Select equity option"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="benefits.healthInsurance"
                  render={({ field }: { field: ControllerRenderProps<RoleFormData, 'benefits.healthInsurance'> }) => (
                    <FormItem>
                      <FormLabel>Health Insurance</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ? 'true' : 'false'}
                          onValueChange={(value) => field.onChange(value === 'true')}
                          options={[
                            { label: 'Yes', value: 'true' },
                            { label: 'No', value: 'false' },
                          ]}
                          placeholder="Select health insurance option"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="benefits.vacationDays"
                  render={({ field }: { field: ControllerRenderProps<RoleFormData, 'benefits.vacationDays'> }) => (
                    <FormItem>
                      <FormLabel>Vacation Days</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="e.g. 20"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CollapsibleSection>

            {/* Interview Process */}
            <CollapsibleSection 
              title="Interview Process" 
              defaultOpen={false}
              hasError={getSectionErrors([])}
            >
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <Card key={field.id} className="p-4">
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name={`interviewProcess.${index}.stage`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Interview Stage</FormLabel>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                                options={INTERVIEW_TYPES.map((type) => ({
                                  label: type,
                                  value: type,
                                }))}
                                placeholder="Select interview stage"
                              />
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`interviewProcess.${index}.duration`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Duration</FormLabel>
                              <Input
                                {...field}
                                placeholder="e.g., 15 minutes, 1 hour"
                              />
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name={`interviewProcess.${index}.customInstructions`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Custom Instructions (Optional)</FormLabel>
                            <Textarea
                              {...field}
                              placeholder="Add any specific instructions, notes, or description for this interview stage"
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button
                        type="button"
                        variant="destructive"
                        className="px-6 bg-transparent text-red-500 hover:text-red-600 hover:bg-red-100/10"
                        onClick={() => remove(index)}
                      >
                        Remove Stage
                      </Button>
                    </CardContent>
                  </Card>
                ))}

                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleAddInterviewStage}
                >
                  Add Interview Stage
                </Button>
              </div>
            </CollapsibleSection>

            <Button
              type="submit"
              className="w-full"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : isEditing ? 'Update Role' : 'Create Role'}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
} 