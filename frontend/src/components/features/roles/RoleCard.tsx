'use client';

import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { 
  MapPin, 
  Briefcase,
  DollarSign,
  Users2,
  ArrowUpRight} from 'lucide-react'
import { Role, RoleStatus, roleStageColors, statusToStage } from '@/types/role'
import { cn } from '@/lib/utils/styles'
import { useMemo } from 'react'

interface RoleCardProps {
  role: Role
  onView?: (id: string) => void
}

export function RoleCard({ role, onView }: RoleCardProps) {
  // Always set to false to disable the warning

  const getStatusColor = (status: RoleStatus) => {
    const stage = statusToStage[status] || 'INTAKE';
    const colors = roleStageColors[stage];
    
    // Create a lighter background version of the color
    const bgClass = colors.bg.replace('bg-', 'bg-') + '/10';
    const textClass = colors.text;
    const ringClass = `ring-1 ring-${colors.text.replace('text-', '')}/20`;
    
    return cn(bgClass, textClass, ringClass);
  };

  // Format salary range
  const formatSalary = (min: string | number, max: string | number, currency: string = 'USD') => {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      maximumFractionDigits: 0
    });
    return `${formatter.format(Number(min))} - ${formatter.format(Number(max))}`;
  };

  // Get first two lines of summary
  const getSummaryPreview = (summary?: string) => {
    if (!summary) return '';
    const lines = summary.split('\n');
    const preview = lines.slice(0, 2).join('\n');
    return preview.length > 150 ? preview.substring(0, 147) + '...' : preview;
  };

  const handleCardClick = () => {
    if (onView && role.id) {
      onView(role.id);
    }
  };

  const handleTitleClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    if (onView && role.id) {
      onView(role.id);
    }
  };

  const cardClass = useMemo(() => {
    return cn(
      "relative h-full flex flex-col",
      "bg-white/80 backdrop-blur-sm border-slate-200/70 shadow-lg shadow-slate-200/30", // Light mode styling
      "dark:bg-slate-800/60 dark:border-slate-700/40 dark:backdrop-blur-[6px] dark:backdrop-saturate-[1.4]", // Exact dark mode styling from JobCard
      "dark:shadow-xl dark:shadow-slate-950/20", // Exact shadow from JobCard
      "transition-all duration-300 ease-out",
      "hover:bg-white hover:border-slate-300 hover:shadow-xl hover:shadow-slate-300/40", // Light mode hover
      "dark:hover:bg-slate-800/70 dark:hover:border-slate-700/60 dark:hover:shadow-xl dark:hover:shadow-slate-950/30", // Exact dark mode hover from JobCard
      "hover:translate-y-[-2px]", // Same transform as JobCard
    );
  }, []);

  const titleClass = useMemo(() => {
    return cn(
      "text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r flex items-center gap-1 group cursor-pointer",
      "from-indigo-400 via-purple-400 to-pink-400", // Gradient matching JobCard's dark mode colors
      "dark:after:absolute dark:after:bottom-0 dark:after:left-0 dark:after:w-full dark:after:h-[2px]", 
      "dark:after:bg-gradient-to-r dark:after:from-indigo-400 dark:after:via-purple-400 dark:after:to-pink-400",
      "dark:after:transform dark:after:scale-x-0 dark:after:origin-bottom-right dark:after:transition-transform dark:after:duration-300",
      "dark:hover:after:scale-x-100 dark:hover:after:origin-bottom-left" // Animated underline on hover
    );
  }, []);

  return (
    <Card 
      className={cardClass}
      onClick={handleCardClick}
    >
      {/* Green dot indicator for published roles */}
      {role.isPublished && (
        <div 
          className="absolute bottom-2 right-2 w-1.5 h-1.5 rounded-full bg-green-500"
          title="Published"
        />
      )}
      <CardHeader className="pb-3 sm:pb-4">
        <div className="flex items-start justify-between">
          <div className="space-y-1 sm:space-y-2">
            <div 
              onClick={handleTitleClick}
              className={titleClass}
            >
              {role.title}
              <ArrowUpRight className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
            </div>
            {role.summary && (
              <p className="text-sm text-slate-700 dark:text-slate-400 line-clamp-2">
                {getSummaryPreview(role.summary)}
              </p>
            )}
          </div>
          <span
            className={cn(
              "inline-flex items-center rounded-md px-2 py-1 text-xs font-semibold ring-1 backdrop-blur-sm",
              getStatusColor(role.status)
            )}
          >
            {role.status.replace('_', ' ')}
          </span>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2 sm:space-y-4">
          {/* First row: Location with Work Type, Job Type, Team */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-1 sm:gap-4 text-xs sm:text-sm text-slate-700 dark:text-slate-300">
            {role.location && (
              <div className="flex items-center gap-1.5 sm:gap-2">
                <MapPin className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-slate-400 flex-shrink-0" />
                <span className="truncate">
                  {role.location.city ? role.location.city : role.location.type}
                </span>
              </div>
            )}
            <div className="flex items-center gap-1.5 sm:gap-2">
              <Briefcase className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-slate-400 flex-shrink-0" />
              <span className="truncate">{role.jobType || 'Full-time'}</span>
            </div>
            {role.team && (
              <div className="flex items-center gap-1.5 sm:gap-2">
                <Users2 className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-slate-400 flex-shrink-0" />
                <span className="truncate">{role.team}</span>
              </div>
            )}
          </div>

          {/* Salary Range */}
          {role.compensation && Number(role.compensation.min) > 0 && Number(role.compensation.max) > 0 && (
            <div className="flex items-center gap-1.5 sm:gap-2 text-xs sm:text-sm text-slate-700 dark:text-slate-300">
              <DollarSign className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-slate-400 flex-shrink-0" />
              <span className="truncate">
                {formatSalary(
                  role.compensation.min,
                  role.compensation.max,
                  role.compensation.currency
                )}
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 