'use client';

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { Plus, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight, Video, Filter } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { Select } from '@/components/ui/Select'
import { RoleCard } from './RoleCard'
import { Role } from '@/types/role'
import { cn } from '@/lib/utils/styles'
import { rolesService } from '@/services/roles'
import { useAuth } from '@/contexts/auth-context'
import { useToast } from '@/hooks/use-toast'

const ROLES_PER_PAGE = 24;

interface RoleListProps {
  roles: Role[]
  onCreateRole?: () => void
  onViewRole?: (id: string) => void
}

export function RoleList({
  roles,
  onCreateRole,
  onViewRole,
}: RoleListProps) {
  const router = useRouter()
  const { user } = useAuth();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [isCreatingRole, setIsCreatingRole] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  const filteredRoles = roles.filter((role) => {
    const matchesSearch =
      role.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (role.team?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false) ||
      (role.location?.city?.toLowerCase().includes(searchQuery.toLowerCase()) ?? false)

    const matchesStatus = statusFilter === 'all' || role.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const totalPages = Math.ceil(filteredRoles.length / ROLES_PER_PAGE)
  const startIndex = (currentPage - 1) * ROLES_PER_PAGE
  const paginatedRoles = filteredRoles.slice(startIndex, startIndex + ROLES_PER_PAGE)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handleStartIntakeCall = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to start an intake call.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsCreatingRole(true);
      // Create a placeholder role first
      const newRole = await rolesService.createPlaceholderRole(user.uid);
      
      // Navigate to video call page with role ID and intake type
      router.push(`/video-call?type=intake&roleId=${newRole.id}`);
    } catch (error) {
      console.error("Error creating placeholder role:", error);
      toast({
        title: "Error",
        description: "Failed to create role for intake call. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingRole(false);
    }
  };

  const renderPaginationControls = () => {
    const pages: (number | string)[] = []
    
    // Always show first page
    pages.push(1)
    
    // Add ellipsis and surrounding pages
    if (currentPage > 3) pages.push('...')
    
    // Add pages around current page
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      if (i === 1 || i === totalPages) continue
      pages.push(i)
    }
    
    // Add ellipsis and last page
    if (currentPage < totalPages - 2) pages.push('...')
    if (totalPages > 1) pages.push(totalPages)

    return (
      <div className="flex items-center justify-center gap-2 flex-wrap">
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1}
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        {pages.map((page, index) => (
          typeof page === 'number' ? (
            <Button
              key={index}
              variant="ghost"
              size="sm"
              className={cn(
                "h-8 w-8 p-0",
                currentPage === page && "bg-slate-100 text-slate-800 dark:bg-slate-800/70 dark:text-slate-100 dark:border-slate-700/40"
              )}
              onClick={() => handlePageChange(page)}
              disabled={page === currentPage}
            >
              {page}
            </Button>
          ) : (
            <span key={index} className="px-2 text-slate-400">...</span>
          )
        ))}

        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    )
  }

  return (
    <Card className="backdrop-blur-[6px] backdrop-saturate-[1.4] bg-white/80 border-slate-200/70 dark:bg-slate-800/60 dark:border-slate-700/40 dark:shadow-xl dark:shadow-slate-950/20">
      <CardHeader className="border-b border-slate-200/70 dark:border-slate-700/40">
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <CardTitle className="text-xl font-bold gradient-text">All Roles</CardTitle>
          
          {/* Mobile Search and Filter Toggle */}
          <div className="flex items-center gap-2 md:hidden w-full">
            <Input
              placeholder="Search roles..."
              className="flex-1 bg-white/85 border-slate-200/80 focus:bg-white/95 dark:bg-slate-800/60 dark:border-slate-700/40 dark:focus:bg-slate-800/70 backdrop-blur-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button 
              variant="secondary" 
              size="icon"
              onClick={() => setShowMobileFilters(!showMobileFilters)}
              className="bg-white/85 border-slate-200/80 dark:bg-slate-800/60 dark:border-slate-700/40"
            >
              <Filter className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Mobile Filters (conditionally shown) */}
          {showMobileFilters && (
            <div className="flex flex-col space-y-3 md:hidden">
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
                options={[
                  { label: 'All Status', value: 'all' },
                  { label: 'Intake', value: 'Intake' },
                  { label: 'Sourcing', value: 'Sourcing' },
                  { label: 'Screening', value: 'Screening' },
                  { label: 'Deep Dive', value: 'Deep_Dive' },
                  { label: 'In Person', value: 'In_Person' },
                  { label: 'Offer', value: 'Offer' },
                  { label: 'Accepted', value: 'Accepted' },
                  { label: 'Rejected', value: 'Rejected' },
                  { label: 'Closed', value: 'Closed' }
                ]}
                placeholder="Status"
                className="w-full bg-white/85 border-slate-200/80 dark:bg-slate-800/60 dark:border-slate-700/40 backdrop-blur-sm"
              />
              <div className="flex gap-2">
                <Button
                  onClick={handleStartIntakeCall}
                  disabled={isCreatingRole}
                  className="flex-1 justify-center backdrop-blur-sm shadow-md hover:shadow-lg transition-shadow"
                >
                  <Video className="mr-2 h-4 w-4" />
                  {isCreatingRole ? "Creating..." : "Intake Call"}
                </Button>
                <Button
                  variant="secondary"
                  onClick={onCreateRole}
                  className="flex-1 justify-center backdrop-blur-sm shadow-md hover:shadow-lg transition-shadow"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  New Role
                </Button>
              </div>
            </div>
          )}
          
          {/* Desktop filters */}
          <div className="hidden md:flex md:items-center md:gap-4">
            <Input
              placeholder="Search roles..."
              className="w-64 bg-white/85 border-slate-200/80 focus:bg-white/95 dark:bg-slate-800/60 dark:border-slate-700/40 dark:focus:bg-slate-800/70 backdrop-blur-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Select
              value={statusFilter}
              onValueChange={setStatusFilter}
              options={[
                { label: 'All Status', value: 'all' },
                { label: 'Intake', value: 'Intake' },
                { label: 'Sourcing', value: 'Sourcing' },
                { label: 'Screening', value: 'Screening' },
                { label: 'Deep Dive', value: 'Deep_Dive' },
                { label: 'In Person', value: 'In_Person' },
                { label: 'Offer', value: 'Offer' },
                { label: 'Accepted', value: 'Accepted' },
                { label: 'Rejected', value: 'Rejected' },
                { label: 'Closed', value: 'Closed' }
              ]}
              placeholder="Status"
              className="w-[150px] bg-white/85 border-slate-200/80 dark:bg-slate-800/60 dark:border-slate-700/40"
            />
            <Button
              onClick={handleStartIntakeCall}
              disabled={isCreatingRole}
              className="ml-2 backdrop-blur-sm shadow-md hover:shadow-lg transition-shadow"
            >
              <Video className="mr-2 h-4 w-4" />
              {isCreatingRole ? "Creating..." : "Intake Call"}
            </Button>
            <Button
              variant="secondary"
              onClick={onCreateRole}
              className="backdrop-blur-sm shadow-md hover:shadow-lg transition-shadow"
            >
              <Plus className="mr-2 h-4 w-4" />
              New Role
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        {roles.length === 0 ? (
          <div className="text-center py-12 text-slate-600 dark:text-slate-400">
            <h3 className="text-xl font-semibold mb-2">No roles found</h3>
            <p>Create a new role to get started.</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {paginatedRoles.map((role) => (
                <RoleCard
                  key={role.id}
                  role={role}
                  onView={onViewRole}
                />
              ))}
            </div>
            {totalPages > 1 && (
              <div className="mt-8 border-t border-slate-200/50 dark:border-slate-700/40 pt-6">
                {renderPaginationControls()}
                <div className="mt-2 text-center text-sm text-slate-600 dark:text-slate-400">
                  Showing {startIndex + 1}-{Math.min(startIndex + ROLES_PER_PAGE, filteredRoles.length)} of {filteredRoles.length} roles
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
} 