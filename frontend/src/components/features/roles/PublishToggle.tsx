import { useState, useEffect, useRef } from 'react';
import { Switch } from '@/components/ui/Switch';
import { Label } from '@/components/ui/Label';
import { useToast } from '@/components/ui/use-toast';
import { rolesService } from '@/services/roles';
import { templatesService } from '@/services/templates';
import { Role } from '@/types/role';
import { cn } from '@/lib/utils';
import { TemplateStatus } from '@/services/templates';

interface PublishToggleProps {
  role: Role;
  onPublishComplete: () => void;
  onStageProcessingChange?: (stageIndex: number, status: 'questions' | 'criteria' | 'activation' | null) => void;
  onPublishStepChange?: (stepName: string) => void;
}

type PublishStep = {
  id: string;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  errorMessage?: string;
  progressPercentage?: number;
};

export function PublishToggle({
  role,
  onPublishComplete,
  onStageProcessingChange,
  onPublishStepChange
}: PublishToggleProps) {
  const [isPublished, setIsPublished] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [steps, setSteps] = useState<PublishStep[]>([
    { id: 'checkTranscript', name: 'Checking transcripts', status: 'pending' },
    { id: 'enrichRole', name: 'Enriching role data', status: 'pending' },
    { id: 'generateJobPosting', name: 'Generating job posting', status: 'pending' },
    { id: 'setupInterviews', name: 'Setting up interview stages', status: 'pending' },
    { id: 'updateStatus', name: 'Updating role status', status: 'pending' },
  ]);

  const { toast } = useToast();
  const isFirstRender = useRef(true);
  const publishProcessRef = useRef<boolean>(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Initialize state from role data
  useEffect(() => {
    if (role) {
      setIsPublished(role.isPublished || false);
    }
  }, [role]);

  // Store the publish process state in sessionStorage to recover on page reload
  useEffect(() => {
    // Only read from sessionStorage on first render
    if (isFirstRender.current && role?.id) {
      const storedPublishProcess = sessionStorage.getItem(`publish_process_${role.id}`);
      if (storedPublishProcess === 'true') {
        publishProcessRef.current = true;
        setIsProcessing(true);
        setIsPublished(true);

        // Also restore steps if available
        const storedSteps = sessionStorage.getItem(`publish_steps_${role.id}`);
        if (storedSteps) {
          try {
            const parsedSteps = JSON.parse(storedSteps);
            setSteps(parsedSteps);

            // Check if we need to continue the process
            const allCompleted = parsedSteps.every((step: PublishStep) =>
              step.status === 'completed' || step.status === 'error'
            );

            if (!allCompleted) {
              // Continue the process where it left off - but do it outside the render phase
              const roleId = role.id; // Capture for the timeout
              setTimeout(() => {
                handlePublishProcess(roleId);
              }, 1000);
            } else {
              // Process is complete
              setIsProcessing(false);
              publishProcessRef.current = false;
              sessionStorage.removeItem(`publish_process_${role.id}`);
              sessionStorage.removeItem(`publish_steps_${role.id}`);
            }
          } catch (error) {
            console.error('Error parsing stored steps:', error);
          }
        }
      }
      isFirstRender.current = false;
    }

    return () => {
      // Cleanup on unmount - save current state
      if (publishProcessRef.current && role?.id) {
        sessionStorage.setItem(`publish_process_${role.id}`, 'true');
        sessionStorage.setItem(`publish_steps_${role.id}`, JSON.stringify(steps));
      }
    };
  }, [role?.id]); // Remove dependencies to avoid circular reference

  // Handle toggle change
  const handleToggleChange = async (checked: boolean) => {
    if (checked === isPublished || !role?.id) return;

    if (checked) {
      // Start publishing process IMMEDIATELY
      setIsPublished(true);
      setIsProcessing(true);
      publishProcessRef.current = true;
      abortControllerRef.current = new AbortController();

      console.log('Starting publish process - this will update multiple role properties');

      // Reset steps
      setSteps(steps.map(step => ({ ...step, status: 'pending', errorMessage: undefined, progressPercentage: undefined })));

      // Store state in sessionStorage
      sessionStorage.setItem(`publish_process_${role.id}`, 'true');

      // Trigger step change IMMEDIATELY for responsive UI
      if (onPublishStepChange) {
        onPublishStepChange('Preparing to publish role...');
      }

      // Start publishing process
      handlePublishProcess(role.id);
    } else {
      // UNPUBLISH PROCESS - ONLY updates isPublished flag, nothing else
      try {
        // If we're currently processing, abort it
        if (isProcessing && abortControllerRef.current) {
          abortControllerRef.current.abort();
          console.log('Aborted any in-progress publishing process');
        }

        console.log('Starting unpublish process - ONLY setting isPublished to false');

        // CRITICAL: First get current role data to ensure we can recover if needed
        let originalRole: Role | null = null;
        try {
          originalRole = await rolesService.getRole(role.id);
          if (originalRole) {
            console.log('✅ Saved original role data before unpublish for recovery if needed');
            // Save to session storage as backup
            sessionStorage.setItem(`original_role_data_${role.id}`, JSON.stringify(originalRole));
          }
        } catch (fetchError) {
          console.error('Failed to get original role data before unpublish:', fetchError);
          // Continue anyway - unpublish is just toggling a flag
        }

        // Create update object with ONLY isPublished field
        // This is critical - do NOT include any other fields
        const updateObj = {
          isPublished: false
          // NO OTHER FIELDS - don't add anything else here!
        };

        console.log('⚠️ CRITICAL: Unpublish update object contains ONLY:', updateObj);
        console.log('⚠️ IMPORTANT: No other fields are being updated during unpublish');

        // Make API call with the minimal single-field update
        // This call should not modify any other role data
        let updatedRole: Role;
        try {
          updatedRole = await rolesService.updateRole(role.id, updateObj);
          console.log('✅ Unpublish successful - only isPublished was updated');

          // CRITICAL: Post-update verification to ensure we didn't lose data
          if (originalRole && updatedRole) {
            const criticalFields = ['interviewProcess', 'location', 'status'];
            let dataLossDetected = false;

            for (const field of criticalFields) {
              if (field === 'interviewProcess') {
                // Check if we lost interview stages
                const originalStages = originalRole.interviewProcess?.length || 0;
                const updatedStages = updatedRole.interviewProcess?.length || 0;

                if (updatedStages < originalStages) {
                  console.error(`⚠️ DATA LOSS DETECTED! Interview stages reduced from ${originalStages} to ${updatedStages}`);
                  dataLossDetected = true;
                }
              }
              // Check if status was unexpectedly reset
              else if (field === 'status' && originalRole.status !== updatedRole.status) {
                console.error(`⚠️ DATA LOSS DETECTED! Status changed from ${originalRole.status} to ${updatedRole.status}`);
                dataLossDetected = true;
              }
              // Check for other data loss
              else if (
                originalRole[field as keyof typeof originalRole] &&
                (
                  !updatedRole[field as keyof typeof updatedRole] ||
                  (typeof updatedRole[field as keyof typeof updatedRole] === 'object' &&
                   Object.keys(updatedRole[field as keyof typeof updatedRole] as object).length === 0)
                )
              ) {
                console.error(`⚠️ DATA LOSS DETECTED in field: ${field}`);
                dataLossDetected = true;
              }
            }

            if (dataLossDetected) {
              console.error('⚠️ DATA LOSS DETECTED after unpublish! Will perform emergency recovery.');

              // Create recovery object with original data but keeping isPublished=false
              const recoveryData = {
                ...originalRole,
                isPublished: false // Keep unpublished state
              };

              // Remove fields that shouldn't be part of an update
              delete recoveryData.id;
              delete recoveryData.created_at;
              delete recoveryData.updated_at;

              try {
                console.log('⚠️ EMERGENCY RECOVERY: Restoring lost role data after unpublish');
                await rolesService.updateRole(role.id, recoveryData);
                console.log('✅ Emergency recovery successful');
              } catch (recoveryError) {
                console.error('❌ Emergency recovery failed:', recoveryError);
              }
            } else {
              console.log('✅ Data verification passed - no data loss detected after unpublish');
            }
          }
        } catch (updateError) {
          console.error('❌ Error during unpublish API call:', updateError);
          throw updateError;
        }

        // Update local state
        setIsPublished(false);
        setIsProcessing(false);
        publishProcessRef.current = false;

        // Clear sessionStorage
        if (role.id) {
          sessionStorage.removeItem(`publish_process_${role.id}`);
          sessionStorage.removeItem(`publish_steps_${role.id}`);
          // Keep the original_role_data backup for a little longer as safety
        }

        toast({
          title: 'Role unpublished',
          description: 'The role has been unpublished and is no longer visible on the job board.',
        });

        // Refresh the page to show the updated role data
        onPublishComplete();
      } catch (error) {
        console.error('Error unpublishing role:', error);
        toast({
          title: 'Error',
          description: 'Failed to unpublish role. Please try again.',
          variant: 'destructive',
        });
        // Revert toggle state
        setIsPublished(true);
      }
    }
  };

  // Main publishing process function
  const handlePublishProcess = async (roleId: string) => {
    try {
      // Check if the abort controller signal is aborted
      if (abortControllerRef.current?.signal.aborted) {
        setIsProcessing(false);
        publishProcessRef.current = false;
        return;
      }

      // Get current role data to check for existing job posting
      const currentRole = await rolesService.getRole(roleId);
      if (!currentRole) {
        throw new Error('Failed to get role data');
      }

      // CRITICAL: Save the original role data to ensure nothing is lost
      const originalRoleData = { ...currentRole };

      // Store original role data in session storage for emergency recovery
      sessionStorage.setItem(`original_role_data_${roleId}`, JSON.stringify(originalRoleData));
      console.log('⚠️ Original role data saved to session storage for recovery if needed');

      // Step 1: Check for transcripts
      await updateStepStatus('checkTranscript', 'processing');
      const transcripts = await rolesService.getIntakeTranscripts(roleId);

      // Find the transcript with the most messages
      let selectedTranscriptId: string | null = null;
      let maxMessageCount = 0;

      if (transcripts && Array.isArray(transcripts)) {
        for (const transcript of transcripts) {
          // Skip deleted transcripts
          if (transcript.deleted) continue;

          const messageCount = transcript.messages?.length || 0;
          if (messageCount > maxMessageCount && messageCount >= 10) {
            maxMessageCount = messageCount;
            selectedTranscriptId = transcript.id;
          }
        }
      }

      if (!selectedTranscriptId) {
        // No suitable transcript found
        await updateStepStatus('checkTranscript', 'error', 'No transcript with 10+ messages found');
        setIsProcessing(false);
        publishProcessRef.current = false;

        toast({
          title: 'Publishing failed',
          description: 'No transcript with at least 10 messages found. Please conduct an intake call first.',
          variant: 'destructive',
        });

        // Revert the toggle
        setIsPublished(false);
        return;
      }

      await updateStepStatus('checkTranscript', 'completed');

      // Step 2: Enrich role with transcript data (only if needed)
      if (!currentRole.jobPosting) {
        await updateStepStatus('enrichRole', 'processing');

        try {
          const enrichResult = await rolesService.enrichRole(roleId, selectedTranscriptId);
          if (!enrichResult || !enrichResult.enriched_data) {
            console.warn('No enriched data returned, but continuing with the process');
            // Don't throw, just mark as completed with a warning
            await updateStepStatus('enrichRole', 'completed', 'Limited data available');
          } else {
            await updateStepStatus('enrichRole', 'completed');
          }
        } catch (error) {
          console.error('Error enriching role:', error);
          await updateStepStatus('enrichRole', 'error', 'Failed to enrich role data');
          // Continue with the process despite the error
        }

        // Step 3: Generate job posting (only if needed)
        await updateStepStatus('generateJobPosting', 'processing');

        try {
          const jobPostingResult = await rolesService.generateJobPosting(roleId, selectedTranscriptId);
          if (!jobPostingResult || !jobPostingResult.job_posting) {
            console.warn('No job posting content returned, but continuing with the process');
            // Don't throw, just mark as completed with a warning
            await updateStepStatus('generateJobPosting', 'completed', 'Limited content available');
          } else {
            await updateStepStatus('generateJobPosting', 'completed');
          }
        } catch (error) {
          console.error('Error generating job posting:', error);
          await updateStepStatus('generateJobPosting', 'error', 'Failed to generate job posting');
          // Continue with the process despite the error
        }
      } else {
        // Skip enrichment and job posting generation if job posting already exists
        console.log('Skipping enrichment and job posting generation - job posting already exists');
        await updateStepStatus('enrichRole', 'completed', 'Skipped - role already enriched');
        await updateStepStatus('generateJobPosting', 'completed', 'Skipped - job posting already exists');
      }

      // Get the updated role data with enriched fields and job posting
      const updatedRole = await rolesService.getRole(roleId);
      if (!updatedRole) {
        throw new Error('Failed to get updated role data');
      }

      // Ensure the original interview process is preserved
      // (In case the enrichment process modified or removed stages)
      const currentInterviewProcess = updatedRole.interviewProcess || [];

      // Skip this check as it can cause issues by replacing stages
      // We'll preserve existing stages and only update their properties when needed

      // Step 4: Setup interview stages
      await updateStepStatus('setupInterviews', 'processing');

      // Get all templates for this role
      const templates = await templatesService.getTemplates(roleId);

      // Generate interview content for each stage concurrently
      if (currentInterviewProcess.length > 0) {
        // Track progress for interview stages

        // Process all stages concurrently but with controlled state updates
        const stagePromises = currentInterviewProcess.map(async (stage, index) => {
          console.log(`Processing interview stage ${index}: ${stage.stage}`);

          // Find the template for this stage
          let template = templates.find(t => t.stageIndex === index);

          // If template doesn't exist for this stage, we need to create it
          if (!template) {
            console.log(`No template found for stage ${index}. Creating a new template.`);
            try {
              // Update processing status
              if (onStageProcessingChange) {
                onStageProcessingChange(index, 'activation');
              }

              // Create a new template for this stage with the stage properties
              const newTemplate = await templatesService.createTemplate(roleId, {
                stage: stage.stage,
                duration: stage.duration || '45 minutes',
                customInstructions: stage.customInstructions,
                stageIndex: index,
                status: TemplateStatus.DRAFT
              });

              template = newTemplate;
              console.log(`Created new template for stage ${index}:`, template.id);
            } catch (error) {
              console.error(`Error creating template for stage ${index}:`, error);
              // Clear processing status on error
              if (onStageProcessingChange) {
                onStageProcessingChange(index, null);
              }
              return { index, success: false }; // Return index with failure
            }
          }

          // Wrap everything in a try/catch but handle specific operations with their own error handling
          try {
            let stageSuccess = true;

            // Generate questions if there are none or very few
            if (!template.questions || template.questions.length < 3) {
              // Update processing status
              if (onStageProcessingChange) {
                onStageProcessingChange(index, 'questions');
              }

              console.log(`Generating questions for stage ${index}`);
              try {
                await templatesService.generateQuestions(roleId, template.id);
                console.log(`Successfully generated questions for stage ${index}`);
              } catch (questionError) {
                console.error(`Error generating questions for stage ${index}:`, questionError);
                // Continue with the process despite the error - don't throw
                // But mark this stage as partially successful
                stageSuccess = false;
              }
            }

            // Generate criteria if there are none or very few
            if (!template.evaluationCriteria || template.evaluationCriteria.length < 2) {
              // Update processing status
              if (onStageProcessingChange) {
                onStageProcessingChange(index, 'criteria');
              }

              console.log(`Generating evaluation criteria for stage ${index}`);
              try {
                await templatesService.generateCriteria(roleId, template.id);
                console.log(`Successfully generated evaluation criteria for stage ${index}`);
              } catch (criteriaError) {
                console.error(`Error generating evaluation criteria for stage ${index}:`, criteriaError);
                // Continue with the process despite the error - don't throw
                // But mark this stage as partially successful
                stageSuccess = false;
              }
            }

            // Update template status to ACTIVE
            console.log(`Activating template for stage ${index}`);
            if (onStageProcessingChange) {
              onStageProcessingChange(index, 'activation');
            }

            try {
              // Updating template WITHOUT triggering role updates
              await templatesService.updateTemplate(roleId, template.id, {
                status: TemplateStatus.ACTIVE
              });
              console.log(`✅ Template status updated to ACTIVE successfully for stage ${index}`);
            } catch (templateError) {
              console.error(`❌ Error updating template status for stage ${index}:`, templateError);
              // Don't throw the error - continue with the process
              // This is a non-critical error that shouldn't block publishing
              console.log(`⚠️ Continuing despite template activation error for stage ${index}`);
              // But mark this stage as partially successful
              stageSuccess = false;
            }

            // Clear processing status
            if (onStageProcessingChange) {
              onStageProcessingChange(index, null);
            }

            // Return success with updated state, but don't modify the stage object
            return {
              index,
              success: true, // Always return true to continue the publishing process
              partialSuccess: stageSuccess, // Indicate if there were any errors
              newState: 'Live' as const
            };
          } catch (error) {
            console.error(`Error setting up interview stage ${index}:`, error);
            // Clear processing status on error
            if (onStageProcessingChange) {
              onStageProcessingChange(index, null);
            }
            // Even if there's a complete failure, return partial success to continue the process
            return {
              index,
              success: true, // Return true to continue the publishing process
              partialSuccess: false // Indicate there were errors
            };
          }
        });

        // Wait for all stage promises to resolve
        const stageResults = await Promise.all(stagePromises);

        // Process the results and update the interview process
        if (stageResults.some(result => result && result.success)) {
          console.log(`Completed processing interview stages with updated states`);

          // NO LONGER update the role interview stages - this can cause data loss
          // We will keep the templates updated but NOT modify the role.interviewProcess

          // Instead, just log that templates were updated
          const successfulStages = stageResults.filter(result => result && result.success).length;
          const partialSuccessStages = stageResults.filter(result => result && result.success && result.partialSuccess === false).length;

          console.log(`Successfully processed ${successfulStages} interview templates`);
          if (partialSuccessStages > 0) {
            console.log(`⚠️ ${partialSuccessStages} stages had partial success with some errors`);
          }

          // Only update progress UI
          await updateStepStatus('setupInterviews', 'processing', undefined, 100);
        }
      } else {
        console.warn('No interview stages found in the role');
      }

      await updateStepStatus('setupInterviews', 'completed');

      // Step 5: Update role status to sourcing and mark as published
      await updateStepStatus('updateStatus', 'processing');

      // Get the final templates to ensure we're checking the actual state
      const finalTemplates = await templatesService.getTemplates(roleId);
      const screeningTemplateActive = finalTemplates.some(t =>
        t.stageIndex === 0 && t.status === TemplateStatus.ACTIVE
      );

      // Only update to Sourcing if the screening stage is active and it's not already in a later stage
      // Create a priority order for stages to avoid demoting a role
      const stagePriority = {
        'Intake': 0,
        'Sourcing': 1,
        'Screening': 2,
        'Deep_Dive': 3,
        'In_Person': 4,
        'Offer': 5,
        'Accepted': 6,
        'Rejected': 6,
        'Closed': 7
      };

      // Get the current role to ensure we have the latest state
      const currentRoleState = await rolesService.getRole(roleId);
      if (!currentRoleState) {
        throw new Error('Failed to get current role state');
      }

      // CRITICAL: ensure we don't lose any data that was added during the process
      const finalRoleState = {
        ...originalRoleData,
        ...currentRoleState,
        // Make sure to keep the original interview process
        interviewProcess: currentRoleState.interviewProcess?.length
          ? currentRoleState.interviewProcess
          : originalRoleData.interviewProcess
      };

      // Determine if we should update the status
      const currentStatus = finalRoleState.status;
      const currentPriority = stagePriority[currentStatus as keyof typeof stagePriority] || 0;

      // Only "promote" the status, never "demote" it

      // Prepare minimal update object with only necessary fields
      // IMPORTANT: NEVER include interviewProcess in this update to prevent overwriting
      let updateObj: Partial<Role> = {
        isPublished: screeningTemplateActive
        // No other fields should be included unless absolutely necessary
      };

      // FIXED: Update to Sourcing if we're publishing and current priority is lower than Sourcing
      // This ensures we always set status to at least Sourcing when publishing, regardless of current status
      if (screeningTemplateActive && currentPriority < stagePriority['Sourcing']) {
        updateObj.status = 'Sourcing';
        console.log(`Updating role status from ${currentStatus} to Sourcing`);
      } else {
        console.log(`Keeping current role status as ${currentStatus}`);
      }

      // CRITICAL VERIFICATION: Double check that we're not accidentally including any other fields
      if (Object.keys(updateObj).some(key =>
        key !== 'isPublished' && key !== 'status')) {
        console.error('⚠️ CRITICAL ERROR: Detected extra fields in update object that could cause data loss!');
        console.error('⚠️ Removing all fields except isPublished and status');

        // Force to only these two fields as safety measure
        updateObj = {
          isPublished: updateObj.isPublished,
          ...(updateObj.status ? { status: updateObj.status } : {})
        };
      }

      // Make the update with minimal fields - NEVER include interviewProcess
      console.log('⚠️ CRITICAL: FINAL UPDATE - ONLY updating these fields:', JSON.stringify(updateObj, null, 2));
      console.log('⚠️ WARNING: No other fields should be modified, especially interviewProcess');

      try {
        await rolesService.updateRole(roleId, updateObj);
        console.log('✅ Publish status update successful');

        // CRITICAL: Verify role data after update to ensure no data loss occurred
        const postUpdateRole = await rolesService.getRole(roleId);

        // Check for potential data loss
        if (postUpdateRole && originalRoleData) {
          const verificationFields = ['interviewProcess', 'location', 'title', 'summary'];
          let dataLossDetected = false;

          for (const field of verificationFields) {
            // Special check for interview process to make sure stages weren't lost
            if (field === 'interviewProcess') {
              const originalStages = originalRoleData.interviewProcess?.length || 0;
              const updatedStages = postUpdateRole.interviewProcess?.length || 0;

              if (updatedStages < originalStages) {
                console.error(`⚠️ DATA LOSS DETECTED! Interview stages reduced from ${originalStages} to ${updatedStages}`);
                dataLossDetected = true;
              }
            }
            // Check for missing or empty fields that were previously populated
            else if (
              originalRoleData[field as keyof typeof originalRoleData] &&
              (
                !postUpdateRole[field as keyof typeof postUpdateRole] ||
                (typeof postUpdateRole[field as keyof typeof postUpdateRole] === 'object' &&
                 Object.keys(postUpdateRole[field as keyof typeof postUpdateRole] as object).length === 0)
              )
            ) {
              console.error(`⚠️ DATA LOSS DETECTED in field: ${field}`);
              dataLossDetected = true;
            }
          }

          if (dataLossDetected) {
            console.error('⚠️ DATA LOSS DETECTED after update! Will use original role data for recovery.');
            console.log('Restoring from saved data...');

            // If data loss is detected, use a more comprehensive update to restore the data
            // We'll use the original data we saved previously
            const recoveryData = {
              ...originalRoleData,
              // Keep the successfully updated fields
              isPublished: postUpdateRole.isPublished,
              status: postUpdateRole.status
            };

            // Remove id and timestamps which shouldn't be part of an update
            delete recoveryData.id;
            delete recoveryData.created_at;
            delete recoveryData.updated_at;

            console.log('⚠️ EMERGENCY RECOVERY: Restoring lost role data');
            try {
              await rolesService.updateRole(roleId, recoveryData);
              console.log('✅ Emergency data recovery successful');
            } catch (recoveryError) {
              console.error('❌ Failed to restore role data:', recoveryError);
              // Continue despite recovery error - will rely on client-side data
            }
          } else {
            console.log('✅ Data verification passed - no data loss detected');
          }
        }
      } catch (updateError) {
        console.error('❌ Error updating publish status:', updateError);
        throw updateError;
      }

      await updateStepStatus('updateStatus', 'completed');

      // Show final message before refresh
      if (onPublishStepChange) {
        onPublishStepChange('Publishing complete! Page will refresh in a moment...');
      }

      // Add a small delay before refreshing for user to see the completion message
      setTimeout(() => {
        // Complete the publishing process
        setIsProcessing(false);
        publishProcessRef.current = false;

        // Clear sessionStorage
        if (role.id) {
          sessionStorage.removeItem(`publish_process_${role.id}`);
          sessionStorage.removeItem(`publish_steps_${role.id}`);
        }

        toast({
          title: 'Role published successfully',
          description: 'The role has been published and is now visible on the job board.',
        });

        // Refresh the page to show the updated role data
        onPublishComplete();
      }, 2000);
    } catch (error) {
      console.error('Error in publish process:', error);

      // Mark any pending steps as error
      const updatedSteps = steps.map(step => {
        if (step.status === 'pending' || step.status === 'processing') {
          return {
            ...step,
            status: 'error' as const,
            errorMessage: 'Process interrupted'
          };
        }
        return step;
      });

      setSteps(updatedSteps);

      setIsProcessing(false);
      publishProcessRef.current = false;

      toast({
        title: 'Publishing failed',
        description: 'An error occurred during the publishing process. Please try again.',
        variant: 'destructive',
      });

      // Revert the toggle
      setIsPublished(false);
    }
  };

  // Helper function to update step status
  const updateStepStatus = async (
    stepId: string,
    status: 'pending' | 'processing' | 'completed' | 'error',
    errorMessage?: string,
    progressPercentage?: number
  ) => {
    return new Promise<void>(resolve => {
      // Find current step before updating

      setSteps(prevSteps => {
        const updatedSteps = prevSteps.map(step =>
          step.id === stepId
            ? { ...step, status, errorMessage, progressPercentage }
            : step
        );

        // Store updated steps in sessionStorage
        if (role?.id) {
          sessionStorage.setItem(`publish_steps_${role.id}`, JSON.stringify(updatedSteps));
        }

        return updatedSteps;
      });

      // Notify parent about step change OUTSIDE of the state update function
      // This prevents the React warning about updating parent during render
      if (status === 'processing' && onPublishStepChange) {
        const stepName = steps.find(s => s.id === stepId)?.name || '';
        setTimeout(() => {
          onPublishStepChange(stepName);
        }, 0);
      }

      // Small delay to allow UI to update
      setTimeout(resolve, 100);
    });
  };

  // Get current step for display

  // Calculate overall progress percentage


  return (
    <div className="flex items-center gap-1.5">
      <Label htmlFor="publish-toggle" className={cn(
        "text-xs font-medium cursor-pointer whitespace-nowrap",
        isPublished ? "text-green-400" : "text-slate-300"
      )}>
        {isPublished ? "Published" : "Publish"}
      </Label>
      <Switch
        id="publish-toggle"
        checked={isPublished}
        onCheckedChange={handleToggleChange}
        disabled={isProcessing}
        className="h-[18px] w-[34px]"
      />
    </div>
  );
}