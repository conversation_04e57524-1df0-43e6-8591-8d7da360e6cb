'use client';

import { useEffect, useState } from 'react';
import { Role } from '@/types/role';
import { useRouter } from 'next/navigation';
import { rolesService } from '@/services/roles';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { RoleList } from '@/components/features/roles/RoleList';
import { useToast } from '@/hooks/use-toast';

export function RolesContent() {
  const router = useRouter();
  const { toast } = useToast();
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [, setHasValidationWarnings] = useState(false);

  useEffect(() => {
    const fetchRoles = async () => {
      try {
        setLoading(true);
        const data = await rolesService.getRoles();
        
        // Transform API roles to ensure all required fields are present
        const transformedRoles: Role[] = (Array.isArray(data) ? data : []).map(role => {
          // Create a normalized role with default values for missing fields
          return {
            ...role,
            id: role.id || '',
            title: role.title || 'Untitled Role',
            status: role.status || 'Intake',
            priority: role.priority || 'Normal',
            jobType: role.jobType || 'Full-time',
            location: role.location || { 
              city: '', 
              type: 'Remote',
              remoteStatus: 'Remote'
            },
            created_at: role.created_at || new Date().toISOString(),
            updated_at: role.updated_at || new Date().toISOString(),
            keyResponsibilities: role.keyResponsibilities || [],
            requiredSkills: role.requiredSkills || {},
            preferredSkills: role.preferredSkills || {},
            keyStakeholders: role.keyStakeholders || [],
            certificates: role.certificates || [],
            interviewProcess: role.interviewProcess || []
          };
        });
        
        setRoles(transformedRoles);
        setHasValidationWarnings(false);
      } catch (err) {
        console.error('Error fetching roles:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchRoles();
  }, [toast]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-destructive">
          <h2 className="text-xl font-semibold mb-2">Error Loading Roles</h2>
          <p>{error}</p>
          <button 
            className="mt-4 px-4 py-2 bg-slate-800 hover:bg-slate-700 text-white rounded-md"
            onClick={() => window.location.reload()}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      <RoleList
        roles={roles}
        onCreateRole={() => router.push('/roles/new')}
        onViewRole={(id) => router.push(`/roles/${id}`)}
      />
    </div>
  );
} 