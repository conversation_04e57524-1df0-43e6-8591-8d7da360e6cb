import { useState, useEffect } from 'react';
import { Role } from '@/types/role';
import { cn } from '@/lib/utils';
import { ExternalLink, FileText, Sparkles } from 'lucide-react';
import Link from 'next/link';

interface PublishingProgressProps {
  role: Role;
  isProcessing: boolean;
  currentStep?: string;
  enrichStep?: boolean;
  jobPostingStep?: boolean;
}

export function PublishingProgress({ 
  role, 
  isProcessing, 
  currentStep,
  enrichStep = false,
  jobPostingStep = false
}: PublishingProgressProps) {
  const [progress, setProgress] = useState(0);
  
  // Create a pulsing animation effect along the timeline
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isProcessing) {
      // Reset progress when processing starts
      setProgress(0);
      
      // Animate progress from 0 to 100 repeatedly
      interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            return 0; // Reset and start over for continuous animation
          }
          return prev + 0.5; // Smooth increment
        });
      }, 20);
    } else {
      // Set progress to 100 when not processing
      setProgress(role.isPublished ? 100 : 0);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isProcessing, role.isPublished]);
  
  // Get the correct job URL based on environment
  const getJobUrl = (roleId: string) => {
    if (typeof window !== 'undefined') {
      const isProd = window.location.hostname !== 'localhost';
      return isProd 
        ? `https://recruiva.ai/jobs/${roleId}`
        : `http://localhost:3000/jobs/${roleId}`;
    }
    return `/jobs/${roleId}`; // fallback
  };
  
  // Determine if we should show the job board link
  const showJobLink = role.isPublished && !isProcessing;
  
  // Determine the step message to display
  const getStepMessage = () => {
    if (!isProcessing) return "";
    
    if (currentStep) {
      return currentStep;
    }
    
    return "Publishing role...";
  };
  
  // Get CSS class for gradient based on the current step
  const getGradientClass = () => {
    if (!isProcessing) return "bg-green-500";
    
    if (enrichStep) {
      return "bg-gradient-to-r from-blue-500 via-purple-500 to-blue-400";
    }
    
    if (jobPostingStep) {
      return "bg-gradient-to-r from-purple-500 via-pink-500 to-purple-400";
    }
    
    return "bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500";
  };
  
  // Show always when processing OR when published
  const shouldRender = isProcessing || role.isPublished;
  
  if (!shouldRender) {
    return null;
  }
  
  return (
    <div className="absolute bottom-3 left-0 right-0 flex flex-col items-center justify-center px-6">
      {isProcessing && (
        <div className={cn(
          "text-xs font-medium text-slate-300 mb-1.5",
          "transition-opacity duration-300 flex items-center gap-1.5"
        )}>
          {enrichStep && <Sparkles className="h-3.5 w-3.5 text-blue-400 animate-pulse" />}
          {jobPostingStep && <FileText className="h-3.5 w-3.5 text-pink-400 animate-pulse" />}
          <span className="animate-pulse">{getStepMessage()}</span>
        </div>
      )}
      
      {/* Only show progress track during processing or if still processing but refreshed */}
      {isProcessing && (
        <div className="w-full">
          <div className="relative">
            {/* Base progress track */}
            <div className="h-0.5 w-full bg-slate-800 rounded-full" />
            
            {/* Animated progress indicator */}
            <div 
              className={cn(
                "absolute top-0 left-0 h-0.5 rounded-full transition-all duration-700",
                getGradientClass()
              )}
              style={{ width: `${progress}%` }}
            />
            
            {/* Glowing dot that follows the progress */}
            <div 
              className={cn(
                "absolute top-0 h-0.5 w-4 rounded-full blur-[2px] transition-all duration-700",
                isProcessing 
                  ? enrichStep 
                    ? "bg-blue-400" 
                    : jobPostingStep 
                      ? "bg-pink-400" 
                      : "bg-purple-400" 
                  : "bg-green-400 opacity-0"
              )}
              style={{ 
                left: `calc(${progress}% - 8px)`,
                boxShadow: isProcessing 
                  ? enrichStep 
                    ? '0 0 10px 3px rgba(96, 165, 250, 0.5)' 
                    : jobPostingStep
                      ? '0 0 10px 3px rgba(244, 114, 182, 0.5)'
                      : '0 0 10px 3px rgba(168, 85, 247, 0.5)' 
                  : 'none'
              }}
            />
          </div>
        </div>
      )}
      
      {showJobLink && (
        <Link 
          href={getJobUrl(role.id!)}
          className={cn(
            "flex items-center gap-1.5 text-xs font-medium text-green-400 hover:text-green-300 transition-colors",
            isProcessing ? "mt-2.5" : "mb-1.5" // Adjust margins - when processing show margin top, otherwise margin bottom
          )}
          target="_blank"
        >
          View on Job Board
          <ExternalLink className="h-3 w-3" />
        </Link>
      )}
    </div>
  );
} 