import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui'
import { Role as APIRole, RoleStatus, roleStageColors, statusToStage } from '@/types/role'
import {
  MapPin,
  Calendar,
  Briefcase,
  DollarSign,
  Star,
  Edit,
  Pause,
  X,
  Users2
} from 'lucide-react'
import { cn } from '@/lib/utils/styles'

interface RoleDetailsProps {
  role: APIRole
  onEdit?: (id: string) => void
  onPause?: (id: string) => void
  onClose?: (id: string) => void
}

// Helper function to normalize job type display
const normalizeJobType = (jobType: string): string => {
  const jobTypeLower = jobType.toLowerCase();
  if (jobTypeLower === 'full-time' || jobTypeLower === 'full time') {
    return 'Full-time';
  } else if (jobTypeLower === 'part-time' || jobTypeLower === 'part time') {
    return 'Part-time';
  } else if (jobTypeLower === 'contract') {
    return 'Contract';
  } else if (jobTypeLower === 'internship') {
    return 'Internship';
  }
  return jobType; // Return original if no match
};

// Helper function to normalize priority display

// Helper function to normalize remote status display

export function RoleDetails({ role, onEdit, onPause, onClose }: RoleDetailsProps) {
  const getStatusColor = (status: RoleStatus) => {
    const stage = statusToStage[status];
    const colors = roleStageColors[stage];
    
    // Create a lighter background version of the color
    const bgClass = colors.bg.replace('bg-', 'bg-') + '/10';
    const textClass = colors.text;
    const ringClass = `ring-1 ring-${colors.text.replace('text-', '')}/20`;
    
    return cn(bgClass, textClass, ringClass);
  };

  const formatSalary = (amount: number | string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: role.compensation?.currency || 'USD',
      maximumFractionDigits: 0,
    }).format(Number(amount))
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-2xl font-bold">{role.title}</CardTitle>
              <div className="mt-2 grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <Users2 className="mr-2 h-4 w-4" />
                  {role.team}
                </div>
                <div className="flex items-center">
                  <MapPin className="mr-2 h-4 w-4" />
                  {role.location?.city && `${role.location.city} · `}{role.location?.type}
                </div>
                <div className="flex items-center">
                  <Briefcase className="mr-2 h-4 w-4" />
                  <Badge variant="outline">{normalizeJobType(role.jobType)}</Badge>
                </div>
                <div className="flex items-center">
                  <Star className="mr-2 h-4 w-4" />
                  {role.yearsOfExperience} years experience
                </div>
                {role.compensation && (
                  <div className="flex items-center">
                    <DollarSign className="mr-2 h-4 w-4" />
                    {formatSalary(role.compensation.min)} - {formatSalary(role.compensation.max)}
                  </div>
                )}
                <div className="flex items-center">
                  <Calendar className="mr-2 h-4 w-4" />
                  Created {role.created_at ? new Date(role.created_at).toLocaleDateString() : 'N/A'}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge
                className={cn(getStatusColor(role.status))}
              >
                {role.status.replace('_', ' ')}
              </Badge>
              <div className="flex space-x-2">
                {onEdit && (
                  <Button variant="ghost" size="sm" onClick={() => onEdit(role.id || '')}>
                    <Edit className="h-4 w-4" />
                  </Button>
                )}
                {onPause && role.status !== 'Accepted' && role.status !== 'Rejected' && (
                  <Button variant="ghost" size="sm" onClick={() => onPause(role.id || '')}>
                    <Pause className="h-4 w-4" />
                  </Button>
                )}
                {onClose && role.status !== 'Accepted' && role.status !== 'Rejected' && (
                  <Button variant="ghost" size="sm" onClick={() => onClose(role.id || '')}>
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Required Skills</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(role.requiredSkills || {}).map(([skill, level]) => (
                <div key={skill} className="space-y-1">
                  <div className="text-sm text-muted-foreground">{skill}</div>
                  <div className="text-base font-medium">{level}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Preferred Skills</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(role.preferredSkills || {}).map(([skill, level]) => (
                <div key={skill} className="space-y-1">
                  <div className="text-sm text-muted-foreground">{skill}</div>
                  <div className="text-base font-medium">{level}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground whitespace-pre-wrap">{role.summary}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Key Responsibilities</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
              {Array.isArray(role.keyResponsibilities) ? 
                role.keyResponsibilities.map((resp, index) => (
                  <li key={index}>{resp}</li>
                )) : 
                <li>{role.keyResponsibilities}</li>
              }
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>About Team</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground whitespace-pre-wrap">{role.aboutTeam}</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 