import { RoleStage, roleStageColors, RoleDecision } from '@/types/role';
import { cn } from '@/lib/utils/styles';

interface RoleStageTimelineProps {
    currentStage: RoleStage;
    decision?: RoleDecision;
}

type ColorKey = 'sky-500' | 'violet-500' | 'indigo-500' | 'blue-500' | 'cyan-500' |
    'emerald-500' | 'amber-500' | 'green-500' | 'red-500' | 'slate-500';

export function RoleStageTimeline({ currentStage, decision }: RoleStageTimelineProps) {
    const stages = Object.values(RoleStage);
    const currentIndex = stages.indexOf(currentStage);

    // Helper function to get stage color
    const getStageColor = (stage: RoleStage, _index: number) => {
        const stageIndex = stages.indexOf(stage);
        const currentStageIndex = stages.indexOf(currentStage);
        const isCurrentOrBefore = stageIndex <= currentStageIndex;
        const isDecisionStage = stage === RoleStage.DECISION;

        if (!isCurrentOrBefore) {
            return { bg: 'bg-slate-800', text: 'text-slate-500' };
        }

        if (isDecisionStage && decision) {
            return roleStageColors[decision];
        }

        return roleStageColors[stage];
    };

    // Helper function to get the background gradient for line segments
    const getLineGradient = (fromStage: RoleStage, toStage: RoleStage) => {
        const colorMap: Record<ColorKey, string> = {
            'sky-500': '#0ea5e9',
            'violet-500': '#8b5cf6',
            'indigo-500': '#6366f1',
            'blue-500': '#3b82f6',
            'cyan-500': '#06b6d4',
            'emerald-500': '#10b981',
            'amber-500': '#f59e0b',
            'green-500': '#22c55e',
            'red-500': '#ef4444',
            'slate-500': '#64748b',
        };

        const fromColorKey = roleStageColors[fromStage].bg.replace('bg-', '') as ColorKey;
        const toColorKey = roleStageColors[toStage].bg.replace('bg-', '') as ColorKey;
        return `linear-gradient(to right, ${colorMap[fromColorKey]}, ${colorMap[toColorKey]})`;
    };

    return (
        <div className="w-full py-4">
            <div className="relative">
                {/* Timeline stages */}
                <div
                    className="relative grid items-center"
                    style={{
                        gridTemplateColumns: `repeat(${stages.length}, 1fr)`,
                        gap: '0',
                    }}
                >
                    {stages.map((stage, index) => {
                        const isCompleted = index < currentIndex;
                        const isCurrent = stage === currentStage;
                        const isDecisionStage = stage === RoleStage.DECISION;
                        const colors = getStageColor(stage, index);
                        const isFirst = index === 0;
                        const isLast = index === stages.length - 1;
                        const nextStage = !isLast ? stages[index + 1] : null;

                        return (
                            <div key={stage} className="flex flex-col items-center relative">
                                {/* Line segments */}
                                {!isFirst && (
                                    <div className="absolute top-[6px] left-0 right-[50%] h-0.5 bg-slate-200/70 dark:bg-slate-700/40" />
                                )}
                                {!isLast && (
                                    <div className="absolute top-[6px] left-[50%] right-0 h-0.5 bg-slate-200/70 dark:bg-slate-700/40" />
                                )}
                                {/* Progress line segments */}
                                {isCompleted && !isFirst && (
                                    <div
                                        className="absolute top-[6px] left-0 right-[50%] h-0.5"
                                        style={{
                                            background: getLineGradient(stages[index - 1], stage)
                                        }}
                                    />
                                )}
                                {(isCompleted || isCurrent) && !isLast && index < currentIndex && nextStage && (
                                    <div
                                        className="absolute top-[6px] left-[50%] right-0 h-0.5"
                                        style={{
                                            background: getLineGradient(stage, nextStage)
                                        }}
                                    />
                                )}

                                {/* Stage dot */}
                                <div
                                    className={cn(
                                        'w-3 h-3 rounded-full relative z-10 transition-colors duration-300',
                                        colors.bg
                                    )}
                                />

                                {/* Stage label */}
                                <span
                                    className={cn(
                                        'text-xs mt-2 font-medium whitespace-nowrap text-center transition-colors duration-300',
                                        colors.text
                                    )}
                                >
                                    {isDecisionStage && decision ?
                                        decision.charAt(0) + decision.slice(1).toLowerCase() :
                                        stage.split('_').map(word =>
                                            word.charAt(0) + word.slice(1).toLowerCase()
                                        ).join(' ')
                                    }
                                </span>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
} 