import { InterviewStage } from '@/types/role';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { Badge } from '@/components/ui';
import { Users, CheckCircle, Award, Clock, Loader2 } from 'lucide-react';

interface InterviewStageCardProps {
  stage: InterviewStage;
  index: number;
  isLast: boolean;
  roleId: string;
  isLoading?: boolean;
  processingStatus?: 'questions' | 'criteria' | 'activation' | null;
}

/**
 * InterviewStageCard component displays a single interview stage in the interview process
 * It shows the stage name, duration, state, statistics, and any custom instructions
 * Responds to hover and navigates to interview setup page when clicked
 * 
 * @param stage - The interview stage data to display
 * @param index - The index of the stage in the interview process
 * @param isLast - Whether this is the last stage in the process
 * @param roleId - The ID of the role this interview stage belongs to
 * @param isLoading - Whether the templates are currently being loaded
 * @param processingStatus - The current processing status for this stage during publish
 */
export function InterviewStageCard({ 
  stage, 
  index, 
  isLast, 
  roleId, 
  isLoading = false,
  processingStatus = null
}: InterviewStageCardProps) {
  const router = useRouter();

  // Handle click to navigate to interview setup page
  const handleClick = () => {
    router.push(`/roles/${roleId}/interview-setup/${index}`);
  };

  // Get color for state badge
  const getStateColor = (state?: string) => {
    switch (state) {
      case 'Draft':
        return 'bg-yellow-500/10 text-yellow-400 ring-1 ring-yellow-400/20';
      case 'Live':
        return 'bg-green-500/10 text-green-400 ring-1 ring-green-400/20';
      case 'Paused':
        return 'bg-blue-500/10 text-blue-400 ring-1 ring-blue-400/20';
      case 'Closed':
        return 'bg-gray-500/10 text-gray-400 ring-1 ring-gray-400/20';
      default:
        return 'bg-yellow-500/10 text-yellow-400 ring-1 ring-yellow-400/20'; // Default to Draft
    }
  };

  // Get processing status text
  const getProcessingStatusText = () => {
    switch (processingStatus) {
      case 'questions':
        return 'Generating questions...';
      case 'criteria':
        return 'Creating evaluation criteria...';
      case 'activation':
        return 'Activating interview...';
      default:
        return null;
    }
  };

  // Check if statistics exist
  const hasStatistics = stage.statistics && (
    stage.statistics.applicantsCount !== undefined ||
    stage.statistics.passedCount !== undefined ||
    stage.statistics.highestScore !== undefined
  );

  const processingStatusText = getProcessingStatusText();
  const isProcessing = processingStatus !== null;

  return (
    <div 
      className={cn(
        "relative pl-8 pb-6 last:pb-0",
        "cursor-pointer",
        "transition-all duration-200",
        "hover:bg-slate-200/40 dark:hover:bg-slate-800/70 hover:rounded-md hover:pl-10 hover:py-2",
        (isLoading || isProcessing) && "opacity-90"
      )}
      onClick={handleClick}
    >
      {/* Timeline connector line with dot */}
      <div className="absolute left-0 top-0 h-full w-px bg-slate-300 dark:bg-slate-700/40">
        <div className={cn(
          "absolute top-0 left-0 -translate-x-1/2 h-3 w-3 rounded-full border-2 border-blue-500",
          "bg-white dark:bg-slate-800/60",
          "transition-all duration-200",
          isProcessing && "border-purple-500 animate-pulse"
        )} />
        {/* Hide the line for the last item */}
        {isLast && <div className="absolute top-3 left-0 h-full w-px bg-white dark:bg-slate-800/60" />}
      </div>
      
      {/* Stage content */}
      <div className="flex justify-between items-start">
        <div className="space-y-1">
          <h3 className="font-medium text-slate-700 dark:text-slate-300 group-hover:text-slate-900 dark:group-hover:text-slate-100">{stage.stage}</h3>
          <div className="flex items-center gap-1">
            <Clock className="h-3.5 w-3.5 text-slate-500 dark:text-slate-400" />
            <p className="text-sm text-slate-600 dark:text-slate-400">{stage.duration}</p>
          </div>
          {processingStatusText && (
            <div className="flex items-center gap-1 text-xs text-purple-600 dark:text-purple-400 mt-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>{processingStatusText}</span>
            </div>
          )}
          {stage.customInstructions && !isProcessing && (
            <p className="text-sm italic text-slate-600 dark:text-slate-500">{stage.customInstructions}</p>
          )}
        </div>

        <div className="flex flex-col items-end gap-2">
          <Badge className={cn(
            getStateColor(stage.state),
            (isLoading || isProcessing) && "animate-pulse"
          )}>
            {stage.state || 'Draft'}
          </Badge>

          {hasStatistics && !isProcessing && (
            <div className="flex flex-col items-end gap-1 text-xs">
              {stage.statistics?.applicantsCount !== undefined && (
                <div className="flex items-center gap-1 text-slate-600 dark:text-slate-400">
                  <Users className="h-3 w-3 text-indigo-500 dark:text-indigo-400" />
                  <span>{stage.statistics.applicantsCount} applicants</span>
                </div>
              )}
              {stage.statistics?.passedCount !== undefined && (
                <div className="flex items-center gap-1 text-slate-600 dark:text-slate-400">
                  <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400" />
                  <span>{stage.statistics.passedCount} passed</span>
                </div>
              )}
              {stage.statistics?.highestScore !== undefined && (
                <div className="flex items-center gap-1 text-slate-600 dark:text-slate-400">
                  <Award className="h-3 w-3 text-purple-600 dark:text-purple-400" />
                  <span>Top: {stage.statistics.highestScore}</span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 