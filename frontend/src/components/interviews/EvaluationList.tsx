import React from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/Button';
import { Eye, Clock } from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Evaluation } from '@/services/evaluation/api';

interface EvaluationListProps {
  evaluations: Evaluation[];
  isLoading?: boolean;
  onViewEvaluation?: (evaluationId: string) => void;
  className?: string;
}

/**
 * EvaluationList component for displaying a list of interview evaluations
 */
export function EvaluationList({
  evaluations,
  isLoading = false,
  onViewEvaluation,
  className,
}: EvaluationListProps) {
  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (evaluations.length === 0) {
    return (
      <div className="text-center py-8 text-slate-400">
        No evaluations found.
      </div>
    );
  }

  const getStatusColor = (status: string, decision?: string) => {
    if (status !== 'completed') {
      return 'bg-yellow-500/10 text-yellow-400 ring-1 ring-yellow-400/20';
    }

    switch (decision) {
      case 'Go':
        return 'bg-green-500/10 text-green-400 ring-1 ring-green-400/20';
      case 'No Go':
        return 'bg-red-500/10 text-red-400 ring-1 ring-red-400/20';
      default:
        return 'bg-blue-500/10 text-blue-400 ring-1 ring-blue-400/20';
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {evaluations.map((evaluation) => (
        <Card key={evaluation.id} className="bg-slate-800/50 border border-slate-700">
          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="font-medium text-slate-300">
                  Evaluation {evaluation.id.substring(0, 8)}
                </h3>
                <p className="text-sm text-slate-400">
                  <Clock className="inline-block h-3 w-3 mr-1" />
                  {evaluation.createdAt.toLocaleDateString()}
                </p>
              </div>
              <div className="flex gap-2">
                <Badge className={cn(
                  "flex items-center px-2 py-1",
                  getStatusColor(evaluation.status, evaluation.data?.decision)
                )}>
                  {evaluation.status === 'completed'
                    ? evaluation.data?.decision || 'Completed'
                    : evaluation.status}
                </Badge>
                {evaluation.data?.overallScore !== undefined && (
                  <Badge className="flex items-center px-2 py-1 bg-blue-500/10 text-blue-400 ring-1 ring-blue-400/20">
                    Score: {evaluation.data.overallScore.toFixed(1)}
                  </Badge>
                )}
              </div>
            </div>

            {evaluation.status === 'completed' && evaluation.data?.summary && (
              <div className="mt-2 text-sm text-slate-400 line-clamp-2">
                {evaluation.data.summary}
              </div>
            )}

            <div className="mt-3 flex justify-end">
              <Button
                size="sm"
                variant="secondary"
                onClick={() => onViewEvaluation?.(evaluation.id)}
                disabled={evaluation.status !== 'completed'}
              >
                {evaluation.status === 'completed' ? (
                  <>
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </>
                ) : (
                  <>
                    <LoadingSpinner className="h-4 w-4 mr-2" />
                    Processing...
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
