import React from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/Button';
import { Eye, Clock, Video, Calendar, Briefcase, ExternalLink } from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { InterviewSession } from '@/services/interviews';

interface InterviewListProps {
  interviews: InterviewSession[];
  isLoading?: boolean;
  onViewInterview?: (interviewId: string) => void;
  className?: string;
}

/**
 * InterviewList component for displaying a list of interview sessions
 */
export function InterviewList({
  interviews,
  isLoading = false,
  onViewInterview,
  className,
}: InterviewListProps) {
  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (interviews.length === 0) {
    return (
      <div className="text-center py-8 text-slate-400">
        No interviews found.
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    const statusLower = status?.toLowerCase() || '';

    if (statusLower.includes('complete') || statusLower.includes('done') || statusLower.includes('finish')) {
      return 'bg-green-500/10 text-green-400 ring-1 ring-green-400/20';
    } else if (statusLower.includes('progress') || statusLower.includes('ongoing') || statusLower.includes('active')) {
      return 'bg-yellow-500/10 text-yellow-400 ring-1 ring-yellow-400/20';
    } else if (statusLower.includes('schedule') || statusLower.includes('planned') || statusLower.includes('upcoming')) {
      return 'bg-blue-500/10 text-blue-400 ring-1 ring-blue-400/20';
    } else {
      return 'bg-slate-500/10 text-slate-400 ring-1 ring-slate-400/20';
    }
  };

  const getStatusDisplay = (status: string) => {
    const statusLower = status?.toLowerCase() || '';

    if (statusLower.includes('complete') || statusLower.includes('done') || statusLower.includes('finish')) {
      return 'Completed';
    } else if (statusLower.includes('progress') || statusLower.includes('ongoing') || statusLower.includes('active')) {
      return 'In Progress';
    } else if (statusLower.includes('schedule') || statusLower.includes('planned') || statusLower.includes('upcoming')) {
      return 'Scheduled';
    } else {
      return status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown';
    }
  };

  const getRoleNameFromId = (roleId?: string) => {
    if (!roleId) return 'Unknown Role';

    // Extract a readable name from the role ID if possible
    // This is a fallback when the actual role name is not available
    const parts = roleId.split('-');
    if (parts.length > 1) {
      return `Role ${parts[0]}`;
    }

    return roleId.length > 8 ? `Role ${roleId.substring(0, 8)}...` : `Role ${roleId}`;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      return 'Invalid date';
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {interviews.map((interview) => (
        <Card key={interview.id} className="bg-slate-800/50 border border-slate-700">
          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="font-medium text-slate-300">
                  {interview.candidateName || interview.candidate?.name || `Candidate ${interview.id.substring(0, 6)}`}
                </h3>
                <p className="text-sm text-slate-400">
                  <Clock className="inline-block h-3 w-3 mr-1" />
                  {formatDate(interview.created_at || interview.createdAt)}
                </p>
              </div>
              <div className="flex gap-2">
                <Badge className={cn(
                  "flex items-center px-2 py-1",
                  getStatusColor(interview.status)
                )}>
                  {getStatusDisplay(interview.status)}
                </Badge>
              </div>
            </div>

            <div className="mt-2 text-sm text-slate-400">
              <div className="flex items-center">
                <Briefcase className="h-3 w-3 mr-1" />
                <span>Role: {interview.roleName || interview.role?.title || getRoleNameFromId(interview.roleId || interview.role_id)}</span>
              </div>
              <div className="flex items-center mt-1">
                <Video className="h-3 w-3 mr-1" />
                <span>Stage: {interview.stage || interview.interviewStage || 'Technical Interview'}</span>
              </div>
              {interview.completed_at || interview.completedAt ? (
                <div className="flex items-center mt-1">
                  <Calendar className="h-3 w-3 mr-1" />
                  <span>Completed: {formatDate(interview.completed_at || interview.completedAt)}</span>
                </div>
              ) : null}
            </div>

            <div className="mt-3 flex justify-end space-x-2">
              {(interview.templateId || interview.template_id || interview.template?.id) && (
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={(e) => {
                    e.stopPropagation();
                    const templateUrl = interview.template?.url ||
                      `/templates/${interview.templateId || interview.template_id || interview.template?.id}`;
                    window.open(templateUrl, '_blank');
                  }}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Template
                </Button>
              )}
              <Button
                size="sm"
                variant="secondary"
                onClick={() => onViewInterview?.(interview.id)}
              >
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
