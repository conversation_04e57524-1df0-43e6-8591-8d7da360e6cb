import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Textarea } from '@/components/ui/Textarea';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Question, QuestionCreate, QuestionUpdate } from '@/services/templates/api';
import { toast } from 'sonner';
import { X } from 'lucide-react';

interface QuestionFormProps {
  question?: Question;
  onSave: (data: QuestionCreate | QuestionUpdate) => Promise<void>;
  onCancel: () => void;
  isEditing?: boolean;
}

/**
 * QuestionForm component
 * A form for adding or editing interview questions
 */
export function QuestionForm({ question, onSave, onCancel, isEditing = false }: QuestionFormProps) {
  const [formData, setFormData] = useState<QuestionCreate | QuestionUpdate>({
    question: question?.question || '',
    purpose: question?.purpose || '',
    idealAnswerCriteria: question?.idealAnswerCriteria || '',
  });
  const [saving, setSaving] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Validate required fields
      if (!formData.question) {
        toast.error('Question text is required');
        return;
      }
      
      setSaving(true);
      await onSave(formData);
      toast.success(isEditing ? 'Question updated successfully' : 'Question added successfully');
    } catch (error) {
      console.error('Error saving question:', error);
      toast.error(isEditing ? 'Failed to update question' : 'Failed to add question');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="bg-slate-900/50 border border-slate-800 rounded-md p-4 transition-all duration-300">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-slate-100">{isEditing ? 'Edit Question' : 'Add New Question'}</h3>
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={onCancel}
          disabled={saving}
          className="h-8 w-8"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="question">Question Text</Label>
          <Textarea
            id="question"
            name="question"
            value={formData.question}
            onChange={handleChange}
            className="bg-slate-800 border-slate-700 min-h-[100px]"
            placeholder="Enter the interview question..."
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="purpose">Question Purpose</Label>
          <Input
            id="purpose"
            name="purpose"
            value={formData.purpose}
            onChange={handleChange}
            className="bg-slate-800 border-slate-700"
            placeholder="e.g. Assess technical knowledge of React"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="idealAnswerCriteria">Ideal Answer Criteria</Label>
          <Textarea
            id="idealAnswerCriteria"
            name="idealAnswerCriteria"
            value={formData.idealAnswerCriteria}
            onChange={handleChange}
            className="bg-slate-800 border-slate-700 min-h-[100px]"
            placeholder="Describe what makes an ideal answer to this question..."
          />
        </div>
        
        <div className="flex justify-end gap-2 pt-2">
          <Button 
            type="button" 
            variant="secondary" 
            onClick={onCancel}
            disabled={saving}
          >
            Cancel
          </Button>
          <Button 
            type="submit"
            disabled={saving}
          >
            {saving ? <LoadingSpinner className="mr-2 h-4 w-4" /> : null}
            {isEditing ? 'Update Question' : 'Add Question'}
          </Button>
        </div>
      </form>
    </div>
  );
} 