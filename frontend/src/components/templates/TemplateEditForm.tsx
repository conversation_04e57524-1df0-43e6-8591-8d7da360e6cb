import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Textarea } from '@/components/ui/Textarea';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { InterviewTemplate, UpdateTemplateRequest } from '@/services/templates';
import { toast } from 'sonner';
import { Save } from 'lucide-react';

interface TemplateEditFormProps {
  template: InterviewTemplate;
  onSave: (data: UpdateTemplateRequest) => Promise<void>;
  onCancel: () => void;
}

/**
 * TemplateEditForm component
 * A form for editing interview templates with a minimal, clean design
 */
export function TemplateEditForm({ template, onSave, onCancel }: TemplateEditFormProps) {
  const [formData, setFormData] = useState<UpdateTemplateRequest>({
    stage: template.stage,
    duration: template.duration,
    customInstructions: template.customInstructions,
  });
  const [saving, setSaving] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      await onSave(formData);
      toast.success('Template updated successfully');
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Failed to update template');
    } finally {
      setSaving(false);
    }
  };

  return (
    <Card className="bg-slate-900/50 border-slate-800 transition-all duration-300">
      <CardHeader className="pb-3">
        <CardTitle className="text-slate-100 text-lg">Edit Stage Details</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          {/* Stage Name and Duration in one row for desktop */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stage" className="text-sm text-slate-300">Stage Name</Label>
              <Input
                id="stage"
                name="stage"
                value={formData.stage}
                onChange={handleChange}
                className="bg-slate-800 border-slate-700 text-slate-200"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="duration" className="text-sm text-slate-300">Duration</Label>
              <Input
                id="duration"
                name="duration"
                value={formData.duration}
                onChange={handleChange}
                className="bg-slate-800 border-slate-700 text-slate-200"
                placeholder="e.g. 30 minutes"
                required
              />
            </div>
          </div>
          
          {/* Custom Instructions */}
          <div className="space-y-4 pt-2 border-t border-slate-700/50">
            <div className="space-y-2">
              <Label htmlFor="customInstructions" className="text-sm text-slate-300">Custom Instructions</Label>
              <Textarea
                id="customInstructions"
                name="customInstructions"
                value={formData.customInstructions}
                onChange={handleChange}
                className="bg-slate-800 border-slate-700 text-slate-200 min-h-[80px] resize-y"
                placeholder="Enter any special instructions for this interview stage..."
              />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end gap-2">
          <Button 
            type="button" 
            variant="secondary" 
            onClick={onCancel}
            disabled={saving}
            className="border-slate-700 text-slate-300 hover:bg-slate-800 hover:text-slate-100"
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={saving}
            className="gap-2"
          >
            {saving ? <LoadingSpinner className="h-4 w-4" /> : <Save className="h-4 w-4" />}
            Save Changes
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
} 