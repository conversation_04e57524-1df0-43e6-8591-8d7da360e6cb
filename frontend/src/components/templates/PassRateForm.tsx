import { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/Input';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { cn } from '@/lib/utils';

interface PassRateFormProps {
  currentPassRate: number;
  onSave: (passRate: number) => Promise<void>;
  className?: string;
}

/**
 * PassRateForm component
 * Minimal, compact form for setting the pass rate threshold with auto-save functionality
 */
export function PassRateForm({ 
  currentPassRate, 
  onSave,
  className
}: PassRateFormProps) {
  // Convert from decimal to percentage for display
  const [passRateDisplay, setPassRateDisplay] = useState(
    (currentPassRate * 100).toFixed(0)
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update the display value when the current pass rate changes
  useEffect(() => {
    setPassRateDisplay((currentPassRate * 100).toFixed(0));
  }, [currentPassRate]);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  // Handle input change
  const handlePassRateChange = (value: string) => {
    setPassRateDisplay(value);
    
    // Convert percentage to decimal for storage
    const numValue = parseFloat(value) / 100;
    if (!isNaN(numValue)) {
      if (numValue < 0 || numValue > 1) {
        setError('Pass rate must be between 0% and 100%');
      } else {
        setError(null);
        
        // Auto-save after a delay
        if (saveTimeoutRef.current) {
          clearTimeout(saveTimeoutRef.current);
        }
        
        saveTimeoutRef.current = setTimeout(() => {
          savePassRate(numValue);
        }, 1000); // 1 second delay before saving
      }
    } else {
      setError('Please enter a valid number');
    }
  };

  // Save the pass rate
  const savePassRate = async (rate: number) => {
    // Don't save if the value hasn't changed
    if (rate === currentPassRate) return;
    
    setLoading(true);
    setError(null);
    
    try {
      await onSave(rate);
    } catch (error) {
      console.error('Error saving pass rate:', error);
      setError('Failed to save pass rate. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <span className="text-sm text-green-400 whitespace-nowrap">Minimum Pass Score</span>
      <div className="relative w-16">
        <Input
          id="passRate"
          value={passRateDisplay}
          onChange={(e) => handlePassRateChange(e.target.value)}
          placeholder="70"
          type="number"
          min="0"
          max="100"
          className={cn(
            "h-7 pr-5 text-center text-sm",
            error ? "border-red-500" : "border-slate-700 focus:border-green-400/50"
          )}
        />
        <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 text-xs">
          %
        </span>
      </div>
      {loading && <LoadingSpinner className="h-3.5 w-3.5 text-slate-400" />}
    </div>
  );
} 