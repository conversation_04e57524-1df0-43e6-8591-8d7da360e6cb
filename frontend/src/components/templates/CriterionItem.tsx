import { useState } from 'react';
import { Criterion, CriterionType } from '@/services/templates/api';
import { Button } from '@/components/ui/Button';
import { Edit, Trash2, MoreVertical, X, Award } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropdownMenu';
import React from 'react';

interface CriterionItemProps {
  criterion: Criterion;
  onEdit: (criterion: Criterion) => void;
  onDelete: (criterionId: string) => void;
  isEditMode?: boolean;
}

/**
 * CriterionItem component
 * Displays a criterion with options to edit or delete via dropdown menu
 */
export function CriterionItem({
  criterion,
  onEdit,
  onDelete,
  isEditMode = false
}: CriterionItemProps) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // This is a no-op to prevent the unused parameter warning
  React.useEffect(() => {
    // This is to prevent the unused parameter warning
    void isEditMode;
  }, [isEditMode]);

  const handleEdit = () => {
    onEdit(criterion);
  };

  const handleDelete = () => {
    setShowDeleteConfirm(true);
  };

  const confirmDelete = () => {
    onDelete(criterion.id);
    setShowDeleteConfirm(false);
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  // Get the weight as a percentage if it's a ScoreCard criterion
  const getWeightPercentage = () => {
    if (criterion.type === CriterionType.SCORECARD) {
      // Type assertion to access weight property
      const scoreCard = criterion as any;
      const weight = scoreCard.weight || 0;
      // Convert to percentage
      return Math.round(weight * 100);
    }
    return 0;
  };

  // Get the competency if it's a ScoreCard criterion
  const getCompetency = () => {
    if (criterion.type === CriterionType.SCORECARD) {
      // Type assertion to access competency property
      const scoreCard = criterion as any;
      return scoreCard.competency;
    }
    return null;
  };

  // Use a more compact layout for Between the Lines and Disqualifiers
  const isSimpleCriterion = criterion.type === CriterionType.BETWEEN_THE_LINES ||
                           criterion.type === CriterionType.DISQUALIFIER;

  return (
    <div className={cn(
      "rounded-md border transition-all duration-300",
      "bg-white border-[#FFE5D9] hover:border-[#FFCDB2] hover:shadow-md dark:bg-slate-800/50 dark:border-slate-700 dark:hover:border-slate-600",
      isSimpleCriterion ? "p-2" : "p-4"
    )}>
      {/* Single row with criterion and actions */}
      <div className="flex items-center justify-between">
        {/* Left side: Criterion content */}
        <div className="flex flex-col flex-grow">
          {/* Display competency for ScoreCard criteria */}
          {criterion.type === CriterionType.SCORECARD && (
            <div className="flex items-center">
              <h3 className="font-medium text-slate-800 dark:text-slate-300">{getCompetency()}</h3>
            </div>
          )}

          {/* Display criteria text */}
          <p className={cn(
            "text-slate-700 dark:text-slate-300",
            criterion.type === CriterionType.SCORECARD ? "text-sm" : "font-medium"
          )}>
            {criterion.criteria}
          </p>
        </div>

        {/* Right side: Importance for ScoreCard and actions */}
        <div className="flex items-center gap-3">
          {/* Importance text for ScoreCard */}
          {criterion.type === CriterionType.SCORECARD && (
            <span className="text-amber-600 dark:text-amber-400 text-sm flex items-center">
              <Award className="h-3 w-3 mr-1" />
              {getWeightPercentage()}%
            </span>
          )}

          {/* Three-dot menu for all criterion types */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 p-0 border-0 text-slate-500 hover:text-slate-700 hover:bg-slate-200/50 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-700/50"
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit} className="cursor-pointer">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleDelete}
                className="cursor-pointer text-red-400 focus:text-red-400"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Delete confirmation */}
      {showDeleteConfirm && (
        <div className="mt-4 p-4 bg-white dark:bg-slate-800/70 border border-[#FFE5D9] dark:border-slate-700 rounded-md">
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">Confirm Deletion</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={cancelDelete}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-sm text-slate-600 dark:text-slate-400 mb-3">
            Are you sure you want to delete this criterion? This action cannot be undone.
          </p>
          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={cancelDelete}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}