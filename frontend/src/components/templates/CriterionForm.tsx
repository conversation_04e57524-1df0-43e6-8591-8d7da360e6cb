import { useState, useEffect } from 'react';
import { CriterionCreate, CriterionUpdate, CriterionType, Criterion } from '@/services/templates/api';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Label } from '@/components/ui/Label';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { X, Save } from 'lucide-react';
import { cn } from '@/lib/utils';
import React from 'react';

interface CriterionFormProps {
  criterion?: Criterion;
  onSave: (criterion: CriterionCreate | CriterionUpdate) => Promise<void>;
  onCancel: () => void;
  isEditing?: boolean;
  existingCriteria?: Criterion[];
  initialType?: CriterionType;
}

/**
 * CriterionForm component
 * Form for adding or editing evaluation criteria
 */
export function CriterionForm({ 
  criterion, 
  onSave, 
  onCancel, 
  isEditing = false,
  existingCriteria = [],
  initialType
}: CriterionFormProps) {
  const [type, setType] = useState<CriterionType>(
    criterion ? criterion.type : initialType || CriterionType.SCORECARD
  );
  const [criteria, setCriteria] = useState(criterion?.criteria || '');
  const [competency, setCompetency] = useState('');
  const [weight, setWeight] = useState<number>(0.2); // Default to 20%
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // This is a no-op to prevent the unused parameter warning
  React.useEffect(() => {
    // This is to prevent the unused parameter warning
    void existingCriteria;
  }, [existingCriteria]);

  // Initialize ScoreCard-specific fields if editing a ScoreCard criterion
  useEffect(() => {
    if (criterion && criterion.type === CriterionType.SCORECARD) {
      // Type assertion to access ScoreCard properties
      const scoreCard = criterion as any;
      setCompetency(scoreCard.competency || '');
      
      // Set weight directly from the criterion
      setWeight(scoreCard.weight || 0.2);
    }
  }, [criterion]);

  // Validate the form based on criterion type
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!criteria.trim()) {
      newErrors.criteria = 'Criteria text is required';
    }
    
    if (type === CriterionType.SCORECARD) {
      if (!competency.trim()) {
        newErrors.competency = 'Competency is required';
      }
      
      if (weight <= 0 || weight > 1) {
        newErrors.weight = 'Weight must be between 1% and 100%';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle weight change
  const handleWeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) / 100;
    if (!isNaN(value) && value >= 0 && value <= 1) {
      setWeight(value);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    
    try {
      let criterionData: CriterionCreate | CriterionUpdate;
      
      if (isEditing) {
        // For editing, only include fields that are being updated
        criterionData = {
          criteria
        };
        
        // Only include type-specific fields if it's a ScoreCard
        if (type === CriterionType.SCORECARD) {
          criterionData.competency = competency;
          criterionData.weight = weight;
        }
      } else {
        // For creating, include all required fields based on type
        if (type === CriterionType.SCORECARD) {
          criterionData = {
            type: CriterionType.SCORECARD,
            criteria,
            competency,
            weight
          };
        } else if (type === CriterionType.BETWEEN_THE_LINES) {
          criterionData = {
            type: CriterionType.BETWEEN_THE_LINES,
            criteria
          };
        } else {
          criterionData = {
            type: CriterionType.DISQUALIFIER,
            criteria
          };
        }
      }
      
      await onSave(criterionData);
      
      // Reset form if not editing
      if (!isEditing) {
        setCriteria('');
        setCompetency('');
        setWeight(0.2);
      }
    } catch (error) {
      console.error('Error saving criterion:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 p-4 bg-slate-800/50 border border-slate-700 rounded-md">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-lg font-medium text-slate-200">
          {isEditing ? 'Edit Criterion' : 'Add Criterion'}
        </h3>
        <Button 
          type="button" 
          variant="ghost" 
          size="sm" 
          onClick={onCancel}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Cancel</span>
        </Button>
      </div>
      
      {/* Criterion Type Selection - Only shown when creating a new criterion */}
      {!isEditing && (
        <div className="space-y-2">
          <Label htmlFor="type">Criterion Type</Label>
          <div className="flex flex-wrap gap-2">
            <Button
              type="button"
              variant={type === CriterionType.SCORECARD ? "default" : "secondary"}
              size="sm"
              onClick={() => setType(CriterionType.SCORECARD)}
              className={cn(
                type === CriterionType.SCORECARD && "bg-amber-600 hover:bg-amber-700 text-white"
              )}
            >
              ScoreCard
            </Button>
            <Button
              type="button"
              variant={type === CriterionType.BETWEEN_THE_LINES ? "default" : "secondary"}
              size="sm"
              onClick={() => setType(CriterionType.BETWEEN_THE_LINES)}
              className={cn(
                type === CriterionType.BETWEEN_THE_LINES && "bg-blue-600 hover:bg-blue-700 text-white"
              )}
            >
              Between the Lines
            </Button>
            <Button
              type="button"
              variant={type === CriterionType.DISQUALIFIER ? "default" : "secondary"}
              size="sm"
              onClick={() => setType(CriterionType.DISQUALIFIER)}
              className={cn(
                type === CriterionType.DISQUALIFIER && "bg-red-600 hover:bg-red-700 text-white"
              )}
            >
              Disqualifier
            </Button>
          </div>
        </div>
      )}
      
      {/* Competency Field - Only for ScoreCard */}
      {type === CriterionType.SCORECARD && (
        <div className="space-y-2">
          <Label htmlFor="competency">
            Competency
            <span className="text-red-500 ml-1">*</span>
          </Label>
          <Input
            id="competency"
            value={competency}
            onChange={(e) => setCompetency(e.target.value)}
            placeholder="e.g., Strategic Thinking"
            className={cn(errors.competency && "border-red-500")}
          />
          {errors.competency && (
            <p className="text-sm text-red-500">{errors.competency}</p>
          )}
        </div>
      )}
      
      {/* Weight Field - Only for ScoreCard */}
      {type === CriterionType.SCORECARD && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="weight">
              Weight (%)
              <span className="text-red-500 ml-1">*</span>
            </Label>
          </div>
          <div className="flex items-center gap-2">
            <Input
              id="weight"
              type="number"
              min="1"
              max="100"
              value={Math.round(weight * 100)}
              onChange={handleWeightChange}
              className={cn(
                "w-24",
                errors.weight && "border-red-500"
              )}
            />
            <span className="text-sm text-slate-400">%</span>
          </div>
          {errors.weight && (
            <p className="text-sm text-red-500">{errors.weight}</p>
          )}
          <p className="text-xs text-slate-400">
            Higher percentage = more important competency
          </p>
        </div>
      )}
      
      {/* Criteria Text - Required for all types */}
      <div className="space-y-2">
        <Label htmlFor="criteria">
          Evaluation Criteria
          <span className="text-red-500 ml-1">*</span>
        </Label>
        <Textarea
          id="criteria"
          value={criteria}
          onChange={(e) => setCriteria(e.target.value)}
          placeholder={
            type === CriterionType.SCORECARD 
              ? "e.g., Can they solve complex problems logically and proactively?"
              : type === CriterionType.BETWEEN_THE_LINES
                ? "e.g., Do they show structured thought processes or just guess their way through?"
                : "e.g., No structured problem-solving approach."
          }
          className={cn(errors.criteria && "border-red-500")}
          rows={3}
        />
        {errors.criteria && (
          <p className="text-sm text-red-500">{errors.criteria}</p>
        )}
      </div>
      
      {/* Form Actions */}
      <div className="flex justify-end gap-2 pt-2">
        <Button 
          type="button" 
          variant="secondary" 
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button 
          type="submit" 
          disabled={loading}
          className="gap-2"
        >
          {loading ? <LoadingSpinner className="h-4 w-4" /> : <Save className="h-4 w-4" />}
          {isEditing ? 'Update' : 'Save'}
        </Button>
      </div>
    </form>
  );
} 