import { useState, useEffect, useRef, KeyboardEvent } from 'react';
import { Criterion, CriterionCreate, CriterionUpdate, CriterionType } from '@/services/templates/api';
import { Button } from '@/components/ui/Button';
import { Plus, Award, Eye, AlertTriangle, Check, X, Save } from 'lucide-react';
import { CriterionItem } from './CriterionItem';
import { CriterionForm } from './CriterionForm';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { toast } from 'sonner';
import React from 'react';

interface CriteriaListProps {
  criteria: Criterion[];
  onAddCriterion: (criterion: CriterionCreate) => Promise<void>;
  onUpdateCriterion: (criterionId: string, criterion: CriterionUpdate) => Promise<void>;
  onDeleteCriterion: (criterionId: string) => Promise<void>;
  onUpdateImportance?: (criterionId: string, importance: number) => Promise<void>;
  isLoading?: boolean;
  showActionButtons?: boolean;
  id?: string;
}

/**
 * InlineCriterionForm component
 * A compact, row-based form for adding Between the Lines and Disqualifier criteria
 */
function InlineCriterionForm({
  type,
  onSave,
  onCancel
}: {
  type: CriterionType;
  onSave: (value: string) => Promise<void>;
  onCancel: () => void;
}) {
  const [value, setValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Focus the input when the component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const handleSubmit = async () => {
    if (!value.trim()) return;

    setIsSubmitting(true);
    try {
      await onSave(value);
      setValue('');
      // Keep focus in the input field after saving to allow for quick entry of multiple items
      if (inputRef.current) {
        inputRef.current.focus();
      }
    } catch (error) {
      console.error('Error saving criterion:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    } else if (e.key === 'Escape') {
      onCancel();
    }
  };

  // Get the appropriate styling based on criterion type
  const getTypeStyles = () => {
    return type === CriterionType.BETWEEN_THE_LINES
      ? "border-blue-400/30 focus-within:border-blue-400/50 bg-blue-500/5"
      : "border-red-400/30 focus-within:border-red-400/50 bg-red-500/5";
  };

  return (
    <div className={cn(
      "flex items-center gap-2 p-2 rounded-md border transition-all duration-200",
      "border-[#FFE5D9] bg-white dark:border-slate-700 dark:bg-slate-800/50",
      getTypeStyles()
    )}>
      <div className="flex items-center gap-2 flex-1 min-w-0 overflow-hidden">
        {type === CriterionType.BETWEEN_THE_LINES ? (
          <Eye className="h-4 w-4 text-blue-400 shrink-0" />
        ) : (
          <AlertTriangle className="h-4 w-4 text-red-400 shrink-0" />
        )}
        <input
          ref={inputRef as any}
          type="text"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          placeholder={
            type === CriterionType.BETWEEN_THE_LINES
              ? "Enter between the lines criterion and press Enter to save..."
              : "Enter disqualifier and press Enter to save..."
          }
          className="flex-1 min-w-0 h-9 py-1.5 bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0 px-0 text-slate-700 dark:text-slate-200 placeholder:text-slate-500 dark:placeholder:text-slate-400 w-full"
          onKeyDown={handleKeyDown as any}
        />
      </div>
      <div className="flex gap-1 shrink-0">
        <Button
          type="button"
          size="icon"
          variant="ghost"
          className={cn(
            "h-7 w-7 p-0",
            type === CriterionType.BETWEEN_THE_LINES
              ? "text-blue-400 hover:text-blue-300"
              : "text-red-400 hover:text-red-300"
          )}
          onClick={handleSubmit}
          disabled={!value.trim() || isSubmitting}
        >
          {isSubmitting ? <LoadingSpinner className="h-3.5 w-3.5" /> : <Check className="h-3.5 w-3.5" />}
        </Button>
        <Button
          type="button"
          size="icon"
          variant="ghost"
          className="h-7 w-7 p-0 text-slate-500 hover:text-slate-700 hover:bg-slate-200/50 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-700/50"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          <X className="h-3.5 w-3.5" />
        </Button>
      </div>
    </div>
  );
}

/**
 * InlineCompetencyForm component
 * A compact form for adding ScoreCard competencies
 */
function InlineCompetencyForm({
  onSave,
  onCancel
}: {
  onSave: (competency: string, criteria: string, importance: number) => Promise<void>;
  onCancel: () => void;
}) {
  const [competency, setCompetency] = useState('');
  const [criteria, setCriteria] = useState('');
  const [weight, setWeight] = useState(0.2); // Default to 20%
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, boolean>>({});
  const competencyRef = useRef<HTMLInputElement>(null);

  // Focus the competency input when the component mounts
  useEffect(() => {
    if (competencyRef.current) {
      competencyRef.current.focus();
    }
  }, []);

  const validateForm = (): boolean => {
    const newErrors: Record<string, boolean> = {};

    if (!competency.trim()) {
      newErrors.competency = true;
    }

    if (!criteria.trim()) {
      newErrors.criteria = true;
    }

    if (weight <= 0 || weight > 1) {
      newErrors.weight = true;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSave(competency, criteria, weight);

      // Reset form for next entry
      setCompetency('');
      setCriteria('');
      setWeight(0.2); // Reset to default

      // Focus back on competency field for quick entry of multiple items
      if (competencyRef.current) {
        competencyRef.current.focus();
      }
    } catch (error) {
      console.error('Error saving competency:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle weight input change
  const handleWeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) / 100;
    if (!isNaN(value) && value >= 0 && value <= 1) {
      setWeight(value);
    }
  };

  return (
    <div className="p-3 bg-white border border-[#FFE5D9] rounded-md space-y-3 dark:bg-amber-500/5 dark:border-amber-400/30">
      <div className="flex items-center gap-2">
        <Award className="h-4 w-4 text-amber-400 shrink-0" />
        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-200">Add Competency</h4>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={onCancel}
          className="h-7 w-7 p-0 ml-auto text-slate-500 hover:text-slate-700 hover:bg-slate-200/50 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-700/50"
        >
          <X className="h-3.5 w-3.5" />
        </Button>
      </div>

      <div className="space-y-3">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <Label htmlFor="competency" className="text-xs">Competency</Label>
            {errors.competency && <span className="text-xs text-red-500">Required</span>}
          </div>
          <Input
            ref={competencyRef}
            id="competency"
            value={competency}
            onChange={(e) => setCompetency(e.target.value)}
            placeholder="e.g., Strategic Thinking"
            className={cn(
              "h-9 bg-white border-[#FFE5D9] dark:bg-slate-800/50 dark:border-slate-700",
              errors.competency && "border-red-500"
            )}
          />
        </div>

        <div className="flex gap-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Label htmlFor="criteria" className="text-xs">Evaluation Criteria</Label>
              {errors.criteria && <span className="text-xs text-red-500">Required</span>}
            </div>
            <Input
              id="criteria"
              value={criteria}
              onChange={(e) => setCriteria(e.target.value)}
              placeholder="e.g., Can solve complex problems logically"
              className={cn(
                "h-9 bg-white border-[#FFE5D9] dark:bg-slate-800/50 dark:border-slate-700",
                errors.criteria && "border-red-500"
              )}
            />
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between mb-1">
            <Label htmlFor="weight" className="text-xs">Weight (%)</Label>
            {errors.weight && <span className="text-xs text-red-500">Must be between 1% and 100%</span>}
          </div>
          <div className="flex items-center gap-2">
            <Input
              id="weight"
              type="number"
              min="1"
              max="100"
              value={Math.round(weight * 100)}
              onChange={handleWeightChange}
              className={cn(
                "h-9 bg-white border-[#FFE5D9] dark:bg-slate-800/50 dark:border-slate-700 w-24",
                errors.weight && "border-red-500"
              )}
            />
            <span className="text-sm text-slate-600 dark:text-slate-400">%</span>
          </div>
          <p className="text-xs text-slate-600 dark:text-slate-400 mt-1">
            Higher percentage = more important competency
          </p>
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          type="button"
          onClick={handleSubmit}
          disabled={isSubmitting}
          size="sm"
          className="gap-1"
        >
          {isSubmitting ? <LoadingSpinner className="h-3.5 w-3.5" /> : <Save className="h-3.5 w-3.5" />}
          Save
        </Button>
      </div>
    </div>
  );
}

/**
 * InlineEditCompetencyForm component
 * A compact form for editing ScoreCard competencies with the same style as InlineCompetencyForm
 */
function InlineEditCompetencyForm({
  criterion,
  onSave,
  onCancel
}: {
  criterion: Criterion;
  onSave: (competency: string, criteria: string, importance: number) => Promise<void>;
  onCancel: () => void;
}) {
  // Type assertion to access ScoreCard properties
  const scoreCard = criterion as any;
  const initialWeight = scoreCard.weight || 0;

  const [competency, setCompetency] = useState(scoreCard.competency || '');
  const [criteria, setCriteria] = useState(criterion.criteria || '');
  const [weight, setWeight] = useState(initialWeight);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, boolean>>({});
  const competencyRef = useRef<HTMLInputElement>(null);

  // Focus the competency input when the component mounts
  useEffect(() => {
    if (competencyRef.current) {
      competencyRef.current.focus();
    }
  }, []);

  const validateForm = (): boolean => {
    const newErrors: Record<string, boolean> = {};

    if (!competency.trim()) {
      newErrors.competency = true;
    }

    if (!criteria.trim()) {
      newErrors.criteria = true;
    }

    if (weight <= 0 || weight > 1) {
      newErrors.weight = true;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSave(competency, criteria, weight);
    } catch (error) {
      console.error('Error updating competency:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle weight input change
  const handleWeightChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) / 100;
    if (!isNaN(value) && value >= 0 && value <= 1) {
      setWeight(value);
    }
  };

  return (
    <div className="p-3 bg-white border border-[#FFE5D9] rounded-md space-y-3 dark:bg-amber-500/5 dark:border-amber-400/30">
      <div className="flex items-center gap-2">
        <Award className="h-4 w-4 text-amber-400 shrink-0" />
        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-200">Edit Competency</h4>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={onCancel}
          className="h-7 w-7 p-0 ml-auto text-slate-500 hover:text-slate-700 hover:bg-slate-200/50 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-700/50"
        >
          <X className="h-3.5 w-3.5" />
        </Button>
      </div>

      <div className="space-y-3">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <Label htmlFor="competency" className="text-xs">Competency</Label>
            {errors.competency && <span className="text-xs text-red-500">Required</span>}
          </div>
          <Input
            ref={competencyRef}
            id="competency"
            value={competency}
            onChange={(e) => setCompetency(e.target.value)}
            placeholder="e.g., Strategic Thinking"
            className={cn(
              "h-9 bg-white border-[#FFE5D9] dark:bg-slate-800/50 dark:border-slate-700",
              errors.competency && "border-red-500"
            )}
          />
        </div>

        <div className="flex gap-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Label htmlFor="criteria" className="text-xs">Evaluation Criteria</Label>
              {errors.criteria && <span className="text-xs text-red-500">Required</span>}
            </div>
            <Input
              id="criteria"
              value={criteria}
              onChange={(e) => setCriteria(e.target.value)}
              placeholder="e.g., Can solve complex problems logically"
              className={cn(
                "h-9 bg-white border-[#FFE5D9] dark:bg-slate-800/50 dark:border-slate-700",
                errors.criteria && "border-red-500"
              )}
            />
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between mb-1">
            <Label htmlFor="weight" className="text-xs">Weight (%)</Label>
            {errors.weight && <span className="text-xs text-red-500">Must be between 1% and 100%</span>}
          </div>
          <div className="flex items-center gap-2">
            <Input
              id="weight"
              type="number"
              min="1"
              max="100"
              value={Math.round(weight * 100)}
              onChange={handleWeightChange}
              className={cn(
                "h-9 bg-white border-[#FFE5D9] dark:bg-slate-800/50 dark:border-slate-700 w-24",
                errors.weight && "border-red-500"
              )}
            />
            <span className="text-sm text-slate-600 dark:text-slate-400">%</span>
          </div>
          <p className="text-xs text-slate-600 dark:text-slate-400 mt-1">
            Higher percentage = more important competency
          </p>
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          type="button"
          onClick={handleSubmit}
          disabled={isSubmitting}
          size="sm"
          className="gap-1"
        >
          {isSubmitting ? <LoadingSpinner className="h-3.5 w-3.5" /> : <Save className="h-3.5 w-3.5" />}
          Save
        </Button>
      </div>
    </div>
  );
}

/**
 * CriteriaList component
 * Manages a list of evaluation criteria with add, edit, and delete functionality
 */
export function CriteriaList({
  criteria,
  onAddCriterion,
  onUpdateCriterion,
  onDeleteCriterion,
  onUpdateImportance,
  isLoading = false,
  showActionButtons = true,
  id
}: CriteriaListProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [addingCriterionType, setAddingCriterionType] = useState<CriterionType | null>(null);
  const [editingCriterion, setEditingCriterion] = useState<Criterion | null>(null);
  const [isAddingBetweenTheLines, setIsAddingBetweenTheLines] = useState(false);
  const [isAddingDisqualifier, setIsAddingDisqualifier] = useState(false);
  const [isAddingCompetency, setIsAddingCompetency] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // This is a no-op to prevent the unused parameter warning
  React.useEffect(() => {
    if (onUpdateImportance) {
      // This function is available but not used in this component
    }

    // This is to prevent the unused parameter warning
    void showActionButtons;
  }, [onUpdateImportance, showActionButtons]);

  // Group criteria by type
  const scoreCardCriteria = criteria.filter(c => c.type === CriterionType.SCORECARD);
  const betweenTheLinesCriteria = criteria.filter(c => c.type === CriterionType.BETWEEN_THE_LINES);
  const disqualifierCriteria = criteria.filter(c => c.type === CriterionType.DISQUALIFIER);

  // Set up event listeners for external control
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !id) return;

    const handleAddCriterion = (e: Event) => {
      const customEvent = e as CustomEvent;
      if (customEvent.detail && customEvent.detail.type) {
        handleAddClick(customEvent.detail.type);
      } else {
        // Default to ScoreCard if no type specified
        handleAddClick(CriterionType.SCORECARD);
      }
    };

    container.addEventListener('add-criterion', handleAddCriterion);

    return () => {
      container.removeEventListener('add-criterion', handleAddCriterion);
    };
  }, [id]);

  const handleAddClick = (type: CriterionType) => {
    if (type === CriterionType.BETWEEN_THE_LINES) {
      // Show inline form for Between the Lines
      setIsAddingBetweenTheLines(true);
      setIsAddingDisqualifier(false);
      setIsAddingCompetency(false);
      setShowAddForm(false);
      setAddingCriterionType(null);
    } else if (type === CriterionType.DISQUALIFIER) {
      // Show inline form for Disqualifiers
      setIsAddingDisqualifier(true);
      setIsAddingBetweenTheLines(false);
      setIsAddingCompetency(false);
      setShowAddForm(false);
      setAddingCriterionType(null);
    } else {
      // For ScoreCard, show the inline competency form
      setIsAddingCompetency(true);
      setIsAddingBetweenTheLines(false);
      setIsAddingDisqualifier(false);
      setShowAddForm(false);
      setAddingCriterionType(null);
    }
  };

  const handleCancelAdd = () => {
    setShowAddForm(false);
    setAddingCriterionType(null);
    setIsAddingBetweenTheLines(false);
    setIsAddingDisqualifier(false);
    setIsAddingCompetency(false);
  };

  const handleSaveNewCriterion = async (criterionData: CriterionCreate | CriterionUpdate) => {
    await onAddCriterion(criterionData as CriterionCreate);
    setShowAddForm(false);
    setAddingCriterionType(null);
  };

  const handleSaveInlineCriterion = async (type: CriterionType, value: string) => {
    if (!value.trim()) return;

    try {
      const criterionData = {
        type: type,
        criteria: value,
        description: ''
      } as CriterionCreate;

      await onAddCriterion(criterionData);

      // Keep the form open to allow adding multiple criteria in succession
      // Don't reset the form state, just show success message
      toast.success(`${type === CriterionType.BETWEEN_THE_LINES ? 'Between the Lines' : 'Disqualifier'} added successfully`);
    } catch (error) {
      console.error('Error adding criterion:', error);
      toast.error('Failed to add criterion');
    }
  };

  const handleSaveCompetency = async (competency: string, criteria: string, weight: number) => {
    try {
      const criterionData: CriterionCreate = {
        type: CriterionType.SCORECARD,
        criteria,
        competency,
        weight,
        description: ''
      };

      await onAddCriterion(criterionData);

      // Keep the form open to allow adding multiple competencies in succession
      toast.success('Competency added successfully');
    } catch (error) {
      console.error('Error adding competency:', error);
      toast.error('Failed to add competency');
    }
  };

  const handleEditCriterion = (criterion: Criterion) => {
    setEditingCriterion(criterion);
    setShowAddForm(false);
    setAddingCriterionType(null);
    setIsAddingBetweenTheLines(false);
    setIsAddingDisqualifier(false);
    setIsAddingCompetency(false);
  };

  const handleCancelEdit = () => {
    setEditingCriterion(null);
  };

  const handleUpdateCriterion = async (criterionData: CriterionUpdate) => {
    if (editingCriterion) {
      await onUpdateCriterion(editingCriterion.id, criterionData);
      setEditingCriterion(null);
    }
  };

  const handleUpdateCompetency = async (competency: string, criteria: string, weight: number) => {
    if (!editingCriterion) return;

    try {
      const criterionData: CriterionUpdate = {
        criteria,
        competency,
        weight
      };

      await onUpdateCriterion(editingCriterion.id, criterionData);
      setEditingCriterion(null);
      toast.success('Competency updated successfully');
    } catch (error) {
      console.error('Error updating competency:', error);
      toast.error('Failed to update competency');
    }
  };

  const handleDeleteCriterion = async (criterionId: string) => {
    await onDeleteCriterion(criterionId);
  };

  // Helper function to render a criterion item or its edit form
  const renderCriterionItem = (criterion: Criterion) => {
    if (editingCriterion && editingCriterion.id === criterion.id) {
      // If this is the criterion being edited, render the appropriate edit form
      if (criterion.type === CriterionType.SCORECARD) {
        return (
          <InlineEditCompetencyForm
            key={criterion.id}
            criterion={criterion}
            onSave={handleUpdateCompetency}
            onCancel={handleCancelEdit}
          />
        );
      } else {
        // For non-ScoreCard criteria, use the standard form
        return (
          <CriterionForm
            key={criterion.id}
            criterion={criterion}
            onSave={handleUpdateCriterion}
            onCancel={handleCancelEdit}
            isEditing
            existingCriteria={criteria}
          />
        );
      }
    } else {
      // Otherwise, render the normal item
      return (
        <CriterionItem
          key={criterion.id}
          criterion={criterion}
          onEdit={handleEditCriterion}
          onDelete={handleDeleteCriterion}
        />
      );
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <LoadingSpinner className="h-8 w-8" />
      </div>
    );
  }

  return (
    <div className="space-y-6" ref={containerRef} id={id}>
      {/* Add Criterion Form */}
      {showAddForm && (
        <CriterionForm
          onSave={handleSaveNewCriterion}
          onCancel={handleCancelAdd}
          existingCriteria={criteria}
          initialType={addingCriterionType || undefined}
        />
      )}

      {/* ScoreCard Criteria Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Award className="h-5 w-5 text-amber-400" />
            <h3 className="text-lg font-medium text-slate-700 dark:text-slate-200">Competencies</h3>
          </div>

          <Button
            variant="link"
            size="sm"
            className="text-amber-400 hover:text-amber-300"
            onClick={() => handleAddClick(CriterionType.SCORECARD)}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Competency
          </Button>
        </div>

        <div className="space-y-3">
          {scoreCardCriteria.map(renderCriterionItem)}

          {/* Inline form for adding Competency */}
          {isAddingCompetency && (
            <InlineCompetencyForm
              onSave={handleSaveCompetency}
              onCancel={handleCancelAdd}
            />
          )}

          {scoreCardCriteria.length === 0 && !isAddingCompetency && (
            <p className="text-slate-600 dark:text-slate-400 text-sm italic">No competencies defined yet.</p>
          )}
        </div>
      </div>

      {/* Between the Lines Criteria Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-blue-400" />
            <h3 className="text-lg font-medium text-slate-700 dark:text-slate-200">Between the Lines</h3>
          </div>

          <Button
            variant="link"
            size="sm"
            className="text-blue-400 hover:text-blue-300"
            onClick={() => handleAddClick(CriterionType.BETWEEN_THE_LINES)}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Criterion
          </Button>
        </div>

        <div className="space-y-3">
          {betweenTheLinesCriteria.map(renderCriterionItem)}

          {/* Inline form for adding Between the Lines criteria */}
          {isAddingBetweenTheLines && (
            <InlineCriterionForm
              type={CriterionType.BETWEEN_THE_LINES}
              onSave={(value) => handleSaveInlineCriterion(CriterionType.BETWEEN_THE_LINES, value)}
              onCancel={handleCancelAdd}
            />
          )}

          {betweenTheLinesCriteria.length === 0 && !isAddingBetweenTheLines && (
            <p className="text-slate-600 dark:text-slate-400 text-sm italic">No between the lines criteria defined yet.</p>
          )}
        </div>
      </div>

      {/* Disqualifiers Criteria Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <h3 className="text-lg font-medium text-slate-700 dark:text-slate-200">Disqualifiers</h3>
          </div>

          <Button
            variant="link"
            size="sm"
            className="text-red-400 hover:text-red-300"
            onClick={() => handleAddClick(CriterionType.DISQUALIFIER)}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Criterion
          </Button>
        </div>

        <div className="space-y-3">
          {disqualifierCriteria.map(renderCriterionItem)}

          {/* Inline form for adding Disqualifier criteria */}
          {isAddingDisqualifier && (
            <InlineCriterionForm
              type={CriterionType.DISQUALIFIER}
              onSave={(value) => handleSaveInlineCriterion(CriterionType.DISQUALIFIER, value)}
              onCancel={handleCancelAdd}
            />
          )}

          {disqualifierCriteria.length === 0 && !isAddingDisqualifier && (
            <p className="text-slate-600 dark:text-slate-400 text-sm italic">No disqualifiers defined yet.</p>
          )}
        </div>
      </div>

      {/* Empty State - Only show if no criteria of any type */}
      {criteria.length === 0 && !showAddForm && !isAddingBetweenTheLines && !isAddingDisqualifier && !isAddingCompetency && (
        <div className="text-center py-8 px-4 border border-dashed border-[#FFE5D9] dark:border-slate-700 rounded-md bg-white dark:bg-transparent">
          <p className="text-slate-600 dark:text-slate-400 mb-4">No evaluation criteria defined yet.</p>
          <div className="flex justify-center gap-4">
            <Button
              variant="secondary"
              size="sm"
              className="gap-2"
              onClick={() => handleAddClick(CriterionType.SCORECARD)}
            >
              <Award className="h-4 w-4" />
              Add Competency
            </Button>
            <Button
              variant="secondary"
              size="sm"
              className="gap-2"
              onClick={() => handleAddClick(CriterionType.BETWEEN_THE_LINES)}
            >
              <Eye className="h-4 w-4" />
              Add Between the Lines
            </Button>
            <Button
              variant="secondary"
              size="sm"
              className="gap-2"
              onClick={() => handleAddClick(CriterionType.DISQUALIFIER)}
            >
              <AlertTriangle className="h-4 w-4" />
              Add Disqualifier
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}