import { useState } from 'react';
import { Question } from '@/services/templates/api';
import { Button } from '@/components/ui/Button';
import { Edit, Trash2, ChevronDown, X, Award, <PERSON><PERSON><PERSON>, Users, User, MoreVertical } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/Badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropdownMenu';

// Mock data for top performers
const mockCandidates = [
  { id: 'c1', name: '<PERSON>', score: 95 },
  { id: 'c2', name: '<PERSON>', score: 92 },
  { id: 'c3', name: '<PERSON>', score: 88 },
  { id: 'c4', name: '<PERSON>', score: 85 },
  { id: 'c5', name: '<PERSON>', score: 82 },
];

interface QuestionItemProps {
  question: Question;
  onEdit: (question: Question) => void;
  onDelete: (questionId: string) => void;
  isEditMode?: boolean;
}

/**
 * QuestionItem component
 * Displays a question with options to edit or delete via dropdown menu
 */
export function QuestionItem({
  question,
  onEdit,
  onDelete,
  isEditMode = false
}: QuestionItemProps) {
  const [expanded, setExpanded] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const toggleExpanded = () => {
    // Don't toggle when in edit mode (to prevent accidental expansion during drag)
    if (isEditMode) return;
    setExpanded(!expanded);
  };

  const handleEdit = () => {
    onEdit(question);
  };

  const handleDelete = () => {
    setShowDeleteConfirm(true);
  };

  const confirmDelete = () => {
    onDelete(question.id);
    setShowDeleteConfirm(false);
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  // Default statistics if not available
  const statistics = question.statistics || {
    topScore: 0,
    topScoringCandidateId: '',
    averageScore: 0,
    totalAnswers: 0
  };

  // Get top performers (mock data for now)
  const getTopPerformers = () => {
    // In a real implementation, this would filter candidates based on their performance on this question
    return mockCandidates.slice(0, 3); // Return top 3 performers
  };

  const topPerformers = getTopPerformers();

  return (
    <div className={cn(
      "p-4 rounded-md border transition-all duration-300",
      isEditMode
        ? "bg-slate-800/70 border-slate-600 shadow-md dark:bg-slate-800/70 dark:border-slate-600 dark:shadow-md dark:hover:border-slate-600 dark:hover:shadow-md"
        : "bg-white border-[#FFE5D9] hover:border-[#FFCDB2] hover:shadow-md dark:bg-slate-800/60 dark:border-slate-700 dark:hover:border-slate-600 dark:hover:shadow-md"
    )}>
      {/* Single row with question and statistics */}
      <div className="flex items-center justify-between gap-4">
        {/* Left side: Question with expand/collapse button */}
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {!isEditMode && (
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleExpanded}
              className="h-6 w-6 p-0 flex-shrink-0 transition-transform duration-200"
              style={{ transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)' }}
            >
              <ChevronDown className="h-4 w-4 text-slate-500 dark:text-slate-400" />
            </Button>
          )}
          <h3 className={cn(
            "font-medium text-slate-800 dark:text-slate-200",
            !expanded && "line-clamp-2"
          )}>{question.question}</h3>
        </div>

        {/* Right side: Statistics badges and action buttons */}
        <div className="flex items-center gap-3 flex-shrink-0">
          {/* Statistics badges with improved styling */}
          <div className="flex items-center gap-3">
            {/* Total answers badge */}
            <div className="flex flex-col items-end">
              <div className="text-xs text-slate-500 dark:text-slate-400 mb-0.5">Answers</div>
              <div className="flex items-center gap-1.5">
                <Users className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                <span className="font-semibold text-blue-600 dark:text-blue-300">{statistics.totalAnswers}</span>
              </div>
            </div>

            {/* Top score badge */}
            <div className="flex flex-col items-end">
              <div className="text-xs text-slate-500 dark:text-slate-400 mb-0.5">Top Score</div>
              <div className="flex items-center gap-1.5">
                <Award className="h-3.5 w-3.5 text-amber-600 dark:text-amber-400" />
                <span className="font-semibold text-amber-600 dark:text-amber-300">{statistics.topScore}</span>
              </div>
            </div>

            {/* Average score badge - only show if there's data */}
            {statistics.averageScore > 0 && (
              <div className="flex flex-col items-end">
                <div className="text-xs text-slate-500 dark:text-slate-400 mb-0.5">Average</div>
                <div className="flex items-center gap-1.5">
                  <BarChart className="h-3.5 w-3.5 text-purple-600 dark:text-purple-400" />
                  <span className="font-semibold text-purple-600 dark:text-purple-300">{statistics.averageScore.toFixed(1)}</span>
                </div>
              </div>
            )}
          </div>

          {/* Three-dot menu for actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 p-0 border-0 text-slate-500 hover:text-slate-700 hover:bg-slate-200/50 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-700/50"
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit} className="cursor-pointer">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleDelete}
                className="cursor-pointer text-red-400 focus:text-red-400"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Expanded content with animation - Only when not in edit mode */}
      {!isEditMode && (
        <div
          className={cn(
            "overflow-hidden transition-all duration-300 ease-in-out",
            expanded ? "max-h-[1000px] opacity-100 mt-4" : "max-h-0 opacity-0"
          )}
        >
          <div className="space-y-4 pl-8 border-l border-[#FFE5D9] dark:border-slate-700/50 ml-3">
            {/* Full question text when expanded */}
            <div>
              <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">Question:</h4>
              <p className="text-sm text-slate-600 dark:text-slate-400 whitespace-pre-wrap">{question.question}</p>
            </div>

            {question.purpose && (
              <div>
                <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">Purpose:</h4>
                <p className="text-sm text-slate-600 dark:text-slate-400">{question.purpose}</p>
              </div>
            )}

            {question.idealAnswerCriteria && (
              <div>
                <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">Ideal Answer Criteria:</h4>
                <p className="text-sm text-slate-600 dark:text-slate-400">{question.idealAnswerCriteria}</p>
              </div>
            )}

            {/* Detailed statistics (only visible when expanded) - Updated to 3 columns */}
            <div>
              <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Statistics:</h4>
              <div className="grid grid-cols-3 gap-3">
                <div className="bg-white dark:bg-slate-800/70 p-3 rounded-md border border-[#FFE5D9] dark:border-slate-700 hover:border-blue-500/30 dark:hover:border-blue-500/30 transition-colors">
                  <div className="flex items-center gap-2 mb-1">
                    <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300">Total Answers</h5>
                  </div>
                  <p className="text-xl font-bold text-blue-600 dark:text-blue-400">{statistics.totalAnswers}</p>
                </div>
                <div className="bg-white dark:bg-slate-800/70 p-3 rounded-md border border-[#FFE5D9] dark:border-slate-700 hover:border-purple-500/30 dark:hover:border-purple-500/30 transition-colors">
                  <div className="flex items-center gap-2 mb-1">
                    <BarChart className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                    <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300">Average Score</h5>
                  </div>
                  <p className="text-xl font-bold text-purple-600 dark:text-purple-400">{statistics.averageScore.toFixed(1)}</p>
                </div>
                <div className="bg-white dark:bg-slate-800/70 p-3 rounded-md border border-[#FFE5D9] dark:border-slate-700 hover:border-amber-500/30 dark:hover:border-amber-500/30 transition-colors">
                  <div className="flex items-center gap-2 mb-1">
                    <Award className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                    <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300">Top Score</h5>
                  </div>
                  <p className="text-xl font-bold text-amber-600 dark:text-amber-400">{statistics.topScore}</p>
                </div>
              </div>
            </div>

            {/* Top Performers Section */}
            <div>
              <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Top Performers:</h4>
              {topPerformers.length > 0 ? (
                <div className="space-y-2">
                  {topPerformers.map((candidate) => (
                    <div
                      key={candidate.id}
                      className="flex items-center justify-between p-2 bg-white dark:bg-slate-800/70 rounded-md border border-[#FFE5D9] dark:border-slate-700 hover:border-[#FFCDB2] dark:hover:border-amber-500/30 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <div className="h-8 w-8 rounded-full bg-[#FFE5D9] dark:bg-slate-700 flex items-center justify-center">
                          <User className="h-4 w-4 text-slate-600 dark:text-slate-300" />
                        </div>
                        <span className="text-sm text-slate-700 dark:text-slate-300">{candidate.name}</span>
                      </div>
                      <Badge className="bg-amber-500/10 text-amber-400 ring-1 ring-amber-400/20">
                        Score: {candidate.score}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-slate-600 dark:text-slate-400">No performance data available yet.</p>
              )}
            </div>
          </div>
        </div>
      )}

      {showDeleteConfirm && (
        <div className="mt-4 p-4 bg-white dark:bg-slate-800/70 border border-[#FFE5D9] dark:border-slate-700 rounded-md">
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">Confirm Deletion</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={cancelDelete}
              className="h-6 w-6 p-0 text-slate-500 hover:text-slate-700 hover:bg-slate-200/50 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-700/50"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-sm text-slate-600 dark:text-slate-400 mb-3">
            Are you sure you want to delete this question? This action cannot be undone.
          </p>
          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={cancelDelete}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}