import { useState, useEffect, useRef } from 'react';
import { Question, QuestionCreate, QuestionUpdate } from '@/services/templates/api';
import { Button } from '@/components/ui/Button';
import { Plus, GripVertical, X, MessageSquare, Check, Sparkles } from 'lucide-react';
import { QuestionItem } from './QuestionItem';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Label } from '@/components/ui/Label';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface QuestionsListProps {
  questions: Question[];
  onAddQuestion: (question: QuestionCreate) => Promise<void>;
  onUpdateQuestion: (questionId: string, question: QuestionUpdate) => Promise<void>;
  onDeleteQuestion: (questionId: string) => Promise<void>;
  onReorderQuestions?: (questionIds: string[]) => Promise<void>;
  onGenerateQuestions?: () => Promise<void>;
  isLoading?: boolean;
  showActionButtons?: boolean;
  id?: string;
}

/**
 * QuestionsList component
 * Manages a list of questions with add, edit, and delete functionality
 * Includes drag-and-drop reordering with auto-save
 */
export function QuestionsList({
  questions,
  onAddQuestion,
  onUpdateQuestion,
  onDeleteQuestion,
  onReorderQuestions,
  onGenerateQuestions,
  isLoading = false,
  showActionButtons = true,
  id
}: QuestionsListProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [reorderLoading, setReorderLoading] = useState(false);
  const [reorderedQuestions, setReorderedQuestions] = useState<Question[]>([]);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dropTargetIndex, setDropTargetIndex] = useState<number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize reorderedQuestions when questions change
  useEffect(() => {
    if (questions && questions.length > 0) {
      setReorderedQuestions([...questions]);
    }
  }, [questions]);

  // Set up event listeners for external control
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !id) return;

    const handleAddQuestion = () => {
      handleAddClick();
    };

    container.addEventListener('add-question', handleAddQuestion);

    return () => {
      container.removeEventListener('add-question', handleAddQuestion);

      // Clear any pending save timeout on unmount
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [id]);

  const handleAddClick = () => {
    setShowAddForm(true);
    setEditingQuestion(null);
  };

  const handleCancelAdd = () => {
    setShowAddForm(false);
  };

  const handleSaveNewQuestion = async (questionData: QuestionCreate | QuestionUpdate) => {
    await onAddQuestion(questionData as QuestionCreate);
    setShowAddForm(false);
  };

  const handleEditQuestion = (question: Question) => {
    setEditingQuestion(question);
    setShowAddForm(false);
  };

  const handleCancelEdit = () => {
    setEditingQuestion(null);
  };

  const handleUpdateQuestion = async (questionData: QuestionUpdate) => {
    if (editingQuestion) {
      await onUpdateQuestion(editingQuestion.id, questionData);
      setEditingQuestion(null);
    }
  };

  const handleDeleteQuestion = async (questionId: string) => {
    await onDeleteQuestion(questionId);
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, index: number) => {
    // Set data transfer properties for drag operation
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', index.toString());

    // Add a class to style the dragged item
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.classList.add('dragging');

      // Add a ghost image for better drag visualization
      const ghostElement = e.currentTarget.cloneNode(true) as HTMLElement;
      ghostElement.style.position = 'absolute';
      ghostElement.style.top = '-1000px';
      ghostElement.style.opacity = '0.5';
      document.body.appendChild(ghostElement);
      e.dataTransfer.setDragImage(ghostElement, 20, 20);

      // Remove the ghost element after drag starts
      setTimeout(() => {
        document.body.removeChild(ghostElement);
      }, 0);
    }

    setDraggedIndex(index);
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    if (draggedIndex === null) return;

    // Set the drop target index for visual feedback
    setDropTargetIndex(index);

    // If dragging over the same item, do nothing
    if (draggedIndex === index) return;

    // Create a new array without the dragged item
    const newQuestions = [...reorderedQuestions];
    const draggedItem = newQuestions[draggedIndex];

    // Remove the item from its original position
    newQuestions.splice(draggedIndex, 1);

    // Insert the item at the new position
    newQuestions.splice(index, 0, draggedItem);

    // Update the dragged index to the new position
    setDraggedIndex(index);

    // Update the reordered questions
    setReorderedQuestions(newQuestions);
  };

  const handleDragEnd = (e: React.DragEvent) => {
    // Remove styling class
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.classList.remove('dragging');
    }

    setDraggedIndex(null);
    setDropTargetIndex(null);

    // Auto-save the new order with a small delay to prevent excessive API calls
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    saveTimeoutRef.current = setTimeout(() => {
      saveReorderedQuestions();
    }, 500);
  };

  // Add a drag leave handler to clear the drop target when dragging outside
  const handleDragLeave = (e: React.DragEvent) => {
    // Only clear if leaving the container, not just moving between items
    if (e.currentTarget.contains(e.relatedTarget as Node)) return;
    setDropTargetIndex(null);
  };

  // Save the reordered questions
  const saveReorderedQuestions = async () => {
    if (!onReorderQuestions || reorderLoading) return;

    try {
      setReorderLoading(true);

      // Extract just the IDs in the new order
      const newOrderIds = reorderedQuestions.map(q => q.id);

      // Call the reorder function
      await onReorderQuestions(newOrderIds);

      // Show a subtle success toast
      toast.success("Question order updated", {
        duration: 2000,
        position: "bottom-right"
      });
    } catch (error) {
      console.error('Error reordering questions:', error);
      toast.error("Failed to update question order");
    } finally {
      setReorderLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <LoadingSpinner className="h-8 w-8" />
      </div>
    );
  }

  return (
    <div
      className="space-y-6 p-4 rounded-lg bg-transparent dark:bg-transparent border-none dark:border-none"
      ref={containerRef}
      id={id}
      onDragLeave={handleDragLeave}
    >
      {/* Action Buttons - Only shown if showActionButtons is true */}
      {showActionButtons && (
        <div className="flex justify-between items-center mb-4">
          <div className="flex gap-3">
            {!showAddForm && !editingQuestion && (
              <>
                <button
                  onClick={handleAddClick}
                  className="flex items-center gap-2 px-3 py-1.5 rounded-md bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 border border-blue-500/20 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  <span className="font-medium">Add Question</span>
                </button>
                {onGenerateQuestions && (
                  <button
                    onClick={onGenerateQuestions}
                    className="flex items-center gap-2.5 px-4 py-2.5 rounded-md bg-purple-600/20 text-purple-300 hover:bg-purple-600/30 border border-purple-500/40 transition-all shadow-sm hover:shadow-purple-500/20 hover:shadow-md"
                    title="Generate questions with AI"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <LoadingSpinner className="h-5 w-5" />
                        <span className="font-medium">Generating...</span>
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-5 w-5 text-purple-200 animate-pulse" />
                        <span className="font-medium">Generate with AI</span>
                      </>
                    )}
                  </button>
                )}
              </>
            )}
          </div>
          {questions.length > 0 && (
            <div className="text-sm text-slate-400">
              {questions.length} question{questions.length !== 1 ? 's' : ''}
            </div>
          )}
        </div>
      )}

      {/* Add Question Form */}
      {showAddForm && (
        <div className="animate-in fade-in slide-in-from-top-4 duration-300">
          <InlineQuestionAddForm
            onSave={handleSaveNewQuestion}
            onCancel={handleCancelAdd}
          />
        </div>
      )}

      {/* Questions List */}
      <div className="space-y-5">
        {questions.length > 0 ? (
          reorderedQuestions.map((question, index) => (
            <div
              key={question.id}
              className={cn(
                "relative group animate-in fade-in duration-300",
                dropTargetIndex === index && draggedIndex !== index && "drop-target"
              )}
              draggable={!editingQuestion}
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={(e) => handleDragOver(e, index)}
              onDragEnd={handleDragEnd}
              style={{
                transition: 'transform 0.2s ease, opacity 0.2s ease, border-color 0.2s ease',
                opacity: draggedIndex === index ? 0.5 : 1,
                transform: draggedIndex === index ? 'scale(0.98)' : 'scale(1)',
                borderColor: dropTargetIndex === index && draggedIndex !== index ? 'rgb(59, 130, 246)' : '',
                boxShadow: dropTargetIndex === index && draggedIndex !== index ? '0 0 0 2px rgba(59, 130, 246, 0.3)' : ''
              }}
            >
              {/* Question number badge */}
              <div className="absolute -left-3 top-1/2 -translate-y-1/2 h-6 w-6 rounded-full bg-slate-200 border border-slate-300 dark:bg-slate-700 dark:border-slate-600 flex items-center justify-center text-xs font-medium text-slate-700 dark:text-slate-300 shadow-sm">
                {index + 1}
              </div>

              {/* Drag handle - only visible on hover */}
              <div className="absolute -left-10 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div className="h-8 w-8 flex items-center justify-center text-slate-500 dark:text-slate-400 cursor-grab active:cursor-grabbing">
                  <GripVertical className="h-5 w-5" />
                </div>
              </div>

              {editingQuestion && editingQuestion.id === question.id ? (
                <div className="animate-in fade-in slide-in-from-top-2 duration-200">
                  <InlineQuestionEditForm
                    question={question}
                    onSave={handleUpdateQuestion}
                    onCancel={handleCancelEdit}
                  />
                </div>
              ) : (
                <QuestionItem
                  question={question}
                  onEdit={handleEditQuestion}
                  onDelete={handleDeleteQuestion}
                />
              )}
            </div>
          ))
        ) :
          <div className="flex flex-col items-center justify-center py-10 px-4 text-center border border-dashed border-[#FFE5D9] dark:border-slate-700 rounded-lg bg-white dark:bg-transparent">
            <MessageSquare className="h-12 w-12 text-slate-400 dark:text-slate-600 mb-3" />
            <p className="text-slate-600 dark:text-slate-400 mb-4">No questions defined yet.</p>
            <button
              onClick={handleAddClick}
              className="flex items-center gap-2 px-3 py-1.5 rounded-md bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 border border-blue-500/20 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span className="font-medium">Add Your First Question</span>
            </button>
          </div>
        }
      </div>
    </div>
  );
}

/**
 * InlineQuestionEditForm component
 * A compact form for editing interview questions with a similar style to InlineCompetencyForm
 */
function InlineQuestionEditForm({
  question,
  onSave,
  onCancel
}: {
  question: Question;
  onSave: (questionData: QuestionUpdate) => Promise<void>;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<QuestionUpdate>({
    question: question.question || '',
    purpose: question.purpose || '',
    idealAnswerCriteria: question.idealAnswerCriteria || '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, boolean>>({});
  const questionInputRef = useRef<HTMLTextAreaElement>(null);

  // Focus the question input when the component mounts
  useEffect(() => {
    if (questionInputRef.current) {
      questionInputRef.current.focus();
    }
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, boolean> = {};

    if (!formData.question?.trim()) {
      newErrors.question = true;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error updating question:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-4 bg-white dark:bg-slate-800/70 border border-[#FFE5D9] dark:border-slate-700 rounded-md space-y-4 shadow-md">
      <div className="flex items-center gap-2">
        <MessageSquare className="h-4 w-4 text-blue-400 shrink-0" />
        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-200">Edit Question</h4>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={onCancel}
          className="h-7 w-7 p-0 ml-auto text-slate-500 hover:text-slate-700 hover:bg-slate-200/50 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-700/50"
        >
          <X className="h-3.5 w-3.5" />
        </Button>
      </div>

      <div className="space-y-4 animate-in fade-in duration-300">
        <div>
          <div className="flex items-center justify-between mb-1.5">
            <Label htmlFor="edit-question" className="text-xs font-medium text-slate-700 dark:text-slate-300">Question Text</Label>
            {errors.question && <span className="text-xs text-red-400">Required</span>}
          </div>
          <Textarea
            ref={questionInputRef}
            id="edit-question"
            name="question"
            value={formData.question}
            onChange={handleChange}
            placeholder="Enter the interview question..."
            className={cn(
              "min-h-[80px] bg-white border-[#FFE5D9] dark:bg-slate-800/70 dark:border-slate-700/80 focus:border-blue-500/50 focus:ring-blue-500/20 transition-colors",
              errors.question && "border-red-500/50 focus:border-red-500/50 focus:ring-red-500/20"
            )}
          />
        </div>

        <div>
          <Label htmlFor="edit-purpose" className="text-xs font-medium text-slate-700 dark:text-slate-300 block mb-1.5">Question Purpose</Label>
          <Input
            id="edit-purpose"
            name="purpose"
            value={formData.purpose}
            onChange={handleChange}
            placeholder="e.g. Assess technical knowledge of React"
            className="h-9 bg-white border-[#FFE5D9] dark:bg-slate-800/70 dark:border-slate-700/80 focus:border-blue-500/50 focus:ring-blue-500/20 transition-colors"
          />
        </div>

        <div>
          <Label htmlFor="edit-idealAnswerCriteria" className="text-xs font-medium text-slate-700 dark:text-slate-300 block mb-1.5">Ideal Answer Criteria</Label>
          <Textarea
            id="edit-idealAnswerCriteria"
            name="idealAnswerCriteria"
            value={formData.idealAnswerCriteria}
            onChange={handleChange}
            placeholder="Describe what makes an ideal answer to this question..."
            className="min-h-[80px] bg-white border-[#FFE5D9] dark:bg-slate-800/70 dark:border-slate-700/80 focus:border-blue-500/50 focus:ring-blue-500/20 transition-colors"
          />
        </div>
      </div>

      <div className="flex justify-end gap-2 pt-1">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
          disabled={isSubmitting}
          size="sm"
          className="ml-2"
        >
          Cancel
        </Button>
        <Button
          type="button"
          onClick={handleSubmit}
          disabled={isSubmitting}
          size="sm"
          className="bg-blue-600 hover:bg-blue-700 text-white gap-1.5"
        >
          {isSubmitting ? <LoadingSpinner className="h-3.5 w-3.5" /> : <Check className="h-3.5 w-3.5" />}
          Save Changes
        </Button>
      </div>
    </div>
  );
}

/**
 * InlineQuestionAddForm component
 * A compact form for adding new interview questions with a similar style to InlineCompetencyForm
 */
function InlineQuestionAddForm({
  onSave,
  onCancel
}: {
  onSave: (questionData: QuestionCreate) => Promise<void>;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<QuestionCreate>({
    question: '',
    purpose: '',
    idealAnswerCriteria: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, boolean>>({});
  const questionInputRef = useRef<HTMLTextAreaElement>(null);

  // Focus the question input when the component mounts
  useEffect(() => {
    if (questionInputRef.current) {
      questionInputRef.current.focus();
    }
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, boolean> = {};

    if (!formData.question?.trim()) {
      newErrors.question = true;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSave(formData);
      // Reset form for next entry
      setFormData({
        question: '',
        purpose: '',
        idealAnswerCriteria: '',
      });
      // Focus back on question field for quick entry of multiple items
      if (questionInputRef.current) {
        questionInputRef.current.focus();
      }
    } catch (error) {
      console.error('Error adding question:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-4 bg-white dark:bg-slate-800/70 border border-[#FFE5D9] dark:border-slate-700 rounded-md space-y-4 shadow-md">
      <div className="flex items-center gap-2">
        <MessageSquare className="h-4 w-4 text-green-400 shrink-0" />
        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-200">Add New Question</h4>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={onCancel}
          className="h-7 w-7 p-0 ml-auto text-slate-500 hover:text-slate-700 hover:bg-slate-200/50 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-700/50"
        >
          <X className="h-3.5 w-3.5" />
        </Button>
      </div>

      <div className="space-y-4 animate-in fade-in duration-300">
        <div>
          <div className="flex items-center justify-between mb-1.5">
            <Label htmlFor="add-question" className="text-xs font-medium text-slate-700 dark:text-slate-300">Question Text</Label>
            {errors.question && <span className="text-xs text-red-400">Required</span>}
          </div>
          <Textarea
            ref={questionInputRef}
            id="add-question"
            name="question"
            value={formData.question}
            onChange={handleChange}
            placeholder="Enter the interview question..."
            className={cn(
              "min-h-[80px] bg-white border-[#FFE5D9] dark:bg-slate-800/70 dark:border-slate-700/80 focus:border-green-500/50 focus:ring-green-500/20 transition-colors",
              errors.question && "border-red-500/50 focus:border-red-500/50 focus:ring-red-500/20"
            )}
          />
        </div>

        <div>
          <Label htmlFor="add-purpose" className="text-xs font-medium text-slate-700 dark:text-slate-300 block mb-1.5">Question Purpose</Label>
          <Input
            id="add-purpose"
            name="purpose"
            value={formData.purpose}
            onChange={handleChange}
            placeholder="e.g. Assess technical knowledge of React"
            className="h-9 bg-white border-[#FFE5D9] dark:bg-slate-800/70 dark:border-slate-700/80 focus:border-green-500/50 focus:ring-green-500/20 transition-colors"
          />
        </div>

        <div>
          <Label htmlFor="add-idealAnswerCriteria" className="text-xs font-medium text-slate-700 dark:text-slate-300 block mb-1.5">Ideal Answer Criteria</Label>
          <Textarea
            id="add-idealAnswerCriteria"
            name="idealAnswerCriteria"
            value={formData.idealAnswerCriteria}
            onChange={handleChange}
            placeholder="Describe what makes an ideal answer to this question..."
            className="min-h-[80px] bg-white border-[#FFE5D9] dark:bg-slate-800/70 dark:border-slate-700/80 focus:border-green-500/50 focus:ring-green-500/20 transition-colors"
          />
        </div>
      </div>

      <div className="flex justify-end gap-2 pt-1">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
          disabled={isSubmitting}
          size="sm"
          className="ml-2"
        >
          Cancel
        </Button>
        <Button
          type="button"
          onClick={handleSubmit}
          disabled={isSubmitting}
          size="sm"
          className="bg-green-600 hover:bg-green-700 text-white gap-1.5"
        >
          {isSubmitting ? <LoadingSpinner className="h-3.5 w-3.5" /> : <Plus className="h-3.5 w-3.5" />}
          Add Question
        </Button>
      </div>
    </div>
  );
}