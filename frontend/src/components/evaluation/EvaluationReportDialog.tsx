"use client";

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useEvaluation } from '@/hooks/useEvaluation';
import { cn } from '@/lib/utils/styles';
import {
  ThumbsUp,
  ThumbsDown,
  HelpCircle,
  ExternalLink} from 'lucide-react';
import Link from 'next/link';

import { ExtendedEvaluation } from '@/types/evaluation';

// Types for the evaluation report
interface EvaluationReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  evaluationId: string;
}

interface QuestionAssessment {
  question: string;
  answer: string;
  score: number;
  reasoning: string;
}

interface CriterionAssessment {
  name: string;
  score: number;
  weight: number;
  reasoning: string;
  weightedScore: number;
}

interface BetweenTheLinesObservation {
  aspect: string;
  observation: string;
}


/**
 * Dialog component to display interview evaluation results
 */
export function EvaluationReportDialog({
  open,
  onOpenChange,
  evaluationId
}: EvaluationReportDialogProps) {
  // Always force a refresh when opening the dialog to avoid caching issues
  const { evaluation, loading, error } = useEvaluation(evaluationId, false, 5000);

  // Log the evaluation ID and data for debugging
  useEffect(() => {
    console.log(`EvaluationReportDialog: evaluationId=${evaluationId}, loading=${loading}, hasError=${!!error}`);
    if (evaluation) {
      console.log(`Evaluation data loaded: id=${evaluation.id}, status=${evaluation.status}`);
    }
  }, [evaluationId, evaluation, loading, error]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col p-0">
        <DialogHeader className="px-6 py-4">
          <DialogTitle className="gradient-text text-2xl">Interview Evaluation Report</DialogTitle>
          <DialogDescription>
            Comprehensive AI-generated evaluation of the candidate&apos;s interview performance
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center py-12 px-6">
            <LoadingSpinner size="lg" />
            <p className="ml-4 text-slate-500">Loading evaluation...</p>
          </div>
        ) : error ? (
          <div className="bg-red-50 dark:bg-red-900/20 p-4 m-6 rounded-md text-red-800 dark:text-red-200">
            <p>Error loading evaluation: {error.message}</p>
          </div>
        ) : !evaluation ? (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 m-6 rounded-md text-yellow-800 dark:text-yellow-200">
            <p>Evaluation not found</p>
          </div>
        ) : (
          <ScrollArea className="flex-1 h-[calc(90vh-10rem)] overflow-y-auto overflow-x-hidden">
            <div className="pr-4">
              <div className="p-6 space-y-8">
              {/* Summary Section */}
              <EvaluationSummary evaluation={evaluation} />

              {/* Scorecard Section */}
              <EvaluationScorecard
                criteria={evaluation.data?.criteria?.map(c => ({
                  name: c?.name || 'Unnamed Criterion',
                  score: typeof c?.score === 'number' ? c.score : 0,
                  weight: 1 / (evaluation.data?.criteria?.length || 1),
                  reasoning: c?.feedback || 'No feedback provided',
                  weightedScore: (typeof c?.score === 'number' ? c.score : 0) * (1 / (evaluation.data?.criteria?.length || 1))
                })) || (evaluation.data as any)?.scorecard_evaluation || []}
              />

              {/* Questions Assessment */}
              <QuestionAssessments
                assessments={evaluation.data?.questionAssessments?.map(qa => ({
                  question: qa?.question || 'Unnamed Question',
                  answer: qa?.answer || 'No answer provided',
                  score: typeof qa?.score === 'number' ? qa.score : 0,
                  reasoning: qa?.feedback || 'No feedback provided'
                })) || (evaluation.data as any)?.question_analysis || []}
              />

              {/* Between the Lines */}
              {evaluation.data?.strengths?.length || evaluation.data?.weaknesses?.length || (evaluation.data as any)?.between_the_lines?.length ? (
                <BetweenTheLines
                  observations={[
                    ...(evaluation.data?.strengths?.filter(s => s)?.map(s => ({
                      aspect: 'Strength',
                      observation: s || 'No details provided'
                    })) || []),
                    ...(evaluation.data?.weaknesses?.filter(w => w)?.map(w => ({
                      aspect: 'Area for Improvement',
                      observation: w || 'No details provided'
                    })) || []),
                    ...((evaluation.data as any)?.between_the_lines?.map(btl => ({
                      aspect: btl.criteria || 'Observation',
                      observation: btl.observation || 'No details provided'
                    })) || [])
                  ]}
                />
              ) : null}

              {/* Final Decision */}
              <FinalDecision
                decision={evaluation.data?.decision || (evaluation.data as any)?.evaluation_summary?.decision || 'Maybe'}
                reasoning={evaluation.data?.summary || (evaluation.data as any)?.evaluation_summary?.summary || (evaluation.data as any)?.decision_reasoning?.final_recommendation || ''}
              />
              </div>
            </div>
          </ScrollArea>
        )}

        <DialogFooter className="px-6 py-4 border-t flex justify-between">
          <Link href={`/evaluation/${evaluationId}`} passHref>
            <Button variant="secondary" className="flex items-center gap-1">
              <ExternalLink className="h-4 w-4 mr-1" />
              Open Full Report
            </Button>
          </Link>
          <Button variant="secondary" onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

/**
 * Evaluation Summary component
 */
function EvaluationSummary({ evaluation }: { evaluation: ExtendedEvaluation }) {
  // Handle cases where evaluation data might be missing or in a different format
  const data = evaluation?.data || {} as any;
  const summary = data.evaluation_summary || {} as any;
  const decision = data.decision || summary.decision || 'No Decision';
  const score = typeof data.overallScore === 'number' ? data.overallScore :
                typeof summary.overall_score === 'number' ? summary.overall_score : 0;

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold gradient-text">
        Evaluation Summary
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-slate-100 dark:bg-slate-800/50 rounded-lg p-4">
          <h3 className="font-medium mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
            Overall Score
          </h3>
          <div className="flex items-center">
            <div className="w-16 h-16 rounded-full flex items-center justify-center text-xl font-bold bg-blue-500 text-white">
              {typeof score === 'number' ? score.toFixed(1) : '0.0'}
            </div>
            <div className="ml-4">
              <p className="text-sm text-slate-600 dark:text-slate-400">
                Out of maximum score 100
              </p>
              {typeof score === 'number' ? (
                score >= 4.5 ? (
                  <p className="text-green-600 dark:text-green-400 font-medium">Excellent</p>
                ) : score >= 3.5 ? (
                  <p className="text-blue-600 dark:text-blue-400 font-medium">Good</p>
                ) : score >= 2.5 ? (
                  <p className="text-yellow-600 dark:text-yellow-400 font-medium">Average</p>
                ) : (
                  <p className="text-red-600 dark:text-red-400 font-medium">Below Expectations</p>
                )
              ) : (
                <p className="text-gray-600 dark:text-gray-400 font-medium">Not Available</p>
              )}
            </div>
          </div>
        </div>

        <div className="bg-slate-100 dark:bg-slate-800/50 rounded-lg p-4">
          <h3 className="font-medium mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
            Final Decision
          </h3>
          <div className="flex items-center">
            <div
              className={cn(
                "w-auto h-10 px-4 rounded-full flex items-center justify-center font-medium",
                decision === 'Go' && "bg-green-500 text-white",
                decision === 'No Go' && "bg-red-500 text-white",
                decision === 'Maybe' && "bg-yellow-500 text-white"
              )}
            >
              {decision}
            </div>
            <div className="ml-4">
              <p className="text-sm text-slate-600 dark:text-slate-400">
                {decision === 'Go' ? 'Recommended to proceed' :
                 decision === 'No Go' ? 'Not recommended to proceed' :
                 'Consider further evaluation'}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-slate-100 dark:bg-slate-800/50 rounded-lg p-4">
        <h3 className="font-medium mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
          Evaluation Date
        </h3>
        <p className="text-slate-600 dark:text-slate-400">
          {evaluation.createdAt ? evaluation.createdAt.toLocaleString() : 'Not available'}
        </p>
      </div>
    </div>
  );
}

/**
 * Dynamic Scorecard component
 */
function EvaluationScorecard({ criteria }: { criteria: CriterionAssessment[] }) {
  if (!criteria || criteria.length === 0) return null;

  // Ensure all criteria have the required properties with proper types
  const validCriteria = criteria.map(criterion => ({
    name: criterion.name || 'Unnamed Criterion',
    score: typeof criterion.score === 'number' ? criterion.score : 0,
    weight: typeof criterion.weight === 'number' ? criterion.weight : 0,
    reasoning: criterion.reasoning || 'No reasoning provided',
    weightedScore: typeof criterion.weightedScore === 'number' ? criterion.weightedScore :
                  (typeof criterion.score === 'number' && typeof criterion.weight === 'number') ?
                  criterion.score * criterion.weight : 0
  }));

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold gradient-text">
        Competency Scorecard
      </h2>

      <div className="overflow-x-auto rounded-md border border-slate-200 dark:border-slate-700">
        <table className="w-full min-w-[600px] border-collapse">
          <thead>
            <tr className="bg-slate-200 dark:bg-slate-700">
              <th className="p-3 text-left text-slate-700 dark:text-slate-300 border border-slate-300 dark:border-slate-600">
                Competency
              </th>
              <th className="p-3 text-center text-slate-700 dark:text-slate-300 border border-slate-300 dark:border-slate-600 w-20">
                Score
              </th>
              <th className="p-3 text-center text-slate-700 dark:text-slate-300 border border-slate-300 dark:border-slate-600 w-20">
                Weight
              </th>
              <th className="p-3 text-center text-slate-700 dark:text-slate-300 border border-slate-300 dark:border-slate-600 w-28">
                Weighted Score
              </th>
            </tr>
          </thead>
          <tbody>
            {validCriteria.map((criterion, index) => (
              <tr
                key={index}
                className={index % 2 === 0 ? 'bg-white dark:bg-slate-800' : 'bg-slate-50 dark:bg-slate-800/50'}
              >
                <td className="p-3 border border-slate-300 dark:border-slate-600">
                  <p className="font-medium text-slate-900 dark:text-slate-100">
                    {criterion.name}
                  </p>
                  <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                    {criterion.reasoning}
                  </p>
                </td>
                <td className="p-3 text-center border border-slate-300 dark:border-slate-600">
                  <div className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 font-medium">
                    {typeof criterion.score === 'number' ? criterion.score.toFixed(1) : '0.0'}
                  </div>
                </td>
                <td className="p-3 text-center border border-slate-300 dark:border-slate-600">
                  {typeof criterion.weight === 'number' ? (criterion.weight * 100).toFixed(0) : '0'}%
                </td>
                <td className="p-3 text-center border border-slate-300 dark:border-slate-600">
                  {typeof criterion.weightedScore === 'number' ? criterion.weightedScore.toFixed(2) : '0.00'}
                </td>
              </tr>
            ))}
            <tr className="bg-slate-200 dark:bg-slate-700 font-medium">
              <td className="p-3 border border-slate-300 dark:border-slate-600">
                Total
              </td>
              <td className="p-3 text-center border border-slate-300 dark:border-slate-600">
                -
              </td>
              <td className="p-3 text-center border border-slate-300 dark:border-slate-600">
                100%
              </td>
              <td className="p-3 text-center border border-slate-300 dark:border-slate-600">
                {validCriteria.reduce((sum, c) => sum + (typeof c.weightedScore === 'number' ? c.weightedScore : 0), 0).toFixed(2)}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

/**
 * Question Assessments component
 */
function QuestionAssessments({ assessments }: { assessments: QuestionAssessment[] }) {
  if (!assessments || assessments.length === 0) return null;

  // Ensure all assessments have the required properties with proper types
  const validAssessments = assessments.map(assessment => ({
    question: assessment.question || 'Unnamed Question',
    answer: assessment.answer || 'No answer provided',
    score: typeof assessment.score === 'number' ? assessment.score : 0,
    reasoning: assessment.reasoning || 'No reasoning provided'
  }));

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold gradient-text">
        Question-by-Question Assessment
      </h2>

      <div className="space-y-4">
        {validAssessments.map((assessment, index) => (
          <div key={index} className="bg-slate-100 dark:bg-slate-800/50 rounded-lg p-4">
            <div className="mb-2">
              <h3 className="font-medium text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
                Q{index + 1}: {assessment.question}
              </h3>
            </div>

            <div className="bg-white dark:bg-slate-900/50 p-3 rounded border border-slate-300 dark:border-slate-700 mb-3">
              <p className="text-slate-700 dark:text-slate-300">
                <strong className="text-slate-600 dark:text-slate-400">Answer: </strong>
                {assessment.answer}
              </p>
            </div>

            <div>
              <p className="text-sm text-slate-700 dark:text-slate-300">
                <strong className="text-slate-600 dark:text-slate-400">Evaluation: </strong>
                {assessment.reasoning}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Between the Lines component for qualitative observations
 */
function BetweenTheLines({ observations }: { observations: BetweenTheLinesObservation[] }) {
  if (!observations || observations.length === 0) return null;

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold gradient-text">
        Between the Lines
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {observations.map((observation, index) => (
          <div key={index} className={cn(
            "bg-slate-100 dark:bg-slate-800/50 rounded-lg p-4",
            observation.aspect === 'Strength' && "border-l-4 border-green-500",
            observation.aspect === 'Area for Improvement' && "border-l-4 border-yellow-500"
          )}>
            <h3 className={cn(
              "font-medium mb-2",
              observation.aspect === 'Strength' ? "text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600 dark:from-green-400 dark:to-blue-400" :
              observation.aspect === 'Area for Improvement' ? "text-transparent bg-clip-text bg-gradient-to-r from-yellow-600 to-orange-600 dark:from-yellow-400 dark:to-orange-400" :
              "text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400"
            )}>
              {observation.aspect}
            </h3>
            <p className="text-slate-700 dark:text-slate-300">
              {observation.observation}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}


/**
 * Final Decision component
 */
function FinalDecision({
  decision,
  reasoning
}: {
  decision: 'Go' | 'No Go' | 'Maybe',
  reasoning: string
}) {
  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold gradient-text">
        Final Decision
      </h2>

      <div className={cn(
        "p-6 rounded-lg border",
        decision === 'Go' && "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800",
        decision === 'No Go' && "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800",
        decision === 'Maybe' && "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800"
      )}>
        <div className="flex items-center mb-4">
          {decision === 'Go' ? (
            <ThumbsUp className="h-8 w-8 text-green-500 mr-3" />
          ) : decision === 'No Go' ? (
            <ThumbsDown className="h-8 w-8 text-red-500 mr-3" />
          ) : (
            <HelpCircle className="h-8 w-8 text-yellow-500 mr-3" />
          )}

          <h3 className={cn(
            "text-xl font-bold",
            decision === 'Go' && "text-green-800 dark:text-green-200",
            decision === 'No Go' && "text-red-800 dark:text-red-200",
            decision === 'Maybe' && "text-yellow-800 dark:text-yellow-200"
          )}>
            {decision === 'Go' ? 'Recommended to Proceed' :
             decision === 'No Go' ? 'Not Recommended to Proceed' :
             'Further Evaluation Recommended'}
          </h3>
        </div>

        <div className={cn(
          "bg-white dark:bg-slate-900 p-4 rounded border",
          decision === 'Go' && "border-green-200 dark:border-green-800",
          decision === 'No Go' && "border-red-200 dark:border-red-800",
          decision === 'Maybe' && "border-yellow-200 dark:border-yellow-800"
        )}>
          <p className="text-slate-700 dark:text-slate-300">
            {reasoning}
          </p>
        </div>
      </div>
    </div>
  );
}

/**
 * Hook for showing the evaluation dialog
 */
export function useEvaluationDialog() {
  const [open, setOpen] = useState(false);
  const [evaluationId, setEvaluationId] = useState<string | null>(null);

  const showEvaluation = (id: string) => {
    setEvaluationId(id);
    setOpen(true);
  };

  const dialog = evaluationId ? (
    <EvaluationReportDialog
      open={open}
      onOpenChange={setOpen}
      evaluationId={evaluationId}
    />
  ) : null;

  return {
    showEvaluation,
    dialog
  };
}
