import React, { useState } from 'react';
import { cn } from '@/lib/utils/styles';
import {
  EvaluationSummary as EvaluationSummaryType,
  QuestionAnalysis,
  ScorecardItem,
  BetweenTheLinesItem,
  DisqualifierCheck,
  DecisionReasoning
} from '@/services/evaluation/types';
import {
  ThumbsUp,
  ThumbsDown,
  HelpCircle,
  CheckCircle,
  XCircle,
  AlertTriangle,
  MessageSquare,
  Lightbulb,
  TrendingUp,
  TrendingDown,
  ChevronDown
} from 'lucide-react';
import { Badge } from '@/components/ui/Badge';

/**
 * Summary Section Component
 */
export function SummarySection({ summary }: { summary: EvaluationSummaryType }) {
  if (!summary) return null;

  const score = summary.overall_score || 0;
  const decision = summary.decision || 'No Decision';

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold gradient-text">
            {summary.candidate_name ? (
              <>Evaluation for <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 dark:from-blue-400 dark:via-purple-400 dark:to-pink-400">{summary.candidate_name}</span></>
            ) : (
              'Candidate Evaluation'
            )}
          </h1>
          {summary.role && (
            <p className="text-slate-600 dark:text-slate-400 mt-1">
              Role: {summary.role}
            </p>
          )}
        </div>

        <div className="flex items-center gap-3">
          <div className={cn(
            "px-4 py-2 rounded-full font-medium flex items-center",
            decision === 'Go' && "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
            decision === 'No Go' && "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
            decision === 'Maybe' && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
          )}>
            {decision === 'Go' ? (
              <ThumbsUp className="h-4 w-4 mr-2" />
            ) : decision === 'No Go' ? (
              <ThumbsDown className="h-4 w-4 mr-2" />
            ) : (
              <HelpCircle className="h-4 w-4 mr-2" />
            )}
            {decision}
          </div>

          <div className="flex items-center gap-3">
            {/* Score circle visualization */}
            <div className="relative">
              <div className="w-14 h-14 rounded-full border-4 border-slate-200 dark:border-slate-700 flex items-center justify-center relative">
                {/* Score value */}
                <span className="text-base font-bold text-slate-800 dark:text-slate-200">
                  {score}
                </span>

                {/* Score fill background */}
                <div className="absolute inset-0 rounded-full overflow-hidden">
                  <div
                    className={cn(
                      "absolute bottom-0 left-0 right-0",
                      decision === 'Go' || decision === 'PASS' ?
                      "bg-green-500" :
                      decision === 'No Go' || decision === 'FAIL' ?
                      "bg-red-500" :
                      "bg-blue-500"
                    )}
                    style={{
                      height: `${Math.min(100, Math.max(0, (parseFloat(String(score)) / 100) * 100))}%`,
                      opacity: '0.3'
                    }}
                  ></div>
                </div>
              </div>

              {/* Score label */}
              <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
                <span className="text-xs text-slate-500 bg-white dark:bg-slate-800 px-1 rounded-sm">
                  /100
                </span>
              </div>
            </div>
          </div>

          {summary.confidence && (
            <Badge variant="outline" className="bg-slate-100 dark:bg-slate-800">
              {summary.confidence} Confidence
            </Badge>
          )}
        </div>
      </div>

      {summary.summary && (
        <div className="bg-slate-100 dark:bg-slate-800/50 rounded-lg p-4 border border-slate-200 dark:border-slate-700">
          <h3 className="font-medium mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
            Executive Summary
          </h3>
          <p className="text-slate-600 dark:text-slate-400">
            {summary.summary}
          </p>
        </div>
      )}

      {summary.minimum_pass_rate && (
        <div className="text-sm text-slate-500 dark:text-slate-400">
          Minimum pass rate: {summary.minimum_pass_rate}%
        </div>
      )}
    </div>
  );
}

/**
 * Scorecard Section Component
 */
export function ScorecardSection({ items }: { items: ScorecardItem[] }) {
  if (!items || items.length === 0) return null;

  const totalScore = items.reduce((sum, item) => sum + item.weighted_score, 0);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold gradient-text">
          Competency Scorecard
        </h2>
        <div className="flex items-center gap-2">
          {/* Score circle visualization */}
          <div className="relative">
            <div className="w-10 h-10 rounded-full border-3 border-slate-200 dark:border-slate-700 flex items-center justify-center relative">
              {/* Score value */}
              <span className="text-xs font-bold text-slate-800 dark:text-slate-200">
                {totalScore.toFixed(1)}
              </span>

              {/* Score fill background */}
              <div className="absolute inset-0 rounded-full overflow-hidden">
                <div
                  className="absolute bottom-0 left-0 right-0 bg-blue-500"
                  style={{
                    height: `${Math.min(100, Math.max(0, (parseFloat(String(totalScore)) / 10) * 100))}%`,
                    opacity: '0.3'
                  }}
                ></div>
              </div>
            </div>
          </div>

          <span className="text-sm font-medium text-blue-800 dark:text-blue-300">
            Total Score
          </span>
        </div>
      </div>

      <div className="overflow-hidden bg-white dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700">
        <div className="grid grid-cols-12 bg-slate-100 dark:bg-slate-800 p-3">
          <div className="col-span-5 font-medium text-slate-700 dark:text-slate-300">Competency</div>
          <div className="col-span-2 font-medium text-slate-700 dark:text-slate-300 text-center">Score</div>
          <div className="col-span-2 font-medium text-slate-700 dark:text-slate-300 text-center">Weight</div>
          <div className="col-span-3 font-medium text-slate-700 dark:text-slate-300 text-center">Weighted</div>
        </div>

        {items.map((item, index) => (
          <div key={index} className={cn(
            "grid grid-cols-12 p-3 border-t border-slate-200 dark:border-slate-700",
            index % 2 === 0 ? "bg-white dark:bg-slate-900" : "bg-slate-50 dark:bg-slate-800/50"
          )}>
            <div className="col-span-5 font-medium text-slate-800 dark:text-slate-200">
              {item.competency}
            </div>
            <div className="col-span-2 text-center">
              <span className={cn(
                "inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium",
                item.score >= 8 ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" :
                item.score >= 6 ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300" :
                "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
              )}>
                {item.score}
              </span>
            </div>
            <div className="col-span-2 text-center text-slate-600 dark:text-slate-400">
              {(item.weight * 100).toFixed(0)}%
            </div>
            <div className="col-span-3 text-center font-medium text-slate-700 dark:text-slate-300">
              {item.weighted_score.toFixed(2)}
            </div>
          </div>
        ))}
      </div>

      <div className="space-y-4 mt-4">
        {items.map((item, index) => (
          <div key={index} className="bg-white dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700 p-4">
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-medium text-slate-800 dark:text-slate-200">
                {item.competency}
              </h3>
              <span className={cn(
                "inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium",
                item.score >= 8 ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300" :
                item.score >= 6 ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300" :
                "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
              )}>
                {item.score}
              </span>
            </div>
            <p className="text-slate-600 dark:text-slate-400">
              {item.reasoning}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Question Analysis Section Component
 */
export function QuestionAnalysisSection({ items }: { items: QuestionAnalysis[] }) {
  if (!items || items.length === 0) return null;

  // State to track which questions are expanded - initialize all to expanded
  const [expandedQuestions, setExpandedQuestions] = useState<{[key: number]: boolean}>(
    items.reduce((acc, _, index) => ({ ...acc, [index]: true }), {})
  );

  // Toggle question expansion
  const toggleQuestion = (index: number) => {
    setExpandedQuestions(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  // Toggle all questions
  const toggleAllQuestions = (expand: boolean) => {
    const newState = items.reduce((acc, _, index) => ({ ...acc, [index]: expand }), {});
    setExpandedQuestions(newState);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold gradient-text">
          Question-by-Question Analysis
        </h2>
        <div className="flex gap-2">
          <button
            onClick={() => toggleAllQuestions(true)}
            className="text-xs px-2 py-1 bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 rounded text-slate-600 dark:text-slate-300 transition-colors"
          >
            Expand All
          </button>
          <button
            onClick={() => toggleAllQuestions(false)}
            className="text-xs px-2 py-1 bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 rounded text-slate-600 dark:text-slate-300 transition-colors"
          >
            Collapse All
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {items.map((item, index) => {
          const isExpanded = expandedQuestions[index] === true;

          return (
          <div key={index} className="bg-white dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700 overflow-hidden transition-all duration-300">
            <div
              className="bg-slate-100 dark:bg-slate-800 p-4 cursor-pointer flex justify-between items-center"
              onClick={() => toggleQuestion(index)}
            >
              <h3 className="font-medium text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
                Q{index + 1}: {item.question}
              </h3>
              <button
                className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 transition-transform duration-300"
                style={{ transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)' }}
              >
                <ChevronDown className="h-5 w-5" />
              </button>
            </div>

            {isExpanded && (
              <>
                <div className="p-4 border-t border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900">
                  <h4 className="text-sm font-medium text-slate-500 dark:text-slate-400 mb-2">
                    Candidate's Answer:
                  </h4>
                  <p className="text-slate-700 dark:text-slate-300 bg-slate-50 dark:bg-slate-800/50 p-3 rounded-md border border-slate-200 dark:border-slate-700 italic">
                    {item.answer}
                  </p>
                </div>

                <div className="p-4 border-t border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900">
                  <h4 className="text-sm font-medium text-slate-500 dark:text-slate-400 mb-2">
                    Evaluation:
                  </h4>
                  <p className="text-slate-700 dark:text-slate-300">
                    {item.evaluation}
                  </p>

                  {/* Related competencies */}
                  {item.related_competencies && item.related_competencies.length > 0 && (
                    <div className="mt-3">
                      <h4 className="text-sm font-medium text-slate-500 dark:text-slate-400 mb-1">
                        Related Competencies:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {item.related_competencies.map((comp, i) => (
                          <Badge key={i} variant="outline" className="bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800">
                            {comp}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Strengths and weaknesses */}
                  <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-3">
                    {item.strengths && item.strengths.length > 0 && (
                      <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md border border-green-200 dark:border-green-800">
                        <h4 className="text-sm font-medium text-green-800 dark:text-green-300 flex items-center mb-2">
                          <TrendingUp className="h-4 w-4 mr-1" />
                          Strengths:
                        </h4>
                        <ul className="text-green-700 dark:text-green-300 text-sm space-y-1">
                          {item.strengths.map((strength, i) => (
                            <li key={i} className="flex items-start">
                              <CheckCircle className="h-4 w-4 mr-1 flex-shrink-0 mt-0.5" />
                              <span>{strength}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {item.weaknesses && item.weaknesses.length > 0 && (
                      <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-800">
                        <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-300 flex items-center mb-2">
                          <TrendingDown className="h-4 w-4 mr-1" />
                          Areas for Improvement:
                        </h4>
                        <ul className="text-yellow-700 dark:text-yellow-300 text-sm space-y-1">
                          {item.weaknesses.map((weakness, i) => (
                            <li key={i} className="flex items-start">
                              <AlertTriangle className="h-4 w-4 mr-1 flex-shrink-0 mt-0.5" />
                              <span>{weakness}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        )})}
      </div>
    </div>
  );
}

/**
 * Between the Lines Section Component
 */
export function BetweenTheLinesSection({ items }: { items: BetweenTheLinesItem[] }) {
  if (!items || items.length === 0) return null;

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold gradient-text flex items-center">
        <Lightbulb className="h-5 w-5 mr-2 text-amber-500" />
        Between the Lines
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {items.map((item, index) => (
          <div key={index} className="bg-white dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700 p-4">
            <h3 className="font-medium mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
              {item.criteria}
            </h3>

            <div className="bg-slate-50 dark:bg-slate-800/50 p-3 rounded-md border border-slate-200 dark:border-slate-700 mb-3">
              <p className="text-slate-700 dark:text-slate-300 text-sm">
                <span className="font-medium text-slate-500 dark:text-slate-400">Observation: </span>
                {item.observation}
              </p>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md border border-blue-200 dark:border-blue-800">
              <p className="text-blue-700 dark:text-blue-300 text-sm">
                <span className="font-medium text-blue-600 dark:text-blue-400">Impact: </span>
                {item.impact}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Disqualifier Check Section Component
 */
export function DisqualifierSection({ items }: { items: DisqualifierCheck[] }) {
  if (!items || items.length === 0) return null;

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold gradient-text flex items-center">
        <AlertTriangle className="h-5 w-5 mr-2 text-amber-500" />
        Disqualifier Check
      </h2>

      <div className="space-y-4">
        {items.map((item, index) => (
          <div key={index} className={cn(
            "bg-white dark:bg-slate-900 rounded-lg border p-4",
            item.triggered
              ? "border-red-300 dark:border-red-700"
              : "border-green-300 dark:border-green-700"
          )}>
            <div className="flex items-start">
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mr-3",
                item.triggered
                  ? "bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400"
                  : "bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400"
              )}>
                {item.triggered ? (
                  <XCircle className="h-5 w-5" />
                ) : (
                  <CheckCircle className="h-5 w-5" />
                )}
              </div>

              <div>
                <h3 className={cn(
                  "font-medium text-transparent bg-clip-text bg-gradient-to-r",
                  item.triggered
                    ? "from-red-600 to-orange-600 dark:from-red-400 dark:to-orange-400"
                    : "from-green-600 to-blue-600 dark:from-green-400 dark:to-blue-400"
                )}>
                  {item.criteria}
                </h3>

                <div className="mt-2 space-y-2">
                  <div className="bg-slate-50 dark:bg-slate-800/50 p-3 rounded-md border border-slate-200 dark:border-slate-700">
                    <p className="text-slate-700 dark:text-slate-300 text-sm">
                      <span className="font-medium text-slate-500 dark:text-slate-400">Evidence: </span>
                      {item.evidence}
                    </p>
                  </div>

                  <div className="bg-slate-50 dark:bg-slate-800/50 p-3 rounded-md border border-slate-200 dark:border-slate-700">
                    <p className="text-slate-700 dark:text-slate-300 text-sm">
                      <span className="font-medium text-slate-500 dark:text-slate-400">Explanation: </span>
                      {item.explanation}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Decision Reasoning Section Component
 */
export function DecisionReasoningSection({ data }: { data: DecisionReasoning }) {
  if (!data) return null;

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold gradient-text flex items-center">
        <MessageSquare className="h-5 w-5 mr-2 text-blue-500" />
        Decision Reasoning
      </h2>

      {data.final_recommendation && (
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <h3 className="font-medium mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
            Final Recommendation
          </h3>
          <p className="text-blue-700 dark:text-blue-300">
            {data.final_recommendation}
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {data.key_factors && data.key_factors.length > 0 && (
          <div className="bg-white dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700 p-4">
            <h3 className="font-medium mb-3 text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
              Key Factors
            </h3>
            <ul className="space-y-2">
              {data.key_factors.map((factor, index) => (
                <li key={index} className="flex items-start">
                  <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 w-6 h-6 rounded-full flex items-center justify-center mr-2 flex-shrink-0 mt-0.5">
                    {index + 1}
                  </div>
                  <span className="text-slate-700 dark:text-slate-300">{factor}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {data.strengths && data.strengths.length > 0 && (
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800 p-4">
            <h3 className="font-medium mb-3 flex items-center text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600 dark:from-green-400 dark:to-blue-400">
              <TrendingUp className="h-4 w-4 mr-1 text-green-600 dark:text-green-400" />
              Strengths
            </h3>
            <ul className="space-y-2">
              {data.strengths.map((strength, index) => (
                <li key={index} className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400 mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-green-700 dark:text-green-300">{strength}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {data.concerns && data.concerns.length > 0 && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800 p-4">
          <h3 className="font-medium mb-3 flex items-center text-transparent bg-clip-text bg-gradient-to-r from-yellow-600 to-orange-600 dark:from-yellow-400 dark:to-orange-400">
            <AlertTriangle className="h-4 w-4 mr-1 text-yellow-600 dark:text-yellow-400" />
            Areas of Concern
          </h3>
          <ul className="space-y-2">
            {data.concerns.map((concern, index) => (
              <li key={index} className="flex items-start">
                <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                <span className="text-yellow-700 dark:text-yellow-300">{concern}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
