import React, { useRef } from 'react';

type CompetitorNode = {
  name: string;
  type: 'direct' | 'adjacent' | 'partial' | 'emerging' | 'center';
  x: number;
  y: number;
};

export const CompetitiveEcosystem: React.FC = () => {
  const canvasRef = useRef<HTMLDivElement>(null);

  // Define the competitors
  const competitors: CompetitorNode[] = [
    // Center - Recruiva
    { name: '<PERSON><PERSON>ru<PERSON>', type: 'center', x: 50, y: 50 },
    
    // Direct Competitors
    { name: 'Workable', type: 'direct', x: 30, y: 30 },
    { name: 'HireVue', type: 'direct', x: 25, y: 35 },
    { name: 'Eightfold AI', type: 'direct', x: 20, y: 25 },
    { name: 'Manatal', type: 'direct', x: 15, y: 30 },
    
    // Adjacent Competitors
    { name: 'Greenhouse', type: 'adjacent', x: 70, y: 30 },
    { name: 'Lever', type: 'adjacent', x: 75, y: 25 },
    { name: 'SmartRecruiters', type: 'adjacent', x: 80, y: 35 },
    { name: 'Workday', type: 'adjacent', x: 85, y: 30 },
    { name: 'LinkedIn Talent', type: 'adjacent', x: 65, y: 25 },
    
    // Partial Competitors
    { name: 'SeekOut', type: 'partial', x: 30, y: 70 },
    { name: 'Beamery', type: 'partial', x: 25, y: 75 },
    { name: 'TestGorilla', type: 'partial', x: 35, y: 75 },
    
    // Emerging Threats
    { name: 'Mercor', type: 'emerging', x: 70, y: 70 },
    { name: 'Sapia.ai', type: 'emerging', x: 75, y: 75 },
    { name: 'JobCannon', type: 'emerging', x: 65, y: 75 },
    { name: 'Adway', type: 'emerging', x: 80, y: 70 },
  ];

  // Get color based on competitor type
  const getNodeColor = (type: string) => {
    switch (type) {
      case 'center':
        return 'bg-gradient-to-r from-purple-600 via-blue-500 to-pink-500';
      case 'direct':
        return 'bg-blue-500';
      case 'adjacent':
        return 'bg-green-500';
      case 'partial':
        return 'bg-yellow-500';
      case 'emerging':
        return 'bg-pink-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Get size based on competitor type
  const getNodeSize = (type: string) => {
    return type === 'center' ? 'w-16 h-16' : 'w-10 h-10';
  };

  // Get label color based on competitor type
  const getLabelColor = (type: string) => {
    switch (type) {
      case 'center':
        return 'text-purple-600';
      case 'direct':
        return 'text-blue-500';
      case 'adjacent':
        return 'text-green-500';
      case 'partial':
        return 'text-yellow-500';
      case 'emerging':
        return 'text-pink-500';
      default:
        return 'text-gray-500';
    }
  };

  // Get category label based on position

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div ref={canvasRef} className="relative h-[600px] bg-gray-50 rounded-xl overflow-hidden">
        {/* Category labels */}
        <div className="absolute top-4 left-4 font-semibold text-blue-500">Direct Competitors</div>
        <div className="absolute top-4 right-4 font-semibold text-green-500">Adjacent Competitors</div>
        <div className="absolute bottom-4 left-4 font-semibold text-yellow-500">Partial Competitors</div>
        <div className="absolute bottom-4 right-4 font-semibold text-pink-500">Emerging Threats</div>
        
        {/* Connecting lines */}
        {competitors.filter(c => c.type !== 'center').map((competitor, index) => {
          const centerNode = competitors.find(c => c.type === 'center')!;
          const angle = Math.atan2(competitor.y - centerNode.y, competitor.x - centerNode.x);
          const length = Math.sqrt(Math.pow(competitor.x - centerNode.x, 2) + Math.pow(competitor.y - centerNode.y, 2));
          
          return (
            <div
              key={`line-${index}`}
              className="absolute bg-gray-300"
              style={{
                left: `${centerNode.x}%`,
                top: `${centerNode.y}%`,
                width: `${length}%`,
                height: '1px',
                transform: `rotate(${angle}rad)`,
                transformOrigin: 'left center',
                opacity: 0.5,
              }}
            ></div>
          );
        })}
        
        {/* Competitor nodes */}
        {competitors.map((competitor, index) => (
          <div
            key={index}
            className={`absolute ${getNodeSize(competitor.type)} rounded-full ${getNodeColor(competitor.type)} flex items-center justify-center text-white font-bold shadow-lg transform -translate-x-1/2 -translate-y-1/2 transition-all duration-300 hover:scale-110 z-10`}
            style={{
              left: `${competitor.x}%`,
              top: `${competitor.y}%`,
            }}
          >
            {competitor.type === 'center' && (
              <span className="text-xs">R</span>
            )}
          </div>
        ))}
        
        {/* Node labels */}
        {competitors.map((competitor, index) => (
          <div
            key={`label-${index}`}
            className={`absolute text-xs font-semibold ${getLabelColor(competitor.type)} transform -translate-x-1/2 transition-all duration-300`}
            style={{
              left: `${competitor.x}%`,
              top: `${competitor.y + (competitor.type === 'center' ? 10 : 7)}%`,
              zIndex: 20,
            }}
          >
            {competitor.name}
          </div>
        ))}
        
        {/* Pulse animation for Recruiva */}
        <div
          className="absolute rounded-full animate-ping"
          style={{
            left: '50%',
            top: '50%',
            width: '60px',
            height: '60px',
            transform: 'translate(-50%, -50%)',
            background: 'radial-gradient(circle, rgba(136,132,216,0.6) 0%, rgba(236,95,227,0) 70%)',
          }}
        ></div>
      </div>
      
      <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center mb-2">
            <div className="w-4 h-4 rounded-full bg-blue-500 mr-2"></div>
            <h3 className="font-semibold text-gray-800">Direct Competitors</h3>
          </div>
          <p className="text-sm text-gray-600">Companies offering similar recruitment automation solutions</p>
        </div>
        
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center mb-2">
            <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
            <h3 className="font-semibold text-gray-800">Adjacent Competitors</h3>
          </div>
          <p className="text-sm text-gray-600">ATS and HR platforms with expanding AI capabilities</p>
        </div>
        
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center mb-2">
            <div className="w-4 h-4 rounded-full bg-yellow-500 mr-2"></div>
            <h3 className="font-semibold text-gray-800">Partial Competitors</h3>
          </div>
          <p className="text-sm text-gray-600">Specialized tools addressing parts of the recruitment process</p>
        </div>
        
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center mb-2">
            <div className="w-4 h-4 rounded-full bg-pink-500 mr-2"></div>
            <h3 className="font-semibold text-gray-800">Emerging Threats</h3>
          </div>
          <p className="text-sm text-gray-600">New entrants with innovative approaches to recruitment</p>
        </div>
      </div>
    </div>
  );
};
