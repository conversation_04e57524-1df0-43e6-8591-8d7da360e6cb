import React from 'react';
import { Globe, MapPin } from 'lucide-react';

type RegionData = {
  name: string;
  recruiva: number;
  workable: number;
  hirevue: number;
  eightfoldAi: number;
  manatal: number;
  description: string;
  keyCompetitors: string[];
  opportunity: string;
  challenge: string;
};

export const GlobalMarketPresence: React.FC = () => {
  const regions: RegionData[] = [
    {
      name: 'North America',
      recruiva: 70,
      workable: 65,
      hirevue: 90,
      eightfoldAi: 85,
      manatal: 30,
      description: 'Largest and most mature market with highest adoption of AI recruitment tools',
      keyCompetitors: ['HireVue', 'Eightfold AI', 'Greenhouse'],
      opportunity: 'Mid-market companies seeking enterprise capabilities without complexity',
      challenge: 'Highly competitive landscape with established players',
    },
    {
      name: 'Europe',
      recruiva: 40,
      workable: 80,
      hirevue: 70,
      eightfoldAi: 60,
      manatal: 45,
      description: 'Strong focus on compliance and data privacy with GDPR considerations',
      keyCompetitors: ['Workable', 'SmartRecruiters', 'Personio'],
      opportunity: 'Growing demand for GDPR-compliant AI recruitment solutions',
      challenge: 'Complex regulatory landscape across different countries',
    },
    {
      name: 'Asia-Pacific',
      recruiva: 25,
      workable: 50,
      hirevue: 40,
      eightfoldAi: 65,
      manatal: 75,
      description: 'Rapidly growing market with increasing adoption of HR technology',
      keyCompetitors: ['Manatal', 'Eightfold AI', 'Glints'],
      opportunity: 'Large talent pools and growing tech hubs in Singapore, India, and Australia',
      challenge: 'Price sensitivity and diverse language requirements',
    },
    {
      name: 'Latin America',
      recruiva: 15,
      workable: 35,
      hirevue: 30,
      eightfoldAi: 20,
      manatal: 40,
      description: 'Emerging market with growing interest in recruitment automation',
      keyCompetitors: ['Manatal', 'Workable', 'Local ATS providers'],
      opportunity: 'Less saturated market with fewer established competitors',
      challenge: 'Economic volatility and language localization needs',
    },
    {
      name: 'Middle East & Africa',
      recruiva: 10,
      workable: 25,
      hirevue: 35,
      eightfoldAi: 30,
      manatal: 35,
      description: 'Growing tech hubs in UAE, Israel, and South Africa driving adoption',
      keyCompetitors: ['HireVue', 'Workable', 'Regional players'],
      opportunity: 'Rapid digitalization initiatives in Gulf countries',
      challenge: 'Varied market maturity and infrastructure limitations in some areas',
    },
  ];

  const getPresenceColor = (value: number) => {
    if (value >= 80) return 'bg-green-500';
    if (value >= 60) return 'bg-green-400';
    if (value >= 40) return 'bg-yellow-500';
    if (value >= 20) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getPresenceLabel = (value: number) => {
    if (value >= 80) return 'Strong';
    if (value >= 60) return 'Established';
    if (value >= 40) return 'Growing';
    if (value >= 20) return 'Limited';
    return 'Minimal';
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div className="lg:col-span-3 bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center mb-6">
          <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
            <Globe className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-800">Global Market Presence</h3>
            <p className="text-gray-600">Competitive landscape across key global regions</p>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Region</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Recruiva</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Workable</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">HireVue</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Eightfold AI</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Manatal</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {regions.map((region, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <MapPin className="w-5 h-5 text-gray-400 mr-2" />
                      <span className="font-medium text-gray-900">{region.name}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <div className="flex flex-col items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                        <div className={`h-2.5 rounded-full ${getPresenceColor(region.recruiva)}`} style={{ width: `${region.recruiva}%` }}></div>
                      </div>
                      <span className="text-xs font-medium text-gray-500">{getPresenceLabel(region.recruiva)}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <div className="flex flex-col items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                        <div className={`h-2.5 rounded-full ${getPresenceColor(region.workable)}`} style={{ width: `${region.workable}%` }}></div>
                      </div>
                      <span className="text-xs font-medium text-gray-500">{getPresenceLabel(region.workable)}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <div className="flex flex-col items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                        <div className={`h-2.5 rounded-full ${getPresenceColor(region.hirevue)}`} style={{ width: `${region.hirevue}%` }}></div>
                      </div>
                      <span className="text-xs font-medium text-gray-500">{getPresenceLabel(region.hirevue)}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <div className="flex flex-col items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                        <div className={`h-2.5 rounded-full ${getPresenceColor(region.eightfoldAi)}`} style={{ width: `${region.eightfoldAi}%` }}></div>
                      </div>
                      <span className="text-xs font-medium text-gray-500">{getPresenceLabel(region.eightfoldAi)}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <div className="flex flex-col items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                        <div className={`h-2.5 rounded-full ${getPresenceColor(region.manatal)}`} style={{ width: `${region.manatal}%` }}></div>
                      </div>
                      <span className="text-xs font-medium text-gray-500">{getPresenceLabel(region.manatal)}</span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      {regions.map((region, index) => (
        <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className={`h-2 ${getPresenceColor(region.recruiva)}`}></div>
          <div className="p-6">
            <h3 className="text-lg font-bold text-gray-800 mb-2">{region.name}</h3>
            <p className="text-gray-600 mb-4">{region.description}</p>
            
            <div className="mb-4">
              <h4 className="text-sm font-semibold text-gray-500 mb-2">Key Competitors</h4>
              <div className="flex flex-wrap gap-2">
                {region.keyCompetitors.map((competitor, idx) => (
                  <span 
                    key={idx} 
                    className="inline-block px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700"
                  >
                    {competitor}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="grid grid-cols-1 gap-4">
              <div className="bg-green-50 rounded-lg p-3">
                <h4 className="text-sm font-semibold text-green-700 mb-1">Opportunity</h4>
                <p className="text-sm text-gray-700">{region.opportunity}</p>
              </div>
              
              <div className="bg-red-50 rounded-lg p-3">
                <h4 className="text-sm font-semibold text-red-700 mb-1">Challenge</h4>
                <p className="text-sm text-gray-700">{region.challenge}</p>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
