import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axis, <PERSON>Axis, ZAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, TooltipProps } from 'recharts';
import { DollarSign, Clock, Users, Award } from 'lucide-react';

interface Competitor {
  name: string;
  price: number;
  featureScore: number;
  size: number;
}

const PriceFeatureMatrix: React.FC = () => {
  const competitors: Competitor[] = [
    { name: '<PERSON><PERSON>ru<PERSON>', price: 1500, featureScore: 95, size: 800 },
    { name: 'Workable', price: 169, featureScore: 65, size: 400 },
    { name: 'HireVue', price: 2916, featureScore: 70, size: 400 },
    { name: 'Eightfold AI', price: 650, featureScore: 75, size: 400 },
    { name: 'Manatal', price: 17, featureScore: 45, size: 400 },
    { name: 'Greenhouse', price: 500, featureScore: 60, size: 300 },
    { name: 'Lever', price: 300, featureScore: 55, size: 300 },
    { name: 'SmartRecruiters', price: 800, featureScore: 65, size: 300 },
    { name: 'LinkedIn Talent', price: 120, featureScore: 50, size: 300 },
    { name: 'Merc<PERSON>', price: 400, featureScore: 80, size: 200 },
  ];

  const customTooltip = ({ active, payload }: TooltipProps<number, string>) => {
    if (!active || !payload || !payload.length) {
      return null;
    }

    const competitor = competitors.find(c => c.name === payload[0].name);
    if (!competitor) return null;
    
    return (
      <div className="bg-white p-3 rounded-lg shadow-lg border border-gray-200">
        <p className="font-bold text-gray-800">{competitor.name}</p>
        <div className="flex justify-between mt-2 text-xs text-gray-500">
          <span>Price: ${competitor.price}/month</span>
          <span>Features: {competitor.featureScore}/100</span>
        </div>
      </div>
    );
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-xl font-bold mb-4 text-gray-800">Price-to-Feature Value Matrix</h3>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart
              margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
            >
              <defs>
                <linearGradient id="recruivaGradient" x1="0" y1="0" x2="1" y2="1">
                  <stop offset="0%" stopColor="#8884d8" />
                  <stop offset="50%" stopColor="#4a72e8" />
                  <stop offset="100%" stopColor="#ec5fe3" />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
              <XAxis 
                type="number" 
                dataKey="featureScore" 
                name="Feature Comprehensiveness" 
                domain={[0, 100]} 
                label={{ value: 'Feature Comprehensiveness', position: 'bottom', offset: 0 }} 
              />
              <YAxis 
                type="number" 
                dataKey="price" 
                name="Monthly Price ($)" 
                domain={[0, 3000]} 
                label={{ value: 'Monthly Price ($)', angle: -90, position: 'left' }} 
              />
              <ZAxis type="number" range={[100, 800]} dataKey="size" />
              <Tooltip content={customTooltip} />
              <Legend />
              
              {/* Quadrant labels */}
              <text x="15%" y="15%" className="text-sm font-medium" fill="#6b7280">Basic & Affordable</text>
              <text x="85%" y="15%" className="text-sm font-medium" fill="#6b7280">High Value</text>
              <text x="15%" y="85%" className="text-sm font-medium" fill="#6b7280">Low Value</text>
              <text x="85%" y="85%" className="text-sm font-medium" fill="#6b7280">Premium</text>
              
              {/* Render each competitor type as a separate series for the legend */}
              <Scatter
                name="Recruiva (Our Solution)"
                data={competitors.filter(c => c.name === 'Recruiva')}
                fill="url(#recruivaGradient)"
                shape="circle"
              />
              <Scatter
                name="Direct Competitors"
                data={competitors.filter(c => c.name !== 'Recruiva' && c.name !== 'Greenhouse' && c.name !== 'Lever')}
                fill="#4a72e8"
                shape="circle"
              />
              <Scatter
                name="Adjacent Competitors"
                data={competitors.filter(c => c.name === 'Greenhouse' || c.name === 'Lever')}
                fill="#22c55e"
                shape="circle"
              />
              <Scatter
                name="Emerging Threats"
                data={competitors.filter(c => c.name === 'Mercor')}
                fill="#ec5fe3"
                shape="circle"
              />
            </ScatterChart>
          </ResponsiveContainer>
        </div>
      </div>
      
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-xl font-bold mb-4 text-gray-800">ROI Calculation</h3>
        <p className="text-gray-700 mb-6">
          Recruiva&apos;s $1,500 monthly investment delivers estimated ROI of 3.2x through:
        </p>
        
        <div className="space-y-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                <Clock className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-800">62% reduction in time-to-hire</h4>
                <p className="text-sm text-gray-600">Saving ~$8,000 per role</p>
              </div>
            </div>
            <div className="w-full bg-gray-200 h-2 rounded-full">
              <div className="bg-purple-500 h-2 rounded-full" style={{ width: '62%' }}></div>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                <Users className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-800">41% decrease in recruiter hours</h4>
                <p className="text-sm text-gray-600">Saving ~$3,200 per month</p>
              </div>
            </div>
            <div className="w-full bg-gray-200 h-2 rounded-full">
              <div className="bg-blue-500 h-2 rounded-full" style={{ width: '41%' }}></div>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <div className="w-8 h-8 rounded-full bg-pink-100 flex items-center justify-center mr-3">
                <Award className="w-4 h-4 text-pink-600" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-800">28% improvement in quality-of-hire</h4>
                <p className="text-sm text-gray-600">Value: ~$15,000 per successful hire</p>
              </div>
            </div>
            <div className="w-full bg-gray-200 h-2 rounded-full">
              <div className="bg-pink-500 h-2 rounded-full" style={{ width: '28%' }}></div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg text-white">
          <div className="flex items-center mb-2">
            <DollarSign className="w-5 h-5 mr-2" />
            <h4 className="font-bold text-lg">Total Monthly Savings</h4>
          </div>
          <p className="font-medium">For a company hiring 5 roles per month: <span className="font-bold text-xl">$52,000</span></p>
        </div>
      </div>
    </div>
  );
};

export default PriceFeatureMatrix;
