import React from 'react';
import { ArrowR<PERSON>, Target, Shield, Zap, Users, Globe, Award } from 'lucide-react';

type RecommendationCardProps = {
  title: string;
  description: string;
  icon: React.ReactNode;
  actions: string[];
  colorClass: string;
  priority: 'High' | 'Medium' | 'Low';
};

const RecommendationCard: React.FC<RecommendationCardProps> = ({
  title,
  description,
  icon,
  actions,
  colorClass,
  priority,
}) => {
  const getPriorityColor = () => {
    switch (priority) {
      case 'High':
        return 'bg-red-500';
      case 'Medium':
        return 'bg-amber-500';
      case 'Low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      <div className={`h-2 ${colorClass}`}></div>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div className={`w-10 h-10 rounded-full ${colorClass} bg-opacity-20 flex items-center justify-center mr-3`}>
              {icon}
            </div>
            <h3 className="text-lg font-bold text-gray-800">{title}</h3>
          </div>
          <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${getPriorityColor()}`}>
            {priority} Priority
          </span>
        </div>
        
        <p className="text-gray-600 mb-4">{description}</p>
        
        <h4 className="text-sm font-semibold text-gray-500 mb-2">Recommended Actions:</h4>
        <ul className="space-y-2">
          {actions.map((action, index) => (
            <li key={index} className="flex items-start">
              <ArrowRight className={`w-4 h-4 ${colorClass.replace('bg-', 'text-')} mr-2 flex-shrink-0 mt-1`} />
              <span className="text-gray-700 text-sm">{action}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export const StrategicRecommendations: React.FC = () => {
  const recommendations = [
    {
      title: 'Differentiation Strategy',
      description: 'Emphasize unique voice AI capabilities and comprehensive end-to-end automation that competitors lack.',
      icon: <Target className="w-5 h-5 text-purple-600" />,
      actions: [
        'Create side-by-side comparison materials highlighting Recruiva&apos;s unique voice AI capabilities',
        'Develop case studies showcasing ROI from end-to-end automation',
        'Create demo videos focusing on features competitors lack'
      ],
      colorClass: 'bg-purple-500',
      priority: 'High' as const,
    },
    {
      title: 'Market Positioning',
      description: 'Position Recruiva as the premium solution for mid-market companies seeking enterprise-grade capabilities without enterprise complexity.',
      icon: <Shield className="w-5 h-5 text-blue-600" />,
      actions: [
        'Target marketing efforts at companies with 500-5,000 employees',
        'Develop messaging around "enterprise capabilities, mid-market simplicity"',
        'Create content addressing mid-market pain points'
      ],
      colorClass: 'bg-blue-500',
      priority: 'High' as const,
    },
    {
      title: 'Technical Excellence',
      description: 'Counter Mercor&apos;s technical recruitment focus by enhancing and promoting Recruiva&apos;s technical assessment capabilities.',
      icon: <Zap className="w-5 h-5 text-amber-600" />,
      actions: [
        'Enhance built-in coding IDE features',
        'Develop tech-specific marketing materials',
        'Create partnerships with technical assessment platforms'
      ],
      colorClass: 'bg-amber-500',
      priority: 'Medium' as const,
    },
    {
      title: 'Pricing Strategy',
      description: 'Maintain premium pricing while emphasizing ROI and offering flexible models to counter low-cost competitors.',
      icon: <Award className="w-5 h-5 text-green-600" />,
      actions: [
        'Develop detailed ROI calculators for sales team',
        'Enhance pay-as-you-go model for price-sensitive segments',
        'Create case studies highlighting cost savings'
      ],
      colorClass: 'bg-green-500',
      priority: 'Medium' as const,
    },
    {
      title: 'Customer Segmentation',
      description: 'Develop targeted marketing and sales approaches for different customer segments based on their specific needs.',
      icon: <Users className="w-5 h-5 text-pink-600" />,
      actions: [
        'Create segment-specific marketing materials',
        'Train sales team on segment-specific approaches',
        'Develop tailored product packages for each segment'
      ],
      colorClass: 'bg-pink-500',
      priority: 'High' as const,
    },
    {
      title: 'Global Expansion',
      description: 'Prepare for international competition by enhancing language support and regional compliance features.',
      icon: <Globe className="w-5 h-5 text-indigo-600" />,
      actions: [
        'Expand language support beyond English',
        'Develop region-specific compliance documentation',
        'Research international competitors in target markets'
      ],
      colorClass: 'bg-indigo-500',
      priority: 'Low' as const,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {recommendations.map((recommendation, index) => (
        <RecommendationCard
          key={index}
          title={recommendation.title}
          description={recommendation.description}
          icon={recommendation.icon}
          actions={recommendation.actions}
          colorClass={recommendation.colorClass}
          priority={recommendation.priority}
        />
      ))}
    </div>
  );
};
