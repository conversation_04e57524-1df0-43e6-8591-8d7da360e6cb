import React from 'react';
import { Check, X } from 'lucide-react';
import styles from './CompetitorsAnalysis.module.css';

type Feature = {
  name: string;
  value: boolean | string;
};

type CompetitorCardProps = {
  name: string;
  type: 'direct' | 'adjacent' | 'partial' | 'emerging';
  description: string;
  keyDifferentiator?: string;
  targetSize?: string;
  startingPrice?: string;
  features: Feature[];
  geographicStrength?: string;
  customerSatisfaction?: string;
  inView: boolean;
  delay: number;
};

export const CompetitorCard: React.FC<CompetitorCardProps> = ({
  name,
  type,
  description,
  keyDifferentiator,
  targetSize,
  startingPrice,
  features,
  geographicStrength,
  customerSatisfaction,
  inView,
  delay
}) => {
  const getBadgeClass = () => {
    switch (type) {
      case 'direct':
        return styles.directBadge;
      case 'adjacent':
        return styles.adjacentBadge;
      case 'partial':
        return styles.partialBadge;
      case 'emerging':
        return styles.emergingBadge;
      default:
        return '';
    }
  };

  const getTypeLabel = () => {
    switch (type) {
      case 'direct':
        return 'Direct Competitor';
      case 'adjacent':
        return 'Adjacent Competitor';
      case 'partial':
        return 'Partial Competitor';
      case 'emerging':
        return 'Emerging Threat';
      default:
        return '';
    }
  };

  return (
    <div
      className={`transform ${inView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 ${styles.competitorCard} bg-white rounded-xl shadow-lg overflow-hidden`}
      style={{ transitionDelay: `${delay}ms` }}
    >
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-xl font-bold text-gray-800">{name}</h3>
          <span className={`${styles.badge} ${getBadgeClass()}`}>{getTypeLabel()}</span>
        </div>
        
        <p className="text-gray-600 mb-4">{description}</p>
        
        {keyDifferentiator && (
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-gray-500 mb-1">Key Differentiator</h4>
            <p className="font-medium text-gray-800">{keyDifferentiator}</p>
          </div>
        )}
        
        {targetSize && (
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-gray-500 mb-1">Target Size</h4>
            <p className="font-medium text-gray-800">{targetSize}</p>
          </div>
        )}
        
        {startingPrice && (
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-gray-500 mb-1">Starting Price</h4>
            <p className="font-medium text-gray-800">{startingPrice}</p>
          </div>
        )}
        
        {features.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-gray-500 mb-2">Key Features</h4>
            <ul className="space-y-1">
              {features.map((feature, index) => (
                <li key={index} className="flex items-center">
                  {typeof feature.value === 'boolean' ? (
                    feature.value ? (
                      <Check className="w-4 h-4 text-green-500 mr-2" />
                    ) : (
                      <X className="w-4 h-4 text-red-500 mr-2" />
                    )
                  ) : (
                    <span className="w-4 h-4 flex items-center justify-center mr-2">•</span>
                  )}
                  <span className="text-gray-700">
                    {feature.name}: {typeof feature.value === 'boolean' ? (feature.value ? 'Yes' : 'No') : feature.value}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {geographicStrength && (
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-gray-500 mb-1">Geographic Strength</h4>
            <p className="font-medium text-gray-800">{geographicStrength}</p>
          </div>
        )}
        
        {customerSatisfaction && (
          <div>
            <h4 className="text-sm font-semibold text-gray-500 mb-1">Customer Satisfaction</h4>
            <p className="font-medium text-gray-800">{customerSatisfaction}</p>
          </div>
        )}
      </div>
    </div>
  );
};
