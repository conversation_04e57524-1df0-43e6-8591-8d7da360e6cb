import React from 'react';
import { Shield, Check, Lock, FileText, Globe } from 'lucide-react';

type ComplianceItem = {
  name: string;
  recruiva: 'Full' | 'Partial' | 'In Progress' | 'Not Available';
  workable: 'Full' | 'Partial' | 'In Progress' | 'Not Available';
  hirevue: 'Full' | 'Partial' | 'In Progress' | 'Not Available';
  eightfoldAi: 'Full' | 'Partial' | 'In Progress' | 'Not Available';
  manatal: 'Full' | 'Partial' | 'In Progress' | 'Not Available';
  description: string;
};

export const SecurityCompliance: React.FC = () => {
  const complianceItems: ComplianceItem[] = [
    {
      name: 'SOC 2 Type II',
      recruiva: 'Full',
      workable: 'Full',
      hirevue: 'Full',
      eightfoldAi: 'Full',
      manatal: 'Not Available',
      description: 'Independent audit verifying security, availability, and confidentiality controls',
    },
    {
      name: 'GDPR Compliance',
      recruiva: 'Full',
      workable: 'Full',
      hirevue: 'Full',
      eightfoldAi: 'Full',
      manatal: 'Partial',
      description: 'European data protection and privacy regulations',
    },
    {
      name: 'CCPA Compliance',
      recruiva: 'Full',
      workable: 'Full',
      hirevue: 'Full',
      eightfoldAi: 'Full',
      manatal: 'Partial',
      description: 'California Consumer Privacy Act regulations',
    },
    {
      name: 'ISO 27001',
      recruiva: 'Full',
      workable: 'Full',
      hirevue: 'Full',
      eightfoldAi: 'Full',
      manatal: 'Not Available',
      description: 'Information security management system standard',
    },
    {
      name: 'HIPAA Compliance',
      recruiva: 'Full',
      workable: 'Partial',
      hirevue: 'Full',
      eightfoldAi: 'Full',
      manatal: 'Not Available',
      description: 'Health Insurance Portability and Accountability Act for healthcare data',
    },
    {
      name: 'AI Ethics Framework',
      recruiva: 'Full',
      workable: 'Partial',
      hirevue: 'Full',
      eightfoldAi: 'Full',
      manatal: 'Not Available',
      description: 'Ethical guidelines for AI usage in recruitment',
    },
    {
      name: 'Bias Monitoring',
      recruiva: 'Full',
      workable: 'Partial',
      hirevue: 'Full',
      eightfoldAi: 'Full',
      manatal: 'Partial',
      description: 'Systems to detect and mitigate algorithmic bias',
    },
    {
      name: 'Multi-Region Data Centers',
      recruiva: 'Full',
      workable: 'Full',
      hirevue: 'Full',
      eightfoldAi: 'Full',
      manatal: 'Partial',
      description: 'Data storage options in multiple geographic regions',
    },
    {
      name: 'SSO Integration',
      recruiva: 'Full',
      workable: 'Full',
      hirevue: 'Full',
      eightfoldAi: 'Full',
      manatal: 'Partial',
      description: 'Single sign-on authentication capabilities',
    },
    {
      name: 'Regular Penetration Testing',
      recruiva: 'Full',
      workable: 'Full',
      hirevue: 'Full',
      eightfoldAi: 'Full',
      manatal: 'In Progress',
      description: 'Independent security testing to identify vulnerabilities',
    },
  ];


  const getComplianceClass = (status: string) => {
    switch (status) {
      case 'Full':
        return 'bg-green-100 text-green-800';
      case 'Partial':
        return 'bg-amber-100 text-amber-800';
      case 'In Progress':
        return 'bg-blue-100 text-blue-800';
      case 'Not Available':
        return 'bg-red-100 text-red-800';
      default:
        return '';
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div className="lg:col-span-3 bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center mb-6">
          <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
            <Shield className="w-6 h-6 text-indigo-600" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-800">Security & Compliance Comparison</h3>
            <p className="text-gray-600">Regulatory compliance and security certifications across competitors</p>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Certification/Standard</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Recruiva</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Workable</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">HireVue</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Eightfold AI</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Manatal</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {complianceItems.map((item, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-900">{item.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getComplianceClass(item.recruiva)}`}>
                      {item.recruiva}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getComplianceClass(item.workable)}`}>
                      {item.workable}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getComplianceClass(item.hirevue)}`}>
                      {item.hirevue}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getComplianceClass(item.eightfoldAi)}`}>
                      {item.eightfoldAi}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getComplianceClass(item.manatal)}`}>
                      {item.manatal}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-normal text-sm text-gray-500">
                    {item.description}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      <div className="lg:col-span-1 bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="h-2 bg-indigo-500"></div>
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
              <Lock className="w-5 h-5 text-indigo-600" />
            </div>
            <h3 className="text-lg font-bold text-gray-800">Security Highlights</h3>
          </div>
          
          <ul className="space-y-3">
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">End-to-end encryption for all data</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">Multi-factor authentication</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">Role-based access controls</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">Regular security audits</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">Vulnerability management program</span>
            </li>
          </ul>
        </div>
      </div>
      
      <div className="lg:col-span-1 bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="h-2 bg-blue-500"></div>
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
              <FileText className="w-5 h-5 text-blue-600" />
            </div>
            <h3 className="text-lg font-bold text-gray-800">Compliance Advantage</h3>
          </div>
          
          <p className="text-gray-600 mb-4">
            Recruiva maintains compliance parity with enterprise solutions while exceeding smaller competitors, particularly in:
          </p>
          
          <ul className="space-y-3">
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">AI ethics framework and governance</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">Comprehensive bias monitoring</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">Healthcare data handling (HIPAA)</span>
            </li>
          </ul>
        </div>
      </div>
      
      <div className="lg:col-span-1 bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="h-2 bg-green-500"></div>
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
              <Globe className="w-5 h-5 text-green-600" />
            </div>
            <h3 className="text-lg font-bold text-gray-800">Global Compliance</h3>
          </div>
          
          <p className="text-gray-600 mb-4">
            Regional compliance capabilities to support global operations:
          </p>
          
          <ul className="space-y-3">
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">GDPR (Europe)</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">CCPA (California)</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">PIPEDA (Canada)</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">PDPA (Singapore)</span>
            </li>
            <li className="flex items-start">
              <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="text-gray-700">LGPD (Brazil)</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};
