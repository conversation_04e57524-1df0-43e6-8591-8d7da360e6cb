import React from 'react';
import { Check, X, HelpCircle } from 'lucide-react';
import styles from './CompetitorsAnalysis.module.css';

export const FeatureComparisonTable: React.FC = () => {
  const features = [
    { name: 'AI Role Definition', recruiva: true, workable: true, hirevue: 'No Mention', eightfoldAi: true, manatal: 'No Mention' },
    { name: 'AI Resume Analysis', recruiva: true, workable: true, hirevue: true, eightfoldAi: true, manatal: true },
    { name: 'AI Voice Interview', recruiva: true, workable: 'No Mention', hirevue: false, eightfoldAi: 'No Mention', manatal: 'No Mention' },
    { name: 'Adaptive Questioning', recruiva: true, workable: 'No Mention', hirevue: 'Video Only', eightfoldAi: 'No Mention', manatal: 'No Mention' },
    { name: 'AI Visual Assessments', recruiva: true, workable: false, hirevue: 'Limited', eightfoldAi: false, manatal: false },
    { name: 'Built-in Coding IDE with AI', recruiva: true, workable: false, hirevue: false, eightfoldAi: false, manatal: false },
    { name: 'Candidate LLM Evaluation', recruiva: true, workable: false, hirevue: false, eightfoldAi: false, manatal: false },
    { name: 'Automated Evaluation', recruiva: true, workable: 'Via Integrations', hirevue: true, eightfoldAi: true, manatal: true },
    { name: 'Bias Reduction', recruiva: true, workable: true, hirevue: true, eightfoldAi: true, manatal: true },
    { name: 'ATS Integrations', recruiva: true, workable: true, hirevue: true, eightfoldAi: true, manatal: 'Limited' },
    { name: 'GDPR Compliance', recruiva: true, workable: true, hirevue: true, eightfoldAi: true, manatal: true },
    { name: 'SOC 2 Compliance', recruiva: true, workable: true, hirevue: true, eightfoldAi: true, manatal: false },
    { name: 'Multi-language Support', recruiva: true, workable: 'Limited', hirevue: true, eightfoldAi: true, manatal: 'Limited' },
    { name: 'Target Customer Size', recruiva: '500-5,000', workable: '10-500', hirevue: '10,000+', eightfoldAi: '10,000+', manatal: 'SMBs, Agencies' },
    { name: 'Starting Price (Monthly)', recruiva: '$1,500*', workable: '$169', hirevue: '$2,916', eightfoldAi: '$650', manatal: '$15-19' },
    { name: 'Key Differentiator', recruiva: 'Voice AI, Adaptive Questioning, Visual Assessment & LLM Evaluation', workable: 'Ease of Use', hirevue: 'Video Analysis', eightfoldAi: 'Talent Intelligence', manatal: 'Affordability' },
  ];

  const renderValue = (value: boolean | string) => {
    if (typeof value === 'boolean') {
      return value ? (
        <Check className={`w-5 h-5 mx-auto ${styles.checkmark}`} />
      ) : (
        <X className={`w-5 h-5 mx-auto ${styles.xmark}`} />
      );
    }
    
    if (value === 'No Mention') {
      return <HelpCircle className="w-5 h-5 mx-auto text-gray-400" />;
    }
    
    if (value === 'Limited') {
      return <span className="text-amber-500">Limited</span>;
    }
    
    return value;
  };

  return (
    <div className="overflow-x-auto">
      <table className={styles.featureTable}>
        <thead>
          <tr>
            <th className="text-left">Feature</th>
            <th className={styles.recruivaColumn}>Recruiva</th>
            <th>Workable</th>
            <th>HireVue</th>
            <th>Eightfold AI</th>
            <th>Manatal</th>
          </tr>
        </thead>
        <tbody>
          {features.map((feature, index) => (
            <tr key={index}>
              <td className="text-left font-medium">{feature.name}</td>
              <td className={styles.recruivaColumn}>{renderValue(feature.recruiva)}</td>
              <td>{renderValue(feature.workable)}</td>
              <td>{renderValue(feature.hirevue)}</td>
              <td>{renderValue(feature.eightfoldAi)}</td>
              <td>{renderValue(feature.manatal)}</td>
            </tr>
          ))}
        </tbody>
      </table>
      <p className="text-sm text-gray-500 mt-2">*Recruiva also offers a pay-as-you-go model at $1/minute of interview with $100/month platform access fee</p>
    </div>
  );
};
