import React from 'react';
import { MessageSquare, Star, Users, Building, Briefcase } from 'lucide-react';

type Testimonial = {
  quote: string;
  author: string;
  title: string;
  company: string;
  companySize: string;
  industry: string;
  rating: number;
  previousSolution: string;
  keyBenefit: string;
  colorClass: string;
};

export const CustomerTestimonials: React.FC = () => {
  const testimonials: Testimonial[] = [
    {
      quote: "Recruiva&apos;s voice AI interviews have cut our screening time by 65% while improving candidate quality. The built-in coding IDE with AI evaluation has transformed our technical hiring process.",
      author: "<PERSON>",
      title: "Head of Talent Acquisition",
      company: "TechVision Inc.",
      companySize: "2,500 employees",
      industry: "Software Development",
      rating: 5,
      previousSolution: "HireVue",
      keyBenefit: "End-to-end automation with superior technical assessment",
      colorClass: "bg-purple-500",
    },
    {
      quote: "We switched from Workable to Recruiva and immediately saw the difference in automation capabilities. The adaptive questioning feature ensures we get deeper insights from candidates without manual follow-up.",
      author: "<PERSON>",
      title: "Director of HR",
      company: "FinanceFlow",
      companySize: "1,200 employees",
      industry: "Financial Services",
      rating: 5,
      previousSolution: "Workable",
      keyBenefit: "Adaptive questioning and comprehensive automation",
      colorClass: "bg-blue-500",
    },
    {
      quote: "As a growing company, we needed a solution that could scale with us. Recruiva&apos;s pay-as-you-go model gave us enterprise-grade capabilities without the enterprise commitment, and we&apos;ve since upgraded to a full subscription.",
      author: "<PERSON>",
      title: "Founder & CEO",
      company: "GrowthLabs",
      companySize: "150 employees",
      industry: "Marketing Technology",
      rating: 5,
      previousSolution: "Lever",
      keyBenefit: "Flexible pricing with enterprise capabilities",
      colorClass: "bg-green-500",
    },
    {
      quote: "The visual assessment capabilities in Recruiva have revolutionized our design team hiring. We can now evaluate candidates&apos; visual thinking and creativity in ways that weren&apos;t possible with our previous solution.",
      author: "Emily Wong",
      title: "VP of Design",
      company: "CreativeForce Agency",
      companySize: "800 employees",
      industry: "Creative Services",
      rating: 4,
      previousSolution: "SmartRecruiters",
      keyBenefit: "Visual assessment capabilities for creative roles",
      colorClass: "bg-pink-500",
    },
    {
      quote: "What impressed us most about Recruiva was how it handled both technical and non-technical roles with equal effectiveness. The LLM evaluation feature has been particularly valuable for assessing analytical thinking.",
      author: "David Patel",
      title: "Chief People Officer",
      company: "DataDrive Solutions",
      companySize: "3,200 employees",
      industry: "Data Analytics",
      rating: 5,
      previousSolution: "Eightfold AI",
      keyBenefit: "Versatility across technical and non-technical roles",
      colorClass: "bg-amber-500",
    },
    {
      quote: "After comparing several AI recruitment platforms, we chose Recruiva for its comprehensive feature set and intuitive interface. Our recruiters were up and running in days, not weeks.",
      author: "Jennifer Martinez",
      title: "Talent Acquisition Manager",
      company: "HealthTech Innovations",
      companySize: "950 employees",
      industry: "Healthcare Technology",
      rating: 4,
      previousSolution: "Manatal",
      keyBenefit: "Ease of implementation with comprehensive features",
      colorClass: "bg-indigo-500",
    },
  ];

  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, i) => (
      <Star 
        key={i} 
        className={`w-4 h-4 ${i < rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} 
      />
    ));
  };

  return (
    <div className="space-y-12">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-gray-800 mb-2">Customer Success Stories</h3>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Real feedback from customers who switched from competitors to Recruiva
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {testimonials.map((testimonial, index) => (
          <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden flex flex-col h-full">
            <div className={`h-2 ${testimonial.colorClass}`}></div>
            <div className="p-6 flex-grow">
              <div className="flex items-center mb-4">
                <div className={`w-10 h-10 rounded-full ${testimonial.colorClass} bg-opacity-20 flex items-center justify-center mr-3`}>
                  <MessageSquare className={`w-5 h-5 ${testimonial.colorClass.replace('bg-', 'text-')}`} />
                </div>
                <div className="flex flex-col">
                  <div className="flex items-center">
                    {renderStars(testimonial.rating)}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    Previously using: <span className="font-medium">{testimonial.previousSolution}</span>
                  </div>
                </div>
              </div>
              
              <blockquote className="text-gray-700 mb-6 italic">
                &quot;{testimonial.quote}&quot;
              </blockquote>
              
              <div className="mt-auto">
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                    <Users className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">{testimonial.author}</h4>
                    <p className="text-sm text-gray-600">{testimonial.title}</p>
                  </div>
                </div>
                
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                    <Building className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">{testimonial.company}</h4>
                    <p className="text-sm text-gray-600">{testimonial.companySize} • {testimonial.industry}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                    <Briefcase className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Key Benefit</h4>
                    <p className="text-sm text-gray-600">{testimonial.keyBenefit}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl shadow-lg p-8 text-white">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <h4 className="text-3xl font-bold mb-2">92%</h4>
            <p className="text-sm opacity-90">Customer Satisfaction Rate</p>
          </div>
          <div className="text-center">
            <h4 className="text-3xl font-bold mb-2">87%</h4>
            <p className="text-sm opacity-90">Would Recommend to Others</p>
          </div>
          <div className="text-center">
            <h4 className="text-3xl font-bold mb-2">4.8/5</h4>
            <p className="text-sm opacity-90">Average Rating</p>
          </div>
        </div>
      </div>
    </div>
  );
};
