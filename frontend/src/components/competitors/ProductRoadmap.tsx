import React from 'react';
import { Clock, Zap, Award, Target, Globe, Shield, Building } from 'lucide-react';

type RoadmapItem = {
  quarter: string;
  title: string;
  description: string;
  features: string[];
  competitiveAdvantage: string;
  status: 'planned' | 'in-progress' | 'completed';
  icon: React.ReactNode;
  colorClass: string;
};

export const ProductRoadmap: React.FC = () => {
  const roadmapItems: RoadmapItem[] = [
    {
      quarter: 'Q1 2025',
      title: 'Enhanced Technical Assessment',
      description: 'Expanding technical assessment capabilities to counter Mercor and strengthen position in technical recruitment',
      features: [
        'Advanced coding IDE with real-time collaboration',
        'Expanded language support for technical assessments',
        'AI-assisted code review and feedback',
        'System design assessment capabilities'
      ],
      competitiveAdvantage: 'Comprehensive technical assessment within end-to-end platform',
      status: 'in-progress',
      icon: <Zap className="w-5 h-5 text-purple-600" />,
      colorClass: 'bg-purple-500',
    },
    {
      quarter: 'Q2 2025',
      title: 'Global Expansion Features',
      description: 'Enhancing capabilities to support international markets and compete globally',
      features: [
        'Expanded language support for voice AI (Spanish, French, German, Japanese)',
        'Region-specific compliance documentation',
        'Localized user interfaces',
        'Multi-currency billing options'
      ],
      competitiveAdvantage: 'True global recruitment capabilities with localized experience',
      status: 'planned',
      icon: <Globe className="w-5 h-5 text-blue-600" />,
      colorClass: 'bg-blue-500',
    },
    {
      quarter: 'Q3 2025',
      title: 'Advanced Analytics & Insights',
      description: 'Delivering deeper recruitment analytics to demonstrate ROI and competitive advantage',
      features: [
        'Enhanced recruitment funnel analytics',
        'Predictive hiring success modeling',
        'Competitive benchmark reporting',
        'Custom dashboard builder'
      ],
      competitiveAdvantage: 'Data-driven recruitment with actionable insights beyond competitors',
      status: 'planned',
      icon: <Target className="w-5 h-5 text-amber-600" />,
      colorClass: 'bg-amber-500',
    },
    {
      quarter: 'Q4 2025',
      title: 'Enterprise Integration Expansion',
      description: 'Strengthening enterprise capabilities to compete with HireVue and Eightfold AI',
      features: [
        'Advanced SSO and identity management',
        'Enhanced API capabilities',
        'Custom workflow builder',
        'Enterprise data warehouse integration'
      ],
      competitiveAdvantage: 'Enterprise-grade capabilities with mid-market simplicity',
      status: 'planned',
      icon: <Building className="w-5 h-5 text-green-600" />,
      colorClass: 'bg-green-500',
    },
    {
      quarter: 'Q1 2026',
      title: 'Next-Gen AI Capabilities',
      description: 'Pushing the innovation boundary to maintain technology leadership',
      features: [
        'Multimodal AI assessment (voice, visual, text)',
        'Advanced behavioral analysis',
        'Personality trait mapping',
        'Team fit prediction'
      ],
      competitiveAdvantage: 'Cutting-edge AI capabilities beyond any competitor',
      status: 'planned',
      icon: <Award className="w-5 h-5 text-pink-600" />,
      colorClass: 'bg-pink-500',
    },
  ];

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'planned':
        return 'bg-gray-100 text-gray-800';
      default:
        return '';
    }
  };

  return (
    <div className="space-y-12">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-gray-800 mb-2">Product Roadmap</h3>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Strategic product development to maintain competitive advantage
        </p>
      </div>
      
      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200 hidden md:block"></div>
        
        {/* Timeline items */}
        <div className="space-y-12">
          {roadmapItems.map((item, index) => (
            <div key={index} className="relative md:pl-20">
              {/* Timeline dot - only visible on md and up */}
              <div className={`absolute left-6 w-5 h-5 rounded-full border-4 border-white shadow-md transform -translate-x-1/2 hidden md:block ${
                item.status === 'completed' ? 'bg-green-500' :
                item.status === 'in-progress' ? 'bg-blue-500' : 'bg-gray-300'
              }`}></div>
              
              {/* Content */}
              <div className="bg-white rounded-xl shadow-lg overflow-hidden flex flex-col h-full">
                <div className={`h-2 ${item.colorClass}`}></div>
                <div className="p-6">
                  <div className="flex flex-wrap items-center justify-between mb-4">
                    <div className="flex items-center mb-2 md:mb-0">
                      <div className={`w-10 h-10 rounded-full ${item.colorClass} bg-opacity-20 flex items-center justify-center mr-3`}>
                        {item.icon}
                      </div>
                      <div>
                        <h4 className="font-bold text-gray-800">{item.title}</h4>
                        <div className="flex items-center mt-1">
                          <Clock className="w-4 h-4 text-gray-500 mr-1" />
                          <span className="text-sm text-gray-500">{item.quarter}</span>
                        </div>
                      </div>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusClass(item.status)}`}>
                      {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  
                  <div className="mb-4">
                    <h5 className="text-sm font-semibold text-gray-500 mb-2">Key Features:</h5>
                    <ul className="space-y-1">
                      {item.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start">
                          <span className={`text-sm mr-2 ${item.colorClass.replace('bg-', 'text-')}`}>•</span>
                          <span className="text-gray-700 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className={`p-3 rounded-lg ${item.colorClass} bg-opacity-10`}>
                    <h5 className="text-sm font-semibold mb-1">Competitive Advantage:</h5>
                    <p className="text-sm">{item.competitiveAdvantage}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-xl font-bold text-gray-800 mb-4">Competitive Innovation Strategy</h3>
        <p className="text-gray-600 mb-6">
          Recruiva&apos;s product roadmap is designed to maintain our competitive advantage through continuous innovation while addressing specific competitive threats:
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 bg-purple-50 rounded-lg">
            <div className="flex items-center mb-2">
              <Shield className="w-5 h-5 text-purple-600 mr-2" />
              <h4 className="font-semibold text-gray-800">Defensive Innovation</h4>
            </div>
            <p className="text-sm text-gray-600">Enhancing core capabilities to maintain differentiation from direct competitors like Workable and HireVue</p>
          </div>
          
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center mb-2">
              <Target className="w-5 h-5 text-blue-600 mr-2" />
              <h4 className="font-semibold text-gray-800">Targeted Response</h4>
            </div>
            <p className="text-sm text-gray-600">Specific feature development to counter emerging threats like Mercor&apos;s technical assessment focus</p>
          </div>
          
          <div className="p-4 bg-pink-50 rounded-lg">
            <div className="flex items-center mb-2">
              <Zap className="w-5 h-5 text-pink-600 mr-2" />
              <h4 className="font-semibold text-gray-800">Breakthrough Innovation</h4>
            </div>
            <p className="text-sm text-gray-600">Next-generation capabilities that create significant distance from all competitors</p>
          </div>
        </div>
      </div>
    </div>
  );
};
