import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, TooltipProps } from 'recharts';
import { Clock, Zap, Award } from 'lucide-react';

interface TimelineEntry {
  year: string;
  [key: string]: number | string;
}

export const FeatureAdoptionTimeline: React.FC = () => {
  const timelineData: TimelineEntry[] = [
    { year: '2018', recruiva: 0, workable: 35, hirevue: 45, eightfoldAi: 30, manatal: 0 },
    { year: '2019', recruiva: 0, workable: 45, hirevue: 55, eightfoldAi: 40, manatal: 20 },
    { year: '2020', recruiva: 0, workable: 55, hirevue: 65, eightfoldAi: 60, manatal: 30 },
    { year: '2021', recruiva: 0, workable: 60, hirevue: 70, eightfoldAi: 70, manatal: 40 },
    { year: '2022', recruiva: 0, workable: 65, hirevue: 75, eightfoldAi: 75, manatal: 45 },
    { year: '2023', recruiva: 70, workable: 70, hirevue: 80, eightfoldAi: 80, manatal: 50 },
    { year: '2024', recruiva: 95, workable: 75, hirevue: 85, eightfoldAi: 85, manatal: 55 },
    { year: '2025*', recruiva: 100, workable: 80, hirevue: 90, eightfoldAi: 90, manatal: 60 },
  ];

  const featureMilestones = [
    {
      year: '2023',
      title: 'Recruiva Launch',
      description: 'Launched with AI voice interviews, adaptive questioning, and visual assessments',
      icon: <Zap className="w-5 h-5 text-purple-500" />,
    },
    {
      year: '2024',
      title: 'Built-in Coding IDE & LLM Evaluation',
      description: 'Added advanced technical assessment capabilities and LLM evaluation',
      icon: <Award className="w-5 h-5 text-purple-500" />,
    },
    {
      year: '2025*',
      title: 'Future Roadmap',
      description: 'Planned: Advanced behavioral analytics, expanded integrations, and global language support',
      icon: <Clock className="w-5 h-5 text-purple-500" />,
    },
  ];

  const customTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {
    if (!active || !payload || !payload.length) {
      return null;
    }

    return (
      <div className="bg-white p-3 rounded-lg shadow-lg border border-gray-200">
        <p className="font-bold text-gray-800">{label}</p>
        <div className="mt-2">
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}%
            </p>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div className="md:col-span-2 bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-xl font-bold mb-6 text-gray-800">AI Feature Adoption Timeline</h3>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={timelineData}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
              <XAxis dataKey="year" />
              <YAxis label={{ value: 'AI Feature Completeness (%)', angle: -90, position: 'insideLeft' }} />
              <Tooltip content={customTooltip} />
              <Legend />
              <Bar dataKey="recruiva" name="Recruiva" fill="url(#recruivaGradient)" radius={[4, 4, 0, 0]} />
              <Bar dataKey="workable" name="Workable" fill="#4a72e8" radius={[4, 4, 0, 0]} />
              <Bar dataKey="hirevue" name="HireVue" fill="#22c55e" radius={[4, 4, 0, 0]} />
              <Bar dataKey="eightfoldAi" name="Eightfold AI" fill="#eab308" radius={[4, 4, 0, 0]} />
              <Bar dataKey="manatal" name="Manatal" fill="#ec5fe3" radius={[4, 4, 0, 0]} />
              <defs>
                <linearGradient id="recruivaGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#8884d8" />
                  <stop offset="50%" stopColor="#4a72e8" />
                  <stop offset="100%" stopColor="#ec5fe3" />
                </linearGradient>
              </defs>
            </BarChart>
          </ResponsiveContainer>
        </div>
        <p className="text-sm text-gray-500 mt-2">*2025 values are projections based on announced roadmaps</p>
      </div>
      
      <div className="md:col-span-1">
        <div className="bg-white rounded-xl shadow-lg p-6 h-full">
          <h3 className="text-xl font-bold mb-6 text-gray-800">Recruiva Milestones</h3>
          
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-purple-100"></div>
            
            {/* Timeline items */}
            <div className="space-y-8">
              {featureMilestones.map((milestone, index) => (
                <div key={index} className="relative pl-12">
                  {/* Timeline dot */}
                  <div className="absolute left-0 w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                    {milestone.icon}
                  </div>
                  
                  {/* Content */}
                  <div>
                    <div className="flex items-center mb-2">
                      <span className="text-sm font-semibold text-purple-500 mr-2">{milestone.year}</span>
                      <h4 className="font-bold text-gray-800">{milestone.title}</h4>
                    </div>
                    <p className="text-sm text-gray-600">{milestone.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="mt-8 p-4 bg-purple-50 rounded-lg">
            <h4 className="font-semibold text-gray-800 mb-2">Innovation Leadership</h4>
            <p className="text-sm text-gray-600">
              Recruiva has achieved in 2 years what competitors have been developing for 5+ years, demonstrating our accelerated innovation and technology advantage.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
