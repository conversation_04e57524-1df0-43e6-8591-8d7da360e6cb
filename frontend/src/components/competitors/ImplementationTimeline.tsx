import React from 'react';
import { Clock, Check, AlertTriangle } from 'lucide-react';

type TimelineItem = {
  phase: string;
  duration: string;
  description: string;
  tasks: string[];
  status: 'completed' | 'in-progress' | 'upcoming';
  riskLevel: 'low' | 'medium' | 'high';
};

export const ImplementationTimeline: React.FC = () => {
  const timelineItems: TimelineItem[] = [
    {
      phase: 'Phase 1: Market Research & Analysis',
      duration: 'Q1 2025 (4 weeks)',
      description: 'Deep dive into competitor capabilities, market positioning, and customer needs',
      tasks: [
        'Complete competitor feature analysis',
        'Identify market gaps and opportunities',
        'Conduct customer interviews',
        'Finalize competitive positioning strategy'
      ],
      status: 'completed',
      riskLevel: 'low',
    },
    {
      phase: 'Phase 2: Strategic Planning',
      duration: 'Q1-Q2 2025 (6 weeks)',
      description: 'Develop detailed implementation plans for each strategic recommendation',
      tasks: [
        'Create detailed action plans for each recommendation',
        'Assign resources and responsibilities',
        'Set KPIs and success metrics',
        'Secure executive buy-in and budget approval'
      ],
      status: 'in-progress',
      riskLevel: 'medium',
    },
    {
      phase: 'Phase 3: Product Enhancements',
      duration: 'Q2-Q3 2025 (12 weeks)',
      description: 'Implement product improvements to address competitive gaps',
      tasks: [
        'Enhance technical assessment capabilities',
        'Expand language support',
        'Improve integration capabilities',
        'Develop new pricing models'
      ],
      status: 'upcoming',
      riskLevel: 'medium',
    },
    {
      phase: 'Phase 4: Go-to-Market Execution',
      duration: 'Q3-Q4 2025 (16 weeks)',
      description: 'Launch new positioning and enhanced capabilities to market',
      tasks: [
        'Update marketing materials with competitive messaging',
        'Train sales team on competitive positioning',
        'Launch targeted campaigns for key segments',
        'Develop competitive battle cards'
      ],
      status: 'upcoming',
      riskLevel: 'high',
    },
    {
      phase: 'Phase 5: Measurement & Optimization',
      duration: 'Q4 2025 (Ongoing)',
      description: 'Track performance against competitors and adjust strategy as needed',
      tasks: [
        'Monitor win/loss rates against key competitors',
        'Track market share changes',
        'Gather customer feedback on competitive positioning',
        'Quarterly competitive strategy reviews'
      ],
      status: 'upcoming',
      riskLevel: 'low',
    },
  ];

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'upcoming':
        return 'bg-gray-100 text-gray-800';
      default:
        return '';
    }
  };

  const getRiskClass = (risk: string) => {
    switch (risk) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-amber-100 text-amber-800';
      case 'high':
        return 'bg-red-100 text-red-800';
      default:
        return '';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="mb-8">
        <h3 className="text-xl font-bold text-gray-800 mb-2">Implementation Timeline</h3>
        <p className="text-gray-600">
          Strategic roadmap for executing competitive positioning recommendations over the next 12 months
        </p>
      </div>
      
      <div className="relative">
        {/* Timeline line */}
        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200"></div>
        
        {/* Timeline items */}
        <div className="space-y-12">
          {timelineItems.map((item, index) => (
            <div key={index} className="relative pl-20">
              {/* Timeline dot */}
              <div className={`absolute left-6 w-5 h-5 rounded-full border-4 border-white shadow-md transform -translate-x-1/2 ${
                item.status === 'completed' ? 'bg-green-500' :
                item.status === 'in-progress' ? 'bg-blue-500' : 'bg-gray-300'
              }`}></div>
              
              {/* Content */}
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="flex flex-wrap items-center justify-between mb-4">
                  <div className="flex items-center mb-2 md:mb-0">
                    <h4 className="font-bold text-gray-800 mr-3">{item.phase}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(item.status)}`}>
                      {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 text-gray-500 mr-1" />
                    <span className="text-sm text-gray-500">{item.duration}</span>
                  </div>
                </div>
                
                <p className="text-gray-600 mb-4">{item.description}</p>
                
                <div className="mb-4">
                  <h5 className="text-sm font-semibold text-gray-500 mb-2">Key Tasks:</h5>
                  <ul className="space-y-1">
                    {item.tasks.map((task, taskIndex) => (
                      <li key={taskIndex} className="flex items-start">
                        <div className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5">
                          {item.status === 'completed' ? (
                            <Check className="w-5 h-5 text-green-500" />
                          ) : (
                            <div className={`w-3 h-3 rounded-full ${
                              item.status === 'in-progress' ? 'bg-blue-500' : 'bg-gray-300'
                            }`}></div>
                          )}
                        </div>
                        <span className="text-gray-700 text-sm">{task}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="flex items-center">
                  <AlertTriangle className={`w-4 h-4 mr-1 ${
                    item.riskLevel === 'low' ? 'text-green-500' :
                    item.riskLevel === 'medium' ? 'text-amber-500' : 'text-red-500'
                  }`} />
                  <span className={`text-xs font-medium px-2 py-1 rounded-full ${getRiskClass(item.riskLevel)}`}>
                    {item.riskLevel.charAt(0).toUpperCase() + item.riskLevel.slice(1)} Risk
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
