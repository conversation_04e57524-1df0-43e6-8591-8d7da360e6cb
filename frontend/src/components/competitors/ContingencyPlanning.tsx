import React from 'react';
import { AlertTriangle, Shield, ArrowRight, TrendingUp, TrendingDown, Zap } from 'lucide-react';

type ScenarioCardProps = {
  title: string;
  description: string;
  icon: React.ReactNode;
  likelihood: 'High' | 'Medium' | 'Low';
  impact: 'High' | 'Medium' | 'Low';
  responses: string[];
  colorClass: string;
};

const ScenarioCard: React.FC<ScenarioCardProps> = ({
  title,
  description,
  icon,
  likelihood,
  impact,
  responses,
  colorClass,
}) => {
  const getLikelihoodColor = () => {
    switch (likelihood) {
      case 'High':
        return 'bg-red-100 text-red-800';
      case 'Medium':
        return 'bg-amber-100 text-amber-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getImpactColor = () => {
    switch (impact) {
      case 'High':
        return 'bg-red-100 text-red-800';
      case 'Medium':
        return 'bg-amber-100 text-amber-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      <div className={`h-2 ${colorClass}`}></div>
      <div className="p-6">
        <div className="flex items-center mb-4">
          <div className={`w-12 h-12 rounded-full ${colorClass} bg-opacity-20 flex items-center justify-center mr-4`}>
            {icon}
          </div>
          <h3 className="text-xl font-bold text-gray-800">{title}</h3>
        </div>
        
        <p className="text-gray-600 mb-4">{description}</p>
        
        <div className="flex flex-wrap gap-3 mb-4">
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getLikelihoodColor()}`}>
            Likelihood: {likelihood}
          </span>
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getImpactColor()}`}>
            Impact: {impact}
          </span>
        </div>
        
        <h4 className="text-sm font-semibold text-gray-500 mb-2">Contingency Responses:</h4>
        <ul className="space-y-2">
          {responses.map((response, index) => (
            <li key={index} className="flex items-start">
              <ArrowRight className={`w-4 h-4 ${colorClass.replace('bg-', 'text-')} mr-2 flex-shrink-0 mt-1`} />
              <span className="text-gray-700 text-sm">{response}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export const ContingencyPlanning: React.FC = () => {
  const scenarios = [
    {
      title: 'Enterprise Competitor Price Reduction',
      description: 'Major enterprise competitors (HireVue, Eightfold AI) significantly lower prices to target mid-market',
      icon: <TrendingDown className="w-6 h-6 text-red-600" />,
      likelihood: 'Medium' as const,
      impact: 'High' as const,
      responses: [
        'Emphasize ROI and total value proposition beyond price',
        'Enhance pay-as-you-go model flexibility',
        'Create feature-specific packages to maintain price competitiveness',
        'Develop case studies highlighting superior outcomes vs. enterprise solutions'
      ],
      colorClass: 'bg-red-500',
    },
    {
      title: 'Technical Competitor Expansion',
      description: 'Mercor or similar technical recruitment platforms expand to offer full-suite recruitment capabilities',
      icon: <TrendingUp className="w-6 h-6 text-amber-600" />,
      likelihood: 'Medium' as const,
      impact: 'Medium' as const,
      responses: [
        'Accelerate development of technical assessment enhancements',
        'Create targeted marketing highlighting Recruiva&apos;s technical capabilities',
        'Develop partnership strategy with complementary technical assessment tools',
        'Enhance built-in coding IDE with additional features'
      ],
      colorClass: 'bg-amber-500',
    },
    {
      title: 'New AI-First Competitor Emergence',
      description: 'New startup launches with similar AI-first approach and significant funding',
      icon: <Zap className="w-6 h-6 text-purple-600" />,
      likelihood: 'High' as const,
      impact: 'Medium' as const,
      responses: [
        'Accelerate product roadmap for next-gen features',
        'Strengthen customer relationships through enhanced support',
        'Increase marketing around established track record and reliability',
        'Consider strategic partnership or acquisition opportunities'
      ],
      colorClass: 'bg-purple-500',
    },
    {
      title: 'Major ATS Platform AI Expansion',
      description: 'Greenhouse, Lever, or other major ATS platforms significantly enhance their AI capabilities',
      icon: <TrendingUp className="w-6 h-6 text-blue-600" />,
      likelihood: 'High' as const,
      impact: 'Medium' as const,
      responses: [
        'Strengthen integration capabilities with major ATS platforms',
        'Emphasize specialized AI capabilities beyond what ATS can offer',
        'Develop co-marketing opportunities with complementary ATS platforms',
        'Create comparison materials highlighting specialized vs. generalized AI'
      ],
      colorClass: 'bg-blue-500',
    },
    {
      title: 'Regulatory Changes for AI Recruitment',
      description: 'New regulations restricting or requiring transparency in AI-based hiring decisions',
      icon: <AlertTriangle className="w-6 h-6 text-orange-600" />,
      likelihood: 'Medium' as const,
      impact: 'High' as const,
      responses: [
        'Enhance explainability features in AI decision-making',
        'Strengthen compliance documentation and certifications',
        'Develop regulatory monitoring and rapid response capabilities',
        'Position as a leader in ethical AI recruitment practices'
      ],
      colorClass: 'bg-orange-500',
    },
    {
      title: 'Market Consolidation',
      description: 'Major acquisitions or mergers among competitors creating larger, more resourced competition',
      icon: <Shield className="w-6 h-6 text-green-600" />,
      likelihood: 'Medium' as const,
      impact: 'Medium' as const,
      responses: [
        'Identify strategic acquisition opportunities to strengthen position',
        'Emphasize agility and innovation advantages over larger competitors',
        'Develop contingency plans for key customer segments affected by mergers',
        'Create targeted campaigns during competitor transition periods'
      ],
      colorClass: 'bg-green-500',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {scenarios.map((scenario, index) => (
        <ScenarioCard
          key={index}
          title={scenario.title}
          description={scenario.description}
          icon={scenario.icon}
          likelihood={scenario.likelihood}
          impact={scenario.impact}
          responses={scenario.responses}
          colorClass={scenario.colorClass}
        />
      ))}
    </div>
  );
};
