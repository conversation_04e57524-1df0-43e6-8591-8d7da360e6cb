import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, TooltipProps } from 'recharts';

interface Competitor {
  name: string;
  type: 'direct' | 'adjacent' | 'partial' | 'emerging' | 'our-solution';
  x: number; // Automation score
  y: number; // Quality score
  description: string;
}

const CompetitivePositioningMap: React.FC = () => {
  const competitors: Competitor[] = [
    { name: 'Re<PERSON>ru<PERSON>', type: 'our-solution', x: 85, y: 80, description: 'End-to-end AI recruitment platform with real-time voice interviews, visual assessments, built-in coding IDE, and candidate LLM evaluation.' },
    { name: 'Workable', type: 'direct', x: 55, y: 45, description: 'HR software solution with AI-enhanced recruitment capabilities for SMBs to mid-market companies' },
    { name: 'HireVue', type: 'direct', x: 45, y: 35, description: 'Video interviewing platform with advanced AI analysis' },
    { name: 'Eightfold AI', type: 'direct', x: 70, y: 40, description: 'Comprehensive talent intelligence platform for large enterprises' },
    { name: 'Manatal', type: 'direct', x: 40, y: 55, description: 'Cost-effective recruitment software for small businesses' },
    { name: 'Greenhouse', type: 'adjacent', x: 35, y: 40, description: 'Major ATS with expanding AI capabilities' },
    { name: 'Lever', type: 'adjacent', x: 30, y: 30, description: 'Modern ATS with growing AI features' },
    { name: 'SmartRecruiters', type: 'adjacent', x: 45, y: 50, description: 'Enterprise ATS with AI enhancement' },
    { name: 'Workday', type: 'adjacent', x: 60, y: 60, description: 'HCM platform with recruitment module' },
    { name: 'LinkedIn Talent', type: 'adjacent', x: 40, y: 70, description: 'Network-based talent sourcing platform' },
    { name: 'SeekOut', type: 'partial', x: 65, y: 65, description: 'AI-driven talent sourcing engine' },
    { name: 'Beamery', type: 'partial', x: 55, y: 70, description: 'Talent engagement and CRM platform' },
    { name: 'Mercor', type: 'emerging', x: 75, y: 50, description: 'Technical recruitment with coding assessments' },
    { name: 'Sapia.ai', type: 'emerging', x: 80, y: 30, description: 'AI chat-based interview screening' },
    { name: 'TestGorilla', type: 'emerging', x: 60, y: 45, description: 'Skills-based assessment platform' },
  ];

  const customTooltip = ({ active, payload }: TooltipProps<number, string>) => {
    if (!active || !payload || !payload.length) {
      return null;
    }

    const competitor = competitors.find(c => c.name === payload[0].name);
    if (!competitor) return null;
    
    return (
      <div className="bg-white p-3 rounded-lg shadow-lg border border-gray-200">
        <p className="font-bold text-gray-800">{competitor.name}</p>
        <p className="text-sm text-gray-600">{competitor.description}</p>
        <div className="flex justify-between mt-2 text-xs text-gray-500">
          <span>Automation: {competitor.x}/100</span>
          <span>Quality: {competitor.y}/100</span>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full h-[600px] bg-white rounded-lg shadow-sm p-4">
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
          <CartesianGrid />
          <XAxis type="number" dataKey="x" name="Automation" unit="%" domain={[0, 100]} />
          <YAxis type="number" dataKey="y" name="Quality" unit="%" domain={[0, 100]} />
          <Tooltip content={customTooltip} />
          <Legend />
          <Scatter
            name="Direct Competitors"
            data={competitors.filter(c => c.type === 'direct')}
            fill="#FF4842"
          />
          <Scatter
            name="Adjacent Players"
            data={competitors.filter(c => c.type === 'adjacent')}
            fill="#54D62C"
          />
          <Scatter
            name="Partial Solutions"
            data={competitors.filter(c => c.type === 'partial')}
            fill="#FFC107"
          />
          <Scatter
            name="Emerging Players"
            data={competitors.filter(c => c.type === 'emerging')}
            fill="#00AB55"
          />
          <Scatter
            name="Recruiva"
            data={competitors.filter(c => c.type === 'our-solution')}
            fill="#1890FF"
          />
        </ScatterChart>
      </ResponsiveContainer>
    </div>
  );
};

export default CompetitivePositioningMap;
