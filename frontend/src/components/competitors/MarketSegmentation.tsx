import React from 'react';
import { Users, Building, Building2, Briefcase } from 'lucide-react';

type SegmentCardProps = {
  title: string;
  icon: React.ElementType;
  keyPlayers: string[];
  description: string;
  buyerPersonas: string[];
  keyNeeds: string[];
  buyingProcess: string;
  isHighlighted?: boolean;
  colorClass: string;
};

const SegmentCard: React.FC<SegmentCardProps> = ({
  title,
  icon: Icon,
  keyPlayers,
  description,
  buyerPersonas,
  keyNeeds,
  buyingProcess,
  isHighlighted = false,
  colorClass,
}) => {
  return (
    <div className={`bg-white rounded-xl shadow-lg overflow-hidden ${isHighlighted ? 'ring-2 ring-purple-500' : ''}`}>
      <div className={`h-2 ${colorClass}`}></div>
      <div className="p-6">
        <div className="flex items-center mb-4">
          <div className={`w-12 h-12 rounded-full ${colorClass} bg-opacity-20 flex items-center justify-center mr-4`}>
            <Icon className={`w-6 h-6 ${colorClass.replace('bg-', 'text-')}`} />
          </div>
          <h3 className="text-xl font-bold text-gray-800">{title}</h3>
        </div>
        
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-gray-500 mb-2">Key Players</h4>
          <div className="flex flex-wrap gap-2">
            {keyPlayers.map((player, index) => (
              <span 
                key={index} 
                className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                  player === 'Recruiva' 
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white' 
                    : 'bg-gray-100 text-gray-700'
                }`}
              >
                {player}
              </span>
            ))}
          </div>
        </div>
        
        <p className="text-gray-600 mb-4">{description}</p>
        
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-gray-500 mb-2">Buyer Personas</h4>
          <ul className="list-disc list-inside text-gray-700">
            {buyerPersonas.map((persona, index) => (
              <li key={index}>{persona}</li>
            ))}
          </ul>
        </div>
        
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-gray-500 mb-2">Key Needs</h4>
          <ul className="list-disc list-inside text-gray-700">
            {keyNeeds.map((need, index) => (
              <li key={index}>{need}</li>
            ))}
          </ul>
        </div>
        
        <div>
          <h4 className="text-sm font-semibold text-gray-500 mb-2">Typical Buying Process</h4>
          <p className="text-gray-700">{buyingProcess}</p>
        </div>
      </div>
    </div>
  );
};

export const MarketSegmentation: React.FC = () => {
  const segments = [
    {
      title: 'Large Enterprise (10,000+ employees)',
      icon: Building,
      keyPlayers: ['HireVue', 'Eightfold AI', 'Workday'],
      description: 'This segment values comprehensive functionality and enterprise-grade security, but often struggles with lengthy implementations and high costs.',
      buyerPersonas: ['Chief HR Officer', 'Global Talent Acquisition Director'],
      keyNeeds: ['Scalability', 'Compliance', 'Global standardization'],
      buyingProcess: '6-12 month sales cycle, multiple stakeholders',
      colorClass: 'bg-blue-500',
    },
    {
      title: 'Mid-Market (500-5,000 employees)',
      icon: Building2,
      keyPlayers: ['Recruiva', 'SmartRecruiters'],
      description: 'This is Recruiva&apos;s sweet spot - companies looking for advanced capabilities without enterprise complexity and costs.',
      buyerPersonas: ['Head of Talent Acquisition', 'VP of HR'],
      keyNeeds: ['Growth-focused recruitment', 'Efficiency', 'Quality hiring'],
      buyingProcess: '3-6 month sales cycle, senior HR + IT approval',
      isHighlighted: true,
      colorClass: 'bg-purple-500',
    },
    {
      title: 'SMB (10-500 employees)',
      icon: Briefcase,
      keyPlayers: ['Workable', 'Manatal', 'Lever'],
      description: 'Focuses on affordability and ease of use, but often lacks advanced AI capabilities for high-volume hiring.',
      buyerPersonas: ['HR Manager', 'Founder/CEO (in smaller companies)'],
      keyNeeds: ['Ease of use', 'Quick implementation', 'Cost-effectiveness'],
      buyingProcess: '1-3 month sales cycle, minimal approval layers',
      colorClass: 'bg-green-500',
    },
    {
      title: 'Pay-as-you-go Market (Startups & On-demand)',
      icon: Users,
      keyPlayers: ['Recruiva'],
      description: 'Startups and companies with irregular hiring needs seeking advanced technology without large ongoing commitments.',
      buyerPersonas: ['Startup Founders', 'HR Generalists'],
      keyNeeds: ['Flexibility', 'No long-term commitment', 'Advanced capabilities'],
      buyingProcess: 'Self-service or 1-month sales cycle',
      colorClass: 'bg-pink-500',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {segments.map((segment, index) => (
        <SegmentCard
          key={index}
          title={segment.title}
          icon={segment.icon}
          keyPlayers={segment.keyPlayers}
          description={segment.description}
          buyerPersonas={segment.buyerPersonas}
          keyNeeds={segment.keyNeeds}
          buyingProcess={segment.buyingProcess}
          isHighlighted={segment.isHighlighted}
          colorClass={segment.colorClass}
        />
      ))}
    </div>
  );
};
