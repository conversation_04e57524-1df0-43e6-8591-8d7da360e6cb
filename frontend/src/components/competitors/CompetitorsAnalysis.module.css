.section {
  position: relative;
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 4rem 0;
}

.sectionContent {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.gradientBorder {
  position: relative;
  border-radius: 0.75rem;
  padding: 1px;
  background: linear-gradient(to right, #8884d8, #4a72e8, #ec5fe3);
}

.gradientBorder > div {
  background: white;
  border-radius: 0.75rem;
}

.competitorCard {
  transition: all 0.3s ease;
}

.competitorCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.directBadge {
  background-color: rgba(74, 114, 232, 0.15);
  color: #4a72e8;
}

.adjacentBadge {
  background-color: rgba(34, 197, 94, 0.15);
  color: #22c55e;
}

.partialBadge {
  background-color: rgba(234, 179, 8, 0.15);
  color: #eab308;
}

.emergingBadge {
  background-color: rgba(236, 95, 227, 0.15);
  color: #ec5fe3;
}

.featureTable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 0.5rem;
  overflow: hidden;
}

.featureTable th,
.featureTable td {
  padding: 0.75rem 1rem;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
}

.featureTable th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #4b5563;
}

.featureTable tr:last-child td {
  border-bottom: none;
}

.featureTable tr:nth-child(even) {
  background-color: #f9fafb;
}

.recruivaColumn {
  background-color: rgba(136, 132, 216, 0.1);
}

.checkmark {
  color: #22c55e;
}

.xmark {
  color: #ef4444;
}

.quadrantMap {
  position: relative;
  width: 100%;
  height: 500px;
  background-color: #f9fafb;
  border-radius: 0.75rem;
  overflow: hidden;
}

.quadrantLabel {
  position: absolute;
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
}

.quadrantLabel.topLeft {
  top: 1rem;
  left: 1rem;
}

.quadrantLabel.topRight {
  top: 1rem;
  right: 1rem;
}

.quadrantLabel.bottomLeft {
  bottom: 1rem;
  left: 1rem;
}

.quadrantLabel.bottomRight {
  bottom: 1rem;
  right: 1rem;
}

.competitor {
  position: absolute;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.competitor:hover {
  transform: translate(-50%, -50%) scale(1.1);
  z-index: 10;
}

.competitorTooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  border-radius: 0.5rem;
  padding: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  width: 200px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 20;
}

.competitor:hover .competitorTooltip {
  opacity: 1;
  visibility: visible;
}

.axis {
  position: absolute;
  background-color: #d1d5db;
}

.xAxis {
  width: 100%;
  height: 1px;
  top: 50%;
  left: 0;
}

.yAxis {
  width: 1px;
  height: 100%;
  top: 0;
  left: 50%;
}

.axisLabel {
  position: absolute;
  font-size: 0.75rem;
  color: #6b7280;
}

.xAxisLabel {
  bottom: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
}

.yAxisLabel {
  top: 50%;
  left: 0.5rem;
  transform: translateY(-50%) rotate(-90deg);
  transform-origin: left center;
}

.networkDiagram {
  position: relative;
  width: 100%;
  height: 600px;
  background-color: #f9fafb;
  border-radius: 0.75rem;
  overflow: hidden;
}

.node {
  position: absolute;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.node:hover {
  transform: translate(-50%, -50%) scale(1.1);
  z-index: 10;
}

.nodeLine {
  position: absolute;
  background-color: #d1d5db;
  transform-origin: 0 0;
  z-index: 1;
}

.nodeLabel {
  position: absolute;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

.timeline {
  position: relative;
  padding: 2rem 0;
}

.timelineItem {
  position: relative;
  padding-left: 2rem;
  margin-bottom: 2rem;
}

.timelineItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: #8884d8;
}

.timelineItem::after {
  content: '';
  position: absolute;
  top: 1rem;
  left: 0.5rem;
  width: 1px;
  height: calc(100% + 1rem);
  background-color: #d1d5db;
}

.timelineItem:last-child::after {
  display: none;
}

.timelineDate {
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.timelineContent {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.recruivaInnovation {
  background-color: rgba(136, 132, 216, 0.1);
  border-left: 3px solid #8884d8;
}

@media (max-width: 768px) {
  .section {
    padding: 2rem 0;
  }
  
  .sectionContent {
    padding: 0 1rem;
  }
  
  .quadrantMap {
    height: 400px;
  }
  
  .networkDiagram {
    height: 500px;
  }
}
