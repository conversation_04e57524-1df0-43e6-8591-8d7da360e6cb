import React from 'react';
import { Check, X, ArrowR<PERSON>, Code } from 'lucide-react';

export const MercorAnalysis: React.FC = () => {
  const strengths = [
    'Strong technical assessment capabilities',
    'Growing recognition in tech recruiting',
    'Superior technical candidate experience',
    'Strong brand among developers'
  ];

  const weaknesses = [
    'Limited to technical roles',
    'No end-to-end process automation',
    'Lacks voice AI capabilities',
    'No visual assessment capabilities',
    'Limited AI integration in coding IDE',
    'No candidate LLM evaluation'
  ];

  const responseStrategies = [
    'Target Mercor clients seeking broader capabilities beyond tech roles',
    'Highlight Recruiva&apos;s technical assessment capabilities with demo comparisons',
    'Develop tech-specific marketing materials emphasizing superior overall value'
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div className="md:col-span-1">
        <div className="bg-white rounded-xl shadow-lg overflow-hidden h-full">
          <div className="bg-gradient-to-r from-blue-500 to-purple-500 h-2"></div>
          <div className="p-6">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                <Code className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-800">Mercor</h3>
            </div>
            
            <p className="text-gray-600 mb-6">
              Technical recruitment platform focusing on coding assessments and technical screening
            </p>
            
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-semibold text-gray-500 mb-2">Focus Area</h4>
                <p className="font-medium text-gray-800">Technical Recruitment</p>
              </div>
              
              <div>
                <h4 className="text-sm font-semibold text-gray-500 mb-2">Target Market</h4>
                <p className="font-medium text-gray-800">Tech Companies</p>
              </div>
              
              <div>
                <h4 className="text-sm font-semibold text-gray-500 mb-2">Pricing</h4>
                <p className="font-medium text-gray-800">Not publicly available</p>
              </div>
              
              <div>
                <h4 className="text-sm font-semibold text-gray-500 mb-2">Market Presence</h4>
                <p className="font-medium text-gray-800">North America, tech hubs</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="md:col-span-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="bg-green-500 h-2"></div>
            <div className="p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">Strengths</h3>
              <ul className="space-y-3">
                {strengths.map((strength, index) => (
                  <li key={index} className="flex items-start">
                    <Check className="w-5 h-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">{strength}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="bg-red-500 h-2"></div>
            <div className="p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">Weaknesses</h3>
              <ul className="space-y-3">
                {weaknesses.map((weakness, index) => (
                  <li key={index} className="flex items-start">
                    <X className="w-5 h-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">{weakness}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg overflow-hidden md:col-span-2">
            <div className="bg-purple-500 h-2"></div>
            <div className="p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">Recruiva&apos;s Advantage</h3>
              <p className="text-gray-700 mb-6">
                While Mercor excels in technical assessments, Recruiva offers a broader solution that handles all role types with comprehensive end-to-end automation including advanced voice AI capabilities, visual assessments, and a built-in coding IDE with AI assistance. Recruiva can integrate coding assessments while also automating the entire recruitment workflow and evaluating candidates&apos; AI usage.
              </p>
              
              <h3 className="text-lg font-bold text-gray-800 mb-4">Competitor Response Strategy</h3>
              <ul className="space-y-3">
                {responseStrategies.map((strategy, index) => (
                  <li key={index} className="flex items-start">
                    <ArrowRight className="w-5 h-5 text-purple-500 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">{strategy}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
