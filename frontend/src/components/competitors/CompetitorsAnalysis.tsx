'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';

// Add interface at the top of the file
interface CompetitorCardProps {
  name: string;
  description: string;
  type: 'direct' | 'adjacent' | 'emerging';
  keyDifferentiator?: string;
  targetSize?: string;
  startingPrice?: string;
  features?: Array<{name: string; value: boolean | string}>;
  geographicStrength?: string;
  customerSatisfaction?: string;
  inView?: boolean;
  delay?: number;
}

// Create dummy components for all the missing imports
const CompetitorCard = ({
  name,
  description,
  type,
  keyDifferentiator,
  targetSize,
  startingPrice,
  geographicStrength,
  customerSatisfaction
}: CompetitorCardProps) => (
  <div className="bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-500 hover:shadow-xl">
    <div className="p-6">
      <h3 className="text-xl font-bold mb-2">{name}</h3>
      <p className="text-sm text-gray-600 mb-4">{description}</p>
      <span className="inline-block px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">{type}</span>
      {keyDifferentiator && <p className="mt-2 text-sm">Key Differentiator: {keyDifferentiator}</p>}
      {targetSize && <p className="text-sm">Target Size: {targetSize}</p>}
      {startingPrice && <p className="text-sm">Starting Price: {startingPrice}</p>}
      {geographicStrength && <p className="text-sm">Geographic Strength: {geographicStrength}</p>}
      {customerSatisfaction && <p className="text-sm">Customer Satisfaction: {customerSatisfaction}</p>}
    </div>
  </div>
);

const FeatureComparisonTable = () => (
  <div className="overflow-x-auto">
    <table className="min-w-full bg-white">
      <thead>
        <tr>
          <th className="py-3 px-4 text-left">Feature</th>
          <th className="py-3 px-4 text-center">Recruiva</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td className="py-3 px-4 border-t">Example Feature</td>
          <td className="py-3 px-4 border-t text-center">✓</td>
        </tr>
      </tbody>
    </table>
  </div>
);

const CompetitivePositioningMap = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Competitive Positioning Map</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Positioning Map Visualization</p>
    </div>
  </div>
);

const CompetitiveEcosystem = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Competitive Ecosystem</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Ecosystem Visualization</p>
    </div>
  </div>
);

const MarketSegmentation = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Market Segmentation</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Segmentation Visualization</p>
    </div>
  </div>
);

const PriceFeatureMatrix = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Price-Feature Matrix</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Matrix Visualization</p>
    </div>
  </div>
);

const MercorAnalysis = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Mercor Analysis</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Analysis Visualization</p>
    </div>
  </div>
);

const FeatureAdoptionTimeline = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Feature Adoption Timeline</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Timeline Visualization</p>
    </div>
  </div>
);

const StrategicRecommendations = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Strategic Recommendations</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Recommendations Visualization</p>
    </div>
  </div>
);

const ImplementationTimeline = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Implementation Timeline</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Timeline Visualization</p>
    </div>
  </div>
);

const CompetitiveStrategy = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Competitive Strategy</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Strategy Visualization</p>
    </div>
  </div>
);

const GlobalMarketPresence = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Global Market Presence</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Market Presence Visualization</p>
    </div>
  </div>
);

const SecurityCompliance = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Security & Compliance</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Security Visualization</p>
    </div>
  </div>
);

const ContingencyPlanning = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Contingency Planning</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Planning Visualization</p>
    </div>
  </div>
);

const CustomerTestimonials = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Customer Testimonials</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Testimonials Visualization</p>
    </div>
  </div>
);

const ProductRoadmap = () => (
  <div className="bg-white p-6 rounded-xl shadow-lg">
    <h3 className="text-xl font-bold mb-4">Product Roadmap</h3>
    <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
      <p>Roadmap Visualization</p>
    </div>
  </div>
);

// Main App Component
export default function CompetitorsAnalysis() {
  const [scrollY, setScrollY] = useState(0);

  // Refs for scroll animation triggers
  const sectionRefs = {
    hero: useRef(null),
    positioning: useRef(null),
    competitors: useRef(null),
    features: useRef(null),
    ecosystem: useRef(null),
    segmentation: useRef(null),
    priceValue: useRef(null),
    mercor: useRef(null),
    timeline: useRef(null),
    recommendations: useRef(null),
    implementation: useRef(null),
    strategy: useRef(null),
    global: useRef(null),
    security: useRef(null),
    contingency: useRef(null),
    testimonials: useRef(null),
    roadmap: useRef(null)
  };

  // Handle scroll events for parallax and animations
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Check if element is in viewport
  const useInView = (ref: React.RefObject<HTMLElement>, options = {}) => {
    const [isInView, setIsInView] = useState(false);

    useEffect(() => {
      if (!ref.current) return;

      const observer = new IntersectionObserver(([entry]) => {
        setIsInView(entry.isIntersecting);
      }, options);

      observer.observe(ref.current);

      // Store the current value of ref.current in a variable
      const currentRef = ref.current;

      return () => {
        if (currentRef) {
          observer.unobserve(currentRef);
        }
      };
    }, [ref, options]);

    return isInView;
  };

  // Generate animated values for each section
  const positioningInView = useInView(sectionRefs.positioning);
  const competitorsInView = useInView(sectionRefs.competitors);
  const featuresInView = useInView(sectionRefs.features);
  const ecosystemInView = useInView(sectionRefs.ecosystem);
  const segmentationInView = useInView(sectionRefs.segmentation);
  const priceValueInView = useInView(sectionRefs.priceValue);
  const mercorInView = useInView(sectionRefs.mercor);
  const timelineInView = useInView(sectionRefs.timeline);
  const recommendationsInView = useInView(sectionRefs.recommendations);
  const implementationInView = useInView(sectionRefs.implementation);
  const strategyInView = useInView(sectionRefs.strategy);
  const globalInView = useInView(sectionRefs.global);
  const securityInView = useInView(sectionRefs.security);
  const contingencyInView = useInView(sectionRefs.contingency);
  const testimonialsInView = useInView(sectionRefs.testimonials);
  const roadmapInView = useInView(sectionRefs.roadmap);

  return (
    <div className="w-full overflow-x-hidden">
      {/* Hero Section */}
      <section
        ref={sectionRefs.hero}
        className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-800 via-blue-900 to-purple-900 text-white overflow-hidden"
        style={{ transform: `translateY(${scrollY * 0.1}px)` }}
      >
        <div
          className="absolute inset-0 bg-gradient-to-br from-gray-800 via-blue-900 to-purple-900 opacity-70"
          style={{ transform: `translateY(${scrollY * 0.2}px)` }}
        ></div>

        <div className="relative z-10 text-center px-4 max-w-4xl mx-auto" style={{ transform: `translateY(${-scrollY * 0.1}px)` }}>
          <h1 className="text-5xl md:text-7xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Recruiva Competitive Landscape
          </h1>
          <h2 className="text-xl md:text-3xl mb-6 font-light">
            Market Intelligence Report | March 2025
          </h2>
          <p className="text-lg md:text-xl mb-12 font-medium">
            Comprehensive analysis of the AI recruitment technology market
          </p>

          <div className="animate-bounce mt-16">
            <svg className="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>
      </section>

      {/* Competitive Positioning Map */}
      <section
        ref={sectionRefs.positioning}
        className="relative z-20 py-16 bg-white"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Competitive Positioning Map
          </h2>

          <div className={`transform ${positioningInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <CompetitivePositioningMap />

            <div className="mt-8 bg-gray-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-3 text-gray-800">Key Insight</h3>
              <p className="text-gray-700">
                Recruiva occupies a unique position with industry-leading automation while maintaining high-quality interactions through its voice AI technology. No current competitor offers the combination of real-time voice AI interviews with adaptive questioning across the entire recruitment lifecycle.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Top Competitor Analysis */}
      <section
        ref={sectionRefs.competitors}
        className="relative z-20 py-16 bg-gray-50"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Top Competitor Analysis
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Competitor cards will be rendered here */}
            <CompetitorCard
              name="Workable"
              type="direct"
              description="HR software solution with AI-enhanced recruitment capabilities"
              keyDifferentiator="Ease of Use & Comprehensive Features"
              targetSize="10-500 employees"
              startingPrice="$169/month"
              features={[
                { name: "AI Role Definition", value: true },
                { name: "AI Resume Analysis", value: true },
                { name: "Voice AI", value: false },
                { name: "Adaptive Questioning", value: false },
                { name: "Automated Evaluation", value: "via integrations" },
                { name: "Bias Reduction", value: true }
              ]}
              geographicStrength="Global, strong in SMB market"
              customerSatisfaction="4.5/5 (G2)"
              inView={competitorsInView}
              delay={0}
            />

            <CompetitorCard
              name="HireVue"
              type="direct"
              description="Video interviewing platform with advanced AI analysis"
              keyDifferentiator="AI-Powered Video Analysis"
              targetSize="10,000+ employees"
              startingPrice="$2,916/month"
              features={[
                { name: "AI Role Definition", value: false },
                { name: "AI Resume Analysis", value: true },
                { name: "Voice AI", value: false },
                { name: "Adaptive Questioning", value: "Video-only analysis" },
                { name: "Automated Evaluation", value: true },
                { name: "Bias Reduction", value: true }
              ]}
              geographicStrength="North America, Europe"
              customerSatisfaction="4.3/5 (G2)"
              inView={competitorsInView}
              delay={100}
            />

            <CompetitorCard
              name="Eightfold AI"
              type="direct"
              description="Comprehensive talent intelligence platform for large enterprises"
              keyDifferentiator="Skills-Based Talent Intelligence"
              targetSize="10,000+ employees"
              startingPrice="$650/month"
              features={[
                { name: "AI Role Definition", value: true },
                { name: "AI Resume Analysis", value: true },
                { name: "Voice AI", value: false },
                { name: "Adaptive Questioning", value: false },
                { name: "Automated Evaluation", value: true },
                { name: "Bias Reduction", value: true }
              ]}
              geographicStrength="Global, enterprise focus"
              customerSatisfaction="4.4/5 (G2)"
              inView={competitorsInView}
              delay={200}
            />

            <CompetitorCard
              name="Manatal"
              type="direct"
              description="Cost-effective recruitment software for small businesses"
              keyDifferentiator="Affordability & Simplicity"
              targetSize="SMBs, Agencies"
              startingPrice="$15-19/month"
              features={[
                { name: "AI Role Definition", value: false },
                { name: "AI Resume Analysis", value: true },
                { name: "Voice AI", value: false },
                { name: "Adaptive Questioning", value: false },
                { name: "Automated Evaluation", value: true },
                { name: "Bias Reduction", value: true }
              ]}
              geographicStrength="Asia-Pacific, growing globally"
              customerSatisfaction="4.6/5 (G2)"
              inView={competitorsInView}
              delay={300}
            />

            <CompetitorCard
              name="Greenhouse"
              type="adjacent"
              description="Major ATS with expanding AI capabilities"
              keyDifferentiator=""
              targetSize="100-1,000+ employees"
              startingPrice="$500+/month"
              features={[]}
              geographicStrength="North America, Europe"
              customerSatisfaction="4.5/5 (G2)"
              inView={competitorsInView}
              delay={400}
            />

            <CompetitorCard
              name="Lever"
              type="adjacent"
              description="Modern ATS with growing AI features"
              keyDifferentiator=""
              targetSize="50-500+ employees"
              startingPrice="$300+/month"
              features={[]}
              geographicStrength="North America"
              customerSatisfaction="4.4/5 (G2)"
              inView={competitorsInView}
              delay={500}
            />

            <CompetitorCard
              name="SmartRecruiters"
              type="adjacent"
              description="Enterprise ATS with AI enhancement"
              keyDifferentiator=""
              targetSize="1,000+ employees"
              startingPrice="Custom pricing"
              features={[]}
              geographicStrength="Global, enterprise focus"
              customerSatisfaction="4.3/5 (G2)"
              inView={competitorsInView}
              delay={600}
            />

            <CompetitorCard
              name="LinkedIn Talent"
              type="adjacent"
              description="Network-based talent sourcing platform"
              keyDifferentiator=""
              targetSize="All segments"
              startingPrice="$120+/month per seat"
              features={[]}
              geographicStrength="Global, strongest in professional roles"
              customerSatisfaction="4.2/5 (G2)"
              inView={competitorsInView}
              delay={700}
            />

            <CompetitorCard
              name="Mercor"
              type="emerging"
              description="Technical recruitment with coding assessments"
              keyDifferentiator="Specialized technical assessment"
              targetSize="Tech companies"
              startingPrice="Not publicly available"
              features={[]}
              geographicStrength="North America, tech hubs"
              customerSatisfaction="Not available"
              inView={competitorsInView}
              delay={800}
            />
          </div>
        </div>
      </section>

      {/* Feature Comparison Table */}
      <section
        ref={sectionRefs.features}
        className="relative z-20 py-16 bg-white"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Feature Comparison
          </h2>

          <div className={`transform ${featuresInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <div className="overflow-x-auto">
              <FeatureComparisonTable />
            </div>

            <div className="mt-8 bg-gray-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-3 text-gray-800">Key Insight</h3>
              <p className="text-gray-700">
                Recruiva is the only platform offering real-time voice AI interviews with adaptive questioning capabilities across the entire recruitment process. With its additional AI-powered assessments, built-in IDE, and LLM evaluation capabilities, it delivers the most comprehensive AI recruitment solution on the market. While competitors excel in specific areas, none provide the end-to-end intelligent automation that Recruiva offers.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Competitive Ecosystem Visualization */}
      <section
        ref={sectionRefs.ecosystem}
        className="relative z-20 py-16 bg-gray-50"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Competitive Ecosystem
          </h2>

          <div className={`transform ${ecosystemInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <CompetitiveEcosystem />
          </div>
        </div>
      </section>

      {/* Market Segmentation Analysis */}
      <section
        ref={sectionRefs.segmentation}
        className="relative z-20 py-16 bg-white"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Market Segmentation Analysis
          </h2>

          <div className={`transform ${segmentationInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <MarketSegmentation />
          </div>
        </div>
      </section>

      {/* Price-to-Feature Value Matrix */}
      <section
        ref={sectionRefs.priceValue}
        className="relative z-20 py-16 bg-gray-50"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Price-to-Feature Value Matrix
          </h2>

          <div className={`transform ${priceValueInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <PriceFeatureMatrix />
          </div>
        </div>
      </section>

      {/* Mercor Analysis */}
      <section
        ref={sectionRefs.mercor}
        className="relative z-20 py-16 bg-white"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Mercor Analysis (Emerging Competitor)
          </h2>

          <div className={`transform ${mercorInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <MercorAnalysis />
          </div>
        </div>
      </section>

      {/* Feature Adoption Timeline */}
      <section
        ref={sectionRefs.timeline}
        className="relative z-20 py-16 bg-gray-50"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Feature Adoption Timeline
          </h2>

          <div className={`transform ${timelineInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <FeatureAdoptionTimeline />
          </div>
        </div>
      </section>

      {/* Strategic Recommendations */}
      <section
        ref={sectionRefs.recommendations}
        className="relative z-20 py-16 bg-white"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Strategic Recommendations
          </h2>

          <div className={`transform ${recommendationsInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <StrategicRecommendations />
          </div>
        </div>
      </section>

      {/* Implementation Timeline */}
      <section
        ref={sectionRefs.implementation}
        className="relative z-20 py-16 bg-gray-50"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Implementation Timeline
          </h2>

          <div className={`transform ${implementationInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <ImplementationTimeline />
          </div>
        </div>
      </section>

      {/* Competitive Strategy Implementation */}
      <section
        ref={sectionRefs.strategy}
        className="relative z-20 py-16 bg-white"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Competitive Strategy Implementation
          </h2>

          <div className={`transform ${strategyInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <CompetitiveStrategy />
          </div>
        </div>
      </section>

      {/* Global Market Presence */}
      <section
        ref={sectionRefs.global}
        className="relative z-20 py-16 bg-gray-50"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Global Market Presence
          </h2>

          <div className={`transform ${globalInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <GlobalMarketPresence />
          </div>
        </div>
      </section>

      {/* Security & Compliance Comparison */}
      <section
        ref={sectionRefs.security}
        className="relative z-20 py-16 bg-white"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Security & Compliance Comparison
          </h2>

          <div className={`transform ${securityInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <SecurityCompliance />
          </div>
        </div>
      </section>

      {/* Contingency Planning */}
      <section
        ref={sectionRefs.contingency}
        className="relative z-20 py-16 bg-gray-50"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Contingency Planning
          </h2>

          <div className={`transform ${contingencyInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <ContingencyPlanning />
          </div>
        </div>
      </section>

      {/* Customer Testimonials and Case Studies */}
      <section
        ref={sectionRefs.testimonials}
        className="relative z-20 py-16 bg-white"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Customer Testimonials and Case Studies
          </h2>

          <div className={`transform ${testimonialsInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <CustomerTestimonials />
          </div>
        </div>
      </section>

      {/* Product Roadmap */}
      <section
        ref={sectionRefs.roadmap}
        className="relative z-20 py-16 bg-gray-50"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
            Product Roadmap
          </h2>

          <div className={`transform ${roadmapInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
            <ProductRoadmap />
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gradient-to-br from-gray-800 via-blue-900 to-purple-900 text-white py-12">
        <div className="container mx-auto px-4 text-center">
          <Link href="/" className="inline-block transition-transform hover:scale-105">
            <h2 className="text-3xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
              Recruiva
            </h2>
          </Link>
          <p className="mb-6">AI-Driven Recruitment Automation</p>
          <div className="flex justify-center space-x-6">
            <Link href="/pitch" className="text-white hover:text-purple-300 transition-colors">
              Pitch Deck
            </Link>
            <Link href="/financials" className="text-white hover:text-purple-300 transition-colors">
              Financials
            </Link>
            <Link href="/" className="text-white hover:text-purple-300 transition-colors">
              Home
            </Link>
          </div>
          <p className="mt-8 text-sm opacity-70">&copy; 2025 Recruiva. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
}
