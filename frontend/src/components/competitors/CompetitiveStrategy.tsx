import React from 'react';
import { Shield, Target, Zap, Award, AlertTriangle } from 'lucide-react';

type StrategyCardProps = {
  title: string;
  description: string;
  icon: React.ReactNode;
  strengths: string[];
  weaknesses: string[];
  colorClass: string;
};

const StrategyCard: React.FC<StrategyCardProps> = ({
  title,
  description,
  icon,
  strengths,
  weaknesses,
  colorClass,
}) => {
  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      <div className={`h-2 ${colorClass}`}></div>
      <div className="p-6">
        <div className="flex items-center mb-4">
          <div className={`w-12 h-12 rounded-full ${colorClass} bg-opacity-20 flex items-center justify-center mr-4`}>
            {icon}
          </div>
          <h3 className="text-xl font-bold text-gray-800">{title}</h3>
        </div>
        
        <p className="text-gray-600 mb-6">{description}</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <Award className="w-5 h-5 text-green-500 mr-2" />
              <h4 className="font-semibold text-gray-800">Strengths</h4>
            </div>
            <ul className="space-y-2">
              {strengths.map((strength, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  <span className="text-gray-700 text-sm">{strength}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div className="bg-red-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
              <h4 className="font-semibold text-gray-800">Challenges</h4>
            </div>
            <ul className="space-y-2">
              {weaknesses.map((weakness, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-red-500 mr-2">•</span>
                  <span className="text-gray-700 text-sm">{weakness}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export const CompetitiveStrategy: React.FC = () => {
  const strategies = [
    {
      title: 'Differentiation Strategy',
      description: 'Focus on unique capabilities that competitors cannot easily replicate',
      icon: <Award className="w-6 h-6 text-purple-600" />,
      strengths: [
        'Unique voice AI capabilities',
        'End-to-end automation',
        'Built-in coding IDE with AI',
        'Candidate LLM evaluation'
      ],
      weaknesses: [
        'Higher price point',
        'Less brand recognition',
        'Newer market entrant'
      ],
      colorClass: 'bg-purple-500',
    },
    {
      title: 'Market Segmentation',
      description: 'Target mid-market companies seeking enterprise capabilities without complexity',
      icon: <Target className="w-6 h-6 text-blue-600" />,
      strengths: [
        'Ideal for 500-5,000 employee companies',
        'Flexible pricing models',
        'Simpler implementation than enterprise solutions',
        'Enterprise-grade capabilities'
      ],
      weaknesses: [
        'Competition from both enterprise and SMB solutions',
        'Market education required',
        'Segment has budget constraints'
      ],
      colorClass: 'bg-blue-500',
    },
    {
      title: 'Technology Leadership',
      description: 'Maintain innovation edge through continuous AI advancement',
      icon: <Zap className="w-6 h-6 text-amber-600" />,
      strengths: [
        'Advanced AI capabilities',
        'Rapid development cycle',
        'Strong technical team',
        'Cutting-edge voice and visual AI'
      ],
      weaknesses: [
        'Resource-intensive R&D',
        'Larger competitors with bigger budgets',
        'Fast-evolving AI landscape'
      ],
      colorClass: 'bg-amber-500',
    },
    {
      title: 'Defensive Positioning',
      description: 'Protect market position against emerging threats and larger competitors',
      icon: <Shield className="w-6 h-6 text-green-600" />,
      strengths: [
        'Comprehensive feature set',
        'Strong ROI justification',
        'Unique value proposition',
        'Pay-as-you-go flexibility'
      ],
      weaknesses: [
        'Mercor\'s technical recruitment focus',
        'Enterprise vendors moving downmarket',
        'Low-cost alternatives for budget-conscious buyers'
      ],
      colorClass: 'bg-green-500',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      {strategies.map((strategy, index) => (
        <StrategyCard
          key={index}
          title={strategy.title}
          description={strategy.description}
          icon={strategy.icon}
          strengths={strategy.strengths}
          weaknesses={strategy.weaknesses}
          colorClass={strategy.colorClass}
        />
      ))}
    </div>
  );
};
