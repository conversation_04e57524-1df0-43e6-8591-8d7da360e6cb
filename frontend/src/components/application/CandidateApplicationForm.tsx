'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/Form';
import { Input } from '@/components/ui/Input';
import { FileUpload } from '@/components/ui/FileUpload';
import { CandidateApplication } from '@/types/role';
import { uploadResume, checkExistingApplication, saveApplication } from '@/lib/firebase';
import { ResumeEvaluationResponse, BasicEvaluationResponse, resumeService } from '@/services/resume';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import { Loader2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/Alert';
import { EvaluationFeedback } from './EvaluationFeedback';
import { cn } from '@/lib/utils';

// Define the form schema
const formSchema = z.object({
  fullName: z.string().min(2, {
    message: 'Full name must be at least 2 characters.',
  }),
  email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  phoneNumber: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface CandidateApplicationFormProps {
  roleId: string;
  jobPosting?: string;
  roleTitle?: string;
  onComplete: (applicationData: {
    applicationId: string;
    evaluation?: ResumeEvaluationResponse | BasicEvaluationResponse;
    isPassed?: boolean;
  }) => void;
  onCancel?: () => void;
  evaluateResume?: boolean;
  useBasicEvaluation?: boolean;
  noCard?: boolean;
}

/**
 * CandidateApplicationForm component
 * Form for candidates to fill out when applying for a role
 */
export function CandidateApplicationForm({
  roleId,
  jobPosting,
  roleTitle = 'this position',
  onComplete,
  onCancel,
  evaluateResume = false,
  useBasicEvaluation = true,
  noCard = false
}: CandidateApplicationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [resumeError, setResumeError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<'form' | 'evaluating' | 'results'>('form');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [evaluation, setEvaluation] = useState<ResumeEvaluationResponse | BasicEvaluationResponse | null>(null);
  const [evaluationError, setEvaluationError] = useState<string | null>(null);
  const [applicationId, setApplicationId] = useState<string>('');
  const [existingAppError, setExistingAppError] = useState<string | null>(null);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: '',
      email: '',
      phoneNumber: '',
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    try {
      setIsSubmitting(true);
      setEvaluationError(null);
      setExistingAppError(null);

      // Validate resume
      if (!resumeFile) {
        setResumeError('Please upload your resume');
        setIsSubmitting(false);
        return;
      }

      // Check if candidate has already applied for this role
      const existingApplication = await checkExistingApplication(roleId, data.email);

      if (existingApplication.exists) {
        setExistingAppError(
          `You have already applied for ${roleTitle}. You can apply for other roles or wait until your current application is closed before reapplying.`
        );
        setIsSubmitting(false);
        return;
      }

      // Create application data
      const applicationData: Omit<CandidateApplication, 'id' | 'roleId' | 'created_at' | 'updated_at'> = {
        fullName: data.fullName,
        email: data.email,
        phoneNumber: data.phoneNumber || '',
        resumeUrl: '', // Will be updated after upload
        linkedInUrl: '',
        githubUrl: '',
        portfolioUrl: '',
        status: 'applied', // Set initial status to 'applied'
      };

      // Save application to get ID
      let applicationId;
      try {
        applicationId = await saveApplication(roleId, applicationData);
        console.log('Application saved successfully with ID:', applicationId);
        setApplicationId(applicationId);

        // Store candidate email in localStorage for reference in interview session
        localStorage.setItem('candidateEmail', data.email);
        localStorage.setItem('candidateName', data.fullName);
      } catch (error) {
        console.error('Error saving application:', error);
        form.setError('root', {
          type: 'manual',
          message: 'Failed to submit application. Please try again.'
        });
        setIsSubmitting(false);
        return;
      }

      // Upload resume and update application
      try {
        const resumeUrl = await uploadResume(roleId, resumeFile, applicationId);
        console.log('Resume uploaded successfully:', resumeUrl);
      } catch (error) {
        console.error('Error uploading resume:', error);
        // Continue anyway - we already have the application saved
      }

      // If we need to evaluate the resume
      if (evaluateResume) {
        setCurrentStep('evaluating');

        // Evaluate the resume using the resume service
        try {
          const result = await resumeService.evaluateResumeWithUpload(
            resumeFile,
            roleId,
            jobPosting,
            {
              applicationId: applicationId,
              basicEvaluation: useBasicEvaluation,
              onProgress: (progress) => setUploadProgress(progress),
              saveToFirebase: true,
              storeEvaluationResults: true
            }
          );

          // Store the results
          setEvaluation(result.evaluation);

          // Move to results step
          setCurrentStep('results');
        } catch (evalError) {
          console.error('Error evaluating resume:', evalError);
          setEvaluationError('We encountered an error evaluating your resume. Please try again.');
          setCurrentStep('form');
        }
      } else {
        // No evaluation needed, proceed directly
        onComplete({ applicationId });
      }

      setIsSubmitting(false);
    } catch (error) {
      console.error('Error in application submission process:', error);
      form.setError('root', {
        type: 'manual',
        message: 'Failed to submit application. Please try again.'
      });
      setIsSubmitting(false);
    }
  };

  // Handle continuing after evaluation
  const handleContinue = () => {
    if (evaluation) {
      // Determine if the evaluation passed
      const isPassed = 'scorecard' in evaluation
        ? evaluation.recommendation.decision === 'PASS'
        : evaluation.decision === 'PASS';

      // Call onComplete with evaluation results
      onComplete({
        applicationId,
        evaluation,
        isPassed
      });
    } else {
      // Just pass the applicationId if no evaluation
      onComplete({ applicationId });
    }
  };

  // Handle starting over
  const handleStartOver = () => {
    // Reset form and state
    form.reset();
    setResumeFile(null);
    setResumeError(null);
    setEvaluation(null);
    setEvaluationError(null);
    setCurrentStep('form');
  };

  // Render based on current step
  if (currentStep === 'evaluating') {
    const content = (
      <div className="flex flex-col items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-center text-sm text-gray-500">
          {uploadProgress < 100
            ? `Uploading resume (${uploadProgress}%)...`
            : 'Evaluating qualifications...'}
        </p>
      </div>
    );

    if (noCard) {
      return content;
    }

    return (
      <Card className="w-full max-w-xl mx-auto backdrop-blur-[6px] backdrop-saturate-[1.4] bg-white/70 border-slate-200/70 dark:bg-slate-800/60 dark:border-slate-700/40 dark:shadow-xl dark:shadow-slate-950/20">
        <CardHeader>
          <CardTitle>Evaluating Your Resume</CardTitle>
          <CardDescription>
            We&apos;re analyzing your resume against the job requirements.
            This should only take a moment.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {content}
        </CardContent>
      </Card>
    );
  }

  if (currentStep === 'results' && evaluation) {
    return (
      <div>
        <EvaluationFeedback
          evaluation={evaluation}
          onContinue={handleContinue}
          onStartOver={handleStartOver}
          roleTitle={roleTitle}
          candidateName={form.getValues().fullName}
          className="max-w-[110%] mx-auto"
        />
      </div>
    );
  }

  // Default to form
  const formContent = (
    <>
      {existingAppError && (
        <div className="mb-4 rounded-md overflow-hidden border border-slate-300 dark:border-slate-800 backdrop-blur-sm">
          <div className="bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-pink-500/10 dark:from-indigo-500/20 dark:via-purple-500/20 dark:to-pink-500/20 p-4">
            <div className="flex gap-3 items-start">
              <div className="rounded-full bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 p-1.5 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-slate-900">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 16v-4"></path>
                  <path d="M12 8h.01"></path>
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400">Application Status</h4>
                <p className="text-sm text-slate-700 dark:text-slate-300 mt-1">You have already applied for {roleTitle}. You can apply for other roles or wait until your current application is closed before reapplying.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {evaluationError && (
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{evaluationError}</AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name *</FormLabel>
                <FormControl>
                  <Input placeholder="John Doe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email *</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field: { onChange, value } }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <PhoneInput
                    international
                    countryCallingCodeEditable={false}
                    defaultCountry="US"
                    value={value}
                    onChange={onChange}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormItem>
            <FormLabel>Resume *</FormLabel>
            <FormControl>
              <FileUpload
                onChange={(file: File | null) => {
                  setResumeFile(file);
                  setResumeError(null);
                }}
                accept=".pdf,.doc,.docx"
                error={resumeError || undefined}
              />
            </FormControl>
            <FormDescription>
              Please upload your resume in PDF, DOC, or DOCX format. Maximum size: 10MB.
            </FormDescription>
          </FormItem>

          <div className="pt-4 flex justify-between">
            {onCancel && (
              <Button
                type="button"
                variant="secondary"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting} className={cn("font-medium", onCancel ? '' : 'ml-auto')}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                'Start Your Interview'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );

  if (noCard) {
    return formContent;
  }

  return (
    <Card className="w-full max-w-xl mx-auto backdrop-blur-[6px] backdrop-saturate-[1.4] bg-white/70 border-slate-200/70 dark:bg-slate-800/60 dark:border-slate-700/40 dark:shadow-xl dark:shadow-slate-950/20">
      <CardHeader>
        <CardTitle>Apply for Instant Interview</CardTitle>
        <CardDescription>
          Please fill out the form below to apply for this position.
          All fields marked with * are required.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {formContent}
      </CardContent>
    </Card>
  );
}