import React from 'react';
import { ResumeEvaluationResponse, BasicEvaluationResponse } from '@/services/resume';
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ChevronRightIcon, 
  CheckIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  DocumentCheckIcon,
  CalendarIcon,
  ChartBarIcon
} from '@heroicons/react/24/solid';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { motion } from 'framer-motion';
import { useTheme } from 'next-themes';

export type EvaluationFeedbackProps = {
  evaluation: ResumeEvaluationResponse | BasicEvaluationResponse;
  onContinue?: () => void; 
  onStartOver?: () => void;
  roleTitle?: string;
  candidateName?: string;
  className?: string;
  compact?: boolean;
}

/**
 * EvaluationFeedback component for displaying resume evaluation results to candidates
 * with enhanced visual styling and animations for Recruiva's themes
 */
export const EvaluationFeedback: React.FC<EvaluationFeedbackProps> = ({
  evaluation,
  onContinue,
  onStartOver,
  roleTitle = 'this role',
  candidateName,
  className = '',
  compact = false
}) => {
  // Add theme support
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  
  // Determine if this is a detailed evaluation or basic evaluation
  const isDetailed = 'scorecard' in evaluation;
  const isPassed = isDetailed 
    ? evaluation.recommendation.decision === 'PASS'
    : evaluation.decision === 'PASS';
  
  const firstName = candidateName ? candidateName.split(' ')[0] : 'there';
  const greeting = `Hi ${firstName},`;

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.5,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };
  
  // Calculate score percentages for visual elements
  const getScorePercentage = (score: number) => (score / 5) * 100;
  
  // Get score value based on evaluation type
  const overallScore = isDetailed 
    ? evaluation.scorecard.overallScore
    : evaluation.overallScore;
  
  const overallScorePercentage = getScorePercentage(overallScore);
  
  // Determine score color based on theme
  const getScoreColor = (score: number) => {
    if (isDarkMode) {
      if (score >= 4) return 'text-emerald-400';
      if (score >= 3) return 'text-blue-400'; 
      if (score >= 2) return 'text-amber-400';
      return 'text-rose-400';
    } else {
      // Light theme colors
      if (score >= 4) return 'text-emerald-600';
      if (score >= 3) return 'text-blue-600'; 
      if (score >= 2) return 'text-amber-600';
      return 'text-rose-600';
    }
  };
  
  const getProgressColor = (score: number) => {
    if (isDarkMode) {
      if (score >= 4) return 'bg-emerald-500';
      if (score >= 3) return 'bg-blue-500'; 
      if (score >= 2) return 'bg-amber-500';
      return 'bg-rose-500';
    } else {
      // Light theme colors
      if (score >= 4) return 'bg-emerald-500';
      if (score >= 3) return 'bg-blue-500'; 
      if (score >= 2) return 'bg-amber-500';
      return 'bg-rose-500';
    }
  };

  // Get date for timestamp
  const today = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  
  // Render different content based on pass/fail and evaluation type
  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className={`w-full max-w-3xl mx-auto ${className}`}
    >
      <Card className={isDarkMode ? 
        "backdrop-blur-[6px] backdrop-saturate-[1.4] bg-slate-800/60 border-slate-700/40 shadow-xl shadow-slate-950/20" : 
        "shadow-xl border border-slate-200 bg-white overflow-hidden"}>
        <CardHeader className={`${isPassed ? 
          (isDarkMode ? 
            'bg-gradient-to-r from-slate-800/80 to-emerald-900/50 border-b border-slate-700/40' : 
            'bg-gradient-to-r from-slate-50 to-emerald-100 border-b border-emerald-200'
          ) : 
          (isDarkMode ? 
            'bg-gradient-to-r from-slate-800/80 to-rose-900/50 border-b border-slate-700/40' : 
            'bg-gradient-to-r from-slate-50 to-rose-100 border-b border-rose-200'
          )} pb-6`}>
          <motion.div variants={itemVariants} className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center space-x-3 mb-3 sm:mb-0">
              <div className={`rounded-full p-2 ${isPassed ? 
                (isDarkMode ? 
                  'bg-emerald-950 border border-emerald-700' : 
                  'bg-emerald-100 border border-emerald-300'
                ) : 
                (isDarkMode ? 
                  'bg-rose-950 border border-rose-700' : 
                  'bg-rose-100 border border-rose-300'
                )}`}>
                {isPassed ? (
                  <CheckCircleIcon className={isDarkMode ? "h-8 w-8 text-emerald-500" : "h-8 w-8 text-emerald-600"} />
                ) : (
                  <XCircleIcon className={isDarkMode ? "h-8 w-8 text-rose-500" : "h-8 w-8 text-rose-600"} />
                )}
              </div>
              <div>
                <CardTitle className={`text-xl ${isPassed ? 
                  (isDarkMode ? 'text-emerald-400' : 'text-emerald-600') : 
                  (isDarkMode ? 'text-rose-400' : 'text-rose-600')}`}>
                  {isPassed ? 'Great Match!' : 'Not Quite a Match'}
                </CardTitle>
                <CardDescription className={isDarkMode ? "text-sm text-slate-400" : "text-sm text-slate-600"}>
                  Resume Analysis
                </CardDescription>
              </div>
            </div>
            
            <div className="flex items-center space-x-1">
              <CalendarIcon className={isDarkMode ? "h-4 w-4 text-slate-500" : "h-4 w-4 text-slate-500"} />
              <span className={isDarkMode ? "text-xs text-slate-500" : "text-xs text-slate-500"}>{today}</span>
            </div>
          </motion.div>

          <motion.div variants={itemVariants} className="mt-4">
            <div className={`p-3 rounded-lg ${isDarkMode ? 
              'bg-slate-800/60 backdrop-blur-[6px] backdrop-saturate-[1.4] border-slate-700/40 border shadow-sm' : 
              'bg-white bg-opacity-70 backdrop-blur-sm border border-slate-200 shadow-sm'}`}>
              <p className={`text-base font-medium mb-1 ${isDarkMode ? 'text-white' : 'text-slate-800'}`}>
                {greeting} Here&apos;s your resume evaluation for <span className="font-semibold italic">{roleTitle}</span>.
              </p>
              <p className={isDarkMode ? "text-slate-300 text-sm leading-relaxed" : "text-sm text-slate-600"}>
                We&apos;ve analyzed your qualifications against the job requirements.
              </p>
            </div>
          </motion.div>
        </CardHeader>
        
        <CardContent className={`pt-6 space-y-6 ${isDarkMode ? 'bg-slate-800/60 backdrop-blur-[6px] backdrop-saturate-[1.4]' : 'bg-slate-50'}`}>
          {/* Score overview */}
          <motion.div variants={itemVariants} className={`flex flex-col sm:flex-row items-center justify-between gap-5 ${isDarkMode ? 
            'bg-slate-800/70 p-4 rounded-xl shadow-md border border-slate-700/40' : 
            'bg-white p-4 rounded-xl shadow-md border border-slate-200'}`}>
            <div className={`flex items-center justify-center rounded-full h-24 w-24 shadow-inner relative ${isDarkMode ? 
              'border-4 border-slate-700/40 bg-slate-800/60' : 
              'border-4 border-slate-200 bg-white'}`}>
              <span className={`text-xl font-bold ${getScoreColor(overallScore)}`}>{overallScore}/5</span>
              <div className="absolute inset-0 rounded-full overflow-hidden">
                <div 
                  className={`absolute bottom-0 left-0 right-0 ${getProgressColor(overallScore)}`} 
                  style={{ height: `${overallScorePercentage}%`, opacity: '0.3' }}
                ></div>
              </div>
            </div>
            
            <div className="flex-1 space-y-2 w-full">
              <p className={`text-lg font-bold text-center sm:text-left ${isDarkMode ? 'text-white' : 'text-slate-800'}`}>Overall Match</p>
              <Progress value={overallScorePercentage} className={isDarkMode ? "h-3 bg-slate-700/40" : "h-3 bg-slate-200"} indicatorClassName={getProgressColor(overallScore)} />
              <div className="flex justify-between text-xs text-slate-500">
                <span>Not a Match</span>
                <span>Perfect Match</span>
              </div>
            </div>
          </motion.div>

          {/* Main decision and reasoning */}
          <motion.div variants={itemVariants} className={isDarkMode ? 
            'bg-slate-800/70 p-5 rounded-xl shadow-md border border-slate-700/40' : 
            'bg-white p-5 rounded-xl shadow-md border border-slate-200'}>
            <div className="flex items-start gap-3 mb-3">
              <DocumentCheckIcon className={`h-6 w-6 mt-1 ${isPassed ? 
                (isDarkMode ? 'text-emerald-500' : 'text-emerald-600') : 
                (isDarkMode ? 'text-amber-500' : 'text-amber-600')}`} />
              <div>
                <p className={`text-lg font-semibold mb-2 ${isPassed ? 
                  (isDarkMode ? 'text-emerald-400' : 'text-emerald-600') : 
                  (isDarkMode ? 'text-rose-400' : 'text-rose-600')}`}>
                  {isPassed 
                    ? `Your profile shows a strong match for ${roleTitle}.` 
                    : `Your profile doesn&apos;t fully align with the requirements for ${roleTitle}.`
                  }
                </p>
                <p className={isDarkMode ? "text-slate-300 text-sm leading-relaxed" : "text-slate-700 text-sm leading-relaxed"}>
                  {isDetailed 
                    ? evaluation.recommendation.reasoning 
                    : evaluation.reasoning
                  }
                </p>
              </div>
            </div>
            
            {/* Confidence level */}
            <div className="flex items-center gap-2 mt-4 pl-9">
              <span className={isDarkMode ? "text-sm font-medium text-slate-400" : "text-sm font-medium text-slate-600"}>Confidence:</span>
              <Badge className={`font-medium ml-1 ${isPassed ? 
                (isDarkMode ? 'bg-emerald-950 text-emerald-400 border-emerald-700' : 'bg-emerald-100 text-emerald-700 border-emerald-300') : 
                (isDarkMode ? 'bg-amber-950 text-amber-400 border-amber-700' : 'bg-amber-100 text-amber-700 border-amber-300')}`}>
                {isDetailed ? evaluation.recommendation.confidence : evaluation.confidence}
              </Badge>
            </div>
          </motion.div>
          
          {!compact && (
            <>              
              {/* Key strengths section */}
              <motion.div variants={itemVariants} className={isDarkMode ? 
                'bg-slate-950 p-5 rounded-xl shadow-md border border-slate-800' : 
                'bg-white p-5 rounded-xl shadow-md border border-slate-200'}>
                <h3 className={`text-base font-semibold flex items-center gap-2 mb-3 ${isDarkMode ? 
                  'text-emerald-400' : 'text-emerald-600'}`}>
                  <div className={isDarkMode ? 
                    'bg-emerald-950 p-1 rounded-full border border-emerald-800' : 
                    'bg-emerald-100 p-1 rounded-full border border-emerald-300'}>
                    <CheckIcon className={isDarkMode ? "h-5 w-5 text-emerald-500" : "h-5 w-5 text-emerald-600"} />
                  </div>
                  Key Strengths
                </h3>
                <ul className="space-y-2 pl-2">
                  {isDetailed 
                    ? evaluation.feedback.candidateStrengths.map((strength, i) => (
                        <motion.li 
                          key={i} 
                          className={`flex items-start gap-2 text-sm ${isDarkMode ? 'text-slate-300' : 'text-slate-700'}`}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 * i }}
                        >
                          <CheckIcon className={`h-4 w-4 mt-1 flex-shrink-0 ${isDarkMode ? 'text-emerald-500' : 'text-emerald-600'}`} />
                          <span>{strength}</span>
                        </motion.li>
                      ))
                    : evaluation.keyStrengths.map((strength, i) => (
                        <motion.li 
                          key={i} 
                          className={`flex items-start gap-2 text-sm ${isDarkMode ? 'text-slate-300' : 'text-slate-700'}`}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 * i }}
                        >
                          <CheckIcon className={`h-4 w-4 mt-1 flex-shrink-0 ${isDarkMode ? 'text-emerald-500' : 'text-emerald-600'}`} />
                          <span>{strength}</span>
                        </motion.li>
                      ))
                  }
                </ul>
              </motion.div>
              
              {/* Improvement areas or gaps section */}
              <motion.div variants={itemVariants} className={isDarkMode ? 
                'bg-slate-950 p-5 rounded-xl shadow-md border border-slate-800' : 
                'bg-white p-5 rounded-xl shadow-md border border-slate-200'}>
                <h3 className={`text-base font-semibold flex items-center gap-2 mb-3 ${isDarkMode ? 
                  'text-amber-400' : 'text-amber-600'}`}>
                  <div className={isDarkMode ? 
                    'bg-amber-950 p-1 rounded-full border border-amber-800' : 
                    'bg-amber-100 p-1 rounded-full border border-amber-300'}>
                    <ExclamationTriangleIcon className={isDarkMode ? "h-5 w-5 text-amber-500" : "h-5 w-5 text-amber-600"} />
                  </div>
                  {isPassed ? 'Areas for Improvement' : 'Key Gaps'}
                </h3>
                <ul className="space-y-2 pl-2">
                  {isDetailed 
                    ? evaluation.feedback.improvementAreas.map((area, i) => (
                        <motion.li 
                          key={i} 
                          className={`flex items-start gap-2 text-sm ${isDarkMode ? 'text-slate-300' : 'text-slate-700'}`}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 * i }}
                        >
                          <ExclamationTriangleIcon className={`h-4 w-4 mt-1 flex-shrink-0 ${isDarkMode ? 'text-amber-500' : 'text-amber-600'}`} />
                          <span>{area}</span>
                        </motion.li>
                      ))
                    : evaluation.keyGaps.map((gap, i) => (
                        <motion.li 
                          key={i} 
                          className={`flex items-start gap-2 text-sm ${isDarkMode ? 'text-slate-300' : 'text-slate-700'}`}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 * i }}
                        >
                          <ExclamationTriangleIcon className={`h-4 w-4 mt-1 flex-shrink-0 ${isDarkMode ? 'text-amber-500' : 'text-amber-600'}`} />
                          <span>{gap}</span>
                        </motion.li>
                      ))
                  }
                </ul>
              </motion.div>
              
              {/* Detailed scores section */}
              {isDetailed && (
                <motion.div variants={itemVariants} className={isDarkMode ? 
                  'bg-gradient-to-r from-slate-950 to-indigo-950 p-5 rounded-xl shadow-md border border-indigo-900' : 
                  'bg-gradient-to-r from-slate-50 to-indigo-100 p-5 rounded-xl shadow-md border border-indigo-200'}>
                  <h3 className={`text-base font-semibold flex items-center gap-2 mb-4 ${isDarkMode ? 'text-indigo-400' : 'text-indigo-600'}`}>
                    <ChartBarIcon className={isDarkMode ? "h-5 w-5 text-indigo-500" : "h-5 w-5 text-indigo-600"} />
                    Detailed Assessment
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between mb-1">
                          <p className={isDarkMode ? "text-sm font-medium text-slate-300" : "text-sm font-medium text-slate-700"}>Technical Skills</p>
                          <Badge variant="outline" className={`font-medium ${isDarkMode ? 'border-slate-700' : 'border-slate-300'} ${getScoreColor(evaluation.scorecard.technicalSkills.score)}`}>
                            {evaluation.scorecard.technicalSkills.score}/5
                          </Badge>
                        </div>
                        <Progress value={getScorePercentage(evaluation.scorecard.technicalSkills.score)} 
                          className={isDarkMode ? "h-2 bg-slate-800" : "h-2 bg-slate-200"} 
                          indicatorClassName={getProgressColor(evaluation.scorecard.technicalSkills.score)} 
                        />
                      </div>
                      
                      <div>
                        <div className="flex justify-between mb-1">
                          <p className={isDarkMode ? "text-sm font-medium text-slate-300" : "text-sm font-medium text-slate-700"}>Soft Skills</p>
                          <Badge variant="outline" className={`font-medium ${isDarkMode ? 'border-slate-700' : 'border-slate-300'} ${getScoreColor(evaluation.scorecard.softSkills.score)}`}>
                            {evaluation.scorecard.softSkills.score}/5
                          </Badge>
                        </div>
                        <Progress value={getScorePercentage(evaluation.scorecard.softSkills.score)} 
                          className={isDarkMode ? "h-2 bg-slate-800" : "h-2 bg-slate-200"} 
                          indicatorClassName={getProgressColor(evaluation.scorecard.softSkills.score)} 
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between mb-1">
                          <p className={isDarkMode ? "text-sm font-medium text-slate-300" : "text-sm font-medium text-slate-700"}>Experience Relevance</p>
                          <Badge variant="outline" className={`font-medium ${isDarkMode ? 'border-slate-700' : 'border-slate-300'} ${getScoreColor(evaluation.scorecard.experienceRelevance.score)}`}>
                            {evaluation.scorecard.experienceRelevance.score}/5
                          </Badge>
                        </div>
                        <Progress value={getScorePercentage(evaluation.scorecard.experienceRelevance.score)} 
                          className={isDarkMode ? "h-2 bg-slate-800" : "h-2 bg-slate-200"} 
                          indicatorClassName={getProgressColor(evaluation.scorecard.experienceRelevance.score)} 
                        />
                      </div>
                      
                      <div>
                        <div className="flex justify-between mb-1">
                          <p className={isDarkMode ? "text-sm font-medium text-slate-300" : "text-sm font-medium text-slate-700"}>Education & Certifications</p>
                          <Badge variant="outline" className={`font-medium ${isDarkMode ? 'border-slate-700' : 'border-slate-300'} ${getScoreColor(evaluation.scorecard.educationCertifications.score)}`}>
                            {evaluation.scorecard.educationCertifications.score}/5
                          </Badge>
                        </div>
                        <Progress value={getScorePercentage(evaluation.scorecard.educationCertifications.score)} 
                          className={isDarkMode ? "h-2 bg-slate-800" : "h-2 bg-slate-200"} 
                          indicatorClassName={getProgressColor(evaluation.scorecard.educationCertifications.score)} 
                        />
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </>
          )}
        </CardContent>
        
        <CardFooter className={`flex flex-col sm:flex-row gap-3 pt-4 pb-6 px-6 ${isPassed ? 
          (isDarkMode ? 'bg-slate-900/50 border-t border-emerald-900/50' : 'bg-slate-50/50 border-t border-emerald-200/50') : 
          (isDarkMode ? 'bg-slate-900/50 border-t border-slate-800' : 'bg-slate-50/50 border-t border-slate-200')}`}>
          {isPassed && onContinue && (
            <motion.div variants={itemVariants} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="w-full sm:w-auto">
              <Button onClick={onContinue} className={`w-full h-12 text-base font-medium shadow-md hover:shadow-lg transition-all border-0 ${isDarkMode ? 
                'bg-gradient-to-r from-emerald-700 to-emerald-600 hover:from-emerald-800 hover:to-emerald-700' : 
                'bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-700 hover:to-emerald-600'}`}>
                Continue to Interview
                <ChevronRightIcon className="ml-2 h-5 w-5" />
              </Button>
            </motion.div>
          )}
          
          {!isPassed && onStartOver && (
            <motion.div variants={itemVariants} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="w-full sm:w-auto">
              <Button onClick={onStartOver} variant="secondary" className={`w-full h-12 text-base font-medium shadow-md ${isDarkMode ? 
                'bg-slate-800 hover:bg-slate-700 text-white border-slate-700' : 
                'bg-slate-200 hover:bg-slate-300 text-slate-800 border-slate-300'}`}>
                <ArrowPathIcon className="mr-2 h-5 w-5" />
                Start Over
              </Button>
            </motion.div>
          )}
          
          {!isPassed && onContinue && (
            <motion.div variants={itemVariants} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="w-full sm:w-auto">
              <Button onClick={onContinue} className={`w-full h-12 text-base font-medium shadow-md hover:shadow-lg transition-all border-0 ${isDarkMode ? 
                'bg-gradient-to-r from-indigo-700 to-indigo-600 hover:from-indigo-800 hover:to-indigo-700' : 
                'bg-gradient-to-r from-indigo-600 to-indigo-500 hover:from-indigo-700 hover:to-indigo-600'}`}>
                Continue Anyway
                <ChevronRightIcon className="ml-2 h-5 w-5" />
              </Button>
            </motion.div>
          )}
        </CardFooter>
      </Card>
    </motion.div>
  );
};

/**
 * Variant that only shows when evaluation passes
 */
export const SuccessEvaluationFeedback: React.FC<EvaluationFeedbackProps> = (props) => {
  // Check if evaluation passed
  const isPassed = 'scorecard' in props.evaluation 
    ? props.evaluation.recommendation.decision === 'PASS' 
    : props.evaluation.decision === 'PASS';
  
  // Only render if passed
  return isPassed ? <EvaluationFeedback {...props} /> : null;
};

/**
 * Variant that only shows when evaluation fails
 */
export const FailureEvaluationFeedback: React.FC<EvaluationFeedbackProps> = (props) => {
  // Check if evaluation failed
  const isFailed = 'scorecard' in props.evaluation 
    ? props.evaluation.recommendation.decision === 'FAIL' 
    : props.evaluation.decision === 'FAIL';
  
  // Only render if failed
  return isFailed ? <EvaluationFeedback {...props} /> : null;
};

export default EvaluationFeedback; 