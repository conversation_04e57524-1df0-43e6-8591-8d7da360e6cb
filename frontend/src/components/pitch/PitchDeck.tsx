'use client';

import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip, ResponsiveContainer } from 'recharts';
import { ChevronDown, ArrowUp, ArrowDown, Activity, Users, Clock, DollarSign, Award, Target, Layers, Zap, Pie<PERSON><PERSON> as Pie<PERSON><PERSON><PERSON><PERSON>, BarChart2, Check, X, Video } from 'lucide-react';
import styles from './PitchDeck.module.css';
import Link from 'next/link';

// Custom gradient text component for titles
const GradientText = ({ children, className = "" }: { children: React.ReactNode; className?: string }) => (
    <span
        className={`bg-clip-text text-transparent bg-gradient-to-r from-purple-600 via-blue-500 to-pink-500 ${className}`}
        style={{ backgroundSize: '200% auto' }}
    >
        {children}
    </span>
);

// Chart data

const marketData = [
    { name: 'TAM', value: 150, fullName: 'Total Addressable Market' },
    { name: 'SAM', value: 24, fullName: 'Serviceable Available Market' },
    { name: 'SOM', value: 2.8, fullName: 'Serviceable Obtainable Market' },
];

// Financial Projection Data

// Color schemes
const COLORS = ['#8884d8', '#4a72e8', '#8459e9', '#5e48e0', '#ec5fe3'];

// Parallax section component
const ParallaxSection = React.forwardRef<HTMLDivElement, {
    children: React.ReactNode;
    bgColor?: string;
}>(({ children, bgColor = "bg-white" }, ref) => {
    return (
        <div ref={ref} className={`${styles.section} ${bgColor}`}>
            <div className={styles.sectionContent}>
                {children}
            </div>
        </div>
    );
});

ParallaxSection.displayName = 'ParallaxSection';

// Feature card component
const FeatureCard = ({ icon: Icon, title, description }: {
    icon: React.ElementType;
    title: string;
    description: string;
}) => (
    <div className="bg-white bg-opacity-90 backdrop-blur-md rounded-xl shadow-xl p-6 transition-all duration-300 hover:shadow-2xl hover:scale-105">
        <div className="bg-gradient-to-r from-purple-600 to-blue-500 rounded-full w-12 h-12 flex items-center justify-center mb-4">
            <Icon className="w-6 h-6 text-white" />
        </div>
        <h3 className="font-bold text-lg mb-2 text-gray-800">{title}</h3>
        <p className="text-gray-600">{description}</p>
    </div>
);

const PitchDeck = () => {
    const [currentSlide, setCurrentSlide] = useState(0);
    const totalSlides = 11;
    const sectionRefs = useRef<Array<HTMLDivElement | null>>([]);
    const containerRef = useRef<HTMLDivElement>(null);
    const isNavigatingRef = useRef(false);

    // Initialize section refs
    useEffect(() => {
        sectionRefs.current = Array(totalSlides).fill(null);
    }, []);

    const setRef = (index: number) => (el: HTMLDivElement | null) => {
        if (sectionRefs.current) {
            sectionRefs.current[index] = el;
        }
    };

    const navigateTo = (slideIndex: number) => {
        if (isNavigatingRef.current) return;

        isNavigatingRef.current = true;
        setCurrentSlide(slideIndex);

        // Use setTimeout to ensure state update has been processed
        setTimeout(() => {
            if (sectionRefs.current[slideIndex]) {
                sectionRefs.current[slideIndex]?.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }

            // Reset the navigation lock after a short delay
            setTimeout(() => {
                isNavigatingRef.current = false;
            }, 100);
        }, 50);
    };

    const next = () => {
        if (currentSlide < totalSlides - 1) {
            navigateTo(currentSlide + 1);
        }
    };

    const prev = () => {
        if (currentSlide > 0) {
            navigateTo(currentSlide - 1);
        }
    };

    // Page indicators (arrows)
    const PageIndicators = () => (
        <div className={styles.pageIndicators}>
            <button
                className={`p-2 rounded-full bg-gradient-to-r from-purple-600/85 to-blue-500/85 backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300 ${currentSlide === 0 ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'
                    }`}
                onClick={prev}
                disabled={currentSlide === 0}
                aria-label="Previous slide"
            >
                <ArrowUp className="w-6 h-6 text-white" />
            </button>
            <div className="text-center font-medium bg-gradient-to-r from-purple-600/85 to-blue-500/85 backdrop-blur-md rounded-full px-4 py-2 text-white shadow-lg border border-white/20">
                {currentSlide + 1}/{totalSlides}
            </div>
            <button
                className={`p-2 rounded-full bg-gradient-to-r from-purple-600/85 to-blue-500/85 backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300 ${currentSlide === totalSlides - 1 ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'
                    }`}
                onClick={next}
                disabled={currentSlide === totalSlides - 1}
                aria-label="Next slide"
            >
                <ArrowDown className="w-6 h-6 text-white" />
            </button>
        </div>
    );

    // Set up scroll behavior and scroll event listener
    useEffect(() => {
        const container = containerRef.current;
        if (!container) return;

        container.style.scrollBehavior = 'smooth';

        // Function to update current slide based on scroll position
        const handleScroll = () => {
            if (isNavigatingRef.current) return;

            const containerHeight = container.clientHeight;

            // Find which section is most visible in the viewport
            let newCurrentSlide = 0;
            let maxVisibleArea = 0;

            sectionRefs.current.forEach((section, index) => {
                if (!section) return;

                const rect = section.getBoundingClientRect();
                const sectionTop = rect.top;
                const sectionBottom = rect.bottom;

                // Calculate how much of the section is visible
                const visibleTop = Math.max(0, sectionTop);
                const visibleBottom = Math.min(containerHeight, sectionBottom);
                const visibleArea = Math.max(0, visibleBottom - visibleTop);

                if (visibleArea > maxVisibleArea) {
                    maxVisibleArea = visibleArea;
                    newCurrentSlide = index;
                }
            });

            if (newCurrentSlide !== currentSlide) {
                setCurrentSlide(newCurrentSlide);
            }
        };

        // Add scroll event listener with throttling
        let scrollTimeout: NodeJS.Timeout;
        const throttledScrollHandler = () => {
            if (scrollTimeout) clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(handleScroll, 100);
        };

        container.addEventListener('scroll', throttledScrollHandler);

        return () => {
            container.removeEventListener('scroll', throttledScrollHandler);
            if (scrollTimeout) clearTimeout(scrollTimeout);
        };
    }, [currentSlide]);

    // Process comparison data
    interface ProcessStep {
        step: string;
        traditional: number;
        traditionalGap: number;
        recruiva: number;
        recruivaGap: number;
        traditionalUnit: string;
        recruivaUnit: string;
    }

    const processComparisonData: ProcessStep[] = [
        {
            step: 'Job Definition',
            traditional: 1, // days
            traditionalGap: 0, // days
            recruiva: 15, // minutes
            recruivaGap: 0, // minutes
            traditionalUnit: 'days',
            recruivaUnit: 'minutes'
        },
        {
            step: 'Resume Screening',
            traditional: 14, // days
            traditionalGap: 7, // days
            recruiva: 0, // minutes
            recruivaGap: 0, // minutes
            traditionalUnit: 'days',
            recruivaUnit: 'minutes'
        },
        {
            step: 'Interview 1',
            traditional: 1, // hour
            traditionalGap: 7, // days
            recruiva: 60, // minutes (part of adaptive interview)
            recruivaGap: 0, // minutes
            traditionalUnit: 'hours',
            recruivaUnit: 'minutes'
        },
        {
            step: 'Interview 2',
            traditional: 1, // hour
            traditionalGap: 7, // days
            recruiva: 60, // minutes (part of adaptive interview)
            recruivaGap: 0, // minutes
            traditionalUnit: 'hours',
            recruivaUnit: 'minutes'
        },
        {
            step: 'Interview 3',
            traditional: 1, // hour
            traditionalGap: 7, // days
            recruiva: 60, // minutes (part of adaptive interview)
            recruivaGap: 0, // minutes
            traditionalUnit: 'hours',
            recruivaUnit: 'minutes'
        },
        {
            step: 'Evaluation',
            traditional: 7, // days
            traditionalGap: 0, // days
            recruiva: 1, // minute
            recruivaGap: 0, // minutes
            traditionalUnit: 'days',
            recruivaUnit: 'minute'
        }
    ];

    // Calculate total time for both processes (in days)


    // Calculate scheduling burden for traditional process (15 hours per role)

    return (
        <div ref={containerRef} className={styles.pitchContainer}>
            {/* Hide PageIndicators on mobile devices */}
            <div className="hidden md:block">
                <PageIndicators />
            </div>

            {/* 1. Introduction */}
            <ParallaxSection bgColor="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900" ref={setRef(0)}>
                <div className="flex flex-col items-center justify-center text-center">
                    <Link href="/" className="transition-transform hover:scale-105">
                        <h1 className="text-6xl md:text-7xl font-bold mb-6 leading-tight font-cal">
                            <GradientText>Recruiva</GradientText>
                        </h1>
                    </Link>
                    <p className="text-white text-2xl mb-8 max-w-3xl">AI-Driven Recruitment Automation: From Role Definition to Hire</p>
                    <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6 inline-block">
                        <div className="space-y-2">
                            <p className="text-white text-lg">Siavash Fahimi, Technical Founder and CEO</p>
                            <p className="text-white text-sm opacity-90"><EMAIL></p>
                        </div>
                    </div>
                    <div className="absolute bottom-12 animate-bounce">
                        <ChevronDown className="w-8 h-8 text-white opacity-70" />
                    </div>
                </div>
            </ParallaxSection>

            {/* 2. Problem */}
            <ParallaxSection bgColor="bg-gradient-to-br from-purple-50 via-blue-50 to-orange-50" ref={setRef(1)}>
                <div>
                    <h2 className="text-4xl md:text-5xl font-bold mb-10 text-center">
                        <GradientText>The Problem</GradientText>
                    </h2>

                    <div className="max-w-5xl mx-auto mb-8 text-center">
                        <p className="text-xl text-gray-700 mb-6">Traditional recruitment is broken and resource intensive</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 max-w-6xl mx-auto">
                        {/* Time Card */}
                        <div className="bg-white rounded-xl shadow-lg p-8 flex flex-col items-center text-center transform transition-all duration-300 hover:scale-105">
                            <div className="bg-purple-100 p-4 rounded-full mb-4">
                                <Clock className="w-12 h-12 text-purple-600" />
                            </div>
                            <h3 className="text-2xl font-bold mb-2 text-gray-800">50+ Days</h3>
                            <div className="w-16 h-1 bg-purple-500 rounded-full mb-4"></div>
                            <p className="text-gray-600">Average time-to-hire</p>
                        </div>

                        {/* Cost Card */}
                        <div className="bg-white rounded-xl shadow-lg p-8 flex flex-col items-center text-center transform transition-all duration-300 hover:scale-105">
                            <div className="bg-blue-100 p-4 rounded-full mb-4">
                                <DollarSign className="w-12 h-12 text-blue-600" />
                            </div>
                            <h3 className="text-2xl font-bold mb-2 text-gray-800">$4,700+</h3>
                            <div className="w-16 h-1 bg-blue-500 rounded-full mb-4"></div>
                            <p className="text-gray-600">Cost per hire, not including opportunity cost</p>
                        </div>

                        {/* Quality Card */}
                        <div className="bg-white rounded-xl shadow-lg p-8 flex flex-col items-center text-center transform transition-all duration-300 hover:scale-105">
                            <div className="bg-red-100 p-4 rounded-full mb-4">
                                <Activity className="w-12 h-12 text-red-600" />
                            </div>
                            <h3 className="text-2xl font-bold mb-2 text-gray-800">64%</h3>
                            <div className="w-16 h-1 bg-red-500 rounded-full mb-4"></div>
                            <p className="text-gray-600">Bad hire rate</p>
                        </div>
                    </div>

                    {/* Pain Points */}
                    <div className="bg-white bg-opacity-90 backdrop-blur-sm rounded-xl shadow-xl p-8 max-w-4xl mx-auto">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="flex flex-col items-center text-center p-4 rounded-lg bg-purple-100 shadow-sm">
                                <Users className="w-8 h-8 text-purple-600 mb-2" />
                                <p className="text-sm font-semibold text-purple-800">Candidate Dropout</p>
                            </div>
                            <div className="flex flex-col items-center text-center p-4 rounded-lg bg-blue-100 shadow-sm">
                                <Layers className="w-8 h-8 text-blue-600 mb-2" />
                                <p className="text-sm font-semibold text-blue-800">Biased Decisions</p>
                            </div>
                            <div className="flex flex-col items-center text-center p-4 rounded-lg bg-orange-100 shadow-sm">
                                <Clock className="w-8 h-8 text-orange-600 mb-2" />
                                <p className="text-sm font-semibold text-orange-800">Scheduling Chaos</p>
                            </div>
                            <div className="flex flex-col items-center text-center p-4 rounded-lg bg-red-100 shadow-sm">
                                <DollarSign className="w-8 h-8 text-red-600 mb-2" />
                                <p className="text-sm font-semibold text-red-800">Lost Productivity</p>
                            </div>
                        </div>
                    </div>
                </div>
            </ParallaxSection>

            {/* 3. Solution */}
            <ParallaxSection bgColor="bg-gradient-to-br from-purple-900 via-indigo-800 to-blue-900" ref={setRef(2)}>
                <div>
                    <h2 className="text-4xl md:text-5xl font-bold mb-10 text-center text-white">
                        <GradientText>Our Solution</GradientText>
                    </h2>
                    <div className="text-center mb-13">
                        <p className="text-white text-base mb-8 mx-auto max-w-3xl">An end-to-end AI recruitment platform that automates the entire hiring proccess with multimodal AI interviews.</p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <FeatureCard
                            icon={Zap}
                            title="AI-Led Role Definition"
                            description="95% time savings in defining job requirements"
                        />
                        <FeatureCard
                            icon={Target}
                            title="Instant Interview to Apply"
                            description="Cutting weeks of bottleneck in the hiring funnel"
                        />
                        <FeatureCard
                            icon={Users}
                            title="Advanced Multimodal Interviews"
                            description="Realtime Video, Voice, Chat, Code and Virtual whiteboarding"
                        />
                        <FeatureCard
                            icon={Award}
                            title="Fully Autonomous Candidate Progression"
                            description="Applicant Tracking System (ATS) with auto-progression"
                        />
                    </div>
                    <div className="mt-16 bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-8 text-white">
                        <h3 className="text-2xl font-bold mb-6">Key Benefits</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="flex items-center space-x-4">
                                <Clock className="w-10 h-10 text-pink-400 flex-shrink-0" />
                                <p>Cuts time-to-hire from 50+ days to 10+ days</p>
                            </div>
                            <div className="flex items-center space-x-4">
                                <DollarSign className="w-10 h-10 text-pink-400 flex-shrink-0" />
                                <p>Reduces direct costs by 65-85% per hire and over 95% in opportunity cost</p>
                            </div>
                            <div className="flex items-center space-x-4">
                                <Activity className="w-10 h-10 text-pink-400 flex-shrink-0" />
                                <p>Unbiased candidate evaluation with tailored criteria for each role, preserving conversational depth</p>
                            </div>
                            <div className="flex items-center space-x-4">
                                <Users className="w-10 h-10 text-pink-400 flex-shrink-0" />
                                <p>Creates a seamless candidate experience with faster feedback loops</p>
                            </div>
                        </div>
                    </div>
                </div>
            </ParallaxSection>

            {/* 4. Recruitment Journey Comparison */}
            <ParallaxSection bgColor="bg-gradient-to-br from-purple-900 via-indigo-800 to-blue-900" ref={setRef(3)}>
                <div>
                    <h2 className="text-4xl md:text-5xl font-bold mb-10 text-center text-white">
                        <GradientText>Recruitment Journey Comparison</GradientText>
                    </h2>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        {/* Traditional Process */}
                        <div className="bg-white bg-opacity-20 backdrop-blur-md rounded-xl shadow-xl p-6 border border-red-300 border-opacity-30">
                            <h3 className="text-2xl font-bold mb-2 flex items-center text-white">
                                <Clock className="w-6 h-6 mr-2 text-red-300" />
                                Traditional Process
                            </h3>
                            <p className="text-gray-200 mb-6">Lengthy, sequential, and full of delays</p>

                            <div className="space-y-3">
                                {processComparisonData.map((step, index) => (
                                    <div key={index} className="relative">
                                        {/* Step */}
                                        <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-3 border-l-4 border-red-400 shadow-md">
                                            <div className="flex justify-between items-center">
                                                <h4 className="font-bold text-white">{step.step}</h4>
                                                <div className="flex items-center bg-red-900 bg-opacity-50 px-3 py-1 rounded-full">
                                                    <Clock className="w-4 h-4 mr-1 text-red-300" />
                                                    <span className="text-red-200">{step.traditional} {step.traditionalUnit}</span>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Gap */}
                                        {step.traditionalGap > 0 && (
                                            <div className="ml-6 my-2 flex items-center text-gray-300">
                                                <ArrowDown className="w-4 h-4 mr-2 text-red-300" />
                                                <span>{step.traditionalGap} days wait</span>
                                            </div>
                                        )}
                                    </div>
                                ))}

                                {/* Total Time */}
                                <div className="mt-6 bg-red-900 bg-opacity-30 rounded-lg p-3 border-t-4 border-red-400">
                                    <div className="flex justify-between items-center">
                                        <h4 className="font-bold text-white">Total Time</h4>
                                        <div className="flex items-center">
                                            <Clock className="w-4 h-4 mr-1 text-red-300" />
                                            <span className="text-red-200 font-bold">~50 days</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Recruiva Process */}
                        <div className="bg-white bg-opacity-20 backdrop-blur-md rounded-xl shadow-xl p-6 border border-green-300 border-opacity-30">
                            <h3 className="text-2xl font-bold mb-2 flex items-center text-white">
                                <Zap className="w-6 h-6 mr-2 text-green-300" />
                                Recruiva Process
                            </h3>
                            <p className="text-gray-200 mb-6">Unlimited concurrent and autonomous candidate progressions</p>

                            <div className="space-y-3">
                                {/* Custom Recruiva Process Steps */}
                                {/* Job Definition */}
                                <div className="relative">
                                    <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-3 border-l-4 border-green-400 shadow-md">
                                        <div className="flex justify-between items-center">
                                            <h4 className="font-bold text-white">Job Definition</h4>
                                            <div className="flex items-center bg-green-900 bg-opacity-50 px-3 py-1 rounded-full">
                                                <Clock className="w-4 h-4 mr-1 text-green-300" />
                                                <span className="text-green-200">15 min</span>
                                            </div>
                                        </div>
                                        <div className="text-xs mt-1 text-gray-300">
                                            A 10 min call with Recruiva AI to start the hiring process
                                        </div>
                                    </div>
                                </div>

                                {/* Adaptive Interview (Combined) */}
                                <div className="relative">
                                    <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-3 border-l-4 border-green-400 shadow-md">
                                        <div className="flex justify-between items-center">
                                            <h4 className="font-bold text-white">Adaptive Interview</h4>
                                            <div className="flex items-center bg-green-900 bg-opacity-50 px-3 py-1 rounded-full">
                                                <Clock className="w-4 h-4 mr-1 text-green-300" />
                                                <span className="text-green-200">3 hrs</span>
                                            </div>
                                        </div>
                                        <div className="text-xs mt-1 text-gray-300">
                                            Up to 3 hours of deep dive with a multimodal AI
                                        </div>
                                    </div>
                                </div>

                                {/* Evaluation */}
                                <div className="relative">
                                    <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-3 border-l-4 border-green-400 shadow-md">
                                        <div className="flex justify-between items-center">
                                            <h4 className="font-bold text-white">Evaluation</h4>
                                            <div className="flex items-center bg-green-900 bg-opacity-50 px-3 py-1 rounded-full">
                                                <Clock className="w-4 h-4 mr-1 text-green-300" />
                                                <span className="text-green-200">1 min</span>
                                            </div>
                                        </div>
                                        <div className="text-xs mt-1 text-gray-300">
                                            Unbiased evaluation with state-of-the-art reasoning models
                                        </div>
                                    </div>
                                </div>

                                {/* Total Time */}
                                <div className="mt-6 bg-green-900 bg-opacity-30 rounded-lg p-3 border-t-4 border-green-400">
                                    <div className="flex justify-between items-center">
                                        <h4 className="font-bold text-white">Total Time</h4>
                                        <div className="flex items-center">
                                            <Clock className="w-4 h-4 mr-1 text-green-300" />
                                            <span className="text-green-200 font-bold">~3.5 hrs</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ParallaxSection>

            {/* 5. Market Opportunity */}
            <ParallaxSection bgColor="bg-gradient-to-br from-purple-50 via-blue-50 to-orange-50" ref={setRef(4)}>
                <div>
                    <h2 className="text-4xl md:text-5xl font-bold mb-10 text-center">
                        <GradientText>Market Opportunity</GradientText>
                    </h2>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        <div className="bg-white bg-opacity-80 backdrop-blur-sm rounded-xl shadow-xl p-6">
                            <h3 className="text-2xl font-bold mb-6 text-gray-800">Market Size</h3>
                            <div className="h-80">
                                <ResponsiveContainer width="100%" height="100%">
                                    <PieChart>
                                        <Pie
                                            data={marketData}
                                            cx="50%"
                                            cy="50%"
                                            labelLine={false}
                                            outerRadius={110}
                                            fill="#8884d8"
                                            dataKey="value"
                                            label={({ cx, cy, midAngle, outerRadius, name, value }) => {
                                                const RADIAN = Math.PI / 180;
                                                const radius = outerRadius + 25;
                                                const x = cx + radius * Math.cos(-midAngle * RADIAN);
                                                const y = cy + radius * Math.sin(-midAngle * RADIAN);

                                                return (
                                                    <text
                                                        x={x}
                                                        y={y}
                                                        fill="#333"
                                                        textAnchor={x > cx ? 'start' : 'end'}
                                                        dominantBaseline="central"
                                                        fontSize={14}
                                                        fontWeight="bold"
                                                    >
                                                        {name}: <tspan fill="#8884d8" fontWeight="bold">${value}B</tspan>
                                                    </text>
                                                );
                                            }}
                                        >
                                            {marketData.map((_entry, index) => (
                                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                            ))}
                                        </Pie>
                                        <Tooltip formatter={(value) => [`${value}B`, ""]} />
                                    </PieChart>
                                </ResponsiveContainer>
                            </div>

                            {/* Market Size Legend with Values */}
                            <div className="grid grid-cols-3 gap-3 mt-4">
                                <div className="bg-purple-100 rounded-lg p-3 text-center">
                                    <p className="font-bold text-gray-800">TAM</p>
                                    <p className="text-base font-bold text-purple-700">$150B</p>
                                    <p className="text-xs text-gray-600">Total Addressable Market</p>
                                </div>
                                <div className="bg-blue-100 rounded-lg p-3 text-center">
                                    <p className="font-bold text-gray-800">SAM</p>
                                    <p className="text-base font-bold text-blue-700">$24B</p>
                                    <p className="text-xs text-gray-600">Serviceable Available Market</p>
                                </div>
                                <div className="bg-pink-100 rounded-lg p-3 text-center">
                                    <p className="font-bold text-gray-800">SOM</p>
                                    <p className="text-base font-bold text-pink-700">$2.8B</p>
                                    <p className="text-xs text-gray-600">Serviceable Obtainable Market</p>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white bg-opacity-80 backdrop-blur-sm rounded-xl shadow-xl p-6">
                            <h3 className="text-2xl font-bold mb-6 text-gray-800">Target Customer Segments</h3>
                            <div className="space-y-6">
                                <div className="bg-purple-50 rounded-lg p-3 border-l-4 border-purple-500">
                                    <h4 className="font-bold text-purple-700">Primary</h4>
                                    <p className="text-gray-700">High-growth tech companies (500-5,000 employees) hiring 100+ roles annually</p>
                                    <div className="mt-2 flex items-center">
                                        <Target className="w-4 h-4 text-purple-500" />
                                        <span className="ml-2 text-sm text-purple-700">Fastest growing segment with 35% CAGR</span>
                                    </div>
                                </div>
                                <div className="bg-blue-50 rounded-lg p-3 border-l-4 border-blue-500">
                                    <h4 className="font-bold text-blue-700">Secondary</h4>
                                    <p className="text-gray-700">Talent acquisition agencies managing multiple client searches</p>
                                    <div className="mt-2 flex items-center">
                                        <Users className="w-4 h-4 text-blue-500" />
                                        <span className="ml-2 text-sm text-blue-700">High volume with consistent recruitment needs</span>
                                    </div>
                                </div>
                                <div className="bg-pink-50 rounded-lg p-3 border-l-4 border-pink-500">
                                    <h4 className="font-bold text-pink-700">Tertiary</h4>
                                    <p className="text-gray-700">Recently funded startups that require rapid recruitment and growth</p>
                                    <div className="mt-2 flex items-center">
                                        <Zap className="w-4 h-4 text-pink-500" />
                                        <span className="ml-2 text-sm text-pink-700">Immediate scaling needs post-funding rounds</span>
                                    </div>
                                </div>
                            </div>
                            <div className="mt-6 text-center">
                                <Link
                                    href="/financials"
                                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-500 text-white font-medium rounded-lg hover:from-purple-700 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg"
                                >
                                    <PieChartIcon className="w-5 h-5 mr-2" />
                                    View Full Market Analysis
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </ParallaxSection>

            {/* 6. Competitive Landscape */}
            <ParallaxSection bgColor="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900" ref={setRef(5)}>
                <div>
                    <h2 className="text-4xl md:text-5xl font-bold mb-8 text-center text-white">
                        <GradientText>Competitive Landscape</GradientText>
                    </h2>

                    <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-5 mb-6">
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm text-white">
                                <thead>
                                    <tr className="border-b border-gray-700">
                                        <th className="py-2 px-4 text-left">Feature</th>
                                        <th className="py-2 px-4 bg-blue-900 bg-opacity-30">Recruiva</th>
                                        <th className="py-2 px-4">Workable</th>
                                        <th className="py-2 px-4">HireVue</th>
                                        <th className="py-2 px-4">Eightfold AI</th>
                                        <th className="py-2 px-4">Mercor</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr className="border-b border-gray-700">
                                        <td className="py-2 px-4 text-left font-medium">Realtime Multimodal Interviews</td>
                                        <td className="py-2 px-4 bg-blue-900 bg-opacity-30"><Check className="w-5 h-5 mx-auto text-green-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><span className="text-amber-400">Text-to-Speech</span></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><span className="text-amber-400">Text-to-Speech</span></td>
                                    </tr>
                                    <tr className="border-b border-gray-700">
                                        <td className="py-2 px-4 text-left font-medium">Adaptive Questioning</td>
                                        <td className="py-2 px-4 bg-blue-900 bg-opacity-30"><Check className="w-5 h-5 mx-auto text-green-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                    </tr>
                                    <tr className="border-b border-gray-700">
                                        <td className="py-2 px-4 text-left font-medium">Virtual White-boarding</td>
                                        <td className="py-2 px-4 bg-blue-900 bg-opacity-30"><Check className="w-5 h-5 mx-auto text-green-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                    </tr>
                                    <tr className="border-b border-gray-700">
                                        <td className="py-2 px-4 text-left font-medium">Code and Home Assignments</td>
                                        <td className="py-2 px-4 bg-blue-900 bg-opacity-30"><Check className="w-5 h-5 mx-auto text-green-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><span className="text-amber-400">Limited</span></td>
                                    </tr>
                                    <tr className="border-b border-gray-700">
                                        <td className="py-2 px-4 text-left font-medium">Advanced Deep Dive Interviews</td>
                                        <td className="py-2 px-4 bg-blue-900 bg-opacity-30"><Check className="w-5 h-5 mx-auto text-green-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                    </tr>
                                    <tr className="border-b border-gray-700">
                                        <td className="py-2 px-4 text-left font-medium">Autonomous Candidate Progression</td>
                                        <td className="py-2 px-4 bg-blue-900 bg-opacity-30"><Check className="w-5 h-5 mx-auto text-green-400" /></td>
                                        <td className="py-2 px-4"><span className="text-amber-400">Limited ATS</span></td>
                                        <td className="py-2 px-4"><span className="text-amber-400">Limited ATS</span></td>
                                        <td className="py-2 px-4"><span className="text-amber-400">Limited ATS</span></td>
                                        <td className="py-2 px-4"><span className="text-amber-400">Limited ATS</span></td>
                                    </tr>
                                    <tr className="border-b border-gray-700">
                                        <td className="py-2 px-4 text-left font-medium">Work with AI</td>
                                        <td className="py-2 px-4 bg-blue-900 bg-opacity-30"><Check className="w-5 h-5 mx-auto text-green-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                        <td className="py-2 px-4"><X className="w-5 h-5 mx-auto text-red-400" /></td>
                                    </tr>
                                    <tr className="border-b border-gray-700">
                                        <td className="py-2 px-4 text-left font-medium">Target Customer Size</td>
                                        <td className="py-2 px-4 bg-blue-900 bg-opacity-30">500-5,000</td>
                                        <td className="py-2 px-4">10-500</td>
                                        <td className="py-2 px-4">10,000+</td>
                                        <td className="py-2 px-4">10,000+</td>
                                        <td className="py-2 px-4">Tech Companies</td>
                                    </tr>
                                    <tr>
                                        <td className="py-2 px-4 text-left font-medium">Key Differentiator</td>
                                        <td className="py-2 px-4 bg-blue-900 bg-opacity-30">Multimodal AI, Autonomous Progression</td>
                                        <td className="py-2 px-4">Ease of Use</td>
                                        <td className="py-2 px-4">Video Analysis</td>
                                        <td className="py-2 px-4">Talent Intelligence</td>
                                        <td className="py-2 px-4">Technical Assessment</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6">
                            <h3 className="text-2xl font-bold mb-4 text-white">Our Unique Advantage</h3>
                            <div className="space-y-3">
                                <div className="flex items-start gap-3 bg-purple-900 bg-opacity-30 rounded-lg p-4">
                                    <div className="bg-purple-500 rounded-full p-3 flex-shrink-0">
                                        <Video className="w-5 h-5 text-white" />
                                    </div>
                                    <div>
                                        <h4 className="font-bold text-base mb-1">Realtime Multimodal AI</h4>
                                        <p className="text-purple-100">Advanced AI-powered interview system that analyzes voice, video, and coding in real-time.</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-3 bg-indigo-900 bg-opacity-30 rounded-lg p-4">
                                    <div className="bg-indigo-500 rounded-full p-3 flex-shrink-0">
                                        <Activity className="w-6 h-6 text-white" />
                                    </div>
                                    <div>
                                        <h4 className="font-bold text-base mb-1">Concurrent Candidate Progression</h4>
                                        <p className="text-indigo-100">Automated progression of multiple candidates simultaneously, eliminating bottlenecks in the hiring pipeline.</p>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6">
                            <h3 className="text-2xl font-bold mb-4 text-white">Our Strategy to Dominance</h3>

                            <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-6 shadow-lg">
                                <ul className="space-y-2 text-white">
                                    <li className="flex items-center">
                                        <Check className="w-5 h-5 mr-2 text-green-400" />
                                        Aggressive Sales and Marketing
                                    </li>
                                    <li className="flex items-center">
                                        <Check className="w-5 h-5 mr-2 text-green-400" />
                                        Cutting edge AI technology
                                    </li>
                                    <li className="flex items-center">
                                        <Check className="w-5 h-5 mr-2 text-green-400" />
                                        Exceptional UX and efficiency
                                    </li>
                                    <li className="flex items-center">
                                        <Check className="w-5 h-5 mr-2 text-green-400" />
                                        Deep Industry connection
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div className="mt-4 text-center">
                                <Link
                                    href="/competitors"
                                    className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-500 text-white font-medium rounded-lg hover:from-purple-700 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg"
                                >
                                    <BarChart2 className="w-5 h-5 mr-2" />
                                    View Full Competitor Analysis
                                </Link>
                            </div>
                </div>
            </ParallaxSection>

            {/* 7. Product and Progress */}
            <ParallaxSection bgColor="bg-gray-900" ref={setRef(6)}>
                <div>
                    <h2 className="text-4xl md:text-5xl font-bold mb-10 text-center text-white">
                        <GradientText>Product & Progress</GradientText>
                    </h2>

                    {/* MVP Status Banner */}
                    <div className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl p-3 mb-8 max-w-3xl mx-auto">
                        <div className="flex items-center justify-center space-x-3">
                            <div className="h-3 w-3 bg-white rounded-full animate-pulse"></div>
                            <p className="text-white text-lg font-bold">MVP Launched, 3 Customers In Pilot</p>
                            <div className="h-3 w-3 bg-white rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                        <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6 text-white">
                            <div className="flex items-center justify-between mb-6">
                                <h3 className="text-2xl font-bold">Completed Features</h3>
                                <span className="bg-green-500 text-white text-sm font-medium px-2.5 py-0.5 rounded-full">Live</span>
                            </div>
                            <div className="grid grid-cols-2 gap-3">
                                <div className="bg-green-900 bg-opacity-20 border border-green-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <Check className="w-4 h-4 text-green-400 mr-2" />
                                        <p className="font-medium text-sm">Role Definition with AI</p>
                                    </div>
                                </div>
                                <div className="bg-green-900 bg-opacity-20 border border-green-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <Check className="w-4 h-4 text-green-400 mr-2" />
                                        <p className="font-medium text-sm">AI Interview Journey Creation</p>
                                    </div>
                                </div>
                                <div className="bg-green-900 bg-opacity-20 border border-green-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <Check className="w-4 h-4 text-green-400 mr-2" />
                                        <p className="font-medium text-sm">Instant Interview to Apply</p>
                                    </div>
                                </div>
                                <div className="bg-green-900 bg-opacity-20 border border-green-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <Check className="w-4 h-4 text-green-400 mr-2" />
                                        <p className="font-medium text-sm">Multimodal Capability</p>
                                    </div>
                                </div>
                                <div className="bg-green-900 bg-opacity-20 border border-green-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <Check className="w-4 h-4 text-green-400 mr-2" />
                                        <p className="font-medium text-sm">Adaptive Questioning</p>
                                    </div>
                                </div>
                                <div className="bg-green-900 bg-opacity-20 border border-green-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <Check className="w-4 h-4 text-green-400 mr-2" />
                                        <p className="font-medium text-sm">AI Evaluations</p>
                                    </div>
                                </div>
                                <div className="bg-green-900 bg-opacity-20 border border-green-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <Check className="w-4 h-4 text-green-400 mr-2" />
                                        <p className="font-medium text-sm">Autonomous Candidate Progression</p>
                                    </div>
                                </div>
                                <div className="bg-green-900 bg-opacity-20 border border-green-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <Check className="w-4 h-4 text-green-400 mr-2" />
                                        <p className="font-medium text-sm">Recruiva&apos;s Job Board</p>
                                    </div>
                                </div>
                                <div className="bg-green-900 bg-opacity-20 border border-green-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <Check className="w-4 h-4 text-green-400 mr-2" />
                                        <p className="font-medium text-sm">Recruiva AI Assistant</p>
                                    </div>
                                </div>
                                <div className="bg-green-900 bg-opacity-20 border border-green-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <Check className="w-4 h-4 text-green-400 mr-2" />
                                        <p className="font-medium text-sm">Recruiva&apos;s Dashboard</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6 text-white">
                            <div className="flex items-center justify-between mb-6">
                                <h3 className="text-2xl font-bold">Features in Work</h3>
                                <span className="bg-blue-500 text-white text-sm font-medium px-3 py-1 rounded-full">Coming Soon</span>
                            </div>

                            <div className="grid grid-cols-2 gap-3">
                                <div className="bg-blue-900 bg-opacity-20 border border-blue-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <div className="w-4 h-4 rounded-full border-2 border-blue-400 mr-2 flex-shrink-0"></div>
                                        <p className="font-medium text-sm">Code Assessment with Built-In IDE</p>
                                    </div>
                                </div>

                                <div className="bg-blue-900 bg-opacity-20 border border-blue-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <div className="w-4 h-4 rounded-full border-2 border-blue-400 mr-2 flex-shrink-0"></div>
                                        <p className="font-medium text-sm">Work With AI</p>
                                    </div>
                                </div>

                                <div className="bg-blue-900 bg-opacity-20 border border-blue-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <div className="w-4 h-4 rounded-full border-2 border-blue-400 mr-2 flex-shrink-0"></div>
                                        <p className="font-medium text-sm">Virtual Whiteboard</p>
                                    </div>
                                </div>

                                <div className="bg-blue-900 bg-opacity-20 border border-blue-700 border-opacity-30 rounded-lg p-3 transition-all duration-200 hover:scale-105 hover:shadow-md hover:bg-opacity-30 cursor-pointer">
                                    <div className="flex items-center">
                                        <div className="w-4 h-4 rounded-full border-2 border-blue-400 mr-2 flex-shrink-0"></div>
                                        <p className="font-medium text-sm">Home Assignments</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ParallaxSection>

            {/* 8. Business Model */}
            <ParallaxSection bgColor="bg-gradient-to-br from-purple-50 via-blue-50 to-orange-50" ref={setRef(7)}>
                <div>
                    <h2 className="text-4xl md:text-5xl font-bold mb-6 text-center">
                        <GradientText>Business Model</GradientText>
                    </h2>
                    <div className="grid grid-cols-1 gap-6 mb-6">
                        <div className="bg-white bg-opacity-80 backdrop-blur-sm rounded-xl shadow-xl p-5">
                            <h3 className="text-2xl font-bold mb-4 text-gray-800">Subscription Plans</h3>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 w-full mb-3">
                                <div className="bg-white rounded-xl shadow-md overflow-hidden border-2 border-gray-200 flex flex-col h-full">
                                    <div className="bg-gradient-to-r from-gray-400 to-gray-500 p-3">
                                        <h4 className="text-white font-bold text-center text-lg">Basic</h4>
                                    </div>
                                    <div className="p-4 flex flex-col items-center flex-grow">
                                        <div className="text-center mb-4">
                                            <span className="text-3xl font-bold text-gray-600">$299</span>
                                            <span className="text-gray-600 text-sm">/month</span>
                                        </div>
                                        <div className="space-y-3 w-full">
                                            <div className="flex items-center text-gray-700">
                                                <Target className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" />
                                                <span className="text-sm">1 Active role</span>
                                            </div>
                                            <div className="flex items-center text-gray-700">
                                                <Users className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" />
                                                <span className="text-sm">5 Interviews</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-white rounded-xl shadow-md overflow-hidden border-2 border-purple-200 flex flex-col h-full">
                                    <div className="bg-gradient-to-r from-purple-400 to-purple-500 p-3">
                                        <h4 className="text-white font-bold text-center text-lg">Startup</h4>
                                    </div>
                                    <div className="p-4 flex flex-col items-center flex-grow">
                                        <div className="text-center mb-4">
                                            <span className="text-3xl font-bold text-purple-600">$1,500</span>
                                            <span className="text-gray-600 text-sm">/month</span>
                                        </div>
                                        <div className="space-y-3 w-full">
                                            <div className="flex items-center text-gray-700">
                                                <Target className="w-4 h-4 text-purple-400 mr-2 flex-shrink-0" />
                                                <span className="text-sm">10 Active roles</span>
                                            </div>
                                            <div className="flex items-center text-gray-700">
                                                <Users className="w-4 h-4 text-purple-400 mr-2 flex-shrink-0" />
                                                <span className="text-sm">50 Interviews</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-white rounded-xl shadow-md overflow-hidden border-2 border-blue-300 flex flex-col h-full">
                                    <div className="bg-gradient-to-r from-blue-400 to-blue-500 p-3">
                                        <h4 className="text-white font-bold text-center text-lg">Growth</h4>
                                    </div>
                                    <div className="p-4 flex flex-col items-center flex-grow">
                                        <div className="text-center mb-4">
                                            <span className="text-3xl font-bold text-blue-600">$3,500</span>
                                            <span className="text-gray-600 text-sm">/month</span>
                                        </div>
                                        <div className="space-y-3 w-full">
                                            <div className="flex items-center text-gray-700">
                                                <Target className="w-4 h-4 text-blue-400 mr-2 flex-shrink-0" />
                                                <span className="text-sm">25 Active roles</span>
                                            </div>
                                            <div className="flex items-center text-gray-700">
                                                <Users className="w-4 h-4 text-blue-400 mr-2 flex-shrink-0" />
                                                <span className="text-sm">150 Interviews</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-white rounded-xl shadow-md overflow-hidden border-2 border-pink-300 flex flex-col h-full">
                                    <div className="bg-gradient-to-r from-pink-400 to-pink-500 p-3">
                                        <h4 className="text-white font-bold text-center text-lg">Enterprise</h4>
                                    </div>
                                    <div className="p-4 flex flex-col items-center flex-grow">
                                        <div className="text-center mb-4">
                                            <span className="text-3xl font-bold text-pink-600">$8,000</span>
                                            <span className="text-gray-600 text-sm">/month</span>
                                        </div>
                                        <div className="space-y-3 w-full">
                                            <div className="flex items-center text-gray-700">
                                                <Target className="w-4 h-4 text-pink-400 mr-2 flex-shrink-0" />
                                                <span className="text-sm">100+ Active Roles</span>
                                            </div>
                                            <div className="flex items-center text-gray-700">
                                                <Users className="w-4 h-4 text-pink-400 mr-2 flex-shrink-0" />
                                                <span className="text-sm">500+ Interviews</span>
                                            </div>
                                            <div className="flex items-center text-gray-700">
                                                <Layers className="w-4 h-4 text-pink-400 mr-2 flex-shrink-0" />
                                                <span className="text-sm">Custom integrations</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="text-center mb-4">
                                <p className="text-sm text-gray-600 italic flex items-center justify-center">
                                    <span className="text-blue-500 mr-1">*</span>
                                    Additional interviews beyond your plan limit are billed at $1 per minute of interview time
                                </p>
                            </div>
                        </div>
                        <div className="bg-white bg-opacity-80 backdrop-blur-sm rounded-xl shadow-xl p-6">
                            <h3 className="text-2xl font-bold mb-6 text-gray-800">Unit Economics</h3>

                            {/* LTV:CAC Ratio Visualization */}
                            <div className="mb-8">
                                <div className="flex justify-between items-center mb-4">
                                    <h4 className="font-bold text-lg text-gray-700">LTV:CAC Ratio</h4>
                                    <span className="bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold rounded-lg px-4 py-2">12:1 Ratio</span>
                                </div>

                                <div className="flex items-end justify-center gap-12 h-48 mb-4">
                                    <div className="flex flex-col items-center">
                                        <div className="w-20 h-20 bg-purple-500 rounded-lg flex items-center justify-center">
                                            <span className="text-white font-bold text-base">CAC</span>
                                        </div>
                                        <div className="mt-4 bg-white px-4 py-2 rounded-lg shadow-md">
                                            <p className="text-purple-600 font-bold text-lg text-center">$12K</p>
                                        </div>
                                    </div>

                                    <div className="flex flex-col items-center">
                                        <div className="w-20 h-60 bg-blue-500 rounded-lg flex items-center justify-center">
                                            <span className="text-white font-bold text-base">LTV</span>
                                        </div>
                                        <div className="mt-4 bg-white px-4 py-2 rounded-lg shadow-md">
                                            <p className="text-blue-600 font-bold text-lg text-center">$145K</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Key Metrics */}
                            <div className="grid grid-cols-3 gap-4">
                                <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 text-center shadow-sm border border-blue-200">
                                    <div className="flex justify-center mb-2">
                                        <DollarSign className="w-8 h-8 text-blue-500" />
                                    </div>
                                    <h5 className="font-medium text-gray-700 mb-1">Average Contract Value</h5>
                                    <div className="flex items-center justify-center">
                                        <span className="text-xl font-bold text-blue-600">$42,000</span>
                                        <span className="text-sm text-blue-500 ml-1">/year</span>
                                    </div>
                                </div>

                                <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 text-center shadow-sm border border-purple-200">
                                    <div className="flex justify-center mb-2">
                                        <Activity className="w-8 h-8 text-purple-500" />
                                    </div>
                                    <h5 className="font-medium text-gray-700 mb-1">Gross Margin</h5>
                                    <div className="flex items-center justify-center">
                                        <span className="text-xl font-bold text-purple-600">85%+</span>
                                        <span className="text-sm text-purple-500 ml-1">at scale</span>
                                    </div>
                                </div>

                                <div className="bg-gradient-to-br from-pink-50 to-pink-100 rounded-xl p-4 text-center shadow-sm border border-pink-200">
                                    <div className="flex justify-center mb-2">
                                        <Users className="w-8 h-8 text-pink-500" />
                                    </div>
                                    <h5 className="font-medium text-gray-700 mb-1">Customer Retention</h5>
                                    <div className="flex items-center justify-center">
                                        <span className="text-xl font-bold text-pink-600">85%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ParallaxSection>

            {/* 9. Funding Request */}
            <ParallaxSection bgColor="bg-gradient-to-br from-purple-900 via-indigo-900 to-blue-900" ref={setRef(8)}>
                <div>
                    <h2 className="text-4xl md:text-5xl font-bold mb-10 text-center text-white">
                        <GradientText>Funding Request</GradientText>
                    </h2>

                    <div className="flex flex-col md:flex-row items-center justify-center gap-8 mb-12">
                        {/* Funding Amount */}
                        <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl px-10 py-8 text-center w-72 h-64 flex flex-col justify-center">
                            <h3 className="text-2xl font-bold text-white mb-2">The Ask</h3>
                            <p className="text-5xl font-bold mt-2 text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-blue-400 to-pink-400">$250K</p>
                            <p className="text-white mt-4">for new customer acquisition</p>
                        </div>

                        {/* Revenue Target */}
                        <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl px-10 py-8 text-center w-72 h-64 flex flex-col justify-center">
                            <h3 className="text-2xl font-bold text-white mb-2">Target ARR</h3>
                            <p className="text-5xl font-bold mt-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-pink-400 to-purple-400">$1.5M</p>
                            <p className="text-white mt-4">by the end of 2025</p>
                        </div>
                    </div>

                    {/* Fund Allocation */}
                    <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-8 max-w-4xl mx-auto">
                        <h3 className="text-2xl font-bold mb-6 text-white">Fund Allocation</h3>

                        <div className="space-y-6 max-w-2xl mx-auto">
                            {/* Sales & Marketing - 70% */}
                            <div>
                                <div className="flex justify-between items-center mb-2">
                                    <div className="flex items-center">
                                        <Target className="w-5 h-5 text-pink-300 mr-2" />
                                        <h4 className="font-semibold text-white">Sales & Marketing</h4>
                                    </div>
                                    <span className="text-pink-300 font-bold">70%</span>
                                </div>
                                <div className="w-full bg-gray-700 bg-opacity-50 h-4 rounded-full overflow-hidden">
                                    <div className="bg-gradient-to-r from-pink-500 to-pink-400 h-4 rounded-full" style={{width: '70%'}}></div>
                                </div>
                                <p className="text-gray-300 text-sm mt-2">Direct sales team, marketing campaigns, and customer acquisition</p>
                            </div>

                            {/* Technology - 20% */}
                            <div>
                                <div className="flex justify-between items-center mb-2">
                                    <div className="flex items-center">
                                        <Layers className="w-5 h-5 text-blue-300 mr-2" />
                                        <h4 className="font-semibold text-white">Technology</h4>
                                    </div>
                                    <span className="text-blue-300 font-bold">20%</span>
                                </div>
                                <div className="w-full bg-gray-700 bg-opacity-50 h-4 rounded-full overflow-hidden">
                                    <div className="bg-gradient-to-r from-blue-500 to-blue-400 h-4 rounded-full" style={{width: '20%'}}></div>
                                </div>
                                <p className="text-gray-300 text-sm mt-2">Product development, AI model improvements, and feature enhancements</p>
                            </div>

                            {/* Administrative - 10% */}
                            <div>
                                <div className="flex justify-between items-center mb-2">
                                    <div className="flex items-center">
                                        <Users className="w-5 h-5 text-purple-300 mr-2" />
                                        <h4 className="font-semibold text-white">Administrative</h4>
                                    </div>
                                    <span className="text-purple-300 font-bold">10%</span>
                                </div>
                                <div className="w-full bg-gray-700 bg-opacity-50 h-4 rounded-full overflow-hidden">
                                    <div className="bg-gradient-to-r from-purple-500 to-purple-400 h-4 rounded-full" style={{width: '10%'}}></div>
                                </div>
                                <p className="text-gray-300 text-sm mt-2">Legal, operations, and business administration</p>
                            </div>
                        </div>
                    </div>
                </div>
            </ParallaxSection>

            {/* 10. Path To Unicorn */}
            <ParallaxSection bgColor="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900" ref={setRef(9)}>
                <div>
                    <h2 className="text-4xl md:text-5xl font-bold mb-8 text-center text-white">
                        <GradientText>Path To Unicorn</GradientText>
                    </h2>

                    <div className="relative max-w-4xl mx-auto mb-10 py-6">
                        <div className="absolute top-0 bottom-0 left-1/2 w-1 bg-gradient-to-b from-purple-500 via-blue-500 to-pink-500 transform -translate-x-1/2"></div>

                        {/* 2025 Milestone */}
                        <div className="relative mb-10 opacity-100 transition-all duration-1000">
                            <div className="flex items-center mb-3">
                                <div className="absolute left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full bg-purple-500 border-4 border-gray-800 z-10"></div>
                                <div className="w-1/2 pr-8 text-right">
                                    <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">2025</h3>
                                </div>
                                <div className="w-1/2 pl-8">
                                    <div className="bg-white bg-opacity-10 backdrop-blur rounded-xl p-4">
                                        <ul className="space-y-2 text-gray-200">
                                            <li className="flex items-center">
                                                <svg className="w-5 h-5 text-purple-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                                <span>$1.5M ARR</span>
                                            </li>
                                            <li className="flex items-center">
                                                <svg className="w-5 h-5 text-purple-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                                <span>30-40 Customers</span>
                                            </li>
                                            <li className="flex items-center">
                                                <svg className="w-5 h-5 text-purple-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                                <span>$15-20M Valuation</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* 2026 Milestone */}
                        <div className="relative mb-10 opacity-100 transition-all duration-1000 delay-300">
                            <div className="flex items-center mb-3">
                                <div className="w-1/2 pr-8 text-left">
                                    <div className="bg-white bg-opacity-10 backdrop-blur rounded-xl p-4">
                                        <ul className="space-y-2 text-gray-200">
                                            <li className="flex items-center">
                                                <svg className="w-5 h-5 text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                                <span>$15M ARR</span>
                                            </li>
                                            <li className="flex items-center">
                                                <svg className="w-5 h-5 text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                                <span>300+ Customers</span>
                                            </li>
                                            <li className="flex items-center">
                                                <svg className="w-5 h-5 text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                                <span>$150-300M Valuation</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div className="absolute left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full bg-blue-500 border-4 border-gray-800 z-10"></div>
                                <div className="w-1/2 pl-8">
                                    <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-pink-400">2026</h3>
                                </div>
                            </div>
                        </div>

                        {/* 2027 Milestone */}
                        <div className="relative opacity-100 transition-all duration-1000 delay-600">
                            <div className="flex items-center mb-2">
                                <div className="absolute left-1/2 transform -translate-x-1/2 w-10 h-10 rounded-full bg-pink-500 border-4 border-gray-800 z-10 animate-pulse"></div>
                                <div className="w-1/2 pr-8 text-right">
                                    <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-pink-400 to-purple-400">2027</h3>
                                </div>
                                <div className="w-1/2 pl-8">
                                    <div className="bg-gradient-to-r from-pink-500 to-purple-500 rounded-xl p-4">
                                        <div className="bg-white bg-opacity-10 backdrop-blur rounded-lg p-3">
                                            <ul className="space-y-2 text-gray-200">
                                                <li className="flex items-center">
                                                    <svg className="w-5 h-5 text-white mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                    </svg>
                                                    <span>$100M+ ARR</span>
                                                </li>
                                                <li className="flex items-center">
                                                    <svg className="w-5 h-5 text-white mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                    </svg>
                                                    <span>1,000+ Customers</span>
                                                </li>
                                                <li className="flex items-center">
                                                    <svg className="w-5 h-5 text-white mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                    </svg>
                                                    <span className="font-bold">$1B+ Valuation (Unicorn Status)</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Key Valuation Drivers */}
                    <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-5 max-w-4xl mx-auto">
                        <h3 className="text-xl font-bold mb-5 text-white text-center">Key Valuation Drivers</h3>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
                            <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-4 shadow-lg border border-indigo-300 border-opacity-30">
                                <div className="flex items-center mb-3">
                                    <div className="bg-indigo-500 bg-opacity-20 rounded-full w-9 h-9 flex items-center justify-center mr-3">
                                        <DollarSign className="w-5 h-5 text-indigo-200" />
                                    </div>
                                    <h4 className="text-base font-bold text-white">Revenue Growth</h4>
                                </div>
                                <p className="text-gray-300 text-sm">15x revenue growth from 2025 to 2027, with continued strong trajectory</p>
                            </div>

                            <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-4 shadow-lg border border-indigo-300 border-opacity-30">
                                <div className="flex items-center mb-3">
                                    <div className="bg-indigo-500 bg-opacity-20 rounded-full w-9 h-9 flex items-center justify-center mr-3">
                                        <Users className="w-5 h-5 text-indigo-200" />
                                    </div>
                                    <h4 className="text-base font-bold text-white">Market Penetration</h4>
                                </div>
                                <p className="text-gray-300 text-sm">Capturing 2% of the serviceable market by 2027, expanding to 5% by 2028</p>
                            </div>

                            <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-4 shadow-lg border border-indigo-300 border-opacity-30">
                                <div className="flex items-center mb-3">
                                    <div className="bg-indigo-500 bg-opacity-20 rounded-full w-9 h-9 flex items-center justify-center mr-3">
                                        <Activity className="w-5 h-5 text-indigo-200" />
                                    </div>
                                    <h4 className="text-base font-bold text-white">Profit Margins</h4>
                                </div>
                                <p className="text-gray-300 text-sm">Maintaining 85%+ gross margins with increasing operational efficiency</p>
                            </div>
                        </div>
                    </div>
                </div>
            </ParallaxSection>

            {/* 11. Conclusion */}
            <ParallaxSection bgColor="bg-gradient-to-br from-purple-50 via-blue-50 to-orange-50" ref={setRef(10)}>
                <div>
                    <h2 className="text-4xl md:text-5xl font-bold mb-8 text-center">
                        <GradientText>Conclusion</GradientText>
                    </h2>

                    {/* Key Takeaways */}
                    <div className="bg-white bg-opacity-80 backdrop-blur-sm rounded-xl shadow-xl p-8 max-w-4xl mx-auto mb-8">
                        <h3 className="text-2xl font-bold mb-6 text-gray-800">Key Takeaways</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {[
                                "Recruiva reduces time-to-hire by 85% with AI-led interviews",
                                "$24B serviceable market with 12:1 LTV:CAC ratio",
                                "MVP completed and pilot program in progress with 2 clients",
                                "Path to $1B+ valuation with 85%+ gross margins"
                            ].map((point, index) => (
                                <div key={index} className="flex items-start bg-gray-50 p-4 rounded-lg">
                                    <div className="bg-gradient-to-r from-purple-500 to-blue-500 rounded-full p-2 mr-3 flex-shrink-0">
                                        <Check className="w-4 h-4 text-white" />
                                    </div>
                                    <p className="text-gray-700 font-medium">{point}</p>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Call to Action */}
                    <div className="bg-gradient-to-r from-purple-900 via-indigo-900 to-blue-900 rounded-xl shadow-xl p-8 max-w-4xl mx-auto mb-6">
                        <div className="text-center mb-6">
                            <h3 className="text-3xl font-bold text-white">Be an Early Investor in Recruiva</h3>
                        </div>

                        <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-xl p-6 text-center">
                            <p className="text-white text-lg mb-6">Seed investment to reach $1.5M ARR in Year 1</p>

                            <a href="https://calendar.app.google/4CBByevkHFdfCgHP6" className="inline-block bg-white text-purple-700 font-bold py-3 px-8 rounded-full text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl">
                                Schedule a Demo
                            </a>
                        </div>
                    </div>

                    {/* Contact Information */}
                    <div className="text-center">
                        <p className="text-gray-700 font-medium">
                            Contact: <a href="mailto:<EMAIL>" className="text-purple-600 hover:text-purple-800"><EMAIL></a> |
                            <a href="http://www.recruiva.ai" className="text-purple-600 hover:text-purple-800 ml-2">www.recruiva.ai</a>
                        </p>
                    </div>
                </div>
            </ParallaxSection>
        </div>
    );
};

export default PitchDeck;