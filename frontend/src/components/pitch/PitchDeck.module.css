/* PitchDeck.module.css */
.pitchContainer {
  font-family: var(--font-sans);
  scroll-snap-type: y mandatory;
  height: 100vh;
  overflow-y: auto;
  scroll-behavior: smooth;
  background: linear-gradient(to bottom right, rgb(17, 24, 39), rgb(30, 58, 138), rgb(88, 28, 135));
}

.pitchContainer * {
  scroll-snap-align: start;
}

.pitchContainer::-webkit-scrollbar {
  display: none;
}

.pitchContainer {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.section {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 1.5rem;
  scroll-snap-align: start;
}

@media (min-width: 768px) {
  .section {
    padding: 4rem 3rem;
  }
}

.sectionContent {
  width: 100%;
  max-width: 80rem;
  margin: 0 auto;
}

.navigation {
  position: fixed;
  bottom: 2rem;
  left: 0;
  right: 0;
  z-index: 50;
  display: flex;
  justify-content: center;
}

.navigationInner {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 9999px;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.pageIndicators {
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 50;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-50%) translateX(20px); }
  to { opacity: 1; transform: translateY(-50%) translateX(0); }
}

@media (max-width: 768px) {
  .pageIndicators {
    right: 1rem;
  }
}

.gradientText {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, rgb(147, 51, 234), rgb(59, 130, 246), rgb(236, 72, 153));
  background-size: 200% auto;
}

.featureCard {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  transition: all 300ms;
}

.featureCard:hover {
  transform: scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.iconContainer {
  background-image: linear-gradient(to right, rgb(147, 51, 234), rgb(59, 130, 246));
  border-radius: 9999px;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.icon {
  width: 1.5rem;
  height: 1.5rem;
  color: white;
}