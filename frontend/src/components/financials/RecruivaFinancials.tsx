'use client';

import React, { useState, useEffect, useRef } from 'react';
import { BarChart, Bar, PieChart, Pie, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell } from 'recharts';

// Main App Component
export default function RecruivaMarketOpportunity() {
    const [scrollY, setScrollY] = useState(0);

    // Refs for scroll animation triggers
    const sectionRefs = {
        hero: useRef(null),
        highlights: useRef(null),
        market: useRef(null),
        competitive: useRef(null),
        process: useRef(null),
        financial: useRef(null),
        unicorn: useRef(null),
        roi: useRef(null),
        features: useRef(null),
        summary: useRef(null)
    };

    // Handle scroll events for parallax and animations
    useEffect(() => {
        const handleScroll = () => {
            setScrollY(window.scrollY);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    // Animated number counter hook

    // Check if element is in viewport
    const useInView = (ref: React.RefObject<HTMLElement>, options = {}) => {
        const [isInView, setIsInView] = useState(false);

        useEffect(() => {
            if (!ref.current) return;

            const observer = new IntersectionObserver(([entry]) => {
                setIsInView(entry.isIntersecting);
            }, options);

            observer.observe(ref.current);

            // Store the current value of ref.current in a variable
            const currentRef = ref.current;

            return () => {
                if (currentRef) {
                    observer.unobserve(currentRef);
                }
            };
        }, [ref, options]);

        return isInView;
    };

    // Generate animated values for each section
    const highlightsInView = useInView(sectionRefs.highlights);
    const marketInView = useInView(sectionRefs.market);
    const competitiveInView = useInView(sectionRefs.competitive);
    const processInView = useInView(sectionRefs.process);
    const financialInView = useInView(sectionRefs.financial);
    const unicornInView = useInView(sectionRefs.unicorn);
    const roiInView = useInView(sectionRefs.roi);
    const featuresInView = useInView(sectionRefs.features);
    const summaryInView = useInView(sectionRefs.summary);

    // Market Size Data
    const marketSizeData = [
        { name: 'TAM', value: 150, fill: '#8884d8' },
        { name: 'SAM', value: 24, fill: '#4a72e8' },
        { name: 'SOM', value: 2.8, fill: '#ec5fe3' }
    ];

    // Market Fragmentation Data
    const fragmentationData = [
        { name: 'LinkedIn Talent', value: 18, fill: '#8884d8' },
        { name: 'Workday', value: 12, fill: '#4a72e8' },
        { name: 'Greenhouse', value: 10, fill: '#82ca9d' },
        { name: 'Lever', value: 8, fill: '#a4de6c' },
        { name: 'SmartRecruiters', value: 7, fill: '#d0ed57' },
        { name: 'Others', value: 45, fill: '#ffc658' }
    ];

    // Financial Projections Data
    const financialData = [
        { year: '2025', revenue: 1.5, profit: 1.42 },
        { year: '2026', revenue: 5.7, profit: 5.41 },
        { year: '2027', revenue: 15.2, profit: 14.44 }
    ];

    // Seed Funding Allocation
    const fundingData = [
        { name: 'Sales & Marketing', value: 70, fill: '#8884d8' },
        { name: 'Product Development', value: 20, fill: '#4a72e8' },
        { name: 'Operations', value: 7, fill: '#82ca9d' },
        { name: 'Contingency', value: 3, fill: '#ec5fe3' }
    ];

    return (
        <div className="w-full overflow-x-hidden">
            {/* Hero Section */}
            <section
                ref={sectionRefs.hero}
                className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-800 via-blue-900 to-purple-900 text-white overflow-hidden"
                style={{ transform: `translateY(${scrollY * 0.1}px)` }}
            >
                <div
                    className="absolute inset-0 bg-gradient-to-br from-gray-800 via-blue-900 to-purple-900 opacity-70"
                    style={{ transform: `translateY(${scrollY * 0.2}px)` }}
                ></div>

                <div className="relative z-10 text-center px-4 max-w-4xl mx-auto" style={{ transform: `translateY(${-scrollY * 0.1}px)` }}>
                    <h1 className="text-5xl md:text-7xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
                        Recruiva
                    </h1>
                    <h2 className="text-xl md:text-3xl mb-6 font-light">
                        AI-Driven Recruitment Automation | Market Opportunity
                    </h2>
                    <p className="text-lg md:text-xl mb-12 font-medium">
                        Seeking $1.5M Seed Investment • 24-Month Runway • $1.5M 2025 Revenue Target
                    </p>

                    <div className="animate-bounce mt-16">
                        <svg className="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                    </div>
                </div>
            </section>

            {/* Key Investment Highlights */}
            <section
                ref={sectionRefs.highlights}
                className="relative z-20 py-16 bg-white"
            >
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
                        Key Investment Highlights
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {/* Card 1 */}
                        <div
                            className={`transform ${highlightsInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-0 bg-white rounded-xl shadow-lg border-t-4 border-purple-500 overflow-hidden`}
                        >
                            <div className="p-6">
                                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center mb-4 animate-pulse">
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <h3 className="text-xl font-bold mb-2 text-gray-800">Massive Market</h3>
                                <p className="font-medium mb-3 text-gray-700">$617.5M global AI recruitment market with 7.2% CAGR</p>
                                <div className="bg-gray-100 rounded-lg p-3">
                                    <p className="text-sm text-gray-700">Projected growth to $1.52B by 2037</p>
                                </div>
                            </div>
                        </div>

                        {/* Card 2 */}
                        <div
                            className={`transform ${highlightsInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300 bg-white rounded-xl shadow-lg border-t-4 border-blue-500 overflow-hidden`}
                        >
                            <div className="p-6">
                                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-pink-500 flex items-center justify-center mb-4 animate-pulse">
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <h3 className="text-xl font-bold mb-2 text-gray-800">Revolutionary Efficiency</h3>
                                <p className="font-medium mb-3 text-gray-700">Transforms entire recruitment process</p>
                                <div className="bg-gray-100 rounded-lg p-3">
                                    <p className="text-sm text-gray-700">Time reduction -92%, Cost reduction -89%</p>
                                </div>
                            </div>
                        </div>

                        {/* Card 3 */}
                        <div
                            className={`transform ${highlightsInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-600 bg-white rounded-xl shadow-lg border-t-4 border-pink-500 overflow-hidden`}
                        >
                            <div className="p-6">
                                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 flex items-center justify-center mb-4 animate-pulse">
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                                    </svg>
                                </div>
                                <h3 className="text-xl font-bold mb-2 text-gray-800">Exceptional Economics</h3>
                                <p className="font-medium mb-3 text-gray-700">Industry-leading margins and metrics</p>
                                <div className="bg-gray-100 rounded-lg p-3">
                                    <p className="text-sm text-gray-700">Gross margin 95%+, LTV:CAC ratio 12:1</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div
                        className={`transform ${highlightsInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-900 mt-10 bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500 rounded-xl p-6 text-white text-center`}
                    >
                        <h3 className="text-xl font-bold">Unicorn Potential: Clear path to $1B valuation within 5-7 years</h3>
                    </div>
                </div>
            </section>

            {/* Market Size Analysis */}
            <section
                ref={sectionRefs.market}
                className="py-16 bg-gradient-to-br from-gray-100 to-white"
            >
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
                        Market Size Analysis
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                        {/* Market Size Chart */}
                        <div
                            className={`transform ${marketInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 bg-white rounded-xl shadow-lg overflow-hidden`}
                        >
                            <div className="p-6">
                                <h3 className="text-xl font-bold mb-4 text-gray-800">Market Size (in Billions $)</h3>
                                <div className="h-64">
                                    <ResponsiveContainer width="100%" height="100%">
                                        <BarChart
                                            data={marketSizeData}
                                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                        >
                                            <CartesianGrid strokeDasharray="3 3" />
                                            <XAxis dataKey="name" />
                                            <YAxis />
                                            <Tooltip formatter={(value) => [`$${value}B`, 'Value']} />
                                            <Legend />
                                            <Bar
                                                dataKey="value"
                                                name="Market Size (Billions $)"
                                                animationBegin={0}
                                                animationDuration={2000}
                                            >
                                                {marketSizeData.map((entry, index) => (
                                                    <Cell key={`cell-${index}`} fill={entry.fill} />
                                                ))}
                                            </Bar>
                                        </BarChart>
                                    </ResponsiveContainer>
                                </div>
                                <div className="mt-4 text-sm">
                                    <p className="mb-2"><span className="font-bold text-gray-800">TAM:</span> <span className="text-gray-700">Total Addressable Market - $150B</span></p>
                                    <p className="mb-2"><span className="font-bold text-gray-800">SAM:</span> <span className="text-gray-700">Serviceable Available Market - $24B</span></p>
                                    <p><span className="font-bold text-gray-800">SOM:</span> <span className="text-gray-700">Serviceable Obtainable Market - $2.8B</span></p>
                                </div>
                            </div>
                        </div>

                        {/* Market Fragmentation */}
                        <div
                            className={`transform ${marketInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300 bg-white rounded-xl shadow-lg overflow-hidden`}
                        >
                            <div className="p-6">
                                <h3 className="text-xl font-bold mb-4 text-gray-800">Market Fragmentation</h3>
                                <div className="h-64">
                                    <ResponsiveContainer width="100%" height="100%">
                                        <PieChart>
                                            <Pie
                                                data={fragmentationData}
                                                cx="50%"
                                                cy="50%"
                                                labelLine={false}
                                                outerRadius={80}
                                                fill="#8884d8"
                                                dataKey="value"
                                                nameKey="name"
                                                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                                                animationBegin={0}
                                                animationDuration={2000}
                                            >
                                                {fragmentationData.map((entry, index) => (
                                                    <Cell key={`cell-${index}`} fill={entry.fill} />
                                                ))}
                                            </Pie>
                                            <Tooltip formatter={(value) => [`${value}%`, 'Market Share']} />
                                        </PieChart>
                                    </ResponsiveContainer>
                                </div>
                                <div className="mt-4 text-sm bg-gray-100 p-3 rounded-lg">
                                    <p className="font-bold text-gray-800">Recruiva Target: 2% by 2027</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Key Market Insights */}
                    <h3 className="text-2xl font-bold mb-6 text-center text-gray-800">Key Market Insights</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                        {[
                            {
                                title: "7.2% CAGR",
                                desc: "The AI recruitment market is experiencing steady growth as organizations recognize the efficiency gains of automation."
                            },
                            {
                                title: "Low AI Adoption",
                                desc: "Current AI adoption in recruitment remains under 20% globally, representing enormous growth potential."
                            },
                            {
                                title: "Fragmented Market",
                                desc: "No single player dominates the market, with 45% of the market spread across numerous smaller providers."
                            }
                        ].map((item, index) => (
                            <div
                                key={index}
                                className={`transform ${marketInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 bg-white bg-opacity-90 backdrop-blur rounded-xl shadow-lg p-6`}
                                style={{ transitionDelay: `${index * 200}ms` }}
                            >
                                <h4 className="text-xl font-bold mb-2 text-gray-800">{item.title}</h4>
                                <p className="text-gray-600">{item.desc}</p>
                            </div>
                        ))}
                    </div>

                    {/* Market Size Context */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div
                            className={`transform ${marketInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-600 bg-white rounded-xl shadow-lg p-6`}
                        >
                            <ul className="space-y-3">
                                <li className="flex items-start">
                                    <svg className="w-5 h-5 text-purple-500 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                    <span className="text-gray-700">Approximately 6 million new hires occur in the US every month</span>
                                </li>
                                <li className="flex items-start">
                                    <svg className="w-5 h-5 text-purple-500 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                    <span className="text-gray-700">Only 60% of these hires are facilitated through digital platforms</span>
                                </li>
                            </ul>
                        </div>

                        <div
                            className={`transform ${marketInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-800 bg-white rounded-xl shadow-lg p-6`}
                        >
                            <ul className="space-y-3">
                                <li className="flex items-start">
                                    <svg className="w-5 h-5 text-purple-500 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                    <span className="text-gray-700">AI-powered recruitment solutions currently penetrate less than 0.6% of all hiring processes</span>
                                </li>
                                <li className="flex items-start">
                                    <svg className="w-5 h-5 text-purple-500 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                    <span className="text-gray-700">This creates an enormous untapped market for Recruiva&apos;s solution</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            {/* Competitive Advantage */}
            <section
                ref={sectionRefs.competitive}
                className="py-16 bg-gradient-to-br from-gray-800 via-blue-900 to-purple-900 text-white"
            >
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-blue-400 to-pink-400">
                        Competitive Advantage
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-16">
                        {/* Competitive Positioning */}
                        <div
                            className={`transform ${competitiveInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 bg-white bg-opacity-10 backdrop-blur rounded-xl p-6`}
                        >
                            <h3 className="text-xl font-bold mb-6 text-white">Competitive Positioning</h3>

                            <div className="relative h-80 w-full bg-gray-900 bg-opacity-50 rounded-lg">
                                {/* Axis labels */}
                                <div className="absolute bottom-0 left-0 w-full text-center text-sm text-gray-300">Automation Level</div>
                                <div className="absolute top-1/2 left-0 transform -translate-y-1/2 -rotate-90 text-center text-sm text-gray-300">Interaction Quality</div>

                                {/* Quadrants */}
                                <div className="absolute top-0 left-0 w-1/2 h-1/2 border-r border-b border-gray-600"></div>
                                <div className="absolute top-0 right-0 w-1/2 h-1/2 border-l border-b border-gray-600"></div>
                                <div className="absolute bottom-0 left-0 w-1/2 h-1/2 border-r border-t border-gray-600"></div>
                                <div className="absolute bottom-0 right-0 w-1/2 h-1/2 border-l border-t border-gray-600"></div>

                                {/* Recruiva dot */}
                                <div className="absolute top-1/6 right-1/6 transform -translate-x-1/2 -translate-y-1/2">
                                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-xs font-bold animate-pulse">
                                        R
                                    </div>
                                    <div className="absolute top-8 left-1/2 transform -translate-x-1/2 text-xs font-bold text-white">
                                        Recruiva
                                    </div>
                                </div>

                                {/* Competitor dots */}
                                <div className="absolute top-1/4 right-1/3 transform -translate-x-1/2 -translate-y-1/2">
                                    <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-xs">
                                        C1
                                    </div>
                                </div>

                                <div className="absolute top-2/3 right-1/4 transform -translate-x-1/2 -translate-y-1/2">
                                    <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center text-xs">
                                        C2
                                    </div>
                                </div>

                                <div className="absolute top-1/3 right-2/3 transform -translate-x-1/2 -translate-y-1/2">
                                    <div className="w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center text-xs">
                                        C3
                                    </div>
                                </div>

                                <div className="absolute top-3/4 right-3/5 transform -translate-x-1/2 -translate-y-1/2">
                                    <div className="w-6 h-6 rounded-full bg-red-500 flex items-center justify-center text-xs">
                                        C4
                                    </div>
                                </div>
                            </div>

                            <div className="mt-4 text-sm text-gray-300">
                                <p>Recruiva uniquely combines industry-leading automation with high quality interactions, setting it apart from competitors who excel in just one dimension.</p>
                            </div>
                        </div>

                        {/* Unique Value Proposition */}
                        <div className={`transform ${competitiveInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300`}>
                            <h3 className="text-xl font-bold mb-6 text-white">Unique Value Proposition</h3>

                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                {/* VP Cards */}
                                {[
                                    {
                                        title: "Real-time Video/Voice AI Interviews",
                                        desc: "Conducts human-like conversations with adaptive questioning based on candidate responses",
                                        delay: 0
                                    },
                                    {
                                        title: "Screen Sharing for Visual Assessment",
                                        desc: "Enables evaluation of design, presentation, and visual skills for roles like graphic designers",
                                        delay: 200
                                    },
                                    {
                                        title: "Built-in Coding IDE",
                                        desc: "Allows technical assessments directly within the platform for software development roles",
                                        delay: 400
                                    },
                                    {
                                        title: "LLM Interaction Monitoring",
                                        desc: "Uniquely evaluates how candidates use AI tools - a critical skill for the modern workforce",
                                        delay: 600
                                    },
                                    {
                                        title: "End-to-End Integration",
                                        desc: "Seamless workflow from role definition to final evaluation with no gaps in the process",
                                        delay: 800
                                    },
                                    {
                                        title: "Superior Automation",
                                        desc: "Highest degree of automation in the market with 95%+ operating margins",
                                        delay: 1000
                                    }
                                ].map((card, index) => (
                                    <div
                                        key={index}
                                        className={`transform ${competitiveInView ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'} transition-all duration-700`}
                                        style={{ transitionDelay: `${card.delay}ms` }}
                                    >
                                        <div className="bg-white bg-opacity-10 backdrop-blur rounded-xl p-4 h-full">
                                            <h4 className="font-bold text-md mb-2">{card.title}</h4>
                                            <p className="text-sm text-gray-300">{card.desc}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Process Comparison */}
            <section
                ref={sectionRefs.process}
                className="py-16 bg-white"
            >
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
                        Process Comparison
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-12">
                        {/* Traditional Recruitment */}
                        <div
                            className={`transform ${processInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 bg-white rounded-xl shadow-lg overflow-hidden`}
                        >
                            <div className="bg-red-600 text-white px-6 py-4">
                                <h3 className="text-xl font-bold">Traditional Recruitment</h3>
                                <p className="text-sm">41+ hours, $3,200+ cost</p>
                            </div>

                            <div className="p-6">
                                <div className="space-y-6">
                                    {[
                                        { step: "Role Definition", time: "7h", cost: "$500" },
                                        { step: "Screening", time: "11h", cost: "$700" },
                                        { step: "Round 1", time: "10h", cost: "$900" },
                                        { step: "Round 2", time: "8h", cost: "$600" },
                                        { step: "Round 3", time: "4h", cost: "$300" },
                                        { step: "Evaluation", time: "4h", cost: "$200" }
                                    ].map((item, index) => (
                                        <div
                                            key={index}
                                            className={`flex items-center ${processInView ? 'opacity-100' : 'opacity-0'} transition-all duration-1000`}
                                            style={{ transitionDelay: `${index * 200}ms` }}
                                        >
                                            <div className="w-8 h-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center font-bold text-sm">
                                                {index + 1}
                                            </div>
                                            <div className="ml-4 flex-grow">
                                                <div className="font-medium">{item.step}</div>
                                                <div className="flex justify-between text-sm text-gray-600">
                                                    <span>{item.time}</span>
                                                    <span>{item.cost}</span>
                                                </div>
                                                <div className="mt-2 w-full h-2 bg-gray-200 rounded">
                                                    <div
                                                        className="h-full bg-red-500 rounded"
                                                        style={{
                                                            width: processInView ? '100%' : '0%',
                                                            transition: 'width 1s ease',
                                                            transitionDelay: `${(index * 200) + 200}ms`
                                                        }}
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Recruiva Process */}
                        <div
                            className={`transform ${processInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-500 bg-white rounded-xl shadow-lg overflow-hidden`}
                        >
                            <div className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-4">
                                <h3 className="text-xl font-bold">Recruiva Process</h3>
                                <p className="text-sm">3-4 hours, $338 cost</p>
                            </div>

                            <div className="p-6">
                                <div className="space-y-6">
                                    {[
                                        { step: "AI Role Definition", time: "15m", cost: "$15" },
                                        { step: "AI Screening", time: "20m", cost: "$20" },
                                        { step: "AI Interviews (with Video, Screen Sharing, IDE)", time: "3h", cost: "$300" },
                                        { step: "Automated Evaluation (incl. LLM Interaction)", time: "1m", cost: "<$1" },
                                        { step: "Full Funnel Automation", time: "", cost: "$2/applicant" }
                                    ].map((item, index) => (
                                        <div
                                            key={index}
                                            className={`flex items-center ${processInView ? 'opacity-100' : 'opacity-0'} transition-all duration-1000`}
                                            style={{ transitionDelay: `${(index * 200) + 500}ms` }}
                                        >
                                            <div className="w-8 h-8 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center font-bold text-sm">
                                                {index + 1}
                                            </div>
                                            <div className="ml-4 flex-grow">
                                                <div className="font-medium">{item.step}</div>
                                                <div className="flex justify-between text-sm text-gray-600">
                                                    <span>{item.time}</span>
                                                    <span>{item.cost}</span>
                                                </div>
                                                <div className="mt-2 w-full h-2 bg-gray-200 rounded">
                                                    <div
                                                        className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded"
                                                        style={{
                                                            width: processInView ? (index === 2 ? '80%' : '20%') : '0%',
                                                            transition: 'width 1s ease',
                                                            transitionDelay: `${(index * 200) + 700}ms`
                                                        }}
                                                    ></div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Supporting Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div
                            className={`transform ${processInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-1000 bg-gradient-to-r from-red-600 to-purple-600 text-white rounded-xl p-6`}
                        >
                            <h3 className="text-xl font-bold mb-4">Time Savings: -92%</h3>
                            <div className="relative h-8 bg-gray-800 bg-opacity-50 rounded">
                                <div className="h-full w-1/12 bg-white rounded" style={{
                                    width: processInView ? '8%' : '0%',
                                    transition: 'width 1.5s ease',
                                    transitionDelay: '1000ms'
                                }}></div>
                                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 font-bold text-gray-200">8%</div>
                            </div>
                            <div className="mt-2 text-gray-200">Recruiva process takes just 8% of the time compared to traditional recruiting</div>
                        </div>

                        <div
                            className={`transform ${processInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-1200 bg-gradient-to-r from-red-600 to-purple-600 text-white rounded-xl p-6`}
                        >
                            <h3 className="text-xl font-bold mb-4">Cost Reduction: -89%</h3>
                            <div className="relative h-8 bg-gray-800 bg-opacity-50 rounded">
                                <div className="h-full w-1/10 bg-white rounded" style={{
                                    width: processInView ? '11%' : '0%',
                                    transition: 'width 1.5s ease',
                                    transitionDelay: '1200ms'
                                }}></div>
                                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 font-bold text-gray-200">11%</div>
                            </div>
                            <div className="mt-2 text-gray-200">Recruiva costs just 11% of traditional recruitment processes</div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Financial Projections */}
            <section
                ref={sectionRefs.financial}
                className="py-16 bg-gradient-to-br from-gray-100 to-white"
            >
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
                        Financial Projections
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                        {/* Revenue Growth Chart */}
                        <div
                            className={`transform ${financialInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 bg-white rounded-xl shadow-lg overflow-hidden`}
                        >
                            <div className="p-6">
                                <h3 className="text-xl font-bold mb-6 text-gray-800">Revenue Growth (in Millions $)</h3>
                                <div className="h-64">
                                    <ResponsiveContainer width="100%" height="100%">
                                        <BarChart
                                            data={financialData}
                                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                        >
                                            <CartesianGrid strokeDasharray="3 3" />
                                            <XAxis dataKey="year" />
                                            <YAxis />
                                            <Tooltip formatter={(value) => [`$${value}M`, '']} />
                                            <Legend />
                                            <Bar
                                                dataKey="revenue"
                                                name="Revenue"
                                                fill="#8884d8"
                                                animationBegin={0}
                                                animationDuration={2000}
                                            />
                                            <Bar
                                                dataKey="profit"
                                                name="Operating Profit"
                                                fill="#82ca9d"
                                                animationBegin={500}
                                                animationDuration={2000}
                                            />
                                        </BarChart>
                                    </ResponsiveContainer>
                                </div>

                                <div className="grid grid-cols-3 gap-4 mt-6">
                                    {financialData.map((item, index) => (
                                        <div
                                            key={index}
                                            className={`bg-white bg-opacity-60 backdrop-blur p-3 rounded-lg shadow ${financialInView ? 'opacity-100' : 'opacity-0'} transition-all duration-1000`}
                                            style={{ transitionDelay: `${index * 200}ms` }}
                                        >
                                            <h4 className="font-bold text-center text-gray-800">{item.year}</h4>
                                            <div className="text-sm mt-2">
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Revenue:</span>
                                                    <span className="font-medium text-gray-800">${item.revenue}M</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-gray-600">Profit:</span>
                                                    <span className="font-medium text-gray-800">${item.profit}M</span>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Seed Funding Allocation */}
                        <div
                            className={`transform ${financialInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300 bg-white rounded-xl shadow-lg overflow-hidden`}
                        >
                            <div className="p-6">
                                <h3 className="text-xl font-bold mb-6 text-gray-800">Seed Funding Allocation ($1.5M)</h3>
                                <div className="h-64">
                                    <ResponsiveContainer width="100%" height="100%">
                                        <PieChart>
                                            <Pie
                                                data={fundingData}
                                                cx="50%"
                                                cy="50%"
                                                labelLine={true}
                                                outerRadius={80}
                                                fill="#8884d8"
                                                dataKey="value"
                                                nameKey="name"
                                                label={({ name, value }) => `${name}: ${value}%`}
                                                animationBegin={0}
                                                animationDuration={2000}
                                            >
                                                {fundingData.map((entry, index) => (
                                                    <Cell key={`cell-${index}`} fill={entry.fill} />
                                                ))}
                                            </Pie>
                                            <Tooltip formatter={(value) => [`${value}%`, 'Allocation']} />
                                        </PieChart>
                                    </ResponsiveContainer>
                                </div>

                                <div
                                    className={`mt-6 bg-gradient-to-r from-purple-500 to-blue-500 p-4 rounded-lg text-center ${financialInView ? 'opacity-100' : 'opacity-0'} transition-all duration-1000 delay-600`}
                                >
                                    <p className="font-bold text-white">24-Month Runway</p>
                                    <p className="text-sm text-gray-100">To aggressively grow our customer base and establish market leadership</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Financial Metrics Section */}
                    <h3 className="text-2xl font-bold mb-6 text-center text-gray-800">Financial Metrics</h3>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        {[
                            {
                                title: "Gross Margin",
                                value: "95%+",
                                compare: "vs. industry average of 65-70%",
                                delay: 0
                            },
                            {
                                title: "LTV:CAC Ratio",
                                value: "12:1",
                                compare: "vs. SaaS benchmark of 3:1",
                                delay: 200
                            },
                            {
                                title: "Avg. Contract Value",
                                value: "$42K",
                                compare: "per year",
                                delay: 400
                            },
                            {
                                title: "Customer Retention",
                                value: "85%",
                                compare: "annual retention rate",
                                delay: 600
                            }
                        ].map((metric, index) => (
                            <div
                                key={index}
                                className={`transform ${financialInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 bg-white rounded-xl shadow-lg p-6 text-center`}
                                style={{ transitionDelay: `${metric.delay}ms` }}
                            >
                                <h4 className="text-lg font-bold mb-2 text-gray-800">{metric.title}</h4>
                                <div className="text-2xl font-bold text-purple-600 mb-2">{metric.value}</div>
                                <p className="text-sm text-gray-600">{metric.compare}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Path to Unicorn Status */}
            <section
                ref={sectionRefs.unicorn}
                className="py-16 bg-gradient-to-br from-gray-800 via-blue-900 to-purple-900 text-white"
            >
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-blue-400 to-pink-400">
                        Path to Unicorn Status
                    </h2>

                    {/* Timeline */}
                    <div
                        className={`relative max-w-4xl mx-auto mb-16 py-8 ${unicornInView ? 'opacity-100' : 'opacity-0'} transition-all duration-1000`}
                    >
                        <div className="absolute top-0 bottom-0 left-1/2 w-1 bg-gradient-to-b from-purple-500 via-blue-500 to-pink-500 transform -translate-x-1/2"></div>

                        {/* 2025 Milestone */}
                        <div
                            className={`relative mb-12 ${unicornInView ? 'opacity-100' : 'opacity-0'} transition-all duration-1000`}
                        >
                            <div className="flex items-center mb-4">
                                <div className="absolute left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full bg-purple-500 border-4 border-gray-800 z-10"></div>
                                <div className="w-1/2 pr-8 text-right">
                                    <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">2025</h3>
                                </div>
                                <div className="w-1/2 pl-8">
                                    <div className="bg-white bg-opacity-10 backdrop-blur rounded-xl p-4">
                                        <ul className="space-y-2 text-gray-200">
                                            <li className="flex items-center">
                                                <svg className="w-5 h-5 text-purple-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                                <span>$1.5M Annual Revenue</span>
                                            </li>
                                            <li className="flex items-center">
                                                <svg className="w-5 h-5 text-purple-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                                <span>35-40 Customers</span>
                                            </li>
                                            <li className="flex items-center">
                                                <svg className="w-5 h-5 text-purple-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                                <span>$22.5-30M Valuation</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* 2027 Milestone */}
                        <div
                            className={`relative mb-12 ${unicornInView ? 'opacity-100' : 'opacity-0'} transition-all duration-1000 delay-300`}
                        >
                            <div className="flex items-center mb-4">
                                <div className="absolute left-1/2 transform -translate-x-1/2 w-8 h-8 rounded-full bg-blue-500 border-4 border-gray-800 z-10"></div>
                                <div className="w-1/2 pr-8 text-right">
                                    <div className="bg-white bg-opacity-10 backdrop-blur rounded-xl p-4">
                                        <ul className="space-y-2 text-gray-200">
                                            <li className="flex items-center justify-end">
                                                <span>$15.2M Annual Revenue</span>
                                                <svg className="w-5 h-5 text-blue-400 ml-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                            </li>
                                            <li className="flex items-center justify-end">
                                                <span>300+ Customers</span>
                                                <svg className="w-5 h-5 text-blue-400 ml-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                            </li>
                                            <li className="flex items-center justify-end">
                                                <span>$228-304M Valuation</span>
                                                <svg className="w-5 h-5 text-blue-400 ml-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div className="w-1/2 pl-8">
                                    <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-pink-400">2027</h3>
                                </div>
                            </div>
                        </div>

                        {/* 2030 Milestone */}
                        <div
                            className={`relative ${unicornInView ? 'opacity-100' : 'opacity-0'} transition-all duration-1000 delay-600`}
                        >
                            <div className="flex items-center mb-4">
                                <div className="absolute left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full bg-pink-500 border-4 border-gray-800 z-10 animate-pulse"></div>
                                <div className="w-1/2 pr-8 text-right">
                                    <h3 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-pink-400 to-purple-400">2030</h3>
                                </div>
                                <div className="w-1/2 pl-8">
                                    <div className="bg-gradient-to-r from-pink-500 to-purple-500 rounded-xl p-4">
                                        <div className="bg-white bg-opacity-10 backdrop-blur rounded-lg p-3">
                                            <ul className="space-y-2 text-gray-200">
                                                <li className="flex items-center">
                                                    <svg className="w-5 h-5 text-white mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                    </svg>
                                                    <span>$50-67M Annual Revenue</span>
                                                </li>
                                                <li className="flex items-center">
                                                    <svg className="w-5 h-5 text-white mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                    </svg>
                                                    <span>1,000+ Customers</span>
                                                </li>
                                                <li className="flex items-center">
                                                    <svg className="w-5 h-5 text-white mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                    </svg>
                                                    <span className="font-bold">$1B+ Valuation (Unicorn Status)</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Key Valuation Drivers */}
                    <div className="mb-12">
                        <h3 className="text-2xl font-bold mb-6 text-center">Key Valuation Drivers</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {[
                                {
                                    title: "Technology Moat",
                                    desc: "Proprietary AI algorithms, multimodal capabilities, and unique features like LLM interaction monitoring create a defensible competitive advantage",
                                    delay: 0
                                },
                                {
                                    title: "Network Effects",
                                    desc: "Growing database of candidate interactions and outcomes improves AI performance over time",
                                    delay: 300
                                },
                                {
                                    title: "Scalability",
                                    desc: "Near-zero marginal cost for additional interviews and assessments allows for exponential growth",
                                    delay: 600
                                }
                            ].map((card, index) => (
                                <div
                                    key={index}
                                    className={`transform ${unicornInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 bg-white bg-opacity-10 backdrop-blur rounded-xl p-6`}
                                    style={{ transitionDelay: `${card.delay + 900}ms` }}
                                >
                                    <h4 className="text-xl font-bold mb-3 text-gray-200">{card.title}</h4>
                                    <p className="text-gray-300">{card.desc}</p>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Exit Opportunities */}
                    <div>
                        <h3 className="text-2xl font-bold mb-6 text-center">Exit Opportunities</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {[
                                {
                                    title: "Strategic Acquisition",
                                    desc: "Attractive acquisition target for major ATS/HRIS platforms looking to enhance AI capabilities",
                                    delay: 0
                                },
                                {
                                    title: "IPO",
                                    desc: "Strong financials and market leadership position could support a successful public offering",
                                    delay: 300
                                },
                                {
                                    title: "Private Equity",
                                    desc: "Exceptional unit economics and profitability attract PE interest for majority stake acquisition",
                                    delay: 600
                                }
                            ].map((card, index) => (
                                <div
                                    key={index}
                                    className={`transform ${unicornInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 bg-white bg-opacity-10 backdrop-blur rounded-xl p-6`}
                                    style={{ transitionDelay: `${card.delay + 1200}ms` }}
                                >
                                    <h4 className="text-xl font-bold mb-3 text-gray-200">{card.title}</h4>
                                    <p className="text-gray-300">{card.desc}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </section>

            {/* Investment ROI Analysis */}
            <section
                ref={sectionRefs.roi}
                className="py-16 bg-white"
            >
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
                        Investment ROI Analysis
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                        {/* ROI Breakdown */}
                        <div
                            className={`transform ${roiInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 bg-white rounded-xl shadow-lg p-6`}
                        >
                            <h3 className="text-xl font-bold mb-6 text-gray-800">Detailed Breakdown of Investor Returns</h3>

                            <div className="space-y-4">
                                {[
                                    {
                                        title: "Year 3 Valuation",
                                        value: "$228-304M",
                                        desc: "(15-20x revenue)"
                                    },
                                    {
                                        title: "ROI Potential",
                                        value: "150-200x",
                                        desc: "return on initial investment within 5-7 years"
                                    },
                                    {
                                        title: "Annual Return Rate",
                                        value: "165-195%",
                                        desc: "year-over-year"
                                    },
                                    {
                                        title: "Multiple Capital Return Opportunities",
                                        value: "Series A, B, and exit"
                                    },
                                    {
                                        title: "Low Operational Costs",
                                        value: "5%",
                                        desc: "vs. industry average of 30-35% due to AI-first approach"
                                    }
                                ].map((item, index) => (
                                    <div
                                        key={index}
                                        className={`flex items-center p-4 border-l-4 border-purple-500 bg-gray-50 rounded-r-lg ${roiInView ? 'opacity-100' : 'opacity-0'} transition-all duration-500`}
                                        style={{ transitionDelay: `${index * 200}ms` }}
                                    >
                                        <div>
                                            <h4 className="font-bold text-gray-800">{item.title}</h4>
                                            <div className="flex items-baseline">
                                                <span className="text-lg font-bold text-purple-600 mr-2">{item.value}</span>
                                                {item.desc && <span className="text-sm text-gray-600">{item.desc}</span>}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Market Context */}
                        <div
                            className={`transform ${roiInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-500 bg-white rounded-xl shadow-lg p-6`}
                        >
                            <h3 className="text-xl font-bold mb-6 text-gray-800">Market Context</h3>

                            <div
                                className={`bg-gradient-to-r from-purple-500 to-blue-500 p-4 rounded-lg mb-6 ${roiInView ? 'opacity-100' : 'opacity-0'} transition-all duration-1000 delay-700`}
                            >
                                <p className="text-white">Despite the competitive landscape, the market size and low penetration rate of current AI solutions create an enormous opportunity</p>
                            </div>

                            <div className="space-y-4">
                                {[
                                    "With 6 million monthly hires in the US alone, and only 60% using digital platforms, the addressable market is significantly underserved",
                                    "Current AI recruitment penetration is estimated at just 0.6% of all digital hiring processes",
                                    "Even capturing a modest 2% market share by 2027 positions Recruiva for unicorn status by 2030"
                                ].map((item, index) => (
                                    <div
                                        key={index}
                                        className={`flex items-start p-4 border-l-4 border-blue-500 bg-gray-50 rounded-r-lg ${roiInView ? 'opacity-100' : 'opacity-0'} transition-all duration-500`}
                                        style={{ transitionDelay: `${(index * 200) + 900}ms` }}
                                    >
                                        <svg className="w-5 h-5 text-blue-500 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        <p className="text-gray-700">{item}</p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* ROI Visualization */}
                    <div
                        className={`bg-white rounded-xl shadow-lg p-6 ${roiInView ? 'opacity-100' : 'opacity-0'} transition-all duration-1000 delay-1100`}
                    >
                        <h3 className="text-xl font-bold mb-6 text-center text-gray-800">Investment Growth Timeline</h3>

                        <div className="relative h-16 mb-8">
                            <div className="absolute top-1/2 left-0 right-0 h-2 bg-gray-200 transform -translate-y-1/2 rounded-full"></div>

                            {/* Timeline points */}
                            <div
                                className="absolute top-1/2 left-0 w-6 h-6 bg-purple-500 rounded-full transform -translate-y-1/2 -translate-x-1/2"
                                style={{ transition: 'left 1.5s ease', left: roiInView ? '0%' : '0%' }}
                            >
                                <div className="absolute bottom-full mb-2 transform -translate-x-1/2 text-center w-20">
                                    <div className="font-bold text-gray-800">Seed</div>
                                    <div className="text-sm text-gray-600">$1.5M</div>
                                </div>
                            </div>

                            <div
                                className="absolute top-1/2 w-6 h-6 bg-blue-500 rounded-full transform -translate-y-1/2 -translate-x-1/2"
                                style={{ transition: 'left 1.5s ease', left: roiInView ? '25%' : '0%', transitionDelay: '300ms' }}
                            >
                                <div className="absolute bottom-full mb-2 transform -translate-x-1/2 text-center w-20">
                                    <div className="font-bold text-gray-800">Series A</div>
                                    <div className="text-sm text-gray-600">$30M</div>
                                </div>
                            </div>

                            <div
                                className="absolute top-1/2 w-6 h-6 bg-pink-500 rounded-full transform -translate-y-1/2 -translate-x-1/2"
                                style={{ transition: 'left 1.5s ease', left: roiInView ? '50%' : '0%', transitionDelay: '600ms' }}
                            >
                                <div className="absolute top-full mt-2 transform -translate-x-1/2 text-center w-20">
                                    <div className="font-bold text-gray-800">Series B</div>
                                    <div className="text-sm text-gray-600">$250M</div>
                                </div>
                            </div>

                            <div
                                className="absolute top-1/2 w-6 h-6 bg-green-500 rounded-full transform -translate-y-1/2 -translate-x-1/2"
                                style={{ transition: 'left 1.5s ease', left: roiInView ? '75%' : '0%', transitionDelay: '900ms' }}
                            >
                                <div className="absolute bottom-full mb-2 transform -translate-x-1/2 text-center w-20">
                                    <div className="font-bold text-gray-800">Series C</div>
                                    <div className="text-sm text-gray-600">$500M</div>
                                </div>
                            </div>

                            <div
                                className="absolute top-1/2 w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transform -translate-y-1/2 -translate-x-1/2 animate-pulse"
                                style={{ transition: 'left 1.5s ease', left: roiInView ? '100%' : '0%', transitionDelay: '1200ms' }}
                            >
                                <div className="absolute top-full mt-2 transform -translate-x-1/2 text-center w-24">
                                    <div className="font-bold text-gray-800">Unicorn</div>
                                    <div className="text-sm text-gray-600">$1B+ Valuation</div>
                                </div>
                            </div>

                            {/* Progress bar */}
                            <div
                                className="absolute top-1/2 left-0 h-2 bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500 transform -translate-y-1/2 rounded-full"
                                style={{ transition: 'width 2s ease', width: roiInView ? '100%' : '0%', transitionDelay: '300ms' }}
                            ></div>
                        </div>

                        <div className="text-center text-sm text-gray-600">Seed investors will see multiple returns as the company progresses through funding rounds to unicorn status</div>
                    </div>

                    {/* Seed Funding Allocation */}
                    <div
                        className={`transform ${financialInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-300 bg-white rounded-xl shadow-lg overflow-hidden`}
                    >
                        <div className="p-6">
                            <h3 className="text-xl font-bold mb-6 text-gray-800">Seed Funding Allocation ($1.5M)</h3>
                            <div className="h-64">
                                <ResponsiveContainer width="100%" height="100%">
                                    <PieChart>
                                        <Pie
                                            data={fundingData}
                                            cx="50%"
                                            cy="50%"
                                            labelLine={true}
                                            outerRadius={80}
                                            fill="#8884d8"
                                            dataKey="value"
                                            nameKey="name"
                                            label={({ name, value }) => `${name}: ${value}%`}
                                            animationBegin={0}
                                            animationDuration={2000}
                                        >
                                            {fundingData.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={entry.fill} />
                                            ))}
                                        </Pie>
                                        <Tooltip formatter={(value) => [`${value}%`, 'Allocation']} />
                                    </PieChart>
                                </ResponsiveContainer>
                            </div>

                            <div
                                className={`mt-6 bg-gradient-to-r from-purple-500 to-blue-500 p-4 rounded-lg text-center ${financialInView ? 'opacity-100' : 'opacity-0'} transition-all duration-1000 delay-600`}
                            >
                                <p className="font-bold text-white">24-Month Runway</p>
                                <p className="text-sm text-gray-100">To aggressively grow our customer base and establish market leadership</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Enhanced Value with Advanced Features */}
            <section
                ref={sectionRefs.features}
                className="py-16 bg-gradient-to-br from-gray-100 to-white"
            >
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500">
                        Enhanced Value with Advanced Features
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        {[
                            {
                                title: "Real-time Video Interviews",
                                icon: (
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                ),
                                marketImpact: "Expands addressable market by 15% to include visual assessment roles",
                                roi: "Reduces need for multiple interview rounds, saving an additional 4-6 hours per hire",
                                advantage: "Only 2 of 15 competitors offer any real-time video capabilities",
                                delay: 0,
                                color: "from-purple-500 to-blue-500"
                            },
                            {
                                title: "Screen Sharing for Visual Assessment",
                                icon: (
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                ),
                                marketImpact: "Captures $45B design/creative hiring market",
                                roi: "Eliminates separate portfolio review sessions",
                                advantage: "Unique capability not matched by any direct competitor",
                                delay: 300,
                                color: "from-blue-500 to-pink-500"
                            },
                            {
                                title: "Built-in Coding IDE",
                                icon: (
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                                    </svg>
                                ),
                                marketImpact: "Addresses $120B technical hiring market",
                                roi: "Eliminates separate technical assessment tools (saving $50-200 per hire)",
                                advantage: "More seamless than even specialized tools like Mercor",
                                delay: 600,
                                color: "from-pink-500 to-purple-500"
                            },
                            {
                                title: "LLM Interaction Monitoring",
                                icon: (
                                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                                    </svg>
                                ),
                                marketImpact: "First-to-market with AI utilization assessment",
                                roi: "Future-proofs hiring by evaluating tomorrow's most critical skills",
                                advantage: "No competitor offers any similar capability",
                                delay: 900,
                                color: "from-purple-500 to-blue-500"
                            }
                        ].map((feature, index) => (
                            <div
                                key={index}
                                className={`transform ${featuresInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 bg-white rounded-xl shadow-lg overflow-hidden`}
                                style={{ transitionDelay: `${feature.delay}ms` }}
                            >
                                <div className={`bg-gradient-to-r ${feature.color} p-6 flex items-center`}>
                                    <div className="w-12 h-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center mr-4">
                                        {feature.icon}
                                    </div>
                                    <h3 className="text-xl font-bold text-white">{feature.title}</h3>
                                </div>

                                <div className="p-6">
                                    <div className="space-y-4">
                                        <div className="flex items-start">
                                            <div className="w-8 h-8 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center font-bold text-sm mr-3 mt-1">
                                                M
                                            </div>
                                            <div>
                                                <h4 className="font-bold mb-2 text-gray-800">Market Impact</h4>
                                                <p className="text-gray-700">{feature.marketImpact}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start">
                                            <div className="w-8 h-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center font-bold text-sm mr-3 mt-1">
                                                R
                                            </div>
                                            <div>
                                                <h4 className="font-bold mb-1 text-gray-800">ROI Enhancement</h4>
                                                <p className="text-gray-700">{feature.roi}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start">
                                            <div className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center font-bold text-sm mr-3 mt-1">
                                                C
                                            </div>
                                            <div>
                                                <h4 className="font-bold mb-1 text-gray-800">Competitive Advantage</h4>
                                                <p className="text-gray-700">{feature.advantage}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Investment Summary */}
            <section
                ref={sectionRefs.summary}
                className="py-16 bg-gradient-to-br from-gray-800 via-blue-900 to-purple-900 text-white"
            >
                <div className="container mx-auto px-4">
                    <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-blue-400 to-pink-400">
                        Investment Summary
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                        {[
                            {
                                value: "$1.52B",
                                label: "Total addressable market by 2037 with 7.2% CAGR",
                                delay: 0
                            },
                            {
                                value: "95%+",
                                label: "Industry-leading gross margins through AI-first approach",
                                delay: 300
                            },
                            {
                                value: "-92%",
                                label: "Reduction in hiring time through revolutionary automation",
                                delay: 600
                            }
                        ].map((item, index) => (
                            <div
                                key={index}
                                className={`transform ${summaryInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 bg-gray-800 bg-opacity-90 backdrop-blur rounded-xl p-6 text-center`}
                                style={{ transitionDelay: `${item.delay}ms` }}
                            >
                                <div className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 mb-3">
                                    {item.value}
                                </div>
                                <p className="text-gray-200">{item.label}</p>
                            </div>
                        ))}
                    </div>

                    <div
                        className={`transform ${summaryInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-900 bg-gray-800 bg-opacity-90 backdrop-blur rounded-xl p-8 mb-12 max-w-4xl mx-auto`}
                    >
                        <h3 className="text-xl font-bold mb-4 text-gray-200">Feature Highlight</h3>
                        <p className="text-gray-300">
                            Recruiva&apos;s unique combination of real-time video interviews, screen sharing, built-in coding IDE, and LLM interaction monitoring creates an unmatched platform for evaluating both traditional and emerging skills critical for today&apos;s workforce.
                        </p>
                    </div>

                    <div
                        className={`transform ${summaryInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-1200 bg-gray-800 bg-opacity-90 backdrop-blur rounded-xl p-8 mb-12 max-w-4xl mx-auto`}
                    >
                        <h3 className="text-xl font-bold mb-4 text-gray-200">Closing Statement</h3>
                        <p className="text-gray-300 mb-4">
                            Recruiva represents a unique investment opportunity in the growing AI recruitment space. With a revolutionary product featuring capabilities no competitor offers, exceptional unit economics driven by an AI-first approach, and a clear path to unicorn status, Recruiva is positioned to capture significant market share and generate outstanding returns for early investors.
                        </p>
                        <p className="text-gray-300">
                            Our platform is fully built and market-ready, with 3 pilot customers already secured. The $1.5M seed funding will provide a 24-month runway to aggressively grow sales and establish market leadership.
                        </p>
                    </div>

                    <div className="text-center">
                        <button
                            className={`transform ${summaryInView ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'} transition-all duration-1000 delay-1500 bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500 text-white font-bold py-3 px-8 rounded-full hover:shadow-lg hover:scale-105`}
                        >
                            Contact Us To Learn More
                        </button>
                    </div>
                </div>
            </section>
        </div>
    );
}