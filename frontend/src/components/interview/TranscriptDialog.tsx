'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { MessageSquare } from 'lucide-react';
import { Badge } from '@/components/ui/Badge';

interface Message {
  role: string;
  content: string;
  timestamp?: string;
  type?: string;
}

interface Transcript {
  id?: string;
  messages?: Message[];
  status?: string;
}

interface TranscriptDialogProps {
  transcript: Transcript | null;
  interviewId: string;
  stageName?: string;
}

export function useTranscriptDialog() {
  const [open, setOpen] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState<Transcript | null>(null);
  const [currentInterviewId, setCurrentInterviewId] = useState<string>('');
  const [currentStageName, setCurrentStageName] = useState<string>('');

  const showTranscript = (transcript: Transcript | null, interviewId: string, stageName?: string) => {
    setCurrentTranscript(transcript);
    setCurrentInterviewId(interviewId);
    setCurrentStageName(stageName || 'Interview');
    setOpen(true);
  };

  const dialog = (
    <TranscriptDialog
      transcript={currentTranscript}
      interviewId={currentInterviewId}
      stageName={currentStageName}
      open={open}
      onOpenChange={setOpen}
    />
  );

  return { showTranscript, dialog };
}

interface TranscriptDialogComponentProps extends TranscriptDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

function TranscriptDialog({
  transcript,
  stageName = 'Interview',
  open,
  onOpenChange
}: TranscriptDialogComponentProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-blue-500" />
            <span>{stageName} Transcript</span>
            {transcript?.id && (
              <Badge variant="outline" className="ml-2 text-xs">
                ID: {transcript.id}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-slate-50 dark:bg-slate-900/50 rounded-md border border-slate-200 dark:border-slate-700 my-2">
          {transcript && transcript.messages && transcript.messages.length > 0 ? (
            transcript.messages.map((message, index) => (
              <div
                key={index}
                className={`flex flex-col ${message.role === 'user' ? 'items-end' : 'items-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-3 shadow-sm ${
                    message.role === 'user'
                      ? 'bg-blue-100 text-blue-900 dark:bg-primary/20 dark:text-slate-100'
                      : 'bg-slate-200 text-slate-900 dark:bg-slate-700/60 dark:text-slate-100'
                  }`}
                >
                  <p className="text-xs text-slate-600 dark:text-slate-400 mb-1">
                    {message.role === 'user' ? 'Candidate' : 'Recruiva'}
                  </p>
                  <p className="whitespace-pre-wrap">{message.content}</p>
                  {message.timestamp && (
                    <p className="text-xs text-slate-500 dark:text-slate-500 mt-1 text-right">
                      {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <div className="bg-slate-100 dark:bg-slate-800 w-14 h-14 rounded-full flex items-center justify-center mx-auto mb-3">
                <MessageSquare className="h-6 w-6 text-slate-400" />
              </div>
              <p className="font-medium text-slate-700 dark:text-slate-300 mb-1">
                No Transcript Available
              </p>
              <p className="text-sm text-slate-500 max-w-md mx-auto">
                This interview session does not have a recorded transcript or the transcript data could not be retrieved.
              </p>
              {transcript?.id && (
                <div className="mt-3">
                  <Badge variant="outline" className="bg-slate-100 dark:bg-slate-800 text-xs">
                    Transcript ID: {transcript.id}
                  </Badge>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <Button variant="secondary" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
