"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider, type ThemeProviderProps } from "next-themes"
import { usePathname } from "next/navigation"

/**
 * Wrapper around next-themes ThemeProvider with custom logic
 * - The pitch, financials and competitors pages will always use dark theme (forced)
 * - Public pages like landing, jobs, and application forms will respect user preferences
 * - User preferences are stored in localStorage for consistent experience
 */
export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  const pathname = usePathname()
  
  // Check if current path is a page that should always use dark theme (restricted pages)
  const isForcedDarkPage = pathname?.startsWith('/pitch') || 
                          pathname?.startsWith('/financials') || 
                          pathname?.startsWith('/competitors')
  
  return (
    <NextThemesProvider 
      {...props}
      forcedTheme={isForcedDarkPage ? 'dark' : undefined}
      enableSystem
      storageKey="recruiva-theme"
    >
      {children}
    </NextThemesProvider>
  )
} 