'use client';

import { ReactNode, useEffect } from 'react';
import { ThemeProvider } from 'next-themes';

/**
 * Provider component for handling theme in video call pages
 * Forces dark theme for optimal video call experience
 * Ensures theme cannot be changed during video calls
 */
interface VideoCallThemeProviderProps {
  children: ReactNode;
}

export function VideoCallThemeProvider({ 
  children 
}: VideoCallThemeProviderProps) {
  // Effect to ensure dark mode is always used and can't be overridden
  useEffect(() => {
    // Store original theme to restore later if needed
    
    // Force dark theme in video calls
    document.documentElement.classList.add('dark');
    
    return () => {
      // Clean up only happens when component unmounts
      // Don't remove dark class as it could affect navigation
    };
  }, []);
  
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="dark"
      enableSystem={false}
      forcedTheme="dark"
      disableTransitionOnChange
    >
      {children}
    </ThemeProvider>
  );
} 