import React, { useState, useEffect } from 'react';
import { DollarSign, Clock } from 'lucide-react';
import { cn } from '@/lib/utils/styles';
import { Slider } from '@/components/ui/Slider';
import { Knob } from 'primereact/knob';
import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import './ROICalculator.css';

interface ROICalculatorProps {
  isDarkMode: boolean;
}

const ROICalculator: React.FC<ROICalculatorProps> = ({ isDarkMode }) => {
  const [hiresPerMonth, setHiresPerMonth] = useState(5);
  // Fixed senior percentage at 50%
  const seniorPercentage = 20;
  const [isAnnual, setIsAnnual] = useState(true);
  const [savings, setSavings] = useState({ cost: 0, time: 0 });
  const [isCalculating, setIsCalculating] = useState(false);

  // Constants for calculations
  const JUNIOR_COST = 1500;
  const SENIOR_COST = 7500;
  const JUNIOR_TIME = 1; // months
  const SENIOR_TIME = 3; // months
  const RECRUIVA_COST_PER_HIRE = 500; // simplified for calculation
  const TIME_REDUCTION_FACTOR = 0.7; // 80% time reduction

  // Calculate ROI whenever inputs change
  useEffect(() => {
    setIsCalculating(true);

    // Short timeout to allow for animation
    const timer = setTimeout(() => {
      calculateROI();
      setIsCalculating(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [hiresPerMonth, isAnnual]);

  const calculateROI = () => {
    // Calculate total hires based on monthly or annual view
    const totalHires = isAnnual ? hiresPerMonth * 12 : hiresPerMonth;

    // Calculate number of junior and senior hires
    const seniorHires = Math.round(totalHires * (seniorPercentage / 100));
    const juniorHires = totalHires - seniorHires;

    // Traditional costs
    const traditionalJuniorCost = juniorHires * JUNIOR_COST;
    const traditionalSeniorCost = seniorHires * SENIOR_COST;
    const traditionalTotalCost = traditionalJuniorCost + traditionalSeniorCost;

    // Recruiva costs (simplified)
    const recruivaTotalCost = totalHires * RECRUIVA_COST_PER_HIRE;

    // Cost savings
    const costSavings = traditionalTotalCost - recruivaTotalCost;

    // Time calculations in hours
    // Convert months to hours (assuming 160 working hours per month)
    const HOURS_PER_MONTH = 160;
    const traditionalJuniorTimeHours = juniorHires * JUNIOR_TIME * HOURS_PER_MONTH;
    const traditionalSeniorTimeHours = seniorHires * SENIOR_TIME * HOURS_PER_MONTH;
    const traditionalTotalTimeHours = traditionalJuniorTimeHours + traditionalSeniorTimeHours;

    // Time with Recruiva (80% reduction)
    const recruivaTimeHours = traditionalTotalTimeHours * (1 - TIME_REDUCTION_FACTOR);

    // Time savings in hours
    const timeSavingsHours = traditionalTotalTimeHours - recruivaTimeHours;

    setSavings({
      cost: Math.max(0, costSavings),
      time: Math.max(0, timeSavingsHours)
    });
  };

  // Handle value changes from both slider and knob
  const handleSliderChange = (value: number[]) => {
    setHiresPerMonth(value[0]);
  };

  const handleKnobChange = (e: { value: number }) => {
    setHiresPerMonth(e.value);
  };

  return (
    <div className={cn(
      "w-full max-w-4xl mx-auto p-3 sm:p-4 rounded-xl transition-all duration-300",
      isDarkMode
        ? "bg-slate-800/60 border border-slate-700/40 shadow-xl shadow-slate-950/20 backdrop-blur-[6px] backdrop-saturate-[1.4]"
        : "bg-white/80 border border-slate-200/70 shadow-lg shadow-slate-200/30 backdrop-blur-sm"
    )}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Input Controls */}
        <div className="flex flex-col items-center justify-center">
          {/* Controls for Hires Per Month */}
          <div className="w-full max-w-xs mb-2">
            <div className="flex flex-col items-center justify-center space-y-4">
              {/* Title with Recruiva coloring */}
              <h3 className={cn(
                "text-base font-semibold mb-2 text-center",
                "text-transparent bg-clip-text bg-gradient-to-r",
                isDarkMode
                  ? "from-indigo-400 via-purple-400 to-pink-400"
                  : "from-indigo-600 via-purple-600 to-pink-600"
              )}>
                Number of Hires per Month
              </h3>

              {/* Knob for Hires Per Month */}
              <div className="flex flex-col items-center justify-center">
                <Knob
                  value={hiresPerMonth}
                  onChange={handleKnobChange}
                  min={1}
                  max={50}
                  size={100}
                  strokeWidth={8}
                  textColor={isDarkMode ? '#f1f5f9' : '#1e293b'}
                  valueColor={isDarkMode ? '#a78bfa' : '#7c3aed'}
                  rangeColor={isDarkMode ? '#334155' : '#e2e8f0'}
                  valueTemplate={'{value}'}
                  className="mb-1"
                />
              </div>

              {/* Slider for Hires Per Month */}
              <div className="w-full">
                <Slider
                  value={[hiresPerMonth]}
                  min={1}
                  max={50}
                  step={1}
                  onValueChange={handleSliderChange}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Results Display */}
        <div className={cn(
          "flex flex-col justify-center p-3 rounded-lg transition-all duration-300",
          isDarkMode
            ? "bg-slate-700/40 border border-slate-600/30"
            : "bg-white/80 border border-slate-200/70 backdrop-blur-sm",
          isCalculating ? "scale-95 opacity-80" : "scale-100 opacity-100"
        )}>
          <div className="flex justify-center mb-2">
            <div className={cn(
              "flex rounded-full p-1",
              isDarkMode ? "bg-slate-700" : "bg-slate-200/80 border border-slate-300/30"
            )}>
              <button
                className={cn(
                  "px-3 py-0.5 text-xs rounded-full transition-all",
                  !isAnnual
                    ? isDarkMode
                      ? "bg-slate-600 text-white"
                      : "bg-white/90 text-slate-800 shadow-sm"
                    : "text-slate-400"
                )}
                onClick={() => setIsAnnual(false)}
              >
                Monthly
              </button>
              <button
                className={cn(
                  "px-3 py-0.5 text-xs rounded-full transition-all",
                  isAnnual
                    ? isDarkMode
                      ? "bg-slate-600 text-white"
                      : "bg-white text-slate-800 shadow-sm"
                    : "text-slate-400"
                )}
                onClick={() => setIsAnnual(true)}
              >
                Annual
              </button>
            </div>
          </div>

          {/* Cost Savings */}
          <div className={cn(
            "p-2 rounded-lg mb-2 transition-all duration-300",
            isDarkMode ? "bg-slate-800/60" : "bg-white/90 border border-slate-200/70 shadow-sm",
            isCalculating ? "translate-y-2 opacity-80" : "translate-y-0 opacity-100"
          )}>
            <div className="flex items-center">
              <div className={cn(
                "p-2 rounded-full mr-3",
                isDarkMode ? "bg-green-900/30" : "bg-green-500/10"
              )}>
                <DollarSign className={cn("h-4 w-4", isDarkMode ? "text-green-400" : "text-green-600")} />
              </div>
              <div>
                <p className={cn("text-xs", isDarkMode ? "text-slate-300" : "text-slate-600")}>
                  Cost Savings
                </p>
                <p className={cn(
                  "text-lg font-bold transition-all duration-300",
                  isDarkMode ? "text-green-400" : "text-green-600"
                )}>
                  ${savings.cost.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          {/* Time Savings */}
          <div className={cn(
            "p-2 rounded-lg transition-all duration-300",
            isDarkMode ? "bg-slate-800/60" : "bg-white/90 border border-slate-200/70 shadow-sm",
            isCalculating ? "translate-y-2 opacity-80" : "translate-y-0 opacity-100",
            // Add a slight delay to the second card animation
            "transition-delay-100"
          )}>
            <div className="flex items-center">
              <div className={cn(
                "p-2 rounded-full mr-3",
                isDarkMode ? "bg-blue-900/30" : "bg-blue-500/10"
              )}>
                <Clock className={cn("h-4 w-4", isDarkMode ? "text-blue-400" : "text-blue-600")} />
              </div>
              <div>
                <p className={cn("text-xs", isDarkMode ? "text-slate-300" : "text-slate-600")}>Time Saved</p>
                <p className={cn(
                  "text-lg font-bold transition-all duration-300",
                  isDarkMode ? "text-blue-400" : "text-blue-600"
                )}>
                  {savings.time.toFixed(0)} hours
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ROICalculator;
