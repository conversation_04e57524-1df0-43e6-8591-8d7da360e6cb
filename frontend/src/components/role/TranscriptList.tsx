import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/Card';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { formatDistanceToNow } from 'date-fns';
import { FileText, FileCode, Trash2, Sparkles } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { rolesService } from '@/services/roles';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/Dialog";
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface TranscriptMessage {
  id: string;
  role: 'user' | 'Recruiva';
  content: string;
  timestamp: number;
  status: 'pending' | 'completed' | 'failed';
  isSpeaking?: boolean;
}

interface Transcript {
  id: string;
  type: 'intake' | 'screening' | 'interview';
  status: 'in_progress' | 'completed';
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
  messages: TranscriptMessage[];
  deleted?: boolean;
}

interface TranscriptListProps {
  roleId: string;
  types?: Array<'intake' | 'screening' | 'interview'>;
}

export const TranscriptList: React.FC<TranscriptListProps> = ({
  roleId,
  types = ['intake'] // Default to just intake transcripts for backward compatibility
}) => {
  const [transcripts, setTranscripts] = useState<Transcript[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTranscript, setSelectedTranscript] = useState<Transcript | null>(null);
  const [generatingJobPosting, setGeneratingJobPosting] = useState(false);
  const [enrichingRole, setEnrichingRole] = useState(false);
  const [deletingTranscript, setDeletingTranscript] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (!roleId) {
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      try {
        let allTranscripts: Transcript[] = [];

        // Fetch intake transcripts if requested
        if (types.includes('intake')) {
          const intakeResponse = await rolesService.getIntakeTranscripts(roleId);
          if (intakeResponse && Array.isArray(intakeResponse)) {
            allTranscripts = [...allTranscripts, ...formatTranscripts(intakeResponse, 'intake')];
          }
        }

        // Fetch interview transcripts if requested
        if (types.includes('interview')) {
          try {
            // Check if the getInterviewTranscripts method exists
            if (typeof rolesService.getInterviewTranscripts === 'function') {
              const interviewResponse = await rolesService.getInterviewTranscripts(roleId);
              if (interviewResponse && Array.isArray(interviewResponse)) {
                allTranscripts = [...allTranscripts, ...formatTranscripts(interviewResponse, 'interview')];
              }
            } else {
              console.warn('getInterviewTranscripts method not found in rolesService');
            }
          } catch (interviewError) {
            console.error('Error fetching interview transcripts:', interviewError);
          }
        }

        // Format and sort all transcripts
        const formattedTranscripts = allTranscripts
          .filter(transcript => transcript && transcript.deleted !== true)
          .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

        setTranscripts(formattedTranscripts);
      } catch (error) {
        console.error('Error fetching transcripts:', error);
        toast({
          title: 'Error',
          description: 'Failed to load transcripts. Please try refreshing the page.',
          variant: 'destructive',
        });
        setTranscripts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [roleId, types, toast]);

  // Helper function to format transcript data
  const formatTranscripts = (transcriptArray: any[], type: 'intake' | 'interview') => {
    return transcriptArray
      .filter(transcript => transcript && typeof transcript === 'object')
      .map(transcript => ({
        ...transcript,
        type: transcript.type || type, // Use type from transcript data or fallback to provided type
        createdAt: transcript.createdAt ? new Date(transcript.createdAt) : new Date(),
        updatedAt: transcript.updatedAt ? new Date(transcript.updatedAt) : new Date(),
        messageCount: transcript.messages?.length || 0,
        messages: Array.isArray(transcript.messages) ? transcript.messages : []
      }));
  };

  const handleViewTranscript = (transcript: Transcript) => {
    setSelectedTranscript(transcript);
  };

  const handleGenerateJobPosting = async (transcript: Transcript) => {
    try {
      setGeneratingJobPosting(true);

      // Validate transcript ID
      if (!transcript || !transcript.id) {
        toast({
          title: 'Error',
          description: 'Invalid transcript data',
          variant: 'destructive',
        });
        return;
      }

      console.log('Generating job posting for transcript:', transcript.id, 'in role:', roleId);

      // Call the API to generate the job posting
      const result = await rolesService.generateJobPosting(roleId, transcript.id);

      if (result && result.job_posting) {
        console.log('Job posting generated successfully');
        toast({
          title: 'Success',
          description: 'Job posting generated successfully',
          variant: 'default',
        });

        // Refresh the page to show the newly generated job posting
        window.location.reload();
      } else {
        console.error('No job posting content returned');
        toast({
          title: 'Error',
          description: 'Failed to generate job posting: No content returned',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error generating job posting:', error);

      // Extract error message
      let errorMessage = 'Failed to generate job posting. Please try again.';
      if (error instanceof Error) {
        errorMessage = error.message || errorMessage;
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setGeneratingJobPosting(false);
    }
  };

  const handleEnrichRole = async (transcript: Transcript) => {
    try {
      setEnrichingRole(true);

      // Validate transcript ID
      if (!transcript || !transcript.id) {
        toast({
          title: 'Error',
          description: 'Invalid transcript data',
          variant: 'destructive',
        });
        return;
      }

      console.log('Enriching role with transcript:', transcript.id, 'in role:', roleId);

      // Show a toast to indicate the process has started
      toast({
        title: 'Processing',
        description: 'Enriching role with transcript data. This may take a minute...',
        variant: 'default',
      });

      // Call the API to enrich the role
      try {
        const result = await rolesService.enrichRole(roleId, transcript.id);

        if (result && result.enriched_data) {
          console.log('Role enriched successfully with fields:', Object.keys(result.enriched_data).join(', '));

          // Count the number of fields that were updated
          const fieldCount = Object.keys(result.enriched_data).length;

          toast({
            title: 'Success',
            description: `Role data enriched successfully with ${fieldCount} fields from transcript`,
            variant: 'default',
          });

          // Refresh the page to show the updated role data
          window.location.reload();
        } else {
          console.error('No enriched data returned');
          toast({
            title: 'Error',
            description: 'Failed to enrich role: No data returned',
            variant: 'destructive',
          });
        }
      } catch (apiError) {
        console.error('API error enriching role:', apiError);

        // Extract error message
        let errorMessage = 'Failed to enrich role. Please try again.';
        if (apiError instanceof Error) {
          errorMessage = apiError.message || errorMessage;
        }

        // Check for specific error messages
        if (errorMessage.includes('insufficient data') ||
            errorMessage.includes('missing required fields') ||
            errorMessage.includes('Invalid response format')) {
          errorMessage = 'The transcript does not contain enough information to create a meaningful role description. Please try with a more detailed transcript.';
        }

        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error in handleEnrichRole:', error);

      // Extract error message
      let errorMessage = 'Failed to enrich role. Please try again.';
      if (error instanceof Error) {
        errorMessage = error.message || errorMessage;
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setEnrichingRole(false);
    }
  };

  const handleDeleteTranscript = async (transcriptId: string, e: React.MouseEvent) => {
    try {
      e.stopPropagation(); // Prevent card click event
      setDeletingTranscript(transcriptId);

      // Call the API to mark the transcript as deleted
      await rolesService.deleteIntakeTranscript(roleId, transcriptId);

      // Update the local state to remove the deleted transcript
      setTranscripts(transcripts.filter(t => t.id !== transcriptId));

      toast({
        title: 'Success',
        description: 'Transcript deleted successfully',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error deleting transcript:', error);

      // Extract error message
      let errorMessage = 'Failed to delete transcript. Please try again.';
      if (error instanceof Error) {
        errorMessage = error.message || errorMessage;
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setDeletingTranscript(null);
    }
  };

  // Calculate the appropriate height for the transcript list
  const getScrollAreaHeight = () => {
    if (transcripts.length === 0) return 'h-[100px]'; // Minimal height when empty
    if (transcripts.length === 1) return 'h-[120px]'; // Slightly taller for one transcript
    if (transcripts.length === 2) return 'h-[180px]'; // Taller for two transcripts
    return 'h-[240px]'; // Maximum height for 3+ transcripts
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <>
      <Card className="backdrop-blur-[6px] backdrop-saturate-[1.4] bg-white/70 border-slate-200/70 dark:bg-slate-800/60 dark:border-slate-700/40 dark:shadow-xl dark:shadow-slate-950/20 transition-all duration-300 hover:transform hover:scale-[1.02] hover:shadow-md hover:shadow-slate-200/50 dark:hover:shadow-xl dark:hover:shadow-slate-950/30">
        <CardHeader>
          <CardTitle className="text-slate-700 dark:text-slate-100">Call Transcripts</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className={`${getScrollAreaHeight()} pr-4`}>
            {transcripts.length > 0 ? (
              <div className="space-y-2">
                {transcripts.map((transcript) => (
                  <div
                    key={transcript.id}
                    className="flex items-center justify-between p-3 rounded-lg backdrop-blur-[6px] backdrop-saturate-[1.4] bg-white/70 border-slate-200/70 dark:bg-slate-800/60 dark:border-slate-700/40 dark:shadow-none hover:bg-white/80 hover:border-slate-300/70 dark:hover:bg-slate-800/70 dark:hover:border-slate-700/60 transition-all cursor-pointer shadow-sm hover:shadow-md"
                    onClick={() => handleViewTranscript(transcript)}
                  >
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-slate-500 dark:text-muted-foreground" />
                      <div>
                        <p className="font-medium capitalize text-slate-800 dark:text-slate-100">
                          {transcript.type} Call
                          {transcript.status === 'in_progress' && (
                            <span className="ml-2 text-xs bg-primary/20 text-primary px-2 py-1 rounded-full">
                              In Progress
                            </span>
                          )}
                        </p>
                        <p className="text-sm text-slate-600 dark:text-muted-foreground">
                          {formatDistanceToNow(transcript.createdAt, { addSuffix: true })}
                          {' • '}
                          {transcript.messageCount} messages
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center justify-center text-purple-400 hover:text-purple-300 hover:bg-purple-500/10"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent card click event
                          handleEnrichRole(transcript);
                        }}
                        disabled={enrichingRole}
                        title="Enrich Role with AI"
                      >
                        {enrichingRole ? (
                          <LoadingSpinner className="h-5 w-5" />
                        ) : (
                          <Sparkles className="h-5 w-5" />
                        )}
                      </Button>
                      <Button
                        variant="secondary"
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent card click event
                          handleGenerateJobPosting(transcript);
                        }}
                        disabled={generatingJobPosting}
                      >
                        {generatingJobPosting ? (
                          <div className="animate-spin h-4 w-4 border-b-2 border-primary rounded-full mr-1"></div>
                        ) : (
                          <FileCode className="h-4 w-4 mr-1" />
                        )}
                        Generate Job Posting
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center justify-center text-red-400 hover:text-red-300 hover:bg-red-500/10"
                        onClick={(e) => handleDeleteTranscript(transcript.id, e)}
                        disabled={deletingTranscript === transcript.id}
                        title="Delete transcript"
                      >
                        {deletingTranscript === transcript.id ? (
                          <LoadingSpinner className="h-5 w-5 text-red-400" />
                        ) : (
                          <Trash2 className="h-5 w-5" />
                        )}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-slate-600 dark:text-slate-400 py-4">
                <FileText className="h-10 w-10 mx-auto mb-2 opacity-50" />
                <p>No transcripts available</p>
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Transcript Dialog */}
      <Dialog open={!!selectedTranscript} onOpenChange={(open) => {
        if (!open) setSelectedTranscript(null);
      }}>
        <DialogContent className="max-w-2xl h-[80vh] p-0 overflow-hidden flex flex-col backdrop-blur-[6px] backdrop-saturate-[1.4] bg-white/70 border-slate-200/70 dark:bg-slate-800/60 dark:border-slate-700/40 dark:shadow-xl dark:shadow-slate-950/20">
          <DialogHeader className="p-6 pb-4">
            <DialogTitle className="text-slate-700 dark:text-slate-100">
              {selectedTranscript?.type} Call Transcript
            </DialogTitle>
            <DialogDescription className="text-slate-600 dark:text-slate-400">
              Detailed conversation transcript between the user and Recruiva AI.
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="flex-1 px-6 pb-6">
            <div className="space-y-4 pr-4">
              {selectedTranscript?.messages.map((message, index) => (
                <div
                  key={`transcript-message-${message.id || `index-${index}-${Date.now()}`}`}
                  className={`flex flex-col ${
                    message.role === 'user' ? 'items-end' : 'items-start'
                  }`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-3 ${
                      message.role === 'user'
                        ? 'bg-blue-100 text-blue-900 dark:bg-primary/20 dark:text-slate-100'
                        : 'bg-slate-200 text-slate-900 dark:bg-slate-700/60 dark:text-slate-100'
                    }`}
                  >
                    <p className="text-xs text-slate-600 dark:text-muted-foreground mb-1">
                      {message.role === 'user' ? 'You' : 'Recruiva'}
                    </p>
                    <p className="whitespace-pre-wrap">{message.content}</p>
                    <p className="text-xs text-slate-600 dark:text-muted-foreground mt-1">
                      {formatDistanceToNow(new Date(message.timestamp), {
                        addSuffix: true,
                      })}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  );
};