"use client"

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils/styles'
import {
  LayoutDashboard,
  Briefcase,
  Settings,
  HelpCircle,
  ChevronLeft,
  ChevronRight,
  UserCheck,
  Video,
  X} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useSidebar } from '@/contexts/sidebar-context'
import { useAuth } from '@/contexts/auth-context'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/DropdownMenu'
import { useEffect, useState } from 'react'
import { doc, onSnapshot } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { ThemeToggle } from '@/components/ui/ThemeToggle'

interface UserProfile {
  fullName: string;
  photoURL: string;
}

const sidebarLinks = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Applications',
    href: '/applications',
    icon: UserCheck,
  },
  {
    title: 'Roles',
    href: '/roles',
    icon: Briefcase,
  },
  {
    title: 'Interviews',
    href: '/interviews',
    icon: Video,
  },
  {
    title: 'Settings',
    href: '/settings',
    icon: Settings,
  },
  {
    title: 'Help',
    href: '/help',
    icon: HelpCircle,
  },
]

export function Sidebar() {
  const pathname = usePathname()
  const { isCollapsed, setCollapsed, isMobileOpen, setMobileOpen } = useSidebar()
  const { user, logout } = useAuth()
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)

  useEffect(() => {
    if (!user) {
      setUserProfile(null);
      return;
    }

    let unsubscribe: (() => void) | undefined;

    const setupSubscription = async () => {
      try {
        // First check if we can access the document
        const docRef = doc(db, 'users', user.uid);

        unsubscribe = onSnapshot(
          docRef,
          (doc) => {
            if (doc.exists()) {
              setUserProfile(doc.data() as UserProfile);
            } else {
              setUserProfile(null);
            }
          },
          (error) => {
            console.error('Firestore subscription error:', error);
            setUserProfile(null);
          }
        );
      } catch (error) {
        console.error('Error setting up Firestore subscription:', error);
        setUserProfile(null);
      }
    };

    setupSubscription();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [user]);

  const getInitials = () => {
    if (userProfile?.fullName) {
      return userProfile.fullName
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase();
    }
    return user?.email?.charAt(0).toUpperCase() || '?';
  };

  const handleLogout = async () => {
    try {
      await logout();
      window.location.href = '/auth/signin';
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const handleSidebarClick = (e: React.MouseEvent) => {
    // Prevent sidebar clicks from closing the sidebar
    e.stopPropagation();
  };

  return (
    <div className={cn(
      "flex h-screen flex-col fixed top-0 left-0 z-40",
      "border-r backdrop-blur-[6px] bg-white/60 border-slate-200/70",
      "dark:bg-slate-800/60 dark:border-slate-700/40 dark:backdrop-saturate-[1.4]",
      "dark:shadow-xl dark:shadow-slate-950/20",
      "transform-gpu will-change-transform",
      "transition-all duration-150 ease-in-out",
      // Desktop behavior
      "md:transition-[width]",
      isCollapsed ? "md:w-[60px]" : "md:w-[200px]",
      // Mobile behavior
      "w-[240px] max-w-[80vw]",
      isMobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0",
    )}
    onClick={handleSidebarClick}
    >
      <div className="flex h-14 items-center justify-between px-4">
        <div className="flex items-center min-w-0 overflow-hidden">
          {!isCollapsed && (
            <Link href="/" className="hover:text-purple-500 transition-colors duration-150">
              <span className={cn(
                "logo-text whitespace-nowrap transform-gpu cursor-pointer",
                "transition-[opacity,transform] duration-150 ease-in-out",
                isCollapsed ? "opacity-0 -translate-x-2" : "opacity-100 translate-x-0"
              )}>Recruiva</span>
            </Link>
          )}
          {isCollapsed && (
            <span className={cn(
              "logo-text whitespace-nowrap transform-gpu",
              "transition-[opacity,transform] duration-150 ease-in-out",
              "opacity-0 -translate-x-2"
            )}>Recruiva</span>
          )}
        </div>
        <div className="flex">
          {/* Mobile close button */}
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className={cn(
              "h-6 w-6 p-0 flex-shrink-0 md:hidden",
              "text-slate-500 hover:text-slate-800 hover:bg-slate-200/80",
              "dark:text-slate-400 dark:hover:text-white dark:hover:bg-slate-800/70"
            )}
            onClick={(e) => {
              e.stopPropagation();
              setMobileOpen(false);
            }}
          >
            <X className="h-4 w-4" />
          </Button>

          {/* Collapse/expand button - only visible on desktop */}
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className={cn(
              "h-6 w-6 p-0 flex-shrink-0 hidden md:flex",
              "text-slate-500 hover:text-slate-800 hover:bg-slate-200/80",
              "dark:text-slate-400 dark:hover:text-white dark:hover:bg-slate-800/70"
            )}
            onClick={() => setCollapsed(!isCollapsed)}
          >
            {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        </div>
      </div>
      <nav className="flex-1 space-y-1 p-2 overflow-y-auto">
        {sidebarLinks.map((link) => {
          const Icon = link.icon
          const isActive = pathname === link.href

          return (
            <Link
              key={link.href}
              href={link.href}
              className={cn(
                'flex items-center space-x-2 rounded-lg px-3 py-2 text-sm font-medium',
                'transition-[colors,transform] duration-150 ease-in-out',
                isActive
                  ? 'active-nav-item'
                  : cn(
                      'text-slate-700 hover:bg-slate-200/80 hover:text-slate-900',
                      'dark:text-slate-300 dark:hover:bg-slate-800/70 dark:hover:text-white'
                    ),
                isCollapsed && !isMobileOpen && "md:justify-center md:px-2"
              )}
              title={isCollapsed && !isMobileOpen ? link.title : undefined}
            >
              <Icon className={cn(
                'h-4 w-4 flex-shrink-0',
                isActive
                  ? 'text-purple-500'
                  : cn(
                      'text-slate-500',
                      'dark:text-slate-400'
                    )
              )} />
              <span className={cn(
                "whitespace-nowrap transform-gpu",
                "transition-[opacity,width,transform] duration-150 ease-in-out",
                isCollapsed && !isMobileOpen ? "md:opacity-0 md:w-0 md:-translate-x-2" : "opacity-100 w-auto translate-x-0"
              )}>{link.title}</span>
            </Link>
          )
        })}
      </nav>
      <div className={cn(
        "px-4 py-3 border-t border-slate-200/70 dark:border-slate-700/40",
        "flex items-center justify-center"
      )}>
        <ThemeToggle />
      </div>
      <div className="p-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className={cn(
              "flex items-center space-x-2 w-full rounded-lg px-2 py-1.5 text-sm overflow-hidden",
              "text-slate-700 hover:bg-slate-200/80 hover:text-slate-900",
              "dark:text-slate-300 dark:hover:bg-slate-800/70 dark:hover:text-white",
              isCollapsed && !isMobileOpen && "md:justify-center"
            )}>
              <Avatar className="h-8 w-8 flex-shrink-0">
                <AvatarImage src={userProfile?.photoURL || ''} alt={userProfile?.fullName || ''} />
                <AvatarFallback>{getInitials()}</AvatarFallback>
              </Avatar>
              <div className={cn(
                "flex flex-col items-start transform-gpu",
                "transition-[opacity,width,transform] duration-150 ease-in-out",
                isCollapsed && !isMobileOpen ? "md:opacity-0 md:w-0 md:-translate-x-2" : "opacity-100 w-auto translate-x-0"
              )}>
                <span className="text-sm font-medium truncate">{userProfile?.fullName || user?.email}</span>
              </div>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className={cn(
            "z-50",
            "bg-white border-slate-200/70",
            "dark:bg-slate-800/60 dark:border-slate-700/40 dark:backdrop-blur-[6px] dark:backdrop-saturate-[1.4]",
            "dark:shadow-xl dark:shadow-slate-950/20"
          )}>
            <DropdownMenuLabel className={cn(
              "text-slate-700",
              "dark:text-slate-300"
            )}>
              My Account
            </DropdownMenuLabel>
            <DropdownMenuItem className="hover:bg-slate-100 dark:hover:bg-slate-800/70 cursor-pointer">
              <Link href="/profile" className="w-full">Profile</Link>
            </DropdownMenuItem>
            <DropdownMenuItem className="hover:bg-slate-100 dark:hover:bg-slate-800/70 cursor-pointer">
              <Link href="/settings" className="w-full">Settings</Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-slate-200/70 dark:bg-slate-700/40" />
            <DropdownMenuItem onClick={handleLogout} className="hover:bg-slate-100 dark:hover:bg-slate-800/70 cursor-pointer text-red-500 dark:text-red-400">
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
