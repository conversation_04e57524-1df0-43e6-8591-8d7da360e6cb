'use client';

import { Sidebar } from './Sidebar'
import { Toaster } from '@/components/ui/Toaster'
import { useSidebar } from '@/contexts/sidebar-context'
import { cn } from '@/lib/utils/styles'
import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'
import { Menu } from 'lucide-react'
import { Button } from '@/components/ui/Button'

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const { isCollapsed, isMobileOpen, setMobileOpen } = useSidebar();
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  
  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Use resolvedTheme which accounts for system preferences when theme is "system"
  const isDarkMode = mounted && (resolvedTheme === 'dark' || theme === 'dark');
  
  return (
    <div 
      className={cn(
        "relative min-h-screen",
        // Apply lighter gradient only for light mode, and use a solid color that will be overridden in dark mode
        !isDarkMode && "bg-gradient-to-br from-orange-50/80 via-pink-50/70 to-purple-50/80", // Lighter gradient for light mode
        // Use !important to ensure dark mode background overrides the gradient
        "dark:!bg-background"
      )}
      style={{
        // Fallback solid background for dark mode to ensure gradient doesn't show through
        backgroundColor: isDarkMode ? 'hsl(220, 13%, 14%)' : undefined
      }}
    >
      {/* Decorative elements for visual interest - only for light mode */}
      {!isDarkMode && mounted && (
        <div className="fixed inset-0 z-0 overflow-hidden pointer-events-none">
          {/* Light mode decorative elements - made slightly more transparent */}
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-200/15 rounded-full blur-3xl opacity-50 animate-pulse"></div>
          <div className="absolute bottom-1/3 right-1/4 w-96 h-96 bg-purple-200/20 rounded-full blur-3xl opacity-40"></div>
        </div>
      )}
      
      {/* Mobile menu toggle button - only visible on small screens */}
      <div className="md:hidden fixed top-3 left-3 z-50">
        <Button
          variant="ghost"
          size="sm"
          className="w-8 h-8 p-0 rounded-full bg-background/80 backdrop-blur-sm border border-border"
          onClick={() => setMobileOpen(!isMobileOpen)}
        >
          <Menu className="h-4 w-4" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </div>
      
      <Sidebar />
      <div className={cn(
        "min-h-screen transition-all duration-200 ease-out z-10 relative", // Added z-index for proper stacking
        // Desktop sidebar spacing
        "md:transition-all md:duration-200 md:ease-out",
        isCollapsed ? "md:ml-[60px]" : "md:ml-[200px]",
        // No left margin on mobile to use full width
        "ml-0"
      )}>
        {/* Center the content with flex but respect sidebar space */}
        <div className="w-full flex justify-center">
          {/* Remove negative margins and use positive padding instead */}
          <div className={cn(
            "w-full max-w-[88rem] transition-all duration-200 ease-out",
          )}>
            <main className="w-full px-4 sm:px-6 lg:px-10 py-6 sm:py-8 space-y-4">
              {children}
            </main>
          </div>
        </div>
      </div>
      <Toaster />
    </div>
  )
} 