import React, { useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';

interface CollapsibleSectionProps {
  title: React.ReactNode;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  headerRight?: React.ReactNode;
  className?: string;
  contentClassName?: string;
}

/**
 * CollapsibleSection component
 * A reusable component that wraps content in a collapsible card with expand/collapse functionality
 */
export function CollapsibleSection({
  title,
  children,
  defaultExpanded = true,
  headerRight,
  className,
  contentClassName
}: CollapsibleSectionProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <Card className={cn("transition-all duration-200", className)}>
      <CardHeader 
        className="flex flex-row items-center justify-between cursor-pointer"
        onClick={toggleExpand}
      >
        <div className="flex items-center gap-2">
          {isExpanded ? (
            <ChevronDown className="h-5 w-5 text-slate-500 dark:text-slate-400 transition-transform" />
          ) : (
            <ChevronRight className="h-5 w-5 text-slate-500 dark:text-slate-400 transition-transform" />
          )}
          {title}
        </div>
        {headerRight && (
          <div onClick={(e) => e.stopPropagation()}>
            {headerRight}
          </div>
        )}
      </CardHeader>
      {isExpanded && (
        <CardContent className={cn("transition-all duration-300", contentClassName)}>
          {children}
        </CardContent>
      )}
    </Card>
  );
}
