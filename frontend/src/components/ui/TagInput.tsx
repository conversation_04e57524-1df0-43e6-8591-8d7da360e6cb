'use client';

import React, { useState, useRef, KeyboardEvent } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TagInputProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  maxTags?: number;
  label?: string;
  error?: string;
  placeholder?: string;
  className?: string;
}

export const TagInput = React.forwardRef<HTMLDivElement, TagInputProps>(
  ({ value = [], onChange, maxTags, label, error, placeholder = 'Type and press Enter', className }, ref) => {
    const [inputValue, setInputValue] = useState('');
    const inputRef = useRef<HTMLInputElement>(null);

    const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter' && inputValue.trim()) {
        e.preventDefault();
        if (maxTags && value.length >= maxTags) {
          return;
        }
        const newValue = [...value, inputValue.trim()];
        onChange?.(newValue);
        setInputValue('');
      } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
        const newValue = value.slice(0, -1);
        onChange?.(newValue);
      }
    };

    const removeTag = (index: number) => {
      const newValue = value.filter((_, i) => i !== index);
      onChange?.(newValue);
    };

    return (
      <div ref={ref} className={cn('space-y-2', className)}>
        {label && (
          <label className="block text-sm font-medium text-gray-300">
            {label}
          </label>
        )}
        <div
          className={cn(
            'flex flex-wrap gap-2 rounded-md border border-input bg-background px-3 py-2 ring-offset-background focus-within:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 transition-colors hover:bg-gradient-to-b hover:from-background hover:to-muted/50',
            error ? 'border-red-500' : 'border-input',
            className
          )}
          onClick={() => inputRef.current?.focus()}
        >
          {value.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center gap-1 rounded-md bg-muted/70 px-2 py-1 text-sm text-muted-foreground"
            >
              {tag}
              <button
                type="button"
                onClick={() => removeTag(index)}
                className="text-muted-foreground/70 hover:text-muted-foreground"
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          ))}
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={value.length === 0 ? placeholder : ''}
            className="flex-1 bg-transparent text-sm text-foreground placeholder:text-muted-foreground focus:outline-none"
          />
        </div>
        {error && <p className="text-sm text-red-500">{error}</p>}
      </div>
    );
  }
);

TagInput.displayName = 'TagInput'; 