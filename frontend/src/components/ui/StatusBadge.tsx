import { cn } from '@/lib/utils/styles'
import { RoleStatus, roleStageColors, statusToStage } from '@/types/role'

type InterviewStatus = 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
type Status = RoleStatus | InterviewStatus;

interface StatusBadgeProps {
    status: Status
    className?: string
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
    const getStatusColor = (status: Status) => {
        // Interview statuses
        if (['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'].includes(status)) {
            switch (status) {
                case 'SCHEDULED':
                    return 'bg-blue-500/20 text-blue-600 ring-1 ring-blue-600/30 dark:bg-blue-500/10 dark:text-blue-500 dark:ring-blue-500/20'
                case 'IN_PROGRESS':
                    return 'bg-amber-500/20 text-amber-600 ring-1 ring-amber-600/30 dark:bg-amber-500/10 dark:text-amber-500 dark:ring-amber-500/20'
                case 'COMPLETED':
                    return 'bg-emerald-500/20 text-emerald-600 ring-1 ring-emerald-600/30 dark:bg-emerald-500/10 dark:text-emerald-500 dark:ring-emerald-500/20'
                case 'CANCELLED':
                    return 'bg-red-500/20 text-red-600 ring-1 ring-red-600/30 dark:bg-red-500/10 dark:text-red-500 dark:ring-red-500/20'
            }
        }

        // Role statuses - use the same colors as the timeline
        const stage = statusToStage[status as RoleStatus];
        if (stage) {
            const colors = roleStageColors[stage];
            // Create a class that matches the role details page style
            // Light mode: color/20 background with darker color text
            const colorName = colors.bg.replace('bg-', '');
            const colorBase = colorName.split('-')[0]; // Get the base color name (e.g., 'blue' from 'blue-500')
            const lightBgClass = `bg-${colorName}/20`;
            const lightTextClass = `text-${colorBase}-600`; // Darker text for light mode
            const lightRingClass = `ring-1 ring-${colorBase}-600/30`;

            // Dark mode: same as before
            const darkBgClass = `dark:bg-${colorName}/10`;
            const darkTextClass = `dark:${colors.text}`;
            const darkRingClass = `dark:ring-1 dark:ring-${colorName}/20`;

            return cn(lightBgClass, lightTextClass, lightRingClass, darkBgClass, darkTextClass, darkRingClass);
        }

        // Fallback for unknown status
        return 'bg-slate-500/20 text-slate-600 ring-1 ring-slate-600/30 dark:bg-slate-500/10 dark:text-slate-500 dark:ring-slate-500/20';
    }

    return (
        <span
            className={cn(
                'inline-flex items-center rounded-md px-2.5 py-1 text-xs font-semibold',
                getStatusColor(status),
                className
            )}
        >
            {status.replace('_', ' ')}
        </span>
    )
}