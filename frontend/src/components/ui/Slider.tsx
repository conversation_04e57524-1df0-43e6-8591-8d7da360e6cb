"use client";

import * as React from "react";
import * as SliderPrimitive from "@radix-ui/react-slider";
import { cn } from "@/lib/utils/styles";

/**
 * Slider component based on Radix UI
 * Used for selecting a value from a range with support for steps
 */
const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root> & {
    showValue?: boolean;
    valuePrefix?: string;
    valueSuffix?: string;
  }
>(({ className, showValue = false, valuePrefix = "", valueSuffix = "", ...props }, ref) => {
  // Get the current value to display
  const value = props.value || props.defaultValue || [1];

  return (
    <div className="relative">
      <SliderPrimitive.Root
        ref={ref}
        className={cn(
          "relative flex w-full touch-none select-none items-center",
          className
        )}
        {...props}
      >
        <SliderPrimitive.Track
          className="relative h-2 w-full grow overflow-hidden rounded-full bg-slate-200 dark:bg-slate-700"
        >
          <SliderPrimitive.Range className="absolute h-full bg-purple-500 dark:bg-purple-400" />
        </SliderPrimitive.Track>
        {value.map((_, index) => (
          <SliderPrimitive.Thumb
            key={index}
            className="block h-5 w-5 rounded-full border border-purple-500/50 dark:border-purple-400/50 bg-white dark:bg-slate-800 shadow-md ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:scale-110 hover:shadow-lg"
          />
        ))}
      </SliderPrimitive.Root>

      {/* Display the current value if showValue is true */}
      {showValue && (
        <div className="absolute -top-6 left-0 right-0 text-center">
          <span className="text-xs font-medium text-slate-300">
            {valuePrefix}{value[0]}{valueSuffix}
          </span>
        </div>
      )}
    </div>
  );
});

Slider.displayName = SliderPrimitive.Root.displayName;

export { Slider };