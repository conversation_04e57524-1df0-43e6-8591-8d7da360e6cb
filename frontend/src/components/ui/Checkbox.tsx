'use client';

import * as React from "react";
import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

/**
 * Checkbox component based on Radix UI
 * Used for toggling boolean states with a checkbox interface
 */
const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> & {
    label?: string;
    description?: string;
  }
>(({ className, label, description, ...props }, ref) => {
  // Generate a unique ID for accessibility
  const id = React.useId();

  return (
    <div className="flex items-start space-x-2">
      <CheckboxPrimitive.Root
        id={id}
        ref={ref}
        className={cn(
          "peer h-5 w-5 shrink-0 rounded border border-slate-300 focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          "data-[state=checked]:bg-indigo-500 data-[state=checked]:border-indigo-500",
          "dark:border-slate-600 dark:focus:ring-slate-400 dark:focus:ring-offset-slate-900",
          "dark:data-[state=checked]:bg-indigo-500 dark:data-[state=checked]:border-indigo-500",
          className
        )}
        {...props}
      >
        <CheckboxPrimitive.Indicator className={cn("flex items-center justify-center")}>
          <Check className="h-4 w-4 text-white" />
        </CheckboxPrimitive.Indicator>
      </CheckboxPrimitive.Root>

      {(label || description) && (
        <div className="grid gap-1.5 leading-none">
          {label && (
            <label
              htmlFor={id}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {label}
            </label>
          )}
          {description && (
            <p className="text-sm text-slate-500 dark:text-slate-400">
              {description}
            </p>
          )}
        </div>
      )}
    </div>
  );
});

Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };
