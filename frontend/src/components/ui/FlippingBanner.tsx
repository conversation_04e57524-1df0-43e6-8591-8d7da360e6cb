'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils/styles';
import { motion, AnimatePresence } from 'framer-motion';

interface FeatureSlide {
  title: string;
  description: string;
  icon?: React.ReactNode;
}

interface FlippingBannerProps {
  features: FeatureSlide[];
  interval?: number; // Time in ms between slides
  className?: string;
}

export const FlippingBanner: React.FC<FlippingBannerProps> = ({
  features,
  interval = 5000, // Default to 5 seconds per slide
  className,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [mounted, setMounted] = useState(false);
  const { theme, resolvedTheme } = useTheme();
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Auto-advance slides
  useEffect(() => {
    if (features.length <= 1) return;

    const advanceSlide = () => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % features.length);
    };

    timerRef.current = setInterval(advanceSlide, interval);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [features.length, interval]);

  // Handle manual navigation
  const goToSlide = (index: number) => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    setCurrentIndex(index);
    timerRef.current = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % features.length);
    }, interval);
  };

  if (!mounted) return null;

  const isDarkMode = mounted && (resolvedTheme === 'dark' || theme === 'dark');

  return (
    <div className={cn("relative overflow-hidden w-full", className)}>
      <div className="relative w-full h-full">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
            className="w-full"
          >
            <div className="text-center py-6 sm:py-10">
              {features[currentIndex].icon && (
                <div className="flex justify-center mb-6">
                  {features[currentIndex].icon}
                </div>
              )}
              <h2 className={cn(
                "text-3xl sm:text-4xl md:text-5xl font-bold mb-4",
                "text-transparent bg-clip-text bg-gradient-to-r",
                isDarkMode
                  ? "from-indigo-400 via-purple-400 to-pink-400"
                  : "from-indigo-600 via-purple-600 to-pink-600"
              )}>
                {features[currentIndex].title}
              </h2>
              <p className={cn(
                "text-lg sm:text-xl max-w-3xl mx-auto",
                isDarkMode ? "text-slate-300" : "text-slate-700"
              )}>
                {features[currentIndex].description}
              </p>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Navigation dots */}
      {features.length > 1 && (
        <div className="flex justify-center gap-2 mt-4">
          {features.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={cn(
                "w-2.5 h-2.5 rounded-full transition-all duration-300",
                index === currentIndex
                  ? isDarkMode
                    ? "bg-purple-400 w-6"
                    : "bg-purple-600 w-6"
                  : isDarkMode
                    ? "bg-slate-600 hover:bg-slate-500"
                    : "bg-slate-300 hover:bg-slate-400"
              )}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};
