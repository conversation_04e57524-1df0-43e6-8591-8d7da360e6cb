"use client";

import * as React from "react";
import { useTheme } from "next-themes";
import { useState, useEffect } from "react";
import { Moon, Sun } from "lucide-react";
import { cn } from "@/lib/utils/styles";

/**
 * Theme toggle component that switches between light and dark mode
 * Shows a single icon representing the opposite theme
 * Uses next-themes for theme management
 */
export function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch by rendering after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle the theme toggle action
  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  if (!mounted) {
    return null;
  }

  const isDark = theme === "dark";

  return (
    <button
      onClick={toggleTheme}
      className={cn(
        "p-2 rounded-md transition-all duration-300",
        "text-slate-700 hover:bg-slate-200 hover:text-slate-900",
        "dark:text-slate-300 dark:hover:bg-slate-800 dark:hover:text-slate-100"
      )}
      aria-label={isDark ? "Switch to light theme" : "Switch to dark theme"}
    >
      {isDark ? (
        <Sun size={18} className="transition-transform duration-200 ease-in-out" />
      ) : (
        <Moon size={18} className="transition-transform duration-200 ease-in-out" />
      )}
    </button>
  );
} 