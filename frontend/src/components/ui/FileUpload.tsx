'use client';

import { ChangeEvent, useRef } from 'react';
import { Button } from './Button';
import { cn } from '@/lib/utils';

interface FileUploadProps {
    label?: string;
    accept?: string;
    error?: string;
    onChange: (file: File | null) => void;
    className?: string;
}

export function FileUpload({
    label,
    accept,
    error,
    onChange,
    className,
}: FileUploadProps) {
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleClick = () => {
        fileInputRef.current?.click();
    };

    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        onChange(file);
    };

    return (
        <div className={cn('space-y-2', className)}>
            {label && <label className="text-sm font-medium">{label}</label>}
            <div className="flex items-center gap-4">
                <input
                    type="file"
                    ref={fileInputRef}
                    accept={accept}
                    onChange={handleChange}
                    className="hidden"
                />
                <Button
                    type="button"
                    variant="secondary"
                    onClick={handleClick}
                >
                    Choose File
                </Button>
                <span className="text-sm text-muted-foreground">
                    {fileInputRef.current?.files?.[0]?.name || 'No file chosen'}
                </span>
            </div>
            {error && <p className="text-sm text-red-500">{error}</p>}
        </div>
    );
} 