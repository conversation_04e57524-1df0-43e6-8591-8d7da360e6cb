import * as React from 'react'
import ReactMarkdown from 'react-markdown'
import { cn } from '@/lib/utils'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

interface MarkdownProps extends React.HTMLAttributes<HTMLDivElement> {
  content: string
  isDarkOptimized?: boolean
}

/**
 * Markdown component for rendering markdown content
 * Supports both light and dark themes
 * Can be forced to use dark-optimized styling with isDarkOptimized prop
 */
export function Markdown({ content, className, isDarkOptimized, ...props }: MarkdownProps) {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Determine if we're in dark mode - use resolved theme to account for system preference
  // If isDarkOptimized is explicitly set, use that value, otherwise use the theme
  const isDark = mounted &&
    (isDarkOptimized !== undefined ? isDarkOptimized : resolvedTheme === 'dark');

  return (
    <div
      className={cn(
        'prose max-w-none',
        // Use custom styling class for dark mode, standard tailwind prose-invert for light mode
        isDark
          ? 'dark-optimized-prose text-slate-300' // Custom class for browser-agnostic dark mode styling
          : 'light-optimized-prose text-slate-700', // Custom class for light mode styling
        className
      )}
      {...props}
    >
      <ReactMarkdown>{content}</ReactMarkdown>
    </div>
  )
}