'use client';

import { useState, useCallback, useEffect } from 'react';
import { auth } from '@/lib/firebase';

type ToastVariant = 'default' | 'destructive';

interface ToastProps {
  title: string;
  description: string;
  variant?: ToastVariant;
}

interface Toast extends ToastProps {
  id: string;
}

export function useToast() {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setIsAuthenticated(!!user);
    });

    return () => unsubscribe();
  }, []);

  const toast = useCallback(({ title, description, variant = 'default' }: ToastProps) => {
    // Only show toasts if authenticated
    if (!isAuthenticated) return;

    const id = Math.random().toString(36).substring(2);
    const newToast: Toast = {
      id,
      title,
      description,
      variant,
    };

    setToasts((currentToasts) => [...currentToasts, newToast]);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
      setToasts((currentToasts) => currentToasts.filter((t) => t.id !== id));
    }, 5000);
  }, [isAuthenticated]);

  const dismiss = useCallback((id: string) => {
    setToasts((currentToasts) => currentToasts.filter((t) => t.id !== id));
  }, []);

  return {
    toast,
    toasts,
    dismiss,
  };
} 