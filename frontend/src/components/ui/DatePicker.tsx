'use client';

import React from 'react';
import ReactDatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { cn } from '@/lib/utils';

interface DatePickerProps {
  value?: string;
  onChange?: (value: string) => void;
  label?: string;
  error?: string;
  placeholder?: string;
  className?: string;
  minDate?: Date;
  maxDate?: Date;
}

export const DatePicker = React.forwardRef<HTMLDivElement, DatePickerProps>(
  ({ value, onChange, label, error, placeholder = 'Select date', className, minDate, maxDate }, ref) => {
    const selectedDate = value ? new Date(value) : null;

    const handleChange = (date: Date | null) => {
      onChange?.(date ? date.toISOString() : '');
    };

    return (
      <div ref={ref} className={cn('space-y-2', className)}>
        {label && (
          <label className="block text-sm font-medium text-gray-300">
            {label}
          </label>
        )}
        <ReactDatePicker
          selected={selectedDate}
          onChange={handleChange}
          dateFormat="yyyy-MM-dd"
          minDate={minDate}
          maxDate={maxDate}
          placeholderText={placeholder}
          className={cn(
            'w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-foreground ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors hover:bg-gradient-to-b hover:from-background hover:to-muted/50',
            error ? 'border-red-500' : 'border-input'
          )}
        />
        {error && <p className="text-sm text-red-500">{error}</p>}
      </div>
    );
  }
);

DatePicker.displayName = 'DatePicker'; 