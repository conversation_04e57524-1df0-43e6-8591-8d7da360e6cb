import React from 'react';
import { cn } from '@/lib/utils';

interface PageHeaderProps {
  title: string;
  description?: string;
  className?: string;
  children?: React.ReactNode;
}

/**
 * PageHeader component for consistent page headers across the application
 */
export function PageHeader({
  title,
  description,
  className,
  children,
}: PageHeaderProps) {
  return (
    <div className={cn("flex flex-col space-y-2 mb-6", className)}>
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight gradient-text">{title}</h1>
        {children}
      </div>
      {description && (
        <p className="text-slate-400">{description}</p>
      )}
    </div>
  );
}
