"use client";

import * as React from "react";
import * as SwitchPrimitives from "@radix-ui/react-switch";
import { cn } from "@/lib/utils/styles";

/**
 * Switch component based on Radix UI
 * Used for toggling between two states
 * Supports both light and dark themes
 */
const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      "peer inline-flex h-5 w-10 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",
      "focus-visible:ring-slate-400 focus-visible:ring-offset-white",
      "dark:focus-visible:ring-slate-400 dark:focus-visible:ring-offset-slate-950",
      "disabled:cursor-not-allowed disabled:opacity-50",
      "data-[state=checked]:bg-indigo-400/40 data-[state=unchecked]:bg-slate-300",
      "dark:data-[state=checked]:bg-indigo-500/40 dark:data-[state=unchecked]:bg-slate-700/60",
      "dark:backdrop-blur-[4px] dark:backdrop-saturate-[1.3]",
      className
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        "pointer-events-none block h-4 w-4 rounded-full shadow-lg ring-0 transition-transform",
        "data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0",
        "data-[state=unchecked]:bg-slate-600 data-[state=checked]:bg-indigo-500",
        "dark:data-[state=unchecked]:bg-slate-300 dark:data-[state=checked]:bg-indigo-400"
      )}
    />
  </SwitchPrimitives.Root>
));

Switch.displayName = SwitchPrimitives.Root.displayName;

export { Switch }; 