'use client';

import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/Button';
import { 
  Mic, MicOff, 
  Video, VideoOff,
  MessageCircle,
  LogOut,
  Settings
} from 'lucide-react';

interface ControlPanelProps {
  isMuted: boolean;
  isCameraOff: boolean;
  isTranscriptOpen: boolean;
  onToggleMute: () => void;
  onToggleCamera: () => void;
  onToggleTranscript: () => void;
  onSettings: () => void;
  onEnd: () => void;
}

export const ControlPanel: React.FC<ControlPanelProps> = ({
  isMuted,
  isCameraOff,
  isTranscriptOpen,
  onToggleMute,
  onToggleCamera,
  onToggleTranscript,
  onSettings,
  onEnd,
}) => {
  // Audio visualization
  const [volume, setVolume] = useState<number>(0);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const sourceNodeRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const rafRef = useRef<number | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const cleanupAudio = () => {
    // Stop all tracks in the stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => {
        track.stop();
      });
      streamRef.current = null;
    }

    // Disconnect and cleanup audio processing nodes
    if (sourceNodeRef.current) {
      sourceNodeRef.current.disconnect();
      sourceNodeRef.current = null;
    }

    if (rafRef.current) {
      cancelAnimationFrame(rafRef.current);
      rafRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    analyserRef.current = null;
    setVolume(0);
  };

  useEffect(() => {
    const initAudio = async () => {
      try {
        if (!isMuted) {
          // Clean up any existing audio processing
          cleanupAudio();

          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          streamRef.current = stream;
          
          audioContextRef.current = new AudioContext();
          analyserRef.current = audioContextRef.current.createAnalyser();
          analyserRef.current.fftSize = 32;
          analyserRef.current.smoothingTimeConstant = 0.4;

          sourceNodeRef.current = audioContextRef.current.createMediaStreamSource(stream);
          sourceNodeRef.current.connect(analyserRef.current);

          const analyzeAudio = () => {
            if (!analyserRef.current) return;
            
            const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
            analyserRef.current.getByteFrequencyData(dataArray);
            
            const average = dataArray.reduce((acc, val) => acc + val, 0) / dataArray.length;
            const normalizedLevel = Math.pow(average / 255, 0.6);
            const enhancedLevel = Math.max(normalizedLevel, 0.05);
            
            setVolume(enhancedLevel);
            rafRef.current = requestAnimationFrame(analyzeAudio);
          };

          analyzeAudio();
        } else {
          cleanupAudio();
        }
      } catch (error) {
        console.error('Error accessing microphone:', error);
      }
    };

    initAudio();

    // Cleanup when component unmounts
    return () => {
      cleanupAudio();
    };
  }, [isMuted]);

  const micButtonStyle = {
    borderColor: isMuted 
      ? 'rgb(239 68 68)' // red-500
      : volume === 0 
        ? 'rgb(51 65 85)' // slate-700
        : `rgb(${Math.round(volume * 30)} ${Math.round(volume * 255)} ${Math.round(volume * 30)})`,
    borderWidth: '1px',
    transition: 'border-color 0.05s ease-out',
    boxShadow: volume > 0 && !isMuted 
      ? `0 0 ${Math.round(volume * 15)}px rgba(${Math.round(volume * 30)}, ${Math.round(volume * 255)}, ${Math.round(volume * 30)}, 0.4)`
      : 'none'
  };

  return (
    <div className="absolute bottom-0 left-4 right-4 flex justify-center p-6 pb-4 z-50 pointer-events-auto">
      <div className="flex gap-4 items-center p-4 bg-slate-900/80 backdrop-blur-md rounded-full border border-slate-700 shadow-lg text-slate-100">
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            'rounded-full w-12 h-12 transition-all duration-200',
            isMuted 
              ? 'bg-slate-800 hover:bg-slate-700' 
              : 'border border-slate-700'
          )}
          style={micButtonStyle}
          onClick={onToggleMute}
        >
          {isMuted ? <MicOff className="h-5 w-5 text-red-500" /> : <Mic className="h-5 w-5 text-slate-100" />}
        </Button>

        <Button
          variant="ghost"
          size="icon"
          className={cn(
            'rounded-full w-12 h-12 transition-all duration-200',
            isCameraOff 
              ? 'bg-slate-800 hover:bg-slate-700' 
              : 'border border-slate-700'
          )}
          style={{
            ...(isCameraOff && {
              border: '1px solid rgb(239, 68, 68)',
            })
          }}
          onClick={onToggleCamera}
        >
          {isCameraOff ? (
            <VideoOff className="h-5 w-5 text-red-500" />
          ) : (
            <Video className="h-5 w-5 text-slate-100" />
          )}
        </Button>

        <Button
          variant="ghost"
          size="icon"
          className={cn(
            'rounded-full w-12 h-12 transition-all duration-200',
            isTranscriptOpen 
              ? 'bg-purple-900/40 hover:bg-purple-900/60 text-purple-300' 
              : 'border border-slate-700 text-slate-100'
          )}
          style={{
            ...(isTranscriptOpen && {
              border: '1px solid rgb(168, 85, 247)',
              boxShadow: 'none'
            })
          }}
          onClick={onToggleTranscript}
        >
          <MessageCircle 
            className="h-5 w-5" 
            fill={isTranscriptOpen ? 'currentColor' : 'none'}
          />
        </Button>

        <Button
          variant="ghost"
          size="icon"
          className="rounded-full w-12 h-12 border border-slate-700 text-slate-100"
          onClick={onSettings}
        >
          <Settings className="h-5 w-5" />
        </Button>

        <Button
          variant="destructive"
          size="icon"
          className="rounded-full w-12 h-12 border border-slate-700"
          onClick={onEnd}
        >
          <LogOut className="h-5 w-5" />
        </Button>
      </div>
      
      {/* Ensure control panel is visible and clickable */}
      <style jsx>{`
        .absolute {
          position: absolute;
          z-index: 50;
        }
      `}</style>
    </div>
  );
}; 