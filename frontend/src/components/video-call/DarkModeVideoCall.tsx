'use client';

import { VideoCall } from './VideoCall';
import { useEffect } from 'react';
import { cn } from '@/lib/utils';

/**
 * Wrapper component that ensures video call renders in dark mode
 * and passes through required props to the main VideoCall component
 * Forces dark theme regardless of system or user preferences
 */
interface DarkModeVideoCallProps {
  type: string;
  roleId: string | null;
  templateId: string | null;
  isEnrichment: boolean;
  isPublicInterview?: boolean;
  sessionData?: {
    session_id: string;
    client_secret: {
      value: string;
      expires_at: number;
    };
    transcript_id?: string;
    role_id?: string;
    template_id?: string;
    is_public?: boolean;
  };
  onConnectionFailure?: () => void;
  reconnectAttempt?: number;
  className?: string;
}

export function DarkModeVideoCall({
  type,
  roleId,
  templateId,
  isEnrichment,
  isPublicInterview = false,
  sessionData,
  onConnectionFailure,
  reconnectAttempt = 0,
  className,
}: DarkModeVideoCallProps) {
  // Force dark mode at the component level
  useEffect(() => {
    // Add dark class to html element to ensure dark mode styles apply
    document.documentElement.classList.add('dark');
    
    return () => {
      // We don't remove the class on cleanup to avoid flashing during navigation
    };
  }, []);

  return (
    <div className={cn("dark bg-black text-white h-full w-full", className)} data-force-dark="true">
      <VideoCall
        type={type as 'intake' | 'screening' | 'interview'}
        roleId={roleId}
        templateId={templateId}
        isEnrichment={isEnrichment}
        isPublicInterview={isPublicInterview}
        sessionData={sessionData}
        onConnectionFailure={onConnectionFailure}
        reconnectAttempt={reconnectAttempt}
        className="h-full w-full"
      />
    </div>
  );
} 