'use client';

import React, { useState, useRef, useEffect, forwardRef, useMemo, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/Button';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { Message } from '@/types';
import { formatDistanceToNow } from 'date-fns';
import { debounce } from 'lodash';

interface TranscriptPanelProps {
  isOpen: boolean;
  onClose: () => void;
  messages: Message[];
}

export const TranscriptPanel = forwardRef<HTMLDivElement, TranscriptPanelProps>(({
  isOpen,
  onClose,
  messages
}, ref) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const transcriptContainerRef = useRef<HTMLDivElement>(null);
  const [autoScroll, setAutoScroll] = useState(true);
  // Use stableMessages as a buffer to avoid jittery updates
  const [stableMessages, setStableMessages] = useState<Message[]>([]);
  // Track previous message lengths for smoother updates
  const prevMessagesRef = useRef<{ [key: string]: string }>({});
  // Track active typing animation for each message
  const typingAnimationsRef = useRef<{ [key: string]: boolean }>({});
  // Track last update time to throttle visual updates
  const lastUpdateTimeRef = useRef<number>(Date.now());

  // Function to scroll to the bottom of messages
  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current && autoScroll) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [autoScroll]);

  // Debounced function to update stable messages
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const updateStableMessages = useCallback(
    debounce((newMessages: Message[]) => {
      // Process messages to create a more stable version
      const processedMessages = newMessages.map(message => {
        const existingMessage = stableMessages.find(m => m.id === message.id);
        const isAgentMessage = message.role === 'Recruiva';

        // For agent messages that are being streamed (content is growing)
        if (isAgentMessage && existingMessage &&
          message.content.length > existingMessage.content.length &&
          message.status === 'pending') {
          // Start typing animation for this message
          typingAnimationsRef.current[message.id] = true;

          // Keep content stable while typing animation is in progress
          return existingMessage;
        }

        // For completed messages or new messages, use the latest content
        if (message.status === 'completed' || !existingMessage) {
          // Mark typing as complete for this message
          typingAnimationsRef.current[message.id] = false;
        }

        return message;
      });

      setStableMessages(processedMessages);

      // Schedule a scroll after state update
      setTimeout(scrollToBottom, 50);
    }, 100),
    [stableMessages, scrollToBottom]
  );

  // Effect to update stable messages when messages change
  useEffect(() => {
    // Don't update if not enough time has passed (reduces jitter)
    const now = Date.now();
    if (now - lastUpdateTimeRef.current < 50) {
      return;
    }

    lastUpdateTimeRef.current = now;
    updateStableMessages(messages);

    // Clean up
    return () => {
      updateStableMessages.cancel();
    };
  }, [messages, updateStableMessages]);

  // Effect to scroll to bottom when panel opens
  useEffect(() => {
    if (isOpen) {
      // Auto-focus the scroll container when panel opens
      if (transcriptContainerRef.current) {
        transcriptContainerRef.current.focus();
      }

      // Scroll to bottom
      const scrollTimer = setTimeout(scrollToBottom, 100);
      return () => clearTimeout(scrollTimer);
    }

    return undefined;
  }, [isOpen, scrollToBottom]);

  // Handle manual scroll to detect when user scrolls up
  const handleScroll = () => {
    if (transcriptContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = transcriptContainerRef.current;
      const isAtBottom = Math.abs(scrollHeight - clientHeight - scrollTop) < 50;
      setAutoScroll(isAtBottom);
    }
  };

  // Word-by-word animation effect for agent messages
  const AnimatedText = ({ content, isAnimating }: { content: string, isAnimating: boolean }) => {
    const [visibleContent, setVisibleContent] = useState(content);
    const contentRef = useRef(content);
    const animationRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
      // Store the latest content
      contentRef.current = content;

      // If not animating, show full content immediately
      if (!isAnimating) {
        setVisibleContent(content);
        return;
      }

      // Set up word-by-word animation
      const words = content.split(' ');
      let currentWordCount = visibleContent.split(' ').length;

      // Don't animate if we already have all words visible
      if (currentWordCount >= words.length) {
        setVisibleContent(content);
        return;
      }

      // Clear any existing animation
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }

      // Function to reveal words one at a time
      const animateNextWord = () => {
        setVisibleContent(prev => {
          const prevWords = prev.split(' ');
          const wordsToShow = Math.min(prevWords.length + 1, words.length);
          return words.slice(0, wordsToShow).join(' ');
        });

        currentWordCount++;

        // Continue animation if more words to reveal
        if (currentWordCount < words.length) {
          // Adjust speed based on word length - faster for shorter words
          const speed = Math.max(30, Math.min(100, 20 + words[currentWordCount].length * 10));
          animationRef.current = setTimeout(animateNextWord, speed);
        }
      };

      // Start animation
      animationRef.current = setTimeout(animateNextWord, 50);

      // Cleanup
      return () => {
        if (animationRef.current) {
          clearTimeout(animationRef.current);
        }
      };
    }, [content, isAnimating, visibleContent]);

    return <>{visibleContent}</>;
  };

  const renderMessage = (message: Message, index: number) => {
    const isUser = message.role === 'user';
    const isAgent = message.role === 'Recruiva';
    const isPending = message.status === 'pending';
    const isLastMessage = index === stableMessages.length - 1;
    const showTypingIndicator = isPending && isLastMessage && isAgent; // Only show typing for agent

    // Message key and content tracking
    const messageId = message.id || `message-${index}`;
    const currentContent = message.content || '';
    const previousContent = prevMessagesRef.current[messageId] || '';
    const isContentUpdate = previousContent !== '' && currentContent !== previousContent;

    // Update content reference for future comparisons
    prevMessagesRef.current[messageId] = currentContent;

    // Calculate animation properties
    const isRecentMessage = Date.now() - (
      typeof message.timestamp === 'number'
        ? message.timestamp
        : Date.parse(message.timestamp as string)
    ) < 1000;

    // Determine if message is actively being typed
    const isTypingAnimation = typingAnimationsRef.current[messageId] && isAgent && !showTypingIndicator;

    // Create a stable key for this message
    const messageKey = `message-${messageId}`;

    return (
      <div
        key={messageKey}
        className={cn(
          'flex flex-col mb-4',
          isUser ? 'items-end' : 'items-start',
          isRecentMessage && !isContentUpdate && 'animate-fadeIn'
        )}
        style={{
          opacity: 1,
          transform: 'translateZ(0)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        }}
      >
        <div
          className={cn(
            'max-w-[80%] rounded-xl p-4 shadow-sm',
            isUser
              ? 'bg-indigo-900/20 text-slate-100'
              : 'text-slate-100 bg-purple-900/20',
            isAgent && isContentUpdate && 'transition-all duration-300 ease-out',
            isPending && isAgent && 'bg-purple-900/15'
          )}
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-white">
              {isUser ? 'You' : 'Recruiva'}
            </span>
            {showTypingIndicator && (
              <span className="text-purple-200 text-xs ml-2 opacity-80 animate-pulse">(speaking...)</span>
            )}
          </div>

          {showTypingIndicator ? (
            <div className="flex items-center space-x-1 h-6 mb-2">
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
                style={{ animationDuration: '0.8s' }} />
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
                style={{ animationDuration: '0.8s', animationDelay: '0.2s' }} />
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
                style={{ animationDuration: '0.8s', animationDelay: '0.4s' }} />
            </div>
          ) : (
            <p className={cn(
              "text-base leading-relaxed whitespace-pre-wrap break-words transition-all duration-200",
              isPending && 'text-slate-200',
              isAgent && 'will-change-contents',
              isAgent && isContentUpdate && 'animate-highlightText'
            )}>
              {isAgent ? (
                <AnimatedText
                  content={message.content}
                  isAnimating={isTypingAnimation}
                />
              ) : (
                message.content
              )}
            </p>
          )}

          <div className="mt-2 text-xs text-slate-400">
            {formatDistanceToNow(new Date(message.timestamp), {
              addSuffix: true,
            })}
          </div>
        </div>
      </div>
    );
  };

  // Memoize the messages list to prevent unnecessary re-renders
  const memoizedMessages = useMemo(() => {
    return stableMessages.map((message, index) => renderMessage(message, index));
  }, [stableMessages, renderMessage]);

  return (
    <div
      ref={ref}
      className={cn(
        'transcript-panel',
        'bg-slate-900/60 backdrop-blur-md',
        'min-h-[400px]',
        'rounded-xl shadow-lg',
        'transform transition-all duration-300 ease-in-out z-50',
        isOpen
          ? 'opacity-100 translate-x-0 scale-100'
          : 'opacity-0 translate-x-[100%] scale-95 pointer-events-none'
      )}
      style={{
        '--tw-backdrop-blur': 'blur(8px)',
        '--tw-backdrop-saturate': 'saturate(180%)',
        position: 'absolute',
        top: '16px',
        right: '16px',
        bottom: '26px',
        width: '380px',
        maxWidth: 'calc(100% - 32px)',
        maxHeight: 'calc(100% - 42px)',
        // Media query adjustment handled in VideoCall.tsx
      } as React.CSSProperties}
    >
      <div className="flex flex-col h-full overflow-hidden rounded-xl">
        <div className="flex items-center justify-between px-4 h-14 backdrop-blur-sm bg-slate-900/30">
          <h2 className="text-lg font-semibold text-slate-100">Transcript</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8 rounded-full hover:bg-slate-800/60 flex items-center justify-center bg-slate-800/20 text-slate-100"
            aria-label="Close transcript"
          >
            <span className="text-xl font-bold text-foreground leading-none" style={{ marginTop: "-2px" }}>×</span>
          </Button>
        </div>

        <ScrollArea
          ref={transcriptContainerRef}
          className="flex-1 p-4 overflow-y-auto bg-slate-900/40 text-slate-100"
          onScroll={handleScroll}
          style={{ height: '100%', overflowAnchor: 'auto' }}
        >
          <div className="space-y-2 pb-2 relative">
            {stableMessages.length > 0 ? (
              <div className="flex flex-col" style={{ minHeight: '100%' }}>
                {memoizedMessages}
              </div>
            ) : (
              <div className="text-center text-slate-400 py-8">
                <p className="italic">
                  Hello!<br />
                  Say something to begin the conversation...
                </p>
              </div>
            )}
            <div ref={messagesEndRef} style={{ float: 'left', clear: 'both', height: '1px' }} />
          </div>
        </ScrollArea>
      </div>

      {/* Custom scrollbar styles */}
      <style jsx global>{`
        .overflow-y-auto::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }

        .overflow-y-auto::-webkit-scrollbar-track {
          background: transparent;
        }

        .overflow-y-auto::-webkit-scrollbar-thumb {
          background: rgba(156, 163, 175, 0.5);
          border-radius: 3px;
        }

        .overflow-y-auto::-webkit-scrollbar-thumb:hover {
          background: rgba(156, 163, 175, 0.7);
        }

        /* For Firefox */
        .overflow-y-auto {
          scrollbar-width: thin;
          scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
        }

        /* Ensure the transcript panel doesn't affect layout flow */
        .transcript-panel {
          position: absolute;
          z-index: 50;
          pointer-events: auto;
          color-scheme: dark; /* Force dark mode for system controls */
        }

        /* Force the scroll container to use hardware acceleration */
        .overflow-y-auto {
          transform: translateZ(0);
          will-change: transform;
          overflow-anchor: auto;
        }

        /* Ensure messages appear smoothly */
        .space-y-2 > * {
          transition: opacity 0.3s ease, transform 0.2s ease-out;
        }

        /* Animation for text highlighting (subtle highlight effect) */
        @keyframes highlightText {
          0% { background-color: rgba(124, 58, 237, 0.15); }
          100% { background-color: transparent; }
        }

        .animate-highlightText {
          animation: highlightText 1.8s ease-out;
        }

        /* Animation for new messages */
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out;
        }

        /* Will-change property for content that changes frequently */
        .will-change-contents {
          will-change: contents;
          backface-visibility: hidden;
        }
      `}</style>
    </div>
  );
});

// Add display name to component
TranscriptPanel.displayName = 'TranscriptPanel';