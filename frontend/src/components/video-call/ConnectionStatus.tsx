'use client';

import React, { useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ConnectionStatusProps {
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
}

const statusConfig = {
  disconnected: {
    color: 'bg-neutral-500',
    label: 'Disconnected',
  },
  connecting: {
    color: 'bg-orange-500',
    label: 'Connecting',
  },
  connected: {
    color: 'bg-green-500',
    label: 'Connected',
  },
  error: {
    color: 'bg-red-500',
    label: 'Error',
  },
};

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ status }) => {
  const config = statusConfig[status];

  // Add text shadow style once when component mounts
  useEffect(() => {
    // Check if style already exists to avoid duplicates
    const existingStyle = document.getElementById('connection-status-style');
    if (!existingStyle) {
      const style = document.createElement('style');
      style.id = 'connection-status-style';
      style.textContent = `
        .text-shadow {
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }
      `;
      document.head.appendChild(style);
      
      // Clean up on unmount
      return () => {
        const styleToRemove = document.getElementById('connection-status-style');
        if (styleToRemove) {
          document.head.removeChild(styleToRemove);
        }
      };
    }
    
    // Return empty cleanup function for consistent return
    return () => {};
  }, []);

  return (
    <div className="m-2 pointer-events-none">
      <div className={cn(
        'w-1.5 h-1.5 rounded-full shadow-md border border-white/30', 
        config.color, 
        status === 'connecting' ? 'animate-pulse' : ''
      )} title={config.label} />
    </div>
  );
}; 