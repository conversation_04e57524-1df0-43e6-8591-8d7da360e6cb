'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { VideoSession } from './VideoSession';
import { ControlPanel } from './ControlPanel';
import { TranscriptPanel } from './TranscriptPanel';
import { useRouter } from 'next/navigation';
import { Message } from '@/types';
import { saveTranscript, updateTranscript, completeTranscript } from '@/lib/firebase/transcript';
import { savePublicInterviewTranscript, completePublicInterviewTranscript, isPublicInterviewFlow } from '@/lib/firebase/public-interview';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth-context';
import { v4 as uuidv4 } from 'uuid';
import { SettingsMenu } from './SettingsMenu';
import { realtimeService } from '@/services/realtime';
import { Button } from '@/components/ui/Button';

// Add type declaration at the top of the file
declare global {
  interface Window {
    __redirectingFromCall: boolean;
  }
}

interface SessionData {
  session_id: string;
  client_secret: {
    value: string;
    expires_at: number;
  };
  transcript_id?: string;
  role_id?: string;
  template_id?: string;
  templateId?: string;
  application_id?: string;
  is_public?: boolean;
  stage_name?: string;
  stageName?: string;
  stage_index?: number;
  stageIndex?: number;
  template?: Record<string, any>;
  resume_text?: string;
  resumeText?: string;
  candidate_data?: {
    resume?: string;
  };
  application_data?: {
    resume_text?: string;
  };
}

interface FunctionCallArgs {
  status?: 'disconnected' | 'connecting' | 'connected' | 'error';
  reason?: string;
}

export interface VideoCallProps {
  type?: 'intake' | 'screening' | 'interview';
  roleId?: string | null;
  templateId?: string | null;
  isEnrichment?: boolean;
  isPublicInterview?: boolean;
  sessionData?: {
    session_id: string;
    client_secret: {
      value: string;
      expires_at: number;
    };
    transcript_id?: string;
    role_id?: string;
    template_id?: string;
    is_public?: boolean;
  };
  className?: string;
  onConnectionFailure?: () => void;
  reconnectAttempt?: number;
}

export const VideoCall: React.FC<VideoCallProps> = ({
  type = 'intake',
  roleId = null,
  templateId = null,
  isEnrichment = false,
  isPublicInterview = false,
  sessionData: initialSessionData,
  className,
  onConnectionFailure,
  reconnectAttempt = 0
}) => {
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();
  const [isTranscriptOpen, setIsTranscriptOpen] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [isCameraOff, setIsCameraOff] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('connecting');
  const [selectedCameraId, setSelectedCameraId] = useState<string>('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [sessionData, setSessionData] = useState<SessionData | null>(initialSessionData || null);
  const [isLoadingSession, setIsLoadingSession] = useState<boolean>(!initialSessionData);
  // Prefer OpenAI-style session IDs (sess_*) when available
  const sessionId = useRef<string>(
    // First check if initialSessionData has a session_id with OpenAI format
    (initialSessionData?.session_id && initialSessionData.session_id.startsWith('sess_')) ? initialSessionData.session_id :
    // Then check if transcript_id has OpenAI format
    (initialSessionData?.transcript_id && initialSessionData.transcript_id.startsWith('sess_')) ? initialSessionData.transcript_id :
    // Otherwise use whatever transcript_id is available or generate a new UUID
    initialSessionData?.transcript_id || uuidv4()
  );
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const connectionFailureReported = useRef<boolean>(false);
  const [exiting, setExiting] = useState(false);

  // Refs to store active media streams
  const audioStreamRef = useRef<MediaStream | null>(null);
  const videoStreamRef = useRef<MediaStream | null>(null);

  // Track the current sync operation - moved to component level
  const isSyncingRef = useRef<boolean>(false);
  const pendingSyncRef = useRef<boolean>(false);

  // Store the latest role data from return_json_output

  // Store the latest interview results from return_interview_results

  // Add a state to track if there was an error loading the interview session
  const [sessionError, setSessionError] = useState<string | null>(null);

  // Update the component to properly handle transcripts for public interviews
  const transcriptPanelRef = useRef<HTMLDivElement>(null);

  // Add a state to track if the settings menu is open
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);

  // Debug log for tracking component initialization
  console.log(`VideoCall component initialized (${type}):`, {
    roleId,
    templateId,
    isEnrichment,
    isPublicInterview,
    hasInitialSessionData: !!initialSessionData,
    reconnectAttempt
  });

  // Fetch session data for interview sessions
  useEffect(() => {
    const initializeSession = async () => {
      // Skip session initialization if we already have session data
      if (initialSessionData) {
        console.log('Using provided session data:', {
          session_id: initialSessionData.session_id,
          has_client_secret: !!initialSessionData.client_secret,
          transcript_id: initialSessionData.transcript_id
        });
        setSessionData(initialSessionData);
        setIsLoadingSession(false);
        setIsTranscriptOpen(true);
        return;
      }

      try {
        console.log(`Initializing ${type} session...`);
        setIsLoadingSession(true);

        let response: any;
        if (type === 'interview' && roleId && templateId) {
          console.log(`Creating interview session for role ${roleId} with template ${templateId}`);
          response = await realtimeService.createInterviewSession(roleId, templateId);
        } else if (type === 'intake' && roleId) {
          console.log(`Creating intake session for role ${roleId}`);
          response = await realtimeService.createSession(roleId, isEnrichment);
        } else {
          console.log('Creating default session');
          response = await realtimeService.createSession();
        }

        if ('detail' in response) {
          console.error('Error creating session:', response.detail);
          setSessionError(response.detail);
          toast({
            title: "Session Error",
            description: response.detail || "Failed to create session",
            variant: "destructive"
          });
        } else {
          console.log('Session created successfully:', {
            session_id: response.session_id,
            has_client_secret: !!response.client_secret,
            expires_at: new Date(response.client_secret.expires_at * 1000).toISOString(),
            role_id: roleId,
            type: type,
            isEnrichment: isEnrichment
          });

          // Make sure to include roleId in the session data if it's not already there
          const enhancedResponse = {
            ...response,
            role_id: roleId || response.role_id,
            template_id: templateId || response.template_id
          };

          setSessionData(enhancedResponse);

          // Prefer OpenAI-style session IDs (sess_*) when available
          if (response.session_id && response.session_id.startsWith('sess_')) {
            sessionId.current = response.session_id;
            console.log('Using OpenAI-style session_id:', response.session_id);
          } else if (response.transcript_id && response.transcript_id.startsWith('sess_')) {
            sessionId.current = response.transcript_id;
            console.log('Using OpenAI-style transcript_id:', response.transcript_id);
          } else {
            sessionId.current = response.transcript_id || uuidv4();
            console.log('Using UUID-style session ID:', sessionId.current);
          }
          setIsTranscriptOpen(true);
        }
      } catch (error) {
        console.error('Error initializing session:', error);
        setSessionError('Failed to initialize session');
        toast({
          title: "Connection Error",
          description: "Failed to initialize video session. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoadingSession(false);
      }
    };

    if (!isPublicInterviewFlow()) {
      initializeSession();
    }
  }, [roleId, templateId, type, isPublicInterview, initialSessionData, toast, isEnrichment]);

  // Handle transcript syncing with debounce
  useEffect(() => {
    // Skip syncing for public interviews - they don't need to be saved to the user's account
    if (isPublicInterviewFlow()) return;

    if (!user?.uid || !roleId || messages.length === 0) return;

    // Clear any existing timeout
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current);
    }

    // Increase debounce delay to reduce frequency of requests
    const syncDelay = 3000; // 3 seconds instead of 5 seconds

    // Track last sync timestamp to avoid redundant syncs
    const lastSyncTimeKey = `last_sync_${roleId}_${sessionId.current}`;
    const lastSyncTime = parseInt(sessionStorage.getItem(lastSyncTimeKey) || '0', 10);
    const now = Date.now();

    // Skip syncing if we've synced recently (within 1.5 seconds)
    const minSyncInterval = 1500; // 1.5 seconds
    if (now - lastSyncTime < minSyncInterval) {
      console.log('Skipping sync - too soon since last sync:', now - lastSyncTime, 'ms');
      return;
    }

    // Set a new timeout for syncing
    syncTimeoutRef.current = setTimeout(async () => {
      if (isSyncingRef.current) {
        pendingSyncRef.current = true;
        return;
      }

      isSyncingRef.current = true;
      try {
        // Record sync attempt time
        sessionStorage.setItem(lastSyncTimeKey, Date.now().toString());

        // Add retry logic with exponential backoff
        let retryCount = 0;
        const maxRetries = 3;

        const attemptSync = async (): Promise<boolean> => {
          try {
            await updateTranscript(roleId, sessionId.current, messages, type || 'intake');
            return true;
          } catch (error) {
            if (retryCount < maxRetries) {
              retryCount++;
              const delayMs = Math.min(1000 * Math.pow(2, retryCount), 5000);
              console.log(`Sync failed, retrying in ${delayMs}ms (attempt ${retryCount}/${maxRetries})`);
              await new Promise(resolve => setTimeout(resolve, delayMs));
              return attemptSync();
            }
            throw error;
          }
        };

        await attemptSync();
        isSyncingRef.current = false;

        // If a sync was requested while we were syncing, do another sync
        if (pendingSyncRef.current) {
          pendingSyncRef.current = false;
          // Use a small delay before the next sync to prevent connection issues
          setTimeout(async () => {
            try {
              await updateTranscript(roleId, sessionId.current, messages, type || 'intake');
            } catch (syncError) {
              console.error('Failed to sync pending update:', syncError);
            }
          }, 1000);
        }
      } catch (error) {
        console.error('Failed to sync transcript after retries:', error);
        isSyncingRef.current = false;
        toast({
          title: 'Sync Warning',
          description: 'Some updates may not have been saved. Your transcript will be saved when the call ends.',
          variant: 'destructive',
        });
      }
    }, syncDelay);

    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
    };
  }, [messages, roleId, user?.uid]);

  const stopAllMediaStreams = () => {
    // Create a single function to stop any media stream
    const stopStream = (stream: MediaStream | null) => {
      if (!stream) return;

      try {
        stream.getTracks().forEach(track => {
          try {
            track.enabled = false;
            track.stop();
          } catch (trackError) {
            console.error('Error stopping track:', trackError);
          }
        });
      } catch (streamError) {
        console.error('Error stopping stream:', streamError);
      }
    };

    // Stop each stream
    stopStream(audioStreamRef.current);
    stopStream(videoStreamRef.current);

    // Clear references
    audioStreamRef.current = null;
    videoStreamRef.current = null;
  };

  const handleExit = async () => {
    // Prevent duplicate calls by checking if already ended
    if (sessionStorage.getItem('call_ending_in_progress') === 'true') {
      console.log('[VideoCall] Call end already in progress, skipping duplicate exit');
      return;
    }

    console.log('[VideoCall] handleExit called with flags:', {
      isPublicInterview,
      roleId,
      type,
      hasUser: !!user,
      localStorage: {
        isPublicInterview: localStorage.getItem('isPublicInterview')
      }
    });

    // Check if this is a public interview - need to handle redirection differently
    const isPublicFlow = isPublicInterviewFlow();

    // If this is an intake call, use the more robust handleEndCall function
    if (roleId && type === 'intake') {
      return handleEndCall();
    }

    // For public interviews, we need special handling to ensure correct redirection
    if (isPublicFlow && roleId) {
      console.log('[VideoCall] Public interview exited by user, redirecting to thank you page');

      // Mark that the interview was completed by user
      localStorage.setItem('publicInterviewCompleted', 'true');
      localStorage.setItem('userEndedInterview', 'true');

      // Store all possible session IDs to ensure evaluation can find them
      if (sessionData?.session_id) {
        localStorage.setItem('openai_session_id', sessionData.session_id);
        localStorage.setItem('completedSessionId', sessionData.session_id);
      }
      if (sessionData?.transcript_id) {
        localStorage.setItem('lastTranscriptId', sessionData.transcript_id);
      }
      if (sessionId.current) {
        localStorage.setItem('currentSessionId', sessionId.current);
      }
        
      // Store stage and template information for evaluation
      if (sessionData?.stage_name || sessionData?.stageName) {
        localStorage.setItem('interviewStageName', (sessionData?.stage_name || sessionData?.stageName) as string);
      }
      if (sessionData?.stage_index || sessionData?.stageIndex) {
        localStorage.setItem('interviewStageIndex', String(sessionData?.stage_index || sessionData?.stageIndex || 0));
      }
      if (sessionData?.template_id || sessionData?.templateId) {
        localStorage.setItem('interviewTemplateId', (sessionData?.template_id || sessionData?.templateId) as string);
      }
      
      // Store resume text for evaluation if available
      if (sessionData?.resume_text || sessionData?.resumeText) {
        localStorage.setItem('resumeText', (sessionData?.resume_text || sessionData?.resumeText) as string);
      } else if (sessionData?.candidate_data?.resume) {
        localStorage.setItem('resumeText', sessionData.candidate_data.resume as string);
      } else if (sessionData?.application_data?.resume_text) {
        localStorage.setItem('resumeText', sessionData.application_data.resume_text as string);
      }
        
      // Store template data for better evaluation context
      if (sessionData?.template && typeof sessionData.template === 'object') {
        try {
          localStorage.setItem('templateData', JSON.stringify(sessionData.template));
        } catch (e) {
          console.warn('[VideoCall] Error storing template data:', e);
        }
      }

      // Clear ALL sessionStorage to prevent authenticated API calls
      sessionStorage.clear();

      // Stop media streams
      stopAllMediaStreams();

      // For public interviews, redirect directly to thank you page
      const appId = sessionData?.application_id || localStorage.getItem('applicationId') || localStorage.getItem('lastApplicationId');
      localStorage.setItem('lastApplicationId', appId || '');
      const thankYouUrl = `/instant-interview/${roleId}/thank-you${appId ? `?applicationId=${appId}` : ''}`;

      // Use window.location for hard redirect
      window.location.href = thankYouUrl;
      return;
    }

    // For non-intake calls or if no roleId, just redirect to dashboard
    stopAllMediaStreams();
    // Use window.location.href for a hard redirect to force a full page reload
    window.location.href = roleId ? `/roles/${roleId}` : '/dashboard';
  };

  // Handle messages update including WebRTC errors
  const handleMessagesUpdate = async (newMessages: Message[]) => {
    // For public interviews, store messages locally without requiring auth
    if (isPublicInterviewFlow()) {
      // Validate newMessages
      if (!Array.isArray(newMessages)) {
        console.error('Invalid messages format:', newMessages);
        return;
      }

      // Check for error messages that indicate a connection failure
      const hasConnectionError = newMessages.some(msg =>
        msg.role === 'system' &&
        msg.status === 'failed' &&
        (msg.content.includes('Connection error') ||
         msg.content.includes('session has expired') ||
         msg.content.includes('Max reconnect attempts reached'))
      );

      if (hasConnectionError && !connectionFailureReported.current && onConnectionFailure) {
        console.log('Reporting connection failure to parent component');
        connectionFailureReported.current = true;
        setConnectionStatus('error');
        onConnectionFailure();
      }

      // Update state with new messages
      setMessages(newMessages);

      // Store messages using our new utility function
      try {
        const transcriptId = sessionId.current || initialSessionData?.transcript_id;
        if (transcriptId) {
          // Get the current role ID from various sources
          const currentRoleId = roleId || initialSessionData?.role_id || localStorage.getItem('roleId');
          if (!currentRoleId) {
            console.warn('No roleId available for saving public interview transcript');
            return;
          }

          // Get application ID from various sources
          const appId = sessionData?.application_id ||
                        localStorage.getItem('applicationId') ||
                        localStorage.getItem('lastApplicationId') ||
                        localStorage.getItem('sessionApplicationId');

          // Use only one method to save the transcript - avoid redundancy
          await savePublicInterviewTranscript(
            transcriptId,
            newMessages,
            currentRoleId,
            appId || undefined
          );
        }
      } catch (error) {
        console.error('Error saving public interview transcript:', error);
      }

      return;
    }

    // Regular authenticated flow:
    if (!user?.uid) {
      console.log('No authenticated user found');
      return;
    }

    // Validate newMessages
    if (!Array.isArray(newMessages)) {
      console.error('Invalid messages format:', newMessages);
      return;
    }

    // Update state with new messages
    setMessages(newMessages);

    // Only proceed with Firebase operations if we have a roleId
    if (!roleId) {
      console.warn('No roleId provided for transcript saving');
      return;
    }

    try {
      // If this is the first message, initialize the transcript
      if (newMessages.length === 1 && newMessages[0]?.content) {
        await saveTranscript(roleId, sessionId.current, newMessages, type || 'intake');
        console.log('Initialized new transcript:', sessionId.current);
      }
      // Only sync when the last message is completed (turn has ended)
      else if (newMessages.length > 0 && newMessages[newMessages.length - 1].status === 'completed') {
        // Clear any existing timeout
        if (syncTimeoutRef.current) {
          clearTimeout(syncTimeoutRef.current);
        }

        // Sync immediately after turn completion
        try {
          await updateTranscript(roleId, sessionId.current, newMessages, type || 'intake');
          console.log('Updated transcript:', sessionId.current);
        } catch (error) {
          console.error('Failed to sync transcript:', error);
          toast({
            title: 'Sync Error',
            description: 'Failed to sync transcript. Will retry automatically.',
            variant: 'destructive',
          });
        }
      }
    } catch (error) {
      console.error('Error handling transcript update:', error);
    }
  };

  // Handle function calls from the AI
  const handleFunctionCall = (functionName: string, args: FunctionCallArgs) => {
    console.log(`Function call from agent: ${functionName}`, args);

    // Check if this is a public interview - need to handle redirection differently
    const isPublicFlow = isPublicInterviewFlow();

    // Remove JSON output handling - we'll use transcript-based enrichment only
    if (functionName === 'connection_status_update' && args?.status) {
      // Update the connection status
      setConnectionStatus(args.status);
      return;
    }
    // Handle both end call function names with a unified approach
    else if (functionName === 'signal_end_call' || functionName === 'end_the_call' || functionName === 'end_the_interview') {
      console.log('Agent signaled end of call:', functionName, args?.reason || 'No reason provided', {isPublicFlow});

      // Show a toast notification with the reason if available
      toast({
        title: 'Call Completed',
        description: args?.reason || 'Your call is now complete.',
      });

      // For public interviews, use a more direct approach to redirect
      if (isPublicFlow && roleId) {
        console.log('[VideoCall] Public interview exited by agent, redirecting to thank you page');

        // Mark that the interview was completed
        localStorage.setItem('publicInterviewCompleted', 'true');

        // Store all possible session IDs to ensure evaluation can find them
        if (sessionData?.session_id) {
          localStorage.setItem('openai_session_id', sessionData.session_id);
          localStorage.setItem('completedSessionId', sessionData.session_id);
        }
        if (sessionData?.transcript_id) {
          localStorage.setItem('lastTranscriptId', sessionData.transcript_id);
        }
        if (sessionId.current) {
          localStorage.setItem('currentSessionId', sessionId.current);
        }
        
        // Store stage and template information for evaluation
        if (sessionData?.stage_name || sessionData?.stageName) {
          localStorage.setItem('interviewStageName', sessionData?.stage_name || sessionData?.stageName || '');
        }
        if (sessionData?.stage_index || sessionData?.stageIndex) {
          localStorage.setItem('interviewStageIndex', String(sessionData?.stage_index || sessionData?.stageIndex || 0));
        }
        if (sessionData?.template_id || sessionData?.templateId) {
          localStorage.setItem('interviewTemplateId', sessionData?.template_id || sessionData?.templateId || '');
        }

        // Clear ALL sessionStorage to prevent authenticated API calls
        sessionStorage.clear();

        // For public interviews, redirect directly to thank you page
        const appId = sessionData?.application_id || localStorage.getItem('applicationId') || localStorage.getItem('lastApplicationId');
        localStorage.setItem('lastApplicationId', appId || '');
        const thankYouUrl = `/instant-interview/${roleId}/thank-you${appId ? `?applicationId=${appId}` : ''}`;

        // Log all saved session IDs for debugging
        console.log('[VideoCall] Saved session IDs before redirect:', {
          openai_session_id: localStorage.getItem('openai_session_id'),
          completedSessionId: localStorage.getItem('completedSessionId'),
          lastTranscriptId: localStorage.getItem('lastTranscriptId'),
          currentSessionId: localStorage.getItem('currentSessionId'),
          applicationId: appId,
          interviewStageName: localStorage.getItem('interviewStageName'),
          interviewStageIndex: localStorage.getItem('interviewStageIndex'),
          interviewTemplateId: localStorage.getItem('interviewTemplateId'),
          hasTemplateData: !!localStorage.getItem('templateData'),
          hasResumeText: !!localStorage.getItem('resumeText')
        });

        // Use window.location for hard redirect
        window.location.href = thankYouUrl;
        return;
      }

      // For all other types of calls, use handleEndCall
      handleEndCall();
      return;
    }

    // If no specific handler matched, return undefined
    return undefined;
  };

  // Robust end call function for intake calls with auto-enrichment
  const handleEndCall = async () => {
    // Create an AbortController to cancel requests when redirecting
    const abortController = new AbortController();

    // Add debug logging to trace execution path
    console.log('[VideoCall] handleEndCall called with flags:', {
      isPublicInterview,
      roleId,
      type,
      hasUser: !!user,
      hasSessionData: !!sessionData,
      sessionStorage: {
        call_ending_in_progress: sessionStorage.getItem('call_ending_in_progress'),
        coming_from_call: sessionStorage.getItem('coming_from_call'),
        call_role_id: sessionStorage.getItem('call_role_id'),
        call_transcript_id: sessionStorage.getItem('call_transcript_id'),
        auto_enrich_role: sessionStorage.getItem('auto_enrich_role')
      },
      localStorage: {
        isPublicInterview: localStorage.getItem('isPublicInterview'),
        applicationId: localStorage.getItem('applicationId'),
        lastApplicationId: localStorage.getItem('lastApplicationId'),
      }
    });

    // Prevent duplicate calls by checking if already ended
    if (sessionStorage.getItem('call_ending_in_progress') === 'true') {
      console.log('[VideoCall] Call end already in progress, skipping duplicate call');
      return;
    }

    // Set flag to prevent duplicate calls - do this FIRST
    sessionStorage.setItem('call_ending_in_progress', 'true');

    // Set a global flag for API interceptors to recognize
    window.__redirectingFromCall = true;

    // Define a cleanup function
    const cleanup = () => {
      sessionStorage.removeItem('call_ending_in_progress');
      // Abort any pending requests when we're redirecting
      try {
        abortController.abort('Navigation: User leaving call page');
      } catch (abortError) {
        console.warn('[VideoCall] Error aborting pending requests:', abortError);
      }
      // Remove global flag after a delay (to let interceptors see it)
      setTimeout(() => {
        window.__redirectingFromCall = false;
      }, 2000);
    };

    try {
      // Stop all media streams IMMEDIATELY to prevent resource leaks
      stopAllMediaStreams();
      setExiting(true);

      // CRITICAL FIX: Force check localStorage for public interview flag as a backup
      // This ensures we catch the public interview state even if the prop is somehow lost
      const isPublicFlow = isPublicInterviewFlow();

      if (!roleId) {
        // For a public interview without roleId, redirect to homepage instead of dashboard
        if (isPublicFlow) {
          console.log('[VideoCall] No roleId but in public flow, redirecting to homepage');
          window.location.href = '/';
        } else {
          window.location.href = '/dashboard';
        }
        cleanup();
        return;
      }

      // Check if this is a public interview (candidate) vs. internal interview/intake
      if (isPublicFlow) {
        console.log('[VideoCall] Public interview completed, preparing for thank you page redirect');

        // For public interviews, we need to ensure the transcript is saved before redirect
        if (sessionId.current && messages.length > 0) {
          try {
            // Use a single method to complete the transcript - avoid redundancy
            // Increased timeout to 5000ms to allow more time for completion
            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Transcript completion timeout')), 5000)
            );

            // Use our utility function to complete the transcript
            await Promise.race([
              completePublicInterviewTranscript(sessionId.current, roleId),
              timeoutPromise
            ]).catch(error => {
              // Log but continue - don't throw the error
              console.warn('[VideoCall] Transcript completion timed out or failed:', error);
            });
            console.log('[VideoCall] Successfully completed public interview transcript');
          } catch (error) {
            console.warn('[VideoCall] Public interview transcript completion failed:', error);
            // Continue with redirect even if this fails
          }
        }

        // CRITICAL: Perform cleanup BEFORE navigation
        // Clear ANY sessionStorage that might cause authenticated API calls
        sessionStorage.clear(); // Clear all session storage to be absolutely sure

        // For public interviews (candidates), redirect to the thank you page
        const appId = sessionData?.application_id || localStorage.getItem('applicationId') || localStorage.getItem('lastApplicationId');
        const thankYouUrl = `/instant-interview/${roleId}/thank-you${appId ? `?applicationId=${appId}` : ''}`;

        // Store session information in localStorage that can be used by thank you page
        try {
          if (sessionId.current) {
            localStorage.setItem('completedSessionId', sessionId.current);
            localStorage.setItem('sessionCompletedAt', new Date().toISOString());
          }
        } catch (storageError) {
          console.warn('[VideoCall] Error storing session completion in localStorage:', storageError);
        }

        // IMPORTANT: Set this flag to indicate public interview completion
        localStorage.setItem('publicInterviewCompleted', 'true');

        // Navigate to thank you page - use replace to prevent browser back to interview
        console.log('[VideoCall] Redirecting to thank you page:', thankYouUrl);

        // Use window.location for hard redirect to avoid Next.js router caching issues
        cleanup();
        window.location.href = thankYouUrl;
        return;
      }

      // For internal users (intake calls, internal interviews)
      // Set flags in sessionStorage for RoleDetailContent to trigger auto-enrichment
      sessionStorage.setItem('coming_from_call', 'true');
      sessionStorage.setItem('call_role_id', roleId);
      sessionStorage.setItem('call_transcript_id', sessionId.current || '');
      // Add a flag to explicitly signal that auto-enrichment should happen
      sessionStorage.setItem('auto_enrich_role', 'true');

      // Show a toast to inform the user of the process
      toast({
        title: 'Call Ended',
        description: 'Saving transcript and redirecting to role details...',
      });

      // Save transcript completion status to Firebase - quick operation
      if (sessionId.current) {
        try {
          // Increased timeout to 5000ms to allow more time for completion
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Transcript completion timeout')), 5000)
          );

          await Promise.race([
            completeTranscript(roleId, sessionId.current, type || 'intake'),
            timeoutPromise
          ]).catch(error => {
            // Log but continue - don't throw the error
            console.warn('[VideoCall] Transcript completion timed out or failed:', error);
            // Continue with navigation even if this fails - don't block
          });
          console.log('[VideoCall] Successfully completed transcript in Firebase');

          // IMPROVEMENT: Redirect immediately after completing transcript
          // This improves perceived performance by not waiting for enrichment
          console.log('[VideoCall] Redirecting to role details page immediately');

          // Start a background process to enrich the role, but don't await it
          try {
            // Don't try to wait for this response, just fire it and forget
            setTimeout(async () => {
              try {
                console.log('[VideoCall] Starting background enrichment process');
                // Use fetch with keepalive to ensure the request continues even after navigation
                fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/realtime/end-call`, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${await user?.getIdToken()}`
                  },
                  body: JSON.stringify({
                    role_id: roleId,
                    transcript_id: sessionId.current || '',
                    user_id: user?.uid || 'unknown',
                    is_enrichment: isEnrichment,
                    process_method: 'enrichment_agent',
                    agent_type: 'intake_agent'
                  }),
                  keepalive: true, // This allows the request to continue even after page navigation
                  // Signal isn't needed for keepalive requests
                }).then(response => {
                  console.log('[VideoCall] Background enrichment completed:', response.status);
                  sessionStorage.setItem('background_enrichment_complete', 'true');
                }).catch(bgError => {
                  console.error('[VideoCall] Background enrichment error:', bgError);
                  // Still consider successful since we're already navigating
                });
              } catch (bgError) {
                console.error('[VideoCall] Background enrichment error:', bgError);
              }
            }, 100);
          } catch (apiError) {
            console.warn('[VideoCall] Failed to start background enrichment:', apiError);
            // Continue with navigation even if this fails
          }

          // Redirect to roles page AFTER starting the background process
          cleanup();
          // Use window.location.href for a hard redirect to force a full page reload
          window.location.href = `/roles/${roleId}`;
          return;
        } catch (transcriptError) {
          console.warn('[VideoCall] Error in main transcript completion flow:', transcriptError);
          // Continue with navigation even if this fails
        }
      }

      // FALLBACK: If we couldn't do the optimized flow, use the original flow
      console.log('[VideoCall] Using fallback enrichment process');
      try {
        // Use the appropriate service based on call type with the AbortController signal
        let response: any;
        if (type === 'intake' || !type) {
          const endCallPromise = realtimeService.endCall({
            role_id: roleId,
            transcript_id: sessionId.current || '',
            user_id: user?.uid || 'unknown',
            is_enrichment: isEnrichment,
            process_method: 'enrichment_agent',
            agent_type: 'intake_agent'
          }, abortController.signal);

          // Set a timeout to avoid waiting too long - increased to 3000ms
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('End call timeout')), 3000)
          );

          // Try to process but give up after timeout - don't throw
          await Promise.race([endCallPromise, timeoutPromise])
            .then(result => {
              response = result;
              console.log('[VideoCall] Intake call end API call completed:', response);
            })
            .catch(apiError => {
              console.warn('[VideoCall] API call failed, but continuing with navigation:', apiError);
              // Continue with navigation even if this fails
            });
        } else if (type === 'interview') {
          const endInterviewPromise = realtimeService.endInterview({
            role_id: roleId,
            transcript_id: sessionId.current || '',
            user_id: user?.uid || 'unknown'
          }, abortController.signal);

          // Set a timeout to avoid waiting too long - increased to 3000ms
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('End interview timeout')), 3000)
          );

          // Try to process but give up after timeout - don't throw
          await Promise.race([endInterviewPromise, timeoutPromise])
            .then(result => {
              response = result;
              console.log('[VideoCall] Interview end API call completed:', response);
            })
            .catch(apiError => {
              console.warn('[VideoCall] API call failed, but continuing with navigation:', apiError);
              // Continue with navigation even if this fails
            });
        }
      } catch (apiError) {
        console.warn('[VideoCall] API call failed, but continuing with navigation:', apiError);
        // Continue with navigation even if API calls fail
      } finally {
        // When everything is finished, navigate away
        console.log('[VideoCall] Redirecting to role details page (fallback)');
        // For internal users, redirect to role details page
        cleanup();
        // Use window.location.href for a hard redirect to force a full page reload
        window.location.href = `/roles/${roleId}`;
      }
    } catch (error) {
      console.error('[VideoCall] Unhandled error in handleEndCall:', error);
      // Clean up flag and redirect anyway
      cleanup();
      setExiting(false);

      // CRITICAL FIX: Force check localStorage for public interview flag as a backup
      const isPublicFlow = isPublicInterviewFlow();

      // Handle redirect based on interview type
      if (isPublicFlow) {
        // CRITICAL: Clear ALL sessionStorage to prevent authenticated API calls
        sessionStorage.clear();

        const appId = sessionData?.application_id || localStorage.getItem('applicationId') || localStorage.getItem('lastApplicationId');
        const thankYouUrl = `/instant-interview/${roleId}/thank-you${appId ? `?applicationId=${appId}` : ''}`;
        console.log('[VideoCall] Error fallback: Redirecting to thank you page:', thankYouUrl);

        // Use window.location for hard redirect to avoid Next.js router caching issues
        window.location.href = thankYouUrl;
      } else if (roleId) {
        // Use window.location.href for a hard redirect to force a full page reload
        window.location.href = `/roles/${roleId}`;
      } else {
        // Use window.location.href for consistency
        window.location.href = '/dashboard';
      }
    }
  };

  const handleCameraChange = (newCameraId: string) => {
    setSelectedCameraId(newCameraId);
  };

  // Add logging when render happens in public interview mode
  useEffect(() => {
    if (isPublicInterviewFlow()) {
      console.log('VideoCall: Running in public interview mode', {
        hasSessionData: !!sessionData,
        roleId,
        templateId,
        initialSessionData: sessionData ? {
          session_id: sessionData.session_id,
          has_transcript_id: !!sessionData.transcript_id,
          role_id: sessionData.role_id,
          template_id: sessionData.template_id,
          is_public: sessionData.is_public
        } : null,
        hasConnectionFailureCallback: !!onConnectionFailure
      });

      // Add additional debug logging for the session data
      if (sessionData?.session_id) {
        console.log('Session details for debugging:', {
          sessionId: sessionData.session_id,
          secretLength: sessionData.client_secret?.value?.length || 0,
          expiresIn: sessionData.client_secret?.expires_at ?
            sessionData.client_secret.expires_at - Math.floor(Date.now()/1000) : 'unknown'
        });
      }

      // Validate session token expiration - essential for public interview mode
      if (sessionData?.client_secret?.expires_at) {
        const expiresAt = sessionData.client_secret.expires_at;
        const currentTime = Math.floor(Date.now() / 1000);
        const timeRemaining = expiresAt - currentTime;

        console.log(`VideoCall: Session token expires in ${timeRemaining} seconds`);

        // If the token is already expired or will expire very soon, report failure immediately
        if (timeRemaining < 30) {
          console.error('VideoCall: Session token will expire too soon!', {
            expiresAt,
            currentTime,
            timeRemaining
          });

          // Report connection failure due to expiring token - don't even try to connect
          if (!connectionFailureReported.current && onConnectionFailure) {
            console.log('Reporting token expiration as connection failure');
            connectionFailureReported.current = true;
            setConnectionStatus('error');

            toast({
              title: "Session Expired",
              description: "Your session has expired. Please refresh the page to get a new interview session.",
              variant: "destructive",
            });

            // Call the failure callback
            onConnectionFailure();

            // Don't attempt to establish WebRTC connection with expired token
            return;
          }
        }
      }
    }
  }, [isPublicInterview, sessionData, roleId, templateId, onConnectionFailure, connectionFailureReported, toast]);

  // Update connection status when messages are received
  useEffect(() => {
    // If we have messages and we're still in "connecting" state, update to "connected"
    if (messages.length > 0 && connectionStatus === 'connecting') {
      console.log('Received messages, setting connection status to connected');
      setConnectionStatus('connected');
    }
  }, [messages, connectionStatus]);

  // Add new function to toggle audio mute state
  const handleToggleMute = () => {
    // Toggle the UI state
    const newMutedState = !isMuted;
    setIsMuted(newMutedState);

    // Apply mute to our local audio track reference
    if (audioStreamRef.current) {
      audioStreamRef.current.getAudioTracks().forEach(track => {
        // This is the correct way to mute an audio track in WebRTC
        track.enabled = !newMutedState;
        console.log(`Local audio track ${track.label} ${newMutedState ? 'muted' : 'unmuted'}`);
      });
    }

    // Find and mute any additional audio tracks from the peer connection
    // This ensures all audio capture devices are properly muted
    const audioElements = document.querySelectorAll<HTMLMediaElement>('audio');
    audioElements.forEach(audio => {
      if (audio.srcObject instanceof MediaStream) {
        audio.srcObject.getAudioTracks().forEach(track => {
          // Apply mute state to all audio tracks
          track.enabled = !newMutedState;
          console.log(`Audio element track ${track.label} ${newMutedState ? 'muted' : 'unmuted'}`);
        });
      }
    });

    // Get all active media streams in the document
    const mediaElements = document.querySelectorAll<HTMLMediaElement>('video, audio');
    mediaElements.forEach(element => {
      if (element.srcObject instanceof MediaStream) {
        element.srcObject.getAudioTracks().forEach(track => {
          track.enabled = !newMutedState;
          console.log(`Media element track ${track.label} ${newMutedState ? 'muted' : 'unmuted'}`);
        });
      }
    });

    // Force a direct mute on all audio tracks globally
    try {
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
          const tracks = stream.getAudioTracks();
          tracks.forEach(track => {
            track.enabled = !newMutedState;
            console.log(`Forced media track ${track.label} ${newMutedState ? 'muted' : 'unmuted'}`);
          });

          // Release the stream after setting state
          setTimeout(() => {
            tracks.forEach(track => track.stop());
          }, 100);
        })
        .catch(err => console.error('Error accessing microphone for mute:', err));
    } catch (err) {
      console.error('Error in forced mute:', err);
    }
  };

  const handleToggleCamera = () => {
    // Update camera state
    setIsCameraOff(prev => !prev);

    // If there's a videoStreamRef, update its tracks rather than recreating the stream
    if (videoStreamRef.current) {
      const videoTracks = videoStreamRef.current.getVideoTracks();
      videoTracks.forEach(track => {
        // Toggle track enabled state
        track.enabled = isCameraOff; // Use current isCameraOff state (which will be the opposite after state updates)
      });

      console.log(`Camera toggled to ${isCameraOff ? 'ON' : 'OFF'}`);

      // If we're enabling the camera (current value of isCameraOff is true, will change to false)
      console.log('Restoring camera feed to video elements');
    } else if (isCameraOff) {
      // If we're turning camera ON and no stream exists, start camera access
      console.log('Starting new camera stream');
      // Camera will be initialized by VideoSession component
    }
  };

  // Note: The key prop on the video container helps force remount when reconnectAttempt changes

  // Add dedicated useEffect for public interview flow to ensure proper cleanup
  useEffect(() => {
    // Check for multiple signals of public interview flow
    const urlIsPublic = typeof window !== 'undefined' &&
      window.location.pathname.includes('/instant-interview/');

    const isPublicFlow = isPublicInterviewFlow() || urlIsPublic;

    if (isPublicFlow) {
      console.log('[VideoCall] Running in public interview mode - setting up public context', {
        propsIsPublic: isPublicInterview,
        localStorageIsPublic: localStorage.getItem('isPublicInterview') === 'true',
        urlIsPublic,
        pathname: window.location.pathname
      });

      // Ensure the localStorage flag is set regardless of how we detected it
      localStorage.setItem('isPublicInterview', 'true');

      // Critical: Clear ALL sessionStorage that might trigger authenticated API calls
      sessionStorage.clear();

      // Interval to check for and clear any sessionStorage flags that might cause auth API calls
      const intervalId = setInterval(() => {
        // These flags would trigger authenticated API calls in the role details page
        const keysToCheck = [
          'coming_from_call',
          'call_role_id',
          'call_transcript_id',
          'auto_enrich_role'
        ];

        // Check and remove any problematic sessionStorage keys
        let found = false;
        keysToCheck.forEach(key => {
          if (sessionStorage.getItem(key)) {
            console.log(`[VideoCall] Detected and removing auth flag: ${key}`);
            sessionStorage.removeItem(key);
            found = true;
          }
        });

        if (found) {
          console.log('[VideoCall] Cleared auth-related sessionStorage flags');
        }
      }, 3000);

      return () => {
        // Clean up on unmount
        clearInterval(intervalId);
        console.log('[VideoCall] Public interview component unmounting - cleaning up');
      };
    }

    // Add an empty cleanup function for when isPublicFlow is false
    return () => {};
  }, [isPublicInterview]);

  useEffect(() => {
    // Handle connection status changes
    if (connectionStatus === 'error') {
      toast({
        title: 'Connection Error',
        description: 'Connection error occurred. Please try again.',
        variant: 'destructive'
      });
    }
    // Add toast and type as dependencies
  }, [connectionStatus, toast]);

  return (
    <div className={cn("flex flex-col h-full w-full relative bg-black px-0 py-6", className)}>
      <div className="flex-1 flex flex-col relative overflow-hidden pt-[8px]">
        {isLoadingSession ? (
          <div className="flex items-center justify-center h-full w-full">
            <div className="flex flex-col items-center space-y-4">
              <div className="animate-pulse text-xl font-medium">
                Initializing session...
              </div>
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          </div>
        ) : sessionData ? (
          <>
            <div className="video-container absolute inset-0" key={`video-${sessionData?.session_id}-${reconnectAttempt || 0}`}>
              <VideoSession
                sessionId={sessionData.session_id}
                type={'interviewer'}
                isCameraOff={isCameraOff}
                isMuted={isMuted}
                selectedCameraId={selectedCameraId}
                videoStreamRef={videoStreamRef}
                onMessagesUpdate={handleMessagesUpdate as any}
                onFunctionCall={handleFunctionCall as any}
                sessionData={sessionData}
                connectionStatus={connectionStatus}
              />

              {/* Transcript Panel - No auth check needed since we handle that internally */}
              <TranscriptPanel
                ref={transcriptPanelRef}
                isOpen={isTranscriptOpen}
                onClose={() => setIsTranscriptOpen(false)}
                messages={messages}
              />
            </div>

            <ControlPanel
              isMuted={isMuted}
              isCameraOff={isCameraOff}
              isTranscriptOpen={isTranscriptOpen}
              onToggleMute={handleToggleMute}
              onToggleCamera={handleToggleCamera}
              onToggleTranscript={() => setIsTranscriptOpen(!isTranscriptOpen)}
              onSettings={() => setIsSettingsOpen(true)}
              onEnd={handleExit}
            />

            {/* Exit overlay */}
            {exiting && (
              <div className="absolute inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-md">
                <div className="text-center">
                  <div className="mb-4 flex justify-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                  </div>
                  <p className="text-2xl font-medium text-white mb-2">Ending call...</p>
                  <p className="text-sm text-slate-300">Redirecting you to role details</p>
                </div>
              </div>
            )}
          </>
        ) : sessionError ? (
          <div className="flex items-center justify-center h-full w-full">
            <div className="text-red-500 text-center max-w-md p-4">
              <h3 className="text-xl font-semibold mb-2">Session Error</h3>
              <p>{sessionError}</p>
              <Button className="mt-4" onClick={() => router.back()}>
                Go Back
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full w-full">
            <div className="text-red-500">Failed to load session</div>
          </div>
        )}
      </div>

      {isSettingsOpen && (
        <SettingsMenu
          onClose={() => setIsSettingsOpen(false)}
          selectedCameraId={selectedCameraId}
          onCameraChange={handleCameraChange}
        />
      )}

      {/* Add global styles to ensure proper layout */}
      <style jsx global>{`
        html, body {
          height: 100%;
          overflow: hidden;
          margin: 0;
          padding: 0;
          background: black;
        }

        .video-container {
          position: absolute;
          top: 0;
          left: 4px;
          right: 4px;
          bottom: 0;
          width: calc(100% - 8px);
          height: 100%;
          z-index: 10;
        }

        /* Ensure controls and transcript panel stay on top */
        .transcript-panel {
          z-index: 40; /* Lower than agent visualizer's z-index of 50 */
          pointer-events: auto;
        }

        /* Create a clearance in the transcript panel for the agent visualizer */
        @media (max-width: 768px) {
          .transcript-panel {
            left: 4px !important;
            right: 4px !important;
            width: auto !important;
            max-width: none !important;
            margin-top: 80px !important; /* Create space for agent visualizer */
          }
        }
      `}</style>
    </div>
  );
};