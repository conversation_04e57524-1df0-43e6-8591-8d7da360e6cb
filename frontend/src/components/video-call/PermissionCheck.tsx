'use client';

import React, { useState, useEffect } from 'react';
import { Checkbox, Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, Button, Alert, AlertDescription } from '@/components/ui';
import { Video, CheckCircle2, XCircle, Volume2, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface PermissionCheckProps {
  onPermissionsGranted: () => void;
  className?: string;
}

export function PermissionCheck({ onPermissionsGranted, className }: PermissionCheckProps) {
  const [micPermission, setMicPermission] = useState<'unchecked' | 'checking' | 'granted' | 'denied'>('unchecked');
  const [cameraPermission, setCameraPermission] = useState<'unchecked' | 'checking' | 'granted' | 'denied'>('unchecked');
  const [micStream, setMicStream] = useState<MediaStream | null>(null);
  const [cameraStream, setCameraStream] = useState<MediaStream | null>(null);
  const { toast } = useToast();

  // Clean up media streams when component unmounts
  useEffect(() => {
    return () => {
      if (micStream) {
        micStream.getTracks().forEach(track => track.stop());
      }
      if (cameraStream) {
        cameraStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [micStream, cameraStream]);

  // Check microphone permission
  const checkMicPermission = async () => {
    try {
      setMicPermission('checking');

      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setMicStream(stream);
      setMicPermission('granted');

      toast({
        title: 'Microphone access granted',
        description: 'Your microphone is working properly.',
      });

      return true;
    } catch (error) {
      console.error('Error accessing microphone:', error);
      setMicPermission('denied');

      toast({
        title: 'Microphone access denied',
        description: 'Please allow microphone access in your browser settings and try again.',
        variant: 'destructive',
      });

      return false;
    }
  };

  // Check camera permission
  const checkCameraPermission = async () => {
    try {
      setCameraPermission('checking');

      // Request camera access
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      setCameraStream(stream);
      setCameraPermission('granted');

      toast({
        title: 'Camera access granted',
        description: 'Your camera is working properly.',
      });

      return true;
    } catch (error) {
      console.error('Error accessing camera:', error);
      setCameraPermission('denied');

      toast({
        title: 'Camera access denied',
        description: 'Please allow camera access in your browser settings and try again.',
        variant: 'destructive',
      });

      return false;
    }
  };

  // Handle checkbox change for microphone
  const handleMicCheckboxChange = async (checked: boolean) => {
    if (checked) {
      await checkMicPermission();
    } else {
      // If unchecking, stop the stream
      if (micStream) {
        micStream.getTracks().forEach(track => track.stop());
        setMicStream(null);
      }
      setMicPermission('unchecked');
    }
  };

  // Handle checkbox change for camera
  const handleCameraCheckboxChange = async (checked: boolean) => {
    if (checked) {
      await checkCameraPermission();
    } else {
      // If unchecking, stop the stream
      if (cameraStream) {
        cameraStream.getTracks().forEach(track => track.stop());
        setCameraStream(null);
      }
      setCameraPermission('unchecked');
    }
  };

  // Check if all permissions are granted
  const allPermissionsGranted = micPermission === 'granted' && cameraPermission === 'granted';

  // Handle continue button click
  const handleContinue = () => {
    // Stop streams before continuing
    if (micStream) {
      micStream.getTracks().forEach(track => track.stop());
    }
    if (cameraStream) {
      cameraStream.getTracks().forEach(track => track.stop());
    }

    onPermissionsGranted();
  };

  return (
    <Card className={cn("max-w-md mx-auto", className)}>
      <CardHeader>
        <CardTitle>Device Permissions</CardTitle>
        <CardDescription>
          Please grant access to your microphone and camera to continue with the interview.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <Alert className="bg-amber-50 text-amber-800 border-amber-200 dark:bg-amber-900/20 dark:text-amber-300 dark:border-amber-800/30">
          <AlertTriangle className="h-4 w-4 mr-2" />
          <AlertDescription>
            Make sure you are in a quiet space with good lighting for the best interview experience.
          </AlertDescription>
        </Alert>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Checkbox
              checked={micPermission === 'granted' || micPermission === 'checking'}
              onCheckedChange={handleMicCheckboxChange}
              label="I allow access to my microphone"
              description="Required for the interviewer to hear you"
              disabled={micPermission === 'checking'}
            />
            <div className="ml-2">
              {micPermission === 'checking' && <Volume2 className="h-5 w-5 text-amber-500 animate-pulse" />}
              {micPermission === 'granted' && <CheckCircle2 className="h-5 w-5 text-emerald-500" />}
              {micPermission === 'denied' && <XCircle className="h-5 w-5 text-red-500" />}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Checkbox
              checked={cameraPermission === 'granted' || cameraPermission === 'checking'}
              onCheckedChange={handleCameraCheckboxChange}
              label="I allow access to my camera"
              description="Required for the interviewer to see you"
              disabled={cameraPermission === 'checking'}
            />
            <div className="ml-2">
              {cameraPermission === 'checking' && <Video className="h-5 w-5 text-amber-500 animate-pulse" />}
              {cameraPermission === 'granted' && <CheckCircle2 className="h-5 w-5 text-emerald-500" />}
              {cameraPermission === 'denied' && <XCircle className="h-5 w-5 text-red-500" />}
            </div>
          </div>
        </div>

        <div className="bg-slate-50 p-4 rounded-md border border-slate-200 dark:bg-slate-800/40 dark:border-slate-700/40">
          <h4 className="font-medium mb-2 text-slate-900 dark:text-slate-100">Interview Tips:</h4>
          <ul className="space-y-2 text-sm text-slate-700 dark:text-slate-300">
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Start with a friendly greeting like "Hi" when the interview begins</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Ensure you're in a quiet environment with minimal background noise</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Position yourself in a well-lit area so you're clearly visible</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">•</span>
              <span>Speak clearly and at a moderate pace</span>
            </li>
          </ul>
        </div>
      </CardContent>

      <CardFooter>
        <Button
          onClick={handleContinue}
          disabled={!allPermissionsGranted}
          className="w-full"
        >
          Continue to Interview
        </Button>
      </CardFooter>
    </Card>
  );
}
