// File: frontend/src/components/video-call/VideoSession.tsx
'use client';

import React, { useState, useEffect, useRef, MutableRefObject, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { AgentVisualizer } from './AgentVisualizer';
import { useWebRTC } from '@/hooks/useWebRTC';
import { Message } from '@/types';
import { getOpenAISessionId } from '@/lib/firebase/public-interview';
import { realtimeService } from '@/services/realtime';
import { ConnectionStatus } from './ConnectionStatus';

// Define a more specific type for browser speech recognition
interface SpeechRecognitionEvent extends Event {
  resultIndex: number;
  results: {
    [key: number]: {
      isFinal: boolean;
      [key: number]: {
        transcript: string;
      };
    };
    length: number;
  };
  error?: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start: () => void;
  stop: () => void;
  abort: () => void;
  onstart: (event: Event) => void;
  onresult: (event: SpeechRecognitionEvent) => void;
  onerror: (event: Event & { error?: string }) => void;
  onend: (event: Event) => void;
}

type BrowserSpeechRecognition = SpeechRecognition;

interface VideoSessionProps {
  isCameraOff: boolean;
  isMuted?: boolean;
  selectedCameraId: string;
  videoStreamRef: MutableRefObject<MediaStream | null>;
  onMessagesUpdate: (messages: Message[]) => void | Promise<void>;
  onFunctionCall?: (functionName: string, args: Record<string, unknown>) => void;
  onConnectionStateChange?: (state: 'disconnected' | 'connecting' | 'connected' | 'error') => void;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  sessionId?: string;
  type?: 'intake' | 'screening' | 'interview' | 'interviewer';
  sessionData?: {
    session_id: string;
    client_secret: {
      value: string;
      expires_at: number;
    };
    transcript_id?: string;
    role_id?: string;
    template_id?: string;
    is_public?: boolean;
  };
}

export const VideoSession = React.forwardRef<HTMLDivElement, VideoSessionProps>(function VideoSession(
  { isCameraOff, isMuted, selectedCameraId, videoStreamRef, onMessagesUpdate, onFunctionCall, sessionData, connectionStatus, sessionId, type },
  ref
) {
  const [isAgentMainView, setIsAgentMainView] = useState(false);
  const mainVideoRef = useRef<HTMLVideoElement>(null);
  const miniVideoRef = useRef<HTMLVideoElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const currentResponseRef = useRef<{ id: string; content: string } | null>(null);
  const userSpeakingRef = useRef<boolean>(false);
  const messagesRef = useRef<Message[]>([]);
  const reconnectAttemptRef = useRef<number>(0);
  const maxReconnectAttempts = 3;
  const [connectionState, setConnectionState] = useState<'initializing' | 'connecting' | 'connected' | 'reconnecting' | 'failed'>('initializing');
  const lastReconnectTimeRef = useRef<number>(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pendingTranscriptionsRef = useRef<{[key: string]: boolean}>({});
  const latestItemIdRef = useRef<string | null>(null);

  // Add speech recognition
  const recognitionRef = useRef<BrowserSpeechRecognition | null>(null);
  const localTranscriptRef = useRef<string>('');
  const currentItemIdRef = useRef<string | null>(null);

  // Add initialization timestamp for tracking
  const initTimestampRef = useRef<number>(Date.now());

  // Debug counter for monitoring connection issues
  const connectAttemptsRef = useRef<number>(0);

  // Define valid function names

  // Function to check if a function name is valid

  // Create speech recognition when component mounts
  useEffect(() => {
    // Use this variable to access browser-specific APIs
    const _window = window as typeof window & {
      SpeechRecognition?: new () => SpeechRecognition;
      webkitSpeechRecognition?: new () => SpeechRecognition;
    };
    const SpeechRecognitionAPI = _window.SpeechRecognition || _window.webkitSpeechRecognition;

    if (SpeechRecognitionAPI) {
      try {
        const recognition = new SpeechRecognitionAPI();

        // Configure recognition with better settings for short utterances
        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.lang = 'en-US';

        // Set up event handlers
        recognition.onstart = () => {
          console.log('Browser speech recognition started');
          userSpeakingRef.current = true;
          localTranscriptRef.current = '';
        };

        recognition.onresult = (event: SpeechRecognitionEvent) => {
          let interimTranscript = '';
          let finalTranscript = '';

          // Process the results
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
              finalTranscript += transcript + ' ';
            } else {
              interimTranscript += transcript;
            }
          }

          // If we have a final transcript, use it
          if (finalTranscript) {
            localTranscriptRef.current = finalTranscript.trim();
            console.log('Final local transcript:', localTranscriptRef.current);

            // Find pending user message to update with local transcript
            updateUserMessageWithLocalTranscript(localTranscriptRef.current);
          } else if (interimTranscript) {
            // Store interim results in case that's all we get
            localTranscriptRef.current = interimTranscript.trim();

            // Also update any active pending message with the interim transcript
            // This makes short utterances visible immediately
            type MessageWithIndex = Message & { index: number };

            const pendingMessages = messagesRef.current
              .map((msg, index) => ({ ...msg, index } as MessageWithIndex))
              .filter(msg =>
                msg.status === 'pending' &&
                msg.role === 'user' &&
                (msg.content === '...' || msg.content === '[Audio received]')
              );

            if (pendingMessages.length > 0 && interimTranscript.length > 0) {
              // Get the most recent pending message
              const indexToUpdate = pendingMessages[pendingMessages.length - 1].index;
              const updatedMessages = [...messagesRef.current];

              // Update with interim transcript for immediate feedback
              updatedMessages[indexToUpdate] = {
                ...updatedMessages[indexToUpdate],
                content: interimTranscript,
                // Keep status as pending since this is interim
                status: 'pending' as const
              };

              // Update the messages
              updateMessagesWithDeduplication(updatedMessages);
            }
          }
        };

        recognition.onerror = (event: Event & { error?: string }) => {
          console.error('Speech recognition error:', event.error);

          // Reset the speaking state if there's an error
          if (event.error === 'aborted' || event.error === 'no-speech') {
            userSpeakingRef.current = false;
          }
        };

        recognition.onend = () => {
          console.log('Browser speech recognition ended');
          userSpeakingRef.current = false;

          // When recognition ends, check if we have a final transcript to use
          if (localTranscriptRef.current) {
            updateUserMessageWithLocalTranscript(localTranscriptRef.current);
          }
        };

        recognitionRef.current = recognition;
        console.log('Speech recognition initialized successfully');
      } catch (err) {
        console.error('Failed to initialize speech recognition:', err);
      }
    } else {
      console.log('Speech Recognition is not supported in this browser');
    }

    // Cleanup
    return () => {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
        } catch (err) {
          // Ignore errors when stopping
        }
      }
    };
  }, []);

  // Helper function to update a user message with local transcript
  const updateUserMessageWithLocalTranscript = (transcript: string) => {
    if (!transcript) return;

    type MessageWithIndex = Message & { index: number };

    // Find pending user message to update
    const pendingMessages = messagesRef.current
      .map((msg, index) => ({ ...msg, index } as MessageWithIndex))
      .filter(msg =>
        msg.status === 'pending' &&
        msg.role === 'user' &&
        (msg.content === '...' || msg.content === '[Audio received]' || msg.content === '[Audio input processed]')
      );

    if (pendingMessages.length > 0) {
      // Get the most recent pending message
      const indexToUpdate = pendingMessages[pendingMessages.length - 1].index;
      const updatedMessages = [...messagesRef.current];

      // Update with local transcript
      updatedMessages[indexToUpdate] = {
        ...updatedMessages[indexToUpdate],
        content: transcript,
        status: 'completed' as const
      };

      // Update the messages
      updateMessagesWithDeduplication(updatedMessages);
      console.log('Updated user message with browser transcript:', transcript);
    } else if (currentItemIdRef.current) {
      // If no pending messages but we have an item ID, try to find a matching message
      const messageIndex = messagesRef.current.findIndex(msg =>
        msg.id === currentItemIdRef.current ||
        msg.metadata?.itemId === currentItemIdRef.current
      );

      if (messageIndex >= 0) {
        const currentContent = messagesRef.current[messageIndex].content;

        // Only update if the message has placeholder content
        if (currentContent === '...' ||
            currentContent === '[Audio received]' ||
            currentContent === '[Audio input processed]') {

          const updatedMessages = [...messagesRef.current];
          updatedMessages[messageIndex] = {
            ...updatedMessages[messageIndex],
            content: transcript,
            status: 'completed' as const
          };

          updateMessagesWithDeduplication(updatedMessages);
          console.log('Updated existing message with browser transcript');
        }
      }
    }
  };

  // Helper function to start speech recognition
  const startSpeechRecognition = (itemId: string | null) => {
    if (recognitionRef.current) {
      try {
        // Check if recognition is already running to prevent "already started" errors
        if (userSpeakingRef.current) {
          console.log('Speech recognition already running, skipping start');
          return;
        }

        // Store the item ID for later use
        currentItemIdRef.current = itemId;
        localTranscriptRef.current = '';

        // Start recognition
        recognitionRef.current.start();
        console.log('Started browser speech recognition');
      } catch (err) {
        console.error('Error starting speech recognition:', err);

        // If we get "already started" error, just update the itemId
        if (err instanceof DOMException && err.name === 'InvalidStateError') {
          console.log('Recognition already started, updating itemId only');
          currentItemIdRef.current = itemId;
        }
      }
    }
  };

  // Helper function to stop speech recognition
  const stopSpeechRecognition = () => {
    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
        console.log('Stopped browser speech recognition');
      } catch (err) {
        console.error('Error stopping speech recognition:', err);
      }
    }
  };

  // Define validation schemas for function arguments

  // Utility function to sanitize JSON strings

  // Log session data for debugging
  useEffect(() => {
    console.log('[VideoSession] Component mounted:', {
      sessionData: sessionData ? {
        hasSession: !!sessionData,
        hasSessionId: !!sessionData?.session_id,
        hasClientSecret: !!sessionData?.client_secret?.value,
        roleId: sessionData?.role_id || 'not provided',
        type: 'type' in sessionData ? sessionData.type : 'unknown'
      } : 'no session data',
      sessionId,
      type,
      initTime: new Date(initTimestampRef.current).toISOString(),
      isCameraOff,
      isMuted
    });

    if (sessionData) {
      console.log('[VideoSession] Received sessionData:', {
        session_id: sessionData.session_id,
        has_client_secret: !!sessionData.client_secret,
        transcript_id: sessionData.transcript_id,
        role_id: sessionData.role_id,
        template_id: sessionData.template_id
      });

      // Validate session data
      if (!sessionData.session_id) {
        console.error('[VideoSession] Missing session_id in sessionData');
      }
      if (!sessionData.client_secret || !sessionData.client_secret.value) {
        console.error('[VideoSession] Missing client_secret in sessionData');
      }

      // Validate token expiration
      if (sessionData.client_secret && sessionData.client_secret.expires_at) {
        const expiresAt = sessionData.client_secret.expires_at;
        const nowSeconds = Math.floor(Date.now() / 1000);
        const timeRemaining = expiresAt - nowSeconds;

        console.log(`[VideoSession] Token expires in ${timeRemaining} seconds`);

        if (timeRemaining < 60) {
          console.error('[VideoSession] Token will expire too soon', {
            expiresAt,
            timeRemaining
          });
        }
      }
    } else {
      console.log('[VideoSession] No sessionData provided');
    }
  }, [sessionData, isCameraOff, isMuted]);

  // New helper function to update messages with deduplication
  const updateMessagesWithDeduplication = (messages: Message[]) => {
    // Store the messages in the local ref
    messagesRef.current = messages;

    // Create a new array to avoid modifying the original
    const uniqueMessages = [...messages];

    // Create a map to deduplicate by ID
    const messageMap = new Map<string, Message>();

    // Track any agent transcript messages being processed
    const agentTranscriptMessages = uniqueMessages.filter(
      msg => msg.role === 'Recruiva'
    );
    const hasAgentTranscripts = agentTranscriptMessages.length > 0;

    // Log if we have agent transcripts for debugging
    if (hasAgentTranscripts && agentTranscriptMessages.length > 0) {
      console.log(`Processing ${agentTranscriptMessages.length} agent transcript messages`);
    }

    // Process messages in order, keeping only the latest version of each ID
    uniqueMessages.forEach(msg => {
      if (!messageMap.has(msg.id)) {
        messageMap.set(msg.id, msg);
      } else {
        // If we already have a message with this ID, keep the one with completed status
        // or the one with the most recent timestamp
        const existingMsg = messageMap.get(msg.id)!;

        // For agent transcript messages, always prefer the newer/longer content
        if (msg.role === 'Recruiva') {
          // If new message has more content, or is completed vs pending, prefer it
          if (msg.content.length > existingMsg.content.length ||
              (msg.status === 'completed' && existingMsg.status !== 'completed')) {
            messageMap.set(msg.id, msg);
          }
        } else {
          // For other messages, use normal rules
          if (msg.status === 'completed' && existingMsg.status !== 'completed') {
            messageMap.set(msg.id, msg);
          } else if (
            msg.timestamp > existingMsg.timestamp &&
            !(existingMsg.status === 'completed' && msg.status !== 'completed')
          ) {
            messageMap.set(msg.id, msg);
          }
        }
      }
    });

    // Convert back to array, preserving order by timestamp
    const dedupedMessages = Array.from(messageMap.values()).sort((a, b) => {
      const timeA = typeof a.timestamp === 'string' ? Date.parse(a.timestamp) : a.timestamp;
      const timeB = typeof b.timestamp === 'string' ? Date.parse(b.timestamp) : b.timestamp;
      return timeA - timeB;
    });

    // Print debug information
    if (dedupedMessages.length !== messages.length) {
      console.log(`Deduplication removed ${messages.length - dedupedMessages.length} duplicate messages`);
    }

    // Verify agent transcript messages are retained
    if (hasAgentTranscripts) {
      const remainingAgentTranscripts = dedupedMessages.filter(
        msg => msg.role === 'Recruiva'
      );

      console.log(`After deduplication: ${remainingAgentTranscripts.length} agent transcript messages remain`);
    }

    // Call the provided callback with deduplicated messages
    onMessagesUpdate(dedupedMessages);
  };

  const { error, connect, disconnect, peerConnectionRef } = useWebRTC({
    sessionData,
    muted: isMuted,
    onMessage: (event) => {
      // Add detailed logging for all events to debug transcription issues
      console.log('WebRTC Event Received:', {
        type: event.type,
        eventId: event.event_id,
        hasTranscript: event.type === 'conversation.item.input_audio_transcription.completed' ? !!event.transcript : 'N/A',
        transcript: event.type === 'conversation.item.input_audio_transcription.completed' ? event.transcript : 'N/A',
        userSpeaking: userSpeakingRef.current,
        pendingMessagesCount: messagesRef.current.filter(msg => msg.status === 'pending' && msg.role === 'user').length
      });

      // Debug helper to check message state
      const logMessagesState = () => {
        console.log('Current Messages State:', {
          totalMessages: messagesRef.current.length,
          userMessages: messagesRef.current.filter(m => m.role === 'user').length,
          agentMessages: messagesRef.current.filter(m => m.role === 'Recruiva').length,
          pendingMessages: messagesRef.current.filter(m => m.status === 'pending').length,
          completedMessages: messagesRef.current.filter(m => m.status === 'completed').length,
          lastMessage: messagesRef.current.length > 0 ?
            {
              role: messagesRef.current[messagesRef.current.length - 1].role,
              status: messagesRef.current[messagesRef.current.length - 1].status,
              contentLength: messagesRef.current[messagesRef.current.length - 1].content.length
            } : 'none'
        });
      };

      // Handle function calls from the agent
      if (event.type === 'response.function_call_arguments.done' && onFunctionCall) {
        try {
          // Make sure we have all the required fields
          if (event.name && event.arguments) {
            console.log('Function call completed:', {
              name: event.name,
              argumentsLength: event.arguments.length
            });

            // Parse the function call arguments from JSON
            let functionArgs;
            try {
              functionArgs = JSON.parse(event.arguments);
              console.log('Parsed function arguments:', functionArgs);
            } catch (parseError) {
              console.error('Error parsing function arguments:', parseError);
              functionArgs = { error: 'Failed to parse arguments' };
            }

            // Call the onFunctionCall callback
            onFunctionCall(event.name, functionArgs);
          } else {
            console.warn('Incomplete function call data:', event);
          }
        } catch (error) {
          console.error('Error handling function call:', error);
        }
      }
      // Also handle old format function calls for backward compatibility
      else if (event.type === 'function_call' && onFunctionCall) {
        try {
          if (event.function && event.function.name && event.function.arguments) {
            console.log('Function call (old format):', {
              name: event.function.name,
              argumentsLength: event.function.arguments.length
            });

            let functionArgs;
            try {
              functionArgs = JSON.parse(event.function.arguments);
            } catch (parseError) {
              console.error('Error parsing function arguments (old format):', parseError);
              functionArgs = { error: 'Failed to parse arguments' };
            }

            onFunctionCall(event.function.name, functionArgs);
          }
        } catch (error) {
          console.error('Error handling old format function call:', error);
        }
      }

      // Handle user's speech states and transcript
      if (event.type === 'input_audio_buffer.speech_started') {
        if (!userSpeakingRef.current) {
          userSpeakingRef.current = true;

          // Create a unique ID
          const uniqueId = Date.now().toString();

          // Start browser speech recognition as a backup
          startSpeechRecognition(event.item_id);

          // Store the item_id if available
          if (event.item_id) {
            latestItemIdRef.current = event.item_id;
            pendingTranscriptionsRef.current[event.item_id] = true;
          }

          // Create a simpler pending message for the user's speech
          const newMessage: Message = {
            id: event.item_id || uniqueId,
            role: 'user',
            content: '...',
            timestamp: Date.now(),
            status: 'pending',
            metadata: {
              itemId: event.item_id,
              eventId: event.event_id
            }
          };

          // Check if we already have this message to avoid duplicates
          const messageExists = messagesRef.current.some(
            msg => (event.item_id && (msg.id === event.item_id || msg.metadata?.itemId === event.item_id))
          );

          if (!messageExists) {
            const updatedMessages = [...messagesRef.current, newMessage];
            updateMessagesWithDeduplication(updatedMessages);
            console.log(`Created pending user message with ID: ${newMessage.id}`);
          }
        } else {
          console.log('Received speech_started while already speaking');
        }
      }
      // Simplify the audio transcription completion handler
      else if (event.type === 'conversation.item.input_audio_transcription.completed') {
        userSpeakingRef.current = false;
        console.log('Received transcription event:', JSON.stringify(event, null, 2));

        const transcript = event.transcript || '';

        // Find the matching message by item_id
        let messageIndex = -1;
        if (event.item_id) {
          messageIndex = messagesRef.current.findIndex(msg =>
            msg.id === event.item_id || msg.metadata?.itemId === event.item_id);
        }

        // If no match by ID, find the most recent pending user message
        if (messageIndex < 0) {
          const pendingUserMessages = messagesRef.current
            .map((msg, index) => ({ ...msg, index }))
            .filter(msg => msg.status === 'pending' && msg.role === 'user');

          if (pendingUserMessages.length > 0) {
            // Get the most recent pending message (last in the array)
            messageIndex = pendingUserMessages[pendingUserMessages.length - 1].index;
          }
        }

        // Update the message with the transcript
        if (messageIndex >= 0) {
          // If no OpenAI transcript, use browser transcript as fallback
          const transcriptToUse = transcript || localTranscriptRef.current || '[Audio received]';

          const updatedMessages = [...messagesRef.current];
          updatedMessages[messageIndex] = {
            ...updatedMessages[messageIndex],
            content: transcriptToUse,
            status: 'completed' as const
          };
          updateMessagesWithDeduplication(updatedMessages);

          console.log(`Updated message at index ${messageIndex} with transcript: "${transcriptToUse}"`);
        } else if (transcript) {
          // Create a new message if we couldn't find an existing one and have a transcript
          const newMessage: Message = {
            id: event.item_id || `transcript_${Date.now()}`,
            role: 'user',
            content: transcript,
            timestamp: Date.now(),
            status: 'completed'
          };
          const updatedMessages = [...messagesRef.current, newMessage];
          updateMessagesWithDeduplication(updatedMessages);
          console.log(`Created new message with transcript: "${transcript}"`);
        }
      }
      // Modify speech_stopped handler for more reliability
      else if (event.type === 'input_audio_buffer.speech_stopped') {
        console.log('Speech stopped, waiting for transcription to complete', {
          eventId: event.event_id,
          itemId: event.item_id
        });

        // Stop the browser speech recognition
        stopSpeechRecognition();

        if (event.item_id) {
          // Mark this item as pending transcription
          pendingTranscriptionsRef.current[event.item_id] = true;

          // Update metadata for any matching pending messages
          const pendingUserMessages = messagesRef.current
            .map((msg, index) => ({ ...msg, index }))
            .filter(msg => msg.status === 'pending' && msg.role === 'user');

          if (pendingUserMessages.length > 0) {
            const indexToUpdate = pendingUserMessages[pendingUserMessages.length - 1].index;
            const updatedMessages = [...messagesRef.current];
            updatedMessages[indexToUpdate] = {
              ...updatedMessages[indexToUpdate],
              metadata: {
                ...(updatedMessages[indexToUpdate].metadata || {}),
                itemId: event.item_id
              }
            };
            updateMessagesWithDeduplication(updatedMessages);
          }
        }

        // Use a shorter timeout to update pending messages with fallback content
        setTimeout(() => {
          userSpeakingRef.current = false;

          // Find all pending ellipsis messages and update them
          const pendingEllipsisMessages = messagesRef.current
            .map((msg, index) => ({ ...msg, index }))
            .filter(msg =>
              msg.status === 'pending' &&
              msg.role === 'user' &&
              (msg.content === '...' || msg.content === ''));

          if (pendingEllipsisMessages.length > 0) {
            console.log(`Found ${pendingEllipsisMessages.length} pending ellipsis messages that need updating`);

            const updatedMessages = [...messagesRef.current];

            pendingEllipsisMessages.forEach(msgInfo => {
              // Use browser transcript if available
              const contentToUse = localTranscriptRef.current || '[Audio received]';

              updatedMessages[msgInfo.index] = {
                ...updatedMessages[msgInfo.index],
                content: contentToUse,
                status: 'completed' as const
              };
            });

            updateMessagesWithDeduplication(updatedMessages);
          }
        }, 750); // Use a shorter timeout for quicker feedback
      }
      // Handle Recruiva's streaming transcript
      else if (event.type === 'response.audio_transcript.delta') {
        console.log('Agent transcript delta received:', {
          responseId: event.response_id,
          delta: event.delta,
          currentResponseId: currentResponseRef.current?.id,
          deltaLength: event.delta ? event.delta.length : 0
        });

        // Store current response ID and content in a ref to track the active response
        if (!currentResponseRef.current || currentResponseRef.current.id !== event.response_id) {
          // Start new response
          currentResponseRef.current = {
            id: event.response_id,
            content: event.delta || ''
          };

          // Check if message with this ID already exists
          const messageExists = messagesRef.current.some(msg => msg.id === event.response_id);

          if (!messageExists) {
            const newMessage: Message = {
              id: event.response_id,
              role: 'Recruiva',
              content: event.delta || '',
              timestamp: Date.now(),
              status: 'pending'
            };
            const updatedMessages = [...messagesRef.current, newMessage];
            updateMessagesWithDeduplication(updatedMessages);
            console.log(`Created new agent message with ID: ${event.response_id} and initial content: "${event.delta}"`);
            logMessagesState();
          }
        } else {
          // Update existing response
          currentResponseRef.current.content += event.delta || '';

          // Find and update the message with this response ID
          const messageIndex = messagesRef.current.findIndex(msg => msg.id === currentResponseRef.current?.id);

          if (messageIndex >= 0) {
            const updatedMessages = [...messagesRef.current];
            updatedMessages[messageIndex] = {
              ...updatedMessages[messageIndex],
              content: currentResponseRef.current.content
            };
            updateMessagesWithDeduplication(updatedMessages);

            // Log state occasionally
            if (Math.random() < 0.05) {  // Log about 5% of updates to avoid too much console spam
              console.log('Updated agent transcript:', {
                messageId: currentResponseRef.current.id,
                contentLength: currentResponseRef.current.content.length,
                preview: currentResponseRef.current.content.substring(0, 30) + '...',
                messageIndex: messageIndex
              });
              logMessagesState();
            }
          } else {
            // If message doesn't exist (rare case), create it
            const newMessage: Message = {
              id: currentResponseRef.current.id,
              role: 'Recruiva',
              content: currentResponseRef.current.content,
              timestamp: Date.now(),
              status: 'pending'
            };
            const updatedMessages = [...messagesRef.current, newMessage];
            updateMessagesWithDeduplication(updatedMessages);
            console.log(`Created missing agent message with ID: ${currentResponseRef.current.id}`);
            logMessagesState();
          }
        }
      }
      else if (event.type === 'response.audio_transcript.done' || event.type === 'response.audio.done') {
        console.log('Agent transcript or audio done:', {
          eventType: event.type,
          hasCurrentResponse: !!currentResponseRef.current,
          responseId: currentResponseRef.current?.id
        });

        if (currentResponseRef.current) {
          // Mark the message as completed and ensure it's synced
          const messageIndex = messagesRef.current.findIndex(msg => msg.id === currentResponseRef.current?.id);

          if (messageIndex >= 0) {
            const updatedMessages = [...messagesRef.current];
            updatedMessages[messageIndex] = {
              ...updatedMessages[messageIndex],
              status: 'completed' as const,
              content: currentResponseRef.current.content // Ensure final content is saved
            };
            updateMessagesWithDeduplication(updatedMessages);
            console.log(`Completed agent message with ID: ${currentResponseRef.current.id}, final content length: ${currentResponseRef.current.content.length}`);
            logMessagesState();
          }

          currentResponseRef.current = null;
        }
      }
    },
    onTrack: (stream) => {
      if (audioRef.current) {
        audioRef.current.srcObject = stream;
      }
    },
    onConnectionStateChange: (state) => {
      console.log(`WebRTC connection state changed: ${state}`);

      // Update the internal connection state
      if (state === 'failed' || state === 'disconnected') {
        setConnectionState('failed');
        handleConnectionError();
      } else if (state === 'connected') {
        // Reset reconnect attempts when successfully connected
        reconnectAttemptRef.current = 0;
        setConnectionState('connected');

        // Only update the parent component state for connected state
        // This is needed to fix the connection indicator
        if (onFunctionCall) {
          onFunctionCall('connection_status_update', { status: 'connected' });
        }
      } else if (state === 'connecting') {
        setConnectionState('connecting');
      }
    }
  });

  // Handle connection errors with retry logic
  const handleConnectionError = (errorEvent?: Error | Event | { message?: string; name?: string; code?: number }) => {
    // Extract error message from the error
    const getErrorMessage = (error: string) => {
      // Try to extract the user-friendly part of the error message
      const match = error.match(/^(.*?)\s*\(Details:/);
      return match ? match[1] : "Connection error occurred";
    };

    // If a specific error event was passed, log it for debugging
    if (errorEvent) {
      console.log('Connection error with details:', errorEvent);
    }

    // Check if this is a microphone access error
    const isMicrophoneError = error && (
      error.includes('microphone') ||
      error.includes('getUserMedia') ||
      error.includes('audio track')
    );

    // If this is a microphone error, show a specific error message and don't retry
    if (isMicrophoneError) {
      console.error('Microphone access error detected');

      const micErrorMessage: Message = {
        id: `error_${Date.now()}`,
        role: 'system',
        content: 'Failed to access microphone. Please check your permissions and settings, then refresh the page.',
        timestamp: Date.now(),
        status: 'failed'
      };
      const updatedMessages = [...messagesRef.current, micErrorMessage];
      updateMessagesWithDeduplication(updatedMessages);

      // Set failed state without retrying
      setConnectionState('failed');
      return;
    }

    console.log(`Connection error occurred. Attempt ${reconnectAttemptRef.current + 1} of ${maxReconnectAttempts}`);

    // Clear any existing reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Check if session might be expired
    if (sessionData?.client_secret?.expires_at) {
      const expiresAt = sessionData.client_secret.expires_at;
      const currentTime = Math.floor(Date.now() / 1000);

      if (currentTime > expiresAt) {
        console.error('Session token has expired. Cannot reconnect.');
        setConnectionState('failed');

        // Add a message to the transcript about session expiration
        const errorMessage: Message = {
          id: `error_${Date.now()}`,
          role: 'system',
          content: 'Your session has expired. Please refresh the page to start a new session.',
          timestamp: Date.now(),
          status: 'failed'
        };
        const updatedMessages = [...messagesRef.current, errorMessage];
        updateMessagesWithDeduplication(updatedMessages);

        return;
      }
    }

    // Check if we should attempt to reconnect
    if (reconnectAttemptRef.current < maxReconnectAttempts) {
      reconnectAttemptRef.current += 1;
      setConnectionState('reconnecting');

      // Calculate delay with exponential backoff
      const now = Date.now();
      const timeSinceLastReconnect = now - lastReconnectTimeRef.current;
      let delay = Math.min(1000 * Math.pow(2, reconnectAttemptRef.current - 1), 30000);

      // If we've tried recently, add a bit more delay
      if (timeSinceLastReconnect < 5000 && reconnectAttemptRef.current > 1) {
        delay = Math.max(delay, 5000);
      }

      console.log(`Attempting reconnection in ${delay/1000} seconds...`);

      // Add a helpful message to the transcript about reconnecting
      const reconnectingMessage: Message = {
        id: `reconnect_${Date.now()}`,
        role: 'system',
        content: `Connection issue detected. Attempting to reconnect... (Attempt ${reconnectAttemptRef.current}/${maxReconnectAttempts})`,
        timestamp: Date.now(),
        status: 'pending'
      };
      const updatedMessages = [...messagesRef.current, reconnectingMessage];
      updateMessagesWithDeduplication(updatedMessages);

      // Attempt to reconnect after delay
      reconnectTimeoutRef.current = setTimeout(() => {
        console.log("Executing reconnection attempt...");
        lastReconnectTimeRef.current = Date.now();

        // Clean up existing connection first
        disconnect();

        // Try to reconnect
        try {
          connect().catch(err => {
            console.error("Error during reconnection attempt:", err);
          });
        } catch (error) {
          console.error("Exception during reconnection attempt:", error);
        }
      }, delay);
    } else {
      console.error('Max reconnect attempts reached. Please refresh the page to try again.');
      setConnectionState('failed');

      // Get the last error message if available
      let errorContent = 'Connection error occurred. Please refresh the page to try again.';

      // Look for error events in the error state
      if (error) {
        errorContent = getErrorMessage(error.toString());
        errorContent += '. Please refresh the page to try again.';
      }

      // Add a message to the transcript about connection issues
      const errorMessage: Message = {
        id: `error_${Date.now()}`,
        role: 'system',
        content: errorContent,
        timestamp: Date.now(),
        status: 'failed'
      };
      const updatedMessages = [...messagesRef.current, errorMessage];
      updateMessagesWithDeduplication(updatedMessages);

      // If onFunctionCall is defined, call it with a special error function
      if (onFunctionCall) {
        onFunctionCall('connection_error', {
          error: 'Max reconnect attempts reached',
          message: 'Please refresh the page to try again'
        });
      }
    }
  };

  useEffect(() => {
    // Check for microphone access first before attempting WebRTC
    const checkMicrophoneAccess = async () => {
      try {
        console.log("[VideoSession] Checking microphone permissions...");
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        console.log("[VideoSession] Microphone access granted");

        // Release the stream right away
        stream.getTracks().forEach(track => track.stop());

        return true;
      } catch (error) {
        console.error("[VideoSession] Microphone access denied:", error);

        // Add an error message
        const errorMessage: Message = {
          id: `error_${Date.now()}`,
          role: 'system',
          content: 'Microphone access is required for this interview. Please allow microphone access and refresh the page.',
          timestamp: Date.now(),
          status: 'failed'
        };
        messagesRef.current = [errorMessage];
        onMessagesUpdate(messagesRef.current);
        setConnectionState('failed');

        return false;
      }
    };

    // Check if session data is valid before connecting
    if (!sessionData || !sessionData.session_id || !sessionData.client_secret || !sessionData.client_secret.value) {
      console.error('[VideoSession] Invalid session data:', sessionData);

      // Add an error message
      const errorMessage: Message = {
        id: `error_${Date.now()}`,
        role: 'system',
        content: 'Invalid session data. Please try again later.',
        timestamp: Date.now(),
        status: 'failed'
      };
      messagesRef.current = [errorMessage];
      onMessagesUpdate(messagesRef.current);
      setConnectionState('failed');

      // Report connection failure to parent
      if (onFunctionCall) {
        onFunctionCall('connection_status_update', { status: 'error' });
      }

      return;
    }

    console.log('[VideoSession] Session data is valid, preparing to connect...');

    // Increment connect attempts counter
    connectAttemptsRef.current++;
    console.log(`[VideoSession] Connection attempt #${connectAttemptsRef.current}`);

    // Check microphone access first, then initialize WebRTC connection
    checkMicrophoneAccess().then(hasAccess => {
      if (hasAccess) {
        // Initialize WebRTC connection when component mounts
        console.log('[VideoSession] Initializing WebRTC connection...');
        connect().catch(error => {
          console.error('[VideoSession] Error establishing initial connection:', error);
          handleConnectionError(error);
        });
      }
    });

    // Cleanup function
    return () => {
      // Clear any reconnect timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      // Disconnect WebRTC
      console.log('[VideoSession] Cleaning up and disconnecting WebRTC');
      disconnect();
    };
  }, [sessionData]);

  useEffect(() => {
    const startCamera = async () => {
      try {
        // Stop any existing tracks
        if (videoStreamRef.current) {
          videoStreamRef.current.getTracks().forEach(track => track.stop());
          videoStreamRef.current = null;

          if (mainVideoRef.current) {
            mainVideoRef.current.srcObject = null;
          }
          if (miniVideoRef.current) {
            miniVideoRef.current.srcObject = null;
          }
        }

        // Only start camera if not disabled
        if (!isCameraOff) {
          const constraints: MediaStreamConstraints = {
            video: selectedCameraId ? { deviceId: { exact: selectedCameraId } } : true,
            audio: false // Audio is handled by WebRTC
          };

          const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
          videoStreamRef.current = mediaStream;

          // Set stream for both video elements
          if (mainVideoRef.current) {
            mainVideoRef.current.srcObject = mediaStream;
          }
          if (miniVideoRef.current) {
            miniVideoRef.current.srcObject = mediaStream;
          }

          console.log('Camera started successfully');
        } else {
          console.log('Camera not started because isCameraOff is true');
        }
      } catch (error) {
        console.error('Error accessing camera:', error);
      }
    };

    startCamera();
  }, [selectedCameraId, isCameraOff]);

  // Handle view switching - ensure both video elements have the same stream
  useEffect(() => {
    if (videoStreamRef.current && !isCameraOff) {
      // When switching view, ensure both video elements display the stream
      if (mainVideoRef.current) {
        mainVideoRef.current.srcObject = videoStreamRef.current;
      }
      if (miniVideoRef.current) {
        miniVideoRef.current.srcObject = videoStreamRef.current;
      }
      console.log('Video streams synchronized between main and mini views');
    }
  }, [isAgentMainView, isCameraOff]);

  // Handle camera toggle separately to avoid recreating the stream each time
  useEffect(() => {
    // Apply camera state to the video tracks if they exist
    if (videoStreamRef.current) {
      videoStreamRef.current.getVideoTracks().forEach(track => {
        track.enabled = !isCameraOff;
      });
      console.log(`VideoSession: Camera ${isCameraOff ? 'disabled' : 'enabled'}`);

      // When toggling camera back on, ensure both video elements display the stream
      if (!isCameraOff) {
        if (mainVideoRef.current) {
          mainVideoRef.current.srcObject = videoStreamRef.current;
        }
        if (miniVideoRef.current) {
          miniVideoRef.current.srcObject = videoStreamRef.current;
        }
        console.log('Video streams restored after camera toggle');
      }
    } else if (!isCameraOff) {
      // If we don't have a stream but camera should be on, try to start it
      const startCameraIfNeeded = async () => {
        try {
          const constraints: MediaStreamConstraints = {
            video: selectedCameraId ? { deviceId: { exact: selectedCameraId } } : true,
            audio: false
          };

          const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
          videoStreamRef.current = mediaStream;

          if (mainVideoRef.current) {
            mainVideoRef.current.srcObject = mediaStream;
          }
          if (miniVideoRef.current) {
            miniVideoRef.current.srcObject = mediaStream;
          }

          console.log('Camera started on enable');
        } catch (error) {
          console.error('Error starting camera on enable:', error);
        }
      };

      startCameraIfNeeded();
    }
  }, [isCameraOff, selectedCameraId]);

  // Handle microphone mute state
  useEffect(() => {
    // Get the audio tracks from the WebRTC connection
    if (audioRef.current && audioRef.current.srcObject instanceof MediaStream) {
      const audioStream = audioRef.current.srcObject;
      audioStream.getAudioTracks().forEach(track => {
        track.enabled = !isMuted;
      });
      console.log(`VideoSession: Audio element tracks ${isMuted ? 'muted' : 'unmuted'}`);
    }

    // Additionally get all active audio input streams from the document
    // This ensures we mute audio coming from the microphone to WebRTC
    const mediaElements = document.querySelectorAll<HTMLMediaElement>('audio, video');
    let tracksMuted = 0;

    mediaElements.forEach(element => {
      if (element.srcObject instanceof MediaStream) {
        element.srcObject.getAudioTracks().forEach(track => {
          track.enabled = !isMuted;
          tracksMuted++;
        });
      }
    });

    // Get the global audio context if it exists and mute/unmute from there
    // This is a secondary approach to ensure all audio tracks are properly muted
    try {
      // Find any WebRTC peer connections in the window object
      const peerConnections = new Map<string, RTCPeerConnection>();
      for (const key in window) {
        if (Object.prototype.hasOwnProperty.call(window, key)) {
          const value = (window as any)[key];
          if (value instanceof RTCPeerConnection) {
            peerConnections.set(key, value);
          }
        }
      }

      // For each peer connection, find the audio sender and mute/unmute it
      peerConnections.forEach(pc => {
        const senders = pc.getSenders();
        senders.forEach(sender => {
          if (sender.track && sender.track.kind === 'audio') {
            sender.track.enabled = !isMuted;
            console.log(`WebRTC sender track ${sender.track.label} ${isMuted ? 'muted' : 'unmuted'}`);
            tracksMuted++;
          }
        });
      });

      console.log(`VideoSession: Total of ${tracksMuted} audio tracks ${isMuted ? 'muted' : 'unmuted'}`);
    } catch (error) {
      console.error('Error handling audio tracks:', error);
    }
  }, [isMuted]);

  // Handle mute state changes for WebRTC active connections
  useEffect(() => {
    // Find any established peer connections to update mute state directly
    if (peerConnectionRef && peerConnectionRef.current) {
      const pc = peerConnectionRef.current;
      const senders = pc.getSenders();

      // For each audio sender, update the enabled state
      senders.forEach(sender => {
        if (sender.track && sender.track.kind === 'audio') {
          sender.track.enabled = !isMuted;
          console.log(`Direct WebRTC sender track ${sender.track.label} ${isMuted ? 'muted' : 'unmuted'}`);
        }
      });
    }
  }, [isMuted]);

  // Add debug logging to inspect media tracks
  useEffect(() => {
    const logMediaTracks = () => {
      console.log('========= Media Track Debug =========');

      // Check audio ref
      if (audioRef.current && audioRef.current.srcObject instanceof MediaStream) {
        const audioTracks = audioRef.current.srcObject.getAudioTracks();
        console.log(`AudioRef tracks (${audioTracks.length}):`,
          audioTracks.map(t => ({
            id: t.id,
            kind: t.kind,
            label: t.label,
            enabled: t.enabled,
            readyState: t.readyState
          }))
        );
      } else {
        console.log('AudioRef: No srcObject or not a MediaStream');
      }

      // Check main video ref
      if (mainVideoRef.current && mainVideoRef.current.srcObject instanceof MediaStream) {
        const videoTracks = mainVideoRef.current.srcObject.getVideoTracks();
        console.log(`MainVideoRef tracks (${videoTracks.length}):`,
          videoTracks.map(t => ({
            id: t.id,
            kind: t.kind,
            label: t.label,
            enabled: t.enabled,
            readyState: t.readyState
          }))
        );
      } else {
        console.log('MainVideoRef: No srcObject or not a MediaStream');
      }

      // Check videoStreamRef
      if (videoStreamRef.current) {
        const videoTracks = videoStreamRef.current.getVideoTracks();
        console.log(`videoStreamRef tracks (${videoTracks.length}):`,
          videoTracks.map(t => ({
            id: t.id,
            kind: t.kind,
            label: t.label,
            enabled: t.enabled,
            readyState: t.readyState
          }))
        );
      } else {
        console.log('videoStreamRef: No MediaStream');
      }

      console.log('===================================');
    };

    // Initial log
    logMediaTracks();

    // Set up interval to log periodically
    const interval = setInterval(logMediaTracks, 5000);

    return () => clearInterval(interval);
  }, []);

  // Update the mainWindowClass to include transition effects
  const mainWindowClass = "h-full w-full bg-neutral-900 rounded-xl overflow-hidden relative border border-white/10 transition-all duration-500 ease-in-out transform";

  // Update the miniWindowClass to include standard dimensions and hover effects
  const miniWindowClass = "w-[275px] h-[185px] bg-neutral-900 rounded-xl overflow-hidden cursor-pointer hover:ring-2 hover:ring-primary/50 transition-all duration-500 ease-in-out hover:scale-110 hover:shadow-xl border border-white/10";

  // Create a variant without hover effects for the agent visualizer
  const miniWindowClassForAgent = "w-[275px] h-[185px] bg-neutral-900 rounded-xl overflow-hidden cursor-pointer border border-white/10 transition-all duration-500 ease-in-out";

  const renderConnectionError = () => {
    if (connectionState === 'failed') {
      return (
        <div className="absolute inset-0 flex items-center justify-center bg-black/70 z-20 backdrop-blur-sm">
          <div className="bg-slate-900 border border-red-500/30 p-6 rounded-lg max-w-md text-center">
            <div className="text-red-500 flex flex-col items-center mb-4">
              <svg className="h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <h3 className="text-xl font-medium">Connection Error</h3>
            </div>
            <p className="text-slate-300 mb-4">
              {error || 'Failed to establish connection. Please try refreshing the page.'}
            </p>
            <button
              className="px-4 py-2 bg-primary text-white rounded font-medium hover:bg-primary/90"
              onClick={() => window.location.reload()}
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    } else if (connectionState === 'reconnecting') {
      return (
        <div className="absolute inset-0 flex items-center justify-center bg-black/30 z-20 backdrop-blur-sm">
          <div className="bg-slate-900/90 border border-yellow-500/30 p-6 rounded-lg max-w-md text-center">
            <div className="text-yellow-500 flex flex-col items-center mb-4">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500 mb-4"></div>
              <h3 className="text-xl font-medium">Reconnecting...</h3>
            </div>
            <p className="text-slate-300 mb-2">
              Connection interrupted. Attempting to reconnect...
            </p>
            <p className="text-slate-400 text-sm">
              Attempt {reconnectAttemptRef.current} of {maxReconnectAttempts}
            </p>
          </div>
        </div>
      );
    }

    return null;
  };

  // Helper to save transcript periodically during public interviews
  const savePublicInterviewTranscript = useCallback(async (msgs: Message[], isPublic: boolean) => {
    if (!isPublic || !sessionData?.role_id || (!sessionData.session_id && !sessionId)) return;

    // Only save if we have at least 2 messages (some conversation has happened)
    if (msgs.length < 2) return;

    try {
      console.log(`[VideoSession] Saving public interview transcript (${msgs.length} messages)`);

      // Use only the backend API method - avoid redundant calls
      try {
        const applicationId = localStorage.getItem('applicationId') || localStorage.getItem('lastApplicationId');

        // Use our utility function to get the OpenAI-style session ID if available
        const currentSessionId = sessionData.session_id || sessionId || '';
        const currentTranscriptId = sessionData.transcript_id || sessionData.session_id || sessionId || '';
        const finalSessionId = getOpenAISessionId(currentSessionId, currentTranscriptId);

        // Store the OpenAI session ID in localStorage for future use
        if (finalSessionId.startsWith('sess_')) {
          try {
            localStorage.setItem('openai_session_id', finalSessionId);
          } catch (e) {
            // Ignore localStorage errors
          }
        }

        await realtimeService.updatePublicTranscript(
          finalSessionId,
          finalSessionId, // Use the same ID for both session and transcript
          sessionData.role_id,
          // Convert our app messages to the format expected by the API
          msgs.map(msg => ({
            role: msg.role === 'Recruiva' || msg.role === 'system' ? 'assistant' : msg.role,
            content: msg.content,
            timestamp: typeof msg.timestamp === 'string' ? parseInt(msg.timestamp) : msg.timestamp
          })),
          applicationId || undefined
        );
        console.log('[VideoSession] Public interview transcript saved via API');
      } catch (apiError) {
        console.warn('[VideoSession] API transcript save failed:', apiError);
      }
    } catch (error) {
      console.error('[VideoSession] Error saving public interview transcript:', error);
    }
  }, [sessionData, sessionId]);

  // Set up periodic transcript saving for public interviews
  useEffect(() => {
    // Check if this is a public interview
    const isPublic = sessionData?.is_public || localStorage.getItem('isPublicInterview') === 'true';
    if (!isPublic) return;

    // Save transcript every 30 seconds for public interviews (reduced frequency)
    const intervalId = setInterval(() => {
      savePublicInterviewTranscript(messagesRef.current, isPublic);
    }, 30000); // 30 seconds - reduced from 15 seconds to prevent too many requests

    return () => clearInterval(intervalId);
  }, [messagesRef, savePublicInterviewTranscript, sessionData?.is_public]);

  // Add effect handler for switching between agent and user main view
  useEffect(() => {
    console.log(`View switched: Agent is ${isAgentMainView ? 'main' : 'mini'} view`);

    // When view changes, ensure both video elements have correct stream
    if (videoStreamRef.current && !isCameraOff) {
      // Slight delay to ensure DOM is updated
      setTimeout(() => {
        if (mainVideoRef.current) {
          // Reset srcObject and then set it again to ensure proper display
          mainVideoRef.current.srcObject = null;
          mainVideoRef.current.srcObject = videoStreamRef.current;
        }
        if (miniVideoRef.current) {
          // Reset srcObject and then set it again to ensure proper display
          miniVideoRef.current.srcObject = null;
          miniVideoRef.current.srcObject = videoStreamRef.current;
        }
        console.log('Video streams refreshed after view switch');
      }, 50);
    }
  }, [isAgentMainView]);

  return (
    <div ref={ref} className="relative w-full h-full">
      <div className="h-full w-full flex flex-col">
        {/* User Video View */}
        <div
          className={cn(
            'absolute inset-0 transition-all duration-500 ease-in-out transform',
            !isAgentMainView ? 'z-10 scale-100 opacity-100' : 'z-5 scale-95 opacity-0'
          )}
          role="region"
          aria-label={`User ${type === 'interviewer' ? 'interviewer' : 'candidate'} video feed`}
        >
          {!isAgentMainView ? (
            <div className={cn(mainWindowClass, "scale-100 opacity-100")}>
              {!isCameraOff ? (
                <video
                  ref={mainVideoRef}
                  className="w-full h-full object-cover mirror transition-transform duration-500"
                  autoPlay
                  playsInline
                  muted
                />
              ) : (
                <div className="flex items-center justify-center h-full bg-gradient-to-b from-neutral-800/50 to-neutral-900">
                  <div className="w-32 h-32 rounded-full bg-neutral-800 flex items-center justify-center border border-white/10 shadow-lg">
                    <span className="text-4xl text-neutral-400">You</span>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div
              className={cn(
                miniWindowClass,
                "absolute top-4 left-4 transform transition-all duration-500 ease-in-out",
                "hover:scale-110 hover:shadow-xl scale-100 opacity-100"
              )}
              onClick={() => setIsAgentMainView(false)}
            >
              {!isCameraOff ? (
                <>
                  <video
                    ref={miniVideoRef}
                    className="w-full h-full object-cover mirror transition-transform duration-500"
                    autoPlay
                    playsInline
                    muted
                  />
                  <div className="absolute top-0 left-0">
                    <ConnectionStatus status={connectionStatus} />
                  </div>
                </>
              ) : (
                <div className="flex items-center justify-center h-full bg-gradient-to-b from-neutral-800/50 to-neutral-900">
                  <div className="w-16 h-16 rounded-full bg-neutral-800 flex items-center justify-center border border-white/10 shadow-lg">
                    <span className="text-lg text-neutral-400">You</span>
                  </div>
                  <div className="absolute top-0 left-0">
                    <ConnectionStatus status={connectionStatus} />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* AI Agent View */}
        <div
          className={cn(
            'absolute transition-all duration-500 ease-in-out transform',
            isAgentMainView
              ? 'inset-0 z-10 scale-100 opacity-100'
              : 'top-4 left-4 z-50 scale-100 opacity-100 hover:scale-110 hover:shadow-xl hover:ring-2 hover:ring-primary/50'
          )}
          style={{
            pointerEvents: 'auto',
            width: !isAgentMainView ? '275px' : 'auto',
            height: !isAgentMainView ? '185px' : 'auto',
            borderRadius: !isAgentMainView ? '12px' : '0',
            overflow: 'hidden'
          }}
          onClick={() => !isAgentMainView && setIsAgentMainView(true)}
        >
          {isAgentMainView ? (
            <div className={cn(mainWindowClass, "scale-100 opacity-100")}>
              <div className="absolute inset-0 w-full h-full flex items-center justify-center">
                <AgentVisualizer audioRef={audioRef} />
              </div>
              <div className="absolute top-4 left-4 z-10">
                <ConnectionStatus status={connectionStatus} />
              </div>
              {/* Add type indicator for debugging */}
              {type && (
                <div className="absolute bottom-4 right-4 z-10 px-2 py-1 bg-black/50 text-white/70 text-xs rounded">
                  {type} mode
                </div>
              )}
            </div>
          ) : (
            <div
              className={cn(
                miniWindowClassForAgent,
                "flex items-center justify-center relative",
                "transition-all duration-500 ease-in-out"
              )}
            >
              <div className="absolute inset-0 w-full h-full flex items-center justify-center">
                <AgentVisualizer mini audioRef={audioRef} />
              </div>
              <div className="absolute top-0 left-0 z-10">
                <ConnectionStatus status={connectionStatus} />
              </div>
            </div>
          )}
        </div>

        {/* User Mini Video on top of Agent when Agent is maximized */}
        {isAgentMainView && (
          <div
            className={cn(
              miniWindowClass,
              "absolute top-4 left-4 z-20 transform transition-all duration-500 ease-in-out",
              "hover:scale-110 hover:shadow-xl scale-100 opacity-100"
            )}
            onClick={() => setIsAgentMainView(false)}
          >
            {!isCameraOff ? (
              <>
                <video
                  ref={miniVideoRef}
                  className="w-full h-full object-cover mirror transition-transform duration-500"
                  autoPlay
                  playsInline
                  muted
                />
                <div className="absolute top-0 left-0">
                  <ConnectionStatus status={connectionStatus} />
                </div>
              </>
            ) : (
              <div className="flex items-center justify-center h-full bg-gradient-to-b from-neutral-800/50 to-neutral-900">
                <div className="w-16 h-16 rounded-full bg-neutral-800 flex items-center justify-center border border-white/10 shadow-lg">
                  <span className="text-lg text-neutral-400">You</span>
                </div>
                <div className="absolute top-0 left-0">
                  <ConnectionStatus status={connectionStatus} />
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Audio element for speech */}
      <audio
        ref={audioRef}
        id="recruiva-audio-element"
        autoPlay
      ></audio>

      {/* Add connection error UI */}
      {renderConnectionError()}

      <style jsx>{`
        .mirror {
          transform: scaleX(-1);
        }

        /* Ensure proper positioning of content */
        .relative {
          position: relative;
          width: 100%;
          height: 100%;
        }

        /* Add smooth transition for all transformable elements */
        * {
          transition-property: transform, opacity, scale;
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        }
      `}</style>
    </div>
  );
});