'use client';

import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface AgentVisualizerProps {
  mini?: boolean;
  audioRef?: React.RefObject<HTMLAudioElement>;
}

export const AgentVisualizer: React.FC<AgentVisualizerProps> = ({ mini = false, audioRef }) => {
  const bars = Array(5).fill(0);
  const [audioLevels, setAudioLevels] = useState<number[]>(bars.map(() => 0));
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const animationFrameRef = useRef<number>();
  const lastUpdateTimeRef = useRef<number>(0); // Added to throttle state updates

  // Control button diameter - scaled based on mini mode
  // Scale down heights proportionally in mini mode (approximately 50% for height)
  const minHeight = mini ? 40 : 75;
  const maxHeight = mini ? 127 : 250;

  // Array of blue and purple colors for the bars
  const barColors = [
    '#4F46E5', // Indigo-600
    '#6366F1', // Indigo-500
    '#818CF8', // Indigo-400
    '#8B5CF6', // Violet-500
    '#A78BFA', // Violet-400
  ];

  useEffect(() => {
    if (!audioRef?.current?.srcObject) return;

    const mediaStream = audioRef.current.srcObject as MediaStream;
    if (!mediaStream.active || mediaStream.getAudioTracks().length === 0) return;

    const initAudioAnalyzer = () => {
      // Cleanup any existing audio context
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        try {
          sourceRef.current?.disconnect();
          audioContextRef.current.close();
        } catch (error) {
          console.warn('Error cleaning up audio context:', error);
        }
      }

      try {
        const audioContext = new AudioContext();
        const analyser = audioContext.createAnalyser();
        analyser.fftSize = 128; // Increased for better frequency resolution
        analyser.smoothingTimeConstant = 0.3; // Increased for smoother transitions

        const source = audioContext.createMediaStreamSource(mediaStream);
        // Create a bandpass filter to focus on voice frequencies
        const filter = audioContext.createBiquadFilter();
        filter.type = 'bandpass';
        filter.frequency.value = 1000; // center frequency for voice
        filter.Q.value = 1; // quality factor

        // Connect the source through the filter to the analyser
        source.connect(filter);
        filter.connect(analyser);

        audioContextRef.current = audioContext;
        analyserRef.current = analyser;
        sourceRef.current = source;

        const dataArray = new Uint8Array(analyser.frequencyBinCount);

        const analyze = () => {
          if (!analyserRef.current) return;

          analyserRef.current.getByteFrequencyData(dataArray);

          // Calculate levels for each bar by averaging frequency ranges
          const barLevels = bars.map((_, index) => {
            const start = Math.floor((index / bars.length) * (dataArray.length * 0.5));
            const end = Math.floor(((index + 1) / bars.length) * (dataArray.length * 0.5));
            let sum = 0;
            for (let i = start; i < end; i++) {
              sum += dataArray[i];
            }
            const average = sum / (end - start);
            // Enhanced normalization with more aggressive response to voice
            return Math.pow(average / 255, 1.2);
          });

          // Throttle state updates to roughly every 40ms to reduce re-render overhead
          const now = performance.now();
          if (now - lastUpdateTimeRef.current >= 40) {
            setAudioLevels(barLevels);
            lastUpdateTimeRef.current = now;
          }

          animationFrameRef.current = requestAnimationFrame(analyze);
        };

        analyze();
      } catch (error) {
        console.error('Error initializing audio analyzer:', error);
      }
    };

    initAudioAnalyzer();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        try {
          sourceRef.current?.disconnect();
          audioContextRef.current.close();
        } catch (error) {
          console.warn('Error cleaning up audio context:', error);
        }
      }
    };
  }, [audioRef, bars]);

  return (
    <div className={cn(
      'flex items-center justify-center gap-3'
    )}>
      {bars.map((_, index) => {
        return (
          <div
            key={index}
            className={cn(
              'rounded-full transition-all duration-75 ease-out',
              mini ? 'w-[28px]' : 'w-[55px]'
            )}
            style={{
              height: `${minHeight + (audioLevels[index] * (maxHeight - minHeight))}px`,
              backgroundColor: barColors[index],
              opacity: 1 - (audioLevels[index] * 0.9),
              transformOrigin: 'bottom'
            }}
          />
        );
      })}
    </div>
  );
};