'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Select, SelectOption } from '@/components/ui/Select';


interface DeviceSelectorProps {
  kind: 'audioinput' | 'videoinput';
  onChange: (deviceId: string) => void;
  selectedDeviceId?: string;
}

export const DeviceSelector: React.FC<DeviceSelectorProps> = ({
  kind,
  onChange,
  selectedDeviceId = '',
}) => {
  const [devices, setDevices] = useState<SelectOption[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>(selectedDeviceId);

  useEffect(() => {
    // Update local state when prop changes
    setSelectedDevice(selectedDeviceId);
  }, [selectedDeviceId]);

  // Memoize loadDevices function
  const loadDevices = useCallback(async () => {
    try {
      // Request permission to access media devices
      await navigator.mediaDevices.getUserMedia({
        audio: kind === 'audioinput',
        video: kind === 'videoinput'
      });
      
      // Get all media devices
      const allDevices = await navigator.mediaDevices.enumerateDevices();
      
      // Filter devices by kind and convert to SelectOption format
      const deviceOptions: SelectOption[] = allDevices
        .filter(device => device.kind === kind)
        .map(device => ({
          value: device.deviceId,
          label: device.label || `${kind === 'audioinput' ? 'Microphone' : 'Camera'} ${devices.length + 1}`
        }));
      
      setDevices(deviceOptions);
      
      // If no device is selected and we have devices, select the first one
      if (!selectedDevice && deviceOptions.length > 0) {
        setSelectedDevice(deviceOptions[0].value);
        onChange(deviceOptions[0].value);
      }
    } catch (error) {
      console.error(`Error accessing ${kind} devices:`, error);
    }
  }, [kind, onChange, selectedDevice, devices.length]);

  useEffect(() => {
    loadDevices();
    
    // Listen for device changes
    navigator.mediaDevices.addEventListener('devicechange', loadDevices);
    
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', loadDevices);
    };
  }, [loadDevices]);

  const handleDeviceChange = (deviceId: string) => {
    setSelectedDevice(deviceId);
    onChange(deviceId);
  };

  return (
    <div>
      <Select
        value={selectedDevice}
        onValueChange={handleDeviceChange}
        options={devices}
        placeholder={kind === 'audioinput' ? "Select Microphone" : "Select Camera"}
      />
    </div>
  );
}; 