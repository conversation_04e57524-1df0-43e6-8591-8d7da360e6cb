import React from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { DeviceSelector } from './DeviceSelector';
import { cn } from '@/lib/utils';

interface SettingsMenuProps {
  onClose: () => void;
  onCameraChange: (deviceId: string) => void;
  selectedCameraId: string;
  className?: string;
}

export const SettingsMenu: React.FC<SettingsMenuProps> = ({
  onClose,
  onCameraChange,
  selectedCameraId,
  className
}) => {
  // Handle microphone changes - keeping the prop for backward compatibility
  const handleMicrophoneChange = (deviceId: string) => {
    console.log('Microphone changed to:', deviceId);
    // Microphone is handled by WebRTC, so we don't need to do anything here
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
      <div className={cn(
        "bg-background border border-border rounded-lg p-6 shadow-lg max-w-md w-full",
        className
      )}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Device Settings</h3>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-5 w-5" />
          </Button>
        </div>
        
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-2">Camera</h4>
            <DeviceSelector 
              kind="videoinput"
              onChange={onCameraChange}
              selectedDeviceId={selectedCameraId}
            />
          </div>
          
          <div>
            <h4 className="text-sm font-medium mb-2">Microphone</h4>
            <DeviceSelector 
              kind="audioinput"
              onChange={handleMicrophoneChange}
              selectedDeviceId=""
            />
          </div>
        </div>
      </div>
    </div>
  );
}; 