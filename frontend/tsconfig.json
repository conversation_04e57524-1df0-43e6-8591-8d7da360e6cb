{
  "compilerOptions": {
    // Base Options
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,

    // Path Aliases
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },

    // Additional Strict Checks
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "forceConsistentCasingInFileNames": true,

    // Module Resolution
    "allowSyntheticDefaultImports": true,
    "preserveSymlinks": true,

    // Advanced Options
    "plugins": [
      {
        "name": "next"
      }
    ],

    // Source Map Options
    "sourceMap": true,
    "inlineSources": true,

    // Additional Options
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "useDefineForClassFields": true,

    // Explicitly specify only the types we need
    "types": ["node", "react", "react-dom", "next", "jest", "@testing-library/jest-dom"],

    "noImplicitAny": false
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "src/types/**/*.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    ".next",
    "out",
    "build",
    "functions"
  ]
}
