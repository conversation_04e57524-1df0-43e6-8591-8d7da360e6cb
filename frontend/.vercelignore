# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage
.nyc_output
__tests__
*.test.*
*.spec.*

# Development files
.git
.github
.gitignore
README.md
*.md
.eslintrc*
.prettierrc*
.editorconfig
jest.config.*
tsconfig.tsbuildinfo

# Debug logs
npm-debug.log*
.pnpm-debug.log*

# Local env files
.env*.local
.env

# IDE
.idea
.vscode
*.swp
*.swo

# Misc
.DS_Store
docs
*.log

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json 