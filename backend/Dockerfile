# Use Python 3.11 slim as base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies required for WebRTC, lxml, and cryptography
RUN apt-get update && apt-get install -y \
    build-essential \
    libssl-dev \
    libffi-dev \
    git \
    libxml2-dev \
    libxslt-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Update pip first
RUN pip install --upgrade pip

# Install wheel package first
RUN pip install --no-cache-dir wheel setuptools

# Install PyYAML explicitly first
RUN pip install --no-cache-dir PyYAML==6.0.1

# Install aiohttp explicitly to ensure it's available
RUN pip install --no-cache-dir aiohttp==3.9.3

# Install python-docx directly from GitHub
RUN pip install --no-cache-dir git+https://github.com/python-openxml/python-docx.git

# Install remaining Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application
COPY . .

# Expose port
EXPOSE 8000

# Set environment variables
ENV PORT=8000
ENV PYTHONUNBUFFERED=1
ENV WORKERS=4

# Command to run the application
CMD uvicorn app.main:app --host 0.0.0.0 --port $PORT --workers $WORKERS 