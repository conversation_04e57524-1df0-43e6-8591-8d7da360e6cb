"""
Logging configuration for the application.
Filters out known protocol errors and configures structured JSON logging.
"""

import logging
import re
from typing import Optional
import json
import traceback
from datetime import datetime


class ProtocolErrorFilter(logging.Filter):
    """Filter out common HTTP protocol errors that are not actionable."""

    # Define patterns for errors to suppress
    ERROR_PATTERNS = [
        r"can't handle event type ConnectionClosed when role=SERVER and state=SEND_RESPONSE",
        r"Can't send data when our state is ERROR",
        r"disconnected\s+client",
        r"Client disconnected",
        r"unexpected event type",
        r"connection state",
        r"received ConnectionClosed",
        r"LocalProtocolError",
        r"h11\._util\.LocalProtocolError",
        r"Exception in callback H11Protocol",
        r"state is ERROR",
        r"HTTP protocol error"
    ]

    # Specific endpoints where protocol errors are expected and should be downgraded to INFO
    EXPECTED_ENDPOINTS = [
        r"/api/v1/templates/.*/generate-questions",
        r"/api/v1/templates/.*/generate-criteria",
        r"/api/v1/roles/.*/interview-transcripts",
        r"/api/v1/roles/.*/intake-transcripts",
        r"/api/v1/roles/.*/interview-setup",
        r"/api/v1/roles/.*/details",
        r"/api/v1/roles/.*/interview-questions",
        # Add any other role-related endpoints that need special handling
        r"/api/v1/roles/.*"  # Catch-all for role-specific endpoints
    ]

    def __init__(self, name: str = ""):
        super().__init__(name)
        self.patterns = [re.compile(pattern) for pattern in self.ERROR_PATTERNS]
        self.endpoint_patterns = [re.compile(pattern) for pattern in self.EXPECTED_ENDPOINTS]

    def filter(self, record: logging.LogRecord) -> bool:
        # Allow all non-error records
        if record.levelno < logging.ERROR:
            return True

        # Check if message matches any of our patterns
        if record.getMessage():
            message = record.getMessage()

            # Check if this is a protocol error
            is_protocol_error = any(pattern.search(message) for pattern in self.patterns)

            if is_protocol_error:
                # Check if this is for an endpoint where errors are expected
                for attr_name in ['path', 'url', 'endpoint']:
                    if hasattr(record, attr_name):
                        endpoint = getattr(record, attr_name)
                        if any(pattern.search(str(endpoint)) for pattern in self.endpoint_patterns):
                            # Downgrade to INFO for expected endpoints
                            record.levelno = logging.INFO
                            record.levelname = 'INFO'
                            return True

                # For other protocol errors, suppress them
                return False

        # Allow all other records
        return True


class StructuredJsonFormatter(logging.Formatter):
    """Format log records as structured JSON."""

    def format(self, record: logging.LogRecord) -> str:
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "name": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }

        # Add exception info if available
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info)
            }

        # Add extra fields if available
        if hasattr(record, "extras") and record.extras:
            log_data.update(record.extras)

        return json.dumps(log_data)


def configure_logging(
    level: int = logging.INFO,
    use_json_format: bool = False,
    filter_protocol_errors: bool = True
) -> None:
    """
    Configure application logging with optional filtering and JSON formatting.

    Args:
        level: The logging level (default: INFO)
        use_json_format: Whether to use JSON formatting (default: False)
        filter_protocol_errors: Whether to filter protocol errors (default: True)
    """
    # Clear any existing handlers
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Configure root logger
    root_logger.setLevel(level)

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)

    # Set formatter
    if use_json_format:
        formatter = StructuredJsonFormatter()
    else:
        formatter = logging.Formatter(
            "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )

    console_handler.setFormatter(formatter)

    # Add protocol error filter if requested
    if filter_protocol_errors:
        protocol_filter = ProtocolErrorFilter()
        console_handler.addFilter(protocol_filter)

        # Also filter uvicorn and asyncio loggers
        for logger_name in ["uvicorn", "uvicorn.error", "asyncio"]:
            logger = logging.getLogger(logger_name)
            logger.addFilter(protocol_filter)

    # Add handler to root logger
    root_logger.addHandler(console_handler)

    # Set specific module levels
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.error").setLevel(logging.WARNING)

    # Log configuration completion
    logging.info(f"Logging configured with level={logging.getLevelName(level)}, "
                 f"json_format={use_json_format}, filter_protocol_errors={filter_protocol_errors}")