"""Logging configuration and utilities for the application."""
import logging
import re
from typing import Dict, Any, Union

# Create a regex pattern to identify and mask JWT tokens
JWT_PATTERN = re.compile(r'Bearer\s+([A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.[A-Za-z0-9-_.+/=]*)')
TOKEN_PLACEHOLDER = "Bearer [TOKEN_MASKED]"

def mask_sensitive_data(data: Union[Dict[str, Any], str]) -> Union[Dict[str, Any], str]:
    """
    Mask sensitive data like tokens in headers or other data.
    
    Args:
        data: Headers dictionary or string containing sensitive information
        
    Returns:
        Data with sensitive information masked
    """
    if isinstance(data, str):
        # Mask JWT tokens in strings
        return JWT_PATTERN.sub(TOKEN_PLACEHOLDER, data)
    elif isinstance(data, dict):
        # Create a copy to avoid modifying the original
        masked_data = data.copy()
        
        # Mask authorization header if present
        if 'authorization' in masked_data:
            masked_data['authorization'] = JWT_PATTERN.sub(TOKEN_PLACEHOLDER, masked_data['authorization'])
        
        # Case insensitive check for Authorization header
        for key in masked_data:
            if key.lower() == 'authorization' and key != 'authorization':
                masked_data[key] = JWT_PATTERN.sub(TOKEN_PLACEHOLDER, masked_data[key])
                
        return masked_data
    
    # Return original data if not string or dict
    return data

def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: The name of the logger
        
    Returns:
        A configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Only set level if not already configured
    if not logger.handlers and not logger.parent.handlers:
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s %(levelname)s %(name)s: %(message)s',
            '%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

# Configure root logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
) 