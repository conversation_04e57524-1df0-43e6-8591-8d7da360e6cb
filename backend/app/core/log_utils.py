"""Utilities for better logging."""

from typing import Dict, Any, <PERSON>, List
import json
import logging
import re

def summarize_dict(data: Dict[str, Any], max_length: int = 100, max_items: int = 3) -> str:
    """
    Summarize a dictionary for logging purposes.

    Args:
        data: The dictionary to summarize
        max_length: Maximum length of string values
        max_items: Maximum number of items to include

    Returns:
        A summarized string representation of the dictionary
    """
    if not data:
        return "{}"

    # Create a summarized copy
    summary = {}

    # Process only the first max_items
    for i, (key, value) in enumerate(data.items()):
        if i >= max_items:
            summary["..."] = f"({len(data) - max_items} more items)"
            break

        if isinstance(value, dict):
            # Recursively summarize nested dictionaries
            summary[key] = summarize_dict(value, max_length, max_items)
        elif isinstance(value, list):
            # Summarize lists
            if not value:
                summary[key] = "[]"
            elif len(value) <= max_items:
                summary[key] = [_truncate_value(item, max_length // 2) for item in value]
            else:
                summary[key] = [_truncate_value(item, max_length // 2) for item in value[:max_items]]
                summary[key].append(f"... ({len(value) - max_items} more items)")
        else:
            # Truncate string values
            summary[key] = _truncate_value(value, max_length)

    return summary

def _truncate_value(value: Any, max_length: int) -> Any:
    """Truncate a value if it's a string and too long."""
    if isinstance(value, str) and len(value) > max_length:
        return value[:max_length] + "..."
    elif isinstance(value, dict):
        return summarize_dict(value, max_length, 2)  # Use fewer items for nested dicts
    return value

def log_data_summary(data: Union[Dict[str, Any], List[Any]], max_length: int = 100, max_items: int = 3) -> str:
    """
    Create a summary string of data for logging.

    Args:
        data: The data to summarize (dict or list)
        max_length: Maximum length of string values
        max_items: Maximum number of items to include

    Returns:
        A summarized string representation of the data
    """
    if isinstance(data, dict):
        summary = summarize_dict(data, max_length, max_items)
    elif isinstance(data, list):
        if not data:
            return "[]"
        if len(data) <= max_items:
            summary = [summarize_dict(item, max_length, max_items) if isinstance(item, dict) else _truncate_value(item, max_length)
                      for item in data]
        else:
            summary = [summarize_dict(item, max_length, max_items) if isinstance(item, dict) else _truncate_value(item, max_length)
                      for item in data[:max_items]]
            summary.append(f"... ({len(data) - max_items} more items)")
    else:
        return str(data)

    # Convert to JSON string for consistent formatting
    try:
        return json.dumps(summary, default=str)
    except:
        return str(summary)


class DataTruncationFilter(logging.Filter):
    """Filter that truncates large data objects in log messages."""

    # Patterns to identify data objects in log messages
    DATA_PATTERNS = [
        r'data: ({.*})',
        r'evaluation data: ({.*})',
        r'evaluation: ({.*})',
        r'evaluation_data: ({.*})',
        r'evaluationData: ({.*})',
        r'data summary: ({.*})',
        r'interview data: ({.*})',
        r'application data: ({.*})',
        r'application: ({.*})',
        r'interview: ({.*})',
        r'transcript: ({.*})',
        r'result: ({.*})',
        r'response: ({.*})',
        r'payload: ({.*})',
        r'body: ({.*})',
        r'document: ({.*})',
        r'doc: ({.*})',
        r'found: ({.*})',
        r'fetched: ({.*})',
        r'retrieved: ({.*})',
        r'created: ({.*})',
        r'updated: ({.*})',
        r'deleted: ({.*})',
        r'added: ({.*})',
        r'removed: ({.*})',
        r'modified: ({.*})',
        r'changed: ({.*})',
        r'processed: ({.*})',
        r'parsed: ({.*})',
        r'formatted: ({.*})',
        r'transformed: ({.*})',
        r'converted: ({.*})',
        r'normalized: ({.*})',
        r'validated: ({.*})',
        r'verified: ({.*})',
        r'checked: ({.*})',
        r'tested: ({.*})',
        r'analyzed: ({.*})',
        r'evaluated: ({.*})',
        r'assessed: ({.*})',
        r'measured: ({.*})',
        r'calculated: ({.*})',
        r'computed: ({.*})',
        r'determined: ({.*})',
        r'estimated: ({.*})',
        r'predicted: ({.*})',
        r'forecasted: ({.*})',
        r'projected: ({.*})',
        r'extrapolated: ({.*})',
        r'interpolated: ({.*})',
        r'approximated: ({.*})',
        r'guessed: ({.*})',
        r'inferred: ({.*})',
        r'deduced: ({.*})',
        r'concluded: ({.*})',
        r'decided: ({.*})',
        r'judged: ({.*})',
        r'ruled: ({.*})',
        r'adjudicated: ({.*})',
        r'arbitrated: ({.*})',
        r'mediated: ({.*})',
        r'negotiated: ({.*})',
        r'bargained: ({.*})',
        r'agreed: ({.*})',
        r'consented: ({.*})',
        r'approved: ({.*})',
        r'authorized: ({.*})',
        r'permitted: ({.*})',
        r'allowed: ({.*})',
        r'granted: ({.*})',
        r'issued: ({.*})',
        r'published: ({.*})',
        r'released: ({.*})',
        r'distributed: ({.*})',
        r'delivered: ({.*})',
        r'shipped: ({.*})',
        r'sent: ({.*})',
        r'received: ({.*})',
        r'accepted: ({.*})',
        r'rejected: ({.*})',
        r'denied: ({.*})',
        r'refused: ({.*})',
        r'declined: ({.*})',
        r'objected: ({.*})',
        r'protested: ({.*})',
        r'complained: ({.*})',
        r'disputed: ({.*})',
        r'contested: ({.*})',
        r'challenged: ({.*})',
        r'questioned: ({.*})',
        r'doubted: ({.*})',
        r'suspected: ({.*})',
        r'distrusted: ({.*})',
        r'mistrusted: ({.*})',
        r'disbelieved: ({.*})',
        r'disregarded: ({.*})',
        r'ignored: ({.*})',
        r'overlooked: ({.*})',
        r'missed: ({.*})',
        r'skipped: ({.*})',
        r'omitted: ({.*})',
        r'excluded: ({.*})',
        r'eliminated: ({.*})',
        r'removed: ({.*})',
        r'deleted: ({.*})',
        r'erased: ({.*})',
        r'wiped: ({.*})',
        r'cleared: ({.*})',
        r'cleaned: ({.*})',
        r'purged: ({.*})',
        r'purified: ({.*})',
        r'sanitized: ({.*})',
        r'disinfected: ({.*})',
        r'sterilized: ({.*})',
        r'fumigated: ({.*})',
        r'decontaminated: ({.*})',
        r'detoxified: ({.*})',
        r'neutralized: ({.*})',
        r'nullified: ({.*})',
        r'invalidated: ({.*})',
        r'voided: ({.*})',
        r'cancelled: ({.*})',
        r'annulled: ({.*})',
        r'rescinded: ({.*})',
        r'revoked: ({.*})',
        r'repealed: ({.*})',
        r'abrogated: ({.*})',
        r'abolished: ({.*})',
        r'terminated: ({.*})',
        r'discontinued: ({.*})',
        r'stopped: ({.*})',
        r'halted: ({.*})',
        r'paused: ({.*})',
        r'suspended: ({.*})',
        r'interrupted: ({.*})',
        r'disrupted: ({.*})',
        r'disturbed: ({.*})',
        r'interfered: ({.*})',
        r'intruded: ({.*})',
        r'invaded: ({.*})',
        r'trespassed: ({.*})',
        r'encroached: ({.*})',
        r'infringed: ({.*})',
        r'violated: ({.*})',
        r'breached: ({.*})',
        r'broken: ({.*})',
        r'damaged: ({.*})',
        r'harmed: ({.*})',
        r'injured: ({.*})',
        r'hurt: ({.*})',
        r'wounded: ({.*})',
        r'maimed: ({.*})',
        r'mutilated: ({.*})',
        r'disfigured: ({.*})',
        r'deformed: ({.*})',
        r'distorted: ({.*})',
        r'warped: ({.*})',
        r'twisted: ({.*})',
        r'bent: ({.*})',
        r'curved: ({.*})',
        r'arched: ({.*})',
        r'bowed: ({.*})',
        r'flexed: ({.*})',
        r'stretched: ({.*})',
        r'extended: ({.*})',
        r'expanded: ({.*})',
        r'enlarged: ({.*})',
        r'increased: ({.*})',
        r'augmented: ({.*})',
        r'amplified: ({.*})',
        r'magnified: ({.*})',
        r'intensified: ({.*})',
        r'strengthened: ({.*})',
        r'reinforced: ({.*})',
        r'fortified: ({.*})',
        r'bolstered: ({.*})',
        r'boosted: ({.*})',
        r'enhanced: ({.*})',
        r'improved: ({.*})',
        r'upgraded: ({.*})',
        r'advanced: ({.*})',
        r'progressed: ({.*})',
        r'developed: ({.*})',
        r'evolved: ({.*})',
        r'matured: ({.*})',
        r'ripened: ({.*})',
        r'aged: ({.*})',
        r'weathered: ({.*})',
        r'seasoned: ({.*})',
        r'tempered: ({.*})',
        r'hardened: ({.*})',
        r'toughened: ({.*})',
        r'strengthened: ({.*})',
        r'reinforced: ({.*})',
        r'fortified: ({.*})',
        r'bolstered: ({.*})',
        r'boosted: ({.*})',
        r'enhanced: ({.*})',
        r'improved: ({.*})',
        r'upgraded: ({.*})',
        r'advanced: ({.*})',
        r'progressed: ({.*})',
        r'developed: ({.*})',
        r'evolved: ({.*})',
        r'matured: ({.*})',
        r'ripened: ({.*})',
        r'aged: ({.*})',
        r'weathered: ({.*})',
        r'seasoned: ({.*})',
        r'tempered: ({.*})',
        r'hardened: ({.*})',
        r'toughened: ({.*})',
    ]

    def __init__(self, name: str = "", max_length: int = 100, max_items: int = 3):
        super().__init__(name)
        self.patterns = [re.compile(pattern) for pattern in self.DATA_PATTERNS]
        self.max_length = max_length
        self.max_items = max_items

    def filter(self, record: logging.LogRecord) -> bool:
        # Always allow the record through, but modify it if needed
        if record.getMessage():
            message = record.getMessage()

            # Check if message contains a data object
            for pattern in self.patterns:
                match = pattern.search(message)
                if match:
                    try:
                        # Extract the data object
                        data_str = match.group(1)

                        # Try to parse as JSON
                        try:
                            data = json.loads(data_str)
                            if isinstance(data, dict) and len(data) > self.max_items:
                                # Summarize the data
                                summary = summarize_dict(data, self.max_length, self.max_items)
                                summary_str = json.dumps(summary, default=str)

                                # Replace the data object in the message
                                new_message = message.replace(data_str, summary_str)

                                # Update the record
                                record.msg = new_message
                                record.args = ()
                        except json.JSONDecodeError:
                            # Not valid JSON, try to extract as a string representation of a dict
                            if len(data_str) > self.max_length:
                                # Truncate the string
                                truncated = data_str[:self.max_length] + "..."

                                # Replace the data object in the message
                                new_message = message.replace(data_str, truncated)

                                # Update the record
                                record.msg = new_message
                                record.args = ()
                    except Exception:
                        # If anything goes wrong, just leave the message as is
                        pass

        return True


def configure_root_logger_with_data_truncation(max_length: int = 100, max_items: int = 3) -> None:
    """
    Configure the root logger with a data truncation filter.

    Args:
        max_length: Maximum length of string values
        max_items: Maximum number of items to include in dictionaries
    """
    # Get the root logger
    root_logger = logging.getLogger()

    # Add the data truncation filter to all handlers
    data_filter = DataTruncationFilter(max_length=max_length, max_items=max_items)
    for handler in root_logger.handlers:
        handler.addFilter(data_filter)
