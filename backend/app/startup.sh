#!/bin/bash
# Startup script for the Recruiva backend

echo "Starting Recruiva backend with optimized connection settings..."

# Set environment variables for logging control
export PYTHONUNBUFFERED=1
export LOG_LEVEL=INFO

# Use Uvicorn with increased keep-alive timeout to prevent connection errors
# Also add h11 protocol settings to better handle client disconnections
exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --timeout-keep-alive 120 --no-access-log --log-level warning