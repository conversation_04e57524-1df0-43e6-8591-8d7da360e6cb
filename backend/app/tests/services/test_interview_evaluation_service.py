# File: backend/app/tests/services/test_interview_evaluation_service.py

import pytest
import json
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime

from app.services.interview_evaluation_service import InterviewEvaluationService

# Sample test data
SAMPLE_ROLE_ID = "test-role-123"
SAMPLE_INTERVIEW_ID = "test-interview-456"
SAMPLE_APPLICATION_ID = "test-application-789"
SAMPLE_USER_ID = "test-user-abc"

# Sample transcript data
SAMPLE_TRANSCRIPT = {
    "messages": [
        {"role": "interviewer", "content": "Tell me about your experience."},
        {"role": "candidate", "content": "I have 5 years of experience in software development."},
        {"role": "interviewer", "content": "What are your strengths?"},
        {"role": "candidate", "content": "I'm good at problem-solving and teamwork."}
    ],
    "template_id": "test-template-123",
    "role_id": SAMPLE_ROLE_ID,
    "application_id": SAMPLE_APPLICATION_ID
}

# Sample role data
SAMPLE_ROLE = {
    "id": SAMPLE_ROLE_ID,
    "title": "Senior Software Engineer",
    "summary": "We are looking for a senior software engineer.",
    "description": "Detailed job description for a senior software engineer position.",
    "requiredSkills": {"Python": "Expert", "JavaScript": "Intermediate"},
    "preferredSkills": {"React": "Intermediate", "AWS": "Beginner"},
    "level": "Senior",
    "yearsOfExperience": "5+"
}

# Sample application data
SAMPLE_APPLICATION = {
    "id": SAMPLE_APPLICATION_ID,
    "roleId": SAMPLE_ROLE_ID,
    "resumeText": "Sample resume text with relevant experience and skills.",
    "evaluations": [
        {
            "parsedResumeText": "Parsed resume text from the evaluation."
        }
    ]
}

# Sample template data
SAMPLE_TEMPLATE = {
    "id": "test-template-123",
    "stage": "Technical Interview",
    "stageIndex": 1,
    "questions": [
        {
            "id": "q1",
            "question": "Tell me about your experience.",
            "purpose": "To assess technical experience",
            "idealAnswerCriteria": "Should demonstrate relevant technical skills"
        },
        {
            "id": "q2",
            "question": "What are your strengths?",
            "purpose": "To assess self-awareness",
            "idealAnswerCriteria": "Should provide specific examples"
        }
    ],
    "evaluationCriteria": [
        {
            "id": "c1",
            "type": "ScoreCard",
            "competency": "Technical Skills",
            "weight": 0.4,
            "criteria": "Evaluate technical knowledge and experience",
            "description": "Look for depth of understanding and practical experience"
        },
        {
            "id": "c2",
            "type": "ScoreCard",
            "competency": "Communication",
            "weight": 0.3,
            "criteria": "Evaluate communication skills",
            "description": "Assess clarity and effectiveness of communication"
        },
        {
            "id": "c3",
            "type": "BetweenTheLines",
            "criteria": "Enthusiasm for the role",
            "description": "Assess genuine interest in the position"
        },
        {
            "id": "c4",
            "type": "Disqualifier",
            "criteria": "Dishonesty",
            "description": "Any evidence of fabricating experience"
        }
    ],
    "passRate": 0.7,
    "duration": "45 minutes",
    "customInstructions": "Focus on technical depth and problem-solving ability."
}

# Sample OpenAI response
SAMPLE_OPENAI_RESPONSE = {
    "choices": [
        {
            "message": {
                "content": """```json
{
  "evaluation_summary": {
    "candidate_name": "John Doe",
    "role": "Senior Software Engineer",
    "overall_score": 75,
    "minimum_pass_rate": 70,
    "decision": "Go",
    "confidence": "Medium",
    "summary": "The candidate demonstrates solid technical skills and good communication."
  },
  "scorecard_evaluation": [
    {
      "competency": "Technical Skills",
      "weight": 0.4,
      "score": 8,
      "weighted_score": 3.2,
      "reasoning": "Demonstrated good technical knowledge in responses."
    },
    {
      "competency": "Communication",
      "weight": 0.3,
      "score": 7,
      "weighted_score": 2.1,
      "reasoning": "Communicated clearly and effectively."
    }
  ],
  "question_analysis": [
    {
      "question": "Tell me about your experience.",
      "answer": "I have 5 years of experience in software development.",
      "related_competencies": ["Technical Skills"],
      "strengths": ["Clear articulation of experience"],
      "weaknesses": ["Could provide more specific examples"],
      "evaluation": "Good overview but lacks specific details."
    }
  ],
  "between_the_lines": [
    {
      "criteria": "Enthusiasm for the role",
      "observation": "Candidate showed genuine interest in the position.",
      "impact": "Positive impact on overall evaluation."
    }
  ],
  "disqualifier_check": [
    {
      "criteria": "Dishonesty",
      "triggered": false,
      "evidence": "None",
      "explanation": "No evidence of dishonesty in responses."
    }
  ],
  "decision_reasoning": {
    "key_factors": ["Strong technical background", "Good communication"],
    "strengths": ["Technical expertise", "Clear communication"],
    "concerns": ["Could provide more specific examples"],
    "final_recommendation": "Recommend proceeding with the candidate."
  }
}
```"""
            }
        }
    ]
}

@pytest.fixture
def mock_firebase_service():
    """Mock Firebase service for testing"""
    with patch('app.services.interview_evaluation_service.FirebaseService') as mock:
        # Create a mock instance
        mock_instance = mock.return_value
        
        # Mock document references and collections
        mock_doc = MagicMock()
        mock_collection = MagicMock()
        mock_instance.db = MagicMock()
        mock_instance.db.collection.return_value = mock_collection
        mock_collection.document.return_value = mock_doc
        
        # Mock get_interview_transcript method
        mock_instance.get_interview_transcript = AsyncMock(return_value=SAMPLE_TRANSCRIPT)
        
        # Mock get_application method
        mock_instance.get_application = AsyncMock(return_value=SAMPLE_APPLICATION)
        
        # Mock document get method
        mock_doc.get = MagicMock()
        mock_doc.get.return_value.exists = True
        mock_doc.get.return_value.to_dict = MagicMock(return_value=SAMPLE_TRANSCRIPT)
        
        # Mock document set and update methods
        mock_doc.set = MagicMock()
        mock_doc.update = MagicMock()
        
        # Return the mock
        yield mock_instance

@pytest.fixture
def mock_roles_service():
    """Mock Roles service for testing"""
    with patch('app.services.interview_evaluation_service.RolesService') as mock:
        # Create a mock instance
        mock_instance = mock.return_value
        
        # Mock get_role_by_id method
        mock_instance.get_role_by_id = AsyncMock(return_value=SAMPLE_ROLE)
        
        # Mock get_template and get_public_template methods
        mock_instance.get_template = AsyncMock(return_value=SAMPLE_TEMPLATE)
        mock_instance.get_public_template = AsyncMock(return_value=SAMPLE_TEMPLATE)
        
        # Return the mock
        yield mock_instance

@pytest.fixture
def mock_chat_completion_service():
    """Mock ChatCompletionService for testing"""
    with patch('app.services.interview_evaluation_service.ChatCompletionService') as mock:
        # Mock generate_completion method
        mock.generate_completion = AsyncMock(return_value=SAMPLE_OPENAI_RESPONSE)
        
        # Return the mock
        yield mock

@pytest.fixture
def mock_prompt_manager():
    """Mock PromptManager for testing"""
    with patch('app.services.interview_evaluation_service.PromptManager') as mock:
        # Mock load_prompt method
        mock.load_prompt = MagicMock(return_value="Mocked prompt template")
        
        # Return the mock
        yield mock

@pytest.mark.asyncio
async def test_evaluate_interview_authenticated(
    mock_firebase_service, mock_roles_service, mock_chat_completion_service, mock_prompt_manager
):
    """Test the authenticated interview evaluation flow"""
    # Call the service method
    result = await InterviewEvaluationService.evaluate_interview_authenticated(
        SAMPLE_INTERVIEW_ID, SAMPLE_ROLE_ID, SAMPLE_APPLICATION_ID, SAMPLE_USER_ID
    )
    
    # Verify the result
    assert result["status"] == "success"
    assert "evaluation" in result
    assert result["evaluation"]["evaluation_summary"]["decision"] == "Go"
    
    # Verify that the Firebase service was called correctly
    mock_firebase_service.get_interview_transcript.assert_called_once_with(
        SAMPLE_USER_ID, SAMPLE_ROLE_ID, SAMPLE_INTERVIEW_ID
    )
    
    # Verify that the Roles service was called correctly
    mock_roles_service.get_role_by_id.assert_called_once_with(SAMPLE_ROLE_ID)
    mock_roles_service.get_template.assert_called_once_with(
        SAMPLE_ROLE_ID, SAMPLE_TRANSCRIPT["template_id"], SAMPLE_USER_ID
    )
    
    # Verify that the ChatCompletionService was called with the correct parameters
    mock_chat_completion_service.generate_completion.assert_called_once()
    call_args = mock_chat_completion_service.generate_completion.call_args[1]
    assert call_args["model"] == "gpt-4o"
    assert call_args["use_case"] == "interview_evaluation_agent"
    assert call_args["temperature"] == 0.3
    assert call_args["max_tokens"] == 4096
    
    # Verify that the evaluation was stored in Firebase
    assert mock_firebase_service.db.collection.call_count >= 2
    
@pytest.mark.asyncio
async def test_evaluate_interview_public(
    mock_firebase_service, mock_roles_service, mock_chat_completion_service, mock_prompt_manager
):
    """Test the public interview evaluation flow"""
    # Call the service method
    result = await InterviewEvaluationService.evaluate_interview_public(
        SAMPLE_INTERVIEW_ID, SAMPLE_ROLE_ID, SAMPLE_APPLICATION_ID
    )
    
    # Verify the result
    assert result["status"] == "success"
    assert "evaluation" in result
    assert result["evaluation"]["evaluation_summary"]["decision"] == "Go"
    
    # Verify that the Firebase service was called correctly
    # For public flow, we should access the public_interview_sessions collection
    mock_firebase_service.db.collection.assert_any_call("public_interview_sessions")
    
    # Verify that the Roles service was called correctly
    mock_roles_service.get_role_by_id.assert_called_once_with(SAMPLE_ROLE_ID)
    mock_roles_service.get_public_template.assert_called_once_with(
        SAMPLE_ROLE_ID, SAMPLE_TRANSCRIPT["template_id"]
    )
    
    # Verify that the ChatCompletionService was called with the correct parameters
    mock_chat_completion_service.generate_completion.assert_called_once()
    call_args = mock_chat_completion_service.generate_completion.call_args[1]
    assert call_args["model"] == "gpt-4o"
    assert call_args["use_case"] == "interview_evaluation_agent"
    assert call_args["temperature"] == 0.3
    assert call_args["max_tokens"] == 4096
    
    # Verify that the evaluation was stored in Firebase
    mock_firebase_service.db.collection.assert_any_call("public_evaluations")

@pytest.mark.asyncio
async def test_auto_evaluate_public_interview(
    mock_firebase_service, mock_roles_service, mock_chat_completion_service, mock_prompt_manager
):
    """Test the auto-evaluation flow for public interviews"""
    # Call the service method
    result = await InterviewEvaluationService.auto_evaluate_public_interview(SAMPLE_INTERVIEW_ID)
    
    # Verify the result
    assert result["status"] == "success"
    assert "evaluation" in result
    
    # Verify that the Firebase service was called correctly
    mock_firebase_service.db.collection.assert_any_call("public_interview_sessions")
    
    # Verify that the ChatCompletionService was called
    mock_chat_completion_service.generate_completion.assert_called_once()

@pytest.mark.asyncio
async def test_evaluate_interview_error_handling(
    mock_firebase_service, mock_roles_service, mock_chat_completion_service
):
    """Test error handling in the interview evaluation service"""
    # Mock Firebase to return None for transcript
    mock_firebase_service.get_interview_transcript.return_value = None
    
    # Call the service method
    result = await InterviewEvaluationService.evaluate_interview_authenticated(
        SAMPLE_INTERVIEW_ID, SAMPLE_ROLE_ID, SAMPLE_APPLICATION_ID, SAMPLE_USER_ID
    )
    
    # Verify the error result
    assert result["status"] == "error"
    assert "message" in result
    assert "not found" in result["message"]

@pytest.mark.asyncio
async def test_evaluate_interview_openai_error(
    mock_firebase_service, mock_roles_service, mock_chat_completion_service
):
    """Test handling of OpenAI API errors"""
    # Mock ChatCompletionService to raise an exception
    mock_chat_completion_service.generate_completion.side_effect = Exception("OpenAI API error")
    
    # Call the service method
    result = await InterviewEvaluationService.evaluate_interview_authenticated(
        SAMPLE_INTERVIEW_ID, SAMPLE_ROLE_ID, SAMPLE_APPLICATION_ID, SAMPLE_USER_ID
    )
    
    # Verify the error result
    assert result["status"] == "error"
    assert "message" in result
    assert "OpenAI API error" in result["message"]

@pytest.mark.asyncio
async def test_evaluate_interview_invalid_response(
    mock_firebase_service, mock_roles_service, mock_chat_completion_service
):
    """Test handling of invalid responses from OpenAI"""
    # Mock ChatCompletionService to return an invalid response
    mock_chat_completion_service.generate_completion.return_value = {
        "choices": [{"message": {"content": "This is not valid JSON"}}]
    }
    
    # Call the service method
    result = await InterviewEvaluationService.evaluate_interview_authenticated(
        SAMPLE_INTERVIEW_ID, SAMPLE_ROLE_ID, SAMPLE_APPLICATION_ID, SAMPLE_USER_ID
    )
    
    # Verify the error result
    assert result["status"] == "error"
    assert "message" in result
    assert "Invalid JSON" in result["message"]

@pytest.mark.asyncio
async def test_evaluate_interview_missing_data(
    mock_firebase_service, mock_roles_service, mock_chat_completion_service
):
    """Test handling of missing data scenarios"""
    # Mock Roles service to return None for role
    mock_roles_service.get_role_by_id.return_value = None
    
    # Call the service method
    result = await InterviewEvaluationService.evaluate_interview_authenticated(
        SAMPLE_INTERVIEW_ID, SAMPLE_ROLE_ID, SAMPLE_APPLICATION_ID, SAMPLE_USER_ID
    )
    
    # Verify the error result
    assert result["status"] == "error"
    assert "message" in result
    assert "Role" in result["message"] and "not found" in result["message"]
