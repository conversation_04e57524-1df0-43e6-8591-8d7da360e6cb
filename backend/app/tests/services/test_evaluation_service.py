# File: backend/app/tests/services/test_evaluation_service.py

import pytest
import json
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime
import uuid

from app.services.evaluation_service import EvaluationService

# Sample test data
SAMPLE_ROLE_ID = "test-role-123"
SAMPLE_INTERVIEW_ID = "test-interview-456"
SAMPLE_APPLICATION_ID = "test-application-789"
SAMPLE_USER_ID = "test-user-abc"
SAMPLE_CANDIDATE_ID = "test-candidate-xyz"
SAMPLE_EVALUATION_ID = "test-evaluation-def"

# Sample evaluation data
SAMPLE_EVALUATION_DATA = {
    "evaluation_summary": "The candidate demonstrated strong technical skills but needs improvement in communication.",
    "overall_score": 75,
    "recommendation": {
        "decision": "Consider",
        "confidence": "Medium",
        "reasoning": "The candidate has the technical skills required but needs to improve communication."
    },
    "scorecard": {
        "technical_skills": {
            "score": 4,
            "comments": "Strong technical background with relevant experience."
        },
        "communication": {
            "score": 3,
            "comments": "Clear communication but could be more concise."
        },
        "problem_solving": {
            "score": 4,
            "comments": "Good analytical skills and approach to problems."
        }
    },
    "strengths": [
        "Technical expertise",
        "Problem-solving ability",
        "Experience with relevant technologies"
    ],
    "areas_for_improvement": [
        "Communication clarity",
        "Conciseness in responses"
    ]
}

# Sample metadata
SAMPLE_METADATA = {
    "interview_duration": "45 minutes",
    "interviewer": "Jane Smith",
    "interview_type": "Technical",
    "platform": "Video Call"
}

# Sample resume text
SAMPLE_RESUME_TEXT = """
John Doe
Software Engineer

Experience:
- 5 years at XYZ Corp as Senior Developer
- 3 years at ABC Inc as Software Engineer

Skills:
- Python, JavaScript, React
- AWS, Docker, Kubernetes
- CI/CD, Test Automation
"""

@pytest.fixture
def mock_firebase_service():
    """Mock Firebase service for testing"""
    with patch('app.services.evaluation_service.FirebaseService') as mock:
        # Mock document references and methods
        mock_doc = MagicMock()
        mock_doc.id = SAMPLE_EVALUATION_ID
        mock_doc.get.return_value.exists = True
        mock_doc.get.return_value.to_dict.return_value = {
            "role_id": SAMPLE_ROLE_ID,
            "interview_id": SAMPLE_INTERVIEW_ID,
            "evaluation_data": SAMPLE_EVALUATION_DATA,
            "status": "completed",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "tags": ["interview"]
        }
        
        # Mock collection references
        mock_collection = MagicMock()
        mock_collection.document.return_value = mock_doc
        mock_collection.where.return_value = mock_collection
        mock_collection.limit.return_value = mock_collection
        
        # Mock stream to return a list of documents
        mock_docs = [mock_doc]
        mock_collection.stream.return_value = mock_docs
        
        # Mock db attribute
        mock_instance = mock.return_value
        mock_instance.db = MagicMock()
        mock_instance.db.collection.return_value = mock_collection
        
        # Set the document ID consistently
        mock_instance.db.collection().document().id = SAMPLE_EVALUATION_ID
        mock_instance.db.collection().document().collection().document().id = SAMPLE_EVALUATION_ID
        mock_instance.db.collection().document().collection().document().collection().document().id = SAMPLE_EVALUATION_ID
        
        yield mock_instance

@pytest.mark.asyncio
async def test_create_evaluation(mock_firebase_service):
    """Test creating an evaluation document"""
    service = EvaluationService()
    service.firebase_service = mock_firebase_service
    
    # Test creating an authenticated evaluation
    evaluation_id = await service.create_evaluation(
        role_id=SAMPLE_ROLE_ID,
        interview_id=SAMPLE_INTERVIEW_ID,
        evaluation_data=SAMPLE_EVALUATION_DATA,
        candidate_id=SAMPLE_CANDIDATE_ID,
        application_id=SAMPLE_APPLICATION_ID,
        user_id=SAMPLE_USER_ID,
        metadata=SAMPLE_METADATA
    )
    
    assert evaluation_id == SAMPLE_EVALUATION_ID
    
    # Verify the correct collection was used
    mock_firebase_service.db.collection.assert_any_call("users")
    
    # Test creating a public evaluation
    evaluation_id = await service.create_evaluation(
        role_id=SAMPLE_ROLE_ID,
        interview_id=SAMPLE_INTERVIEW_ID,
        evaluation_data=SAMPLE_EVALUATION_DATA,
        candidate_id=SAMPLE_CANDIDATE_ID,
        application_id=SAMPLE_APPLICATION_ID,
        user_id=None,  # Public evaluation
        metadata=SAMPLE_METADATA
    )
    
    assert evaluation_id == SAMPLE_EVALUATION_ID
    
    # Verify the correct collection was used
    mock_firebase_service.db.collection.assert_any_call("evaluations")

@pytest.mark.asyncio
async def test_get_evaluation(mock_firebase_service):
    """Test retrieving an evaluation by ID"""
    service = EvaluationService()
    service.firebase_service = mock_firebase_service
    
    # Test retrieving a public evaluation
    evaluation = await service.get_evaluation(SAMPLE_EVALUATION_ID)
    
    assert evaluation is not None
    assert evaluation["role_id"] == SAMPLE_ROLE_ID
    assert evaluation["interview_id"] == SAMPLE_INTERVIEW_ID
    
    # Test retrieving an authenticated evaluation
    evaluation = await service.get_evaluation(SAMPLE_EVALUATION_ID, SAMPLE_USER_ID)
    
    assert evaluation is not None
    assert evaluation["role_id"] == SAMPLE_ROLE_ID
    assert evaluation["interview_id"] == SAMPLE_INTERVIEW_ID

@pytest.mark.asyncio
async def test_get_evaluations_by_role(mock_firebase_service):
    """Test retrieving evaluations for a role"""
    service = EvaluationService()
    service.firebase_service = mock_firebase_service
    
    # Set up the mock document to have the expected fields
    mock_doc = mock_firebase_service.db.collection().document()
    mock_doc.get.return_value.to_dict.return_value = {
        "role_id": SAMPLE_ROLE_ID,
        "interview_id": SAMPLE_INTERVIEW_ID
    }
    
    # Mock the stream method to return documents with proper dictionaries
    mock_eval_doc = MagicMock()
    mock_eval_doc.id = SAMPLE_EVALUATION_ID
    mock_eval_doc.to_dict.return_value = {
        "role_id": SAMPLE_ROLE_ID,
        "interview_id": SAMPLE_INTERVIEW_ID
    }
    mock_firebase_service.db.collection().stream.return_value = [mock_eval_doc]
    mock_firebase_service.db.collection().where().stream.return_value = [mock_eval_doc]
    
    # Test retrieving evaluations for a role
    evaluations = await service.get_evaluations_by_role(SAMPLE_ROLE_ID)
    
    assert len(evaluations) > 0
    assert "role_id" in evaluations[0]
    
    # Test retrieving authenticated evaluations for a role
    evaluations = await service.get_evaluations_by_role(SAMPLE_ROLE_ID, SAMPLE_USER_ID)
    
    assert len(evaluations) > 0
    assert "role_id" in evaluations[0]

@pytest.mark.asyncio
async def test_get_evaluations_by_candidate(mock_firebase_service):
    """Test retrieving evaluations for a candidate"""
    service = EvaluationService()
    service.firebase_service = mock_firebase_service
    
    # Test retrieving evaluations for a candidate
    evaluations = await service.get_evaluations_by_candidate(SAMPLE_CANDIDATE_ID)
    
    assert len(evaluations) > 0
    
    # Test retrieving authenticated evaluations for a candidate
    evaluations = await service.get_evaluations_by_candidate(SAMPLE_CANDIDATE_ID, SAMPLE_USER_ID)
    
    assert len(evaluations) > 0

@pytest.mark.asyncio
async def test_get_evaluation_by_interview(mock_firebase_service):
    """Test retrieving an evaluation for an interview"""
    service = EvaluationService()
    service.firebase_service = mock_firebase_service
    
    # Set up the mock document to have the expected fields
    mock_doc = mock_firebase_service.db.collection().document()
    mock_doc.get.return_value.to_dict.return_value = {
        "interview_id": SAMPLE_INTERVIEW_ID,
        "role_id": SAMPLE_ROLE_ID
    }
    
    # Mock the stream method to return documents with proper dictionaries
    mock_eval_doc = MagicMock()
    mock_eval_doc.id = SAMPLE_EVALUATION_ID
    mock_eval_doc.to_dict.return_value = {
        "interview_id": SAMPLE_INTERVIEW_ID,
        "role_id": SAMPLE_ROLE_ID
    }
    mock_firebase_service.db.collection().where().stream.return_value = [mock_eval_doc]
    mock_firebase_service.db.collection().where().limit().stream.return_value = [mock_eval_doc]
    
    # Test retrieving an evaluation for an interview
    evaluation = await service.get_evaluation_by_interview(SAMPLE_INTERVIEW_ID)
    
    assert evaluation is not None
    assert "interview_id" in evaluation
    
    # Test retrieving an authenticated evaluation for an interview
    evaluation = await service.get_evaluation_by_interview(SAMPLE_INTERVIEW_ID, SAMPLE_USER_ID)
    
    assert evaluation is not None
    assert "interview_id" in evaluation

@pytest.mark.asyncio
async def test_update_evaluation_status(mock_firebase_service):
    """Test updating the status of an evaluation"""
    service = EvaluationService()
    service.firebase_service = mock_firebase_service
    
    # Test updating a public evaluation status
    result = await service.update_evaluation_status(SAMPLE_EVALUATION_ID, "reviewed")
    
    assert result is True
    
    # Test updating an authenticated evaluation status
    result = await service.update_evaluation_status(SAMPLE_EVALUATION_ID, "reviewed", SAMPLE_USER_ID)
    
    assert result is True

@pytest.mark.asyncio
async def test_get_evaluation_public(mock_firebase_service):
    """Test retrieving a public evaluation with limited data"""
    service = EvaluationService()
    service.firebase_service = mock_firebase_service
    
    # Set up the mock document to have the expected fields
    mock_doc = mock_firebase_service.db.collection().document()
    mock_doc.get.return_value.to_dict.return_value = {
        "id": SAMPLE_EVALUATION_ID,
        "role_id": SAMPLE_ROLE_ID,
        "interview_id": SAMPLE_INTERVIEW_ID,
        "evaluation_data": {
            "evaluation_summary": "Summary",
            "recommendation": {},
            "overall_score": 75
        },
        "status": "completed",
        "created_at": datetime.utcnow(),
        "tags": ["interview"]
    }
    
    # Test retrieving a public evaluation
    evaluation = await service.get_evaluation_public(SAMPLE_INTERVIEW_ID)
    
    assert evaluation is not None
    assert "interview_id" in evaluation
    
    # Test retrieving a public evaluation with application ID
    evaluation = await service.get_evaluation_public(SAMPLE_INTERVIEW_ID, SAMPLE_APPLICATION_ID)
    
    assert evaluation is not None
    assert "interview_id" in evaluation

@pytest.mark.asyncio
async def test_create_resume_evaluation(mock_firebase_service):
    """Test creating a resume evaluation"""
    service = EvaluationService()
    service.firebase_service = mock_firebase_service
    
    # Patch uuid.uuid4 to return a consistent value
    with patch('uuid.uuid4', return_value=MagicMock(hex='ccd6089bfcc348909fa8628cd8ade6fd')):
        # Test creating a resume evaluation
        evaluation_id = await service.create_resume_evaluation(
            role_id=SAMPLE_ROLE_ID,
            evaluation_data=SAMPLE_EVALUATION_DATA,
            resume_text=SAMPLE_RESUME_TEXT,
            candidate_id=SAMPLE_CANDIDATE_ID,
            application_id=SAMPLE_APPLICATION_ID,
            user_id=SAMPLE_USER_ID,
            metadata=SAMPLE_METADATA
        )
        
        assert evaluation_id == SAMPLE_EVALUATION_ID
        
        # Verify the correct collection was used
        mock_firebase_service.db.collection.assert_any_call("users")
        
        # Test creating a public resume evaluation
        evaluation_id = await service.create_resume_evaluation(
            role_id=SAMPLE_ROLE_ID,
            evaluation_data=SAMPLE_EVALUATION_DATA,
            resume_text=SAMPLE_RESUME_TEXT,
            candidate_id=SAMPLE_CANDIDATE_ID,
            application_id=SAMPLE_APPLICATION_ID,
            user_id=None,  # Public evaluation
            metadata=SAMPLE_METADATA
        )
        
        assert evaluation_id == SAMPLE_EVALUATION_ID
        
        # Verify the correct collection was used
        mock_firebase_service.db.collection.assert_any_call("evaluations")

@pytest.mark.asyncio
async def test_error_handling(mock_firebase_service):
    """Test error handling in the evaluation service"""
    service = EvaluationService()
    service.firebase_service = mock_firebase_service
    
    # Mock an exception when calling Firestore
    mock_firebase_service.db.collection.side_effect = Exception("Firestore error")
    
    # Test error handling in create_evaluation
    with pytest.raises(ValueError) as excinfo:
        await service.create_evaluation(
            role_id=SAMPLE_ROLE_ID,
            interview_id=SAMPLE_INTERVIEW_ID,
            evaluation_data=SAMPLE_EVALUATION_DATA
        )
    
    assert "Failed to create evaluation" in str(excinfo.value)
    
    # Test error handling in get_evaluation
    with pytest.raises(ValueError) as excinfo:
        await service.get_evaluation(SAMPLE_EVALUATION_ID)
    
    assert "Failed to retrieve evaluation" in str(excinfo.value)
