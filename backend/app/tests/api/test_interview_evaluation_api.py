# File: backend/app/tests/api/test_interview_evaluation_api.py

import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi import FastAP<PERSON>, Depends, APIRouter, HTTPException
from fastapi.testclient import TestClient

from app.api.v1.endpoints.interview_evaluation import router, get_current_user
from app.services.interview_evaluation_service import InterviewEvaluationService

# Sample test data
SAMPLE_INTERVIEW_ID = "test-interview-456"
SAMPLE_ROLE_ID = "test-role-123"
SAMPLE_APPLICATION_ID = "test-application-789"
SAMPLE_USER_ID = "test-user-abc"

# Sample successful evaluation result
SAMPLE_SUCCESS_RESULT = {
    "status": "success",
    "evaluation": {
        "evaluation_summary": {
            "candidate_name": "<PERSON>",
            "role": "Senior Software Engineer",
            "overall_score": 75,
            "minimum_pass_rate": 70,
            "decision": "Go",
            "confidence": "Medium",
            "summary": "The candidate demonstrates solid technical skills and good communication."
        },
        "scorecard_evaluation": [
            {
                "competency": "Technical Skills",
                "weight": 0.4,
                "score": 8,
                "weighted_score": 3.2,
                "reasoning": "Demonstrated good technical knowledge in responses."
            }
        ],
        "question_analysis": [],
        "between_the_lines": [],
        "disqualifier_check": [],
        "decision_reasoning": {
            "key_factors": [],
            "strengths": [],
            "concerns": [],
            "final_recommendation": ""
        }
    }
}

# Sample error result
SAMPLE_ERROR_RESULT = {
    "status": "error",
    "message": "Interview transcript not found"
}

# Create a test user for authentication
async def mock_get_current_user():
    return {"uid": SAMPLE_USER_ID, "email": "<EMAIL>"}

# Create a test FastAPI app with dependency override
app = FastAPI()
app.include_router(router, prefix="/interview-evaluation")
app.dependency_overrides[get_current_user] = mock_get_current_user

# Create a test client
client = TestClient(app)

@pytest.fixture
def mock_interview_evaluation_service():
    """Mock the InterviewEvaluationService"""
    with patch.object(InterviewEvaluationService, "evaluate_interview_authenticated", new_callable=AsyncMock) as mock_auth, \
         patch.object(InterviewEvaluationService, "evaluate_interview_public", new_callable=AsyncMock) as mock_public, \
         patch.object(InterviewEvaluationService, "auto_evaluate_public_interview", new_callable=AsyncMock) as mock_auto:
        
        # Set return values
        mock_auth.return_value = SAMPLE_SUCCESS_RESULT
        mock_public.return_value = SAMPLE_SUCCESS_RESULT
        mock_auto.return_value = SAMPLE_SUCCESS_RESULT
        
        yield {
            "authenticated": mock_auth,
            "public": mock_public,
            "auto": mock_auto
        }

def test_evaluate_interview_authenticated(mock_interview_evaluation_service):
    """Test the authenticated interview evaluation endpoint"""
    # Prepare request data
    request_data = {
        "interview_id": SAMPLE_INTERVIEW_ID,
        "role_id": SAMPLE_ROLE_ID,
        "application_id": SAMPLE_APPLICATION_ID
    }
    
    # Make the request
    response = client.post("/interview-evaluation/evaluate", json=request_data)
    
    # Verify the response
    assert response.status_code == 200
    assert response.json()["status"] == "success"
    assert "evaluation" in response.json()
    
    # Verify the service was called correctly
    mock_interview_evaluation_service["authenticated"].assert_called_once_with(
        SAMPLE_INTERVIEW_ID, SAMPLE_ROLE_ID, SAMPLE_APPLICATION_ID, SAMPLE_USER_ID
    )

def test_evaluate_interview_authenticated_error(mock_interview_evaluation_service):
    """Test error handling in the authenticated endpoint"""
    # Mock the service to return an error
    mock_interview_evaluation_service["authenticated"].return_value = SAMPLE_ERROR_RESULT
    
    # Prepare request data
    request_data = {
        "interview_id": SAMPLE_INTERVIEW_ID,
        "role_id": SAMPLE_ROLE_ID,
        "application_id": SAMPLE_APPLICATION_ID
    }
    
    # Make the request
    response = client.post("/interview-evaluation/evaluate", json=request_data)
    
    # Verify the response
    assert response.status_code == 400
    assert "detail" in response.json()
    assert response.json()["detail"] == SAMPLE_ERROR_RESULT["message"]

def test_evaluate_interview_unauthenticated():
    """Test that authentication is required for the authenticated endpoint"""
    # Create a new app with no dependency override for this test
    test_app = FastAPI()
    test_app.include_router(router, prefix="/interview-evaluation")
    test_client = TestClient(test_app)
    
    # Prepare request data
    request_data = {
        "interview_id": SAMPLE_INTERVIEW_ID,
        "role_id": SAMPLE_ROLE_ID,
        "application_id": SAMPLE_APPLICATION_ID
    }
    
    # Make the request
    response = test_client.post("/interview-evaluation/evaluate", json=request_data)
    
    # Verify the response
    assert response.status_code in [401, 403]  # Either unauthorized or forbidden

def test_evaluate_public_interview(mock_interview_evaluation_service):
    """Test the public interview evaluation endpoint"""
    # Prepare request data
    request_data = {
        "session_id": SAMPLE_INTERVIEW_ID,
        "role_id": SAMPLE_ROLE_ID,
        "application_id": SAMPLE_APPLICATION_ID
    }
    
    # Make the request
    response = client.post("/interview-evaluation/public/evaluate", json=request_data)
    
    # Verify the response
    assert response.status_code == 200
    assert response.json()["status"] == "success"
    assert "evaluation" in response.json()
    
    # Verify the service was called correctly
    mock_interview_evaluation_service["public"].assert_called_once_with(
        SAMPLE_INTERVIEW_ID, SAMPLE_ROLE_ID, SAMPLE_APPLICATION_ID
    )

def test_evaluate_public_interview_error(mock_interview_evaluation_service):
    """Test error handling in the public endpoint"""
    # Mock the service to return an error
    mock_interview_evaluation_service["public"].return_value = SAMPLE_ERROR_RESULT
    
    # Prepare request data
    request_data = {
        "session_id": SAMPLE_INTERVIEW_ID,
        "role_id": SAMPLE_ROLE_ID,
        "application_id": SAMPLE_APPLICATION_ID
    }
    
    # Make the request
    response = client.post("/interview-evaluation/public/evaluate", json=request_data)
    
    # Verify the response
    assert response.status_code == 400
    assert "detail" in response.json()
    assert response.json()["detail"] == SAMPLE_ERROR_RESULT["message"]

def test_auto_evaluate_public_interview(mock_interview_evaluation_service):
    """Test the auto-evaluation endpoint"""
    # Make the request
    response = client.post(f"/interview-evaluation/public/auto-evaluate/{SAMPLE_INTERVIEW_ID}")
    
    # Verify the response
    assert response.status_code == 200
    assert response.json()["status"] == "success"
    
    # Verify the service was called correctly
    mock_interview_evaluation_service["auto"].assert_called_once_with(SAMPLE_INTERVIEW_ID)

def test_auto_evaluate_public_interview_error(mock_interview_evaluation_service):
    """Test error handling in the auto-evaluation endpoint"""
    # Create a new app for this test with a custom endpoint handler
    test_app = FastAPI()
    
    # Define a custom router with error handling
    test_router = APIRouter()
    
    @test_router.post("/public/auto-evaluate/{session_id}", response_model=dict)
    async def test_auto_evaluate(session_id: str):
        # Simulate an error
        raise HTTPException(
            status_code=400,
            detail="Interview transcript not found"
        )
    
    test_app.include_router(test_router, prefix="/interview-evaluation")
    test_client = TestClient(test_app)
    
    # Make the request to the test endpoint
    response = test_client.post(f"/interview-evaluation/public/auto-evaluate/{SAMPLE_INTERVIEW_ID}")
    
    # Verify the response
    assert response.status_code == 400
    assert "detail" in response.json()
    assert "Interview transcript not found" in response.json()["detail"]
