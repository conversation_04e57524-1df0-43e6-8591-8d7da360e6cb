# File: backend/app/main.py
"""Main application module."""

import asyncio
from fastapi import FastAPI, Request, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
import logging
from app.core.config import settings
from app.core.logging_config import configure_logging
from app.core.logging import mask_sensitive_data
from app.core.log_utils import configure_root_logger_with_data_truncation
from app.services.email_service import EmailService
from app.services.firebase_service import FirebaseService
from app.api.v1.api import api_router
from fastapi.responses import J<PERSON>NResponse, Response
import traceback
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.requests import ClientDisconnect
import h11

# Configure logging with error filtering
configure_logging(level=logging.INFO, filter_protocol_errors=True)
# Configure data truncation for large objects in logs - use more aggressive truncation
configure_root_logger_with_data_truncation(max_length=50, max_items=2)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    redirect_slashes=False
)

# Configure CORS
origins = [
    "http://localhost:3000",  # Local development
    "http://127.0.0.1:3000",  # Local development alternative
    "http://localhost:5173",  # Vite dev server
    "https://www.recruiva.ai",  # Production domain
    "https://recruiva.ai",      # Production domain without www
    "https://recruiva-frontend.vercel.app",  # Vercel preview deployments
    "https://recruiva-frontend-git-main-recruiva.vercel.app",  # Vercel main branch
    "https://recruiva-frontend-*.vercel.app",  # All Vercel preview deployments
    "https://*.vercel.app",  # All Vercel deployments
    "https://recruiva-backend.onrender.com",  # Render.com backend URL
    "https://exactly-square-walleye.ngrok-free.app",  # Ngrok tunnel
    "*"  # Allow all origins for development
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=False,  # Set to False to match frontend withCredentials=false
    allow_methods=["*"],
    allow_headers=["*", "x-candidate-email", "x-candidate-name", "X-Candidate-Email", "X-Candidate-Name"],  # Explicitly include custom headers in both formats
    expose_headers=["*"],
    max_age=3600,
)

# Initialize services
email_service = EmailService()

# Custom middleware to handle connection errors
class ConnectionErrorHandlingMiddleware(BaseHTTPMiddleware):
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ):
        try:
            # Wrap the response in a try/except block to catch disconnections
            response = await call_next(request)
            return response
        except ClientDisconnect:
            # Log but don't raise an exception for client disconnections
            logging.warning(f"Client disconnected during request processing: {request.url.path}")
            # Return a response that won't attempt to send data back to the client
            return Response(status_code=499, content="Client Disconnected")
        except h11._util.LocalProtocolError as protocol_error:
            # Handle HTTP protocol errors separately
            error_message = str(protocol_error)
            # Expanded list of h11 protocol errors that indicate client disconnection
            disconnect_indicators = [
                "can't handle event type ConnectionClosed",
                "Can't send data when our state is ERROR",
                "unexpected event type",
                "connection state",
                "received ConnectionClosed",
                "LocalProtocolError",
                "state is ERROR"
            ]

            if any(indicator in error_message for indicator in disconnect_indicators):
                # Get the path for better logging
                path = request.url.path
                # Check if this is a template-related endpoint
                is_template_endpoint = 'templates' in path and ('generate-questions' in path or 'generate-criteria' in path)

                if is_template_endpoint:
                    logging.info(f"HTTP protocol error in template generation endpoint: {path}. This is expected and handled gracefully.")
                else:
                    logging.warning(f"HTTP protocol error (client likely disconnected): {error_message} for path: {path}")

                # Return a response that won't attempt to send data back to the client
                return Response(status_code=499, content="Client Disconnected")

            # For other protocol errors, log and re-raise
            logging.error(f"HTTP protocol error: {error_message}")
            raise
        except Exception as e:
            # For all other exceptions, log and re-raise
            logging.error(f"Error during request processing: {str(e)}")
            raise

# Add connection error handling middleware
app.add_middleware(ConnectionErrorHandlingMiddleware)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log request details and handle errors."""
    # Generate request ID
    request_id = request.headers.get("X-Request-ID", "unknown")

    # Log request details
    logger.info(f"Request {request_id}: {request.method} {request.url}")

    # Mask sensitive data in headers before logging
    headers_dict = dict(request.headers)
    masked_headers = mask_sensitive_data(headers_dict)
    logger.info(f"Request headers: {masked_headers}")

    try:
        # Process request
        response = await call_next(request)

        # Log response status
        logger.info(f"Response {request_id}: Status {response.status_code}")

        return response
    except Exception as e:
        # Log the full error with traceback
        logger.error(f"Error processing request {request_id}: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Return error response
        return JSONResponse(
            status_code=500,
            content={
                "detail": "An internal server error occurred",
                "request_id": request_id
            }
        )

@app.middleware("http")
async def add_cors_headers(request: Request, call_next):
    """Add CORS headers to all responses."""
    response = await call_next(request)

    # Get the origin from the request headers
    origin = request.headers.get("origin")

    # Set CORS headers for all origins
    response.headers["Access-Control-Allow-Origin"] = origin or "*"
    response.headers["Access-Control-Allow-Credentials"] = "false"  # Set to false to match frontend withCredentials=false
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS, PATCH"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, X-Requested-With, Cache-Control, Pragma, Expires, X-No-Retry, x-candidate-email, x-candidate-name, X-Candidate-Email, X-Candidate-Name"

    return response

@app.on_event("startup")
async def startup_event():
    """Initialize services on application startup."""
    try:
        # Initialize Firebase using the singleton service
        firebase_service = FirebaseService()
        logging.info("Firebase service initialized successfully")

        # Start email listener if enabled
        # NOTE: Email listener is currently DISABLED in config.py (EMAIL_LISTENER_ENABLED=False)
        # To re-enable:
        # 1. Set EMAIL_LISTENER_ENABLED=True in backend/app/core/config.py
        # 2. Restart the application
        if settings.EMAIL_LISTENER_ENABLED:
            logging.info("Email listener service is ENABLED. Starting service...")
            asyncio.create_task(email_service.start_email_listener())
        else:
            logging.warning("Email listener service is DISABLED in configuration. Set EMAIL_LISTENER_ENABLED=True to enable.")

    except Exception as e:
        logger.error(f"Error during startup: {str(e)}")
        raise

@app.get("/")
async def root():
    return {"message": "Welcome to Recruiva API"}

@app.get("/api/v1/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, timeout_keep_alive=75)
