# File: backend/app/services/openai/prompt_loader.py

import os
import logging
from typing import Dict, Any, Optional
from string import Template

class PromptLoader:
    """
    Utility class for loading and formatting prompt templates.
    Supports loading templates from files with .prompt extension.
    """
    
    def __init__(self, prompts_dir: Optional[str] = None):
        """
        Initialize the prompt loader with the directory containing prompt templates.
        
        Args:
            prompts_dir: Optional directory path for prompt templates.
                         If not provided, defaults to the 'prompts' directory in the same directory as this file.
        """
        if prompts_dir is None:
            self.prompts_dir = os.path.join(os.path.dirname(__file__), 'prompts')
        else:
            self.prompts_dir = prompts_dir
            
        logging.info(f"Initialized PromptLoader with prompts directory: {self.prompts_dir}")
    
    def load_prompt(self, template_name: str, variables: Dict[str, Any] = None) -> str:
        """
        Load a prompt template and substitute variables.
        
        Args:
            template_name: Name of the template file (without extension)
            variables: Dictionary of variables to substitute in the template
            
        Returns:
            Formatted prompt string
        """
        if variables is None:
            variables = {}
            
        # Construct the full path to the prompt file with .prompt extension
        prompt_path = os.path.join(self.prompts_dir, f"{template_name}.prompt")
        
        # Check if the file exists
        if not os.path.exists(prompt_path):
            error_msg = f"Prompt template not found: {prompt_path}"
            logging.error(error_msg)
            raise FileNotFoundError(error_msg)
        
        try:
            # Read the template file
            with open(prompt_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
                
            # Create a template and substitute variables
            template = Template(template_content)
            formatted_prompt = template.safe_substitute(variables)
            
            logging.debug(f"Loaded prompt template: {template_name}")
            return formatted_prompt
            
        except Exception as e:
            error_msg = f"Error loading prompt template {template_name}: {str(e)}"
            logging.error(error_msg)
            raise ValueError(error_msg)
    
    def get_available_prompts(self) -> list:
        """
        Get a list of available prompt templates.
        
        Returns:
            List of template names (without extension)
        """
        try:
            # List all files in the prompts directory with .prompt extension
            prompt_files = [f for f in os.listdir(self.prompts_dir) if f.endswith('.prompt')]
            
            # Remove the extension to get the template names
            template_names = [os.path.splitext(f)[0] for f in prompt_files]
            
            return template_names
            
        except Exception as e:
            logging.error(f"Error listing prompt templates: {str(e)}")
            return [] 