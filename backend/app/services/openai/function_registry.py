"""
Enhanced function registry with granular function loading.
"""

from typing import Dict, Any, List, Optional, Callable
import logging

logger = logging.getLogger(__name__)

class FunctionRegistry:
    """
    Enhanced function registry with granular function loading.
    Supports selective function loading based on context.
    """
    
    # Function definitions by category
    _function_definitions = {
        "realtime": {
            "update_role": {
                "description": "Update role details",
                "parameters": {}
            },
            "end_the_call": {
                "description": "End the conversation",
                "parameters": {}
            },
            "return_json_output": {
                "description": "Return structured JSON output",
                "parameters": {}
            }
        },
        "chat": {
            "generate_job_posting": {
                "description": "Generate a job posting",
                "parameters": {}
            }
        },
        "intake_agent": {
            "update_role": {
                "description": "Update role details",
                "parameters": {}
            },
            "end_the_call": {
                "description": "End the conversation",
                "parameters": {}
            }
        },
        "interview_agent": {
            "evaluate_response": {
                "description": "Evaluate candidate response",
                "parameters": {}
            },
            "end_the_interview": {
                "description": "End the interview",
                "parameters": {}
            }
        }
    }
    
    # Function handlers
    _handlers = {}
    
    @classmethod
    def register_handler(cls, function_name: str, handler: Callable) -> None:
        """
        Register a handler for a function.
        
        Args:
            function_name: Name of the function
            handler: Function handler
        """
        cls._handlers[function_name] = handler
        logger.info(f"Registered handler for function: {function_name}")
    
    @classmethod
    def get_handler(cls, function_name: str) -> Optional[Callable]:
        """
        Get the handler for a function.
        
        Args:
            function_name: Name of the function
            
        Returns:
            Function handler or None if not found
        """
        return cls._handlers.get(function_name)
    
    @classmethod
    def get_functions(cls, agent_type: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Get functions for a specific agent type and context.
        
        Args:
            agent_type: Type of agent
            context: Optional context for function selection
            
        Returns:
            Dictionary of function definitions
        """
        context = context or {}
        
        # Start with functions for the specific agent type
        functions = {}
        if agent_type in cls._function_definitions:
            functions.update(cls._function_definitions[agent_type])
        
        # Add general functions if available
        if "realtime" in cls._function_definitions and agent_type != "realtime":
            # Only add functions that don't already exist
            for name, definition in cls._function_definitions["realtime"].items():
                if name not in functions:
                    functions[name] = definition
        
        # Filter functions based on context if needed
        if context.get("filter_functions"):
            filter_list = context["filter_functions"]
            functions = {name: definition for name, definition in functions.items() if name in filter_list}
        
        logger.info(f"Loaded {len(functions)} functions for agent type: {agent_type}")
        return functions
    
    @classmethod
    def register_function(cls, category: str, name: str, description: str, parameters: Dict[str, Any]) -> None:
        """
        Register a new function definition.
        
        Args:
            category: Function category (agent type)
            name: Function name
            description: Function description
            parameters: Function parameters schema
        """
        # Ensure category exists
        if category not in cls._function_definitions:
            cls._function_definitions[category] = {}
        
        # Add the function
        cls._function_definitions[category][name] = {
            "description": description,
            "parameters": parameters
        }
        
        logger.info(f"Registered function {name} in category {category}")
    
    # Legacy properties for backward compatibility
    @property
    def realtime_functions(self) -> Dict[str, Any]:
        """Get realtime functions (legacy property)."""
        return self.get_functions("realtime")
    
    @property
    def chat_functions(self) -> Dict[str, Any]:
        """Get chat functions (legacy property)."""
        return self.get_functions("chat") 