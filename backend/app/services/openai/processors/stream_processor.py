# File: backend/app/services/openai/processors/stream_processor.py

from typing import As<PERSON><PERSON>enerator, Any, Dict, Optional
from openai.types.chat import ChatCompletionChunk
import json

class StreamProcessor:
    """Processor for handling streaming responses from OpenAI."""
    
    @staticmethod
    async def process_text_stream(
        stream: AsyncGenerator[Cha<PERSON><PERSON><PERSON>ple<PERSON><PERSON>hunk, None] | Any
    ) -> AsyncGenerator[str, None]:
        """
        Process a text stream from OpenAI.
        
        Args:
            stream: OpenAI response stream or coroutine that will return a stream
            
        Yields:
            Text chunks from the stream
        """
        # Ensure we have an async generator
        if not hasattr(stream, '__aiter__'):
            stream = await stream
            
        async for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content
    
    @staticmethod
    async def process_json_stream(
        stream: AsyncGenerator[ChatCompletionChunk, None]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a JSON stream from OpenAI.
        
        Args:
            stream: OpenAI response stream
            
        Yields:
            Parsed JSON chunks from the stream
        """
        buffer = ""
        async for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                buffer += chunk.choices[0].delta.content
                
                try:
                    # Try to parse complete JSON objects from the buffer
                    while True:
                        # Find the next complete JSON object
                        start = buffer.find("{")
                        if start == -1:
                            break
                            
                        # Find matching closing brace
                        brace_count = 0
                        end = -1
                        for i in range(start, len(buffer)):
                            if buffer[i] == "{":
                                brace_count += 1
                            elif buffer[i] == "}":
                                brace_count -= 1
                                if brace_count == 0:
                                    end = i + 1
                                    break
                        
                        if end == -1:
                            break
                            
                        # Try to parse the JSON object
                        try:
                            json_str = buffer[start:end]
                            json_obj = json.loads(json_str)
                            yield json_obj
                            buffer = buffer[end:]
                        except json.JSONDecodeError:
                            break
                            
                except Exception:
                    # If any error occurs, just continue to next chunk
                    continue
        
        # Try to parse any remaining complete JSON in the buffer
        if buffer:
            try:
                json_obj = json.loads(buffer)
                yield json_obj
            except json.JSONDecodeError:
                pass
    
    @staticmethod
    async def process_function_stream(
        stream: AsyncGenerator[ChatCompletionChunk, None]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a function call stream from OpenAI.
        
        Args:
            stream: OpenAI response stream
            
        Yields:
            Function call information as it becomes available
        """
        function_name = None
        argument_buffer = ""
        
        async for chunk in stream:
            delta = chunk.choices[0].delta
            
            # Process function name
            if delta.function_call and hasattr(delta.function_call, 'name'):
                function_name = delta.function_call.name
                yield {"type": "function_name", "name": function_name}
            
            # Process function arguments
            if delta.function_call and hasattr(delta.function_call, 'arguments'):
                argument_buffer += delta.function_call.arguments
                try:
                    args = json.loads(argument_buffer)
                    yield {
                        "type": "function_args",
                        "name": function_name,
                        "arguments": args
                    }
                    argument_buffer = ""
                except json.JSONDecodeError:
                    # Continue accumulating if not valid JSON yet
                    continue
            
            # Process regular content
            elif delta.content is not None:
                yield {
                    "type": "content",
                    "content": delta.content
                }
