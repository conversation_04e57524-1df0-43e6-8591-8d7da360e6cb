# File: backend/app/services/openai/processors/json_processor.py

from typing import Any, Dict, Optional, Type, TypeVar
import json
from pydantic import BaseModel, ValidationError
import logging

T = TypeVar('T', bound=BaseModel)

class JSONProcessor:
    """Processor for handling JSON responses from OpenAI."""
    
    @staticmethod
    def extract_json(text: str) -> Dict[str, Any]:
        """Extract JSON from text that might contain other content."""
        logging.info(f"Attempting to extract JSON from text: {text}")
        
        if not isinstance(text, str):
            raise ValueError(f"Expected string input but got {type(text)}")
            
        # Clean up the text first
        text = text.strip()
        if not text:
            raise ValueError("Empty text provided")
            
        # Try direct JSON parsing first
        try:
            result = json.loads(text)
            logging.info("Successfully parsed text as JSON directly")
            return result
        except json.JSONDecodeError:
            logging.info("Direct JSON parsing failed, trying cleanup")
            
        # Find the outermost JSON structure
        start_idx = text.find('{')
        end_idx = text.rfind('}')
        
        if start_idx == -1 or end_idx == -1:
            raise ValueError("No JSON object found in text")
            
        # Extract potential JSON string
        json_str = text[start_idx:end_idx + 1]
        
        # Clean up common issues
        json_str = json_str.strip()
        json_str = json_str.strip('"\'')  # Remove quotes
        json_str = json_str.replace('\n', ' ')  # Remove newlines
        json_str = json_str.replace('\r', ' ')  # Remove carriage returns
        json_str = ' '.join(json_str.split())  # Normalize whitespace
        
        # Try to parse the cleaned JSON
        try:
            result = json.loads(json_str)
            logging.info("Successfully parsed cleaned JSON")
            return result
        except json.JSONDecodeError as e:
            # One last attempt with unicode escape handling
            try:
                cleaned_json = json_str.encode('utf-8').decode('unicode-escape')
                result = json.loads(cleaned_json)
                logging.info("Successfully parsed JSON after unicode escape handling")
                return result
            except json.JSONDecodeError:
                error_msg = f"Failed to parse JSON after all cleanup attempts: {str(e)}"
                logging.error(error_msg)
                raise ValueError(error_msg)
    
    @staticmethod
    def validate_schema(data: Dict[str, Any], model: Type[T]) -> T:
        """
        Validate JSON data against a Pydantic model.
        
        Args:
            data: JSON data to validate
            model: Pydantic model class to validate against
            
        Returns:
            Validated Pydantic model instance
        """
        try:
            return model.model_validate(data)
        except ValidationError as e:
            raise ValueError(f"JSON validation failed: {str(e)}")
    
    @staticmethod
    def process_response(
        text: str,
        model: Optional[Type[T]] = None
    ) -> Dict[str, Any] | T:
        """
        Process and optionally validate OpenAI response.
        
        Args:
            text: Response text from OpenAI
            model: Optional Pydantic model for validation
            
        Returns:
            Processed JSON as dictionary or validated Pydantic model
        """
        logging.info(f"Processing response text: {text}")
        if not isinstance(text, str):
            error_msg = f"Expected string input but got {type(text)}"
            logging.error(error_msg)
            raise TypeError(error_msg)
            
        json_data = JSONProcessor.extract_json(text)
        logging.info(f"Extracted JSON data: {json.dumps(json_data, indent=2)}")
        
        if model:
            logging.info(f"Validating against model: {model.__name__}")
            return JSONProcessor.validate_schema(json_data, model)
        return json_data
    
    @staticmethod
    def create_function_schema(model: Type[BaseModel]) -> Dict[str, Any]:
        """
        Create OpenAI function schema from Pydantic model.
        
        Args:
            model: Pydantic model to convert to function schema
            
        Returns:
            Function schema compatible with OpenAI function calling
        """
        schema = model.model_json_schema()
        
        # Convert to OpenAI function schema format
        function_schema = {
            "name": model.__name__.lower(),
            "description": model.__doc__ or f"Create a {model.__name__}",
            "parameters": schema
        }
        
        # Remove Pydantic-specific fields
        if "title" in function_schema["parameters"]:
            del function_schema["parameters"]["title"]
        
        return function_schema
