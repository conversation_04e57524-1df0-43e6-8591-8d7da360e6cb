# File: backend/app/services/openai/processors/function_registry.py

from typing import Dict, Any, List, Callable
import inspect
import logging
import json

class FunctionRegistry:
    def __init__(self):
        self.functions: Dict[str, Dict[str, Any]] = {}
        self.handlers: Dict[str, Callable] = {}
        logging.info("Function Registry initialized")
    
    def register(self, name: str, description: str, parameters: Dict[str, Any]):
        """Register a new function definition."""
        if name in self.functions:
            logging.warning(f"Function {name} already registered. Overwriting...")
        
        self.functions[name] = {
            "name": name,
            "description": description,
            "parameters": parameters
        }
        logging.info(f"Registered function: {name}")
        logging.debug(f"Function details: {json.dumps(self.functions[name], indent=2)}")
    
    def register_handler(self, name: str, handler: Callable):
        """Register a handler for a function."""
        if not callable(handler):
            raise ValueError(f"Handler for {name} must be callable")
        
        if name not in self.functions:
            raise ValueError(f"Function {name} not registered. Register function before handler.")
        
        # Validate handler signature matches parameters
        sig = inspect.signature(handler)
        params = sig.parameters
        
        # Handler should accept a single dict argument for the parameters
        if len(params) != 1:
            raise ValueError(f"Handler for {name} should accept exactly one argument (parameters dict)")
        
        self.handlers[name] = handler
        logging.info(f"Registered handler for function: {name}")
    
    def get_functions(self) -> List[Dict[str, Any]]:
        """Get all registered functions."""
        functions = list(self.functions.values())
        if not functions:
            logging.warning("No functions registered in the registry")
        return functions
    
    def get_handler(self, name: str) -> Callable:
        """Get handler for a function."""
        handler = self.handlers.get(name)
        if not handler:
            logging.warning(f"No handler found for function: {name}")
        return handler
    
    def validate_parameters(self, name: str, parameters: Dict[str, Any]) -> bool:
        """Validate parameters against function schema."""
        if name not in self.functions:
            raise ValueError(f"Function {name} not registered")
        
        function_schema = self.functions[name]["parameters"]
        required = function_schema.get("required", [])
        properties = function_schema.get("properties", {})
        
        # Check required parameters
        for param in required:
            if param not in parameters:
                logging.error(f"Missing required parameter {param} for function {name}")
                return False
        
        # Validate parameter types
        for param, value in parameters.items():
            if param not in properties:
                logging.warning(f"Unexpected parameter {param} for function {name}")
                continue
            
            expected_type = properties[param].get("type")
            if expected_type == "array" and not isinstance(value, list):
                logging.error(f"Parameter {param} should be an array")
                return False
            elif expected_type == "string" and not isinstance(value, str):
                logging.error(f"Parameter {param} should be a string")
                return False
            elif expected_type == "object" and not isinstance(value, dict):
                logging.error(f"Parameter {param} should be an object")
                return False
        
        return True
    
    def clear(self):
        """Clear all registered functions and handlers."""
        self.functions.clear()
        self.handlers.clear()
        logging.info("Function Registry cleared") 