# File: backend/app/services/openai/models/base.py
import logging
from typing import Optional, Dict, Any, List
import asyncio

from openai import Async<PERSON>penAI
from app.core.config import settings

class BaseOpenAIClient:
    """Base class for OpenAI API clients."""
    
    def __init__(
        self,
        model: str = settings.OPENAI_CONFIG["model"],
        max_retries: int = settings.OPENAI_CONFIG["max_retries"]
    ):
        """Initialize the client."""
        self.model = model
        self.max_retries = max_retries
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        
        logging.info(f"Initialized OpenAI client with model: {model}")
    
    async def process_with_fallback(
        self,
        messages: List[Dict[str, str]],
        response_format: Optional[Dict[str, str]] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> Dict[str, Any]:
        """Process messages with fallback retry logic."""
        try:
            logging.info("Processing with OpenAI")
            
            # Prepare the API call
            kwargs = {
                "model": self.model,
                "messages": messages,
                "temperature": temperature
            }
            
            if response_format:
                kwargs["response_format"] = response_format
            
            if max_tokens:
                kwargs["max_tokens"] = max_tokens
            
            # Make the API call with retries
            for attempt in range(self.max_retries):
                try:
                    response = await self.client.chat.completions.create(**kwargs)
                    break
                except Exception as e:
                    if attempt == self.max_retries - 1:
                        raise
                    logging.warning(f"OpenAI API call failed (attempt {attempt + 1}): {str(e)}")
                    await asyncio.sleep(1)  # Add a small delay between retries
                    continue
            
            # Extract and return the response
            choice = response.choices[0]
            message = choice.message
            
            return {
                "response": message.content or "",
                "role": message.role
            }
            
        except Exception as e:
            logging.error(f"Error in OpenAI processing: {str(e)}", exc_info=True)
            raise 