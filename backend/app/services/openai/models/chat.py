# File: backend/app/services/openai/models/chat.py

from typing import Optional, Any, Dict, AsyncGenerator, Union, List
from openai.types.chat import ChatCompletion, ChatCompletionChunk
import json
from ..base import BaseOpenAIClient, OpenAIError
from ..config.model_configs import ModelConfigurationManager
from app.core.config import settings
from tests.helpers import log_openai_conversation
import logging
import openai
import asyncio
import random

class ChatClient(BaseOpenAIClient):
    """Client for chat-based interactions with OpenAI models."""
    
    def __init__(self, test_mode: bool = False, use_case: Optional[str] = None, **kwargs):
        super().__init__(use_case=use_case, **kwargs)
        self.test_mode = test_mode
        self.default_model = settings.OPENAI_MODELS_CONFIG["chat"]["default"]
        self.fallback_model = settings.OPENAI_MODELS_CONFIG["chat"]["fallback"]
        openai.api_key = settings.OPENAI_API_KEY
        
        # Log initialization
        logging.debug(f"Initialized ChatClient with use_case={use_case}")
    
    async def process_with_fallback(
        self,
        messages: list[Dict[str, str]],
        response_format: Optional[Dict[str, str]] = None,
        model: Optional[str] = None,
        **kwargs
    ) -> Union[ChatCompletion, AsyncGenerator[ChatCompletionChunk, None]]:
        """
        Process chat with fallback to a simpler model if the primary model fails.
        
        Args:
            messages: List of message dictionaries
            response_format: Optional response format specification
            model: Model to use (defaults to self.default_model)
            **kwargs: Additional parameters for chat completion
        
        Returns:
            ChatCompletion or AsyncGenerator[ChatCompletionChunk] if streaming
        """
        model = model or self.default_model
        
        try:
            # Ensure proper JSON formatting for models that support it
            if response_format and response_format.get("type") == "json_object":
                # Use the json_response use case configuration
                json_config = ModelConfigurationManager.get_model_config(model, "json_response")
                kwargs.update(json_config)
                logging.info(f"Using JSON mode with model: {model}")
            
            # Log request summary
            system_message = next((m["content"][:100] + "..." for m in messages if m["role"] == "system"), "No system message")
            user_message_count = sum(1 for m in messages if m["role"] == "user")
            logging.info(f"Request: model={model}, system={system_message}, user_messages={user_message_count}")
            
            response = await self.create_chat_completion(
                messages=messages,
                model=model,
                **kwargs
            )
            
            # Log the response for debugging
            if hasattr(response, 'choices') and response.choices:
                logging.info(f"Response content: {response.choices[0].message.content[:100]}...")
            
            return response
            
        except OpenAIError as e:
            logging.error(f"OpenAI Error: {str(e)}")
            # If primary model fails, try fallback
            fallback_model = ModelConfigurationManager.get_fallback_model(model)
            if fallback_model and fallback_model != model:
                logging.info(f"Trying fallback model: {fallback_model}")
                return await self.create_chat_completion(
                    messages=messages,
                    model=fallback_model,
                    **kwargs
                )
            raise e
    
    async def process_with_function_calling(
        self,
        messages: List[Dict[str, str]],
        functions: List[Dict[str, Any]],
        model: Optional[str] = None,
        function_call: Optional[Union[str, Dict[str, Any]]] = "auto",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a conversation with function calling capabilities.
        
        Args:
            messages: List of message dictionaries
            functions: List of function definitions
            model: Model to use (defaults to self.default_model)
            function_call: Control function calling behavior
                - "auto": Let the model decide
                - "none": Don't call any functions
                - {"name": "function_name"}: Call a specific function
                - {"type": "function", "function": {"name": "auto"}}: Legacy format
            **kwargs: Additional parameters to pass to the API
            
        Returns:
            ChatCompletion object
        """
        try:
            # Log the request for debugging
            system_message = next((m["content"][:100] + "..." for m in messages if m["role"] == "system"), "No system message")
            user_message_count = sum(1 for m in messages if m["role"] == "user")
            function_count = len(functions) if functions else 0
            
            logging.info(f"Function call request: model={model or self.default_model}, "
                         f"system={system_message[:50]}..., user_messages={user_message_count}, "
                         f"functions={function_count}, function_call={function_call}")
            
            # Get model-specific configuration
            model = model or self.default_model
            
            # Log the functions for debugging
            if functions:
                logging.info(f"Available functions ({len(functions)}):")
                for i, func in enumerate(functions):
                    logging.debug(f"Function {i+1}: {func.get('name', 'unnamed')} - {func.get('description', 'no description')[:100]}")
                    
                    # For email agent, log the full function definition for better debugging
                    if self.use_case == "email_agent":
                        logging.debug(f"Email agent function {func.get('name', 'unnamed')} definition: {json.dumps(func, indent=2)}")
            else:
                logging.warning("No functions provided for function calling")
            
            # Ensure function_call is properly formatted
            if isinstance(function_call, str) and function_call not in ["auto", "none"]:
                logging.warning(f"Invalid function_call value: {function_call}, defaulting to 'auto'")
                function_call = "auto"
            
            # Filter out parameters that are not meant for the OpenAI API
            api_kwargs = {}
            valid_openai_params = [
                "temperature", "max_tokens", "n", 
                "presence_penalty", "frequency_penalty", "logit_bias",
                "user", "stop", "response_format"
            ]
            
            for param in valid_openai_params:
                if param in kwargs:
                    api_kwargs[param] = kwargs[param]
                    logging.debug(f"Using parameter {param}={kwargs[param]}")
            
            # Create the completion
            response = await self.create_chat_completion(
                messages=messages,
                model=model,
                functions=functions,
                function_call=function_call,
                **api_kwargs
            )
            
            # Log the response type
            if response.choices and response.choices[0].message:
                message = response.choices[0].message
                if hasattr(message, 'function_call') and message.function_call:
                    logging.info(f"Function call response: {message.function_call.name}")
                    logging.debug(f"Function arguments: {message.function_call.arguments[:200]}...")
                    
                    # For email agent, log the full function call for better debugging
                    if self.use_case == "email_agent":
                        logging.info(f"Email agent function call: {message.function_call.name}")
                        logging.info(f"Email agent function arguments: {message.function_call.arguments}")
                elif hasattr(message, 'tool_calls') and message.tool_calls:
                    for tool_call in message.tool_calls:
                        if tool_call.type == 'function':
                            logging.info(f"Tool call response: {tool_call.function.name}")
                            logging.debug(f"Tool arguments: {tool_call.function.arguments[:200]}...")
                            
                            # For email agent, log the full tool call for better debugging
                            if self.use_case == "email_agent":
                                logging.info(f"Email agent tool call: {tool_call.function.name}")
                                logging.info(f"Email agent tool arguments: {tool_call.function.arguments}")
                else:
                    # This is expected behavior when the model decides to respond directly
                    # Change from warning to debug level since this is not an error condition
                    logging.debug("No function call detected in message - model responded directly.")
                    if message.content:
                        logging.debug(f"Message content: {message.content[:200]}...")
                        
                        # For email agent, log the full message content for better debugging
                        if self.use_case == "email_agent":
                            logging.info(f"Email agent direct response: {message.content[:200]}...")
                    else:
                        logging.warning("Message has no content")
            else:
                logging.error("Response has no choices or message")
            
            # For testing, log the full conversation
            if self.test_mode:
                log_openai_conversation(messages, response)
            
            return response
            
        except Exception as e:
            logging.error(f"Error in process_with_function_calling: {str(e)}", exc_info=True)
            raise
    
    async def process_with_tool_calling(
        self,
        messages: List[Dict[str, str]],
        tools: List[Dict[str, Any]],
        model: Optional[str] = None,
        tool_choice: Optional[Union[str, Dict[str, Any]]] = "auto",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a conversation with tool calling capabilities (newer OpenAI API).
        
        Args:
            messages: List of message dictionaries
            tools: List of tool definitions 
            model: Model to use (defaults to self.default_model)
            tool_choice: Control tool selection behavior
                - "auto": Let the model decide
                - "none": Don't call any tools
                - {"type": "function", "function": {"name": "tool_name"}}: Call a specific function tool
            **kwargs: Additional parameters to pass to the API
            
        Returns:
            ChatCompletion object
        """
        try:
            # Log the request for debugging
            system_message = next((m["content"][:100] + "..." for m in messages if m["role"] == "system"), "No system message")
            user_message_count = sum(1 for m in messages if m["role"] == "user")
            tool_count = len(tools) if tools else 0
            
            logging.info(f"Tool call request: model={model or self.default_model}, "
                         f"system={system_message[:50]}..., user_messages={user_message_count}, "
                         f"tools={tool_count}, tool_choice={tool_choice}")
            
            # Get model-specific configuration
            model = model or self.default_model
            
            # Log the tools for debugging
            if tools:
                logging.info(f"Available tools ({len(tools)}):")
                for i, tool in enumerate(tools):
                    if tool.get("type") == "function" and "function" in tool:
                        func = tool["function"]
                        logging.debug(f"Tool {i+1}: {func.get('name', 'unnamed')} - {func.get('description', 'no description')[:100]}")
            else:
                logging.warning("No tools provided for tool calling")
            
            # Ensure tool_choice is properly formatted
            if isinstance(tool_choice, str) and tool_choice not in ["auto", "none"]:
                logging.warning(f"Invalid tool_choice value: {tool_choice}, defaulting to 'auto'")
                tool_choice = "auto"
            
            # Filter out parameters that are not meant for the OpenAI API
            api_kwargs = {}
            valid_openai_params = [
                "temperature", "max_tokens", "n", 
                "presence_penalty", "frequency_penalty", "logit_bias",
                "user", "stop", "response_format"
            ]
            
            for param in valid_openai_params:
                if param in kwargs:
                    api_kwargs[param] = kwargs[param]
            
            # Add tools and tool_choice to the parameters
            params = {
                "messages": messages,
                "model": model,
                "tools": tools,
                "tool_choice": tool_choice,
                **api_kwargs
            }
            
            # Call the OpenAI API directly
            try:
                # Use the OpenAI client to create a chat completion
                response = await self.client.chat.completions.create(**params)
                
                # Log success
                logging.info(f"Successful API call with {len(response.choices)} choices")
                
                # Log tool calls if present
                for choice in response.choices:
                    if hasattr(choice.message, 'tool_calls') and choice.message.tool_calls:
                        logging.info(f"Response includes {len(choice.message.tool_calls)} tool calls")
                        for tc in choice.message.tool_calls:
                            if tc.type == "function":
                                func_name = tc.function.name if hasattr(tc.function, 'name') else 'unknown'
                                logging.info(f"Tool call: {func_name}")
                
                return response
            except Exception as e:
                logging.error(f"API error: {str(e)}")
                raise
                
        except Exception as e:
            logging.error(f"Error in process_with_tool_calling: {str(e)}")
            raise
    
    async def stream_chat(
        self,
        messages: list[Dict[str, str]],
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Stream chat responses.
        
        Args:
            messages: List of message dictionaries
            **kwargs: Additional parameters for chat completion
        
        Yields:
            Chunks of the response text
        """
        # Filter out parameters that are not meant for the OpenAI API
        api_kwargs = {}
        valid_openai_params = [
            "temperature", "max_tokens", "n", "top_p",
            "presence_penalty", "frequency_penalty", "logit_bias",
            "user", "stop", "response_format", "model"
        ]
        
        for param in valid_openai_params:
            if param in kwargs:
                api_kwargs[param] = kwargs[param]
        
        # Ensure stream is set to True
        api_kwargs["stream"] = True
        
        stream = await self.process_with_fallback(
            messages=messages,
            **api_kwargs
        )
        
        # Ensure we're working with an async generator
        if not hasattr(stream, '__aiter__'):
            stream = await stream
        
        async for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content

    async def create_chat_completion(
        self,
        messages: List[Dict[str, Any]],
        model: Optional[str] = None,
        functions: Optional[List[Dict[str, Any]]] = None,
        function_call: Optional[Union[str, Dict[str, str]]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[Dict[str, Any], AsyncGenerator[Dict[str, Any], None]]:
        """Create a chat completion with optional function calling and response format"""
        if self.test_mode:
            return await log_openai_conversation(self._create_chat_completion)(
                messages=messages,
                model=model,
                functions=functions,
                function_call=function_call,
                stream=stream,
                **kwargs
            )
        else:
            return await self._create_chat_completion(
                messages=messages,
                model=model,
                functions=functions,
                function_call=function_call,
                stream=stream,
                **kwargs
            )
    
    async def _create_chat_completion(
        self,
        messages: List[Dict[str, Any]],
        model: Optional[str] = None,
        functions: Optional[List[Dict[str, Any]]] = None,
        function_call: Optional[Union[str, Dict[str, str]]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[Dict[str, Any], AsyncGenerator[Dict[str, Any], None]]:
        """Internal method to create chat completion"""
        try:
            # Use default model if none specified
            if model is None:
                model = self.default_model
                
            # Prepare parameters for the parent class method
            params = {
                "messages": messages,
                "model": model,
                "stream": stream,
                **kwargs
            }
            
            # Only include functions and function_call if functions are provided
            if functions:
                params["functions"] = functions
                if function_call:
                    params["function_call"] = function_call
                
            # Call the parent class method with filtered parameters
            return await super().create_chat_completion(**params)
        except Exception as e:
            logging.error(f"Error in _create_chat_completion: {str(e)}")
            raise
