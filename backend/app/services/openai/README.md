# OpenAI Integration Architecture

## Overview
This directory contains the core components for OpenAI integration in the Recruiva platform. The architecture is designed to provide a robust, scalable, and maintainable interface to OpenAI's services, with particular focus on chat completions and function calling capabilities.

## Directory Structure

```
openai/
├── base.py                    # Base OpenAI client with error handling and retry logic
├── chat_completion_service.py # High-level service for chat completions
├── function_registry.py       # Registry for OpenAI function calling
├── models/                    # Model-specific implementations
│   ├── base.py                # Base model class
│   ├── chat.py                # Chat model implementation with fallback logic
│   └── __init__.py
├── processors/                # Content processors
├── prompts/                   # System prompts for different use cases
│   ├── manager.py             # Prompt loading and templating
│   ├── intake_agent.md        # Intake agent prompt
│   ├── interview_agent.md     # Interview agent prompt
│   └── job_posting.md         # Job posting generation prompt
```

## Core Components

### 1. Base Client (`base.py`)
The foundation of OpenAI integration providing:

**Key Features:**
- Error handling with custom exception classes (`OpenAIError`, `RateLimitError`, `TokenLimitError`, `APIError`)
- Retry logic with exponential backoff using the `backoff` library
- Configurable timeouts and rate limiting
- Streaming support
- Model management

**Main Methods:**
- `create_chat_completion()`: Create a chat completion with retry logic and error handling
- `get_models()`: Get available models

### 2. Chat Completion Service (`chat_completion_service.py`)
High-level service for generating completions:

**Key Features:**
- Simplified interface for common use cases
- Function validation
- Logging and error handling

**Main Methods:**
- `generate_completion()`: Generate a completion using the OpenAI API

### 3. Function Registry (`function_registry.py`)
Manages OpenAI function definitions and handlers:

**Key Features:**
- Centralized registry for function definitions
- Dynamic handler registration and retrieval
- Agent-specific function sets

**Main Methods:**
- `register_handler()`: Register a function handler
- `get_handler()`: Get a registered handler
- `get_functions()`: Get functions for a specific agent type

### 4. Models

#### Base Model (`models/base.py`)
Base class for model implementations:

**Key Features:**
- Common model functionality
- Interface definition
- Configuration management

#### Chat Model (`models/chat.py`)
Implements chat-based interactions with OpenAI models:

**Key Features:**
- Function calling support
- Response streaming
- Model fallback mechanisms
- JSON mode support
- Test mode integration

**Main Methods:**
- `process_with_fallback()`: Process chat with fallback to a simpler model if the primary model fails
- `process_with_function_calling()`: Process chat with function calling capabilities
- `stream_chat()`: Stream chat responses
- `create_chat_completion()`: Create a chat completion

### 5. Prompts (`prompts/`)
Contains system prompts and templates for various AI interactions:

**Key Components:**
- `manager.py`: Prompt loading and templating
- Various prompt files for different use cases

**Main Methods:**
- `load_prompt()`: Load a prompt from a file and format it with context

## Configuration

### Environment Variables
```env
# OpenAI Configuration
OPENAI_API_KEY="your-api-key"
OPENAI_ORG_ID="your-org-id"  # Optional
OPENAI_TIMEOUT_SECONDS=30
OPENAI_MAX_RETRIES=3

# Model Configuration
OPENAI_DEFAULT_MODEL="gpt-4o"
OPENAI_FALLBACK_MODEL="gpt-4o-mini"
```

### Model Configuration
```python
OPENAI_MODELS_CONFIG = {
    "chat": {
        "default": "gpt-4o",
        "fallback": "gpt-4o-mini"
    }
}
```

## Usage Examples

### Basic Chat Completion
```python
from app.services.openai.chat_completion_service import ChatCompletionService

response = await ChatCompletionService.generate_completion(
    prompt="You are a helpful assistant",
    model="gpt-4o"
)
```

### Function Calling
```python
from app.services.openai.models.chat import ChatClient
from app.services.openai.function_registry import FunctionRegistry

# Get functions from registry
functions = FunctionRegistry.get_functions("realtime")

# Create chat client
chat_client = ChatClient()

# Process with function calling
response = await chat_client.process_with_function_calling(
    messages=[
        {"role": "system", "content": "You are a helpful assistant"},
        {"role": "user", "content": "Create a new role for a Senior Developer"}
    ],
    functions=functions,
    model="gpt-4o"
)
```

### Streaming Response
```python
from app.services.openai.models.chat import ChatClient

chat_client = ChatClient()
async for chunk in chat_client.stream_chat(
    messages=[
        {"role": "system", "content": "You are a helpful assistant"},
        {"role": "user", "content": "Tell me a story"}
    ],
    model="gpt-4o"
):
    print(chunk, end="")
```

## Error Handling
The integration implements comprehensive error handling:

1. **Rate Limiting**
   - Automatic retry with exponential backoff
   - Rate limit error detection and handling

2. **Token Limits**
   - Token limit monitoring
   - Automatic fallback to simpler models

3. **API Errors**
   - Detailed error logging
   - Custom error classes for different scenarios
   - Graceful degradation

## Integration with Realtime Agents

The OpenAI integration is designed to work seamlessly with the realtime agent architecture:

1. **Agent-Specific Functions**
   - The `FunctionRegistry` provides agent-specific function sets
   - Functions are registered centrally and retrieved dynamically

2. **Agent-Specific Prompts**
   - The `PromptManager` loads prompts from files
   - Prompts can be customized for each agent type

3. **Dynamic Agent Loading**
   - The `AgentLoader` dynamically loads agent components
   - Agents can use the OpenAI integration with minimal configuration

## Best Practices

1. **Model Usage**
   - Use the `ChatCompletionService` for simple completions
   - Use the `ChatClient` for advanced use cases
   - Implement proper error handling
   - Use streaming for long responses
   - Set appropriate timeouts

2. **Function Calling**
   - Register functions in the `FunctionRegistry`
   - Implement handlers for each function
   - Validate parameters
   - Use type hints

3. **Prompt Management**
   - Store prompts as separate files
   - Use the `PromptManager` to load prompts
   - Format prompts with context
   - Keep prompts modular and reusable

## Dependencies
- `openai>=1.59.6`
- `pydantic>=2.10.5`
- `backoff>=2.2.1` 