"""
Standardized error types for OpenAI service interactions.
Provides a comprehensive set of error classes for better error handling and reporting.
"""

from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class OpenAIError(Exception):
    """Base exception class for OpenAI-related errors."""
    def __init__(self, message: str, status_code: Optional[int] = None, 
                 request_id: Optional[str] = None, context: Optional[Dict[str, Any]] = None):
        self.message = message
        self.status_code = status_code
        self.request_id = request_id
        self.context = context or {}
        super().__init__(self.message)
        
        # Log the error with context
        self._log_error()
    
    def _log_error(self):
        """Log the error with appropriate context."""
        log_data = {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "status_code": self.status_code,
            "request_id": self.request_id,
            **self.context
        }
        logger.error(f"OpenAI API Error: {log_data}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for API responses."""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "status_code": self.status_code,
            "request_id": self.request_id
        }


class RateLimitError(OpenAIError):
    """Raised when OpenAI rate limits are hit."""
    pass


class TokenLimitError(OpenAIError):
    """Raised when token limits are exceeded."""
    pass


class InvalidRequestError(OpenAIError):
    """Raised for invalid request parameters."""
    pass


class AuthenticationError(OpenAIError):
    """Raised for authentication issues."""
    pass


class PermissionError(OpenAIError):
    """Raised for permission issues."""
    pass


class APIConnectionError(OpenAIError):
    """Raised for network connection issues."""
    pass


class ServiceUnavailableError(OpenAIError):
    """Raised when the OpenAI service is unavailable."""
    pass


class TimeoutError(OpenAIError):
    """Raised when a request times out."""
    pass


class ModelNotAvailableError(OpenAIError):
    """Raised when the requested model is not available."""
    pass


class ContentFilterError(OpenAIError):
    """Raised when content is filtered by OpenAI's content filter."""
    pass


class FunctionCallError(OpenAIError):
    """Raised when there's an error with function calling."""
    pass


def map_openai_error(error: Exception, context: Optional[Dict[str, Any]] = None) -> OpenAIError:
    """
    Map an OpenAI API exception to our custom error types.
    
    Args:
        error: The original OpenAI exception
        context: Additional context information
        
    Returns:
        An instance of our custom OpenAIError subclass
    """
    import openai
    
    error_msg = str(error)
    error_context = context or {}
    
    # Extract status code and request ID if available
    status_code = getattr(error, "status_code", None)
    request_id = getattr(error, "request_id", None)
    
    # Map to specific error types
    if isinstance(error, openai.RateLimitError):
        return RateLimitError(error_msg, status_code, request_id, error_context)
    elif isinstance(error, openai.BadRequestError):
        if "maximum context length" in error_msg.lower():
            return TokenLimitError(error_msg, status_code, request_id, error_context)
        return InvalidRequestError(error_msg, status_code, request_id, error_context)
    elif isinstance(error, openai.AuthenticationError):
        return AuthenticationError(error_msg, status_code, request_id, error_context)
    elif isinstance(error, openai.PermissionDeniedError):
        return PermissionError(error_msg, status_code, request_id, error_context)
    elif isinstance(error, openai.APIConnectionError):
        return APIConnectionError(error_msg, status_code, request_id, error_context)
    elif isinstance(error, openai.APITimeoutError):
        return TimeoutError(error_msg, status_code, request_id, error_context)
    elif isinstance(error, openai.APIError):
        if "currently overloaded" in error_msg.lower():
            return ServiceUnavailableError(error_msg, status_code, request_id, error_context)
    
    # Default to base OpenAIError for unrecognized errors
    return OpenAIError(error_msg, status_code, request_id, error_context) 