"""
Enhanced chat completion service with improved error handling and model configuration.
"""

from typing import Dict, Any, List, Optional, Union, AsyncGenerator
import logging
from .models.chat import ChatClient
from .config.model_configs import ModelConfigurationManager
from .config.error_types import OpenAIError

logger = logging.getLogger(__name__)

class ChatCompletionService:
    """
    Service for generating chat completions with OpenAI models.
    Uses model-specific configurations and enhanced error handling.
    """
    
    @staticmethod
    async def generate_completion(
        prompt: str, 
        functions: Optional[List[Dict[str, Any]]] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        model: str = "gpt-4o", 
        use_case: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        function_call: Optional[Union[str, Dict[str, Any]]] = "auto",
        tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a completion using the OpenAI API with model-specific configuration.
        
        Args:
            prompt: The prompt to send to the model
            functions: Optional list of function definitions (legacy parameter)
            tools: Optional list of tool definitions (new parameter for OpenAI API)
            model: The model to use
            use_case: Optional use case for model configuration
            context: Optional context variables for prompt formatting
            function_call: Control function calling behavior (legacy parameter)
            tool_choice: Control tool selection behavior (new parameter for OpenAI API)
            **kwargs: Additional parameters to pass to the API
            
        Returns:
            The response from the model
        """
        try:
            logger.info(f"Generating completion with model: {model}, use_case: {use_case}")
            
            # Create chat client with use case
            chat_client = ChatClient(use_case=use_case)
            
            # Log which style of parameters we're using (tools or functions)
            if tools and functions:
                logger.warning("Both tools and functions provided, using tools (newer API)")
                functions = None  # Prefer tools over functions if both are provided
            
            # Validate tools and functions
            if tools:
                logger.info(f"Using tools API with {len(tools)} tools")
                # Validate tools format
                for i, tool in enumerate(tools):
                    if not isinstance(tool, dict):
                        logger.error(f"Tool {i} is not a dictionary: {tool}")
                        raise ValueError(f"Tool {i} is not a dictionary")
                    if "type" not in tool:
                        logger.error(f"Tool {i} is missing 'type' key: {tool}")
                        raise ValueError(f"Tool {i} is missing 'type' key")
                    if tool.get("type") == "function" and "function" not in tool:
                        logger.error(f"Function tool {i} is missing 'function' key: {tool}")
                        raise ValueError(f"Function tool {i} is missing 'function' key")
            elif functions:
                logger.info(f"Using legacy functions API with {len(functions)} functions")
                # Validate functions format
                for i, func in enumerate(functions):
                    if not isinstance(func, dict):
                        logger.error(f"Function {i} is not a dictionary: {func}")
                        raise ValueError(f"Function {i} is not a dictionary")
                    if "name" not in func:
                        logger.error(f"Function {i} is missing 'name' key: {func}")
                        raise ValueError(f"Function {i} is missing 'name' key")
            
            # Format the prompt with context if provided
            formatted_prompt = prompt
            if context and use_case:
                # Import here to avoid circular imports
                from .prompts.manager import PromptManager
                try:
                    # Try to load and format the prompt with the provided context
                    formatted_prompt = PromptManager.load_prompt(use_case, context=context)
                    logger.info(f"Formatted prompt for use case: {use_case}")
                except Exception as e:
                    logger.warning(f"Error formatting prompt with context: {str(e)}. Using original prompt.")
            
            # Get model-specific configuration
            model_config = ModelConfigurationManager.get_model_config(model, use_case)
            
            # Filter out parameters that are not meant for the OpenAI API
            api_kwargs = {}
            valid_openai_params = [
                "temperature", "max_tokens", "n", "top_p",
                "presence_penalty", "frequency_penalty", "logit_bias",
                "user", "stop", "response_format"
            ]
            
            # Start with model-specific configuration
            for param in valid_openai_params:
                if param in model_config:
                    api_kwargs[param] = model_config[param]
            
            # Override with explicitly provided parameters
            for param in valid_openai_params:
                if param in kwargs and kwargs[param] is not None:
                    api_kwargs[param] = kwargs[param]
            
            # Remove context from kwargs if present to avoid passing it to the API
            if 'context' in kwargs:
                del kwargs['context']
                
            # Decide which method to call based on whether tools or functions are provided
            if tools:
                logger.info("Calling process_with_tool_calling method")
                response = await chat_client.process_with_tool_calling(
                    messages=[{"role": "system", "content": formatted_prompt}],
                    tools=tools,
                    model=model,
                    tool_choice=tool_choice,
                    **api_kwargs
                )
            else:
                logger.info("Calling process_with_function_calling method")
                response = await chat_client.process_with_function_calling(
                    messages=[{"role": "system", "content": formatted_prompt}],
                    functions=functions,
                    model=model,
                    function_call=function_call,
                    **api_kwargs
                )
            
            logger.info("Successfully generated completion")
            return response
        except OpenAIError as e:
            logger.exception(f"Error generating completion: {str(e)}")
            raise
        except Exception as e:
            logger.exception(f"Unexpected error generating completion: {str(e)}")
            raise
    
    @staticmethod
    async def generate_streaming_completion(
        prompt: str,
        model: str = "gpt-4o",
        use_case: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Generate a streaming completion using the OpenAI API.
        
        Args:
            prompt: The prompt to send to the model
            model: The model to use
            use_case: Optional use case for model configuration
            **kwargs: Additional parameters to pass to the API
            
        Returns:
            AsyncGenerator yielding response chunks
        """
        try:
            logger.info(f"Generating streaming completion with model: {model}, use_case: {use_case}")
            
            # Create chat client with use case
            chat_client = ChatClient(use_case=use_case)
            
            # Get model-specific configuration
            model_config = ModelConfigurationManager.get_model_config(model, use_case)
            
            # Filter out parameters that are not meant for the OpenAI API
            api_kwargs = {}
            valid_openai_params = [
                "temperature", "max_tokens", "n", "top_p",
                "presence_penalty", "frequency_penalty", "logit_bias",
                "user", "stop", "response_format"
            ]
            
            # Start with model-specific configuration
            for param in valid_openai_params:
                if param in model_config:
                    api_kwargs[param] = model_config[param]
            
            # Override with explicitly provided parameters
            for param in valid_openai_params:
                if param in kwargs and kwargs[param] is not None:
                    api_kwargs[param] = kwargs[param]
            
            # Force streaming
            api_kwargs["stream"] = True
            
            async for chunk in chat_client.stream_chat(
                messages=[{"role": "system", "content": prompt}],
                model=model,
                **api_kwargs
            ):
                yield chunk
                
        except OpenAIError as e:
            logger.exception(f"Error generating streaming completion: {str(e)}")
            raise
        except Exception as e:
            logger.exception(f"Unexpected error generating streaming completion: {str(e)}")
            raise 