# Function Registry Documentation

## Overview

The Function Registry is a core component of Recruiva's OpenAI integration that manages function definitions and handlers for OpenAI's function calling capability. It provides a centralized registry for function definitions, dynamic handler registration and retrieval, and agent-specific function sets.

## Architecture

### Core Components

#### 1. Function Definitions
- Structured JSON schemas for OpenAI function calling
- Organized by agent type (e.g., email_agent, realtime, chat)
- Parameter definitions with validation rules

#### 2. Function Handlers
- Handler registration and retrieval
- Mapping between function names and implementation functions
- Support for dynamic handler registration

#### 3. Function Selection
- Context-aware function selection
- Agent-specific function sets
- Function filtering based on context

### Implementation Details

#### Function Registry Class
The `FunctionRegistry` class is implemented as a static class with class methods:

```python
class FunctionRegistry:
    """
    Enhanced function registry with granular function loading.
    Supports selective function loading based on context.
    """
    
    # Function definitions by category
    _function_definitions = {
        "realtime": {
            "update_role": {
                "description": "Update role details",
                "parameters": {}
            },
            # More functions...
        },
        "chat": {
            "generate_job_posting": {
                "description": "Generate a job posting",
                "parameters": {}
            }
        },
        "email_agent": {
            "create_role": {
                "description": "Create a new role",
                "parameters": {}
            },
            # More functions...
        }
    }
    
    # Function handlers
    _handlers = {}
```

#### Handler Registration
Handlers are registered using the `register_handler` method:

```python
@classmethod
def register_handler(cls, function_name: str, handler: Callable) -> None:
    """
    Register a handler for a function.
    
    Args:
        function_name: Name of the function
        handler: Function handler
    """
    cls._handlers[function_name] = handler
    logger.info(f"Registered handler for function: {function_name}")
```

#### Function Retrieval
Functions are retrieved using the `get_functions` method:

```python
@classmethod
def get_functions(cls, agent_type: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Get functions for a specific agent type and context.
    
    Args:
        agent_type: Type of agent
        context: Optional context for function selection
        
    Returns:
        Dictionary of function definitions
    """
    context = context or {}
    
    # Start with functions for the specific agent type
    functions = {}
    if agent_type in cls._function_definitions:
        functions.update(cls._function_definitions[agent_type])
    
    # Add general functions if available
    if "realtime" in cls._function_definitions and agent_type != "realtime":
        # Only add functions that don't already exist
        for name, definition in cls._function_definitions["realtime"].items():
            if name not in functions:
                functions[name] = definition
    
    # Filter functions based on context if needed
    if context.get("filter_functions"):
        filter_list = context["filter_functions"]
        functions = {name: definition for name, definition in functions.items() if name in filter_list}
    
    logger.info(f"Loaded {len(functions)} functions for agent type: {agent_type}")
    return functions
```

## Usage

### Registering Function Definitions

```python
# In AIService initialization
def __init__(self):
    # Initialize function registry
    self.function_registry = FunctionRegistry()
    
    # Register function definitions
    self.function_registry.register_function(
        category="email_agent",
        name="create_role",
        description="Create a new role",
        parameters={
            "type": "object",
            "properties": {
                "title": {"type": "string", "description": "Role title"},
                "summary": {"type": "string", "description": "Brief summary of the role"},
                # Additional parameters...
            },
            "required": ["title", "summary"]
        }
    )
```

### Registering Function Handlers

```python
# In AIService initialization
def __init__(self):
    # Register function handlers
    self.function_registry.register_handler("create_role", self._handle_role_creation)
    self.function_registry.register_handler("get_roles", self._handle_get_roles)
    self.function_registry.register_handler("get_role_details", self._handle_get_role_details)
    self.function_registry.register_handler("update_role", self._handle_role_update)
```

### Getting Functions for an Agent

```python
# In AIService.process_email
functions = self.function_registry.get_functions("email_agent")

# Use the functions in OpenAI API call
response = await chat_client.process_with_function_calling(
    messages=messages,
    functions=functions,
    model=email_model,
    function_call="auto"
)
```

### Implementing Function Handlers

```python
async def _handle_role_creation(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle role creation function call."""
    # Extract role data from parameters
    role_data = {k: v for k, v in params.items() if k not in ['metadata', 'user_id']}
    
    # Extract user_id and metadata
    user_id = params.get('user_id')
    metadata = params.get('metadata', {})
    
    # Create the role using RolesService
    role = await self.roles_service.create_role(role_data)
    
    # Return success response
    return {
        'status': 'success',
        'role': role,
        'role_id': role.get('id'),
        'message': f'Role created successfully'
    }
```

## Function Calling Flow

1. **Function Definition**: Functions are defined with JSON schemas
2. **Handler Registration**: Handlers are registered for each function
3. **Function Selection**: Functions are selected based on agent type and context
4. **API Call**: Functions are passed to the OpenAI API
5. **Function Execution**: The model selects a function to call
6. **Handler Execution**: The corresponding handler is executed
7. **Response Processing**: The handler's response is processed

## Best Practices

1. **Function Definition**
   - Use clear and descriptive function names
   - Define parameters with proper types and descriptions
   - Include required parameters
   - Use consistent naming conventions

2. **Handler Implementation**
   - Validate parameters before execution
   - Handle errors gracefully
   - Return structured responses
   - Log function calls and results

3. **Function Selection**
   - Only include relevant functions for each agent
   - Use context to filter functions when appropriate
   - Avoid function name collisions across agent types

## Troubleshooting

### Common Issues

1. **Function Not Called**
   - Check that the function is registered correctly
   - Verify that the function is included in the agent's function set
   - Ensure the function schema is valid
   - Check that the model has enough context to decide to call the function

2. **Handler Not Found**
   - Verify that the handler is registered with the correct function name
   - Check for typos in function names
   - Ensure the handler is registered before it's needed

3. **Parameter Validation Errors**
   - Validate parameters before using them
   - Provide default values for optional parameters
   - Handle missing parameters gracefully 