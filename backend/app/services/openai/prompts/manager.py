"""
Enhanced prompt management system with versioning and template support.
"""

import os
import yaml
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from string import Template

logger = logging.getLogger(__name__)

class PromptManager:
    """
    Enhanced prompt manager that supports:
    - Versioned prompts
    - Template variables
    - Conditional sections
    - Reusable components
    """
    
    PROMPT_DIR = os.path.join(os.path.dirname(__file__))
    
    @staticmethod
    def load_prompt(prompt_name: str, context: Dict[str, Any] = None, 
                    version: Optional[str] = None) -> str:
        """
        Load a prompt with optional versioning and apply context variables.
        
        Args:
            prompt_name: Name of the prompt file or directory
            context: Dictionary of variables to substitute in the prompt
            version: Optional version string (e.g., "v1", "v2")
            
        Returns:
            Formatted prompt string
        """
        context = context or {}
        
        # Determine the prompt path
        if version:
            # Look for versioned prompt first
            versioned_path = os.path.join(PromptManager.PROMPT_DIR, f"{prompt_name}_{version}.prompt")
            if os.path.exists(versioned_path):
                prompt_path = versioned_path
            else:
                logger.warning(f"Versioned prompt {prompt_name}_{version} not found, falling back to default")
                prompt_path = os.path.join(PromptManager.PROMPT_DIR, f"{prompt_name}.prompt")
        else:
            prompt_path = os.path.join(PromptManager.PROMPT_DIR, f"{prompt_name}.prompt")
        
        # Check if the path exists
        if not os.path.exists(prompt_path):
            # Try without .prompt extension for backward compatibility
            alt_path = os.path.join(PromptManager.PROMPT_DIR, prompt_name)
            if os.path.exists(alt_path):
                prompt_path = alt_path
                logger.warning(f"Using legacy prompt file without .prompt extension: {prompt_path}")
            else:
                raise FileNotFoundError(f"Prompt not found: {prompt_path} or {alt_path}")
        
        # Load the prompt
        try:
            with open(prompt_path, 'r', encoding='utf-8') as file:
                content = file.read()
                
                # Check if it's a YAML file
                if prompt_path.endswith('.yaml') or prompt_path.endswith('.yml'):
                    prompt_data = yaml.safe_load(content)
                    if isinstance(prompt_data, dict):
                        # Extract the prompt from the YAML structure
                        if 'system_prompt' in prompt_data:
                            content = prompt_data['system_prompt']
                        elif 'prompt' in prompt_data:
                            content = prompt_data['prompt']
                        else:
                            # Use the first value as the prompt
                            content = next(iter(prompt_data.values()))
                
                # Format the prompt with context variables
                return PromptManager._format_prompt(content, context)
        except Exception as e:
            logger.error(f"Error loading prompt {prompt_name}: {str(e)}")
            raise
    
    @staticmethod
    def _format_prompt(template: str, context: Dict[str, Any]) -> str:
        """
        Format a prompt template with context variables.
        
        Args:
            template: The prompt template string
            context: Dictionary of variables to substitute
            
        Returns:
            Formatted prompt string
        """
        try:
            # Add some standard variables
            full_context = {
                "timestamp": datetime.now().isoformat(),
                "date": datetime.now().strftime("%Y-%m-%d"),
                **context
            }
            
            # Use string.Template for safer substitution with $ placeholders
            template_obj = Template(template)
            return template_obj.safe_substitute(full_context)
        except KeyError as e:
            logger.warning(f"Missing context variable in prompt: {str(e)}")
            return template
        except Exception as e:
            logger.error(f"Error formatting prompt: {str(e)}")
            return template
    
    @staticmethod
    def list_available_prompts() -> List[str]:
        """
        List all available prompts.
        
        Returns:
            List of prompt names
        """
        prompts = []
        for item in os.listdir(PromptManager.PROMPT_DIR):
            # Skip Python files and directories
            if item.endswith('.py') or item == '__pycache__':
                continue
            
            # Handle .prompt files
            if item.endswith('.prompt'):
                # Remove the .prompt extension
                base_name = os.path.splitext(item)[0]
                prompts.append(base_name)
            else:
                # Add legacy files as is
                prompts.append(item)
        
        return prompts
    
    @staticmethod
    def get_prompt_versions(prompt_name: str) -> List[str]:
        """
        Get all available versions of a prompt.
        
        Args:
            prompt_name: Base name of the prompt
            
        Returns:
            List of version strings
        """
        versions = []
        for item in os.listdir(PromptManager.PROMPT_DIR):
            # Check for versioned prompts with .prompt extension
            if item.endswith('.prompt') and item.startswith(f"{prompt_name}_v"):
                version = item.replace(f"{prompt_name}_", "").replace(".prompt", "")
                versions.append(version)
            # Check for legacy versioned prompts without extension
            elif item.startswith(f"{prompt_name}_v"):
                version = item.replace(f"{prompt_name}_", "")
                versions.append(version)
        
        return versions 