# Email Agent Documentation

## Overview

The Email Agent is a specialized AI agent that processes email communications for Recruiva. It uses OpenAI's function calling capabilities to understand user intents from emails and execute appropriate actions, such as creating roles, updating roles, and retrieving role information.

## Architecture

### Core Components

#### 1. System Prompt
The email agent uses a detailed system prompt (`email_agent.prompt`) that defines:
- The agent's personality and tone
- Common user intents and how to identify them
- Required information for different operations
- Response templates for various scenarios
- JSON schema for role data
- Data validation rules
- Function calling instructions

#### 2. Function Registry
The agent uses the `FunctionRegistry` to define available functions:
- `create_role`: Create a new role
- `update_role`: Update an existing role
- `get_roles`: List roles with optional filtering
- `get_role_details`: Get detailed information about a specific role

#### 3. AI Service Integration
The `AIService` processes emails by:
- Formatting the email content with context
- Loading the email agent prompt
- Calling the OpenAI API with function definitions
- Processing the response and executing functions
- Generating appropriate email responses

### Implementation Details

#### System Prompt Structure
The system prompt is structured into sections:
1. **Understanding User Needs**: Guidelines for identifying user intent
2. **Gathering the Right Details**: Required information for different operations
3. **Response Examples**: Templates for different scenarios
4. **Output JSON Schema**: Schema for role data
5. **Data Validation**: Rules for standardizing values
6. **Function Calling Instructions**: Guidelines for when to use each function

#### Function Calling Flow
1. The email content is sent to the OpenAI API with the system prompt
2. The model identifies the user's intent and required information
3. The model decides which function to call (if any)
4. The function is executed with the extracted parameters
5. The result is used to generate a response email

#### Email Processing Pipeline
```
Email → EmailService → AIService → OpenAI API → Function Execution → Response Generation → Email Response
```

## Usage

### System Prompt Configuration

The system prompt can be customized to adjust:
- The agent's tone and personality
- Response templates
- Required information for different operations
- Data validation rules

### Function Registry Configuration

Functions can be added or modified in the `FunctionRegistry`:

```python
function_registry.register_function(
    category="email_agent",
    name="create_role",
    description="Create a new role",
    parameters={
        "type": "object",
        "properties": {
            "title": {"type": "string", "description": "Role title"},
            "summary": {"type": "string", "description": "Brief summary of the role"},
            # Additional parameters...
        },
        "required": ["title", "summary"]
    }
)
```

### Function Handler Implementation

Function handlers are implemented in the `AIService`:

```python
async def _handle_role_creation(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle role creation function call."""
    # Extract role data from parameters
    role_data = {k: v for k, v in params.items() if k not in ['metadata', 'user_id']}
    
    # Extract user_id and metadata
    user_id = params.get('user_id')
    
    # Create the role using RolesService
    role = await self.roles_service.create_role(role_data)
    
    # Return success response
    return {
        'status': 'success',
        'role': role,
        'role_id': role.get('id'),
        'message': f'Role created successfully'
    }
```

## Examples

### Example 1: Creating a Role

**User Email:**
```
Subject: New Role Request
Body: Hi, I need to create a new role for a Senior Software Engineer. This person will be responsible for leading our backend development team. They should have at least 5 years of experience with Python and cloud technologies.
```

**Agent Processing:**
1. Identifies intent: Create a new role
2. Extracts information:
   - Title: Senior Software Engineer
   - Summary: Leading backend development team
   - Required Skills: Python, cloud technologies
   - Years of Experience: 5+ years
3. Calls `create_role` function with extracted parameters
4. Generates a response with the role details and a link to view the role

**Response Email:**
```
Subject: Role Created: Senior Software Engineer
Body: 
Hi [User],

Fantastic news! I've just set up the Senior Software Engineer position. Here's a quick overview:

## 📍 Position Details
- Level: Senior (5+ years experience)
- Team: Backend Development

## ✨ Key Responsibilities
- Leading the backend development team
- Working with Python and cloud technologies

Ready to take a closer look? You can view all the details and make any tweaks here: [View Role Details](https://recruiva.ai/roles/role123)

Let me know if you'd like to make any adjustments!

Best wishes,
Recruiva AI
```

### Example 2: Getting Role Information

**User Email:**
```
Subject: Show my roles
Body: Can you show me all my active roles?
```

**Agent Processing:**
1. Identifies intent: List roles
2. Extracts filters: Status = Active
3. Calls `get_roles` function with filters
4. Generates a response listing the roles

**Response Email:**
```
Subject: Your Roles (3 found)
Body:
Hi [User],

I found 3 active roles. Here they are:

## Senior Software Engineer
**Status:** Active
**Team:** Backend Development
**Created:** March 8, 2024

Leading the backend development team with expertise in Python and cloud technologies.

[View Role Details](https://recruiva.ai/roles/role123)

---

## Product Manager
**Status:** Active
**Team:** Product
**Created:** March 5, 2024

Overseeing product development and working with cross-functional teams.

[View Role Details](https://recruiva.ai/roles/role456)

---

## UX Designer
**Status:** Active
**Team:** Design
**Created:** March 1, 2024

Creating user-centered designs and improving user experience.

[View Role Details](https://recruiva.ai/roles/role789)

Let me know if you'd like to:
- Create a new role
- Search with different criteria
- Get more details about any specific role

Best wishes,
Recruiva AI
```

## Best Practices

1. **Prompt Engineering**
   - Keep the system prompt clear and structured
   - Use examples to guide the model's behavior
   - Regularly update the prompt based on user interactions

2. **Function Calling**
   - Define clear function signatures
   - Validate parameters before execution
   - Handle errors gracefully
   - Return structured responses

3. **Response Generation**
   - Use consistent formatting
   - Include all relevant information
   - Provide clear next steps
   - Maintain a friendly and professional tone

## Troubleshooting

### Common Issues

1. **Function Not Called**
   - Check the system prompt for clear function calling instructions
   - Ensure the user's intent is clearly identifiable
   - Verify that all required parameters are extractable from the email

2. **Incorrect Parameter Extraction**
   - Improve the system prompt with clearer guidelines
   - Add more examples of parameter extraction
   - Implement validation in the function handler

3. **Response Format Issues**
   - Update the response templates in the system prompt
   - Ensure the function returns all necessary data
   - Implement fallback templates for error cases 