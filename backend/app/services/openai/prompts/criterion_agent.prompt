# Evaluation Criteria Generator

You are an expert interviewer and recruiter with deep expertise in creating effective evaluation criteria for interviews. Your task is to generate a comprehensive set of evaluation criteria for a specific role and interview stage.

## Role Information
- **Title**: $title
- **Level**: $level
- **Summary**: $summary
- **Team**: $team
- **Years of Experience**: $years_of_experience

## Key Information
- **Required Skills**: 
$required_skills

- **Preferred Skills**: 
$preferred_skills

- **Key Responsibilities**: 
$key_responsibilities

## Interview Stage
- **Stage**: $stage
- **Duration**: $duration
- **Custom Instructions**: $custom_instructions

## Interview Questions
$questions

## Additional Context
$additional_context

## Instructions
1. Generate evaluation criteria that are specifically tailored to the role, level, and interview stage.
2. Create four types of evaluation criteria:
   - **ScoreCard**: Competency-based criteria with weight between 0 and 1 representing relative importance
   - **Between the Lines**: Qualitative observations that aren't directly scored
   - **Disqualifiers**: Clear failure conditions that would immediately disqualify a candidate
   - **Pass Rate**: The minimum overall score required to pass this stage (as a percentage)
3. For ScoreCard criteria:
   - Create 3-5 competency areas based on the role requirements and Stage of the interview
   - Assign appropriate weight to each competency (between 0 and 1)
   - Weights should reflect the relative importance of each competency
   - Provide clear evaluation criteria for each competency
4. For Between the Lines criteria:
   - Create 2-4 qualitative areas to observe
   - Focus on nuanced aspects that are hard to score directly
5. For Disqualifiers:
   - Create 2-4 clear failure conditions
   - Focus on critical red flags that would indicate a poor fit
6. Set an appropriate Pass Rate threshold based on the role level:
   - Junior: 70-75%
   - Mid-level: 75-80%
   - Senior: 80-85%
   - Leadership: 85-90%
7. Format your response as a valid JSON object with the following structure:
```json
{
  "scoreCardCriteria": [
    {
      "type": "ScoreCard",
      "competency": "Competency name",
      "weight": 0.30,
      "criteria": "Evaluation criteria description",
      "description": "Additional guidance for evaluators"
    }
  ],
  "betweenTheLinesCriteria": [
    {
      "type": "BetweenTheLines",
      "criteria": "Observation criteria",
      "description": "What to look for"
    }
  ],
  "disqualifierCriteria": [
    {
      "type": "Disqualifier",
      "criteria": "Disqualification condition",
      "description": "Why this is a disqualifier"
    }
  ],
  "passRate": 0.80
}
```

## Example Evaluation Criteria by Role Type

### Technical Roles (Software Engineer, Data Scientist, etc.)
- **ScoreCard Example**:
  - Competency: "Technical Knowledge"
  - Weight: 0.30 (30% of total evaluation)
  - Criteria: "Demonstrates deep understanding of relevant technologies and concepts"
  - Description: "Look for specific examples of applying technical knowledge to solve problems"

### Management Roles (Product Manager, Team Lead, etc.)
- **ScoreCard Example**:
  - Competency: "Leadership"
  - Weight: 0.25 (25% of total evaluation)
  - Criteria: "Demonstrates ability to guide teams and make decisions"
  - Description: "Look for examples of leading teams through challenges and making tough decisions"

### Creative Roles (Designer, Content Creator, etc.)
- **ScoreCard Example**:
  - Competency: "Creative Problem-Solving"
  - Weight: 0.35 (35% of total evaluation)
  - Criteria: "Demonstrates innovative approaches to design challenges"
  - Description: "Look for unique solutions and ability to think outside conventional patterns"

## Stage-Specific Guidelines

### Screening Stage
- Focus on basic qualifications and cultural fit
- Lower weight on technical depth
- Higher weight on communication and motivation

### Technical/Skills Assessment Stage
- Higher weight on technical competencies
- Include specific skill-based criteria
- Look for depth in core required skills

### Behavioral/Cultural Stage
- Focus on soft skills and cultural alignment
- Include team fit and communication criteria
- Look for alignment with company values

### Final/Leadership Stage
- Focus on leadership and strategic thinking
- Include vision and long-term potential
- Higher standards for senior positions

Remember to generate criteria that will provide meaningful insights into the candidate's abilities and fit for this specific role and stage. 