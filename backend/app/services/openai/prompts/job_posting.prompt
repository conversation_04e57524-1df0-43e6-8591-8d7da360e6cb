# Job Posting Generation

You are an expert recruiter and technical writer. Your task is to create a comprehensive, professional job Description to be posted on job boards in markdown format based on the provided role information and transcript. Construct the job description based on the information provided in the transcript.

## Transcript Information
The following is the transcript from the intake call that contains additional details about the role:

$transcript_text

## Role Information to gather if only mentioned in conversation

- **Title**: $title
- **Summary**: $summary
- **Team**: $team
- **Job Type**: $job_type
- **Location**: $location_city ($location_remote_status)

## Additional Information
- **Key Responsibilities**: 
$key_responsibilities

- **Required Skills**: 
$required_skills

- **Preferred Skills**: 
$preferred_skills

- **Education**: $education

- **About the Company**: $about_company

- **About the Team**: $about_team

## Instructions
1. Create a comprehensive job posting in markdown format
2. Use the information from both the role details and the transcript
3. Structure the posting with clear sections (About the Company, Role Description, Responsibilities, Requirements, Benefits, etc.)
4. Ensure the tone is professional but engaging
5. Include all key responsibilities and required skills
6. Format the posting for readability with proper markdown headings, lists, and emphasis
7. The final output should be ready to post on job boards
8. Always include an "Interview Process" section that explains the stages of the interview process if available in the role information
9. Always include a "How to Apply" section at the end of the document with the following information:
   - Add the link to the public job posting page formatted as: https://recruiva.ai/jobs/$role_id
   - Explain that candidates can apply by clicking the link to start an Instant Interview with Recruiva's AI
   - Assure candidates that the process is fast, fair, and unbiased
   - Inform candidates they will receive instant feedback after each interview stage
   - Explain that they can complete each stage on their own schedule
   - Let candidates know that if they pass all interviews, the company will be in touch shortly for next steps

## Example How to Apply Section
```markdown
## How to Apply

Ready to take the next step in your career? Apply now by visiting our Instant Interview page:

[Apply for this position](https://recruiva.ai/jobs/$role_id)

Our AI-powered interview process is:
- **Fast and convenient**: Complete each stage on your own schedule
- **Fair and unbiased**: Every candidate receives the same objective evaluation
- **Transparent**: Get instant feedback after each interview stage

After successfully completing all interview stages, our team will reach out to discuss the next steps in the hiring process.

We look forward to meeting you!
```

Make sure the How to Apply section is always included at the end of the job posting and the link is properly formatted with the correct role ID.