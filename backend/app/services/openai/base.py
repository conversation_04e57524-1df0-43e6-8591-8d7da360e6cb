# File: backend/app/services/openai/base.py

from typing import Optional, Any, Dict, AsyncGenerator, Union
import openai
from openai import AsyncOpenAI
from openai.types.chat import ChatCompletion, ChatCompletionChunk
import asyncio
import logging
from functools import wraps
import backoff
import time
from app.core.config import settings
from .config.error_types import map_openai_error, OpenAIError
from .config.model_configs import ModelConfigurationManager
import json

logger = logging.getLogger(__name__)

def handle_openai_errors(func):
    """Decorator to handle OpenAI API errors uniformly using our enhanced error mapping."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Extract request context for error reporting
        context = {
            "function": func.__name__,
            "model": kwargs.get("model", "unknown"),
            "timestamp": time.time()
        }
        
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            # Map the exception to our custom error types
            mapped_error = map_openai_error(e, context)
            raise mapped_error
    return wrapper

class BaseOpenAIClient:
    """Base client for OpenAI API interactions with retry logic and error handling."""
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        org_id: Optional[str] = None,
        timeout: Optional[int] = None,
        max_retries: Optional[int] = None,
        use_case: Optional[str] = None
    ):
        self.api_key = api_key or settings.OPENAI_API_KEY
        self.org_id = org_id or settings.OPENAI_ORG_ID
        self.timeout = timeout or settings.OPENAI_TIMEOUT_SECONDS
        self.max_retries = max_retries or settings.OPENAI_MAX_RETRIES
        self.use_case = use_case
        
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            organization=self.org_id,
            timeout=self.timeout,
            max_retries=self.max_retries
        )
    
    @handle_openai_errors
    @backoff.on_exception(
        backoff.expo,
        (openai.RateLimitError, openai.APIError),
        max_tries=3,
        max_time=30
    )
    async def create_chat_completion(
        self,
        messages: list[Dict[str, str]],
        model: Optional[str] = None,
        stream: bool = False,
        functions: Optional[list[Dict[str, Any]]] = None,
        function_call: Optional[Union[str, Dict[str, Any]]] = None,
        **kwargs
    ) -> Union[ChatCompletion, AsyncGenerator[ChatCompletionChunk, None]]:
        """
        Create a chat completion with retry logic and error handling.
        
        Args:
            messages: List of message dictionaries
            model: Model to use (defaults to settings.OPENAI_DEFAULT_MODEL)
            stream: Whether to stream the response
            functions: List of function definitions
            function_call: Control function calling behavior
                - "auto": Let the model decide
                - "none": Don't call any functions
                - {"name": "function_name"}: Call a specific function
            **kwargs: Additional parameters to pass to the API
        
        Returns:
            ChatCompletion or AsyncGenerator[ChatCompletionChunk] if streaming
        """
        model = model or settings.OPENAI_DEFAULT_MODEL
        
        # Get model-specific configuration
        model_config = ModelConfigurationManager.get_model_config(model, self.use_case)
        
        # Start with model-specific configuration
        completion_kwargs = {
            "model": model,
            "messages": messages,
            "stream": stream,
        }
        
        # Add OpenAI API parameters from model_config
        # Only include parameters that are valid for the OpenAI API
        valid_openai_params = [
            "temperature", "top_p", "n", "max_tokens", 
            "presence_penalty", "frequency_penalty", "logit_bias",
            "user", "stop", "response_format"
        ]
        
        for param in valid_openai_params:
            if param in model_config:
                completion_kwargs[param] = model_config[param]
        
        # Override with any explicitly provided parameters
        for param in valid_openai_params:
            if param in kwargs:
                completion_kwargs[param] = kwargs[param]
        
        # Add functions if provided
        if functions:
            # Convert to the new tools format for OpenAI API
            completion_kwargs["tools"] = [{"type": "function", "function": func} for func in functions]
            
            # Handle function_call parameter (convert to tool_choice for new API)
            if function_call:
                if function_call == "auto":
                    completion_kwargs["tool_choice"] = "auto"
                elif function_call == "none":
                    completion_kwargs["tool_choice"] = "none"
                elif isinstance(function_call, dict) and "name" in function_call:
                    # Format for specific function call
                    completion_kwargs["tool_choice"] = {
                        "type": "function",
                        "function": {"name": function_call["name"]}
                    }
                # Handle legacy format
                elif isinstance(function_call, dict) and function_call.get("type") == "function":
                    func_details = function_call.get("function", {})
                    if func_details.get("name") == "auto":
                        completion_kwargs["tool_choice"] = "auto"
                    else:
                        completion_kwargs["tool_choice"] = {
                            "type": "function",
                            "function": {"name": func_details.get("name")}
                        }
        
        # Log request details at debug level
        logger.debug(f"Creating chat completion with model {model}, use case: {self.use_case}")
        logger.debug(f"Completion kwargs: {json.dumps({k: v for k, v in completion_kwargs.items() if k != 'messages'}, default=str)}")
        
        if stream:
            return self.client.chat.completions.create(**completion_kwargs)
        else:
            return await self.client.chat.completions.create(**completion_kwargs)
    
    @handle_openai_errors
    async def get_models(self) -> list[str]:
        """Get available models."""
        models = await self.client.models.list()
        return [model.id for model in models.data]
