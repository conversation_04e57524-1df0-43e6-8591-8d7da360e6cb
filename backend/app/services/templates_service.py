"""
Templates service for handling interview templates.
"""

import logging
from typing import Dict, Any, List, Optional
from app.services.firebase_service import FirebaseService

# Set up logging
logger = logging.getLogger(__name__)

class TemplatesService:
    """
    Service for handling interview templates.
    """
    
    def __init__(self):
        """
        Initialize the templates service.
        """
        self.firebase_service = FirebaseService()
    
    async def get_templates(self, role_id: str, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all templates for a role.
        
        Args:
            role_id: The ID of the role
            user_id: The ID of the user
            
        Returns:
            List of templates
        """
        logger.info(f"Getting templates for role {role_id}")
        return await self.firebase_service.get_templates(role_id=role_id, user_id=user_id)
    
    async def get_template(self, role_id: str, template_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific template.
        
        Args:
            role_id: The ID of the role
            template_id: The ID of the template
            user_id: The ID of the user
            
        Returns:
            The template or None if not found
        """
        logger.info(f"Getting template {template_id} for role {role_id}")
        return await self.firebase_service.get_template(role_id=role_id, template_id=template_id, user_id=user_id)
    
    async def create_template(self, role_id: str, template_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """
        Create a new template.
        
        Args:
            role_id: The ID of the role
            template_data: The template data
            user_id: The ID of the user
            
        Returns:
            The created template
        """
        logger.info(f"Creating template for role {role_id}")
        return await self.firebase_service.create_template(role_id=role_id, template_data=template_data, user_id=user_id)
    
    async def update_template(self, role_id: str, template_id: str, template_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """
        Update a template.
        
        Args:
            role_id: The ID of the role
            template_id: The ID of the template
            template_data: The template data
            user_id: The ID of the user
            
        Returns:
            The updated template
        """
        logger.info(f"Updating template {template_id} for role {role_id}")
        return await self.firebase_service.update_template(role_id=role_id, template_id=template_id, template_data=template_data, user_id=user_id)
    
    async def delete_template(self, role_id: str, template_id: str, user_id: str) -> None:
        """
        Delete a template.
        
        Args:
            role_id: The ID of the role
            template_id: The ID of the template
            user_id: The ID of the user
        """
        logger.info(f"Deleting template {template_id} for role {role_id}")
        await self.firebase_service.delete_template(role_id=role_id, template_id=template_id, user_id=user_id)
    
    async def get_template_questions(self, role_id: str, template_id: str, user_id: str) -> List[Dict[str, Any]]:
        """
        Get questions for a template.
        
        Args:
            role_id: The ID of the role
            template_id: The ID of the template
            user_id: The ID of the user
            
        Returns:
            List of questions
        """
        logger.info(f"Getting questions for template {template_id}")
        template = await self.get_template(role_id, template_id, user_id)
        if not template:
            logger.error(f"Template {template_id} not found")
            return []
        
        return template.get("questions", [])
    
    async def get_template_criteria(self, role_id: str, template_id: str, user_id: str) -> List[Dict[str, Any]]:
        """
        Get evaluation criteria for a template.
        
        Args:
            role_id: The ID of the role
            template_id: The ID of the template
            user_id: The ID of the user
            
        Returns:
            List of evaluation criteria
        """
        logger.info(f"Getting evaluation criteria for template {template_id}")
        template = await self.get_template(role_id, template_id, user_id)
        if not template:
            logger.error(f"Template {template_id} not found")
            return []
        
        return template.get("evaluationCriteria", []) 