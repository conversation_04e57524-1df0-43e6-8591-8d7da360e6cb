# File: backend/app/services/firebase_service.py

"""Firebase service for interacting with Firebase."""

import os
import logging
import json
import firebase_admin
from firebase_admin import credentials, firestore, auth, storage
from app.core.log_utils import log_data_summary

# Configure logger
logger = logging.getLogger(__name__)
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
from ..core.config import settings
from app.utils.data_transformers import normalize_role_data, normalize_enum_values
import uuid
from app.utils.id_generator import generate_question_id, generate_criterion_id
from app.utils.validation import validate_criteria_consistency
import csv
import aiofiles
import aiohttp
import traceback
from firebase_admin.exceptions import FirebaseError
from firebase_admin.firestore import SERVER_TIMESTAMP

class FirebaseService:
    _instance = None
    _initialized = False
    _test_mode = False

    def __new__(cls, test_mode=False):
        if cls._instance is None:
            cls._instance = super(FirebaseService, cls).__new__(cls)
            cls._test_mode = test_mode
        return cls._instance

    def __init__(self, test_mode=False):
        if not FirebaseService._initialized:
            self._initialize_firebase()
            FirebaseService._initialized = True

    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK with robust error handling"""
        if self._test_mode:
            self.db = Mock()
            return

        try:
            # Check if Firebase is already initialized
            try:
                self.app = firebase_admin.get_app()
                logging.info("Using existing Firebase Admin app")
            except ValueError:
                # Create credentials from environment variables
                cred_dict = {
                    "type": "service_account",
                    "project_id": settings.FIREBASE_PROJECT_ID,
                    "private_key_id": settings.FIREBASE_PRIVATE_KEY_ID,
                    "private_key": settings.FIREBASE_PRIVATE_KEY.replace('\\n', '\n'),
                    "client_email": settings.FIREBASE_CLIENT_EMAIL,
                    "client_id": settings.FIREBASE_CLIENT_ID,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                    "client_x509_cert_url": f"https://www.googleapis.com/robot/v1/metadata/x509/{settings.FIREBASE_CLIENT_EMAIL.replace('@', '%40')}"
                }

                # Log initialization attempt
                logger.info(f"Initializing Firebase Admin SDK with project: {settings.FIREBASE_PROJECT_ID}")

                try:
                    cred = credentials.Certificate(cred_dict)
                    self.app = firebase_admin.initialize_app(cred, {
                        'storageBucket': settings.FIREBASE_STORAGE_BUCKET
                    })
                    logger.info("Successfully initialized Firebase Admin app")
                except ValueError as ve:
                    logger.error(f"Invalid Firebase credentials: {str(ve)}")
                    raise
                except Exception as e:
                    logger.error(f"Error initializing Firebase app: {str(e)}")
                    raise

            # Initialize Firestore client
            try:
                self.db = firestore.client()
                logger.info("Successfully initialized Firestore client")
            except Exception as e:
                logger.error(f"Error initializing Firestore client: {str(e)}")
                raise

        except Exception as e:
            logger.error(f"Fatal error in Firebase initialization: {str(e)}")
            raise

    # Role Methods
    async def create_role(self, user_id: str, role_data: Dict[str, Any]) -> str:
        """Create a new role document in the user's roles collection."""
        try:
            if not user_id:
                raise ValueError("user_id is required")

            logger.info(f"Creating role for user {user_id}")
            logger.debug(f"Role data summary: {log_data_summary(role_data)}")

            # Add timestamps
            role_data['createdAt'] = datetime.utcnow()
            role_data['updatedAt'] = datetime.utcnow()

            # Create role in the user's roles collection
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document()
            doc_ref.set(role_data)

            logger.info(f"Created role with ID {doc_ref.id} for user {user_id}")
            return doc_ref.id
        except Exception as e:
            logger.error(f"Error creating role: {str(e)}")
            raise

    async def add_document(self, collection_name: str, document_data: Dict[str, Any], document_id: Optional[str] = None) -> str:
        """
        Add a document to a collection with optional document ID.

        Args:
            collection_name: Name of the collection
            document_data: Data to store in the document
            document_id: Optional document ID (if not provided, a new ID will be generated)

        Returns:
            Document ID
        """
        try:
            logger.info(f"Adding document to collection {collection_name}")
            logger.debug(f"Document data summary: {log_data_summary(document_data)}")

            # Add timestamps if they don't exist
            if 'createdAt' not in document_data:
                document_data['createdAt'] = datetime.utcnow()
            if 'updatedAt' not in document_data:
                document_data['updatedAt'] = datetime.utcnow()

            # Create document reference
            if document_id:
                doc_ref = self.db.collection(collection_name).document(document_id)
            else:
                doc_ref = self.db.collection(collection_name).document()

            # Set document data
            doc_ref.set(document_data)

            logger.info(f"Added document with ID {doc_ref.id} to collection {collection_name}")
            return doc_ref.id
        except Exception as e:
            logger.error(f"Error adding document to {collection_name}: {str(e)}")
            raise

    async def get_role(self, user_id: str, role_id: str) -> dict:
        """Get a role document by ID from a user's roles collection."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")

            logger.info(f"Fetching role {role_id} for user {user_id}")
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id)

            # Firestore operations are not natively async, so we don't await them
            doc = doc_ref.get()

            if not doc.exists:
                logger.warning(f"Role {role_id} not found for user {user_id}")
                return None

            role_data = doc.to_dict()

            # Ensure role_data is a dictionary
            if not isinstance(role_data, dict):
                logger.warning(f"Role data for {role_id} is not a dictionary, it's {type(role_data).__name__}")
                # Create a default dictionary instead of returning a simple object
                role_data = {
                    "id": role_id,
                    "title": "Untitled Role",
                    "summary": "",
                    "status": "Intake"
                }

            # Add the ID to the role data
            role_data["id"] = role_id

            # Convert Firestore timestamps to datetime objects
            if 'createdAt' in role_data and hasattr(role_data['createdAt'], 'timestamp'):
                role_data['createdAt'] = datetime.fromtimestamp(role_data['createdAt'].timestamp())

            if 'updatedAt' in role_data and hasattr(role_data['updatedAt'], 'timestamp'):
                role_data['updatedAt'] = datetime.fromtimestamp(role_data['updatedAt'].timestamp())

            # Normalize field names and values

            # First normalize field names
            role_data = normalize_role_data(role_data)

            # Then normalize enum values
            role_data = normalize_enum_values(role_data)

            # Ensure required fields are present with valid values
            if 'title' not in role_data or not role_data['title']:
                role_data['title'] = "Untitled Role"

            if 'status' not in role_data or not role_data['status']:
                role_data['status'] = "Intake"

            if 'jobType' not in role_data or not role_data['jobType']:
                role_data['jobType'] = "Full-time"

            if 'priority' not in role_data or not role_data['priority']:
                role_data['priority'] = "Normal"

            if 'location' not in role_data or not isinstance(role_data['location'], dict):
                role_data['location'] = {"city": "", "remoteStatus": "Remote"}
            elif 'remoteStatus' not in role_data['location'] or not role_data['location']['remoteStatus']:
                role_data['location']['remoteStatus'] = "Remote"

            if 'interviewProcess' not in role_data or not isinstance(role_data['interviewProcess'], list):
                role_data['interviewProcess'] = [
                    {
                        "stage": "Screening",
                        "duration": "30 minutes",
                        "customInstructions": ""
                    }
                ]

            # Ensure timestamps are present
            if 'createdAt' not in role_data:
                role_data['createdAt'] = datetime.now()

            if 'updatedAt' not in role_data:
                role_data['updatedAt'] = datetime.now()

            logger.info(f"Successfully fetched and normalized role {role_id}")
            logger.debug(f"Role data summary: {log_data_summary(role_data)}")
            return role_data

        except ValueError as ve:
            logger.error(f"Validation error in get_role: {str(ve)}")
            raise
        except Exception as e:
            logger.error(f"Error getting role {role_id} for user {user_id}: {str(e)}", exc_info=True)
            # Return a minimal valid dictionary instead of raising an exception
            return {
                "id": role_id,
                "title": "Untitled Role",
                "summary": "Error retrieving role details",
                "status": "Intake",
                "jobType": "Full-time",
                "priority": "Normal",
                "location": {"city": "", "remoteStatus": "Remote"},
                "interviewProcess": [
                    {
                        "stage": "Screening",
                        "duration": "30 minutes",
                        "customInstructions": ""
                    }
                ],
                "createdAt": datetime.now(),
                "updatedAt": datetime.now()
            }

    async def get_roles(self, user_id: str, limit: int = None) -> list:
        """
        Get all roles for a specific user.

        Args:
            user_id: The ID of the user
            limit: Optional limit on the number of roles to return

        Returns:
            List of roles
        """
        try:
            logger.info(f"Getting roles for user {user_id}" + (f" with limit {limit}" if limit else ""))
            roles = []
            docs = self.db.collection('users').document(user_id).collection('roles').stream()
            for doc in docs:
                role_data = doc.to_dict()

                # Add the ID to the role data
                role_data["id"] = doc.id

                # Convert Firestore timestamps to datetime objects
                if 'createdAt' in role_data and hasattr(role_data['createdAt'], 'timestamp'):
                    role_data['createdAt'] = datetime.fromtimestamp(role_data['createdAt'].timestamp())
                    # Also add created_at for frontend compatibility
                    role_data['created_at'] = role_data['createdAt']

                if 'updatedAt' in role_data and hasattr(role_data['updatedAt'], 'timestamp'):
                    role_data['updatedAt'] = datetime.fromtimestamp(role_data['updatedAt'].timestamp())
                    # Also add updated_at for frontend compatibility
                    role_data['updated_at'] = role_data['updatedAt']

                # Transform data to match the Pydantic model
                if 'status' in role_data:
                    role_data['status'] = role_data['status'].capitalize()  # Convert INTAKE to Intake

                # Ensure interview process has required fields
                if 'interviewProcess' in role_data:
                    for stage in role_data['interviewProcess']:
                        if 'duration' not in stage:
                            stage['duration'] = '30 minutes'  # Default duration
                        if 'description' not in stage:
                            stage['description'] = f"Evaluate candidate's fit for {stage.get('stage', 'this stage')}"
                        if 'customInstructions' not in stage:
                            stage['customInstructions'] = ''
                else:
                    role_data['interviewProcess'] = [{
                        'stage': 'Screening',
                        'duration': '30 minutes',
                        'description': 'Initial screening call to assess basic qualifications and mutual fit',
                        'customInstructions': ''
                    }]

                if 'details' in role_data:
                    details = role_data['details']
                    # Move fields from details to root
                    role_data['summary'] = details.get('summary', '')
                    role_data['keyResponsibilities'] = role_data.get('requirements', [])
                    role_data['requiredSkills'] = {str(i): skill for i, skill in enumerate(details.get('required_skills', []))}
                    role_data['preferredSkills'] = {str(i): skill for i, skill in enumerate(details.get('preferred_skills', []))}
                    role_data['team'] = details.get('team_dynamics', '')
                    role_data['keyStakeholders'] = []  # Default empty list

                # Format location data
                if isinstance(role_data.get('location'), dict):
                    location = role_data['location']
                    role_data['location'] = {
                        'city': location.get('city', ''),
                        'remoteStatus': location.get('remoteStatus', 'Remote')
                    }
                elif isinstance(role_data.get('location'), str):
                    role_data['location'] = {
                        'city': '',
                        'remoteStatus': 'Remote'
                    }
                else:
                    role_data['location'] = {
                        'city': '',
                        'remoteStatus': 'Remote'
                    }

                # Convert hiring manager contact to string if it's an object
                if isinstance(role_data.get('hiringManagerContact'), dict):
                    contact = role_data['hiringManagerContact']
                    role_data['hiringManagerContact'] = f"{contact.get('name', '')} ({contact.get('email', '')})"

                # Ensure title is present
                if 'title' not in role_data:
                    # Try to get title from subject or details
                    role_data['title'] = (
                        role_data.get('subject', '') or
                        role_data.get('details', {}).get('title', '') or
                        'Untitled Role'
                    )

                # Ensure benefits has required fields with default values
                if 'benefits' not in role_data or role_data['benefits'] is None:
                    role_data['benefits'] = {}
                if not isinstance(role_data['benefits'], dict):
                    role_data['benefits'] = {}
                if 'healthInsurance' not in role_data['benefits'] or role_data['benefits']['healthInsurance'] is None:
                    role_data['benefits']['healthInsurance'] = False
                if 'vacationDays' not in role_data['benefits'] or role_data['benefits']['vacationDays'] is None:
                    role_data['benefits']['vacationDays'] = 0

                roles.append(role_data)

            # Apply limit if specified
            if limit and len(roles) > limit:
                roles = roles[:limit]
                logger.info(f"Limited to {limit} roles out of {len(roles)} total")

            logger.info(f"Found {len(roles)} roles for user {user_id}")
            return roles
        except Exception as e:
            logger.error(f"Error getting roles: {e}")
            raise

    async def update_role(self, user_id: str, role_id: str, updates: Dict[str, Any]) -> None:
        """Update a role document in the user's roles collection."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")

            logger.info(f"Updating role {role_id} for user {user_id}")
            logger.debug(f"Update data summary: {log_data_summary(updates)}")

            # Add updated timestamp if not present
            if 'updatedAt' not in updates:
                updates['updatedAt'] = datetime.utcnow()

            # Convert any datetime objects to Firestore timestamps
            for key, value in updates.items():
                if isinstance(value, datetime):
                    updates[key] = value

            # Get the doc reference
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id)

            # Check if role exists
            doc = doc_ref.get()
            if not doc.exists:
                logger.error(f"Role {role_id} not found for update")
                raise ValueError(f"Role {role_id} not found")

            # Log update details
            logger.info(f"Updating role {role_id} with {len(updates)} fields")

            # Update the role document
            doc_ref.update(updates)

            logger.info(f"Successfully updated role {role_id}")

        except Exception as e:
            logger.error(f"Error updating role: {str(e)}", exc_info=True)
            raise

    async def delete_role(self, user_id: str, role_id: str) -> None:
        """Delete a role document from a user's roles collection."""
        try:
            logger.info(f"Deleting role {role_id} for user {user_id}")
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id)
            doc_ref.delete()
            logger.info(f"Successfully deleted role {role_id}")
        except Exception as e:
            logger.error(f"Error deleting role: {e}")
            raise

    async def verify_role_exists(self, user_id: str, role_id: str) -> bool:
        """Verify if a role exists in a user's roles collection.

        Args:
            user_id: The ID of the user who owns the role
            role_id: The ID of the role to verify

        Returns:
            bool: True if the role exists, False otherwise
        """
        try:
            logger.info(f"Verifying existence of role {role_id} for user {user_id}")
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id)
            doc = doc_ref.get()
            result = doc.exists
            logger.info(f"Role {role_id} exists: {result}")
            return result
        except Exception as e:
            logger.error(f"Error verifying role existence: {str(e)}")
            return False

    # New methods for handling role sub-collections

    async def create_intake_call(self, user_id: str, role_id: str, call_data: dict) -> str:
        """Create a new intake call document for a role."""
        try:
            call_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('intakeCalls').document()
            call_data.update({
                'id': call_ref.id,
                'createdAt': datetime.utcnow()
            })
            call_ref.set(call_data)
            return call_ref.id
        except Exception as e:
            logging.error(f"Error creating intake call: {e}")
            raise

    async def create_intake_transcript(
        self,
        user_id: str,
        role_id: str,
        transcript_id: str,
        transcript_data: dict
    ) -> None:
        """Create a new intake transcript."""
        try:
            # Create transcript document
            transcript_ref = self.db.collection('users').document(user_id)\
                .collection('roles').document(role_id)\
                .collection('intakeTranscripts').document(transcript_id)

            # Add timestamps
            transcript_data['createdAt'] = firestore.SERVER_TIMESTAMP
            transcript_data['updatedAt'] = firestore.SERVER_TIMESTAMP

            # Firestore operations are not natively async, so we don't await them
            transcript_ref.set(transcript_data)
        except Exception as e:
            logging.error(f"Error creating intake transcript: {str(e)}")
            raise ValueError(f"Failed to create intake transcript: {str(e)}")

    async def update_intake_transcript(self, user_id: str, role_id: str, transcript_id: str, messages: List[Dict], status: str = "in_progress") -> Dict[str, Any]:
        """
        Update an intake transcript with messages.

        Args:
            user_id: The ID of the user
            role_id: The ID of the role
            transcript_id: The ID of the transcript
            messages: List of conversation messages
            status: Transcript status (e.g., "in_progress", "completed")

        Returns:
            The updated transcript data
        """
        try:
            logging.info(f"Updating intake transcript {transcript_id} for role {role_id}")

            # Create a reference to the transcript document
            transcript_ref = self.db.collection("users").document(user_id) \
                .collection("roles").document(role_id) \
                .collection("intakeTranscripts").document(transcript_id)

            # First check if the document exists
            transcript_doc = transcript_ref.get()

            # Update data with messages and status
            update_data = {
                "messages": messages,
                "status": status,
                "updatedAt": firestore.SERVER_TIMESTAMP,
                "messageCount": len(messages) if messages else 0
            }

            if not transcript_doc.exists:
                # If transcript doesn't exist, create it
                create_data = {
                    "id": transcript_id,
                    "role_id": role_id,
                    "user_id": user_id,
                    "createdAt": firestore.SERVER_TIMESTAMP,
                    **update_data
                }
                transcript_ref.set(create_data)
                logging.info(f"Created new intake transcript {transcript_id} as it didn't exist")
            else:
                # Otherwise update the existing document
                transcript_ref.update(update_data)
                logging.info(f"Updated existing intake transcript {transcript_id}")

            # Get the updated transcript
            updated_doc = transcript_ref.get()
            if updated_doc.exists:
                transcript_data = updated_doc.to_dict()
                transcript_data["id"] = transcript_id
                return transcript_data

            # If we got here, something went wrong but didn't raise an exception
            logging.warning(f"Transcript update appeared successful but couldn't retrieve data")
            return {"id": transcript_id, "status": status}

        except Exception as e:
            logging.error(f"Error updating intake transcript: {str(e)}")
            # Re-raise to allow proper error handling in the endpoint
            raise

    async def complete_intake_transcript(
        self,
        user_id: str,
        role_id: str,
        transcript_id: str
    ) -> None:
        """Complete an intake transcript."""
        try:
            logging.info(f"Completing intake transcript: {transcript_id} for role {role_id}")
            # Get transcript reference
            transcript_ref = self.db.collection('users').document(user_id)\
                .collection('roles').document(role_id)\
                .collection('intakeTranscripts').document(transcript_id)

            # First check if transcript exists
            transcript_doc = transcript_ref.get()
            if not transcript_doc.exists:
                logging.error(f"Transcript {transcript_id} does not exist")
                raise ValueError(f"Transcript {transcript_id} not found")

            # Update is synchronous, don't use await
            transcript_ref.update({
                'status': 'completed',
                'updatedAt': firestore.SERVER_TIMESTAMP
            })

            logging.info(f"Successfully completed transcript {transcript_id}")

        except Exception as e:
            logging.error(f"Error completing intake transcript: {str(e)}")
            raise ValueError(f"Failed to complete intake transcript: {str(e)}")

    async def get_intake_calls(self, user_id: str, role_id: str) -> list:
        """Get all intake calls for a role."""
        try:
            calls = []
            docs = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('intakeCalls').stream()
            for doc in docs:
                calls.append({**doc.to_dict(), 'id': doc.id})
            return calls
        except Exception as e:
            logging.error(f"Error getting intake calls: {e}")
            raise

    async def get_intake_transcripts(self, user_id: str, role_id: str) -> List[Dict[str, Any]]:
        """Get all intake transcripts for a role."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")

            logging.info(f"Getting intake transcripts for role {role_id}")

            # Get the transcripts collection
            transcripts_ref = self.db.collection('users').document(user_id)\
                .collection('roles').document(role_id)\
                .collection('intakeTranscripts')

            # Get all transcripts
            transcripts = transcripts_ref.stream()

            # Convert to list of dictionaries
            result = []
            for doc in transcripts:
                transcript_data = doc.to_dict()

                # Skip invalid data
                if not isinstance(transcript_data, dict):
                    continue

                # Add the ID to the transcript data
                transcript_data["id"] = doc.id

                # Convert Firestore timestamps to datetime objects
                if 'createdAt' in transcript_data and hasattr(transcript_data['createdAt'], 'timestamp'):
                    transcript_data['createdAt'] = datetime.fromtimestamp(transcript_data['createdAt'].timestamp())

                if 'updatedAt' in transcript_data and hasattr(transcript_data['updatedAt'], 'timestamp'):
                    transcript_data['updatedAt'] = datetime.fromtimestamp(transcript_data['updatedAt'].timestamp())

                result.append(transcript_data)

            return result
        except Exception as e:
            logging.error(f"Error getting intake transcripts: {str(e)}")
            raise

    async def get_intake_transcript(
        self,
        user_id: str,
        role_id: str,
        transcript_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get a specific intake transcript by ID."""
        try:
            # Get transcript reference
            transcript_ref = self.db.collection('users').document(user_id)\
                .collection('roles').document(role_id)\
                .collection('intakeTranscripts').document(transcript_id)

            # Get the transcript
            transcript_doc = transcript_ref.get()
            if not transcript_doc.exists:
                logging.warning(f"Transcript {transcript_id} not found for role {role_id}")
                return None

            # Convert to dict and add ID
            transcript_data = transcript_doc.to_dict()
            transcript_data['id'] = transcript_doc.id

            return transcript_data
        except Exception as e:
            logging.error(f"Error getting intake transcript {transcript_id}: {str(e)}")
            raise ValueError(f"Failed to retrieve intake transcript: {str(e)}")

    # Candidate Methods
    async def create_candidate(self, user_id: str, role_id: str, candidate_data: Dict[str, Any]) -> str:
        """Create a new candidate in the role's candidates collection."""
        try:
            candidate_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('candidates').document()
            candidate_data.update({
                'id': candidate_ref.id,
                'createdAt': datetime.utcnow(),
                'updatedAt': datetime.utcnow(),
                'roleId': role_id
            })
            candidate_ref.set(candidate_data)
            return candidate_ref.id
        except Exception as e:
            logging.error(f"Error creating candidate: {str(e)}")
            raise

    async def update_candidate(self, user_id: str, role_id: str, candidate_id: str, candidate_data: Dict[str, Any]) -> None:
        """Update an existing candidate in the role's candidates collection."""
        try:
            candidate_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('candidates').document(candidate_id)
            candidate_data['updatedAt'] = datetime.utcnow()
            candidate_ref.update(candidate_data)
        except Exception as e:
            logging.error(f"Error updating candidate: {str(e)}")
            raise

    async def get_candidate(self, user_id: str, role_id: str, candidate_id: str) -> Optional[Dict[str, Any]]:
        """Get a candidate by ID from a role's candidates collection."""
        try:
            candidate_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('candidates').document(candidate_id)
            candidate = candidate_ref.get()
            return {**candidate.to_dict(), 'id': candidate.id} if candidate.exists else None
        except Exception as e:
            logging.error(f"Error getting candidate: {str(e)}")
            raise

    async def get_candidates_by_role(self, user_id: str, role_id: str) -> List[Dict[str, Any]]:
        """Get all candidates for a specific role."""
        try:
            candidates = []
            docs = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('candidates').stream()
            for doc in docs:
                candidates.append({**doc.to_dict(), 'id': doc.id})
            return candidates
        except Exception as e:
            logging.error(f"Error getting candidates by role: {str(e)}")
            raise

    async def delete_candidate(self, user_id: str, role_id: str, candidate_id: str) -> None:
        """Delete a candidate from a role's candidates collection."""
        try:
            candidate_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('candidates').document(candidate_id)
            candidate_ref.delete()
        except Exception as e:
            logging.error(f"Error deleting candidate: {str(e)}")
            raise

    # Interview Methods
    async def create_interview(self, interview_data: Dict[str, Any]) -> str:
        """Create a new interview record in Firestore."""
        interview_ref = self.db.collection('interviews').document()
        interview_data['created_at'] = datetime.utcnow()
        interview_data['updated_at'] = datetime.utcnow()
        interview_ref.set(interview_data)
        return interview_ref.id

    async def update_interview(self, interview_id: str, interview_data: Dict[str, Any]) -> None:
        """Update an existing interview in Firestore."""
        interview_ref = self.db.collection('interviews').document(interview_id)
        interview_data['updated_at'] = datetime.utcnow()
        interview_ref.update(interview_data)

    async def get_interview(self, interview_id: str) -> Optional[Dict[str, Any]]:
        """Get an interview by ID."""
        interview_ref = self.db.collection('interviews').document(interview_id)
        interview = interview_ref.get()
        return interview.to_dict() if interview.exists else None

    async def get_interviews_by_candidate(self, candidate_id: str) -> List[Dict[str, Any]]:
        """Get all interviews for a specific candidate."""
        interviews = self.db.collection('interviews').where(filter=('candidate_id', '==', candidate_id)).stream()
        return [interview.to_dict() for interview in interviews]

    # Evaluation Methods
    async def create_evaluation(self, evaluation_data: Dict[str, Any]) -> str:
        """Create a new evaluation record in Firestore."""
        from app.services.evaluation_service import EvaluationService
        evaluation_service = EvaluationService()

        # Extract required fields from evaluation_data
        role_id = evaluation_data.get("role_id")
        interview_id = evaluation_data.get("interview_id")
        candidate_id = evaluation_data.get("candidate_id")
        application_id = evaluation_data.get("application_id")
        user_id = evaluation_data.get("user_id")
        metadata = evaluation_data.get("metadata")
        tags = evaluation_data.get("tags")

        # Validate required fields
        if not role_id:
            raise ValueError("role_id is required for evaluation")
        if not interview_id:
            raise ValueError("interview_id is required for evaluation")

        # Create the evaluation using the specialized service
        return await evaluation_service.create_evaluation(
            role_id=role_id,
            interview_id=interview_id,
            evaluation_data=evaluation_data.get("evaluation_data", {}),
            candidate_id=candidate_id,
            application_id=application_id,
            user_id=user_id,
            metadata=metadata,
            tags=tags
        )

    async def update_evaluation(self, evaluation_id: str, evaluation_data: Dict[str, Any]) -> None:
        """Update an existing evaluation in Firestore."""
        from app.services.evaluation_service import EvaluationService
        evaluation_service = EvaluationService()

        # If status is being updated, use the specialized method
        if "status" in evaluation_data:
            user_id = evaluation_data.get("user_id")
            await evaluation_service.update_evaluation_status(
                evaluation_id=evaluation_id,
                status=evaluation_data["status"],
                user_id=user_id
            )
        else:
            # For more complex updates, we would need to implement a full update method
            # in the EvaluationService. For now, just update the status if provided.
            logging.warning("Full evaluation updates not implemented. Only status updates are supported.")

    async def get_evaluation(self, evaluation_id: str) -> Optional[Dict[str, Any]]:
        """Get an evaluation by ID."""
        from app.services.evaluation_service import EvaluationService
        evaluation_service = EvaluationService()

        # Retrieve the evaluation using the specialized service
        return await evaluation_service.get_evaluation(evaluation_id)

    async def get_evaluations_by_candidate(self, candidate_id: str) -> List[Dict[str, Any]]:
        """Get all evaluations for a specific candidate."""
        from app.services.evaluation_service import EvaluationService
        evaluation_service = EvaluationService()

        # Retrieve evaluations using the specialized service
        return await evaluation_service.get_evaluations_by_candidate(candidate_id)

    async def get_evaluations_by_role(self, role_id: str, user_id: str = None) -> List[Dict[str, Any]]:
        """Get all evaluations for a specific role."""
        from app.services.evaluation_service import EvaluationService
        evaluation_service = EvaluationService()

        # Retrieve evaluations using the specialized service
        return await evaluation_service.get_evaluations_by_role(role_id, user_id)

    async def get_evaluation_by_interview(self, interview_id: str, user_id: str = None) -> Optional[Dict[str, Any]]:
        """Get evaluation for an interview."""
        from app.services.evaluation_service import EvaluationService
        evaluation_service = EvaluationService()

        # Retrieve the evaluation using the specialized service
        return await evaluation_service.get_evaluation_by_interview(interview_id, user_id)

    async def get_evaluation_public(self, interview_id: str, application_id: str = None) -> Optional[Dict[str, Any]]:
        """Get a public evaluation with limited data."""
        from app.services.evaluation_service import EvaluationService
        evaluation_service = EvaluationService()

        # Retrieve the public evaluation using the specialized service
        return await evaluation_service.get_evaluation_public(interview_id, application_id)

    async def create_resume_evaluation(
        self,
        role_id: str,
        evaluation_data: dict,
        resume_text: str,
        candidate_id: str = None,
        application_id: str = None,
        user_id: str = None,
        metadata: dict = None
    ) -> str:
        """Create a resume evaluation document in Firestore."""
        from app.services.evaluation_service import EvaluationService
        evaluation_service = EvaluationService()

        # Create the resume evaluation using the specialized service
        return await evaluation_service.create_resume_evaluation(
            role_id=role_id,
            evaluation_data=evaluation_data,
            resume_text=resume_text,
            candidate_id=candidate_id,
            application_id=application_id,
            user_id=user_id,
            metadata=metadata
        )

    # User Methods
    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user document by ID."""
        try:
            doc_ref = self.db.collection('users').document(user_id)
            doc = doc_ref.get()
            if doc.exists:
                return {**doc.to_dict(), 'id': doc.id}
            return None
        except Exception as e:
            logging.error(f"Error getting user by ID: {str(e)}")
            raise

    async def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user document by email."""
        try:
            # First check if user exists in Firebase Auth
            try:
                user = auth.get_user_by_email(email)
                if not user.email_verified:
                    logging.warning(f"User email not verified: {email}")
                    return None
            except auth.UserNotFoundError:
                logging.warning(f"User not found in Firebase Auth: {email}")
                return None

            # Then get user data from Firestore
            query = self.db.collection('users').where(filter=('email', '==', email)).limit(1).get()
            if not query:
                return None

            doc = query[0]
            user_data = doc.to_dict()

            # Check if user is active
            if user_data.get('status') != 'active':
                logging.warning(f"User account not active: {email}")
                return None

            return {**user_data, 'id': doc.id}

        except Exception as e:
            logging.error(f"Error getting user by email: {str(e)}")
            raise

    async def verify_user_active(self, user_id: str) -> bool:
        """Verify if a user exists and is active."""
        try:
            user_data = await self.get_user_by_id(user_id)
            return user_data is not None and user_data.get('status') == 'active'
        except Exception as e:
            logging.error(f"Error verifying user status: {str(e)}")
            return False

    async def migrate_roles_to_team_structure(self):
        """Migrate existing roles to use team_name instead of department fields."""
        try:
            # Get all users
            users = self.db.collection('users').stream()

            for user in users:
                # Get all roles for each user
                roles = self.db.collection('users').document(user.id).collection('roles').stream()

                for role in roles:
                    role_data = role.to_dict()
                    updates = {}

                    # Migrate department_name to team_name if it exists
                    if 'department_name' in role_data:
                        updates['team_name'] = role_data['department_name']
                        updates['department_name'] = firestore.DELETE_FIELD

                    # Remove department_id and cost_center
                    if 'department_id' in role_data:
                        updates['department_id'] = firestore.DELETE_FIELD
                    if 'cost_center' in role_data:
                        updates['cost_center'] = firestore.DELETE_FIELD

                    # Only update if there are changes
                    if updates:
                        self.db.collection('users').document(user.id).collection('roles').document(role.id).update(updates)

            return True
        except Exception as e:
            logging.error(f"Error migrating roles: {str(e)}")
            raise

    async def list_roles(self, user_id: str) -> List[Dict[str, Any]]:
        """List all roles for a user."""
        try:
            if not user_id:
                raise ValueError("user_id is required")

            logging.info(f"Listing roles for user {user_id}")
            roles_ref = self.db.collection('users').document(user_id).collection('roles')
            roles_docs = roles_ref.stream()

            roles = []
            for doc in roles_docs:
                try:
                    role_data = doc.to_dict()

                    # Skip invalid data
                    if not isinstance(role_data, dict):
                        logging.warning(f"Role data for {doc.id} is not a dictionary, it's {type(role_data).__name__}")
                        continue

                    # Add the ID to the role data
                    role_data["id"] = doc.id

                    # Convert Firestore timestamps to datetime objects
                    if 'createdAt' in role_data and hasattr(role_data['createdAt'], 'timestamp'):
                        role_data['createdAt'] = datetime.fromtimestamp(role_data['createdAt'].timestamp())

                    if 'updatedAt' in role_data and hasattr(role_data['updatedAt'], 'timestamp'):
                        role_data['updatedAt'] = datetime.fromtimestamp(role_data['updatedAt'].timestamp())

                    # Normalize field names and values
                    # First normalize field names
                    role_data = normalize_role_data(role_data)

                    # Then normalize enum values
                    role_data = normalize_enum_values(role_data)

                    # Ensure required fields are present with valid values
                    if 'title' not in role_data or not role_data['title']:
                        role_data['title'] = "Untitled Role"

                    if 'status' not in role_data or not role_data['status']:
                        role_data['status'] = "Intake"

                    if 'jobType' not in role_data or not role_data['jobType']:
                        role_data['jobType'] = "Full-time"

                    if 'priority' not in role_data or not role_data['priority']:
                        role_data['priority'] = "Normal"

                    if 'location' not in role_data or not isinstance(role_data['location'], dict):
                        role_data['location'] = {"city": "", "remoteStatus": "Remote"}
                    elif 'remoteStatus' not in role_data['location'] or not role_data['location']['remoteStatus']:
                        role_data['location']['remoteStatus'] = "Remote"

                    if 'interviewProcess' not in role_data or not isinstance(role_data['interviewProcess'], list):
                        role_data['interviewProcess'] = [
                            {
                                "stage": "Screening",
                                "duration": "30 minutes",
                                "customInstructions": ""
                            }
                        ]

                    # Ensure keyResponsibilities is a list
                    if 'keyResponsibilities' not in role_data or role_data['keyResponsibilities'] is None:
                        role_data['keyResponsibilities'] = []
                    elif not isinstance(role_data['keyResponsibilities'], list):
                        # Try to convert to list if possible
                        try:
                            if isinstance(role_data['keyResponsibilities'], str):
                                # If it's a string, try to split by commas
                                role_data['keyResponsibilities'] = [item.strip() for item in role_data['keyResponsibilities'].split(',') if item.strip()]
                            else:
                                # For other types, convert to string and make a single-item list
                                role_data['keyResponsibilities'] = [str(role_data['keyResponsibilities'])]
                        except Exception:
                            # If conversion fails, use empty list
                            role_data['keyResponsibilities'] = []
                            logging.warning(f"Could not convert keyResponsibilities to list for role {doc.id}, using empty list")

                    # Ensure team is a string
                    if 'team' not in role_data or role_data['team'] is None:
                        role_data['team'] = ""
                    elif not isinstance(role_data['team'], str):
                        # Convert to string
                        try:
                            role_data['team'] = str(role_data['team'])
                        except Exception:
                            role_data['team'] = ""
                            logging.warning(f"Could not convert team to string for role {doc.id}, using empty string")

                    # Ensure timestamps are present
                    if 'createdAt' not in role_data:
                        role_data['createdAt'] = datetime.now()

                    if 'updatedAt' not in role_data:
                        role_data['updatedAt'] = datetime.now()

                    # Validate all required fields are present and of correct type before adding to roles list
                    roles.append(role_data)

                except Exception as e:
                    # Log the error but continue processing other roles
                    logging.error(f"Error processing role {doc.id}: {str(e)}", exc_info=True)
                    continue

            logging.info(f"Successfully processed {len(roles)} roles for user {user_id}")
            return roles

        except Exception as e:
            logging.error(f"Error listing roles for user {user_id}: {str(e)}", exc_info=True)
            raise

    # Job Posting Methods
    async def save_job_posting(self, user_id: str, role_id: str, job_posting_data: Dict[str, Any]) -> str:
        """
        Save a job posting as a subcollection under the role document.

        Args:
            user_id: The ID of the user who owns the role
            role_id: The ID of the role
            job_posting_data: The job posting data to save

        Returns:
            str: The ID of the created job posting document
        """
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not job_posting_data:
                raise ValueError("job_posting_data is required")

            logging.info(f"Saving job posting for role {role_id}")

            # Add timestamps
            job_posting_data['createdAt'] = datetime.utcnow()
            job_posting_data['updatedAt'] = datetime.utcnow()

            # Create job posting in the role's jobPostings collection
            # Use 'current' as the document ID to always have a single current job posting
            doc_ref = self.db.collection('users').document(user_id)\
                .collection('roles').document(role_id)\
                .collection('jobPostings').document('current')

            doc_ref.set(job_posting_data)

            logging.info(f"Saved job posting for role {role_id}")
            return 'current'

        except Exception as e:
            logging.error(f"Error saving job posting: {str(e)}")
            raise

    async def get_job_posting(self, user_id: str, role_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current job posting for a role.

        Args:
            user_id: The ID of the user who owns the role
            role_id: The ID of the role

        Returns:
            Optional[Dict[str, Any]]: The job posting data or None if not found
        """
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")

            logging.info(f"Getting job posting for role {role_id}")

            # Get the job posting document
            doc_ref = self.db.collection('users').document(user_id)\
                .collection('roles').document(role_id)\
                .collection('jobPostings').document('current')

            doc = doc_ref.get()

            if not doc.exists:
                logging.info(f"No job posting found for role {role_id}")
                return None

            job_posting_data = doc.to_dict()

            # Convert Firestore timestamps to datetime objects
            if 'createdAt' in job_posting_data and hasattr(job_posting_data['createdAt'], 'timestamp'):
                job_posting_data['createdAt'] = datetime.fromtimestamp(job_posting_data['createdAt'].timestamp())

            if 'updatedAt' in job_posting_data and hasattr(job_posting_data['updatedAt'], 'timestamp'):
                job_posting_data['updatedAt'] = datetime.fromtimestamp(job_posting_data['updatedAt'].timestamp())

            return job_posting_data

        except Exception as e:
            logging.error(f"Error getting job posting: {str(e)}")
            raise

    # Interview Template Methods
    async def create_interview_template(self, user_id: str, role_id: str, template_data: Dict[str, Any]) -> str:
        """Create a new interview template document in the role's interviewTemplates collection."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")

            logging.info(f"Creating interview template for role {role_id} (user {user_id})")

            # Add timestamps if not present
            if 'createdAt' not in template_data:
                template_data['createdAt'] = datetime.utcnow()
            if 'updatedAt' not in template_data:
                template_data['updatedAt'] = datetime.utcnow()

            # Create template in the role's interviewTemplates collection
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document()
            doc_ref.set(template_data)

            return doc_ref.id
        except Exception as e:
            logging.error(f"Error creating interview template: {str(e)}")
            raise

    async def get_interview_template(self, user_id: str, role_id: str, template_id: str) -> Optional[Dict[str, Any]]:
        """Get an interview template document by ID from a role's interviewTemplates collection."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not template_id:
                raise ValueError("template_id is required")

            logging.info(f"Fetching interview template {template_id} for role {role_id} (user {user_id})")
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document(template_id)

            doc = doc_ref.get()

            if not doc.exists:
                logging.warning(f"Interview template {template_id} not found for role {role_id}")
                return None

            template_data = doc.to_dict()

            # Ensure template_data is a dictionary
            if not isinstance(template_data, dict):
                logging.warning(f"Template data for {template_id} is not a dictionary")
                return None

            # Add the ID to the template data
            template_data["id"] = template_id

            # Convert Firestore timestamps to datetime objects
            if 'createdAt' in template_data and hasattr(template_data['createdAt'], 'timestamp'):
                template_data['createdAt'] = datetime.fromtimestamp(template_data['createdAt'].timestamp())

            if 'updatedAt' in template_data and hasattr(template_data['updatedAt'], 'timestamp'):
                template_data['updatedAt'] = datetime.fromtimestamp(template_data['updatedAt'].timestamp())

            # Validate and fix questions if they exist
            if 'questions' in template_data and isinstance(template_data['questions'], list):
                for i, question in enumerate(template_data['questions']):
                    if not isinstance(question, dict):
                        logging.warning(f"Question {i} in template {template_id} is not a dictionary")
                        continue

                    # Handle field name mismatch - ensure both 'text' and 'question' fields exist
                    if "text" in question and "question" not in question:
                        question["question"] = question["text"]
                        logging.info(f"Template {template_id}: Copied 'text' to 'question' field for question {i}")
                    elif "question" in question and "text" not in question:
                        question["text"] = question["question"]
                        logging.info(f"Template {template_id}: Copied 'question' to 'text' field for question {i}")
                    elif "text" not in question and "question" not in question:
                        logging.warning(f"Template {template_id}: Question {i} is missing both 'text' and 'question' fields")
                        question["text"] = "Missing question text"
                        question["question"] = "Missing question text"

                    # Ensure other required fields exist
                    if "purpose" not in question:
                        logging.warning(f"Template {template_id}: Question {i} is missing 'purpose' field")
                        question["purpose"] = "General evaluation"

                    if "idealAnswerCriteria" not in question:
                        logging.warning(f"Template {template_id}: Question {i} is missing 'idealAnswerCriteria' field")
                        question["idealAnswerCriteria"] = "Clear, concise, and relevant response"

                    # Ensure statistics field exists
                    if "statistics" not in question:
                        question["statistics"] = {
                            "topScore": 0,
                            "topScoringCandidateId": "",
                            "averageScore": 0,
                            "totalAnswers": 0
                        }

            return template_data

        except Exception as e:
            logging.error(f"Error getting interview template: {str(e)}")
            raise

    async def get_interview_templates(self, user_id: str, role_id: str) -> List[Dict[str, Any]]:
        """Get all interview templates for a specific role."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")

            logging.info(f"Fetching interview templates for role {role_id} (user {user_id})")
            templates_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates')

            templates_docs = templates_ref.stream()

            templates = []
            for doc in templates_docs:
                template_data = doc.to_dict()

                # Skip invalid data
                if not isinstance(template_data, dict):
                    logging.warning(f"Template data for {doc.id} is not a dictionary")
                    continue

                # Add the ID to the template data
                template_data["id"] = doc.id

                # Convert Firestore timestamps to datetime objects
                if 'createdAt' in template_data and hasattr(template_data['createdAt'], 'timestamp'):
                    template_data['createdAt'] = datetime.fromtimestamp(template_data['createdAt'].timestamp())

                if 'updatedAt' in template_data and hasattr(template_data['updatedAt'], 'timestamp'):
                    template_data['updatedAt'] = datetime.fromtimestamp(template_data['updatedAt'].timestamp())

                # Validate and fix questions if they exist
                if 'questions' in template_data and isinstance(template_data['questions'], list):
                    for i, question in enumerate(template_data['questions']):
                        if not isinstance(question, dict):
                            logging.warning(f"Question {i} in template {doc.id} is not a dictionary")
                            continue

                        # Handle field name mismatch - ensure both 'text' and 'question' fields exist
                        if "text" in question and "question" not in question:
                            question["question"] = question["text"]
                            logging.info(f"Template {doc.id}: Copied 'text' to 'question' field for question {i}")
                        elif "question" in question and "text" not in question:
                            question["text"] = question["question"]
                            logging.info(f"Template {doc.id}: Copied 'question' to 'text' field for question {i}")
                        elif "text" not in question and "question" not in question:
                            logging.warning(f"Template {doc.id}: Question {i} is missing both 'text' and 'question' fields")
                            question["text"] = "Missing question text"
                            question["question"] = "Missing question text"

                        # Ensure other required fields exist
                        if "purpose" not in question:
                            logging.warning(f"Template {doc.id}: Question {i} is missing 'purpose' field")
                            question["purpose"] = "General evaluation"

                        if "idealAnswerCriteria" not in question:
                            logging.warning(f"Template {doc.id}: Question {i} is missing 'idealAnswerCriteria' field")
                            question["idealAnswerCriteria"] = "Clear, concise, and relevant response"

                        # Ensure statistics field exists
                        if "statistics" not in question:
                            question["statistics"] = {
                                "topScore": 0,
                                "topScoringCandidateId": "",
                                "averageScore": 0,
                                "totalAnswers": 0
                            }

                templates.append(template_data)

            return templates

        except Exception as e:
            logging.error(f"Error getting interview templates: {str(e)}")
            raise

    async def update_interview_template(self, user_id: str, role_id: str, template_id: str, updates: Dict[str, Any]) -> None:
        """Update an interview template document in the role's interviewTemplates collection."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not template_id:
                raise ValueError("template_id is required")

            logging.info(f"Updating interview template {template_id} for role {role_id} (user {user_id})")

            # Add updated timestamp if not present
            if 'updatedAt' not in updates:
                updates['updatedAt'] = datetime.utcnow()

            # Get the doc reference
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document(template_id)

            # Check if template exists
            doc = doc_ref.get()
            if not doc.exists:
                logging.error(f"Interview template {template_id} not found for update")
                raise ValueError(f"Interview template {template_id} not found")

            # Validate and fix questions if they exist in the updates
            if 'questions' in updates and isinstance(updates['questions'], list):
                for i, question in enumerate(updates['questions']):
                    if not isinstance(question, dict):
                        logging.warning(f"Question {i} in update for template {template_id} is not a dictionary")
                        continue

                    # Handle field name mismatch - ensure both 'text' and 'question' fields exist
                    if "text" in question and "question" not in question:
                        question["question"] = question["text"]
                        logging.info(f"Update for template {template_id}: Copied 'text' to 'question' field for question {i}")
                    elif "question" in question and "text" not in question:
                        question["text"] = question["question"]
                        logging.info(f"Update for template {template_id}: Copied 'question' to 'text' field for question {i}")
                    elif "text" not in question and "question" not in question:
                        logging.warning(f"Update for template {template_id}: Question {i} is missing both 'text' and 'question' fields")
                        question["text"] = "Missing question text"
                        question["question"] = "Missing question text"

                    # Ensure other required fields exist
                    if "purpose" not in question:
                        logging.warning(f"Update for template {template_id}: Question {i} is missing 'purpose' field")
                        question["purpose"] = "General evaluation"

                    if "idealAnswerCriteria" not in question:
                        logging.warning(f"Update for template {template_id}: Question {i} is missing 'idealAnswerCriteria' field")
                        question["idealAnswerCriteria"] = "Clear, concise, and relevant response"

                    # Ensure statistics field exists
                    if "statistics" not in question:
                        question["statistics"] = {
                            "topScore": 0,
                            "topScoringCandidateId": "",
                            "averageScore": 0,
                            "totalAnswers": 0
                        }

            # Update the template document
            doc_ref.update(updates)

            logging.info(f"Successfully updated interview template {template_id}")

        except Exception as e:
            logging.error(f"Error updating interview template: {str(e)}", exc_info=True)
            raise

    async def delete_interview_template(self, user_id: str, role_id: str, template_id: str) -> None:
        """Delete an interview template document from the role's interviewTemplates collection."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not template_id:
                raise ValueError("template_id is required")

            logging.info(f"Deleting interview template {template_id} for role {role_id} (user {user_id})")

            # Get the doc reference
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document(template_id)

            # Check if template exists
            doc = doc_ref.get()
            if not doc.exists:
                logging.warning(f"Interview template {template_id} not found for deletion")
                raise ValueError(f"Interview template {template_id} not found")

            # Delete the template document
            doc_ref.delete()

            logging.info(f"Successfully deleted interview template {template_id}")

        except Exception as e:
            logging.error(f"Error deleting interview template: {str(e)}")
            raise

    # Question Management Methods
    async def add_question_to_template(self, user_id: str, role_id: str, template_id: str, question_data: Dict[str, Any]) -> str:
        """Add a question to an interview template."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not template_id:
                raise ValueError("template_id is required")

            logging.info(f"Adding question to template {template_id} for role {role_id} (user {user_id})")

            # Generate a unique ID for the question
            question_id = generate_question_id()

            # Add the ID to the question data
            question_data["id"] = question_id

            # Handle field name mismatch - ensure both 'text' and 'question' fields exist
            if "text" in question_data and "question" not in question_data:
                question_data["question"] = question_data["text"]
                logging.info(f"New question for template {template_id}: Copied 'text' to 'question' field")
            elif "question" in question_data and "text" not in question_data:
                question_data["text"] = question_data["question"]
                logging.info(f"New question for template {template_id}: Copied 'question' to 'text' field")
            elif "text" not in question_data and "question" not in question_data:
                logging.warning(f"New question for template {template_id}: Missing both 'text' and 'question' fields")
                question_data["text"] = "Missing question text"
                question_data["question"] = "Missing question text"

            # Ensure other required fields exist
            if "purpose" not in question_data:
                logging.warning(f"New question for template {template_id}: Missing 'purpose' field")
                question_data["purpose"] = "General evaluation"

            if "idealAnswerCriteria" not in question_data:
                logging.warning(f"New question for template {template_id}: Missing 'idealAnswerCriteria' field")
                question_data["idealAnswerCriteria"] = "Clear, concise, and relevant response"

            # Initialize statistics if not provided
            if "statistics" not in question_data:
                question_data["statistics"] = {}

            # Ensure all statistics fields are initialized
            if "topScore" not in question_data["statistics"]:
                question_data["statistics"]["topScore"] = 0
            if "topScoringCandidateId" not in question_data["statistics"]:
                question_data["statistics"]["topScoringCandidateId"] = ""
            if "averageScore" not in question_data["statistics"]:
                question_data["statistics"]["averageScore"] = 0
            if "totalAnswers" not in question_data["statistics"]:
                question_data["statistics"]["totalAnswers"] = 0

            # Get the template document reference
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document(template_id)

            # Get the current template
            doc = doc_ref.get()
            if not doc.exists:
                logging.error(f"Interview template {template_id} not found")
                raise ValueError(f"Interview template {template_id} not found")

            template_data = doc.to_dict()

            # Initialize questions array if it doesn't exist
            if 'questions' not in template_data or not isinstance(template_data['questions'], list):
                template_data['questions'] = []

            # Add the question to the questions array
            template_data['questions'].append(question_data)

            # Update the updatedAt timestamp
            template_data['updatedAt'] = datetime.utcnow()

            # Update the template document
            doc_ref.update({
                'questions': template_data['questions'],
                'updatedAt': template_data['updatedAt']
            })

            logging.info(f"Successfully added question {question_id} to template {template_id}")

            return question_id

        except Exception as e:
            logging.error(f"Error adding question to template: {str(e)}")
            raise

    async def update_question_in_template(self, user_id: str, role_id: str, template_id: str, question_id: str, updates: Dict[str, Any]) -> None:
        """Update a question in an interview template."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not template_id:
                raise ValueError("template_id is required")
            if not question_id:
                raise ValueError("question_id is required")

            logging.info(f"Updating question {question_id} in template {template_id} for role {role_id} (user {user_id})")

            # Handle field name mismatch in updates - ensure both 'text' and 'question' fields exist
            if "text" in updates and "question" not in updates:
                updates["question"] = updates["text"]
                logging.info(f"Question update for {question_id}: Copied 'text' to 'question' field")
            elif "question" in updates and "text" not in updates:
                updates["text"] = updates["question"]
                logging.info(f"Question update for {question_id}: Copied 'question' to 'text' field")

            # Get the template document reference
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document(template_id)

            # Get the current template
            doc = doc_ref.get()
            if not doc.exists:
                logging.error(f"Interview template {template_id} not found")
                raise ValueError(f"Interview template {template_id} not found")

            template_data = doc.to_dict()

            # Check if questions array exists
            if 'questions' not in template_data or not isinstance(template_data['questions'], list):
                logging.error(f"Questions array not found in template {template_id}")
                raise ValueError(f"Questions array not found in template {template_id}")

            # Find the question by ID
            question_index = None
            for i, question in enumerate(template_data['questions']):
                if isinstance(question, dict) and question.get('id') == question_id:
                    question_index = i
                    break

            if question_index is None:
                logging.error(f"Question {question_id} not found in template {template_id}")
                raise ValueError(f"Question {question_id} not found in template {template_id}")

            # Update the question
            for key, value in updates.items():
                template_data['questions'][question_index][key] = value

            # Ensure the updated question has all required fields
            updated_question = template_data['questions'][question_index]

            # Handle field name mismatch - ensure both 'text' and 'question' fields exist
            if "text" in updated_question and "question" not in updated_question:
                updated_question["question"] = updated_question["text"]
                logging.info(f"Updated question {question_id}: Copied 'text' to 'question' field")
            elif "question" in updated_question and "text" not in updated_question:
                updated_question["text"] = updated_question["question"]
                logging.info(f"Updated question {question_id}: Copied 'question' to 'text' field")
            elif "text" not in updated_question and "question" not in updated_question:
                logging.warning(f"Updated question {question_id}: Missing both 'text' and 'question' fields")
                updated_question["text"] = "Missing question text"
                updated_question["question"] = "Missing question text"

            # Ensure other required fields exist
            if "purpose" not in updated_question:
                logging.warning(f"Updated question {question_id}: Missing 'purpose' field")
                updated_question["purpose"] = "General evaluation"

            if "idealAnswerCriteria" not in updated_question:
                logging.warning(f"Updated question {question_id}: Missing 'idealAnswerCriteria' field")
                updated_question["idealAnswerCriteria"] = "Clear, concise, and relevant response"

            # Ensure statistics field exists
            if "statistics" not in updated_question:
                updated_question["statistics"] = {
                    "topScore": 0,
                    "topScoringCandidateId": "",
                    "averageScore": 0,
                    "totalAnswers": 0
                }

            # Update the updatedAt timestamp
            template_data['updatedAt'] = datetime.utcnow()

            # Update the template document
            doc_ref.update({
                'questions': template_data['questions'],
                'updatedAt': template_data['updatedAt']
            })

            logging.info(f"Successfully updated question {question_id} in template {template_id}")

        except Exception as e:
            logging.error(f"Error updating question in template: {str(e)}")
            raise

    async def delete_question_from_template(self, user_id: str, role_id: str, template_id: str, question_id: str) -> None:
        """Delete a question from an interview template."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not template_id:
                raise ValueError("template_id is required")
            if not question_id:
                raise ValueError("question_id is required")

            logging.info(f"Deleting question {question_id} from template {template_id} for role {role_id} (user {user_id})")

            # Get the template document reference
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document(template_id)

            # Get the current template
            doc = doc_ref.get()
            if not doc.exists:
                logging.error(f"Interview template {template_id} not found")
                raise ValueError(f"Interview template {template_id} not found")

            template_data = doc.to_dict()

            # Check if questions array exists
            if 'questions' not in template_data or not isinstance(template_data['questions'], list):
                logging.error(f"Questions array not found in template {template_id}")
                raise ValueError(f"Questions array not found in template {template_id}")

            # Filter out the question with the specified ID
            updated_questions = [q for q in template_data['questions'] if not (isinstance(q, dict) and q.get('id') == question_id)]

            # Check if the question was found and removed
            if len(updated_questions) == len(template_data['questions']):
                logging.error(f"Question {question_id} not found in template {template_id}")
                raise ValueError(f"Question {question_id} not found in template {template_id}")

            # Update the updatedAt timestamp
            template_data['updatedAt'] = datetime.utcnow()

            # Update the template document
            doc_ref.update({
                'questions': updated_questions,
                'updatedAt': template_data['updatedAt']
            })

            logging.info(f"Successfully deleted question {question_id} from template {template_id}")

        except Exception as e:
            logging.error(f"Error deleting question from template: {str(e)}")
            raise

    async def reorder_questions_in_template(self, user_id: str, role_id: str, template_id: str, question_ids: List[str]) -> None:
        """Reorder questions in an interview template."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not template_id:
                raise ValueError("template_id is required")
            if not question_ids:
                raise ValueError("question_ids is required")

            logging.info(f"Reordering questions in template {template_id} for role {role_id} (user {user_id})")

            # Get the template document reference
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document(template_id)

            # Get the current template
            doc = doc_ref.get()
            if not doc.exists:
                logging.error(f"Interview template {template_id} not found")
                raise ValueError(f"Interview template {template_id} not found")

            template_data = doc.to_dict()

            # Check if questions array exists
            if 'questions' not in template_data or not isinstance(template_data['questions'], list):
                logging.error(f"Questions array not found in template {template_id}")
                raise ValueError(f"Questions array not found in template {template_id}")

            # Create a dictionary of questions by ID for quick lookup
            questions_by_id = {q.get('id'): q for q in template_data['questions'] if isinstance(q, dict) and 'id' in q}

            # Check if all question IDs exist in the template
            for qid in question_ids:
                if qid not in questions_by_id:
                    logging.error(f"Question {qid} not found in template {template_id}")
                    raise ValueError(f"Question {qid} not found in template {template_id}")

            # Create the reordered questions array
            reordered_questions = [questions_by_id[qid] for qid in question_ids]

            # Update the updatedAt timestamp
            template_data['updatedAt'] = datetime.utcnow()

            # Update the template document
            doc_ref.update({
                'questions': reordered_questions,
                'updatedAt': template_data['updatedAt']
            })

            logging.info(f"Successfully reordered questions in template {template_id}")

        except Exception as e:
            logging.error(f"Error reordering questions in template: {str(e)}")
            raise

    # Evaluation Criteria Methods
    async def add_criterion_to_template(self, user_id: str, role_id: str, template_id: str, criterion_data: Dict[str, Any]) -> str:
        """Add an evaluation criterion to an interview template."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not template_id:
                raise ValueError("template_id is required")

            logging.info(f"Adding criterion to template {template_id} for role {role_id} (user {user_id})")

            # Generate a unique ID for the criterion
            criterion_id = generate_criterion_id()

            # Add the ID to the criterion data
            criterion_data["id"] = criterion_id

            # Get the template document reference
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document(template_id)

            # Get the current template
            doc = doc_ref.get()
            if not doc.exists:
                logging.error(f"Interview template {template_id} not found")
                raise ValueError(f"Interview template {template_id} not found")

            template_data = doc.to_dict()

            # Initialize evaluationCriteria array if it doesn't exist
            if 'evaluationCriteria' not in template_data or not isinstance(template_data['evaluationCriteria'], list):
                template_data['evaluationCriteria'] = []

            # Add the criterion to the evaluationCriteria array
            template_data['evaluationCriteria'].append(criterion_data)

            # Validate criteria consistency
            validation_result = validate_criteria_consistency(template_data['evaluationCriteria'])
            if not validation_result['valid']:
                logging.error(f"Invalid evaluation criteria: {validation_result['errors']}")
                raise ValueError(f"Invalid evaluation criteria: {', '.join(validation_result['errors'])}")

            # Update the updatedAt timestamp
            template_data['updatedAt'] = datetime.utcnow()

            # Update the template document
            doc_ref.update({
                'evaluationCriteria': template_data['evaluationCriteria'],
                'updatedAt': template_data['updatedAt']
            })

            logging.info(f"Successfully added criterion {criterion_id} to template {template_id}")

            return criterion_id

        except Exception as e:
            logging.error(f"Error adding criterion to template: {str(e)}")
            raise

    async def update_criterion_in_template(self, user_id: str, role_id: str, template_id: str, criterion_id: str, updates: Dict[str, Any]) -> None:
        """Update an evaluation criterion in an interview template."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not template_id:
                raise ValueError("template_id is required")
            if not criterion_id:
                raise ValueError("criterion_id is required")

            logging.info(f"Updating criterion {criterion_id} in template {template_id} for role {role_id} (user {user_id})")

            # Get the template document reference
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document(template_id)

            # Get the current template
            doc = doc_ref.get()
            if not doc.exists:
                logging.error(f"Interview template {template_id} not found")
                raise ValueError(f"Interview template {template_id} not found")

            template_data = doc.to_dict()

            # Check if evaluationCriteria array exists
            if 'evaluationCriteria' not in template_data or not isinstance(template_data['evaluationCriteria'], list):
                logging.error(f"Evaluation criteria array not found in template {template_id}")
                raise ValueError(f"Evaluation criteria array not found in template {template_id}")

            # Find the criterion by ID
            criterion_index = None
            for i, criterion in enumerate(template_data['evaluationCriteria']):
                if isinstance(criterion, dict) and criterion.get('id') == criterion_id:
                    criterion_index = i
                    break

            if criterion_index is None:
                logging.error(f"Criterion {criterion_id} not found in template {template_id}")
                raise ValueError(f"Criterion {criterion_id} not found in template {template_id}")

            # Update the criterion
            for key, value in updates.items():
                if key != 'id' and key != 'type':  # Don't update the ID or type
                    template_data['evaluationCriteria'][criterion_index][key] = value

            # Validate criteria consistency
            validation_result = validate_criteria_consistency(template_data['evaluationCriteria'])
            if not validation_result['valid']:
                logging.error(f"Invalid evaluation criteria: {validation_result['errors']}")
                raise ValueError(f"Invalid evaluation criteria: {', '.join(validation_result['errors'])}")

            # Update the updatedAt timestamp
            template_data['updatedAt'] = datetime.utcnow()

            # Update the template document
            doc_ref.update({
                'evaluationCriteria': template_data['evaluationCriteria'],
                'updatedAt': template_data['updatedAt']
            })

            logging.info(f"Successfully updated criterion {criterion_id} in template {template_id}")

        except Exception as e:
            logging.error(f"Error updating criterion in template: {str(e)}")
            raise

    async def delete_criterion_from_template(self, user_id: str, role_id: str, template_id: str, criterion_id: str) -> None:
        """Delete an evaluation criterion from an interview template."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not template_id:
                raise ValueError("template_id is required")
            if not criterion_id:
                raise ValueError("criterion_id is required")

            logging.info(f"Deleting criterion {criterion_id} from template {template_id} for role {role_id} (user {user_id})")

            # Get the template document reference
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document(template_id)

            # Get the current template
            doc = doc_ref.get()
            if not doc.exists:
                logging.error(f"Interview template {template_id} not found")
                raise ValueError(f"Interview template {template_id} not found")

            template_data = doc.to_dict()

            # Check if evaluationCriteria array exists
            if 'evaluationCriteria' not in template_data or not isinstance(template_data['evaluationCriteria'], list):
                logging.error(f"Evaluation criteria array not found in template {template_id}")
                raise ValueError(f"Evaluation criteria array not found in template {template_id}")

            # Filter out the criterion with the specified ID
            updated_criteria = [c for c in template_data['evaluationCriteria'] if not (isinstance(c, dict) and c.get('id') == criterion_id)]

            # Check if the criterion was found and removed
            if len(updated_criteria) == len(template_data['evaluationCriteria']):
                logging.error(f"Criterion {criterion_id} not found in template {template_id}")
                raise ValueError(f"Criterion {criterion_id} not found in template {template_id}")

            # Validate criteria consistency
            validation_result = validate_criteria_consistency(updated_criteria)
            if not validation_result['valid']:
                logging.error(f"Invalid evaluation criteria: {validation_result['errors']}")
                raise ValueError(f"Invalid evaluation criteria: {', '.join(validation_result['errors'])}")

            # Update the updatedAt timestamp
            template_data['updatedAt'] = datetime.utcnow()

            # Update the template document
            doc_ref.update({
                'evaluationCriteria': updated_criteria,
                'updatedAt': template_data['updatedAt']
            })

            logging.info(f"Successfully deleted criterion {criterion_id} from template {template_id}")

        except Exception as e:
            logging.error(f"Error deleting criterion from template: {str(e)}")
            raise

    async def set_template_pass_rate(self, user_id: str, role_id: str, template_id: str, pass_rate: float) -> None:
        """Set the pass rate for an interview template."""
        try:
            if not user_id:
                raise ValueError("user_id is required")
            if not role_id:
                raise ValueError("role_id is required")
            if not template_id:
                raise ValueError("template_id is required")
            if pass_rate < 0 or pass_rate > 1:
                raise ValueError("Pass rate must be between 0 and 1")

            logging.info(f"Setting pass rate to {pass_rate} for template {template_id} (role {role_id}, user {user_id})")

            # Get the template document reference
            doc_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('interviewTemplates').document(template_id)

            # Get the current template
            doc = doc_ref.get()
            if not doc.exists:
                logging.error(f"Interview template {template_id} not found")
                raise ValueError(f"Interview template {template_id} not found")

            # Update the updatedAt timestamp
            updated_at = datetime.utcnow()

            # Update the template document
            doc_ref.update({
                'passRate': pass_rate,
                'updatedAt': updated_at
            })

            logging.info(f"Successfully set pass rate to {pass_rate} for template {template_id}")

        except Exception as e:
            logging.error(f"Error setting pass rate for template: {str(e)}")
            raise

    async def create_interview_transcript(
        self,
        user_id: str,
        role_id: str,
        transcript_id: str,
        transcript_data: dict
    ) -> None:
        """
        Create a new interview transcript.

        Args:
            user_id: The ID of the user
            role_id: The ID of the role
            transcript_id: The ID of the transcript
            transcript_data: The transcript data
        """
        try:
            logging.info(f"Creating interview transcript {transcript_id} for role {role_id}")

            # Add metadata
            transcript_data["createdAt"] = firestore.SERVER_TIMESTAMP
            transcript_data["updatedAt"] = firestore.SERVER_TIMESTAMP
            transcript_data["id"] = transcript_id
            transcript_data["status"] = "in_progress"

            # Create the transcript
            transcript_ref = self.db.collection("users").document(user_id) \
                .collection("roles").document(role_id) \
                .collection("interview_transcripts").document(transcript_id)

            await transcript_ref.set(transcript_data)
            logging.info(f"Interview transcript {transcript_id} created successfully")
        except Exception as e:
            logging.error(f"Error creating interview transcript: {str(e)}")
            raise

    async def update_interview_transcript(
        self,
        user_id: str,
        role_id: str,
        transcript_id: str,
        updates: dict
    ) -> None:
        """
        Update an interview transcript.

        Args:
            user_id: The ID of the user
            role_id: The ID of the role
            transcript_id: The ID of the transcript
            updates: The updates to apply
        """
        try:
            logging.info(f"Updating interview transcript {transcript_id} for role {role_id}")

            # Add metadata
            updates["updatedAt"] = firestore.SERVER_TIMESTAMP

            # Update the transcript
            transcript_ref = self.db.collection("users").document(user_id) \
                .collection("roles").document(role_id) \
                .collection("interview_transcripts").document(transcript_id)

            await transcript_ref.update(updates)
            logging.info(f"Interview transcript {transcript_id} updated successfully")
        except Exception as e:
            logging.error(f"Error updating interview transcript: {str(e)}")
            raise

    async def complete_interview_transcript(
        self,
        user_id: str,
        role_id: str,
        transcript_id: str
    ) -> None:
        """
        Mark an interview transcript as completed.

        Args:
            user_id: The ID of the user
            role_id: The ID of the role
            transcript_id: The ID of the transcript
        """
        try:
            logging.info(f"Marking interview transcript {transcript_id} as completed")

            # Update the transcript
            transcript_ref = self.db.collection("users").document(user_id) \
                .collection("roles").document(role_id) \
                .collection("interview_transcripts").document(transcript_id)

            await transcript_ref.update({
                "status": "completed",
                "completedAt": firestore.SERVER_TIMESTAMP,
                "updatedAt": firestore.SERVER_TIMESTAMP
            })

            logging.info(f"Interview transcript {transcript_id} marked as completed")
        except Exception as e:
            logging.error(f"Error completing interview transcript: {str(e)}")
            raise

    async def get_interview_transcripts(
        self,
        user_id: str,
        role_id: str
    ) -> List[Dict[str, Any]]:
        """
        Get all interview transcripts for a role.

        Args:
            user_id: The ID of the user
            role_id: The ID of the role

        Returns:
            List of transcripts
        """
        try:
            logging.info(f"Getting interview transcripts for role {role_id}")

            # Get the transcripts
            transcripts_ref = self.db.collection("users").document(user_id) \
                .collection("roles").document(role_id) \
                .collection("interview_transcripts")

            # Firebase operations are synchronous, don't use await
            transcripts_docs = transcripts_ref.stream()

            # Convert to list of dictionaries
            transcripts = []
            for doc in transcripts_docs:
                transcript = doc.to_dict()
                transcript["id"] = doc.id
                transcripts.append(transcript)

            logging.info(f"Found {len(transcripts)} interview transcripts for role {role_id}")
            return transcripts
        except Exception as e:
            logging.error(f"Error getting interview transcripts: {str(e)}")
            return []

    async def get_interview_transcript(
        self,
        user_id: str,
        role_id: str,
        transcript_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a specific interview transcript.

        Args:
            user_id: The ID of the user
            role_id: The ID of the role
            transcript_id: The ID of the transcript

        Returns:
            The transcript or None if not found
        """
        try:
            logging.info(f"Getting interview transcript {transcript_id} for role {role_id}")

            # First, try to get the transcript from the user's collection
            transcript_ref = self.db.collection("users").document(user_id) \
                .collection("roles").document(role_id) \
                .collection("interview_transcripts").document(transcript_id)

            # Firebase operations are synchronous, don't use await
            transcript_doc = transcript_ref.get()

            if transcript_doc.exists:
                # Convert to dictionary
                transcript = transcript_doc.to_dict()
                transcript["id"] = transcript_doc.id
                logging.info(f"Found interview transcript {transcript_id} in user collection")
                return transcript

            # If not found in user collection, try public_interview_sessions
            logging.info(f"Transcript not found in user collection, checking public_interview_sessions")
            public_session_ref = self.db.collection("public_interview_sessions").document(transcript_id)
            public_session_doc = public_session_ref.get()

            if public_session_doc.exists:
                # Convert to dictionary
                session_data = public_session_doc.to_dict()

                # Format the session data to match the expected transcript format
                transcript = {
                    "id": transcript_id,
                    "role_id": session_data.get("role_id"),
                    "messages": session_data.get("messages", []),
                    "status": session_data.get("status", "completed"),
                    "createdAt": session_data.get("created_at"),
                    "updatedAt": session_data.get("updated_at"),
                    "template_id": session_data.get("template_id"),
                    "template": session_data.get("template"),
                    "applicationId": session_data.get("application_id"),
                    "candidateId": session_data.get("candidate_id")
                }

                logging.info(f"Found interview transcript {transcript_id} in public_interview_sessions")
                return transcript

            # If we get here, the transcript was not found in either location
            logging.warning(f"Interview transcript {transcript_id} not found in any collection")
            return None
        except Exception as e:
            logging.error(f"Error getting interview transcript: {str(e)}")
            return None

    async def save_interview_results(
        self,
        user_id: str,
        role_id: str,
        transcript_id: str,
        results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Save interview results.

        Args:
            user_id: The ID of the user
            role_id: The ID of the role
            transcript_id: The ID of the transcript
            results: The interview results

        Returns:
            The saved results
        """
        try:
            logging.info(f"Saving interview results for transcript {transcript_id}")

            # Add metadata
            results["createdAt"] = firestore.SERVER_TIMESTAMP
            results["updatedAt"] = firestore.SERVER_TIMESTAMP
            results["transcriptId"] = transcript_id

            # Create a unique ID for the results
            results_id = str(uuid.uuid4())
            results["id"] = results_id

            # Save the results
            results_ref = self.db.collection("users").document(user_id) \
                .collection("roles").document(role_id) \
                .collection("interview_results").document(results_id)

            await results_ref.set(results)

            # Update the transcript with the results ID
            transcript_ref = self.db.collection("users").document(user_id) \
                .collection("roles").document(role_id) \
                .collection("interview_transcripts").document(transcript_id)

            await transcript_ref.update({
                "resultsId": results_id,
                "updatedAt": firestore.SERVER_TIMESTAMP
            })

            logging.info(f"Interview results saved successfully with ID {results_id}")
            return results
        except Exception as e:
            logging.error(f"Error saving interview results: {str(e)}")
            raise

    async def get_interview_results(
        self,
        user_id: str,
        role_id: str,
        results_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a specific interview results document.

        Args:
            user_id: The ID of the user
            role_id: The ID of the role
            results_id: The ID of the results

        Returns:
            The results or None if not found
        """
        try:
            logging.info(f"Getting interview results {results_id} for role {role_id}")

            # Get the results
            results_ref = self.db.collection("users").document(user_id) \
                .collection("roles").document(role_id) \
                .collection("interview_results").document(results_id)

            # Firebase operations are synchronous, don't use await
            results_doc = results_ref.get()

            if not results_doc.exists:
                logging.warning(f"Interview results {results_id} not found")
                return None

            # Convert to dictionary
            results = results_doc.to_dict()
            results["id"] = results_doc.id

            logging.info(f"Found interview results {results_id}")
            return results
        except Exception as e:
            logging.error(f"Error getting interview results: {str(e)}")
            return None

    async def get_templates(self, role_id: str, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all templates for a role.

        Args:
            role_id: The ID of the role
            user_id: The ID of the user

        Returns:
            List of templates
        """
        try:
            logging.info(f"Getting templates for role {role_id}")
            return await self.get_interview_templates(user_id, role_id)
        except Exception as e:
            logging.error(f"Error getting templates: {str(e)}")
            return []

    async def get_template(self, role_id: str, template_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific template.

        Args:
            role_id: The ID of the role
            template_id: The ID of the template
            user_id: The ID of the user

        Returns:
            The template or None if not found
        """
        try:
            logging.info(f"Getting template {template_id} for role {role_id}")
            return await self.get_interview_template(user_id, role_id, template_id)
        except Exception as e:
            logging.error(f"Error getting template: {str(e)}")
            return None

    # Add new method for public interview sessions

    async def create_public_interview_session(self, session_data: Dict[str, Any]) -> str:
        """
        Create a record of a public interview session

        Parameters:
        - session_data: Data for the session including role_id, template_id and transcript_id

        Returns:
        - session_id: The ID of the created session
        """
        try:
            logging.info(f"Creating public interview session for role_id: {session_data.get('role_id')}")

            # Standardize field names - ensure we use consistent naming
            # Convert camelCase to snake_case for consistency
            if "applicationId" in session_data and "application_id" not in session_data:
                session_data["application_id"] = session_data["applicationId"]
            if "candidateId" in session_data and "candidate_id" not in session_data:
                session_data["candidate_id"] = session_data["candidateId"]
            if "roleId" in session_data and "role_id" not in session_data:
                session_data["role_id"] = session_data["roleId"]
            if "templateId" in session_data and "template_id" not in session_data:
                session_data["template_id"] = session_data["templateId"]

            # Get application and candidate IDs
            application_id = session_data.get("application_id") or session_data.get("applicationId")
            candidate_id = session_data.get("candidate_id") or session_data.get("candidateId")

            # If we have an application_id, try to get candidate info from the application
            candidate_name = None
            if application_id:
                try:
                    # Check if the application exists and get the candidate info
                    app_ref = self.db.collection("applications").document(application_id)
                    app_doc = app_ref.get()

                    if app_doc.exists:
                        app_data = app_doc.to_dict()
                        # Get candidate ID from the application if not provided
                        if not candidate_id:
                            candidate_id = app_data.get("candidate_id") or app_data.get("candidateId") or app_data.get("email")

                            if candidate_id:
                                logging.info(f"Found candidate_id {candidate_id} from application {application_id}")
                                session_data["candidate_id"] = candidate_id
                                # Also keep the camelCase version for backward compatibility
                                session_data["candidateId"] = candidate_id

                        # Get candidate name from the application
                        candidate_name = app_data.get("fullName") or app_data.get("full_name")
                        if candidate_name:
                            logging.info(f"Found candidate name '{candidate_name}' from application {application_id}")
                            session_data["candidateName"] = candidate_name
                            session_data["candidate_name"] = candidate_name
                except Exception as app_error:
                    logging.warning(f"Error getting candidate info from application: {str(app_error)}")

            # If we have a candidate_id but no name, try to get it from the candidates collection
            if candidate_id and not candidate_name:
                try:
                    # Check if the candidate exists and get their name
                    candidate_ref = self.db.collection("candidates").document(candidate_id)
                    candidate_doc = candidate_ref.get()

                    if candidate_doc.exists:
                        candidate_data = candidate_doc.to_dict()
                        # Get candidate name
                        candidate_name = candidate_data.get("fullName") or candidate_data.get("full_name")

                        if candidate_name:
                            logging.info(f"Found candidate name '{candidate_name}' from candidate {candidate_id}")
                            session_data["candidateName"] = candidate_name
                            session_data["candidate_name"] = candidate_name
                except Exception as candidate_error:
                    logging.warning(f"Error getting candidate name from candidates collection: {str(candidate_error)}")

            # Check if we have candidate information to link this session to
            # Check both applicationId and candidateId
            application_id = session_data.get("application_id") or session_data.get("applicationId")
            candidate_id = session_data.get("candidate_id") or session_data.get("candidateId")

            # First check if a session already exists for this application
            if application_id and candidate_id:
                logging.info(f"Checking for existing interview sessions for candidate_id={candidate_id}, application_id={application_id}")

                try:
                    # Check for any existing interviews for this application
                    interviews_ref = self.db.collection("candidates").document(candidate_id) \
                        .collection("applications").document(application_id) \
                        .collection("interviews")

                    # Get recent interviews (last 10 minutes) for this application
                    ten_mins_ago = datetime.utcnow() - timedelta(minutes=10)
                    existing_interviews = interviews_ref.where("created_at", ">", ten_mins_ago).get()

                    # If we found any recent interviews, use the most recent one instead of creating a new one
                    interview_list = list(existing_interviews)
                    if interview_list:
                        # Sort by created_at (newest first)
                        sorted_interviews = sorted(
                            interview_list,
                            key=lambda x: x.get("created_at").timestamp() if hasattr(x.get("created_at"), "timestamp") else 0,
                            reverse=True
                        )

                        most_recent = sorted_interviews[0].to_dict()
                        most_recent_id = sorted_interviews[0].id

                        # Check if it has a session_id and transcript_id
                        if most_recent.get("session_id") and most_recent.get("transcript_id"):
                            # Use the existing data
                            logging.info(f"Found recent interview {most_recent_id} created at {most_recent.get('created_at')} - reusing instead of creating new one")

                            # Return the existing session ID
                            return most_recent.get("session_id")
                except Exception as check_error:
                    logging.error(f"Error checking for existing interviews: {str(check_error)}")
                    # Continue execution to create a new session

            # Add created_at timestamp
            session_data["created_at"] = firestore.SERVER_TIMESTAMP

            # Ensure both snake_case and camelCase versions of IDs are present for compatibility
            # This ensures we can query by either naming convention
            if application_id:
                session_data["application_id"] = application_id
                session_data["applicationId"] = application_id
            if candidate_id:
                session_data["candidate_id"] = candidate_id
                session_data["candidateId"] = candidate_id

            # Get template information if template_id is provided
            template_id = session_data.get("template_id")
            if template_id:
                try:
                    # Try to get the template information
                    # First, find the user who owns the role
                    role_id = session_data.get("role_id")
                    if role_id:
                        # Query users collection to find who owns this role
                        users_ref = self.db.collection("users")
                        roles_query = users_ref.limit(10).stream()  # Limit to avoid excessive reads

                        user_id = None
                        for user_doc in roles_query:
                            # Check if this user has the role
                            role_ref = self.db.collection("users").document(user_doc.id).collection("roles").document(role_id)
                            if role_ref.get().exists:
                                user_id = user_doc.id
                                break

                        if user_id:
                            # Get the template
                            template_ref = self.db.collection("users").document(user_id).collection("roles").document(role_id).collection("interviewTemplates").document(template_id)
                            template_doc = template_ref.get()

                            if template_doc.exists:
                                template_data = template_doc.to_dict()
                                # Add template name to session data
                                session_data["template_name"] = template_data.get("name", "Interview Template")
                                session_data["templateName"] = template_data.get("name", "Interview Template")

                                # Add stage information if available
                                if "stage" in template_data:
                                    session_data["stage"] = template_data["stage"]
                                    session_data["interviewStage"] = template_data["stage"]
                                elif "type" in template_data:
                                    session_data["stage"] = template_data["type"]
                                    session_data["interviewStage"] = template_data["type"]

                                # Get role data to find the stage index
                                role_ref = self.db.collection("users").document(user_id).collection("roles").document(role_id)
                                role_doc = role_ref.get()

                                if role_doc.exists:
                                    role_data = role_doc.to_dict()
                                    if "interviewProcess" in role_data and isinstance(role_data["interviewProcess"], list):
                                        # Try to match the template to a stage in the interview process
                                        for i, stage in enumerate(role_data["interviewProcess"]):
                                            stage_name = stage.get("stage", "")
                                            if stage_name and (stage_name.lower() == session_data.get("stage", "").lower() or
                                                              stage_name.lower() == template_data.get("name", "").lower()):
                                                session_data["stage_index"] = i
                                                session_data["stageIndex"] = i
                                                session_data["stage"] = stage_name
                                                session_data["interviewStage"] = stage_name
                                                break
                except Exception as e:
                    logging.warning(f"Error getting template information: {str(e)}")
                    # Continue without template information

            # Store in a public sessions collection
            doc_ref = self.db.collection("public_interview_sessions").document(session_data["session_id"])
            doc_ref.set(session_data)  # Remove await since Firebase operations are not async

            if application_id and candidate_id:
                logging.info(f"Linking session to candidate application: candidate_id={candidate_id}, application_id={application_id}")

                # Check if an interview with this transcript ID already exists
                # Note: We're not actually awaiting this query since Firebase Admin SDK is synchronous
                try:
                    interviews_ref = self.db.collection("candidates").document(candidate_id) \
                        .collection("applications").document(application_id) \
                        .collection("interviews")

                    # Query existing interviews with this transcript ID
                    existing_interviews = interviews_ref.where("transcript_id", "==", session_data["transcript_id"]).get()

                    # If an interview already exists, don't create a duplicate
                    if len(list(existing_interviews)) > 0:
                        logging.info(f"Interview with transcript_id {session_data['transcript_id']} already exists for this application, skipping creation")
                        return session_data["session_id"]

                    # Now check for any interviews with the same session ID
                    existing_session = interviews_ref.where("session_id", "==", session_data["session_id"]).get()
                    if len(list(existing_session)) > 0:
                        logging.info(f"Interview with session_id {session_data['session_id']} already exists for this application, skipping creation")
                        return session_data["session_id"]

                    # Determine the stage index and name
                    stage_index = 0  # Default to screening (first stage)
                    stage_name = "Screening"

                    # If we have a template ID, extract the stage info
                    template_id = session_data.get("template_id")
                    if template_id:
                        try:
                            # Try to get the template to determine the stage
                            role_id = session_data["role_id"]
                            template_ref = self.db.collection("roles").document(role_id) \
                                .collection("templates").document(template_id)
                            template_doc = template_ref.get()

                            if template_doc.exists:
                                template_data = template_doc.to_dict()
                                stage_index = template_data.get("stageIndex", 0)
                                stage_name = template_data.get("stage", "Screening")
                        except Exception as template_error:
                            logging.warning(f"Error fetching template data: {str(template_error)}")

                    # Add the interview session to the candidate's application interviews subcollection
                    # Using transcript_id as the document ID to ensure uniqueness
                    interview_ref = interviews_ref.document(session_data["transcript_id"])

                    # Create the interview record with both snake_case and camelCase fields for compatibility
                    interview_data = {
                        "session_id": session_data["session_id"],
                        "transcript_id": session_data["transcript_id"],
                        "role_id": session_data["role_id"],
                        "application_id": application_id,  # Add application_id explicitly
                        "candidate_id": candidate_id,      # Add candidate_id explicitly
                        "template_id": session_data.get("template_id"),
                        "stage_index": stage_index,
                        "stage_name": stage_name,
                        "status": "in_progress",
                        "created_at": firestore.SERVER_TIMESTAMP,
                        "updated_at": firestore.SERVER_TIMESTAMP,
                        # Also add camelCase versions for backward compatibility
                        "sessionId": session_data["session_id"],
                        "transcriptId": session_data["transcript_id"],
                        "roleId": session_data["role_id"],
                        "applicationId": application_id,
                        "candidateId": candidate_id,
                        "templateId": session_data.get("template_id"),
                        "stageIndex": stage_index,
                        "stageName": stage_name
                    }

                    # Add template name if available
                    if "template_name" in session_data:
                        interview_data["template_name"] = session_data["template_name"]
                        interview_data["templateName"] = session_data["template_name"]

                    # Add stage information if available
                    if "stage" in session_data:
                        interview_data["stage"] = session_data["stage"]
                        interview_data["interviewStage"] = session_data["stage"]

                    # Add stage index if available
                    if "stage_index" in session_data:
                        interview_data["stage_index"] = session_data["stage_index"]
                        interview_data["stageIndex"] = session_data["stage_index"]

                    # Add candidate name if available
                    if "candidateName" in session_data:
                        interview_data["candidateName"] = session_data["candidateName"]
                        interview_data["candidate_name"] = session_data["candidateName"]
                    elif "candidate_name" in session_data:
                        interview_data["candidateName"] = session_data["candidate_name"]
                        interview_data["candidate_name"] = session_data["candidate_name"]

                    interview_ref.set(interview_data)
                    logging.info(f"Successfully created interview record in candidate application, stage: {stage_name} ({stage_index})")

                    # Also add to the main applications collection for easier querying
                    try:
                        # Create a reference to the application's interviews subcollection
                        app_interviews_ref = self.db.collection("applications").document(application_id).collection("interviews")

                        # Create the interview document with the same ID
                        app_interview_ref = app_interviews_ref.document(session_data["transcript_id"])
                        app_interview_ref.set(interview_data)

                        logging.info(f"Created interview record in main applications collection: {session_data['transcript_id']}")

                        # Update the application document to include a reference to this interview session
                        app_ref = self.db.collection("applications").document(application_id)
                        app_ref.update({
                            "updated_at": firestore.SERVER_TIMESTAMP,
                            "last_interview_id": session_data["transcript_id"],
                            "last_interview_session_id": session_data["session_id"],
                            "last_interview_timestamp": firestore.SERVER_TIMESTAMP
                        })
                        logging.info(f"Updated application {application_id} with reference to interview session")
                    except Exception as app_error:
                        logging.warning(f"Error creating interview in applications collection: {str(app_error)}")
                        # Continue execution - this is not critical
                except Exception as interview_error:
                    logging.error(f"Error linking interview to candidate: {str(interview_error)}")
            else:
                logging.info(f"No candidate/application info provided, session not linked to a candidate")

            # In a production environment, we would set a TTL for automatic cleanup
            # For Firebase, this would be handled through a scheduled function

            logging.info(f"Successfully created public interview session: {session_data['session_id']}")
            return session_data["session_id"]
        except Exception as e:
            logging.error(f"Error creating public interview session in Firebase: {str(e)}")
            raise ValueError(f"Failed to create public interview session: {str(e)}")

    async def get_application(self, application_id: str) -> Optional[Dict[str, Any]]:
        """
        Get application data by ID.

        This method attempts to find an application in Firebase, first in the global applications collection,
        and if not found, it will search within candidates' applications subcollections.
        It also enriches the application with role information, evaluations, and interview data.

        Args:
            application_id: The ID of the application to retrieve

        Returns:
            The application data if found, otherwise None
        """
        try:
            # First, check in the applications collection
            application_ref = self.db.collection("applications").document(application_id)
            application_doc = application_ref.get()
            application_data = None

            if application_doc.exists:
                application_data = application_doc.to_dict()
                application_data["id"] = application_id
                logging.info(f"Found application {application_id} in applications collection")
            else:
                # If not found in main collection, search in all candidates' applications
                logging.info(f"Application {application_id} not found in main collection, searching in candidates")

                # Get all candidates
                candidates_ref = self.db.collection("candidates")
                candidates = candidates_ref.stream()

                # Iterate through candidates to check for the application
                for candidate_doc in candidates:
                    candidate_id = candidate_doc.id
                    app_ref = self.db.collection("candidates").document(candidate_id).collection("applications").document(application_id)
                    app_doc = app_ref.get()

                    if app_doc.exists:
                        application_data = app_doc.to_dict()
                        application_data["id"] = application_id
                        logging.info(f"Found application {application_id} in candidate {candidate_id}'s applications")
                        break

            # If application was not found in any collection
            if not application_data:
                logging.warning(f"Application {application_id} not found")
                return None

            # Enrich application with role information
            role_id = application_data.get("roleId")
            logging.info(f"Looking up role information for role_id: {role_id}")

            if role_id:
                try:
                    # First try to get from global roles collection
                    role_ref = self.db.collection("roles").document(role_id)
                    role_doc = role_ref.get()

                    if role_doc.exists:
                        role_data = role_doc.to_dict()
                        logging.info(f"Found role in global roles collection: {role_data}")
                        application_data["roleName"] = role_data.get("title", "Untitled Role")
                        # Also add the full role data for reference
                        application_data["role"] = role_data
                    else:
                        # If not found in global collection, try to find the user who owns this role
                        logging.info("Role not found in global collection, searching in user collections")
                        users_ref = self.db.collection("users")
                        users_docs = users_ref.stream()

                        role_found = False
                        for user_doc in users_docs:
                            user_id = user_doc.id
                            user_role_ref = self.db.collection("users").document(user_id).collection("roles").document(role_id)
                            user_role_doc = user_role_ref.get()

                            if user_role_doc.exists:
                                role_data = user_role_doc.to_dict()
                                logging.info(f"Found role in user {user_id}'s collection: {role_data}")
                                application_data["roleName"] = role_data.get("title", "Untitled Role")
                                # Also add the full role data for reference
                                application_data["role"] = role_data
                                role_found = True
                                break

                        if not role_found:
                            # As a last resort, check if the role information is already in the application data
                            if "role" in application_data and isinstance(application_data["role"], dict):
                                logging.info("Using role information from application data")
                                if "title" in application_data["role"]:
                                    application_data["roleName"] = application_data["role"]["title"]
                            elif "roleName" in application_data:
                                logging.info(f"Role name already in application data: {application_data['roleName']}")
                            else:
                                logging.warning(f"Role {role_id} not found in any collection")
                                application_data["roleName"] = "Unknown Role"
                except Exception as role_error:
                    logging.error(f"Error fetching role data for application {application_id}: {str(role_error)}")
                    # Don't fail the whole request if role lookup fails
                    application_data["roleName"] = "Unknown Role"

            # Get resume evaluation data
            try:
                evaluations_ref = self.db.collection("applications").document(application_id).collection("evaluations")
                evaluations_docs = evaluations_ref.stream()
                evaluations = []

                for eval_doc in evaluations_docs:
                    eval_data = eval_doc.to_dict()
                    eval_data["id"] = eval_doc.id
                    evaluations.append(eval_data)

                if evaluations:
                    # Use the most recent evaluation (assuming they're ordered by creation time)
                    latest_evaluation = evaluations[0]

                    # Add evaluation data to the application
                    logging.info(f"Processing evaluation data for application {application_id}")
                    logging.info(f"Evaluation keys: {list(latest_evaluation.keys())}")

                    # Create a comprehensive evaluation data object
                    evaluation_data = {}

                    # First, try to get structured evaluation data
                    if "evaluation_data" in latest_evaluation:
                        evaluation_data = latest_evaluation["evaluation_data"]
                        logging.info("Found evaluation_data field")
                    elif "data" in latest_evaluation:
                        evaluation_data = latest_evaluation["data"]
                        logging.info("Found data field")

                    # Then, add any root-level fields that might be useful
                    for field in ["decision", "confidence", "reasoning", "keyStrengths", "keyGaps",
                                 "overallScore", "metadata", "full_evaluation"]:
                        if field in latest_evaluation and field not in evaluation_data:
                            evaluation_data[field] = latest_evaluation[field]
                            logging.info(f"Added {field} from root level")

                    # If we have full_evaluation, extract its contents too
                    if "full_evaluation" in evaluation_data and isinstance(evaluation_data["full_evaluation"], dict):
                        logging.info("Extracting data from full_evaluation")
                        for key, value in evaluation_data["full_evaluation"].items():
                            if key not in evaluation_data:
                                evaluation_data[key] = value
                                logging.info(f"Added {key} from full_evaluation")

                    # Make sure we have the parsed resume text
                    # First ensure we have a metadata object
                    if "metadata" not in evaluation_data:
                        evaluation_data["metadata"] = {}

                    # Check various places where the resume text might be stored
                    if "parsedText" not in evaluation_data["metadata"]:
                        # Check if parsedText is at the root level of the evaluation
                        if "parsedText" in latest_evaluation:
                            evaluation_data["metadata"]["parsedText"] = latest_evaluation["parsedText"]
                            logging.info("Added parsedText from root level")
                        # Or check if it's in the resume_text field
                        elif "resume_text" in latest_evaluation:
                            evaluation_data["metadata"]["parsedText"] = latest_evaluation["resume_text"]
                            logging.info("Added resume_text as parsedText")
                        # Or check if it's in the metadata at the root level
                        elif "metadata" in latest_evaluation and "parsedText" in latest_evaluation["metadata"]:
                            evaluation_data["metadata"]["parsedText"] = latest_evaluation["metadata"]["parsedText"]
                            logging.info("Added parsedText from root metadata")

                    # Also add the resume text directly to the application data for easier access
                    if "metadata" in evaluation_data and "parsedText" in evaluation_data["metadata"]:
                        application_data["resumeText"] = evaluation_data["metadata"]["parsedText"]
                        logging.info("Added resumeText to application data")

                    # Only set evaluationData if we found some relevant fields
                    if evaluation_data:
                        application_data["evaluationData"] = evaluation_data
                        logging.info(f"Final evaluation data keys: {list(evaluation_data.keys())}")
                    else:
                        logging.warning(f"No evaluation data found for application {application_id}")
            except Exception as eval_error:
                logging.error(f"Error fetching evaluation data for application {application_id}: {str(eval_error)}")
                # Don't fail the whole request if evaluation lookup fails

            # Get interview data
            try:
                interviews_ref = self.db.collection("applications").document(application_id).collection("interviews")
                interviews_docs = interviews_ref.stream()
                interviews = []

                for interview_doc in interviews_docs:
                    interview_data = interview_doc.to_dict()
                    interview_data["id"] = interview_doc.id

                    # If this interview has a transcript ID, try to get the transcript
                    transcript_id = interview_data.get("transcript_id")
                    if transcript_id:
                        try:
                            # Check in public_interview_sessions first
                            transcript_ref = self.db.collection("public_interview_sessions").document(transcript_id)
                            transcript_doc = transcript_ref.get()

                            if transcript_doc.exists:
                                transcript_data = transcript_doc.to_dict()
                                interview_data["transcript"] = transcript_data
                            else:
                                # If not found, it might be in a user's collection - but we'd need the user ID
                                # This is a limitation of the current data model
                                interview_data["transcript"] = {"id": transcript_id, "status": "unavailable"}
                        except Exception as transcript_error:
                            logging.error(f"Error fetching transcript {transcript_id}: {str(transcript_error)}")

                    interviews.append(interview_data)

                if interviews:
                    application_data["interviews"] = interviews

                    # Also set the interview stage from the first interview if available
                    if "interviewStage" not in application_data and interviews[0].get("stage_name"):
                        application_data["interviewStage"] = interviews[0].get("stage_name")

                    # Check for evaluations in the user's role collections for each interview
                    # First, try to get the user ID from the role data
                    user_id = None
                    if application_data.get("role") and application_data["role"].get("user_id"):
                        user_id = application_data["role"]["user_id"]
                        logging.info(f"Found user_id {user_id} from role data")
                    elif application_data.get("role") and application_data["role"].get("hiringManagerId"):
                        user_id = application_data["role"]["hiringManagerId"]
                        logging.info(f"Found user_id {user_id} from hiringManagerId")

                    if user_id and role_id:
                        logging.info(f"Checking for evaluations in user {user_id}'s role {role_id} collections for {len(interviews)} interviews")

                        # First, get all evaluations for this role to avoid multiple queries
                        try:
                            all_evals_ref = self.db.collection("users").document(user_id) \
                                            .collection("roles").document(role_id) \
                                            .collection("evaluations")

                            all_evals = list(all_evals_ref.stream())
                            logging.info(f"Found {len(all_evals)} evaluations in user {user_id}'s role {role_id} collection")

                            # Create dictionaries for different ways to match evaluations
                            evals_by_id = {}  # Match by id field (which contains session_id)
                            evals_by_transcript_id = {}  # Match by transcript_id field
                            evals_by_interview_id = {}  # Match by interview_id field

                            # Log all evaluation documents for debugging
                            for eval_doc in all_evals:
                                eval_data = eval_doc.to_dict()
                                eval_id = eval_doc.id
                                logging.info(f"Evaluation {eval_id} data: {eval_data}")

                                # Index by different fields for matching
                                if "id" in eval_data:
                                    evals_by_id[eval_data["id"]] = {"id": eval_id, "data": eval_data}
                                if "transcript_id" in eval_data:
                                    evals_by_transcript_id[eval_data["transcript_id"]] = {"id": eval_id, "data": eval_data}
                                if "interview_id" in eval_data:
                                    evals_by_interview_id[eval_data["interview_id"]] = {"id": eval_id, "data": eval_data}

                            # Log the keys we have for matching
                            logging.info(f"Evaluation id keys: {list(evals_by_id.keys())}")
                            logging.info(f"Evaluation transcript_id keys: {list(evals_by_transcript_id.keys())}")
                            logging.info(f"Evaluation interview_id keys: {list(evals_by_interview_id.keys())}")

                            # Now also check in the interviews/{interview_id}/evaluations path
                            logging.info(f"Checking for evaluations in interviews subcollection")

                            # Get all interviews for this role
                            interviews_ref = self.db.collection("users").document(user_id) \
                                            .collection("roles").document(role_id) \
                                            .collection("interviews")

                            interviews_docs = list(interviews_ref.stream())
                            logging.info(f"Found {len(interviews_docs)} interviews in user {user_id}'s role {role_id} collection")

                            # Check each interview for evaluations
                            for interview_doc in interviews_docs:
                                interview_id = interview_doc.id
                                logging.info(f"Checking for evaluations in interview {interview_id}")

                                # Get evaluations for this interview
                                interview_evals_ref = self.db.collection("users").document(user_id) \
                                                    .collection("roles").document(role_id) \
                                                    .collection("interviews").document(interview_id) \
                                                    .collection("evaluations")

                                interview_evals = list(interview_evals_ref.stream())
                                logging.info(f"Found {len(interview_evals)} evaluations in interview {interview_id}")

                                # Add each evaluation to our dictionaries
                                for eval_doc in interview_evals:
                                    eval_data = eval_doc.to_dict()
                                    eval_id = eval_doc.id
                                    logging.info(f"Interview evaluation {eval_id} data: {eval_data}")

                                    # Store the interview ID and evaluation ID mapping
                                    evals_by_id[interview_id] = {"id": eval_id, "data": eval_data, "interview_id": interview_id}

                                    # Also store by transcript_id if available
                                    if "transcript_id" in eval_data:
                                        evals_by_transcript_id[eval_data["transcript_id"]] = {"id": eval_id, "data": eval_data, "interview_id": interview_id}

                                    # Also store by session_id if that's in the id field
                                    if "id" in eval_data:
                                        evals_by_id[eval_data["id"]] = {"id": eval_id, "data": eval_data, "interview_id": interview_id}

                            # Log the updated keys we have for matching
                            logging.info(f"Updated evaluation id keys: {list(evals_by_id.keys())}")
                            logging.info(f"Updated evaluation transcript_id keys: {list(evals_by_transcript_id.keys())}")

                        except Exception as e:
                            logging.error(f"Error fetching evaluations: {str(e)}")
                            all_evals = []
                            evals_by_id = {}
                            evals_by_transcript_id = {}
                            evals_by_interview_id = {}

                        for interview in interviews:
                            interview_id = interview.get("id")
                            if interview_id and not interview.get("evaluation_id") and not interview.get("evaluationId"):
                                # Log the interview data for debugging
                                logging.info(f"Processing interview: {interview_id}")
                                logging.info(f"Interview data: {interview}")

                                evaluation_id = None

                                # Try to match by different fields
                                # 1. Try to match by session_id (stored in id field of evaluation)
                                session_id = interview.get("session_id")
                                if session_id and session_id in evals_by_id:
                                    evaluation_id = evals_by_id[session_id]["id"]
                                    logging.info(f"Found evaluation {evaluation_id} by matching session_id {session_id} with id field")

                                # 2. Try to match by transcript_id
                                transcript_id = interview.get("transcript_id")
                                if not evaluation_id and transcript_id and transcript_id in evals_by_transcript_id:
                                    evaluation_id = evals_by_transcript_id[transcript_id]["id"]
                                    logging.info(f"Found evaluation {evaluation_id} by matching transcript_id {transcript_id}")

                                # 3. Try to match by interview_id
                                if not evaluation_id and interview_id in evals_by_interview_id:
                                    evaluation_id = evals_by_interview_id[interview_id]["id"]
                                    logging.info(f"Found evaluation {evaluation_id} by matching interview_id {interview_id}")

                                # 4. If we still don't have a match, try direct queries
                                if not evaluation_id:
                                    try:
                                        # Try by id field (session_id)
                                        if session_id:
                                            evals_ref = self.db.collection("users").document(user_id) \
                                                        .collection("roles").document(role_id) \
                                                        .collection("evaluations").where("id", "==", session_id).limit(1)

                                            evals = list(evals_ref.stream())
                                            if evals:
                                                eval_doc = evals[0]
                                                evaluation_id = eval_doc.id
                                                logging.info(f"Found evaluation {evaluation_id} by querying for id={session_id}")

                                        # Try by transcript_id
                                        if not evaluation_id and transcript_id:
                                            evals_ref = self.db.collection("users").document(user_id) \
                                                        .collection("roles").document(role_id) \
                                                        .collection("evaluations").where("transcript_id", "==", transcript_id).limit(1)

                                            evals = list(evals_ref.stream())
                                            if evals:
                                                eval_doc = evals[0]
                                                evaluation_id = eval_doc.id
                                                logging.info(f"Found evaluation {evaluation_id} by querying for transcript_id={transcript_id}")

                                        # If we still don't have a match and there's only one evaluation, use it
                                        if not evaluation_id and len(all_evals) == 1:
                                            evaluation_id = all_evals[0].id
                                            logging.info(f"Using the only available evaluation {evaluation_id} as fallback")
                                    except Exception as e:
                                        logging.error(f"Error querying for evaluation: {str(e)}")

                                # If we found an evaluation, update the interview
                                if evaluation_id:
                                    # Check if we need to include the interview_id in the path
                                    interview_path = ""
                                    eval_data = None

                                    if "interview_id" in evals_by_id.get(session_id, {}) or "interview_id" in evals_by_transcript_id.get(transcript_id, {}):
                                        # This is an evaluation in the interviews/{interview_id}/evaluations subcollection
                                        # We need to include the interview_id in the path when accessing the evaluation
                                        if session_id and session_id in evals_by_id and "interview_id" in evals_by_id[session_id]:
                                            interview_path = evals_by_id[session_id]["interview_id"]
                                            eval_data = evals_by_id[session_id]["data"]
                                        elif transcript_id and transcript_id in evals_by_transcript_id and "interview_id" in evals_by_transcript_id[transcript_id]:
                                            interview_path = evals_by_transcript_id[transcript_id]["interview_id"]
                                            eval_data = evals_by_transcript_id[transcript_id]["data"]

                                        logging.info(f"Evaluation is in interviews/{interview_path}/evaluations subcollection")

                                    # Update the interview with the evaluation ID and path
                                    interview["evaluation_id"] = evaluation_id
                                    interview["evaluationId"] = evaluation_id  # Add both for compatibility
                                    if interview_path:
                                        interview["evaluation_interview_path"] = interview_path

                                    # Now fetch the evaluation data to get the decision and score
                                    try:
                                        # If we already have the evaluation data from our lookup, use it
                                        if not eval_data:
                                            # Otherwise, fetch the evaluation data
                                            if interview_path:
                                                # Evaluation is in the interviews/{interview_id}/evaluations subcollection
                                                eval_ref = self.db.collection("users").document(user_id) \
                                                            .collection("roles").document(role_id) \
                                                            .collection("interviews").document(interview_path) \
                                                            .collection("evaluations").document(evaluation_id)
                                            else:
                                                # Evaluation is in the roles/{role_id}/evaluations subcollection
                                                eval_ref = self.db.collection("users").document(user_id) \
                                                            .collection("roles").document(role_id) \
                                                            .collection("evaluations").document(evaluation_id)

                                            eval_doc = eval_ref.get()
                                            if eval_doc.exists:
                                                eval_data = eval_doc.to_dict()
                                                logging.info(f"Fetched evaluation data for {evaluation_id}")

                                        # Extract decision and score from evaluation data
                                        if eval_data:
                                            # Extract decision
                                            decision = None
                                            if "decision" in eval_data:
                                                decision = eval_data["decision"]
                                            elif "evaluation_data" in eval_data and "decision" in eval_data["evaluation_data"]:
                                                decision = eval_data["evaluation_data"]["decision"]
                                            elif "full_evaluation" in eval_data and "decision" in eval_data["full_evaluation"]:
                                                decision = eval_data["full_evaluation"]["decision"]
                                            elif "evaluation_summary" in eval_data and "decision" in eval_data["evaluation_summary"]:
                                                decision = eval_data["evaluation_summary"]["decision"]

                                            # Extract score
                                            score = None
                                            if "overall_score" in eval_data:
                                                score = eval_data["overall_score"]
                                            elif "overallScore" in eval_data:
                                                score = eval_data["overallScore"]
                                            elif "evaluation_data" in eval_data and "overall_score" in eval_data["evaluation_data"]:
                                                score = eval_data["evaluation_data"]["overall_score"]
                                            elif "evaluation_data" in eval_data and "overallScore" in eval_data["evaluation_data"]:
                                                score = eval_data["evaluation_data"]["overallScore"]
                                            elif "full_evaluation" in eval_data and "overall_score" in eval_data["full_evaluation"]:
                                                score = eval_data["full_evaluation"]["overall_score"]
                                            elif "full_evaluation" in eval_data and "overallScore" in eval_data["full_evaluation"]:
                                                score = eval_data["full_evaluation"]["overallScore"]
                                            elif "evaluation_summary" in eval_data and "overall_score" in eval_data["evaluation_summary"]:
                                                score = eval_data["evaluation_summary"]["overall_score"]

                                            # Add decision and score to interview
                                            if decision:
                                                interview["decision"] = decision
                                                logging.info(f"Added decision {decision} to interview {interview_id}")

                                            if score is not None:
                                                interview["overall_score"] = score
                                                interview["overallScore"] = score  # Add both for compatibility
                                                logging.info(f"Added score {score} to interview {interview_id}")

                                            # Also add the evaluation data to the interview
                                            interview["evaluation"] = {
                                                "id": evaluation_id,
                                                "decision": decision,
                                                "overall_score": score,
                                                "overallScore": score
                                            }
                                    except Exception as eval_error:
                                        logging.error(f"Error fetching evaluation data: {str(eval_error)}")
                                        # Continue even if evaluation data fetch fails

                                    # Also update the interview document in Firestore
                                    try:
                                        interview_ref = self.db.collection("applications").document(application_id) \
                                                        .collection("interviews").document(interview_id)

                                        update_data = {
                                            "evaluation_id": evaluation_id,
                                            "evaluationId": evaluation_id,
                                            "updated_at": firestore.SERVER_TIMESTAMP
                                        }

                                        if interview_path:
                                            update_data["evaluation_interview_path"] = interview_path

                                        # Add decision and score to update data if available
                                        if "decision" in interview:
                                            update_data["decision"] = interview["decision"]

                                        if "overall_score" in interview:
                                            update_data["overall_score"] = interview["overall_score"]
                                            update_data["overallScore"] = interview["overall_score"]

                                        interview_ref.update(update_data)
                                        logging.info(f"Updated interview document {interview_id} with evaluation ID {evaluation_id}")
                                    except Exception as update_error:
                                        logging.error(f"Error updating interview document: {str(update_error)}")
                                        # Continue even if update fails
                                else:
                                    logging.warning(f"No evaluation found for interview {interview_id} after all attempts")
                    else:
                        if not user_id:
                            logging.warning(f"Could not determine user_id for application {application_id}")
                        if not role_id:
                            logging.warning(f"No role_id found for application {application_id}")
            except Exception as interview_error:
                logging.error(f"Error fetching interview data for application {application_id}: {str(interview_error)}")
                # Don't fail the whole request if interview lookup fails

            return application_data

        except Exception as e:
            logging.error(f"Error getting application {application_id}: {str(e)}")
            logging.error(traceback.format_exc())
            return None

    async def get_public_interview_sessions_for_role(
        self,
        role_id: str
    ) -> List[Dict[str, Any]]:
        """
        Get all public interview sessions for a role.

        Args:
            role_id: The ID of the role

        Returns:
            List of public interview sessions with transcripts
        """
        try:
            if not role_id:
                logging.warning("Role ID is required for get_public_interview_sessions_for_role")
                return []

            logging.info(f"Getting public interview sessions for role {role_id}")

            # Query public_interview_sessions collection
            try:
                sessions_ref = self.db.collection("public_interview_sessions").where("role_id", "==", role_id)
                sessions_docs = sessions_ref.stream()
            except Exception as query_error:
                logging.error(f"Error querying public_interview_sessions: {str(query_error)}")
                return []

            # Convert to list of dictionaries
            sessions = []
            for doc in sessions_docs:
                session = doc.to_dict()

                # Only include sessions that have transcript data
                if "transcript" in session:
                    # Format to match the structure of interview_transcripts
                    transcript = {
                        "id": session.get("transcript_id", doc.id),
                        "type": "interview",
                        "status": session.get("status", "completed"),
                        "createdAt": session.get("created_at", datetime.now()),
                        "updatedAt": session.get("updated_at", datetime.now()),
                        "messages": session.get("transcript", {}).get("messages", []),
                        "messageCount": session.get("transcript", {}).get("message_count", 0),
                        "session_id": session.get("session_id"),
                        "candidateId": session.get("candidateId"),
                        "applicationId": session.get("applicationId"),
                        "is_public": True
                    }
                    sessions.append(transcript)

            logging.info(f"Found {len(sessions)} public interview sessions with transcripts for role {role_id}")
            return sessions
        except Exception as e:
            logging.error(f"Error getting public interview sessions for role: {str(e)}")
            return []

    async def get_public_interview_sessions_count(
        self,
        role_ids: List[str]
    ) -> Dict[str, int]:
        """
        Get a count of public interview sessions for multiple roles efficiently.
        This is an optimized version that only counts sessions without retrieving all data.

        Args:
            role_ids: List of role IDs to count sessions for

        Returns:
            Dictionary with total count and this week count
        """
        try:
            if not role_ids:
                logging.warning("Role IDs are required for get_public_interview_sessions_count")
                return {"total": 0, "this_week": 0}

            logging.info(f"Getting public interview sessions count for {len(role_ids)} roles")

            # Get the start of the current week (use UTC for consistency)
            now = datetime.utcnow()
            start_of_week = now - timedelta(days=now.weekday())
            start_of_week = start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)

            total_count = 0
            this_week_count = 0

            # Process roles in batches to avoid excessive queries
            batch_size = 10
            for i in range(0, len(role_ids), batch_size):
                batch_role_ids = role_ids[i:i+batch_size]

                # For each batch, query all sessions at once
                try:
                    # We can't do a direct "where in" query with Firestore for role_id
                    # So we'll do individual queries for each role in the batch
                    for role_id in batch_role_ids:
                        # Count total sessions for this role
                        sessions_ref = self.db.collection("public_interview_sessions").where("role_id", "==", role_id)
                        sessions = list(sessions_ref.stream())
                        role_count = len(sessions)
                        total_count += role_count

                        # Count sessions from this week
                        for session_doc in sessions:
                            session = session_doc.to_dict()
                            created_at = session.get("created_at") or session.get("createdAt")

                            if created_at:
                                # Convert to datetime if it's a string
                                if isinstance(created_at, str):
                                    try:
                                        if 'Z' in created_at:
                                            created_at = created_at.replace('Z', '+00:00')
                                        created_at = datetime.fromisoformat(created_at)
                                    except ValueError:
                                        # Skip if we can't parse the date
                                        continue

                                # Convert to UTC naive datetime for comparison
                                if hasattr(created_at, 'tzinfo') and created_at.tzinfo is not None:
                                    created_at = created_at.astimezone(timezone.utc).replace(tzinfo=None)

                                # Compare with start of week
                                if created_at >= start_of_week:
                                    this_week_count += 1

                except Exception as batch_error:
                    logging.warning(f"Error processing batch of roles: {str(batch_error)}")
                    # Continue with the next batch

            logging.info(f"Found {total_count} total sessions, {this_week_count} from this week")
            return {
                "total": total_count,
                "this_week": this_week_count
            }
        except Exception as e:
            logging.error(f"Error getting public interview sessions count: {str(e)}")
            return {"total": 0, "this_week": 0}

    async def get_applications_for_user(self, user_id: str, limit: int = None) -> List[Dict[str, Any]]:
        """
        Get all applications for a user, including those from public_applications collection.

        Args:
            user_id: The ID of the user
            limit: Optional limit on the number of applications to return

        Returns:
            List of applications with role information
        """
        try:
            logger.info(f"Getting applications for user {user_id}" + (f" with limit {limit}" if limit else ""))
            applications = []

            # Get all roles for this user
            roles = await self.get_roles(user_id)
            role_ids = [role["id"] for role in roles]

            # Query applications collection for applications matching these role IDs
            for role_id in role_ids:
                # Check main applications collection
                apps_ref = self.db.collection("applications").where("roleId", "==", role_id)
                apps_docs = apps_ref.stream()

                for doc in apps_docs:
                    app_data = doc.to_dict()
                    app_data["id"] = doc.id

                    # Add role information
                    matching_role = next((role for role in roles if role["id"] == role_id), None)
                    if matching_role:
                        app_data["roleName"] = matching_role.get("title", "Untitled Role")

                    applications.append(app_data)

                # Check public_applications collection
                public_apps_ref = self.db.collection("public_applications").where("roleId", "==", role_id)
                public_apps_docs = public_apps_ref.stream()

                for doc in public_apps_docs:
                    app_data = doc.to_dict()
                    app_data["id"] = doc.id

                    # Add role information
                    matching_role = next((role for role in roles if role["id"] == role_id), None)
                    if matching_role:
                        app_data["roleName"] = matching_role.get("title", "Untitled Role")

                    applications.append(app_data)

            # Apply limit if specified
            if limit and len(applications) > limit:
                applications = applications[:limit]
                logger.info(f"Limited to {limit} applications out of {len(applications)} total")

            logger.info(f"Found {len(applications)} applications for user {user_id}")
            return applications
        except Exception as e:
            logger.error(f"Error getting applications for user: {str(e)}")
            return []

    async def get_resume_evaluation(self, user_id: str, role_id: str, application_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the resume evaluation for an application.

        Args:
            user_id: The ID of the user
            role_id: The ID of the role
            application_id: The ID of the application

        Returns:
            The resume evaluation data if found, otherwise None
        """
        try:
            logger.info(f"Fetching resume evaluation for application {application_id} in role {role_id}")

            # First try to get the evaluation from the role's evaluations collection
            evaluations_ref = self.db.collection("users").document(user_id) \
                            .collection("roles").document(role_id) \
                            .collection("evaluations")

            logger.info(f"Searching for evaluations in users/{user_id}/roles/{role_id}/evaluations with application_id={application_id}")

            # Query for evaluations with this application ID
            query = evaluations_ref.where("application_id", "==", application_id).limit(1)
            evaluations = list(query.stream())

            # If no results, try with applicationId (camelCase)
            if not evaluations:
                logger.info(f"No evaluations found with application_id, trying applicationId")
                query = evaluations_ref.where("applicationId", "==", application_id).limit(1)
                evaluations = list(query.stream())

            # Log the number of evaluations found
            logger.info(f"Found {len(evaluations)} evaluations for application {application_id} in role {role_id}")

            if evaluations:
                # Found an evaluation
                evaluation_doc = evaluations[0]
                evaluation_data = evaluation_doc.to_dict()
                evaluation_data["id"] = evaluation_doc.id

                # Only log the ID, not the entire evaluation data
                logger.debug(f"Found evaluation with ID: {evaluation_data['id']}")

                # Extract decision and score
                decision = None
                score = None

                # Try to get decision from various locations
                if "decision" in evaluation_data and evaluation_data["decision"]:
                    decision = evaluation_data["decision"]
                elif "evaluation_data" in evaluation_data:
                    eval_data = evaluation_data["evaluation_data"]
                    if "decision" in eval_data and eval_data["decision"]:
                        decision = eval_data["decision"]
                    elif "full_evaluation" in eval_data and "decision" in eval_data["full_evaluation"]:
                        decision = eval_data["full_evaluation"]["decision"]
                elif "evaluation_summary" in evaluation_data and "decision" in evaluation_data["evaluation_summary"]:
                    decision = evaluation_data["evaluation_summary"]["decision"]

                # Log the extracted decision
                logger.info(f"Extracted decision: {decision} from evaluation data")

                # Try to get score from various locations
                if "overall_score" in evaluation_data and evaluation_data["overall_score"]:
                    score = evaluation_data["overall_score"]
                elif "overallScore" in evaluation_data and evaluation_data["overallScore"]:
                    score = evaluation_data["overallScore"]
                elif "evaluation_data" in evaluation_data:
                    eval_data = evaluation_data["evaluation_data"]
                    if "overall_score" in eval_data and eval_data["overall_score"]:
                        score = eval_data["overall_score"]
                    elif "overallScore" in eval_data and eval_data["overallScore"]:
                        score = eval_data["overallScore"]
                    elif "full_evaluation" in eval_data:
                        full_eval = eval_data["full_evaluation"]
                        if "overall_score" in full_eval and full_eval["overall_score"]:
                            score = full_eval["overall_score"]
                        elif "overallScore" in full_eval and full_eval["overallScore"]:
                            score = full_eval["overallScore"]
                elif "evaluation_summary" in evaluation_data and "overall_score" in evaluation_data["evaluation_summary"]:
                    score = evaluation_data["evaluation_summary"]["overall_score"]

                # Log the extracted score
                logger.info(f"Extracted score: {score} from evaluation data")

                # Create a simplified evaluation object
                simplified_evaluation = {
                    "id": evaluation_data["id"],
                    "decision": decision,
                    "overall_score": score,
                    "overallScore": score
                }

                logger.info(f"Found resume evaluation for application {application_id}")
                return simplified_evaluation

            # If we didn't find an evaluation, check if there's one in the application document
            logger.info(f"Checking for evaluation in applications/{application_id}")
            application_ref = self.db.collection("applications").document(application_id)
            application_doc = application_ref.get()

            if application_doc.exists:
                application_data = application_doc.to_dict()
                logger.info(f"Application document exists. Keys: {list(application_data.keys())}")

                # Check if the application has an evaluation field
                if "evaluation" in application_data and isinstance(application_data["evaluation"], dict):
                    evaluation_data = application_data["evaluation"]
                    logger.debug(f"Found evaluation data in application with ID: {evaluation_data.get('id', 'unknown')}")

                    # Extract decision and score
                    decision = evaluation_data.get("decision")
                    score = evaluation_data.get("overall_score") or evaluation_data.get("overallScore")

                    # Create a simplified evaluation object
                    simplified_evaluation = {
                        "id": evaluation_data.get("id", "unknown"),
                        "decision": decision,
                        "overall_score": score,
                        "overallScore": score
                    }

                    logger.info(f"Created simplified evaluation with decision={decision}, score={score}")
                    return simplified_evaluation

                # Check if there's an evaluations subcollection
                try:
                    logger.info(f"Checking for evaluations subcollection in applications/{application_id}")
                    evaluations_ref = application_ref.collection("evaluations")
                    evaluations = list(evaluations_ref.limit(1).stream())

                    if evaluations:
                        evaluation_doc = evaluations[0]
                        evaluation_data = evaluation_doc.to_dict()
                        evaluation_data["id"] = evaluation_doc.id

                        # Only log the ID, not the entire evaluation data
                        logger.debug(f"Found evaluation in subcollection with ID: {evaluation_data['id']}")

                        # Extract decision and score
                        decision = None
                        score = None

                        # Try to get decision from various locations
                        if "decision" in evaluation_data and evaluation_data["decision"]:
                            decision = evaluation_data["decision"]
                        elif "evaluation_data" in evaluation_data:
                            eval_data = evaluation_data["evaluation_data"]
                            if "decision" in eval_data and eval_data["decision"]:
                                decision = eval_data["decision"]
                            elif "full_evaluation" in eval_data and "decision" in eval_data["full_evaluation"]:
                                decision = eval_data["full_evaluation"]["decision"]
                        elif "evaluation_summary" in evaluation_data and "decision" in evaluation_data["evaluation_summary"]:
                            decision = evaluation_data["evaluation_summary"]["decision"]

                        # Log the extracted decision
                        logger.info(f"Extracted decision: {decision} from evaluation data")

                        # Try to get score from various locations
                        if "overall_score" in evaluation_data and evaluation_data["overall_score"]:
                            score = evaluation_data["overall_score"]
                        elif "overallScore" in evaluation_data and evaluation_data["overallScore"]:
                            score = evaluation_data["overallScore"]
                        elif "evaluation_data" in evaluation_data:
                            eval_data = evaluation_data["evaluation_data"]
                            if "overall_score" in eval_data and eval_data["overall_score"]:
                                score = eval_data["overall_score"]
                            elif "overallScore" in eval_data and eval_data["overallScore"]:
                                score = eval_data["overallScore"]
                            elif "full_evaluation" in eval_data:
                                full_eval = eval_data["full_evaluation"]
                                if "overall_score" in full_eval and full_eval["overall_score"]:
                                    score = full_eval["overall_score"]
                                elif "overallScore" in full_eval and full_eval["overallScore"]:
                                    score = full_eval["overallScore"]
                        elif "evaluation_summary" in evaluation_data and "overall_score" in evaluation_data["evaluation_summary"]:
                            score = evaluation_data["evaluation_summary"]["overall_score"]

                        # Log the extracted score
                        logger.info(f"Extracted score: {score} from evaluation data")

                        # Create a simplified evaluation object
                        simplified_evaluation = {
                            "id": evaluation_data["id"],
                            "decision": decision,
                            "overall_score": score,
                            "overallScore": score
                        }

                        logger.info(f"Created simplified evaluation from subcollection with decision={decision}, score={score}")
                        return simplified_evaluation
                except Exception as e:
                    logger.error(f"Error checking evaluations subcollection: {str(e)}")
                    # Continue with other checks

            logger.info(f"No resume evaluation found for application {application_id}")
            return None

        except Exception as e:
            logger.error(f"Error fetching resume evaluation for application {application_id}: {str(e)}")
            return None

    async def get_evaluations_for_user(self, user_id: str, role_id: Optional[str] = None, limit: int = None) -> List[Dict[str, Any]]:
        """
        Get all evaluations for a user, optionally filtered by role.

        Args:
            user_id: The ID of the user
            role_id: Optional role ID to filter evaluations
            limit: Optional limit on the number of evaluations to return

        Returns:
            List of evaluations
        """
        try:
            logger.info(f"Getting evaluations for user {user_id}" + (f" with limit {limit}" if limit else ""))
            evaluations = []

            # If role_id is provided, only get evaluations for that role
            if role_id:
                # Get evaluations from the evaluations collection
                evals_ref = self.db.collection("evaluations").where("roleId", "==", role_id)
                evals_docs = evals_ref.stream()

                for doc in evals_docs:
                    eval_data = doc.to_dict()
                    eval_data["id"] = doc.id
                    evaluations.append(eval_data)

                logging.info(f"Found {len(evaluations)} evaluations for role {role_id}")
                return evaluations

            # Get all roles for this user
            roles = await self.get_roles(user_id)
            role_ids = [role["id"] for role in roles]

            # Query evaluations collection for evaluations matching these role IDs
            for role_id in role_ids:
                # Check evaluations collection
                evals_ref = self.db.collection("evaluations").where("roleId", "==", role_id)
                evals_docs = evals_ref.stream()

                for doc in evals_docs:
                    eval_data = doc.to_dict()
                    eval_data["id"] = doc.id
                    evaluations.append(eval_data)

            # Apply limit if specified
            if limit and len(evaluations) > limit:
                evaluations = evaluations[:limit]
                logger.info(f"Limited to {limit} evaluations out of {len(evaluations)} total")

            logger.info(f"Found {len(evaluations)} evaluations for user {user_id}")
            return evaluations
        except Exception as e:
            logger.error(f"Error getting evaluations for user: {str(e)}")
            return []