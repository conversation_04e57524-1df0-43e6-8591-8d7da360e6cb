# File: backend/app/services/ai_service.py

from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import json
import logging
import os
import yaml
import openai
import re
from string import Template

from .openai.models.chat import ChatClient
from .openai.processors.function_registry import FunctionRegistry as ProcessorFunctionRegistry
from ..core.config import settings
from .roles_service import RolesService
from .openai.prompts.manager import PromptManager
from .openai.function_registry import FunctionRegistry
from .openai.chat_completion_service import ChatCompletionService
from .firebase_service import FirebaseService
from app.services.openai.config.model_configs import ModelConfigurationManager

# Default system prompt as fallback
DEFAULT_SYSTEM_PROMPT = """
You are <PERSON><PERSON><PERSON><PERSON>, an AI assistant for Recruiva, a recruitment platform. Your role is to help users with their recruitment needs.
You can create roles, update roles, and provide information about existing roles.
Always be professional, helpful, and concise in your responses.
"""

# Custom JSON encoder to handle datetime objects
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (datetime, timedelta)):
            return obj.isoformat()
        return super().default(obj)

def _serialize_firestore_data(obj: Any) -> Any:
    """Helper function to serialize Firestore data types."""
    # Handle any object that has an isoformat method (datetime-like objects)
    if hasattr(obj, 'isoformat'):
        return obj.isoformat()
    if isinstance(obj, dict):
        return {k: _serialize_firestore_data(v) for k, v in obj.items()}
    if isinstance(obj, list):
        return [_serialize_firestore_data(item) for item in obj]
    return obj

class AIService:
    _instance = None
    _initialized = False
    _system_prompt = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AIService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the AI service."""
        # Initialize function registry
        self.function_registry = ProcessorFunctionRegistry()

        # Initialize RolesService
        self.roles_service = RolesService()

        # Register functions
        self._register_functions()

        # Explicitly register handlers
        self.function_registry.register_handler("create_role", self._handle_role_creation)
        self.function_registry.register_handler("get_roles", self._handle_get_roles)
        self.function_registry.register_handler("get_role_details", self._handle_get_role_details)
        self.function_registry.register_handler("update_role", self._handle_role_update)

    def _load_system_prompt(self, prompt_name: str):
        """Load the system prompt from a .prompt file."""
        try:
            # Use PromptManager to load the prompt
            prompt_content = PromptManager.load_prompt(prompt_name)
            logging.info(f"Successfully loaded {prompt_name} prompt ({len(prompt_content)} characters)")
            return prompt_content
        except Exception as e:
            logging.error(f"Error loading system prompt: {str(e)}", exc_info=True)
            return DEFAULT_SYSTEM_PROMPT

    def _register_functions(self):
        """Register available functions for the AI to use."""
        self.function_registry.register(
            name="create_role",
            description="Create a new role based on email content",
            parameters={
                "type": "object",
                "properties": {
                    "title": {
                        "type": "string",
                        "description": "The title of the role"
                    },
                    "summary": {
                        "type": "string",
                        "description": "A brief summary of the role"
                    },
                    "level": {
                        "type": "string",
                        "description": "The level of the role (e.g., Entry Level, Junior, Mid-Level, Senior, Lead, Principal, Distinguished)"
                    },
                    "keyResponsibilities": {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "description": "Key responsibilities for the role"
                    },
                    "requiredSkills": {
                        "type": "object",
                        "description": "Required skills for the role"
                    },
                    "preferredSkills": {
                        "type": "object",
                        "description": "Preferred skills for the role"
                    },
                    "yearsOfExperience": {
                        "type": "string",
                        "description": "Minimum years of experience required"
                    },
                    "team": {
                        "type": "string",
                        "description": "The team the role belongs to"
                    },
                    "location": {
                        "type": "object",
                        "properties": {
                            "city": {
                                "type": "string",
                                "description": "The city where the role is located"
                            },
                            "remoteStatus": {
                                "type": "string",
                                "description": "Whether the role is remote, hybrid, or on-site"
                            }
                        },
                        "description": "Location information for the role"
                    },
                    "keyStakeholders": {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "description": "Key stakeholders for the role"
                    },
                    "jobType": {
                        "type": "string",
                        "description": "The type of job (e.g., Full-Time, Part-Time, Contract)"
                    },
                    "aboutTeam": {
                        "type": "string",
                        "description": "Description of the team"
                    },
                    "aboutCompany": {
                        "type": "string",
                        "description": "Brief description of the company"
                    },
                    "hiringManagerContact": {
                        "type": "string",
                        "description": "Contact information for the hiring manager"
                    },
                    "interviewProcess": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "stage": {
                                    "type": "string",
                                    "description": "The name of the interview stage"
                                },
                                "duration": {
                                    "type": "string",
                                    "description": "The duration of the interview stage"
                                },
                                "customInstructions": {
                                    "type": "string",
                                    "description": "Custom instructions for the interview stage"
                                }
                            }
                        },
                        "description": "The interview process for the role"
                    },
                    "user_id": {
                        "type": "string",
                        "description": "The ID of the user creating the role"
                    }
                },
                "required": ["title", "summary", "user_id"]
            }
        )

        self.function_registry.register(
            name="get_roles",
            description="Get a list of roles for the user",
            parameters={
                "type": "object",
                "properties": {
                    "user_id": {
                        "type": "string",
                        "description": "The ID of the user to get roles for"
                    },
                    "filters": {
                        "type": "object",
                        "properties": {
                            "status": {
                                "type": "string",
                                "description": "Filter by role status"
                            },
                            "team": {
                                "type": "string",
                                "description": "Filter by team name"
                            },
                            "dateRange": {
                                "type": "object",
                                "properties": {
                                    "start": {
                                        "type": "string",
                                        "description": "Start date for filtering (ISO format)"
                                    },
                                    "end": {
                                        "type": "string",
                                        "description": "End date for filtering (ISO format)"
                                    }
                                },
                                "description": "Date range for filtering roles"
                            },
                            "title": {
                                "type": "string",
                                "description": "Filter by role title (partial match)"
                            }
                        },
                        "description": "Filters to apply when getting roles"
                    }
                },
                "required": ["user_id"]
            }
        )

        self.function_registry.register(
            name="get_role_details",
            description="Get details for a specific role",
            parameters={
                "type": "object",
                "properties": {
                    "role_id": {
                        "type": "string",
                        "description": "The ID of the role to get details for"
                    },
                    "user_id": {
                        "type": "string",
                        "description": "The ID of the user who owns the role"
                    }
                },
                "required": ["role_id", "user_id"]
            }
        )

        self.function_registry.register(
            name="update_role",
            description="Update an existing role",
            parameters={
                "type": "object",
                "properties": {
                    "role_id": {
                        "type": "string",
                        "description": "The ID of the role to update"
                    },
                    "user_id": {
                        "type": "string",
                        "description": "The ID of the user who owns the role"
                    },
                    "updates": {
                        "type": "object",
                        "description": "The updates to apply to the role"
                    }
                },
                "required": ["role_id", "user_id", "updates"]
            }
        )

    async def _handle_role_creation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle role creation function call."""
        try:
            # Extract role data from parameters
            role_data = {k: v for k, v in params.items() if k not in ['metadata', 'user_id']}

            # Extract user_id and metadata
            user_id = params.get('user_id')
            metadata = params.get('metadata', {})

            if not user_id:
                logging.error("Missing required parameter: user_id")
                return {
                    'status': 'error',
                    'error': 'Missing required parameter: user_id'
                }

            # Add user_id to role_data
            role_data['user_id'] = user_id

            # Log the role data for debugging
            logging.info(f"Creating role for user {user_id}")
            try:
                logging.debug(f"Role data: {json.dumps(role_data, cls=DateTimeEncoder)}")
            except Exception as e:
                logging.warning(f"Could not serialize role data for logging: {str(e)}")
                # Serialize role data manually for logging
                safe_role_data = _serialize_firestore_data(role_data)
                logging.debug(f"Role data (manually serialized): {safe_role_data}")

            # Ensure RolesService is initialized
            if not hasattr(self, 'roles_service') or self.roles_service is None:
                self.roles_service = RolesService()
                logging.info("Initialized RolesService on demand in _handle_role_creation")

            # Create the role using RolesService
            role = await self.roles_service.create_role(role_data)

            # Get the role ID
            role_id = role.get('id')
            if not role_id:
                raise ValueError("Failed to get role ID from created role")

            # Create base response with role details
            base_url = "https://www.recruiva.ai"
            role_url = f"{base_url}/roles/{role_id}"

            # Create success response with just the necessary data
            response = {
                'status': 'success',
                'role': role,
                'role_id': role_id,
                'role_url': role_url,
                'message': f'Role created successfully with ID: {role_id}'
            }

            logging.info(f"Role created successfully with ID: {role_id}")
            return response

        except Exception as e:
            logging.error(f"Error creating role: {str(e)}", exc_info=True)
            return {
                'status': 'error',
                'error': str(e),
                'message': f'Error creating role: {str(e)}'
            }

    async def _handle_get_roles(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle retrieving roles."""
        try:
            # Extract user_id (required)
            user_id = params.get('user_id')
            if not user_id:
                logging.error("Missing required parameter: user_id")
                return {
                    'status': 'error',
                    'error': 'Missing required parameter: user_id'
                }

            # Extract filters if present
            filters = params.get('filters', {})

            # If no date range is specified, default to last 30 days
            if not filters.get('dateRange'):
                today = datetime.utcnow()
                thirty_days_ago = today - timedelta(days=30)
                filters['dateRange'] = {
                    'start': thirty_days_ago.replace(hour=0, minute=0, second=0, microsecond=0).isoformat(),
                    'end': today.replace(hour=23, minute=59, second=59, microsecond=999999).isoformat()
                }

            logging.info(f"Getting roles for user {user_id} with filters: {filters}")

            # Ensure RolesService is initialized
            if not hasattr(self, 'roles_service') or self.roles_service is None:
                self.roles_service = RolesService()
                logging.info("Initialized RolesService on demand in _handle_get_roles")

            # Get roles with user_id and filters
            roles = await self.roles_service.get_roles(
                user_id=user_id,
                filters=filters
            )

            # Add role URLs to each role
            base_url = "https://www.recruiva.ai"
            for role in roles:
                role['url'] = f"{base_url}/roles/{role['id']}"

            # Serialize the roles data to handle Firestore types
            serialized_roles = _serialize_firestore_data(roles)

            return {
                'status': 'success',
                'roles': serialized_roles,
                'count': len(roles)
            }
        except Exception as e:
            logging.error(f"Error getting roles: {str(e)}", exc_info=True)
            return {
                'status': 'error',
                'error': str(e)
            }

    async def _handle_get_role_details(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle retrieving role details."""
        try:
            role = await self.roles_service.get_role(
                role_id=params['role_id'],
                user_id=params['user_id']
            )
            # Serialize the role data
            serialized_role = _serialize_firestore_data(role)
            return {'status': 'success', 'role': serialized_role}
        except Exception as e:
            logging.error(f"Error getting role details: {str(e)}")
            return {'status': 'error', 'error': str(e)}

    async def _handle_role_update(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Handle role update from intake call analysis."""
        try:
            logging.info(f"Updating role with extracted information: {params['role_id']}")

            user_id = params['user_id']
            role_id = params['role_id']
            updates = params['updates']

            # Log the updates for debugging
            logging.info(f"Role update parameters: user_id={user_id}, role_id={role_id}")
            logging.info(f"Updates preview: {json.dumps(updates, default=str)[:200]}...")

            # Ensure updates is a dictionary
            if not isinstance(updates, dict):
                logging.error(f"Updates is not a dictionary: {type(updates)}")
                return {
                    'status': 'error',
                    'error': f"Updates must be a dictionary, got {type(updates)}"
                }

            # Add metadata to updates
            updates["updatedAt"] = datetime.utcnow().isoformat()
            updates["transcriptProcessed"] = True
            updates["intakeCallCompleted"] = True

            # Update the role
            updated_role = await self.roles_service.update_role(
                role_id=role_id,
                updates=updates,
                user_id=user_id
            )

            # Serialize the updated role data
            serialized_role = _serialize_firestore_data(updated_role)

            logging.info(f"Successfully updated role {role_id} with information from intake call")

            return {
                'status': 'success',
                'role': serialized_role,
                'message': 'Role updated successfully with information from intake call'
            }
        except Exception as e:
            logging.error(f"Error updating role from intake call: {str(e)}", exc_info=True)
            return {'status': 'error', 'error': str(e)}

    async def process_email(
        self,
        user_message: str,
        metadata: Dict[str, Any],
        temperature: float = 0.7,
        max_tokens: int = 2048,
        presence_penalty: float = 0.0,
        frequency_penalty: float = 0.0
    ) -> Dict[str, Any]:
        """
        Process an email message with OpenAI API and execute appropriate functions.

        Args:
            user_message: The email content to process
            metadata: Additional context about the email (sender, recipient, etc.)
            temperature: Controls randomness in the response (0-1)
            max_tokens: Maximum tokens in the response
            presence_penalty: Penalizes repeated tokens
            frequency_penalty: Penalizes frequent tokens

        Returns:
            Dict containing action and response data
        """
        try:
            # Extract user_id from metadata for logging
            user_id = metadata.get('user_id', 'unknown')
            logging.info(f"Processing email for user {user_id}")

            # Use custom encoder for datetime objects
            try:
                logging.debug(f"Email metadata: {json.dumps(metadata, indent=2, cls=DateTimeEncoder)}")
            except Exception as e:
                logging.warning(f"Could not serialize metadata for logging: {str(e)}")
                # Serialize metadata manually for logging
                safe_metadata = _serialize_firestore_data(metadata)
                logging.debug(f"Email metadata (manually serialized): {safe_metadata}")

            # Format the user message with context
            formatted_message = f"""
Email Content:
{user_message}

Context:
"""
            # Add all metadata as context
            for key, value in metadata.items():
                if key == 'previous_messages':
                    continue  # Skip previous messages to avoid token limit issues

                if isinstance(value, dict):
                    formatted_message += f"- {key}:\n"
                    for sub_key, sub_value in value.items():
                        formatted_message += f"  - {sub_key}: {sub_value}\n"
                else:
                    formatted_message += f"- {key}: {value}\n"

            # Ensure RolesService is initialized
            if not hasattr(self, 'roles_service') or self.roles_service is None:
                self.roles_service = RolesService()
                logging.info("Initialized RolesService on demand")

            # Check for available functions
            available_functions = {
                "create_role": self._handle_role_creation,
                "get_roles": self._handle_get_roles,
                "get_role_details": self._handle_get_role_details,
                "update_role": self._handle_role_update
            }

            # Get the appropriate model for email processing from configuration
            email_model = ModelConfigurationManager.get_appropriate_model("email_agent")

            # Prepare functions for the API
            functions = self.function_registry.get_functions()

            # Load the system prompt for email agent
            system_prompt = self._load_system_prompt("email_agent")

            # Create messages array
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": formatted_message}
            ]

            # Log the messages being sent to the model
            logging.info(f"Sending {len(messages)} messages to the model")
            logging.debug(f"System message length: {len(messages[0]['content'])}")
            logging.debug(f"User message length: {len(messages[1]['content'])}")

            # Use the ChatClient directly for more control
            chat_client = ChatClient(use_case="email_agent")
            response = await chat_client.process_with_function_calling(
                messages=messages,
                functions=functions,
                model=email_model,
                function_call="auto",
                temperature=temperature,
                max_tokens=max_tokens,
                presence_penalty=presence_penalty,
                frequency_penalty=frequency_penalty
            )

            # Log response type
            logging.debug(f"Response type: {type(response)}")

            # Extract content from response
            message = response.choices[0].message

            # Check for function calls (handling both old and new API formats)
            function_call = None
            function_name = None
            function_args = {}
            has_function_call = False

            # Log the message for debugging
            logging.debug(f"Message content: {message.content}")
            logging.debug(f"Message attributes: {dir(message)}")

            # Check for tool_calls (new API format)
            if hasattr(message, 'tool_calls') and message.tool_calls:
                logging.info("Found tool_calls in response")
                for tool_call in message.tool_calls:
                    if tool_call.type == 'function':
                        function_call = tool_call.function
                        function_name = function_call.name
                        has_function_call = True
                        try:
                            function_args = json.loads(function_call.arguments)
                        except json.JSONDecodeError:
                            logging.error(f"Invalid JSON in function arguments: {function_call.arguments}")
                            function_args = {}
                        break
            # Check for function_call (old API format)
            elif hasattr(message, 'function_call') and message.function_call:
                logging.info("Found function_call in response")
                function_call = message.function_call
                function_name = function_call.name
                has_function_call = True
                try:
                    function_args = json.loads(function_call.arguments)
                except json.JSONDecodeError:
                    logging.error(f"Invalid JSON in function arguments: {function_call.arguments}")
                    function_args = {}

            # If function call is detected and it's a valid function, execute it
            if has_function_call and function_name and function_name in available_functions:
                logging.info(f"Function call detected: {function_name}")

                # Add user_id to function arguments if not present
                if 'user_id' not in function_args and user_id != 'unknown':
                    function_args['user_id'] = user_id
                    logging.info(f"Added user_id={user_id} to function arguments")

                # Add metadata to function arguments
                function_args['metadata'] = metadata

                # Execute function and get result
                function_to_call = available_functions[function_name]
                logging.info(f"Executing function: {function_name}")
                function_result = await function_to_call(function_args)

                # Return result with action
                return {
                    'action': function_name,
                    'result': function_result,
                    'content': message.content
                }
            else:
                # No function call detected or invalid function, return the content directly
                # This is expected behavior when the model decides to respond directly
                action = metadata.get('action', 'direct_response')
                logging.info(f"No function call detected or direct response requested. Action: {action}")

                return {
                    'action': action,
                    'result': {
                        'status': 'success',
                        'message': 'Processed without function call'
                    },
                    'content': message.content
                }

        except Exception as e:
            logging.error(f"Error processing email: {str(e)}", exc_info=True)
            return {
                'action': 'error',
                'error': str(e),
                'message': f"Error processing email: {str(e)}"
            }

    async def generate_job_posting(self, role, transcript):
        try:
            logging.info("Generating job posting")

            # Log transcript information for debugging
            transcript_content = transcript.get("content", "")
            transcript_length = len(transcript_content)
            transcript_preview = transcript_content[:200] + "..." if transcript_length > 200 else transcript_content

            logging.info(f"Transcript length: {transcript_length} characters")
            logging.info(f"Transcript preview: {transcript_preview}")

            # Create context with all required variables
            context = {
                "title": role["title"],
                "summary": role["summary"],
                "team": role["team"],
                "job_type": role["jobType"],
                "location_city": role["location"]["city"],
                "location_remote_status": role["location"]["remoteStatus"],
                "key_responsibilities": "\n".join(role["keyResponsibilities"]),
                "required_skills": "\n".join(role["requiredSkills"].values()),
                "preferred_skills": "\n".join(role["preferredSkills"].values()),
                "education": role.get("education", {}).get("value", "N/A"),
                "about_company": role.get("aboutCompany", "N/A"),
                "about_team": role.get("aboutTeam", "N/A"),
                "transcript_text": transcript_content,
                "role_id": role.get("id", "")  # Add role_id to the context for link generation
            }

            # Log context keys for debugging
            logging.info(f"Context keys: {', '.join(context.keys())}")

            # Load and format the prompt
            prompt = PromptManager.load_prompt("job_posting", context=context)

            # Log prompt length for debugging
            prompt_length = len(prompt)
            prompt_preview = prompt[:300] + "..." if prompt_length > 300 else prompt
            logging.info(f"Generated prompt length: {prompt_length} characters")
            logging.info(f"Prompt preview: {prompt_preview}")

            # Define the functions in the correct format expected by the OpenAI API
            functions = [
                {
                    "name": "generate_job_posting",
                    "description": "Generate a job posting based on role information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "job_posting": {
                                "type": "string",
                                "description": "The generated job posting in markdown format"
                            }
                        },
                        "required": ["job_posting"]
                    }
                }
            ]

            logging.info(f"Using {len(functions)} functions for job posting generation")

            # Get the appropriate model for job posting generation
            model = ModelConfigurationManager.get_appropriate_model("job_posting")
            logging.info(f"Using model {model} for job posting generation")

            # Call the chat completion service with the properly formatted functions and use_case
            response = await ChatCompletionService.generate_completion(
                prompt=prompt,
                functions=functions,
                model=model,
                use_case="job_posting",
                function_call={"name": "generate_job_posting"}  # Force the model to use this function
            )

            # Log the response type
            logging.info(f"Response type: {type(response).__name__}")

            # Check if the response is a ChatCompletion object
            if hasattr(response, 'choices') and response.choices:
                # Get the first choice
                choice = response.choices[0]

                # Check if the choice has a message
                if hasattr(choice, 'message'):
                    message = choice.message

                    # Check for function_call (older API format)
                    if hasattr(message, 'function_call') and message.function_call:
                        function_call = message.function_call
                        function_name = function_call.name
                        function_args = function_call.arguments

                        logging.info(f"Function called: {function_name}")
                        logging.info(f"Function arguments preview: {function_args[:100]}...")

                        if function_name == "generate_job_posting" and function_args:
                            try:
                                args = json.loads(function_args)
                                job_posting = args.get("job_posting")
                                if job_posting:
                                    job_posting_length = len(job_posting)
                                    job_posting_preview = job_posting[:200] + "..." if job_posting_length > 200 else job_posting
                                    logging.info(f"Generated job posting length: {job_posting_length} characters")
                                    logging.info(f"Generated job posting preview: {job_posting_preview}")
                                    return {"status": "success", "job_posting": job_posting}
                            except json.JSONDecodeError as e:
                                logging.error(f"Error parsing function arguments: {str(e)}")

                    # Check for tool_calls (newer API format)
                    elif hasattr(message, 'tool_calls') and message.tool_calls:
                        for tool_call in message.tool_calls:
                            if tool_call.type == 'function':
                                function_name = tool_call.function.name
                                function_args = tool_call.function.arguments

                                logging.info(f"Tool called: {function_name}")
                                logging.info(f"Tool arguments preview: {function_args[:100]}...")

                                if function_name == "generate_job_posting" and function_args:
                                    try:
                                        args = json.loads(function_args)
                                        job_posting = args.get("job_posting")
                                        if job_posting:
                                            job_posting_length = len(job_posting)
                                            job_posting_preview = job_posting[:200] + "..." if job_posting_length > 200 else job_posting
                                            logging.info(f"Generated job posting length: {job_posting_length} characters")
                                            logging.info(f"Generated job posting preview: {job_posting_preview}")
                                            return {"status": "success", "job_posting": job_posting}
                                    except json.JSONDecodeError as e:
                                        logging.error(f"Error parsing function arguments: {str(e)}")

            logging.error("Failed to generate job posting: Invalid response format")
            return {"status": "error", "message": "Failed to generate job posting"}

        except Exception as e:
            logging.exception(f"Error generating job posting: {str(e)}")
            return {"status": "error", "message": str(e)}

    async def generate_interview_questions(self, role, template):
        """
        Generate interview questions for a specific role and interview stage.

        Args:
            role: The role document containing role details
            template: The interview template document

        Returns:
            A dictionary containing the generated questions or an error message
        """
        try:
            logging.info(f"Generating interview questions for role {role.get('id', 'unknown')} and stage {template.get('stage', 'unknown')}")

            # Extract required skills as a formatted string
            required_skills = "\n".join([f"- {skill}" for skill in role.get("requiredSkills", {}).values()])

            # Extract preferred skills as a formatted string
            preferred_skills = "\n".join([f"- {skill}" for skill in role.get("preferredSkills", {}).values()])

            # Extract key responsibilities as a formatted string
            key_responsibilities = "\n".join([f"- {resp}" for resp in role.get("keyResponsibilities", [])])

            # Create context with all required variables
            context = {
                "title": role.get("title", ""),
                "level": role.get("level", ""),
                "summary": role.get("summary", ""),
                "team": role.get("team", ""),
                "years_of_experience": role.get("yearsOfExperience", ""),
                "required_skills": required_skills,
                "preferred_skills": preferred_skills,
                "key_responsibilities": key_responsibilities,
                "stage": template.get("stage", ""),
                "duration": template.get("duration", ""),
                "custom_instructions": template.get("customInstructions", ""),
                "additional_context": role.get("aboutTeam", "") + "\n" + role.get("aboutCompany", "")
            }

            # Log context keys for debugging
            logging.info(f"Context keys: {', '.join(context.keys())}")

            # Load and format the prompt
            prompt = PromptManager.load_prompt("interview_question_agent", context=context)

            # Log prompt length for debugging
            prompt_length = len(prompt)
            prompt_preview = prompt[:300] + "..." if prompt_length > 300 else prompt
            logging.info(f"Generated prompt length: {prompt_length} characters")
            logging.info(f"Prompt preview: {prompt_preview}")

            # Define the functions in the correct format expected by the OpenAI API
            functions = [
                {
                    "name": "generate_interview_questions",
                    "description": "Generate interview questions based on role and stage information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "questions": {
                                "type": "array",
                                "description": "Array of generated interview questions",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "text": {
                                            "type": "string",
                                            "description": "The question text"
                                        },
                                        "purpose": {
                                            "type": "string",
                                            "description": "What this question aims to evaluate"
                                        },
                                        "idealAnswerCriteria": {
                                            "type": "string",
                                            "description": "What makes a good answer to this question"
                                        }
                                    },
                                    "required": ["text", "purpose", "idealAnswerCriteria"]
                                }
                            }
                        },
                        "required": ["questions"]
                    }
                }
            ]

            logging.info(f"Using {len(functions)} functions for interview question generation")

            # Get the appropriate model for interview question generation
            model = ModelConfigurationManager.get_appropriate_model("interview_question_agent")
            logging.info(f"Using model {model} for interview question generation")

            # Call the chat completion service with the properly formatted functions and use_case
            response = await ChatCompletionService.generate_completion(
                prompt=prompt,
                functions=functions,
                model=model,
                use_case="interview_question_agent",
                function_call={"name": "generate_interview_questions"}  # Force the model to use this function
            )

            # Log the response type
            logging.info(f"Response type: {type(response).__name__}")

            # Process the response
            result = self._process_interview_questions_response(response)

            if result["status"] == "success":
                logging.info(f"Successfully generated {len(result.get('questions', []))} interview questions")
            else:
                logging.error(f"Failed to generate interview questions: {result.get('message', 'Unknown error')}")

            return result

        except Exception as e:
            logging.exception(f"Error generating interview questions: {str(e)}")
            return {"status": "error", "message": str(e)}

    def _process_interview_questions_response(self, response):
        """
        Process the response from the interview question generation API call.

        Args:
            response: The response from the OpenAI API

        Returns:
            A dictionary containing the generated questions or an error message
        """
        try:
            # Check if the response is a ChatCompletion object
            if hasattr(response, 'choices') and response.choices:
                # Get the first choice
                choice = response.choices[0]

                # Check if the choice has a message
                if hasattr(choice, 'message'):
                    message = choice.message

                    # Check for function_call (older API format)
                    if hasattr(message, 'function_call') and message.function_call:
                        function_call = message.function_call
                        function_name = function_call.name
                        function_args = function_call.arguments

                        logging.info(f"Function called: {function_name}")
                        logging.info(f"Function arguments preview: {function_args[:100]}...")

                        if function_name == "generate_interview_questions" and function_args:
                            try:
                                args = json.loads(function_args)
                                questions = args.get("questions", [])
                                if questions:
                                    # Validate the questions format
                                    for i, question in enumerate(questions):
                                        if not isinstance(question, dict):
                                            logging.warning(f"Question {i} is not a dictionary: {question}")
                                            continue

                                        # Handle field name mismatch - ensure both 'text' and 'question' fields exist
                                        if "text" in question and "question" not in question:
                                            question["question"] = question["text"]
                                            logging.info(f"Copied 'text' to 'question' field for question {i}")
                                        elif "question" in question and "text" not in question:
                                            question["text"] = question["question"]
                                            logging.info(f"Copied 'question' to 'text' field for question {i}")
                                        elif "text" not in question and "question" not in question:
                                            logging.warning(f"Question {i} is missing both 'text' and 'question' fields")
                                            question["text"] = "Missing question text"
                                            question["question"] = "Missing question text"

                                        if "purpose" not in question:
                                            logging.warning(f"Question {i} is missing 'purpose' field")
                                            question["purpose"] = "General evaluation"

                                        if "idealAnswerCriteria" not in question:
                                            logging.warning(f"Question {i} is missing 'idealAnswerCriteria' field")
                                            question["idealAnswerCriteria"] = "Clear, concise, and relevant response"

                                    return {"status": "success", "questions": questions}
                            except json.JSONDecodeError as e:
                                logging.error(f"Error parsing function arguments: {str(e)}")

                    # Check for tool_calls (newer API format)
                    elif hasattr(message, 'tool_calls') and message.tool_calls:
                        for tool_call in message.tool_calls:
                            if tool_call.type == 'function':
                                function_name = tool_call.function.name
                                function_args = tool_call.function.arguments

                                logging.info(f"Tool called: {function_name}")
                                logging.info(f"Tool arguments preview: {function_args[:100]}...")

                                if function_name == "generate_interview_questions" and function_args:
                                    try:
                                        args = json.loads(function_args)
                                        questions = args.get("questions", [])
                                        if questions:
                                            # Validate the questions format
                                            for i, question in enumerate(questions):
                                                if not isinstance(question, dict):
                                                    logging.warning(f"Question {i} is not a dictionary: {question}")
                                                    continue

                                                # Handle field name mismatch - ensure both 'text' and 'question' fields exist
                                                if "text" in question and "question" not in question:
                                                    question["question"] = question["text"]
                                                    logging.info(f"Copied 'text' to 'question' field for question {i}")
                                                elif "question" in question and "text" not in question:
                                                    question["text"] = question["question"]
                                                    logging.info(f"Copied 'question' to 'text' field for question {i}")
                                                elif "text" not in question and "question" not in question:
                                                    logging.warning(f"Question {i} is missing both 'text' and 'question' fields")
                                                    question["text"] = "Missing question text"
                                                    question["question"] = "Missing question text"

                                                if "purpose" not in question:
                                                    logging.warning(f"Question {i} is missing 'purpose' field")
                                                    question["purpose"] = "General evaluation"

                                                if "idealAnswerCriteria" not in question:
                                                    logging.warning(f"Question {i} is missing 'idealAnswerCriteria' field")
                                                    question["idealAnswerCriteria"] = "Clear, concise, and relevant response"

                                            return {"status": "success", "questions": questions}
                                    except json.JSONDecodeError as e:
                                        logging.error(f"Error parsing function arguments: {str(e)}")

            logging.error("Failed to generate interview questions: Invalid response format")
            return {"status": "error", "message": "Failed to generate interview questions: Invalid response format"}

        except Exception as e:
            logging.exception(f"Error processing interview questions response: {str(e)}")
            return {"status": "error", "message": f"Error processing response: {str(e)}"}

    async def generate_evaluation_criteria(self, role, template):
        """
        Generate evaluation criteria for a specific role and interview stage.

        Args:
            role: The role document containing role details
            template: The interview template document

        Returns:
            A dictionary containing the generated evaluation criteria or an error message
        """
        try:
            logging.info(f"Generating evaluation criteria for role {role.get('id', 'unknown')} and stage {template.get('stage', 'unknown')}")

            # Extract required skills as a formatted string
            required_skills = "\n".join([f"- {skill}" for skill in role.get("requiredSkills", {}).values()])

            # Extract preferred skills as a formatted string
            preferred_skills = "\n".join([f"- {skill}" for skill in role.get("preferredSkills", {}).values()])

            # Extract key responsibilities as a formatted string
            key_responsibilities = "\n".join([f"- {resp}" for resp in role.get("keyResponsibilities", [])])

            # Format questions as a string
            questions_text = ""
            if template.get("questions"):
                for i, question in enumerate(template.get("questions", []), 1):
                    q_text = question.get("text", question.get("question", ""))
                    if q_text:
                        questions_text += f"{i}. {q_text}\n"
                        if question.get("purpose"):
                            questions_text += f"   Purpose: {question.get('purpose')}\n"
                        if question.get("idealAnswerCriteria"):
                            questions_text += f"   Ideal Answer: {question.get('idealAnswerCriteria')}\n"
                        questions_text += "\n"

            # Create context with all required variables
            context = {
                "title": role.get("title", ""),
                "level": role.get("level", ""),
                "summary": role.get("summary", ""),
                "team": role.get("team", ""),
                "years_of_experience": role.get("yearsOfExperience", ""),
                "required_skills": required_skills,
                "preferred_skills": preferred_skills,
                "key_responsibilities": key_responsibilities,
                "stage": template.get("stage", ""),
                "duration": template.get("duration", ""),
                "custom_instructions": template.get("customInstructions", ""),
                "questions": questions_text,
                "additional_context": role.get("aboutTeam", "") + "\n" + role.get("aboutCompany", "")
            }

            # Log context keys for debugging
            logging.info(f"Context keys: {', '.join(context.keys())}")

            # Load and format the prompt
            prompt = PromptManager.load_prompt("criterion_agent", context=context)

            # Log prompt length for debugging
            prompt_length = len(prompt)
            prompt_preview = prompt[:300] + "..." if prompt_length > 300 else prompt
            logging.info(f"Generated prompt length: {prompt_length} characters")
            logging.info(f"Prompt preview: {prompt_preview}")

            # Define the functions in the correct format expected by the OpenAI API
            functions = [
                {
                    "name": "generate_evaluation_criteria",
                    "description": "Generate evaluation criteria based on role and stage information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "scoreCardCriteria": {
                                "type": "array",
                                "description": "Array of ScoreCard criteria with competency, weight, and description",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "type": {
                                            "type": "string",
                                            "enum": ["ScoreCard"],
                                            "description": "Type of criterion"
                                        },
                                        "competency": {
                                            "type": "string",
                                            "description": "Name of the competency being evaluated"
                                        },
                                        "weight": {
                                            "type": "number",
                                            "description": "Weight of this competency (0.0-1.0, must sum to 1.0 across all ScoreCard criteria)"
                                        },
                                        "criteria": {
                                            "type": "string",
                                            "description": "Evaluation criteria description"
                                        },
                                        "description": {
                                            "type": "string",
                                            "description": "Additional guidance for evaluators"
                                        }
                                    },
                                    "required": ["type", "competency", "weight", "criteria"]
                                }
                            },
                            "betweenTheLinesCriteria": {
                                "type": "array",
                                "description": "Array of Between the Lines criteria for qualitative observations",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "type": {
                                            "type": "string",
                                            "enum": ["BetweenTheLines"],
                                            "description": "Type of criterion"
                                        },
                                        "criteria": {
                                            "type": "string",
                                            "description": "Observation criteria"
                                        },
                                        "description": {
                                            "type": "string",
                                            "description": "What to look for"
                                        }
                                    },
                                    "required": ["type", "criteria"]
                                }
                            },
                            "disqualifierCriteria": {
                                "type": "array",
                                "description": "Array of Disqualifier criteria that would immediately disqualify a candidate",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "type": {
                                            "type": "string",
                                            "enum": ["Disqualifier"],
                                            "description": "Type of criterion"
                                        },
                                        "criteria": {
                                            "type": "string",
                                            "description": "Disqualification condition"
                                        },
                                        "description": {
                                            "type": "string",
                                            "description": "Why this is a disqualifier"
                                        }
                                    },
                                    "required": ["type", "criteria"]
                                }
                            },
                            "passRate": {
                                "type": "number",
                                "description": "Minimum score required to pass this stage (0.0-1.0)"
                            }
                        },
                        "required": ["scoreCardCriteria", "betweenTheLinesCriteria", "disqualifierCriteria", "passRate"]
                    }
                }
            ]

            logging.info(f"Using {len(functions)} functions for evaluation criteria generation")

            # Get the appropriate model for evaluation criteria generation
            model = ModelConfigurationManager.get_appropriate_model("criterion_agent")
            logging.info(f"Using model {model} for evaluation criteria generation")

            # Call the chat completion service with the properly formatted functions and use_case
            response = await ChatCompletionService.generate_completion(
                prompt=prompt,
                functions=functions,
                model=model,
                use_case="criterion_agent",
                function_call={"name": "generate_evaluation_criteria"}  # Force the model to use this function
            )

            # Log the response type
            logging.info(f"Response type: {type(response).__name__}")

            # Process the response
            result = self._process_evaluation_criteria_response(response)

            if result["status"] == "success":
                logging.info(f"Successfully generated evaluation criteria")
                logging.info(f"Generated {len(result.get('scoreCardCriteria', []))} ScoreCard criteria")
                logging.info(f"Generated {len(result.get('betweenTheLinesCriteria', []))} Between the Lines criteria")
                logging.info(f"Generated {len(result.get('disqualifierCriteria', []))} Disqualifier criteria")
                logging.info(f"Pass rate: {result.get('passRate', 0.0)}")
            else:
                logging.error(f"Failed to generate evaluation criteria: {result.get('message', 'Unknown error')}")

            return result

        except Exception as e:
            logging.exception(f"Error generating evaluation criteria: {str(e)}")
            return {"status": "error", "message": str(e)}

    def _process_evaluation_criteria_response(self, response):
        """
        Process the response from the evaluation criteria generation API call.

        Args:
            response: The response from the OpenAI API

        Returns:
            A dictionary containing the generated evaluation criteria or an error message
        """
        try:
            # Check if the response is a ChatCompletion object
            if hasattr(response, 'choices') and response.choices:
                # Get the first choice
                choice = response.choices[0]

                # Check if the choice has a message
                if hasattr(choice, 'message'):
                    message = choice.message

                    # Check for function_call (older API format)
                    if hasattr(message, 'function_call') and message.function_call:
                        function_call = message.function_call
                        function_name = function_call.name
                        function_args = function_call.arguments

                        logging.info(f"Function called: {function_name}")
                        logging.info(f"Function arguments preview: {function_args[:100]}...")

                        if function_name == "generate_evaluation_criteria" and function_args:
                            try:
                                args = json.loads(function_args)

                                # Extract the criteria
                                score_card_criteria = args.get("scoreCardCriteria", [])
                                between_the_lines_criteria = args.get("betweenTheLinesCriteria", [])
                                disqualifier_criteria = args.get("disqualifierCriteria", [])
                                pass_rate = args.get("passRate", 0.8)  # Default to 80% if not provided

                                # Validate ScoreCard criteria
                                if score_card_criteria:
                                    # Ensure all ScoreCard criteria have the required fields
                                    for i, criterion in enumerate(score_card_criteria):
                                        if not isinstance(criterion, dict):
                                            logging.warning(f"ScoreCard criterion {i} is not a dictionary: {criterion}")
                                            continue

                                        # Ensure type is set to ScoreCard
                                        criterion["type"] = "ScoreCard"

                                        # Ensure required fields exist
                                        if "competency" not in criterion:
                                            logging.warning(f"ScoreCard criterion {i} is missing 'competency' field")
                                            criterion["competency"] = f"Competency {i+1}"

                                        if "weight" not in criterion:
                                            logging.warning(f"ScoreCard criterion {i} is missing 'weight' field")
                                            criterion["weight"] = 1.0 / len(score_card_criteria)  # Equal weight

                                        if "criteria" not in criterion:
                                            logging.warning(f"ScoreCard criterion {i} is missing 'criteria' field")
                                            criterion["criteria"] = "Evaluation criteria not specified"

                                        if "description" not in criterion:
                                            criterion["description"] = "Additional guidance not provided"

                                    # Normalize weights to ensure they sum to 1.0
                                    total_weight = sum(criterion.get("weight", 0) for criterion in score_card_criteria)
                                    if abs(total_weight - 1.0) > 0.01:  # Allow for small floating-point errors
                                        logging.warning(f"ScoreCard weights sum to {total_weight}, normalizing to 1.0")
                                        for criterion in score_card_criteria:
                                            criterion["weight"] = criterion.get("weight", 0) / total_weight

                                # Validate Between the Lines criteria
                                if between_the_lines_criteria:
                                    for i, criterion in enumerate(between_the_lines_criteria):
                                        if not isinstance(criterion, dict):
                                            logging.warning(f"Between the Lines criterion {i} is not a dictionary: {criterion}")
                                            continue

                                        # Ensure type is set to BetweenTheLines
                                        criterion["type"] = "BetweenTheLines"

                                        # Ensure required fields exist
                                        if "criteria" not in criterion:
                                            logging.warning(f"Between the Lines criterion {i} is missing 'criteria' field")
                                            criterion["criteria"] = "Observation criteria not specified"

                                        if "description" not in criterion:
                                            criterion["description"] = "What to look for not provided"

                                # Validate Disqualifier criteria
                                if disqualifier_criteria:
                                    for i, criterion in enumerate(disqualifier_criteria):
                                        if not isinstance(criterion, dict):
                                            logging.warning(f"Disqualifier criterion {i} is not a dictionary: {criterion}")
                                            continue

                                        # Ensure type is set to Disqualifier
                                        criterion["type"] = "Disqualifier"

                                        # Ensure required fields exist
                                        if "criteria" not in criterion:
                                            logging.warning(f"Disqualifier criterion {i} is missing 'criteria' field")
                                            criterion["criteria"] = "Disqualification condition not specified"

                                        if "description" not in criterion:
                                            criterion["description"] = "Why this is a disqualifier not provided"

                                # Validate pass rate
                                if not isinstance(pass_rate, (int, float)):
                                    logging.warning(f"Pass rate is not a number: {pass_rate}, using default 0.8")
                                    pass_rate = 0.8
                                elif pass_rate < 0 or pass_rate > 1:
                                    logging.warning(f"Pass rate is out of range [0, 1]: {pass_rate}, clamping")
                                    pass_rate = max(0, min(1, pass_rate))

                                return {
                                    "status": "success",
                                    "scoreCardCriteria": score_card_criteria,
                                    "betweenTheLinesCriteria": between_the_lines_criteria,
                                    "disqualifierCriteria": disqualifier_criteria,
                                    "passRate": pass_rate
                                }
                            except json.JSONDecodeError as e:
                                logging.error(f"Error parsing function arguments: {str(e)}")

                    # Check for tool_calls (newer API format)
                    elif hasattr(message, 'tool_calls') and message.tool_calls:
                        for tool_call in message.tool_calls:
                            if tool_call.type == 'function':
                                function_name = tool_call.function.name
                                function_args = tool_call.function.arguments

                                logging.info(f"Tool called: {function_name}")
                                logging.info(f"Tool arguments preview: {function_args[:100]}...")

                                if function_name == "generate_evaluation_criteria" and function_args:
                                    try:
                                        args = json.loads(function_args)

                                        # Extract the criteria
                                        score_card_criteria = args.get("scoreCardCriteria", [])
                                        between_the_lines_criteria = args.get("betweenTheLinesCriteria", [])
                                        disqualifier_criteria = args.get("disqualifierCriteria", [])
                                        pass_rate = args.get("passRate", 0.8)  # Default to 80% if not provided

                                        # Validate ScoreCard criteria
                                        if score_card_criteria:
                                            # Ensure all ScoreCard criteria have the required fields
                                            for i, criterion in enumerate(score_card_criteria):
                                                if not isinstance(criterion, dict):
                                                    logging.warning(f"ScoreCard criterion {i} is not a dictionary: {criterion}")
                                                    continue

                                                # Ensure type is set to ScoreCard
                                                criterion["type"] = "ScoreCard"

                                                # Ensure required fields exist
                                                if "competency" not in criterion:
                                                    logging.warning(f"ScoreCard criterion {i} is missing 'competency' field")
                                                    criterion["competency"] = f"Competency {i+1}"

                                                if "weight" not in criterion:
                                                    logging.warning(f"ScoreCard criterion {i} is missing 'weight' field")
                                                    criterion["weight"] = 1.0 / len(score_card_criteria)  # Equal weight

                                                if "criteria" not in criterion:
                                                    logging.warning(f"ScoreCard criterion {i} is missing 'criteria' field")
                                                    criterion["criteria"] = "Evaluation criteria not specified"

                                                if "description" not in criterion:
                                                    criterion["description"] = "Additional guidance not provided"

                                            # Normalize weights to ensure they sum to 1.0
                                            total_weight = sum(criterion.get("weight", 0) for criterion in score_card_criteria)
                                            if abs(total_weight - 1.0) > 0.01:  # Allow for small floating-point errors
                                                logging.warning(f"ScoreCard weights sum to {total_weight}, normalizing to 1.0")
                                                for criterion in score_card_criteria:
                                                    criterion["weight"] = criterion.get("weight", 0) / total_weight

                                        # Validate Between the Lines criteria
                                        if between_the_lines_criteria:
                                            for i, criterion in enumerate(between_the_lines_criteria):
                                                if not isinstance(criterion, dict):
                                                    logging.warning(f"Between the Lines criterion {i} is not a dictionary: {criterion}")
                                                    continue

                                                # Ensure type is set to BetweenTheLines
                                                criterion["type"] = "BetweenTheLines"

                                                # Ensure required fields exist
                                                if "criteria" not in criterion:
                                                    logging.warning(f"Between the Lines criterion {i} is missing 'criteria' field")
                                                    criterion["criteria"] = "Observation criteria not specified"

                                                if "description" not in criterion:
                                                    criterion["description"] = "What to look for not provided"

                                        # Validate Disqualifier criteria
                                        if disqualifier_criteria:
                                            for i, criterion in enumerate(disqualifier_criteria):
                                                if not isinstance(criterion, dict):
                                                    logging.warning(f"Disqualifier criterion {i} is not a dictionary: {criterion}")
                                                    continue

                                                # Ensure type is set to Disqualifier
                                                criterion["type"] = "Disqualifier"

                                                # Ensure required fields exist
                                                if "criteria" not in criterion:
                                                    logging.warning(f"Disqualifier criterion {i} is missing 'criteria' field")
                                                    criterion["criteria"] = "Disqualification condition not specified"

                                                if "description" not in criterion:
                                                    criterion["description"] = "Why this is a disqualifier not provided"

                                        # Validate pass rate
                                        if not isinstance(pass_rate, (int, float)):
                                            logging.warning(f"Pass rate is not a number: {pass_rate}, using default 0.8")
                                            pass_rate = 0.8
                                        elif pass_rate < 0 or pass_rate > 1:
                                            logging.warning(f"Pass rate is out of range [0, 1]: {pass_rate}, clamping")
                                            pass_rate = max(0, min(1, pass_rate))

                                        return {
                                            "status": "success",
                                            "scoreCardCriteria": score_card_criteria,
                                            "betweenTheLinesCriteria": between_the_lines_criteria,
                                            "disqualifierCriteria": disqualifier_criteria,
                                            "passRate": pass_rate
                                        }
                                    except json.JSONDecodeError as e:
                                        logging.error(f"Error parsing function arguments: {str(e)}")

            logging.error("Failed to generate evaluation criteria: Invalid response format")
            return {"status": "error", "message": "Failed to generate evaluation criteria: Invalid response format"}

        except Exception as e:
            logging.exception(f"Error processing evaluation criteria response: {str(e)}")
            return {"status": "error", "message": f"Error processing response: {str(e)}"}

    async def enrich_role(self, role, transcript):
        """
        Analyze a transcript and extract structured role information to enrich the role data.

        Args:
            role: The current role data
            transcript: The transcript to analyze

        Returns:
            Dictionary with the enriched role data or error information
        """
        try:
            logging.info("Enriching role with transcript data")

            # Log transcript information for debugging
            transcript_content = transcript.get("content", "")
            transcript_length = len(transcript_content)
            transcript_preview = transcript_content[:200] + "..." if transcript_length > 200 else transcript_content

            logging.info(f"Transcript length: {transcript_length} characters")
            logging.info(f"Transcript preview: {transcript_preview}")

            # Log if the transcript contains specific sections for required and preferred skills
            if "Required Qualifications" in transcript_content or "Required Skills" in transcript_content:
                logging.info("Transcript contains 'Required Qualifications' or 'Required Skills' section")
            if "Preferred Qualifications" in transcript_content or "Preferred Skills" in transcript_content:
                logging.info("Transcript contains 'Preferred Qualifications' or 'Preferred Skills' section")

            # Create context with all required variables
            context = {
                "title": str(role.get("title", "")) if role.get("title") else "",
                "summary": str(role.get("summary", "")) if role.get("summary") else "",
                "team": str(role.get("team", "")) if role.get("team") else "",
                "job_type": str(role.get("jobType", "Full-time")) if role.get("jobType") else "Full-time",
                "location_city": str(role.get("location", {}).get("city", "")) if role.get("location", {}).get("city") else "",
                "location_remote_status": str(role.get("location", {}).get("remoteStatus", "Remote")) if role.get("location", {}).get("remoteStatus") else "Remote",
                "transcript_text": transcript_content
            }

            # Log context keys for debugging
            logging.info(f"Context keys: {', '.join(context.keys())}")

            # Load and format the prompt
            try:
                prompt = PromptManager.load_prompt("role_enrichment", context=context)
                logging.info(f"Generated prompt with PromptManager, length: {len(prompt)} characters")
                prompt_preview = prompt[:300] + "..." if len(prompt) > 300 else prompt
                logging.info(f"Prompt preview: {prompt_preview}")
            except Exception as e:
                logging.error(f"Error loading prompt with PromptManager: {str(e)}")
                # No fallback - propagate the error
                raise ValueError(f"Failed to load role enrichment prompt: {str(e)}")

            # Define the function for role enrichment with complete schema
            functions = [
                {
                    "name": "update_role",
                    "description": "Update role with enriched data from transcript analysis",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "title": {
                                "type": "string",
                                "description": "The job title"
                            },
                            "level": {
                                "type": "string",
                                "description": "Experience level (Entry Level, Junior, Mid-Level, Senior, Lead, Principal, Distinguished)"
                            },
                            "summary": {
                                "type": "string",
                                "description": "A brief summary of the role"
                            },
                            "jobType": {
                                "type": "string",
                                "description": "Type of job (Full-time, Part-time, Contract, Temporary, Internship)"
                            },
                            "location": {
                                "type": "object",
                                "properties": {
                                    "city": {
                                        "type": "string",
                                        "description": "City where the job is located"
                                    },
                                    "remoteStatus": {
                                        "type": "string",
                                        "description": "Remote status (Remote, Hybrid, On-site)"
                                    }
                                }
                            },
                            "keyResponsibilities": {
                                "type": "array",
                                "items": {
                                    "type": "string"
                                },
                                "description": "List of key responsibilities for the role"
                            },
                            "yearsOfExperience": {
                                "type": "string",
                                "description": "Required years of experience"
                            },
                            "requiredSkills": {
                                "type": "object",
                                "description": "Dictionary of required skills (both technical AND soft skills) with their proficiency levels",
                                "additionalProperties": {
                                    "type": "string",
                                    "description": "Skill proficiency level (Beginner, Intermediate, Expert)"
                                }
                            },
                            "preferredSkills": {
                                "type": "object",
                                "description": "Dictionary of preferred skills (both technical AND soft skills) with their proficiency levels",
                                "additionalProperties": {
                                    "type": "string",
                                    "description": "Skill proficiency level (Beginner, Intermediate, Expert)"
                                }
                            },
                            "education": {
                                "type": "object",
                                "properties": {
                                    "value": {
                                        "type": "string",
                                        "description": "Education requirement description"
                                    },
                                    "isRequired": {
                                        "type": "boolean",
                                        "description": "Whether the education is required"
                                    }
                                }
                            },
                            "certificates": {
                                "type": "array",
                                "items": {
                                    "type": "string"
                                },
                                "description": "List of required or preferred certificates"
                            },
                            "team": {
                                "type": "string",
                                "description": "Team name"
                            },
                            "keyStakeholders": {
                                "type": "array",
                                "items": {
                                    "type": "string"
                                },
                                "description": "List of key stakeholders for the role"
                            },
                            "aboutCompany": {
                                "type": "string",
                                "description": "Information about the company"
                            },
                            "aboutTeam": {
                                "type": "string",
                                "description": "Information about the team"
                            },
                            "compensation": {
                                "type": "object",
                                "properties": {
                                    "range": {
                                        "type": "string",
                                        "description": "Compensation range"
                                    },
                                    "currency": {
                                        "type": "string",
                                        "description": "Currency for compensation (e.g., USD)"
                                    },
                                    "equity": {
                                        "type": "boolean",
                                        "description": "Whether equity is offered"
                                    }
                                }
                            },
                            "benefits": {
                                "type": "object",
                                "properties": {
                                    "healthInsurance": {
                                        "type": "boolean",
                                        "description": "Whether health insurance is offered"
                                    },
                                    "vacationDays": {
                                        "type": "integer",
                                        "description": "Number of vacation days"
                                    },
                                    "dentalInsurance": {
                                        "type": "boolean",
                                        "description": "Whether dental insurance is offered"
                                    },
                                    "visionInsurance": {
                                        "type": "boolean",
                                        "description": "Whether vision insurance is offered"
                                    },
                                    "lifeInsurance": {
                                        "type": "boolean",
                                        "description": "Whether life insurance is offered"
                                    },
                                    "retirement401k": {
                                        "type": "boolean",
                                        "description": "Whether 401k is offered"
                                    },
                                    "stockOptions": {
                                        "type": "boolean",
                                        "description": "Whether stock options are offered"
                                    },
                                    "otherBenefits": {
                                        "type": "array",
                                        "items": {
                                            "type": "string"
                                        },
                                        "description": "List of other benefits"
                                    }
                                }
                            },
                            "startDate": {
                                "type": "string",
                                "description": "Expected start date"
                            },
                            "hiringManagerContact": {
                                "type": "string",
                                "description": "Contact information for the hiring manager"
                            },
                            "interviewProcess": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "stage": {
                                            "type": "string",
                                            "description": "Interview stage name"
                                        },
                                        "duration": {
                                            "type": "string",
                                            "description": "Duration of the interview stage"
                                        },
                                        "customInstructions": {
                                            "type": "string",
                                            "description": "Custom instructions for the interview stage"
                                        }
                                    }
                                },
                                "description": "Interview process stages"
                            }
                        }
                    }
                }
            ]

            logging.info(f"Using {len(functions)} functions for role enrichment")

            # Get the appropriate model for role enrichment
            model = ModelConfigurationManager.get_appropriate_model("role_enrichment")
            logging.info(f"Using model {model} for role enrichment")

            # Call the chat completion service with the properly formatted functions and use_case
            try:
                logging.info("Calling OpenAI API for role enrichment")

                response = await ChatCompletionService.generate_completion(
                    prompt=prompt,
                    functions=functions,
                    model=model,
                    use_case="role_enrichment",
                    function_call={"name": "update_role"},
                    max_tokens=2000
                )

                logging.info("OpenAI API call completed successfully")
                logging.info(f"Response type: {type(response).__name__}")

                # Check if the response is a ChatCompletion object
                if hasattr(response, 'choices') and response.choices:
                    # Get the first choice
                    choice = response.choices[0]

                    # Check if the choice has a message
                    if hasattr(choice, 'message'):
                        message = choice.message

                        # Check for function_call (older API format)
                        if hasattr(message, 'function_call') and message.function_call:
                            function_call = message.function_call
                            function_name = function_call.name
                            function_args = function_call.arguments

                            logging.info(f"Function called: {function_name}")
                            logging.info(f"Function arguments preview: {function_args[:100]}...")

                            if function_name == "update_role" and function_args:
                                try:
                                    # Parse the function arguments
                                    enriched_data = json.loads(function_args)
                                    logging.info(f"Successfully extracted enriched data with {len(enriched_data)} fields")

                                    # Add the transcript to the original role for validation
                                    original_role_with_transcript = dict(role)
                                    original_role_with_transcript["transcript"] = transcript

                                    # Validate and normalize the enriched data
                                    validated_data = self._validate_and_normalize_role_data(enriched_data, original_role_with_transcript)

                                    return validated_data
                                except json.JSONDecodeError as e:
                                    logging.error(f"Error parsing function arguments: {str(e)}")
                                    logging.error(f"Raw function arguments: {function_args}")
                                    return {"status": "error", "message": f"Error parsing enriched data: {str(e)}"}

                        # Check for tool_calls (newer API format)
                        elif hasattr(message, 'tool_calls') and message.tool_calls:
                            for tool_call in message.tool_calls:
                                if tool_call.type == 'function':
                                    function_name = tool_call.function.name
                                    function_args = tool_call.function.arguments

                                    logging.info(f"Tool called: {function_name}")
                                    logging.info(f"Tool arguments preview: {function_args[:100]}...")

                                    if function_name == "update_role" and function_args:
                                        try:
                                            # Parse the function arguments
                                            enriched_data = json.loads(function_args)
                                            logging.info(f"Successfully extracted enriched data with {len(enriched_data)} fields")

                                            # Add the transcript to the original role for validation
                                            original_role_with_transcript = dict(role)
                                            original_role_with_transcript["transcript"] = transcript

                                            # Validate and normalize the enriched data
                                            validated_data = self._validate_and_normalize_role_data(enriched_data, original_role_with_transcript)

                                            return validated_data
                                        except json.JSONDecodeError as e:
                                            logging.error(f"Error parsing tool arguments: {str(e)}")
                                            logging.error(f"Raw tool arguments: {function_args}")
                                            return {"status": "error", "message": f"Error parsing enriched data: {str(e)}"}
                        else:
                            logging.error("Message has no function_call or tool_calls")
                    else:
                        logging.error("Choice has no message attribute")
                else:
                    logging.error("Response has no choices")

                return {"status": "error", "message": "Failed to extract enriched data from OpenAI response"}
            except Exception as api_error:
                logging.error(f"Error calling OpenAI API: {str(api_error)}")
                return {"status": "error", "message": f"Error calling OpenAI API: {str(api_error)}"}
        except Exception as e:
            logging.exception(f"Error enriching role: {str(e)}")
            return {"status": "error", "message": str(e)}

    def _validate_and_normalize_role_data(self, enriched_data: dict, original_role: dict) -> dict:
        """
        Validate and normalize the enriched role data to ensure it conforms to the expected schema.

        Args:
            enriched_data: The enriched data from the AI
            original_role: The original role data

        Returns:
            Validated and normalized role data
        """
        validated_data = {}

        # Log the entire enriched data for debugging
        logging.info(f"Validating and normalizing enriched data with {len(enriched_data)} fields")
        logging.info(f"Fields in enriched data: {', '.join(enriched_data.keys())}")

        # Check if required and preferred skills are present
        if "requiredSkills" in enriched_data:
            logging.info(f"requiredSkills found in enriched data: {json.dumps(enriched_data['requiredSkills'])}")
        else:
            logging.warning("No requiredSkills found in enriched data")

        if "preferredSkills" in enriched_data:
            logging.info(f"preferredSkills found in enriched data: {json.dumps(enriched_data['preferredSkills'])}")
        else:
            logging.warning("No preferredSkills found in enriched data")

        # Validate and normalize basic string fields
        string_fields = ["title", "summary", "team", "aboutCompany", "aboutTeam", "yearsOfExperience", "startDate", "hiringManagerContact"]
        for field in string_fields:
            if field in enriched_data and enriched_data[field]:
                validated_data[field] = str(enriched_data[field])

        # Validate and normalize level field
        if "level" in enriched_data and enriched_data["level"]:
            valid_levels = ["Entry Level", "Junior", "Mid-Level", "Senior", "Lead", "Principal", "Distinguished"]
            level = str(enriched_data["level"])
            if level in valid_levels:
                validated_data["level"] = level
            else:
                logging.warning(f"Invalid level: {level}, using original value")
                if "level" in original_role:
                    validated_data["level"] = original_role["level"]

        # Validate and normalize jobType field
        if "jobType" in enriched_data and enriched_data["jobType"]:
            valid_job_types = ["Full-time", "Part-time", "Contract", "Temporary", "Internship"]
            job_type = str(enriched_data["jobType"])
            if job_type in valid_job_types:
                validated_data["jobType"] = job_type
            else:
                logging.warning(f"Invalid jobType: {job_type}, using original value")
                if "jobType" in original_role:
                    validated_data["jobType"] = original_role["jobType"]

        # Validate and normalize location field
        if "location" in enriched_data and enriched_data["location"]:
            location = enriched_data["location"]
            validated_location = {}

            if "city" in location and location["city"]:
                validated_location["city"] = str(location["city"])

            if "remoteStatus" in location and location["remoteStatus"]:
                valid_remote_statuses = ["Remote", "Hybrid", "On-site"]
                remote_status = str(location["remoteStatus"])
                if remote_status in valid_remote_statuses:
                    validated_location["remoteStatus"] = remote_status
                else:
                    logging.warning(f"Invalid remoteStatus: {remote_status}, using original value")
                    if "location" in original_role and "remoteStatus" in original_role["location"]:
                        validated_location["remoteStatus"] = original_role["location"]["remoteStatus"]

            if validated_location:
                validated_data["location"] = validated_location

        # Validate and normalize array fields
        array_fields = ["keyResponsibilities", "certificates", "keyStakeholders"]
        for field in array_fields:
            if field in enriched_data and enriched_data[field]:
                if isinstance(enriched_data[field], list):
                    validated_data[field] = [str(item) for item in enriched_data[field] if item]
                else:
                    logging.warning(f"Field {field} is not a list, skipping")

        # Validate and normalize education field
        if "education" in enriched_data and enriched_data["education"]:
            education = enriched_data["education"]
            validated_education = {}

            if "value" in education and education["value"]:
                validated_education["value"] = str(education["value"])

            if "isRequired" in education:
                validated_education["isRequired"] = bool(education["isRequired"])

            if validated_education:
                validated_data["education"] = validated_education

        # Validate and normalize compensation field
        if "compensation" in enriched_data and enriched_data["compensation"]:
            compensation = enriched_data["compensation"]
            validated_compensation = {}

            if "range" in compensation and compensation["range"]:
                validated_compensation["range"] = str(compensation["range"])

            if "currency" in compensation and compensation["currency"]:
                validated_compensation["currency"] = str(compensation["currency"])

            if "equity" in compensation:
                validated_compensation["equity"] = bool(compensation["equity"])

            if validated_compensation:
                validated_data["compensation"] = validated_compensation

        # Validate and normalize benefits field
        if "benefits" in enriched_data and enriched_data["benefits"]:
            benefits = enriched_data["benefits"]
            validated_benefits = {}

            boolean_benefit_fields = ["healthInsurance", "dentalInsurance", "visionInsurance", "lifeInsurance", "retirement401k", "stockOptions"]
            for field in boolean_benefit_fields:
                if field in benefits:
                    validated_benefits[field] = bool(benefits[field])

            if "vacationDays" in benefits and benefits["vacationDays"]:
                try:
                    validated_benefits["vacationDays"] = int(benefits["vacationDays"])
                except (ValueError, TypeError):
                    logging.warning(f"Invalid vacationDays: {benefits['vacationDays']}, skipping")

            if "otherBenefits" in benefits and benefits["otherBenefits"]:
                if isinstance(benefits["otherBenefits"], list):
                    validated_benefits["otherBenefits"] = [str(item) for item in benefits["otherBenefits"] if item]
                else:
                    logging.warning("otherBenefits is not a list, skipping")

            if validated_benefits:
                validated_data["benefits"] = validated_benefits

        # Validate and normalize interviewProcess field
        if "interviewProcess" in enriched_data and enriched_data["interviewProcess"]:
            interview_process = enriched_data["interviewProcess"]
            validated_interview_process = []

            if isinstance(interview_process, list):
                valid_stages = ["Screening", "Cultural & Company fit", "Behavioral", "Domain Expert", "Technical Challenge with Code", "Home Assignment", "Experience & Role fit", "Advance Problem Solving", "Team Ethics"]

                for stage in interview_process:
                    validated_stage = {}

                    if "stage" in stage and stage["stage"]:
                        stage_name = str(stage["stage"])
                        if stage_name in valid_stages:
                            validated_stage["stage"] = stage_name
                        else:
                            logging.warning(f"Invalid stage: {stage_name}, using as is")
                            validated_stage["stage"] = stage_name

                    if "duration" in stage and stage["duration"]:
                        validated_stage["duration"] = str(stage["duration"])

                    if "customInstructions" in stage and stage["customInstructions"]:
                        validated_stage["customInstructions"] = str(stage["customInstructions"])

                    if validated_stage:
                        validated_interview_process.append(validated_stage)

                if validated_interview_process:
                    validated_data["interviewProcess"] = validated_interview_process
            else:
                logging.warning("interviewProcess is not a list, skipping")

        # Validate and normalize requiredSkills and preferredSkills
        skill_fields = ["requiredSkills", "preferredSkills"]
        valid_skill_levels = ["Beginner", "Intermediate", "Expert"]

        for field in skill_fields:
            if field in enriched_data:
                normalized_skills = {}

                # Log the raw skills data for debugging
                logging.info(f"Raw {field} data: {json.dumps(enriched_data[field])}")
                logging.info(f"Type of {field} data: {type(enriched_data[field]).__name__}")

                # Check if the field is empty or None
                if not enriched_data[field]:
                    logging.warning(f"{field} is empty or None")
                    continue

                # Handle different formats of skill data
                if isinstance(enriched_data[field], list):
                    # Handle list of skill objects
                    for skill_obj in enriched_data[field]:
                        if isinstance(skill_obj, dict):
                            skill_name = skill_obj.get("name", "").strip()
                            skill_level = skill_obj.get("level", "Intermediate").strip()

                            if skill_name:
                                # Normalize skill level
                                normalized_level = "Intermediate"  # Default
                                for level in valid_skill_levels:
                                    if level.lower() in skill_level.lower():
                                        normalized_level = level
                                        break

                                normalized_skills[skill_name] = normalized_level
                        elif isinstance(skill_obj, str):
                            # Handle plain string skills
                            parts = skill_obj.split("-")
                            if len(parts) > 1:
                                skill_name = parts[0].strip()
                                skill_level = parts[1].strip()

                                if skill_name:
                                    # Normalize skill level
                                    normalized_level = "Intermediate"  # Default
                                    for level in valid_skill_levels:
                                        if level.lower() in skill_level.lower():
                                            normalized_level = level
                                            break

                                    normalized_skills[skill_name] = normalized_level
                            else:
                                # No level specified, use default
                                skill_name = skill_obj.strip()
                                if skill_name:
                                    normalized_skills[skill_name] = "Intermediate"
                elif isinstance(enriched_data[field], dict):
                    # Check if the keys are level names instead of skill names
                    has_level_keys = any(key.lower() in [level.lower() for level in valid_skill_levels] for key in enriched_data[field].keys())

                    if has_level_keys:
                        # Handle case where level names are used as keys
                        logging.warning(f"{field} has level names as keys, attempting to extract skills from values")

                        for level_name, skills in enriched_data[field].items():
                            normalized_level = "Intermediate"  # Default
                            for level in valid_skill_levels:
                                if level.lower() == level_name.lower():
                                    normalized_level = level
                                    break

                            # Handle different formats of skills in values
                            if isinstance(skills, str):
                                # Split by commas or semicolons
                                skill_list = [s.strip() for s in skills.replace(";", ",").split(",")]
                                for skill_name in skill_list:
                                    if skill_name:
                                        normalized_skills[skill_name] = normalized_level
                            elif isinstance(skills, list):
                                for skill_name in skills:
                                    if isinstance(skill_name, str) and skill_name.strip():
                                        normalized_skills[skill_name.strip()] = normalized_level
                    else:
                        # Handle dictionary with skill names as keys and levels as values
                        for skill_name, skill_level in enriched_data[field].items():
                            if skill_name.strip():
                                # Normalize skill level
                                normalized_level = "Intermediate"  # Default
                                if isinstance(skill_level, str):
                                    for level in valid_skill_levels:
                                        if level.lower() in skill_level.lower():
                                            normalized_level = level
                                            break

                                normalized_skills[skill_name.strip()] = normalized_level
                else:
                    logging.warning(f"{field} is not a list or dictionary, skipping")

                # Add the normalized skills to the validated data
                if normalized_skills:
                    validated_data[field] = normalized_skills
                    logging.info(f"Normalized {field}: {json.dumps(normalized_skills)}")
                else:
                    logging.warning(f"No valid skills found in {field}")

        # Check if required skills are missing - this is a critical field
        if "requiredSkills" not in validated_data or not validated_data["requiredSkills"]:
            logging.warning("No requiredSkills found in enriched data and no fallback extraction will be performed")
            # Instead of trying to extract skills, we'll raise an error to ensure proper enrichment
            raise ValueError("Required skills are missing from the enriched data")

        # Check if preferred skills are missing - this is not as critical but still important
        if "preferredSkills" not in validated_data or not validated_data["preferredSkills"]:
            logging.warning("No preferredSkills found in enriched data and no fallback extraction will be performed")

        return validated_data