# File: backend/app/services/evaluation_service.py

"""Service for managing evaluation data in Firestore."""

from typing import Dict, Any, List, Optional, Union
import logging
from datetime import datetime
import uuid

from app.services.firebase_service import FirebaseService
from firebase_admin import firestore
from firebase_admin.firestore import SERVER_TIMESTAMP
from app.core.log_utils import log_data_summary

# Configure logging
logger = logging.getLogger(__name__)

class EvaluationService:
    """Service for managing evaluation data in Firestore."""

    _instance = None

    def __new__(cls):
        """Implement singleton pattern"""
        if cls._instance is None:
            cls._instance = super(EvaluationService, cls).__new__(cls)
            cls._instance.firebase_service = FirebaseService()
        return cls._instance

    async def create_evaluation(
        self,
        role_id: str,
        interview_id: str,
        evaluation_data: dict,
        candidate_id: str = None,
        application_id: str = None,
        user_id: str = None,
        metadata: dict = None,
        tags: List[str] = None
    ) -> str:
        """
        Create a new evaluation document in Firestore.

        Args:
            role_id: Reference to the role
            interview_id: Reference to the interview session
            evaluation_data: The structured evaluation results
            candidate_id: Reference to the candidate (optional)
            application_id: Reference to the application (optional)
            user_id: Reference to the user who triggered the evaluation (null for automated)
            metadata: Additional context information
            tags: List of tags to categorize the evaluation (e.g., "interview", "resume")

        Returns:
            str: The ID of the created evaluation document
        """
        try:
            logger.info(f"Creating evaluation for interview {interview_id} and role {role_id}")

            # Prepare the evaluation document
            evaluation_doc = {
                "role_id": role_id,
                "interview_id": interview_id,
                "evaluation_data": evaluation_data,
                "status": "completed",
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "tags": tags or ["interview"]
            }

            # Add optional fields if provided
            if candidate_id:
                evaluation_doc["candidate_id"] = candidate_id

            if application_id:
                evaluation_doc["application_id"] = application_id

            if user_id:
                evaluation_doc["user_id"] = user_id
                evaluation_doc["is_public"] = False
            else:
                evaluation_doc["is_public"] = True

            if metadata:
                evaluation_doc["metadata"] = metadata

            # Store in the appropriate location based on context
            evaluation_id = None

            if user_id:
                # For authenticated users, store in the user's collection
                eval_ref = self.firebase_service.db.collection("users").document(user_id) \
                            .collection("roles").document(role_id) \
                            .collection("evaluations").document()

                eval_ref.set(evaluation_doc)
                evaluation_id = eval_ref.id

                # Also update the interview document if it exists
                interview_ref = self.firebase_service.db.collection("users").document(user_id) \
                                .collection("roles").document(role_id) \
                                .collection("interviews").document(interview_id)

                interview_doc = interview_ref.get()
                if interview_doc.exists:
                    interview_ref.update({
                        "has_evaluation": True,
                        "evaluation_id": evaluation_id,
                        "evaluation_summary": evaluation_data.get("evaluation_summary", ""),
                        "updated_at": datetime.utcnow()
                    })
            else:
                # For public/automated evaluations, store in the top-level evaluations collection
                eval_ref = self.firebase_service.db.collection("evaluations").document()
                eval_ref.set(evaluation_doc)
                evaluation_id = eval_ref.id

                # Update the public interview session if it exists
                if "interview" in (tags or ["interview"]):
                    session_ref = self.firebase_service.db.collection("public_interview_sessions").document(interview_id)
                    session_doc = session_ref.get()
                    if session_doc.exists:
                        session_ref.update({
                            "has_evaluation": True,
                            "evaluation_id": evaluation_id,
                            "evaluation_summary": evaluation_data.get("evaluation_summary", ""),
                            "updated_at": datetime.utcnow()
                        })

                # Update the application if it exists
                if application_id:
                    app_ref = self.firebase_service.db.collection("applications").document(application_id)
                    app_doc = app_ref.get()
                    if app_doc.exists:
                        app_ref.update({
                            "status": "evaluated",
                            "evaluation_id": evaluation_id,
                            "evaluation_summary": evaluation_data.get("evaluation_summary", ""),
                            "updated_at": datetime.utcnow()
                        })
                        logger.info(f"Updated application {application_id} with evaluation data in applications collection")
                    else:
                        logger.warning(f"Application {application_id} not found in applications collection")

            logger.info(f"Successfully created evaluation with ID: {evaluation_id}")
            return evaluation_id

        except Exception as e:
            logger.error(f"Error creating evaluation: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to create evaluation: {str(e)}")

    async def get_evaluation(self, evaluation_id: str, user_id: str = None) -> Optional[Dict[str, Any]]:
        """
        Retrieve an evaluation by ID.

        Args:
            evaluation_id: The ID of the evaluation to retrieve
            user_id: Optional user ID for authenticated access

        Returns:
            Optional[Dict[str, Any]]: The evaluation document or None if not found
        """
        try:
            logger.info(f"Retrieving evaluation {evaluation_id}")

            evaluation = None

            # First try to get from the top-level evaluations collection (public)
            eval_ref = self.firebase_service.db.collection("evaluations").document(evaluation_id)
            eval_doc = eval_ref.get()

            if eval_doc.exists:
                evaluation = eval_doc.to_dict()
                evaluation["id"] = evaluation_id
                logger.info(f"Found evaluation in public collection with ID: {evaluation_id}")
                return evaluation

            # If not found and user_id is provided, check user's collection
            if user_id:
                # We need to search across all roles since we don't know which role it belongs to
                roles_ref = self.firebase_service.db.collection("users").document(user_id).collection("roles")
                roles = roles_ref.stream()

                for role in roles:
                    role_id = role.id
                    eval_ref = self.firebase_service.db.collection("users").document(user_id) \
                                .collection("roles").document(role_id) \
                                .collection("evaluations").document(evaluation_id)

                    eval_doc = eval_ref.get()
                    if eval_doc.exists:
                        evaluation = eval_doc.to_dict()
                        evaluation["id"] = evaluation_id
                        evaluation["role_id"] = role_id
                        logger.info(f"Found evaluation in user's collection under role {role_id}")
                        return evaluation

            logger.warning(f"Evaluation {evaluation_id} not found")
            return None

        except Exception as e:
            logger.error(f"Error retrieving evaluation: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to retrieve evaluation: {str(e)}")

    async def get_evaluations_by_role(self, role_id: str, user_id: str = None) -> List[Dict[str, Any]]:
        """
        Retrieve all evaluations for a specific role.

        Args:
            role_id: The ID of the role
            user_id: Optional user ID for authenticated access

        Returns:
            List[Dict[str, Any]]: List of evaluation documents
        """
        try:
            logger.info(f"Retrieving evaluations for role {role_id}")

            evaluations = []

            # If user_id is provided, get from user's collection
            if user_id:
                evals_ref = self.firebase_service.db.collection("users").document(user_id) \
                            .collection("roles").document(role_id) \
                            .collection("evaluations")

                evals = evals_ref.stream()
                for eval_doc in evals:
                    evaluation = eval_doc.to_dict()
                    evaluation["id"] = eval_doc.id
                    evaluation["role_id"] = role_id
                    evaluations.append(evaluation)

                logger.info(f"Found {len(evaluations)} evaluations in user's collection")

            # Also get public evaluations for this role
            public_evals_ref = self.firebase_service.db.collection("evaluations").where("role_id", "==", role_id)
            public_evals = public_evals_ref.stream()

            for eval_doc in public_evals:
                evaluation = eval_doc.to_dict()
                evaluation["id"] = eval_doc.id
                evaluations.append(evaluation)

            logger.info(f"Found total of {len(evaluations)} evaluations for role {role_id}")
            return evaluations

        except Exception as e:
            logger.error(f"Error retrieving evaluations by role: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to retrieve evaluations: {str(e)}")

    async def get_evaluations_by_candidate(self, candidate_id: str, user_id: str = None) -> List[Dict[str, Any]]:
        """
        Retrieve all evaluations for a specific candidate.

        Args:
            candidate_id: The ID of the candidate
            user_id: Optional user ID for authenticated access

        Returns:
            List[Dict[str, Any]]: List of evaluation documents
        """
        try:
            logger.info(f"Retrieving evaluations for candidate {candidate_id}")

            evaluations = []

            # Get public evaluations for this candidate
            public_evals_ref = self.firebase_service.db.collection("evaluations").where("candidate_id", "==", candidate_id)
            public_evals = public_evals_ref.stream()

            for eval_doc in public_evals:
                evaluation = eval_doc.to_dict()
                evaluation["id"] = eval_doc.id
                evaluations.append(evaluation)

            # If user_id is provided, search across all roles
            if user_id:
                roles_ref = self.firebase_service.db.collection("users").document(user_id).collection("roles")
                roles = roles_ref.stream()

                for role in roles:
                    role_id = role.id

                    # Get candidates for this role
                    candidates_ref = self.firebase_service.db.collection("users").document(user_id) \
                                    .collection("roles").document(role_id) \
                                    .collection("candidates")

                    # Check if this candidate exists in this role
                    candidate_ref = candidates_ref.document(candidate_id)
                    candidate_doc = candidate_ref.get()

                    if candidate_doc.exists:
                        # Get evaluations for this role
                        evals_ref = self.firebase_service.db.collection("users").document(user_id) \
                                    .collection("roles").document(role_id) \
                                    .collection("evaluations")

                        # Filter evaluations by candidate_id
                        role_evals = evals_ref.where("candidate_id", "==", candidate_id).stream()

                        for eval_doc in role_evals:
                            evaluation = eval_doc.to_dict()
                            evaluation["id"] = eval_doc.id
                            evaluation["role_id"] = role_id
                            evaluations.append(evaluation)

            logger.info(f"Found {len(evaluations)} evaluations for candidate {candidate_id}")
            return evaluations

        except Exception as e:
            logger.error(f"Error retrieving evaluations by candidate: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to retrieve evaluations: {str(e)}")

    async def get_evaluation_by_interview(self, interview_id: str, user_id: str = None) -> Optional[Dict[str, Any]]:
        """
        Retrieve the evaluation for a specific interview.

        Args:
            interview_id: The ID of the interview
            user_id: Optional user ID for authenticated access

        Returns:
            Optional[Dict[str, Any]]: The evaluation document or None if not found
        """
        try:
            logger.info(f"Retrieving evaluation for interview {interview_id}")

            # First check public evaluations
            public_evals_ref = self.firebase_service.db.collection("evaluations").where("interview_id", "==", interview_id).limit(1)
            public_evals = public_evals_ref.stream()

            for eval_doc in public_evals:
                evaluation = eval_doc.to_dict()
                evaluation["id"] = eval_doc.id
                logger.info(f"Found evaluation in public collection")
                return evaluation

            # If user_id is provided, check user's collection
            if user_id:
                # We need to search across all roles since we don't know which role it belongs to
                roles_ref = self.firebase_service.db.collection("users").document(user_id).collection("roles")
                roles = roles_ref.stream()

                for role in roles:
                    role_id = role.id

                    # First check if this interview exists in this role
                    interview_ref = self.firebase_service.db.collection("users").document(user_id) \
                                    .collection("roles").document(role_id) \
                                    .collection("interviews").document(interview_id)

                    interview_doc = interview_ref.get()

                    if interview_doc.exists:
                        # Check if the interview has an evaluation_id
                        interview_data = interview_doc.to_dict()
                        evaluation_id = interview_data.get("evaluation_id")

                        if evaluation_id:
                            # Get the evaluation directly
                            eval_ref = self.firebase_service.db.collection("users").document(user_id) \
                                        .collection("roles").document(role_id) \
                                        .collection("evaluations").document(evaluation_id)

                            eval_doc = eval_ref.get()
                            if eval_doc.exists:
                                evaluation = eval_doc.to_dict()
                                evaluation["id"] = evaluation_id
                                evaluation["role_id"] = role_id
                                logger.info(f"Found evaluation in user's collection using evaluation_id")
                                return evaluation

                        # If no evaluation_id or not found, try querying by interview_id
                        evals_ref = self.firebase_service.db.collection("users").document(user_id) \
                                    .collection("roles").document(role_id) \
                                    .collection("evaluations").where("interview_id", "==", interview_id).limit(1)

                        evals = evals_ref.stream()
                        for eval_doc in evals:
                            evaluation = eval_doc.to_dict()
                            evaluation["id"] = eval_doc.id
                            evaluation["role_id"] = role_id
                            logger.info(f"Found evaluation in user's collection by query")
                            return evaluation

            logger.warning(f"No evaluation found for interview {interview_id}")
            return None

        except Exception as e:
            logger.error(f"Error retrieving evaluation by interview: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to retrieve evaluation: {str(e)}")

    async def update_evaluation_status(self, evaluation_id: str, status: str, user_id: str = None) -> bool:
        """
        Update the status of an evaluation.

        Args:
            evaluation_id: The ID of the evaluation to update
            status: The new status value
            user_id: Optional user ID for authenticated access

        Returns:
            bool: True if the update was successful, False otherwise
        """
        try:
            logger.info(f"Updating status of evaluation {evaluation_id} to {status}")

            # First try to update in the top-level evaluations collection
            eval_ref = self.firebase_service.db.collection("evaluations").document(evaluation_id)
            eval_doc = eval_ref.get()

            if eval_doc.exists:
                eval_ref.update({
                    "status": status,
                    "updated_at": datetime.utcnow()
                })
                logger.info(f"Updated evaluation status in public collection")
                return True

            # If not found and user_id is provided, check user's collection
            if user_id:
                # Get the evaluation first to find which role it belongs to
                evaluation = await self.get_evaluation(evaluation_id, user_id)

                if evaluation and "role_id" in evaluation:
                    role_id = evaluation["role_id"]

                    eval_ref = self.firebase_service.db.collection("users").document(user_id) \
                                .collection("roles").document(role_id) \
                                .collection("evaluations").document(evaluation_id)

                    eval_doc = eval_ref.get()
                    if eval_doc.exists:
                        eval_ref.update({
                            "status": status,
                            "updated_at": datetime.utcnow()
                        })
                        logger.info(f"Updated evaluation status in user's collection")
                        return True

            logger.warning(f"Evaluation {evaluation_id} not found for status update")
            return False

        except Exception as e:
            logger.error(f"Error updating evaluation status: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to update evaluation status: {str(e)}")

    async def get_evaluation_public(self, interview_id: str, application_id: str = None) -> Optional[Dict[str, Any]]:
        """
        Retrieve an evaluation in a public context with limited data.

        Args:
            interview_id: The ID of the interview
            application_id: Optional ID of the application

        Returns:
            Optional[Dict[str, Any]]: The evaluation document with limited fields or None if not found
        """
        try:
            logger.info(f"Retrieving public evaluation for interview {interview_id}")

            # First check in the top-level evaluations collection
            # Query by interview_id
            query = self.firebase_service.db.collection("evaluations").where("interview_id", "==", interview_id)

            # Add application_id filter if provided
            if application_id:
                query = query.where("application_id", "==", application_id)

            evals = query.stream()

            for eval_doc in evals:
                evaluation = eval_doc.to_dict()
                evaluation["id"] = eval_doc.id

                # Extract decision and score
                decision = None
                score = None

                # Try to get decision from various locations
                if "decision" in evaluation:
                    decision = evaluation["decision"]
                elif "evaluation_data" in evaluation and "decision" in evaluation["evaluation_data"]:
                    decision = evaluation["evaluation_data"]["decision"]
                elif "evaluation_summary" in evaluation and "decision" in evaluation["evaluation_summary"]:
                    decision = evaluation["evaluation_summary"]["decision"]

                # Try to get score from various locations
                if "overall_score" in evaluation:
                    score = evaluation["overall_score"]
                elif "overallScore" in evaluation:
                    score = evaluation["overallScore"]
                elif "evaluation_data" in evaluation and "overall_score" in evaluation["evaluation_data"]:
                    score = evaluation["evaluation_data"]["overall_score"]
                elif "evaluation_summary" in evaluation and "overall_score" in evaluation["evaluation_summary"]:
                    score = evaluation["evaluation_summary"]["overall_score"]

                # Filter sensitive data for public access
                public_evaluation = {
                    "id": evaluation["id"],
                    "status": evaluation["status"],
                    "created_at": evaluation["created_at"],
                    "role_id": evaluation["role_id"],
                    "interview_id": evaluation["interview_id"],
                    "evaluation_summary": evaluation.get("evaluation_data", {}).get("evaluation_summary", ""),
                    "recommendation": evaluation.get("evaluation_data", {}).get("recommendation", {}),
                    "overall_score": score if score is not None else evaluation.get("evaluation_data", {}).get("overall_score", 0),
                    "decision": decision,
                    "tags": evaluation.get("tags", ["interview"])
                }

                logger.info(f"Found public evaluation in evaluations collection")
                return public_evaluation

            # If not found in the top-level collection, check in the interviews subcollection
            # First, get the session data to find the role_id and user_id
            session_ref = self.firebase_service.db.collection("public_interview_sessions").document(interview_id)
            session_doc = session_ref.get()

            if session_doc.exists:
                session_data = session_doc.to_dict()
                role_id = session_data.get("role_id")

                if role_id:
                    logger.info(f"Found role_id {role_id} for interview {interview_id}")

                    # Try to find the user who owns this role
                    users_ref = self.firebase_service.db.collection("users")
                    users = users_ref.stream()

                    for user_doc in users:
                        user_id = user_doc.id

                        # Check if this user has this role
                        role_ref = self.firebase_service.db.collection("users").document(user_id).collection("roles").document(role_id)
                        role_doc = role_ref.get()

                        if role_doc.exists:
                            logger.info(f"Found user {user_id} who owns role {role_id}")

                            # Check if this interview exists in the user's interviews collection
                            interviews_ref = self.firebase_service.db.collection("users").document(user_id).collection("roles").document(role_id).collection("interviews")
                            interviews = interviews_ref.stream()

                            # First try to find the interview by ID
                            interview_ref = interviews_ref.document(interview_id)
                            interview_doc = interview_ref.get()

                            if interview_doc.exists:
                                logger.info(f"Found interview {interview_id} in user's collection")
                                interview_data = interview_doc.to_dict()

                                # Check if the interview has an evaluation_id
                                evaluation_id = interview_data.get("evaluation_id")

                                if evaluation_id:
                                    # Get the evaluation from the interviews/{interview_id}/evaluations subcollection
                                    eval_ref = self.firebase_service.db.collection("users").document(user_id).collection("roles").document(role_id).collection("interviews").document(interview_id).collection("evaluations").document(evaluation_id)
                                    eval_doc = eval_ref.get()

                                    if eval_doc.exists:
                                        evaluation = eval_doc.to_dict()
                                        evaluation["id"] = evaluation_id

                                        # Extract decision and score
                                        decision = None
                                        score = None

                                        # Try to get decision from various locations
                                        if "decision" in evaluation:
                                            decision = evaluation["decision"]
                                        elif "evaluation_data" in evaluation and "decision" in evaluation["evaluation_data"]:
                                            decision = evaluation["evaluation_data"]["decision"]
                                        elif "evaluation_summary" in evaluation and "decision" in evaluation["evaluation_summary"]:
                                            decision = evaluation["evaluation_summary"]["decision"]

                                        # Try to get score from various locations
                                        if "overall_score" in evaluation:
                                            score = evaluation["overall_score"]
                                        elif "overallScore" in evaluation:
                                            score = evaluation["overallScore"]
                                        elif "evaluation_data" in evaluation and "overall_score" in evaluation["evaluation_data"]:
                                            score = evaluation["evaluation_data"]["overall_score"]
                                        elif "evaluation_summary" in evaluation and "overall_score" in evaluation["evaluation_summary"]:
                                            score = evaluation["evaluation_summary"]["overall_score"]

                                        # Filter sensitive data for public access
                                        public_evaluation = {
                                            "id": evaluation["id"],
                                            "status": evaluation.get("status", "completed"),
                                            "created_at": evaluation.get("created_at", datetime.utcnow()),
                                            "role_id": role_id,
                                            "interview_id": interview_id,
                                            "evaluation_summary": evaluation.get("evaluation_data", {}).get("evaluation_summary", ""),
                                            "recommendation": evaluation.get("evaluation_data", {}).get("recommendation", {}),
                                            "overall_score": score if score is not None else evaluation.get("evaluation_data", {}).get("overall_score", 0),
                                            "decision": decision,
                                            "tags": evaluation.get("tags", ["interview"])
                                        }

                                        logger.info(f"Found evaluation {evaluation_id} in interviews/{interview_id}/evaluations subcollection")
                                        return public_evaluation

                            # If not found by ID, try to find by session_id or transcript_id
                            for interview_doc in interviews:
                                interview_data = interview_doc.to_dict()

                                # Check if this interview matches our interview_id
                                if (interview_data.get("id") == interview_id or
                                    interview_data.get("session_id") == interview_id or
                                    interview_data.get("transcript_id") == interview_id):

                                    logger.info(f"Found matching interview {interview_doc.id} by session_id or transcript_id")

                                    # Check if the interview has an evaluation_id
                                    evaluation_id = interview_data.get("evaluation_id")

                                    if evaluation_id:
                                        # Get the evaluation from the interviews/{interview_id}/evaluations subcollection
                                        eval_ref = self.firebase_service.db.collection("users").document(user_id).collection("roles").document(role_id).collection("interviews").document(interview_doc.id).collection("evaluations").document(evaluation_id)
                                        eval_doc = eval_ref.get()

                                        if eval_doc.exists:
                                            evaluation = eval_doc.to_dict()
                                            evaluation["id"] = evaluation_id

                                            # Extract decision and score
                                            decision = None
                                            score = None

                                            # Try to get decision from various locations
                                            if "decision" in evaluation:
                                                decision = evaluation["decision"]
                                            elif "evaluation_data" in evaluation and "decision" in evaluation["evaluation_data"]:
                                                decision = evaluation["evaluation_data"]["decision"]
                                            elif "evaluation_summary" in evaluation and "decision" in evaluation["evaluation_summary"]:
                                                decision = evaluation["evaluation_summary"]["decision"]

                                            # Try to get score from various locations
                                            if "overall_score" in evaluation:
                                                score = evaluation["overall_score"]
                                            elif "overallScore" in evaluation:
                                                score = evaluation["overallScore"]
                                            elif "evaluation_data" in evaluation and "overall_score" in evaluation["evaluation_data"]:
                                                score = evaluation["evaluation_data"]["overall_score"]
                                            elif "evaluation_summary" in evaluation and "overall_score" in evaluation["evaluation_summary"]:
                                                score = evaluation["evaluation_summary"]["overall_score"]

                                            # Filter sensitive data for public access
                                            public_evaluation = {
                                                "id": evaluation["id"],
                                                "status": evaluation.get("status", "completed"),
                                                "created_at": evaluation.get("created_at", datetime.utcnow()),
                                                "role_id": role_id,
                                                "interview_id": interview_id,
                                                "evaluation_summary": evaluation.get("evaluation_data", {}).get("evaluation_summary", ""),
                                                "recommendation": evaluation.get("evaluation_data", {}).get("recommendation", {}),
                                                "overall_score": score if score is not None else evaluation.get("evaluation_data", {}).get("overall_score", 0),
                                                "decision": decision,
                                                "tags": evaluation.get("tags", ["interview"])
                                            }

                                            logger.info(f"Found evaluation {evaluation_id} in interviews/{interview_doc.id}/evaluations subcollection")
                                            return public_evaluation

            logger.warning(f"No public evaluation found for interview {interview_id}")
            return None

        except Exception as e:
            logger.error(f"Error retrieving public evaluation: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to retrieve public evaluation: {str(e)}")

    async def create_resume_evaluation(
        self,
        role_id: str,
        evaluation_data: dict,
        resume_text: str,
        candidate_id: str = None,
        application_id: str = None,
        user_id: str = None,
        metadata: dict = None
    ) -> str:
        """
        Create a resume evaluation document in Firestore.

        Args:
            role_id: Reference to the role
            evaluation_data: The structured evaluation results
            resume_text: The parsed resume text
            candidate_id: Reference to the candidate (optional)
            application_id: Reference to the application (optional)
            user_id: Reference to the user who triggered the evaluation (null for automated)
            metadata: Additional context information

        Returns:
            str: The ID of the created evaluation document
        """
        try:
            logger.info(f"Creating resume evaluation for role {role_id}")

            # Generate a unique ID for the interview field (required by schema)
            # This is a placeholder since resume evaluations don't have interviews
            resume_id = f"resume_{uuid.uuid4().hex}"

            # Prepare metadata if not provided
            if not metadata:
                metadata = {}

            # Only add resume_text if it's not already in metadata
            if "parsedText" not in metadata and "resume_text" not in metadata:
                metadata["resume_text"] = resume_text

            metadata["evaluation_type"] = "resume"

            # Create the evaluation with the resume tag
            return await self.create_evaluation(
                role_id=role_id,
                interview_id=resume_id,  # Using the generated ID
                evaluation_data=evaluation_data,
                candidate_id=candidate_id,
                application_id=application_id,
                user_id=user_id,
                metadata=metadata,
                tags=["resume"]  # Tag as resume evaluation
            )

        except Exception as e:
            logger.error(f"Error creating resume evaluation: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to create resume evaluation: {str(e)}")

    async def create_candidate(self, email: str, name: str = None, additional_data: Dict[str, Any] = None) -> str:
        """Create or update a candidate document in Firestore.

        Args:
            email: The candidate's email address (used as document ID)
            name: The candidate's full name (optional)
            additional_data: Additional data to include in the candidate document

        Returns:
            The candidate ID (email)
        """
        try:
            if not email:
                logger.warning("Cannot create candidate without email")
                return None

            logger.info(f"Creating/updating candidate with email: {email}")

            # Prepare candidate data
            candidate_data = {
                "email": email,
                "updated_at": SERVER_TIMESTAMP
            }

            # Check if this is a new document or an update
            db = self.firebase_service.db
            candidate_ref = db.collection("candidates").document(email)
            candidate_doc = candidate_ref.get()

            # If it's a new document, add created_at timestamp
            if not candidate_doc.exists:
                candidate_data["created_at"] = SERVER_TIMESTAMP
                logger.info(f"Creating new candidate document for {email}")
            else:
                logger.info(f"Updating existing candidate document for {email}")

            # Add name if provided
            if name:
                candidate_data["fullName"] = name

            # Add any additional data
            if additional_data:
                candidate_data.update(additional_data)

            # Save to Firestore
            candidate_ref.set(candidate_data, merge=True)
            logger.info(f"Successfully created/updated candidate document with ID: {email}")

            return email
        except Exception as e:
            logger.error(f"Error creating/updating candidate: {str(e)}")
            # Try alternative method
            try:
                logger.info(f"Attempting alternative method to create candidate {email}")
                from firebase_admin import firestore as admin_firestore
                db_admin = admin_firestore.client()
                candidate_ref = db_admin.collection("candidates").document(email)
                candidate_ref.set({
                    "email": email,
                    "fullName": name or "",
                    "created_at": admin_firestore.SERVER_TIMESTAMP,
                    "updated_at": admin_firestore.SERVER_TIMESTAMP
                }, merge=True)
                logger.info(f"Successfully created candidate using alternative method: {email}")
                return email
            except Exception as alt_error:
                logger.error(f"Alternative method also failed: {str(alt_error)}")
                return None
