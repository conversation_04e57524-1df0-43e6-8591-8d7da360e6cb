# File: backend/app/services/parser_service.py

from fastapi import UploadFile
import PyPDF2
import docx
import io

async def parse_job_description(file: UploadFile) -> str:
    """Parse job description from various file formats."""
    content = await file.read()
    
    if file.content_type == 'text/plain':
        return content.decode('utf-8')
    
    elif file.content_type == 'application/pdf':
        # Parse PDF
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
        text = ""
        for page in pdf_reader.pages:
            text += page.extract_text()
        return text
    
    elif file.content_type in ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
        # Parse DOC/DOCX
        doc = docx.Document(io.BytesIO(content))
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text.strip()
    
    else:
        raise ValueError("Unsupported file type") 