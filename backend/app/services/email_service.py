# File: backend/app/services/email_service.py

import imaplib
import email
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import asyncio
import logging
from email.utils import parseaddr
import json
import markdown
import re
from bs4 import BeautifulSoup
from firebase_admin import auth
import backoff
from socket import gaierror, error as socket_error
from google.cloud.firestore_v1.base_query import FieldFilter

from ..core.config import settings
from .firebase_service import FirebaseService
from .ai_service import AIService

# Add new configuration
EMAIL_WORKFLOW_CONFIG = {
    "workspace_email": "<EMAIL>",
    "allowed_domains": ["recruiva.ai"],
    "email_check_interval": 60,  # seconds
    "max_email_age": 24 * 60 * 60  # 24 hours in seconds
}

# NOTE: The email listener service is TEMPORARILY DISABLED.
# The service will not automatically check for new emails in the mailbox.
#
# To re-enable the email service:
# 1. Open backend/app/core/config.py
# 2. Change EMAIL_LISTENER_ENABLED to True
# 3. Restart the application
#
# The send_email method will still work for manually sending emails.

class EmailService:
    _instance = None
    _initialized = False
    _processed_message_ids = set()  # Track processed message IDs
    # Flag to control logging verbosity for email checking
    DEBUG = False  # Set to True to enable verbose logging for debugging

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(EmailService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not EmailService._initialized:
            self.imap_server = "imap.gmail.com"
            self.smtp_server = "smtp.gmail.com"
            self.smtp_port = 587
            self.email = settings.EMAIL_USERNAME
            self.password = settings.EMAIL_PASSWORD
            self.firebase = FirebaseService()
            EmailService._initialized = True

    async def connect_imap(self) -> imaplib.IMAP4_SSL:
        """Connect to IMAP server with retry logic."""
        @backoff.on_exception(
            backoff.expo,
            (gaierror, socket_error, OSError),
            max_tries=5,
            max_time=300
        )
        def _connect_with_retry():
            try:
                imap = imaplib.IMAP4_SSL(self.imap_server)
                imap.login(self.email, self.password)
                return imap
            except (gaierror, socket_error) as e:
                logging.error(f"Network error connecting to IMAP server: {str(e)}")
                raise
            except imaplib.IMAP4.error as e:
                logging.error(f"IMAP server error: {str(e)}")
                raise
            except Exception as e:
                logging.error(f"Unexpected error connecting to IMAP: {str(e)}")
                raise

        try:
            return await asyncio.to_thread(_connect_with_retry)
        except Exception as e:
            logging.error(f"Failed to connect to IMAP server after retries: {str(e)}")
            raise

    async def send_email(
        self,
        subject: str,
        body: str,
        to_email: str,
        cc: Optional[Union[str, List[str]]] = None,
        attachments: Optional[List[Dict[str, Any]]] = None,
        is_reply: bool = False,
        original_sender: Optional[str] = None,
        recruiter_email: Optional[str] = None,
        is_markdown: bool = True,
        is_html: bool = False
    ) -> None:
        """Send an email with optional CC and attachments."""
        try:
            logging.info(f"Preparing to send email to {to_email}")
            logging.debug(f"Subject: {subject}")

            # Format content if markdown and not already HTML
            if is_markdown and not is_html:
                plain_text, html_content = self._format_markdown_content(body)
            elif is_html:
                # If content is HTML, create a plain text version by stripping tags
                soup = BeautifulSoup(body, 'html.parser')
                plain_text = soup.get_text()
                html_content = body
            else:
                plain_text = body
                html_content = None

            # Convert single cc email to list if needed
            cc_list = []
            if cc:
                if isinstance(cc, str):
                    cc_list = [cc]
                elif isinstance(cc, list):
                    cc_list = cc

            # For replies, ensure recruiter is CC'd
            if is_reply and recruiter_email and recruiter_email not in cc_list:
                cc_list.append(recruiter_email)

            recipients = [to_email] + cc_list
            logging.info(f"Recipients: {recipients}")

            # Create message
            msg = MIMEMultipart('alternative')
            msg["Subject"] = subject
            msg["From"] = f"Recruiva AI <{settings.EMAIL_FROM}>"
            msg["To"] = to_email
            if cc_list:
                msg["Cc"] = ", ".join(cc_list)
            if is_reply and original_sender:
                msg["In-Reply-To"] = original_sender

            # Add plain text version
            msg.attach(MIMEText(plain_text, 'plain'))

            # Add HTML version
            if html_content:
                msg.attach(MIMEText(html_content, 'html'))

            # Add any additional attachments
            if attachments:
                for attachment in attachments:
                    if attachment.get('content_type') == 'text/html':
                        msg.attach(MIMEText(attachment['content'].decode('utf-8'), 'html'))
                    else:
                        part = MIMEText(attachment['content'], attachment['content_type'])
                        part.add_header('Content-Disposition', f'attachment; filename="{attachment["filename"]}"')
                        msg.attach(part)

            # Connect to SMTP server and send email
            logging.info("Connecting to SMTP server...")
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()  # Enable TLS
                server.login(self.email, self.password)
                logging.info("SMTP login successful")

                # Send the email
                server.send_message(msg)
                logging.info(f"Email sent successfully to {to_email}")

        except Exception as e:
            logging.error(f"Error sending email: {str(e)}", exc_info=True)
            raise

    def _decode_email_part(self, part) -> str:
        """Safely decode email part content."""
        content = part.get_payload(decode=True)
        charset = part.get_content_charset()

        if charset is None:
            # Try common charsets
            for encoding in ['utf-8', 'latin-1', 'ascii']:
                try:
                    return content.decode(encoding)
                except UnicodeDecodeError:
                    continue
            # If all fails, use 'replace' error handler
            return content.decode('utf-8', errors='replace')

        try:
            return content.decode(charset)
        except UnicodeDecodeError:
            return content.decode(charset, errors='replace')

    def _limit_processed_ids_cache(self, max_size=1000):
        """Limit the size of the processed message IDs cache to prevent memory leaks."""
        if len(EmailService._processed_message_ids) > max_size:
            # Convert to list, sort by insertion order (if possible), and keep only the most recent
            # Since we don't track insertion order, we'll just remove random elements
            excess = len(EmailService._processed_message_ids) - max_size
            if excess > 0:
                if self.DEBUG:
                    logging.info(f"Trimming processed message IDs cache by {excess} entries")
                for _ in range(excess):
                    if EmailService._processed_message_ids:
                        EmailService._processed_message_ids.pop()

    async def check_new_emails(self) -> List[Dict[str, Any]]:
        """Check for new emails and return parsed messages."""
        try:
            # Limit the size of the processed message IDs cache
            self._limit_processed_ids_cache()

            if self.DEBUG:
                logging.info("Connecting to IMAP server...")
            imap = await self.connect_imap()
            imap.select('INBOX')
            if self.DEBUG:
                logging.info("Connected to INBOX")

            # Search for unread emails
            if self.DEBUG:
                logging.info("Searching for unread emails...")
            _, message_numbers = imap.search(None, 'UNSEEN')
            message_list = message_numbers[0].split()
            if self.DEBUG:
                logging.info(f"Found {len(message_list)} unread messages")

            emails = []
            for num in message_list:
                try:
                    # Check if we've already processed this message
                    message_id = num.decode('utf-8') if isinstance(num, bytes) else num
                    if message_id in EmailService._processed_message_ids:
                        if self.DEBUG:
                            logging.info(f"Skipping already processed message {message_id}")
                        continue

                    if self.DEBUG:
                        logging.info(f"Fetching message {num}...")
                    _, msg_data = imap.fetch(num, '(RFC822)')
                    if not msg_data or not msg_data[0]:
                        logging.warning(f"No data found for message {num}")
                        continue

                    email_body = msg_data[0][1]
                    email_message = email.message_from_bytes(email_body)

                    # Get message ID from headers if available
                    message_id = email_message.get('Message-ID', message_id)

                    # Skip if already processed
                    if message_id in EmailService._processed_message_ids:
                        if self.DEBUG:
                            logging.info(f"Skipping already processed message with ID {message_id}")
                        continue

                    # Add to processed set
                    EmailService._processed_message_ids.add(message_id)

                    # Parse email content
                    parsed_email = {
                        'subject': email_message['subject'],
                        'from': email_message['from'],
                        'to': email_message['to'],
                        'cc': email_message['cc'],
                        'date': email_message['date'],
                        'body': '',
                        'attachments': []
                    }
                    if self.DEBUG:
                        logging.info(f"Processing email: {parsed_email['subject']}")

                    # Get email body and attachments
                    if email_message.is_multipart():
                        for part in email_message.walk():
                            if part.get_content_type() == "text/plain":
                                parsed_email['body'] = self._decode_email_part(part)
                                if self.DEBUG:
                                    logging.info("Found plain text content")
                            elif part.get_content_type() == "text/html":
                                parsed_email['html_body'] = self._decode_email_part(part)
                                if self.DEBUG:
                                    logging.info("Found HTML content")
                            elif part.get_content_maintype() != 'multipart':
                                # Handle attachments
                                filename = part.get_filename()
                                if filename:
                                    parsed_email['attachments'].append({
                                        'filename': filename,
                                        'content': part.get_payload(decode=True),
                                        'content_type': part.get_content_type()
                                    })
                                    if self.DEBUG:
                                        logging.info(f"Found attachment: {filename}")
                    else:
                        parsed_email['body'] = self._decode_email_part(email_message)
                        if self.DEBUG:
                            logging.info("Found single-part email content")

                    emails.append(parsed_email)
                    if self.DEBUG:
                        logging.info(f"Successfully processed email {num}")

                    # Mark email as seen after successful processing
                    imap.store(num, '+FLAGS', '\\Seen')
                    if self.DEBUG:
                        logging.info(f"Marked email {num} as seen")

                except Exception as e:
                    logging.error(f"Error processing email {num}: {str(e)}")
                    continue

            imap.close()
            imap.logout()
            if self.DEBUG:
                logging.info(f"Completed email check, found {len(emails)} new messages")
            else:
                # Minimal logging even when DEBUG is False
                if emails:
                    logging.info(f"Found {len(emails)} new emails")

            return emails

        except Exception as e:
            logging.error(f"Error in check_new_emails: {str(e)}")
            raise

    async def start_email_listener(self):
        """Start the email listening service.

        NOTE: This service is currently disabled via EMAIL_LISTENER_ENABLED=False in config.py.
        To re-enable:
        1. Set EMAIL_LISTENER_ENABLED=True in backend/app/core/config.py
        2. Restart the application
        """
        logging.info("Starting email listener service...")
        # Check if the service is enabled in settings
        if not settings.EMAIL_LISTENER_ENABLED:
            logging.warning("Email listener service is DISABLED in configuration. Set EMAIL_LISTENER_ENABLED=True to enable.")
            return

        while True:
            try:
                if self.DEBUG:
                    logging.info("Checking for new emails...")
                await self._process_new_emails()
                if self.DEBUG:
                    logging.debug(f"Sleeping for {EMAIL_WORKFLOW_CONFIG['email_check_interval']} seconds")
                await asyncio.sleep(EMAIL_WORKFLOW_CONFIG["email_check_interval"])
            except Exception as e:
                logging.error(f"Error in email listener: {str(e)}", exc_info=True)
                await asyncio.sleep(60)  # Wait before retrying

    async def _process_new_emails(self):
        """Process new incoming emails."""
        if self.DEBUG:
            logging.info("Fetching new emails...")
        emails = await self.check_new_emails()
        if self.DEBUG:
            logging.info(f"Found {len(emails)} new emails to process")

        for email_data in emails:
            try:
                if self.DEBUG:
                    logging.info(f"Processing email from {email_data['from']}")

                # Validate sender
                if self.DEBUG:
                    logging.debug(f"Validating sender: {email_data['from']}")
                is_valid, user_id = await self._validate_sender(email_data['from'])
                if not is_valid or not user_id:
                    logging.info(f"Skipping email from unauthorized sender: {email_data['from']}")
                    continue

                # Add user_id to email_data
                email_data['user_id'] = user_id

                # Process email content
                if self.DEBUG:
                    logging.info("Handling email content...")
                await self._handle_email(email_data)
                logging.info(f"Successfully processed email from {email_data['from']}")

            except Exception as e:
                logging.error(f"Error processing email: {str(e)}", exc_info=True)
                # Send error notification if needed
                await self._send_error_notification(email_data, str(e))

    async def _validate_sender(self, from_address: str) -> Tuple[bool, Optional[str]]:
        """Validate if the sender is authorized and return their UID if valid."""
        _, email_address = parseaddr(from_address)

        try:
            # First check if user exists in Firebase Auth
            try:
                user = auth.get_user_by_email(email_address)
                if not user.email_verified:
                    logging.warning(f"User email not verified: {email_address}")
                    return False, None
            except auth.UserNotFoundError:
                logging.warning(f"User not found in Firebase Auth: {email_address}")
                return False, None

            # Then check if user exists in Firestore and is active
            user_query = self.firebase.db.collection('users').where(
                filter=FieldFilter('email', '==', email_address)
            ).limit(1)
            user_docs = user_query.get()

            if not user_docs or len(user_docs) == 0:
                logging.warning(f"User not found in Firestore: {email_address}")
                return False, None

            user_data = user_docs[0].to_dict()
            if user_data.get('status') != 'active':
                logging.warning(f"User account not active: {email_address}")
                return False, None

            return True, user.uid

        except Exception as e:
            logging.error(f"Error validating sender {email_address}: {str(e)}")
            return False, None

    async def _handle_email(self, email_data: Dict[str, Any]):
        """Process an incoming email with AI service."""
        logging.info(f"Processing email from {email_data['from']} with subject: {email_data['subject']}")

        # Validate sender and get user ID
        is_valid, user_id = await self._validate_sender(email_data['from'])
        if not is_valid:
            logging.warning(f"Invalid sender: {email_data['from']}")
            return

        # Get AI service instance
        ai_service = AIService()

        # Extract metadata and context
        logging.debug("Extracting email metadata")
        metadata = await self._extract_metadata(email_data)
        metadata['user_id'] = user_id  # Add user ID to metadata

        # Extract sender's name from email address
        sender_email = metadata.get('sender', '')
        sender_name = sender_email.split('@')[0] if '@' in sender_email else 'User'
        # Clean up the name (capitalize first letter of each word, remove dots/underscores)
        sender_name = ' '.join([part.capitalize() for part in re.split(r'[._]', sender_name)])
        metadata['sender_name'] = sender_name  # Add sender's name to metadata

        logging.debug(f"Extracted metadata: {json.dumps(metadata, indent=2)}")

        # Process with AI service
        logging.info("Sending email to AI service for processing")
        try:
            # Process the email with AI service
            response = await ai_service.process_email(
                user_message=email_data['body'],
                metadata=metadata
            )

            # Handle the AI response
            await self._handle_ai_response(response, email_data, user_id)

        except Exception as e:
            logging.error(f"Error in AI service processing: {str(e)}", exc_info=True)

            # Get AI service instance for error response
            try:
                # Create error metadata
                error_metadata = {
                    'user_id': user_id,
                    'sender': email_data.get('from', ''),
                    'sender_name': sender_name,
                    'subject': email_data.get('subject', ''),
                    'action': 'error',
                    'error': str(e)
                }

                # Get error response from AI service
                error_response = await ai_service.process_email(
                    user_message=f"Generate an error response for: {str(e)}",
                    metadata=error_metadata
                )

                # Use the generated content or fallback to simple error message
                error_body = error_response.get('content', '')
                if not error_body:
                    error_body = f"""
Hi {sender_name}! 👋

I apologize, but I encountered an error processing your request: {str(e)}

Please try again or contact support if the issue persists.

Best wishes,
Recruiva AI
"""

                await self.send_email(
                    subject="Error Processing Your Request",
                    body=error_body,
                    to_email=email_data['from'],
                    is_markdown=True
                )
                logging.info(f"Sent error response email to {email_data['from']}")
            except Exception as send_error:
                logging.error(f"Failed to send error email: {str(send_error)}", exc_info=True)

            await self._send_error_notification(
                email_data,
                f"Error processing email: {str(e)}"
            )

    async def _extract_metadata(self, email_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and structure email metadata."""
        return {
            'subject': email_data['subject'],
            'sender': email_data['from'],
            'recipient': email_data['to'],
            'cc': email_data.get('cc'),
            'date': email_data['date'],
            'thread_id': email_data.get('thread_id'),
            'has_attachments': len(email_data.get('attachments', [])) > 0,
            'timestamp': datetime.now().isoformat(),
            'previous_messages': email_data.get('previous_messages', [])
        }

    async def _handle_ai_response(self, response: Dict[str, Any], email_data: Dict[str, Any], user_id: str):
        """Handle AI service response with proper authentication."""
        action = response.get('action')
        content = response.get('content', '')
        result = response.get('result', {})

        # Log the response for debugging
        logging.info(f"AI response action: {action}")
        logging.debug(f"AI response: {json.dumps(response, default=str)}")

        try:
            # Extract sender's name from email address
            sender_email = email_data.get('from', '')
            sender_name = sender_email.split('@')[0] if '@' in sender_email else 'User'
            # Clean up the name (capitalize first letter of each word, remove dots/underscores)
            sender_name = ' '.join([part.capitalize() for part in re.split(r'[._]', sender_name)])

            # Format the email subject based on the action
            subject = f'Response: {email_data.get("subject", "Your Request")}'
            if action == 'create_role':
                subject = f'Role Created: {result.get("role", {}).get("title", "New Role")}'
            elif action == 'get_roles':
                subject = f'Your Roles ({result.get("count", 0)} found)'
            elif action == 'get_role_details':
                subject = 'Role Details'
            elif action == 'update_role':
                subject = 'Role Updated'
            elif action == 'error':
                subject = 'Error Processing Your Request'

            # Use the content from the AI response
            body = content

            # If no content is provided, we need to get a response from the AI service
            if not body:
                # Get AI service instance
                ai_service = AIService()

                # Prepare metadata for AI service with all necessary context
                metadata = {
                    'user_id': user_id,
                    'sender': email_data.get('from', ''),
                    'sender_name': sender_name,
                    'subject': email_data.get('subject', ''),
                    'action': action,
                    'result': result
                }

                # Let the AI service generate a response based on the action and result
                # The email_agent.prompt will handle the specific instructions
                ai_response = await ai_service.process_email(
                    user_message=f"Generate response for {action}",
                    metadata=metadata
                )

                # Use the generated content
                body = ai_response.get('content', '')

                # If still no content, log error
                if not body:
                    logging.error("Failed to generate email content")
                    return

            # Send the email response
            await self.send_email(
                subject=subject,
                body=body,
                to_email=email_data['from'],
                is_reply=True,
                original_sender=email_data.get('to'),
                is_markdown=True
            )
            logging.info(f"Sent email response to {email_data['from']}")

            # Store the email interaction in Firebase
            try:
                # Create a record of this interaction
                interaction_data = {
                    'user_id': user_id,
                    'timestamp': datetime.now().isoformat(),
                    'email_from': email_data.get('from', ''),
                    'email_to': email_data.get('to', ''),
                    'subject': email_data.get('subject', ''),
                    'action': action or 'direct_response',
                    'response_sent': True,
                    'content_generated': body != ''
                }

                # Add additional data based on action
                if action == 'create_role' and 'result' in response:
                    interaction_data['role_id'] = response.get('result', {}).get('role_id')

                # Store in Firebase
                await self.firebase.add_document('email_interactions', interaction_data)
                logging.info(f"Stored email interaction record for user {user_id}")

            except Exception as e:
                logging.error(f"Error storing email interaction: {str(e)}", exc_info=True)

        except Exception as e:
            logging.error(f"Error handling AI response: {str(e)}", exc_info=True)
            # Try to get an error response from the AI service
            try:
                ai_service = AIService()
                error_metadata = {
                    'user_id': user_id,
                    'sender': email_data.get('from', ''),
                    'sender_name': sender_name if 'sender_name' in locals() else 'User',
                    'subject': email_data.get('subject', ''),
                    'action': 'error',
                    'error': str(e)
                }

                # Let the AI service generate an error response
                # The email_agent.prompt will handle the specific instructions
                error_response = await ai_service.process_email(
                    user_message=f"Generate error response",
                    metadata=error_metadata
                )

                error_body = error_response.get('content', '')

                if error_body:
                    await self.send_email(
                        subject="Error Processing Your Request",
                        body=error_body,
                        to_email=email_data['from'],
                        is_reply=True,
                        original_sender=email_data.get('to'),
                        is_markdown=True
                    )
                    logging.info(f"Sent AI-generated error response email to {email_data['from']}")
                else:
                    logging.error(f"Failed to generate error response content")
            except Exception as send_error:
                logging.error(f"Failed to send error email: {str(send_error)}", exc_info=True)

    async def _send_error_notification(self, email_data: Dict[str, Any], error_message: str):
        """Send error notification to system administrators."""
        logging.info(f"Sending error notification to admin: {settings.ADMIN_EMAIL}")

        try:
            # Get AI service instance
            ai_service = AIService()

            # Prepare metadata for AI service with all necessary context
            metadata = {
                'recipient': settings.ADMIN_EMAIL,
                'action': 'error_notification',
                'error': error_message,
                'email_data': {
                    'from': email_data.get('from', 'Unknown'),
                    'subject': email_data.get('subject', 'No Subject'),
                    'date': email_data.get('date', 'Unknown')
                },
                'timestamp': datetime.now().isoformat()
            }

            # Let the AI service generate an error notification
            # The email_agent.prompt will handle the specific instructions
            ai_response = await ai_service.process_email(
                user_message="Generate error notification for admin",
                metadata=metadata
            )

            # Use the generated content
            body = ai_response.get('content', '')
            subject = "Email Processing Error Alert"

            # If AI service didn't provide content, log error and return
            if not body:
                logging.error("Failed to generate error notification content")
                return

            # Send the notification
            await self.send_email(
                subject=subject,
                body=body,
                to_email=settings.ADMIN_EMAIL,
                is_markdown=True
            )
            logging.info("Error notification sent successfully")
        except Exception as e:
            logging.error(f"Failed to send error notification: {str(e)}", exc_info=True)

    def _format_markdown_content(self, content: str) -> Tuple[str, str]:
        """Format content as both plain text and HTML with proper markdown support."""
        # Convert markdown to HTML
        html = markdown.markdown(content, extensions=['tables', 'fenced_code'])

        # Add some basic styling
        styled_html = f"""
        <html>
        <head>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                pre {{
                    background-color: #f5f5f5;
                    padding: 15px;
                    border-radius: 5px;
                    overflow-x: auto;
                }}
                code {{
                    background-color: #f5f5f5;
                    padding: 2px 5px;
                    border-radius: 3px;
                }}
                blockquote {{
                    border-left: 4px solid #ddd;
                    padding-left: 15px;
                    color: #666;
                    margin: 15px 0;
                }}
                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin: 15px 0;
                }}
                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }}
                th {{
                    background-color: #f5f5f5;
                }}
            </style>
        </head>
        <body>
            {html}
        </body>
        </html>
        """

        # Convert HTML to plain text (for email clients that don't support HTML)
        soup = BeautifulSoup(html, 'html.parser')
        plain_text = soup.get_text()

        # Clean up the plain text
        plain_text = re.sub(r'\n\s*\n', '\n\n', plain_text)  # Remove extra newlines
        plain_text = plain_text.strip()

        return plain_text, styled_html
