# Email Service Documentation

## Overview

The Email Service is a core component of Recruiva's backend that manages all email-related operations, including:

- Automated email monitoring and processing
- AI-powered email response generation
- Role management via email commands
- Email sender validation against Firebase users
- HTML/Markdown email formatting

The service is implemented as a singleton to ensure consistent state management and resource utilization across the application.

## Architecture

### Core Components

#### 1. Email Client Integration
- IMAP integration for receiving and monitoring emails
- SMTP integration for sending emails
- Support for Gmail and other email providers

#### 2. Email Processing Pipeline
- Email fetching and parsing
- Sender validation against Firebase users
- Content extraction and preprocessing
- AI processing with OpenAI integration
- Response generation and sending

#### 3. AI Integration
- Integration with AIService for email content processing
- Function calling for structured operations
- Context-aware response generation
- Error handling and fallback mechanisms

### Implementation Details

#### Singleton Pattern
The EmailService is implemented as a singleton to ensure only one instance exists throughout the application lifecycle:

```python
class EmailService:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(EmailService, cls).__new__(cls)
        return cls._instance
```

#### Email Monitoring
The service implements an asynchronous email listener that periodically checks for new emails:

```python
async def start_email_listener(self):
    """Start the email listening service."""
    while True:
        try:
            await self._process_new_emails()
            await asyncio.sleep(EMAIL_WORKFLOW_CONFIG["email_check_interval"])
        except Exception as e:
            logging.error(f"Error in email listener: {str(e)}", exc_info=True)
            await asyncio.sleep(60)  # Wait before retrying
```

#### Email Processing
New emails are processed through a pipeline that:
1. Validates the sender against Firebase users
2. Extracts metadata and context
3. Sends the email content to the AI service
4. Handles the AI response (which may include function calls)
5. Sends a response email

```python
async def _process_new_emails(self):
    """Process new incoming emails."""
    emails = await self.check_new_emails()
    
    for email_data in emails:
        try:
            # Validate sender
            is_valid, user_id = await self._validate_sender(email_data['from'])
            if not is_valid or not user_id:
                continue

            # Add user_id to email_data
            email_data['user_id'] = user_id

            # Process email content
            await self._handle_email(email_data)
            
        except Exception as e:
            logging.error(f"Error processing email: {str(e)}", exc_info=True)
            await self._send_error_notification(email_data, str(e))
```

#### AI Integration
The service integrates with the AIService to process email content and generate responses:

```python
async def _handle_email(self, email_data: Dict[str, Any]):
    """Process an incoming email with AI service."""
    # Get AI service instance
    ai_service = AIService()
    
    # Extract metadata and context
    metadata = await self._extract_metadata(email_data)
    metadata['user_id'] = user_id
    
    # Process with AI service
    response = await ai_service.process_email(
        user_message=email_data['body'],
        metadata=metadata
    )

    # Handle the AI response
    await self._handle_ai_response(response, email_data, user_id)
```

#### Function Calling
The AI service can execute functions based on the email content, such as:
- Creating a new role
- Updating an existing role
- Getting role details
- Listing roles

## Configuration

### Environment Variables
```env
# Email Configuration
EMAIL_USERNAME="<EMAIL>"
EMAIL_PASSWORD="your-email-password"
EMAIL_FROM="Recruiva AI"

# Email Workflow Configuration
EMAIL_WORKFLOW_CONFIG = {
    "workspace_email": "<EMAIL>",
    "allowed_domains": ["recruiva.ai"],
    "email_check_interval": 60,  # seconds
    "max_email_age": 24 * 60 * 60  # 24 hours in seconds
}
```

## Usage Examples

### Sending an Email
```python
email_service = EmailService()
await email_service.send_email(
    subject="Your Role Has Been Created",
    body="Your role has been successfully created.",
    to_email="<EMAIL>",
    is_html=False
)
```

### Starting the Email Listener
```python
email_service = EmailService()
await email_service.start_email_listener()
```

### Sending a Formatted Email
```python
email_service = EmailService()
markdown_content = """
# Role Created Successfully

Your role **Senior Software Engineer** has been created.

## Next Steps
1. Review the role details
2. Start the interview process
3. Track candidates

[View Role Details](https://recruiva.ai/roles/role123)
"""

await email_service.send_email(
    subject="Role Created: Senior Software Engineer",
    body=markdown_content,
    to_email="<EMAIL>",
    is_html=True,
    convert_markdown=True
)
```

## Error Handling

The Email Service implements comprehensive error handling:

1. **Connection Errors**
   - Automatic retry with exponential backoff
   - Graceful handling of IMAP/SMTP connection issues

2. **Processing Errors**
   - Detailed error logging
   - Error notifications to administrators
   - Skipping problematic emails

3. **AI Service Errors**
   - Fallback to error templates
   - Detailed error reporting
   - Graceful degradation

## Security Considerations

1. **Sender Validation**
   - All email senders are validated against Firebase users
   - Unauthorized senders are ignored

2. **Content Validation**
   - Email content is validated before processing
   - Suspicious content is flagged

3. **Authentication**
   - Secure SMTP/IMAP authentication
   - Environment variable-based credentials

## Integration with Other Services

The Email Service integrates with:

1. **AIService**
   - For email content processing
   - For function calling

2. **FirebaseService**
   - For user validation
   - For role operations

3. **RolesService**
   - For role creation and management
   - For role data retrieval

## Best Practices

1. Use the singleton instance of EmailService
2. Handle service exceptions appropriately
3. Validate input data before service calls
4. Use proper typing for function parameters
5. Follow the established error handling patterns 