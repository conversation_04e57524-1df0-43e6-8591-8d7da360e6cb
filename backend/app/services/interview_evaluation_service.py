# File: backend/app/services/interview_evaluation_service.py

from typing import Dict, Any, List, Optional, Union
import logging
import json
from datetime import datetime
from pydantic import BaseModel, Field, ValidationError

from app.services.openai.chat_completion_service import ChatCompletionService
from app.services.openai.prompts.manager import PromptManager
from app.services.openai.config.model_configs import ModelConfigurationManager
from app.services.firebase_service import FirebaseService
from app.services.roles_service import RolesService

# Configure logging
logger = logging.getLogger(__name__)

class InterviewEvaluationService:
    """Service for evaluating interview transcripts against job requirements using AI"""

    _instance = None

    def __new__(cls):
        """Implement singleton pattern"""
        if cls._instance is None:
            cls._instance = super(InterviewEvaluationService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the service"""
        self.firebase_service = FirebaseService()
        self.roles_service = RolesService()

    @staticmethod
    async def evaluate_interview(
        interview_id: str,
        role_id: str,
        application_id: str = None,
        is_authenticated: bool = True,
        user_id: str = None,
        template_id: str = None,
        stage_name: str = None,
        stage_index: int = None,
        template_data: Dict[str, Any] = None,
        direct_resume_text: str = None
    ) -> Dict[str, Any]:
        """
        Core method to evaluate an interview transcript against job requirements.

        Args:
            interview_id: ID of the interview transcript
            role_id: ID of the role
            application_id: Optional ID of the application
            is_authenticated: Whether the request is from an authenticated user
            user_id: Optional ID of the authenticated user
            template_id: Optional ID of the template to use
            stage_name: Optional name of the interview stage
            stage_index: Optional index of the interview stage
            template_data: Optional template data to use directly
            direct_resume_text: Optional resume text to use directly without fetching from database

        Returns:
            Dictionary containing evaluation results
        """
        try:
            logger.info(f"Evaluating interview {interview_id} for role {role_id}")
            logger.info(f"Context: {'Authenticated' if is_authenticated else 'Public'}")

            # Initialize services
            firebase_service = FirebaseService()
            roles_service = RolesService()

            # Step 1: Gather all required data
            # This will be different based on authentication context

            # Get interview transcript
            transcript = None
            transcript_messages = []

            if is_authenticated:
                logger.info(f"Fetching authenticated interview transcript for user {user_id}")
                try:
                    # For authenticated users, get transcript from the user's collection
                    transcript = await firebase_service.get_interview_transcript(user_id, role_id, interview_id)
                    if not transcript:
                        logger.error(f"Interview transcript {interview_id} not found for user {user_id}")
                        return {"status": "error", "message": f"Interview transcript {interview_id} not found"}

                    # Extract messages from transcript
                    transcript_messages = transcript.get("messages", [])
                except Exception as e:
                    logger.error(f"Error fetching authenticated transcript: {str(e)}")
                    return {"status": "error", "message": f"Error fetching transcript: {str(e)}"}
            else:
                logger.info(f"Fetching public interview transcript {interview_id}")
                try:
                    # For public context, get transcript from public_interview_sessions
                    session_ref = firebase_service.db.collection("public_interview_sessions").document(interview_id)
                    session_doc = session_ref.get()

                    if not session_doc.exists:
                        logger.error(f"Public interview session {interview_id} not found")
                        return {"status": "error", "message": f"Interview session {interview_id} not found"}

                    transcript = session_doc.to_dict()
                    transcript_messages = transcript.get("messages", [])
                except Exception as e:
                    logger.error(f"Error fetching public transcript: {str(e)}")
                    return {"status": "error", "message": f"Error fetching transcript: {str(e)}"}

            if not transcript_messages:
                logger.error(f"No messages found in transcript {interview_id}")
                return {"status": "error", "message": "No messages found in transcript"}

            logger.info(f"Found {len(transcript_messages)} messages in transcript")

            # Format transcript for the prompt
            formatted_transcript = ""
            for msg in transcript_messages:
                role = msg.get("role", "unknown")
                content = msg.get("content", "")
                if content:
                    formatted_transcript += f"{role.upper()}: {content}\n\n"

            # Get role data
            role_data = await roles_service.get_role_by_id(role_id)
            if not role_data:
                logger.error(f"Role {role_id} not found")
                return {"status": "error", "message": f"Role {role_id} not found"}

            logger.info(f"Found role: {role_data.get('title')}")

            # Get job posting from role data
            job_posting = role_data.get("description", role_data.get("summary", ""))
            if not job_posting:
                logger.warning(f"No job posting found for role {role_id}, using title and summary")
                job_posting = f"Title: {role_data.get('title', '')}\nSummary: {role_data.get('summary', '')}"

            # Get resume text - this is critical for evaluation
            resume_text = direct_resume_text  # Use directly provided resume text if available
            
            # Only fetch from application if we don't have direct resume text
            if not resume_text and application_id:
                try:
                    logger.info(f"Fetching application data for application ID: {application_id}")

                    # Get application data from the main applications collection (single source of truth)
                    app_ref = firebase_service.db.collection("applications").document(application_id)
                    app_doc = app_ref.get()
                    if app_doc.exists:
                        application_data = app_doc.to_dict()
                        logger.info(f"Found application data in applications collection")
                        # Try to get resume text from evaluations
                        if "evaluations" in application_data and application_data["evaluations"]:
                            evaluations = application_data["evaluations"]
                            if isinstance(evaluations, list) and len(evaluations) > 0:
                                latest_eval = evaluations[0]
                                resume_text = latest_eval.get("parsedResumeText")

                        # If not in evaluations, check other possible locations
                        if not resume_text and "resumeText" in application_data:
                            resume_text = application_data["resumeText"]
                        elif not resume_text and "resume_text" in application_data:
                            resume_text = application_data["resume_text"]
                            
                        # Also check for standalone resume field
                        if not resume_text and "resume" in application_data:
                            if isinstance(application_data["resume"], str):
                                resume_text = application_data["resume"]
                            elif isinstance(application_data["resume"], dict) and "text" in application_data["resume"]:
                                resume_text = application_data["resume"]["text"]
                        
                        # Extract any candidate information we can find if no resume
                        if not resume_text:
                            candidate_info = []
                            if "fullName" in application_data and application_data["fullName"]:
                                candidate_info.append(f"Candidate Name: {application_data['fullName']}")
                            if "email" in application_data and application_data["email"]:
                                candidate_info.append(f"Email: {application_data['email']}")
                            if "phone" in application_data and application_data["phone"]:
                                candidate_info.append(f"Phone: {application_data['phone']}")
                            if "experience" in application_data and application_data["experience"]:
                                candidate_info.append(f"Experience: {application_data['experience']}")
                            if "education" in application_data and application_data["education"]:
                                candidate_info.append(f"Education: {application_data['education']}")
                            if "skills" in application_data and application_data["skills"]:
                                if isinstance(application_data["skills"], list):
                                    candidate_info.append(f"Skills: {', '.join(application_data['skills'])}")
                                else:
                                    candidate_info.append(f"Skills: {application_data['skills']}")
                            
                            if candidate_info:
                                resume_text = "\n".join(candidate_info)
                                logger.info("Created basic candidate profile from application data")

                        if resume_text:
                            logger.info(f"Found resume text in application data (length: {len(resume_text)})")
                        else:
                            logger.warning("No resume text found in application data")
                    else:
                        # Fallback check in candidate subcollection for backward compatibility
                        if not is_authenticated and application_id:
                            try:
                                # Try to search in candidates collection as fallback during transition
                                candidateId = None
                                # Try to get candidateId from localStorage reference in the transcript
                                if "candidateId" in transcript:
                                    candidateId = transcript.get("candidateId")

                                    if candidateId:
                                        candidate_app_ref = firebase_service.db.collection("candidates").document(candidateId).collection("applications").document(application_id)
                                        candidate_app_doc = candidate_app_ref.get()
                                        if candidate_app_doc.exists:
                                            application_data = candidate_app_doc.to_dict()
                                            logger.info(f"Found application data in candidates subcollection")
                            except Exception as fallback_error:
                                logger.warning(f"Error in fallback application lookup: {str(fallback_error)}")
                except Exception as e:
                    logger.warning(f"Error fetching application data: {str(e)}")
                    # Continue without application data, not critical

            # If we don't have a resume text yet, extract candidate information from the transcript
            if not resume_text:
                logger.info("Extracting candidate information from transcript")
                candidate_responses = []
                
                # Extract candidate introduction from the transcript
                for idx, msg in enumerate(transcript_messages):
                    if msg.get("role") == "user" and msg.get("content"):
                        content = msg.get("content", "").strip()
                        
                        # First check if this is a candidate introduction message (near the beginning of interview)
                        if idx < 10 and len(content) > 50:
                            candidate_responses.append(content)
                            
                        # Then look for longer responses that contain background information
                        elif len(content) > 100 and any(keyword in content.lower() for keyword in [
                            "experience", "background", "worked", "skills", "education", 
                            "projects", "role", "position", "job", "career", "university",
                            "college", "degree", "certification", "knowledge"
                        ]):
                            candidate_responses.append(content)
                
                if candidate_responses:
                    resume_text = "CANDIDATE INFORMATION EXTRACTED FROM INTERVIEW:\n\n" + "\n\n".join(candidate_responses)
                    logger.info(f"Extracted candidate background information from transcript ({len(resume_text)} chars)")
                else:
                    # Combine all user responses as background information
                    all_user_responses = [msg.get("content", "") for msg in transcript_messages 
                                         if msg.get("role") == "user" and msg.get("content")]
                    
                    # If we have a substantial amount of text, use all responses
                    if all_user_responses and sum(len(r) for r in all_user_responses) > 200:
                        resume_text = "CANDIDATE RESPONSES FROM INTERVIEW:\n\n" + "\n\n".join(all_user_responses)
                        logger.info(f"Created comprehensive background from all user responses ({len(resume_text)} chars)")
                    elif all_user_responses:
                        # Fallback to first responses if we don't have much text
                        resume_text = "CANDIDATE RESPONSES FROM INTERVIEW:\n\n" + "\n\n".join(all_user_responses[:5])
                        logger.info(f"Created background from the first few responses ({len(resume_text)} chars)")
                    else:
                        # This should never happen in a real interview, but handle it just in case
                        resume_text = "CANDIDATE INTERVIEW EVALUATION\nNo background information available. Evaluation based solely on interview responses."
                        logger.warn("Unable to extract candidate information from transcript, proceeding with interview-only evaluation")

            # Get interview template and questions
            template = None

            # Use provided template_id if available, otherwise get from transcript
            if not template_id:
                template_id = transcript.get("template_id") or transcript.get("templateId")

            if template_id:
                try:
                    logger.info(f"Fetching template {template_id} for role {role_id}")
                    if is_authenticated and user_id:
                        template = await roles_service.get_template(role_id, template_id, user_id)
                    else:
                        # Try to get the public template first
                        template = await roles_service.get_public_template(role_id, template_id)
                        
                        # If not found and we have an owner user_id, try getting it from the user's collection
                        if not template and user_id:
                            try:
                                logger.info(f"Trying to get template from user {user_id}'s collection")
                                template = await roles_service.get_template(role_id, template_id, user_id)
                            except Exception as user_template_error:
                                logger.warning(f"Error fetching template from user collection: {str(user_template_error)}")
                except Exception as e:
                    logger.warning(f"Error fetching template: {str(e)}")

            if not template:
                # If template_data was provided directly, use it
                if template_data and isinstance(template_data, dict):
                    logger.info("Using provided template_data parameter")
                    template = template_data
                else:
                    logger.warning("Template not found, checking transcript for template data")
                    # Try to get template data from the transcript itself
                    template = transcript.get("template", {})
                    
                    # Check several fields that might contain template data
                    if not template or not isinstance(template, dict) or len(template) == 0:
                        # If template isn't a valid object, try other fields
                        if "templateData" in transcript and isinstance(transcript["templateData"], dict):
                            template = transcript["templateData"]
                        elif "stage_data" in transcript and isinstance(transcript["stage_data"], dict):
                            template = transcript["stage_data"]
                        
            # If we still don't have a template but we have stage information, try to
            # find the template from the role's interview process
            if (not template or not isinstance(template, dict) or len(template) == 0) and role_data.get("interviewProcess"):
                interview_process = role_data.get("interviewProcess", [])
                target_stage = None
                
                # Try to find the matching stage by name
                if stage_name:
                    target_stage = next((s for s in interview_process if s.get("stage") == stage_name), None)
                    
                # If not found by name but we have an index, try by index
                if not target_stage and stage_index is not None:
                    if 0 <= stage_index < len(interview_process):
                        target_stage = interview_process[stage_index]
                        
                if target_stage:
                    logger.info(f"Found matching stage in interview process: {target_stage.get('stage')}")
                    template = {
                        "id": template_id or f"{role_id}_stage_{stage_index}",
                        "stage": target_stage.get("stage"),
                        "stageIndex": stage_index,
                        "duration": target_stage.get("duration"),
                        "customInstructions": target_stage.get("customInstructions"),
                    }
                    
                    # Try to get a real template for this stage
                    try:
                        templates = await roles_service.get_templates(role_id)
                        matching_template = next((t for t in templates if t.get("stageIndex") == stage_index), None)
                        if matching_template:
                            logger.info(f"Found matching template for stage index {stage_index}")
                            template = matching_template
                    except Exception as template_error:
                        logger.warning(f"Error fetching templates for role: {str(template_error)}")

            # If template still isn't available, derive it from role information
            if not template or not isinstance(template, dict):
                logger.info("Deriving template structure from role information")
                
                # Extract stage information from role data
                derived_stage = None
                derived_index = None
                
                if role_data and "interviewProcess" in role_data:
                    interview_process = role_data.get("interviewProcess", [])
                    if interview_process and len(interview_process) > 0:
                        # If stage_name provided, try to match it
                        if stage_name:
                            for idx, stage in enumerate(interview_process):
                                if stage.get("stage") == stage_name:
                                    derived_stage = stage_name
                                    derived_index = idx
                                    break
                        
                        # If stage_index provided or no match found, use the index
                        if derived_stage is None:
                            idx = stage_index if stage_index is not None and 0 <= stage_index < len(interview_process) else 0
                            derived_stage = interview_process[idx].get("stage")
                            derived_index = idx
                
                # If we still don't have a stage, use defaults from role title or application stage
                if not derived_stage:
                    if role_data and "title" in role_data:
                        derived_stage = f"{role_data.get('title')} Interview"
                    else:
                        derived_stage = stage_name or "Technical Interview"
                
                if derived_index is None:
                    derived_index = stage_index if stage_index is not None else 0
                
                # Build a minimal but accurate template
                template = {
                    "id": template_id or f"{role_id}_interview_{derived_index}",
                    "stage": derived_stage,
                    "stageIndex": derived_index,
                    "roleId": role_id
                }
                
                logger.info(f"Created derived template for stage '{derived_stage}' (index: {derived_index})")

            # Format interview questions
            interview_questions_str = ""
            
            # First try to get questions from the template
            if template.get("questions") and isinstance(template["questions"], list):
                questions = []
                for q in template["questions"]:
                    if not q.get("question"):
                        continue

                    question_str = f"Question: {q.get('question', '')}\n"
                    if q.get("purpose"):
                        question_str += f"Purpose: {q.get('purpose')}\n"
                    if q.get("idealAnswerCriteria"):
                        question_str += f"Ideal Answer Criteria: {q.get('idealAnswerCriteria')}\n"

                    questions.append(question_str)

                interview_questions_str = "\n\n".join(questions)
                logger.info(f"Prepared {len(questions)} interview questions from template")
            
            # If no questions in template, try to extract them from the transcript
            if not interview_questions_str:
                # Extract questions from the AI messages in the transcript
                ai_questions = []
                for msg in transcript_messages:
                    if msg.get("role") in ["assistant", "Recruiva", "system"] and msg.get("content"):
                        content = msg.get("content", "")
                        # Look for clear question patterns
                        if "?" in content and len(content) < 500:  # Simple questions shouldn't be too long
                            ai_questions.append(f"Question: {content.strip()}")
                
                if ai_questions:
                    interview_questions_str = "\n\n".join(ai_questions)
                    logger.info(f"Extracted {len(ai_questions)} questions from transcript")
            
            # If we still don't have questions, try to get them from the role's description
            if not interview_questions_str and role_data:
                # Look for key responsibilities in the role data
                key_responsibilities = role_data.get("keyResponsibilities") or []
                required_skills = role_data.get("requiredSkills") or {}
                
                potential_questions = []
                
                # Convert responsibilities to questions
                for resp in key_responsibilities:
                    if resp:
                        potential_questions.append(f"Question: Can you explain your experience with {resp.lower()}?")
                
                # Convert skills to questions
                for skill, level in required_skills.items():
                    if skill:
                        potential_questions.append(f"Question: How would you rate your proficiency in {skill}? Please provide examples of how you've demonstrated this skill.")
                
                if potential_questions:
                    # Take up to 5 questions to avoid overwhelming
                    questions_to_use = potential_questions[:5]
                    interview_questions_str = "\n\n".join(questions_to_use)
                    logger.info(f"Generated {len(questions_to_use)} questions from role requirements")

            # Last resort: if we have no questions at this point, extract them directly from the transcript
            if not interview_questions_str:
                # Extract any assistant messages that look like questions directly from the transcript
                questions = []
                for msg in transcript_messages:
                    if msg.get("role") in ["assistant", "Recruiva", "system"] and msg.get("content") and "?" in msg.get("content", ""):
                        questions.append(f"Question: {msg.get('content').strip()}")
                
                # If we found any questions at all, use them
                if questions:
                    interview_questions_str = "\n\n".join(questions)
                    logger.info(f"Extracted {len(questions)} interview questions directly from transcript messages")
                else:
                    # This should never happen in a real interview, but handle the edge case
                    logger.error("Unable to find any questions in transcript, generating from required skills")
                    # Generate one question for each required skill in the role data
                    questions = []
                    if role_data and role_data.get("requiredSkills"):
                        for skill in role_data.get("requiredSkills", {}).keys():
                            questions.append(f"Question: Describe your experience with {skill} and how you've applied it in your previous roles.")
                        interview_questions_str = "\n\n".join(questions)
                        logger.info(f"Generated {len(questions)} interview questions from required skills")
                    else:
                        # Extract keywords from job description for questions if all else fails
                        common_topics = ["experience", "background", "skills", "challenges", "projects", "teamwork", "leadership"]
                        job_text = job_posting.lower()
                        for topic in common_topics:
                            if topic in job_text and len(questions) < 5:
                                questions.append(f"Question: Tell me about your {topic} as it relates to this role.")
                        interview_questions_str = "\n\n".join(questions)
                        logger.info(f"Generated {len(questions)} interview questions from job description keywords")

            # Format evaluation criteria
            evaluation_criteria_str = ""
            pass_rate = 0.7  # Default pass rate

            if template.get("evaluationCriteria") and isinstance(template["evaluationCriteria"], list):
                criteria_list = []
                scorecard_criteria = []
                between_lines_criteria = []
                disqualifier_criteria = []

                for c in template["evaluationCriteria"]:
                    criterion_type = c.get("type", "")

                    if criterion_type == "ScoreCard":
                        scorecard_str = f"Competency: {c.get('competency', '')}\n"
                        scorecard_str += f"Weight: {c.get('weight', 0.0)}\n"
                        scorecard_str += f"Criteria: {c.get('criteria', '')}\n"
                        if c.get("description"):
                            scorecard_str += f"Description: {c.get('description')}\n"
                        scorecard_criteria.append(scorecard_str)

                    elif criterion_type == "BetweenTheLines":
                        btl_str = f"Criteria: {c.get('criteria', '')}\n"
                        if c.get("description"):
                            btl_str += f"Description: {c.get('description')}\n"
                        between_lines_criteria.append(btl_str)

                    elif criterion_type == "Disqualifier":
                        disq_str = f"Criteria: {c.get('criteria', '')}\n"
                        if c.get("description"):
                            disq_str += f"Description: {c.get('description')}\n"
                        disqualifier_criteria.append(disq_str)

                # Format all criteria types
                if scorecard_criteria:
                    criteria_list.append("## SCORECARD CRITERIA")
                    criteria_list.append("\n\n".join(scorecard_criteria))

                if between_lines_criteria:
                    criteria_list.append("## BETWEEN THE LINES CRITERIA")
                    criteria_list.append("\n\n".join(between_lines_criteria))

                if disqualifier_criteria:
                    criteria_list.append("## DISQUALIFIER CRITERIA")
                    criteria_list.append("\n\n".join(disqualifier_criteria))

                evaluation_criteria_str = "\n\n".join(criteria_list)

                # Get pass rate if available
                if template.get("passRate") is not None:
                    pass_rate = float(template.get("passRate"))
                elif template.get("statistics", {}).get("passRate") is not None:
                    pass_rate = float(template.get("statistics", {}).get("passRate"))

                logger.info(f"Prepared evaluation criteria with {len(scorecard_criteria)} scorecard, " +
                           f"{len(between_lines_criteria)} between-the-lines, and " +
                           f"{len(disqualifier_criteria)} disqualifier criteria")

            # If no criteria in template, try to generate relevant criteria from role data
            if not evaluation_criteria_str and role_data:
                logger.info("Generating evaluation criteria from role requirements")
                criteria_list = []
                scorecard_criteria = []
                between_lines_criteria = []
                disqualifier_criteria = []
                
                # Generate scorecard criteria from required skills
                if role_data.get("requiredSkills"):
                    for skill, level in role_data.get("requiredSkills", {}).items():
                        weight = 0.0
                        if level == "Expert":
                            weight = 0.3
                        elif level == "Intermediate":
                            weight = 0.2
                        else:
                            weight = 0.1
                            
                        # Normalize total weights to sum to 1.0
                        total_skills = len(role_data.get("requiredSkills", {}))
                        if total_skills > 0:
                            weight = round(1.0 / total_skills, 2)
                            
                        scorecard_str = f"Competency: {skill}\n"
                        scorecard_str += f"Weight: {weight}\n"
                        scorecard_str += f"Criteria: Evaluate the candidate's {skill.lower()} abilities\n"
                        scorecard_str += f"Description: Assess how well the candidate demonstrates {skill.lower()} during the interview\n"
                        scorecard_criteria.append(scorecard_str)
                
                # Add the company values as between-the-lines criteria
                company_name = role_data.get("aboutCompany", "").split(' ')[0] if role_data.get("aboutCompany") else "Company"
                between_lines_criteria.append(f"Criteria: Cultural Fit with {company_name}\nDescription: Assess how well the candidate would fit into the company culture\n")
                between_lines_criteria.append("Criteria: Communication Clarity\nDescription: Evaluate how clearly and effectively the candidate communicates their thoughts\n")
                
                # Add standard disqualifiers
                disqualifier_criteria.append("Criteria: Misrepresentation of Qualifications\nDescription: Evidence that the candidate has misrepresented their qualifications or experience\n")
                disqualifier_criteria.append("Criteria: Inappropriate Conduct\nDescription: Inappropriate language, behavior, or attitude during the interview\n")
                
                # Format all criteria types
                if scorecard_criteria:
                    criteria_list.append("## SCORECARD CRITERIA")
                    criteria_list.append("\n\n".join(scorecard_criteria))

                if between_lines_criteria:
                    criteria_list.append("## BETWEEN THE LINES CRITERIA")
                    criteria_list.append("\n\n".join(between_lines_criteria))

                if disqualifier_criteria:
                    criteria_list.append("## DISQUALIFIER CRITERIA")
                    criteria_list.append("\n\n".join(disqualifier_criteria))

                evaluation_criteria_str = "\n\n".join(criteria_list)
                logger.info(f"Generated evaluation criteria with {len(scorecard_criteria)} scorecard, " +
                           f"{len(between_lines_criteria)} between-the-lines, and " +
                           f"{len(disqualifier_criteria)} disqualifier criteria from role data")

            # Only use generic criteria as a last resort
            if not evaluation_criteria_str:
                logger.error("Failed to generate evaluation criteria, extracting from job posting")
                # Extract criteria directly from job posting keywords
                criteria_list = []
                
                # Extract 3-4 key words from job posting to use as competencies
                job_words = job_posting.lower().split()
                common_skills = ["communication", "leadership", "technical", "management", 
                                 "problem solving", "teamwork", "analytical", "organization"]
                
                found_skills = []
                for skill in common_skills:
                    if skill in job_posting.lower() and len(found_skills) < 4:
                        found_skills.append(skill.title())
                
                # If we couldn't find any skills in the job posting, use the role title components
                if not found_skills and role_data and role_data.get("title"):
                    title_words = role_data.get("title", "").split()
                    for word in title_words:
                        if len(word) > 3 and word.lower() not in ["and", "the", "for", "with"]:
                            found_skills.append(word.title())
                            if len(found_skills) >= 3:
                                break
                
                # If we still have no criteria, extract from the job title
                if not found_skills and role_data and role_data.get("title"):
                    title_components = role_data.get("title", "").split()
                    found_skills = [w for w in title_components if len(w) > 3][:3]
                
                # If we still have nothing, use generic professional skills to avoid errors
                if not found_skills:
                    found_skills = ["Communication", "Technical Skills", "Problem Solving"]
                    logger.warning(f"Unable to extract specific criteria, using professional skills: {found_skills}")
                
                # Create basic criteria from the found skills
                weight_per_skill = round(1.0 / len(found_skills), 2)
                
                scorecard_criteria = []
                for skill in found_skills:
                    scorecard_str = f"Competency: {skill}\n"
                    scorecard_str += f"Weight: {weight_per_skill}\n"
                    scorecard_str += f"Criteria: Evaluate the candidate's {skill.lower()} abilities\n"
                    scorecard_str += f"Description: Assess how well the candidate demonstrates {skill.lower()} during the interview\n"
                    scorecard_criteria.append(scorecard_str)
                
                # Add minimal criteria for the other sections
                criteria_list.append("## SCORECARD CRITERIA")
                criteria_list.append("\n\n".join(scorecard_criteria))
                
                criteria_list.append("## BETWEEN THE LINES CRITERIA")
                criteria_list.append("Criteria: Overall Impression\nDescription: Your overall impression of the candidate based on the interview\n")
                
                criteria_list.append("## DISQUALIFIER CRITERIA")
                criteria_list.append("Criteria: Critical Skill Gap\nDescription: Missing essential skills required for the position\n")
                
                evaluation_criteria_str = "\n\n".join(criteria_list)
                logger.info(f"Created minimal evaluation criteria with {len(found_skills)} skills extracted from job posting")

            # Step 2: Prepare context for prompt template
            context = {
                "job_posting": job_posting,
                "candidate_resume": resume_text,
                "interview_transcript": formatted_transcript,
                "interview_questions": interview_questions_str,
                "evaluation_criteria": evaluation_criteria_str,
                "minimum_pass_rate": str(pass_rate)
            }

            logger.info(f"Prepared context for interview evaluation with keys: {', '.join(context.keys())}")

            # Step 3: Call ChatCompletionService with the interview evaluation agent
            try:
                logger.info("Calling OpenAI API for interview evaluation")
                # Get the appropriate model for interview evaluation
                model = ModelConfigurationManager.get_appropriate_model("interview_evaluation_agent")
                logger.info(f"Using model {model} for interview evaluation")

                response = await ChatCompletionService.generate_completion(
                    prompt="",
                    model=model,
                    use_case="interview_evaluation_agent",
                    context=context,
                    temperature=0.3,
                    max_tokens=4096
                )

                logger.info("Successfully received response from OpenAI")
            except Exception as e:
                logger.error(f"Error calling OpenAI API: {str(e)}")
                return {"status": "error", "message": f"Error generating evaluation: {str(e)}"}

            # Step 4: Parse and validate the response
            try:
                # Extract content from the response
                content = ""
                if hasattr(response, 'choices') and response.choices:
                    message = response.choices[0].message
                    content = message.content
                else:
                    # Handle dictionary response format
                    content = response.get("choices", [{}])[0].get("message", {}).get("content", "")

                if not content:
                    logger.error("Empty response from OpenAI API")
                    return {"status": "error", "message": "Empty response from evaluation service"}

                # Extract JSON from the response
                # The response might contain markdown formatting with ```json blocks
                json_content = content

                # Check if the content contains a JSON code block
                json_start = content.find("```json")
                json_end = content.rfind("```")

                if json_start != -1 and json_end != -1 and json_end > json_start:
                    # Extract the JSON content from the code block
                    json_content = content[json_start + 7:json_end].strip()

                # Parse the JSON
                evaluation_result = json.loads(json_content)

                # Basic validation of the result structure
                required_keys = ["evaluation_summary", "scorecard_evaluation", "question_analysis",
                                "between_the_lines", "disqualifier_check", "decision_reasoning"]

                for key in required_keys:
                    if key not in evaluation_result:
                        logger.error(f"Missing required key in evaluation result: {key}")
                        return {"status": "error", "message": f"Invalid evaluation format: missing {key}"}

                logger.info("Successfully parsed and validated evaluation result")

                # Step 5: Store the results based on authentication context
                try:
                    # Add base metadata to the result
                    evaluation_result["metadata"] = {
                        "interview_id": interview_id,
                        "role_id": role_id,
                        "application_id": application_id,
                        "evaluated_at": datetime.now().isoformat(),
                        "evaluated_by": "AI"
                    }
                    
                    # Set evaluation ID to None initially
                    evaluation_id = None
                    
                    # CONSISTENT STORAGE APPROACH:
                    # For both authenticated and public interviews with an owner, 
                    # we'll always store in users/{user_id}/roles/{role_id}/interviews/{interview_id}/evaluations/
                    # which is the pattern used by the "Evaluate with AI" button
                    
                    # Determine the effective user ID (either authenticated user or role owner)
                    effective_user_id = user_id if is_authenticated or user_id else None
                    
                    if effective_user_id:
                        logger.info(f"Storing evaluation for user {effective_user_id}, role {role_id}, interview {interview_id}")
                        
                        # Check if the interview document exists
                        interview_ref = firebase_service.db.collection("users").document(effective_user_id) \
                                        .collection("roles").document(role_id) \
                                        .collection("interviews").document(interview_id)

                        interview_doc = interview_ref.get()
                        interview_created = False

                        if not interview_doc.exists:
                            # If not found with exact ID, try to find it by transcript_id or session_id
                            # (only for public interviews with owner)
                            matching_interview_id = None
                            if not is_authenticated and effective_user_id:
                                try:
                                    # Search for matching interviews in the user's collection
                                    interviews_ref = firebase_service.db.collection("users").document(effective_user_id) \
                                                    .collection("roles").document(role_id) \
                                                    .collection("interviews")
                                    
                                    # Try by transcript_id
                                    q = interviews_ref.where("transcript_id", "==", interview_id).limit(1)
                                    matching_interviews = list(q.stream())
                                    
                                    if matching_interviews:
                                        matching_interview_id = matching_interviews[0].id
                                        logger.info(f"Found matching interview {matching_interview_id} by transcript_id={interview_id}")
                                    else:
                                        # Try by session_id
                                        q = interviews_ref.where("session_id", "==", interview_id).limit(1)
                                        matching_interviews = list(q.stream())
                                        
                                        if matching_interviews:
                                            matching_interview_id = matching_interviews[0].id
                                            logger.info(f"Found matching interview {matching_interview_id} by session_id={interview_id}")
                                except Exception as search_error:
                                    logger.warning(f"Error searching for matching interview: {str(search_error)}")
                            
                            if matching_interview_id:
                                # Use the matching interview
                                interview_ref = firebase_service.db.collection("users").document(effective_user_id) \
                                                .collection("roles").document(role_id) \
                                                .collection("interviews").document(matching_interview_id)
                                interview_doc = interview_ref.get()
                                logger.info(f"Using matching interview document {matching_interview_id}")
                            else:
                                # Create the interview document
                                logger.info(f"Interview document {interview_id} doesn't exist, creating it")
                                interview_data = {
                                    "id": interview_id,
                                    "role_id": role_id,
                                    "status": "completed",
                                    "createdAt": datetime.now().isoformat(),
                                    "updatedAt": datetime.now().isoformat(),
                                    "transcript_id": interview_id,  # Use the same ID for transcript
                                    "session_id": interview_id      # Also set session_id for querying
                                }
                                
                                # Set the interview document
                                interview_ref.set(interview_data)
                                interview_created = True
                                logger.info(f"Created new interview document {interview_id}")
                        
                        # Store the evaluation in the interviews/{interview_id}/evaluations collection
                        eval_ref = interview_ref.collection("evaluations").document()
                        eval_ref.set(evaluation_result)
                        evaluation_id = eval_ref.id
                        
                        # Update the interview document with evaluation status
                        update_data = {
                            "has_evaluation": True,
                            "evaluation_id": evaluation_id,
                            "evaluation_summary": evaluation_result["evaluation_summary"],
                            "updated_at": datetime.now().isoformat()
                        }
                        
                        # Only add these fields if we created the document (don't overwrite existing data)
                        if interview_created:
                            if application_id:
                                update_data["application_id"] = application_id
                            if not is_authenticated:
                                update_data["is_public"] = True
                        
                        interview_ref.update(update_data)
                        
                        logger.info(f"Successfully stored evaluation with ID: {evaluation_id} in user's collection")
                    
                    # For public interviews, ALSO update the public collections for backward compatibility
                    # but ONLY if we don't have an owner or if the evaluation_id isn't set yet
                    if not is_authenticated:
                        if not evaluation_id:
                            # Only create in public_evaluations if we haven't stored it in a user's collection
                            public_eval_ref = firebase_service.db.collection("evaluations").document()
                            public_eval_doc = evaluation_result.copy()
                            public_eval_ref.set(public_eval_doc)
                            evaluation_id = public_eval_ref.id
                            logger.info(f"Stored evaluation in public collection with ID: {evaluation_id}")
                        
                        # Always update the public interview session with the evaluation ID
                        try:
                            session_ref = firebase_service.db.collection("public_interview_sessions").document(interview_id)
                            session_doc = session_ref.get()
                            
                            if session_doc.exists:
                                session_ref.update({
                                    "has_evaluation": True,
                                    "evaluation_id": evaluation_id,
                                    "evaluation_summary": evaluation_result["evaluation_summary"],
                                    "updated_at": datetime.now().isoformat()
                                })
                                logger.info(f"Updated public session {interview_id} with evaluation ID {evaluation_id}")
                        except Exception as session_error:
                            logger.warning(f"Error updating public session: {str(session_error)}")
                        
                        # Update application if needed
                        if application_id:
                            try:
                                app_ref = firebase_service.db.collection("applications").document(application_id)
                                app_doc = app_ref.get()
                                
                                if app_doc.exists:
                                    app_ref.update({
                                        "status": "evaluated",
                                        "evaluation_id": evaluation_id,
                                        "evaluation_summary": evaluation_result["evaluation_summary"],
                                        "updated_at": datetime.now().isoformat()
                                    })
                                    logger.info(f"Updated application {application_id} with evaluation ID {evaluation_id}")
                            except Exception as app_error:
                                logger.warning(f"Error updating application: {str(app_error)}")
                    
                    # Add the evaluation ID to the result
                    if evaluation_id:
                        evaluation_result["evaluation_id"] = evaluation_id
                    
                except Exception as e:
                    logger.error(f"Error storing evaluation results: {str(e)}", exc_info=True)
                    # Don't fail the entire operation if storage fails

                # Return the evaluation results with success status
                return {
                    "status": "success",
                    "evaluation": evaluation_result
                }

            except json.JSONDecodeError as e:
                logger.error(f"Error parsing JSON from response: {str(e)}")
                logger.error(f"Response content: {content[:500]}...")
                return {"status": "error", "message": f"Invalid JSON in evaluation response: {str(e)}"}
            except Exception as e:
                logger.error(f"Error processing evaluation response: {str(e)}")
                return {"status": "error", "message": f"Error processing evaluation: {str(e)}"}

        except Exception as e:
            logger.error(f"Unexpected error in evaluate_interview: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Unexpected error: {str(e)}"}

    @staticmethod
    async def evaluate_interview_authenticated(
        interview_id: str,
        role_id: str,
        application_id: str = None,
        user_id: str = None,
        template_id: str = None,
        stage_name: str = None,
        stage_index: int = None
    ) -> Dict[str, Any]:
        """
        Evaluate an interview in an authenticated context.

        Args:
            interview_id: ID of the interview transcript
            role_id: ID of the role
            application_id: Optional ID of the application
            user_id: ID of the authenticated user

        Returns:
            Dictionary containing evaluation results
        """
        if not user_id:
            logger.error("User ID is required for authenticated evaluation")
            return {"status": "error", "message": "User ID is required for authenticated evaluation"}

        return await InterviewEvaluationService.evaluate_interview(
            interview_id, role_id, application_id, True, user_id, template_id, stage_name, stage_index
        )

    @staticmethod
    async def evaluate_interview_public(
        interview_id: str,
        role_id: str,
        application_id: str = None,
        template_id: str = None,
        stage_name: str = None,
        stage_index: int = None,
        owner_user_id: str = None,
        template_data: Dict[str, Any] = None,
        resume_text: str = None
    ) -> Dict[str, Any]:
        """
        Evaluate an interview in a public/unauthenticated context.

        Args:
            interview_id: ID of the interview transcript
            role_id: ID of the role
            application_id: Optional ID of the application
            template_id: Optional ID of the interview template
            stage_name: Optional name of the interview stage
            stage_index: Optional index of the interview stage
            owner_user_id: Optional ID of the user who owns the role
            template_data: Optional template data to use for evaluation
            resume_text: Optional resume text to use directly for evaluation

        Returns:
            Dictionary containing evaluation results
        """
        return await InterviewEvaluationService.evaluate_interview(
            interview_id, role_id, application_id, False, owner_user_id, template_id, stage_name, stage_index, template_data, resume_text
        )

    @staticmethod
    async def auto_evaluate_public_interview(
        session_id: str,
        explicit_role_id: str = None,
        explicit_application_id: str = None,
        explicit_template_id: str = None,
        explicit_stage_name: str = None,
        explicit_stage_index: int = None,
        explicit_template_data: Dict[str, Any] = None,
        explicit_resume_text: str = None
    ) -> Dict[str, Any]:
        """
        Automatically evaluate a public interview at the end of a session.
        This method is designed to be called as a hook at the end of public interview sessions.
        It identifies the role owner to ensure the evaluation is stored in the correct user's collection.

        Args:
            session_id: ID of the public interview session
            explicit_role_id: Optional role ID to use if not found in session data
            explicit_application_id: Optional application ID to use if not found in session data
            explicit_template_id: Optional template ID to use if not found in session data
            explicit_stage_name: Optional stage name to use if not found in session data
            explicit_stage_index: Optional stage index to use if not found in session data
            explicit_template_data: Optional template data to use for evaluation

        Returns:
            Dictionary containing evaluation results or error information
        """
        try:
            logger.info(f"Auto-evaluating public interview session {session_id}")

            # Get the session data
            firebase_service = FirebaseService()
            session_ref = firebase_service.db.collection("public_interview_sessions").document(session_id)
            session_doc = session_ref.get()

            if not session_doc.exists:
                logger.error(f"Public interview session {session_id} not found")
                return {"status": "error", "message": f"Interview session {session_id} not found"}

            session_data = session_doc.to_dict()

            # Extract required fields
            role_id = session_data.get("role_id")
            application_id = session_data.get("application_id")
            template_id = session_data.get("template_id")
            stage_name = session_data.get("stage_name") or session_data.get("stage")
            stage_index = session_data.get("stage_index") or session_data.get("stageIndex")
            
            # Use explicit parameters if provided and needed
            if not role_id and explicit_role_id:
                logger.info(f"Using explicit role_id {explicit_role_id} for session {session_id}")
                role_id = explicit_role_id
                
            if not application_id and explicit_application_id:
                logger.info(f"Using explicit application_id {explicit_application_id} for session {session_id}")
                application_id = explicit_application_id
                
            if not template_id and explicit_template_id:
                logger.info(f"Using explicit template_id {explicit_template_id} for session {session_id}")
                template_id = explicit_template_id
                
            if not stage_name and explicit_stage_name:
                logger.info(f"Using explicit stage_name {explicit_stage_name} for session {session_id}")
                stage_name = explicit_stage_name
                
            if stage_index is None and explicit_stage_index is not None:
                logger.info(f"Using explicit stage_index {explicit_stage_index} for session {session_id}")
                stage_index = explicit_stage_index
            
            if not role_id:
                logger.error(f"Role ID not found in session {session_id}")
                return {"status": "error", "message": "Role ID not found in session data"}
            
            # Find the user who owns this role
            # This is critical for ensuring the evaluation is stored in the correct user's collection
            owner_user_id = None
            existing_interview_id = None
            try:
                # Query the roles collection to find the owner user ID
                role_ref = firebase_service.db.collection("roles").document(role_id)
                role_doc = role_ref.get()
                
                if role_doc.exists:
                    role_data = role_doc.to_dict()
                    owner_user_id = role_data.get("user_id") or role_data.get("userId")
                    logger.info(f"Found role owner from roles collection: {owner_user_id}")
                
                # If not found in roles collection, search in user collections
                if not owner_user_id:
                    users_ref = firebase_service.db.collection("users")
                    users = list(users_ref.stream())
                    
                    for user_doc in users:
                        user_id = user_doc.id
                        user_role_ref = firebase_service.db.collection("users").document(user_id).collection("roles").document(role_id)
                        user_role_doc = user_role_ref.get()
                        
                        if user_role_doc.exists:
                            owner_user_id = user_id
                            logger.info(f"Found role owner by searching user collections: {owner_user_id}")
                            break
                
                if owner_user_id:
                    logger.info(f"Role {role_id} is owned by user {owner_user_id}")
                    
                    # Check if interview already exists in user's collection
                    # This helps avoid creating duplicate interviews
                    try:
                        interviews_ref = firebase_service.db.collection("users").document(owner_user_id) \
                                        .collection("roles").document(role_id) \
                                        .collection("interviews")
                        
                        # Try exact match with session_id first
                        interview_ref = interviews_ref.document(session_id)
                        interview_doc = interview_ref.get()
                        
                        if interview_doc.exists:
                            existing_interview_id = session_id
                            logger.info(f"Found existing interview with exact ID match: {existing_interview_id}")
                        else:
                            # Try to find by session_id field
                            session_query = interviews_ref.where("session_id", "==", session_id).limit(1)
                            session_matches = list(session_query.stream())
                            
                            if session_matches:
                                existing_interview_id = session_matches[0].id
                                logger.info(f"Found existing interview by session_id field: {existing_interview_id}")
                            else:
                                # Try to find by transcript_id field
                                transcript_query = interviews_ref.where("transcript_id", "==", session_id).limit(1)
                                transcript_matches = list(transcript_query.stream())
                                
                                if transcript_matches:
                                    existing_interview_id = transcript_matches[0].id
                                    logger.info(f"Found existing interview by transcript_id field: {existing_interview_id}")
                    except Exception as interview_error:
                        logger.warning(f"Error searching for existing interview: {str(interview_error)}")
                else:
                    logger.warning(f"Could not determine owner for role {role_id}, evaluation will be stored in public collection only")
                
            except Exception as owner_error:
                logger.warning(f"Error determining role owner: {str(owner_error)}")
                # Continue without owner - evaluation will be stored in public collection

            # Check if this interview has already been evaluated
            # This prevents duplicate evaluations
            if owner_user_id and existing_interview_id:
                try:
                    interview_ref = firebase_service.db.collection("users").document(owner_user_id) \
                                .collection("roles").document(role_id) \
                                .collection("interviews").document(existing_interview_id)
                    
                    interview_data = interview_ref.get().to_dict()
                    if interview_data and interview_data.get("has_evaluation"):
                        logger.info(f"Interview {existing_interview_id} already has an evaluation, skipping")
                        
                        # Return existing evaluation ID if available
                        if interview_data.get("evaluation_id"):
                            return {
                                "status": "success", 
                                "message": "Interview already evaluated",
                                "evaluation": {
                                    "evaluation_id": interview_data.get("evaluation_id")
                                }
                            }
                        else:
                            return {
                                "status": "success", 
                                "message": "Interview already evaluated"
                            }
                except Exception as check_error:
                    logger.warning(f"Error checking existing evaluation: {str(check_error)}")

            # Log all the parameters for debugging
            logger.info(f"Final parameters for evaluation: session_id={session_id}, role_id={role_id}, application_id={application_id}, owner_user_id={owner_user_id}, existing_interview_id={existing_interview_id}")
            
            # If we found an existing interview, use its ID instead of the session_id
            interview_id_to_use = existing_interview_id if existing_interview_id else session_id
            
            # If we have template data, use it directly
            template_data_to_use = explicit_template_data
            
            # If we don't have template data but the session contains it, extract it
            if not template_data_to_use and session_data.get("template"):
                template_data_to_use = session_data.get("template")
                logger.info("Using template data from session")
            
            # Call the public evaluation method with the owner user ID if found
            try:
                return await InterviewEvaluationService.evaluate_interview_public(
                    interview_id_to_use, role_id, application_id, template_id, stage_name, stage_index, owner_user_id, 
                    template_data_to_use, explicit_resume_text
                )
            except Exception as e:
                logger.error(f"Error in evaluate_interview_public: {str(e)}", exc_info=True)
                return {"status": "error", "message": f"Error evaluating interview: {str(e)}"}

        except Exception as e:
            logger.error(f"Error in auto_evaluate_public_interview: {str(e)}", exc_info=True)
            return {"status": "error", "message": f"Error auto-evaluating interview: {str(e)}"}
