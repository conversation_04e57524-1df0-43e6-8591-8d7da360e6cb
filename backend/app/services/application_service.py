"""Application service for handling application-related operations."""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from app.services.firebase_service import FirebaseService
from firebase_admin import firestore
from firebase_admin.firestore import SERVER_TIMESTAMP
from app.core.log_utils import log_data_summary

# Configure logger
logger = logging.getLogger(__name__)

class ApplicationService:
    """Service for handling application-related operations."""

    def __init__(self):
        self.firebase_service = FirebaseService()

    async def get_application_with_interviews(self, application_id: str) -> Optional[Dict[str, Any]]:
        """
        Get application data by ID with enhanced interview data fetching.

        This method extends the standard get_application method to ensure
        all interview data is properly fetched from both the application's
        interviews subcollection and the public_interview_sessions collection.

        Args:
            application_id: The ID of the application to retrieve

        Returns:
            The application data with complete interview information if found, otherwise None
        """
        try:
            # First get the application using the standard method
            application_data = await self.firebase_service.get_application(application_id)

            if not application_data:
                logger.warning(f"Application {application_id} not found")
                return None

            # Get the role ID from the application
            role_id = application_data.get("roleId")
            if not role_id:
                logger.warning(f"Application {application_id} has no roleId")
                return application_data

            # Get the candidate ID from the application
            candidate_id = application_data.get("candidateId") or application_data.get("candidate_id")
            if not candidate_id:
                logger.warning(f"Application {application_id} has no candidateId")
                return application_data

            # Initialize interviews list if not present
            if "interviews" not in application_data:
                application_data["interviews"] = []

            # Get interviews from the application's interviews subcollection
            # (This is already done in the standard get_application method)

            # Now also check for interviews in the public_interview_sessions collection
            # that match this application ID
            try:
                # Query public_interview_sessions for this application
                public_sessions_ref = self.firebase_service.db.collection("public_interview_sessions")
                public_sessions_query = public_sessions_ref.where("application_id", "==", application_id).stream()

                # Process each matching session
                for session_doc in public_sessions_query:
                    session_data = session_doc.to_dict()
                    session_data["id"] = session_doc.id

                    # Check if this session is already in the interviews list
                    session_id = session_data.get("session_id")
                    if session_id:
                        # Skip if already in the list
                        if any(interview.get("session_id") == session_id for interview in application_data["interviews"]):
                            continue

                    # Create an interview object from the session data
                    interview_data = {
                        "id": session_data.get("transcript_id") or session_data.get("id"),
                        "session_id": session_data.get("session_id"),
                        "transcript_id": session_data.get("transcript_id"),
                        "role_id": session_data.get("role_id"),
                        "application_id": application_id,
                        "candidate_id": candidate_id,
                        "template_id": session_data.get("template_id"),
                        "stage_index": session_data.get("stage_index") or session_data.get("stageIndex"),
                        "stage_name": session_data.get("stage_name") or session_data.get("stageName"),
                        "status": session_data.get("status") or "completed",
                        "created_at": session_data.get("created_at") or session_data.get("createdAt"),
                        "updated_at": session_data.get("updated_at") or session_data.get("updatedAt"),
                        # Include the transcript data directly
                        "transcript": session_data.get("transcript") or {
                            "messages": session_data.get("messages"),
                            "message_count": session_data.get("message_count")
                        }
                    }

                    # Add to the interviews list
                    application_data["interviews"].append(interview_data)

                logger.info(f"Added public interview sessions for application {application_id}")

            except Exception as e:
                logger.error(f"Error fetching public interview sessions for application {application_id}: {str(e)}")
                # Don't fail the whole request if this part fails

            # Also check for interviews directly in the public_interview_sessions collection
            # that match this candidate ID and role ID
            try:
                # Query public_interview_sessions for this candidate and role
                public_sessions_ref = self.firebase_service.db.collection("public_interview_sessions")
                public_sessions_query = public_sessions_ref.where("candidate_id", "==", candidate_id).where("role_id", "==", role_id).stream()

                # Process each matching session
                for session_doc in public_sessions_query:
                    session_data = session_doc.to_dict()
                    session_data["id"] = session_doc.id

                    # Check if this session is already in the interviews list
                    session_id = session_data.get("session_id")
                    if session_id:
                        # Skip if already in the list
                        if any(interview.get("session_id") == session_id for interview in application_data["interviews"]):
                            continue

                    # Create an interview object from the session data
                    interview_data = {
                        "id": session_data.get("transcript_id") or session_data.get("id"),
                        "session_id": session_data.get("session_id"),
                        "transcript_id": session_data.get("transcript_id"),
                        "role_id": session_data.get("role_id"),
                        "application_id": application_id,
                        "candidate_id": candidate_id,
                        "template_id": session_data.get("template_id"),
                        "stage_index": session_data.get("stage_index") or session_data.get("stageIndex"),
                        "stage_name": session_data.get("stage_name") or session_data.get("stageName"),
                        "status": session_data.get("status") or "completed",
                        "created_at": session_data.get("created_at") or session_data.get("createdAt"),
                        "updated_at": session_data.get("updated_at") or session_data.get("updatedAt"),
                        # Include the transcript data directly
                        "transcript": session_data.get("transcript") or {
                            "messages": session_data.get("messages"),
                            "message_count": session_data.get("message_count")
                        }
                    }

                    # Add to the interviews list
                    application_data["interviews"].append(interview_data)

                logger.info(f"Added candidate-role public interview sessions for application {application_id}")

            except Exception as e:
                logger.error(f"Error fetching candidate-role public interview sessions for application {application_id}: {str(e)}")
                # Don't fail the whole request if this part fails

            # Also check for interviews in the applications/{application_id}/interviews collection
            # that might not have been fetched by the standard method
            try:
                # Check if we already have interviews from the standard method
                if not application_data.get("interviews"):
                    # Query the interviews subcollection
                    interviews_ref = self.firebase_service.db.collection("applications").document(application_id).collection("interviews")
                    interviews_docs = interviews_ref.stream()

                    for interview_doc in interviews_docs:
                        interview_data = interview_doc.to_dict()
                        interview_data["id"] = interview_doc.id

                        # If this interview has a transcript ID, try to get the transcript
                        transcript_id = interview_data.get("transcript_id")
                        if transcript_id:
                            try:
                                # Check in public_interview_sessions first
                                transcript_ref = self.firebase_service.db.collection("public_interview_sessions").document(transcript_id)
                                transcript_doc = transcript_ref.get()

                                if transcript_doc.exists:
                                    transcript_data = transcript_doc.to_dict()
                                    interview_data["transcript"] = transcript_data
                            except Exception as transcript_error:
                                logger.error(f"Error fetching transcript {transcript_id}: {str(transcript_error)}")

                        # Add to the interviews list
                        if "interviews" not in application_data:
                            application_data["interviews"] = []
                        application_data["interviews"].append(interview_data)

            except Exception as e:
                logger.error(f"Error fetching interviews from subcollection for application {application_id}: {str(e)}")
                # Don't fail the whole request if this part fails

            # Check for evaluations in the user's role collections for each interview
            try:
                # Get the user ID from the role data or application data
                user_id = None

                # Try to get user_id from the role data
                if application_data.get("role") and application_data["role"].get("user_id"):
                    user_id = application_data["role"]["user_id"]
                    logger.info(f"Found user_id {user_id} from role data")
                # Try to get user_id from the hiringManagerId field
                elif application_data.get("role") and application_data["role"].get("hiringManagerId"):
                    user_id = application_data["role"]["hiringManagerId"]
                    logger.info(f"Found user_id {user_id} from hiringManagerId")
                # Try to get user_id directly from the application
                elif application_data.get("user_id"):
                    user_id = application_data["user_id"]
                    logger.info(f"Found user_id {user_id} from application data")

                if user_id and role_id and application_data.get("interviews"):
                    logger.info(f"Checking for evaluations in user {user_id}'s role {role_id} collections for {len(application_data['interviews'])} interviews")

                    # First, get all evaluations for this role to avoid multiple queries
                    try:
                        all_evals_ref = self.firebase_service.db.collection("users").document(user_id) \
                                        .collection("roles").document(role_id) \
                                        .collection("evaluations")

                        all_evals = list(all_evals_ref.stream())
                        logger.info(f"Found {len(all_evals)} evaluations in user {user_id}'s role {role_id} collection")

                        # Create dictionaries for different ways to match evaluations
                        evals_by_id = {}  # Match by id field (which contains session_id)
                        evals_by_transcript_id = {}  # Match by transcript_id field
                        evals_by_interview_id = {}  # Match by interview_id field

                        # Process evaluation documents without excessive logging
                        for eval_doc in all_evals:
                            eval_data = eval_doc.to_dict()
                            eval_id = eval_doc.id

                            # Index by different fields for matching
                            if "id" in eval_data:
                                evals_by_id[eval_data["id"]] = {"id": eval_id, "data": eval_data}
                            if "transcript_id" in eval_data:
                                evals_by_transcript_id[eval_data["transcript_id"]] = {"id": eval_id, "data": eval_data}
                            if "interview_id" in eval_data:
                                evals_by_interview_id[eval_data["interview_id"]] = {"id": eval_id, "data": eval_data}

                        # Log only the count of keys we have for matching
                        logger.info(f"Found {len(evals_by_id)} evaluation ID keys, {len(evals_by_transcript_id)} transcript ID keys, and {len(evals_by_interview_id)} interview ID keys")

                        # Now also check in the interviews/{interview_id}/evaluations path
                        logger.info(f"Checking for evaluations in interviews subcollection")

                        # Get all interviews for this role
                        interviews_ref = self.firebase_service.db.collection("users").document(user_id) \
                                        .collection("roles").document(role_id) \
                                        .collection("interviews")

                        interviews_docs = list(interviews_ref.stream())
                        logger.info(f"Found {len(interviews_docs)} interviews in user {user_id}'s role {role_id} collection")

                        # Check each interview for evaluations
                        for interview_doc in interviews_docs:
                            interview_id = interview_doc.id
                            logger.info(f"Checking for evaluations in interview {interview_id}")

                            # Get evaluations for this interview
                            interview_evals_ref = self.firebase_service.db.collection("users").document(user_id) \
                                                .collection("roles").document(role_id) \
                                                .collection("interviews").document(interview_id) \
                                                .collection("evaluations")

                            interview_evals = list(interview_evals_ref.stream())
                            logger.info(f"Found {len(interview_evals)} evaluations in interview {interview_id}")

                            # Add each evaluation to our dictionaries
                            for eval_doc in interview_evals:
                                eval_data = eval_doc.to_dict()
                                eval_id = eval_doc.id
                                # Only log the evaluation ID, not the data
                                logger.info(f"Found interview evaluation {eval_id}")

                                # Store the interview ID and evaluation ID mapping
                                evals_by_id[interview_id] = {"id": eval_id, "data": eval_data, "interview_id": interview_id}

                                # Also store by transcript_id if available
                                if "transcript_id" in eval_data:
                                    evals_by_transcript_id[eval_data["transcript_id"]] = {"id": eval_id, "data": eval_data, "interview_id": interview_id}

                                # Also store by session_id if that's in the id field
                                if "id" in eval_data:
                                    evals_by_id[eval_data["id"]] = {"id": eval_id, "data": eval_data, "interview_id": interview_id}

                        # Log only the updated count of keys
                        logger.info(f"Updated counts: {len(evals_by_id)} evaluation ID keys, {len(evals_by_transcript_id)} transcript ID keys")

                    except Exception as e:
                        logger.error(f"Error fetching evaluations: {str(e)}")
                        all_evals = []
                        evals_by_id = {}
                        evals_by_transcript_id = {}
                        evals_by_interview_id = {}

                    for interview in application_data["interviews"]:
                        interview_id = interview.get("id")
                        if interview_id and not interview.get("evaluation_id") and not interview.get("evaluationId"):
                            # Log minimal interview info
                            logger.info(f"Processing interview: {interview_id}")

                            evaluation_id = None

                            # Try to match by different fields
                            # 1. Try to match by session_id (stored in id field of evaluation)
                            session_id = interview.get("session_id")
                            if session_id and session_id in evals_by_id:
                                evaluation_id = evals_by_id[session_id]["id"]
                                logger.info(f"Found evaluation {evaluation_id} by matching session_id {session_id} with id field")
                            
                            # 1.1 Also try matching by id directly
                            if not evaluation_id and interview_id in evals_by_id:
                                evaluation_id = evals_by_id[interview_id]["id"]
                                logger.info(f"Found evaluation {evaluation_id} by matching interview_id {interview_id} with id field")

                            # 2. Try to match by transcript_id
                            transcript_id = interview.get("transcript_id")
                            if not evaluation_id and transcript_id and transcript_id in evals_by_transcript_id:
                                evaluation_id = evals_by_transcript_id[transcript_id]["id"]
                                logger.info(f"Found evaluation {evaluation_id} by matching transcript_id {transcript_id}")

                            # 3. Try to match by interview_id
                            if not evaluation_id and interview_id in evals_by_interview_id:
                                evaluation_id = evals_by_interview_id[interview_id]["id"]
                                logger.info(f"Found evaluation {evaluation_id} by matching interview_id {interview_id}")

                            # 4. If we still don't have a match, try direct queries
                            if not evaluation_id:
                                try:
                                    # Try by id field (session_id)
                                    if session_id:
                                        evals_ref = self.firebase_service.db.collection("users").document(user_id) \
                                                    .collection("roles").document(role_id) \
                                                    .collection("evaluations").where("id", "==", session_id).limit(1)

                                        evals = list(evals_ref.stream())
                                        if evals:
                                            eval_doc = evals[0]
                                            evaluation_id = eval_doc.id
                                            logger.info(f"Found evaluation {evaluation_id} by querying for id={session_id}")

                                    # Try by transcript_id
                                    if not evaluation_id and transcript_id:
                                        evals_ref = self.firebase_service.db.collection("users").document(user_id) \
                                                    .collection("roles").document(role_id) \
                                                    .collection("evaluations").where("transcript_id", "==", transcript_id).limit(1)

                                        evals = list(evals_ref.stream())
                                        if evals:
                                            eval_doc = evals[0]
                                            evaluation_id = eval_doc.id
                                            logger.info(f"Found evaluation {evaluation_id} by querying for transcript_id={transcript_id}")

                                    # Try searching in public_evaluations collection
                                    if not evaluation_id and (session_id or transcript_id):
                                        try:
                                            # Look in public_evaluations by session_id first
                                            if session_id:
                                                public_eval_ref = self.firebase_service.db.collection("public_evaluations")
                                                public_eval_query = public_eval_ref.where("interview_id", "==", session_id).limit(1)
                                                public_evals = list(public_eval_query.stream())
                                                
                                                if public_evals:
                                                    public_eval = public_evals[0]
                                                    evaluation_id = public_eval.id
                                                    logger.info(f"Found evaluation {evaluation_id} in public_evaluations by session_id {session_id}")
                                            
                                            # If not found by session_id, try by transcript_id
                                            if not evaluation_id and transcript_id:
                                                public_eval_ref = self.firebase_service.db.collection("public_evaluations")
                                                public_eval_query = public_eval_ref.where("interview_id", "==", transcript_id).limit(1)
                                                public_evals = list(public_eval_query.stream())
                                                
                                                if public_evals:
                                                    public_eval = public_evals[0]
                                                    evaluation_id = public_eval.id
                                                    logger.info(f"Found evaluation {evaluation_id} in public_evaluations by transcript_id {transcript_id}")
                                                    
                                            # If not found by either, try searching by role_id and application_id
                                            if not evaluation_id and application_id:
                                                public_eval_ref = self.firebase_service.db.collection("public_evaluations")
                                                public_eval_query = public_eval_ref.where("role_id", "==", role_id).where("application_id", "==", application_id).limit(1)
                                                public_evals = list(public_eval_query.stream())
                                                
                                                if public_evals:
                                                    public_eval = public_evals[0]
                                                    evaluation_id = public_eval.id
                                                    logger.info(f"Found evaluation {evaluation_id} in public_evaluations by role_id and application_id")
                                        except Exception as e:
                                            logger.warning(f"Error searching public_evaluations: {str(e)}")
                                    
                                    # If we still don't have a match and there's only one evaluation, use it
                                    if not evaluation_id and len(all_evals) == 1:
                                        evaluation_id = all_evals[0].id
                                        logger.info(f"Using the only available evaluation {evaluation_id} as fallback")
                                except Exception as e:
                                    logger.error(f"Error querying for evaluation: {str(e)}")

                            # If we found an evaluation, update the interview
                            if evaluation_id:
                                # Check if we need to include the interview_id in the path
                                interview_path = ""
                                eval_data = None

                                if "interview_id" in evals_by_id.get(session_id, {}) or "interview_id" in evals_by_transcript_id.get(transcript_id, {}):
                                    # This is an evaluation in the interviews/{interview_id}/evaluations subcollection
                                    # We need to include the interview_id in the path when accessing the evaluation
                                    if session_id and session_id in evals_by_id and "interview_id" in evals_by_id[session_id]:
                                        interview_path = evals_by_id[session_id]["interview_id"]
                                        eval_data = evals_by_id[session_id]["data"]
                                    elif transcript_id and transcript_id in evals_by_transcript_id and "interview_id" in evals_by_transcript_id[transcript_id]:
                                        interview_path = evals_by_transcript_id[transcript_id]["interview_id"]
                                        eval_data = evals_by_transcript_id[transcript_id]["data"]

                                    logger.info(f"Evaluation is in interviews/{interview_path}/evaluations subcollection")

                                # Update the interview with the evaluation ID and path
                                interview["evaluation_id"] = evaluation_id
                                interview["evaluationId"] = evaluation_id  # Add both for compatibility
                                if interview_path:
                                    interview["evaluation_interview_path"] = interview_path

                                # Now fetch the evaluation data to get the decision and score
                                try:
                                    # If we already have the evaluation data from our lookup, use it
                                    if not eval_data:
                                        # Otherwise, fetch the evaluation data
                                        if interview_path:
                                            # Evaluation is in the interviews/{interview_id}/evaluations subcollection
                                            eval_ref = self.firebase_service.db.collection("users").document(user_id) \
                                                        .collection("roles").document(role_id) \
                                                        .collection("interviews").document(interview_path) \
                                                        .collection("evaluations").document(evaluation_id)
                                        else:
                                            # Evaluation is in the roles/{role_id}/evaluations subcollection
                                            eval_ref = self.firebase_service.db.collection("users").document(user_id) \
                                                        .collection("roles").document(role_id) \
                                                        .collection("evaluations").document(evaluation_id)

                                        eval_doc = eval_ref.get()
                                        if eval_doc.exists:
                                            eval_data = eval_doc.to_dict()
                                            logger.info(f"Fetched evaluation data for {evaluation_id}")

                                    # Extract decision and score from evaluation data
                                    if eval_data:
                                        # Extract decision
                                        decision = None
                                        if "decision" in eval_data:
                                            decision = eval_data["decision"]
                                        elif "evaluation_data" in eval_data and "decision" in eval_data["evaluation_data"]:
                                            decision = eval_data["evaluation_data"]["decision"]
                                        elif "full_evaluation" in eval_data and "decision" in eval_data["full_evaluation"]:
                                            decision = eval_data["full_evaluation"]["decision"]
                                        elif "evaluation_summary" in eval_data and "decision" in eval_data["evaluation_summary"]:
                                            decision = eval_data["evaluation_summary"]["decision"]

                                        # Extract score
                                        score = None
                                        if "overall_score" in eval_data:
                                            score = eval_data["overall_score"]
                                        elif "overallScore" in eval_data:
                                            score = eval_data["overallScore"]
                                        elif "evaluation_data" in eval_data and "overall_score" in eval_data["evaluation_data"]:
                                            score = eval_data["evaluation_data"]["overall_score"]
                                        elif "evaluation_data" in eval_data and "overallScore" in eval_data["evaluation_data"]:
                                            score = eval_data["evaluation_data"]["overallScore"]
                                        elif "full_evaluation" in eval_data and "overall_score" in eval_data["full_evaluation"]:
                                            score = eval_data["full_evaluation"]["overall_score"]
                                        elif "full_evaluation" in eval_data and "overallScore" in eval_data["full_evaluation"]:
                                            score = eval_data["full_evaluation"]["overallScore"]
                                        elif "evaluation_summary" in eval_data and "overall_score" in eval_data["evaluation_summary"]:
                                            score = eval_data["evaluation_summary"]["overall_score"]

                                        # Add decision and score to interview
                                        if decision:
                                            interview["decision"] = decision
                                            logger.info(f"Added decision {decision} to interview {interview_id}")

                                        if score is not None:
                                            interview["overall_score"] = score
                                            interview["overallScore"] = score  # Add both for compatibility
                                            logger.info(f"Added score {score} to interview {interview_id}")

                                        # Also add the evaluation data to the interview
                                        interview["evaluation"] = {
                                            "id": evaluation_id,
                                            "decision": decision,
                                            "overall_score": score,
                                            "overallScore": score
                                        }
                                except Exception as eval_error:
                                    logger.error(f"Error fetching evaluation data: {str(eval_error)}")
                                    # Continue even if evaluation data fetch fails

                                # Also update the interview document in Firestore if it exists in the applications collection
                                try:
                                    interview_ref = self.firebase_service.db.collection("applications").document(application_id) \
                                                    .collection("interviews").document(interview_id)

                                    interview_doc = interview_ref.get()
                                    if interview_doc.exists:
                                        update_data = {
                                            "evaluation_id": evaluation_id,
                                            "evaluationId": evaluation_id,
                                            "updated_at": SERVER_TIMESTAMP
                                        }

                                        if interview_path:
                                            update_data["evaluation_interview_path"] = interview_path

                                        # Add decision and score to update data if available
                                        if "decision" in interview:
                                            update_data["decision"] = interview["decision"]

                                        if "overall_score" in interview:
                                            update_data["overall_score"] = interview["overall_score"]
                                            update_data["overallScore"] = interview["overall_score"]

                                        interview_ref.update(update_data)
                                        logger.info(f"Updated interview document {interview_id} with evaluation ID {evaluation_id}")
                                except Exception as update_error:
                                    logger.error(f"Error updating interview document: {str(update_error)}")
                                    # Continue even if update fails
                            else:
                                # Try one last search in evaluations collection directly
                                try:
                                    # Search by interview_id in evaluations collection
                                    eval_ref = self.firebase_service.db.collection("evaluations")
                                
                                    # Try different fields that might contain the interview ID
                                    for field_name in ["interview_id", "session_id", "transcript_id"]:
                                        if not evaluation_id:
                                            for potential_id in [interview_id, session_id, transcript_id]:
                                                if potential_id:
                                                    try:
                                                        eval_query = eval_ref.where(field_name, "==", potential_id).limit(1)
                                                        evals = list(eval_query.stream())
                                                    
                                                        if evals:
                                                            eval_doc = evals[0]
                                                            evaluation_id = eval_doc.id
                                                            logger.info(f"Found evaluation {evaluation_id} in global evaluations collection by {field_name}={potential_id}")
                                                            break
                                                    except Exception as field_error:
                                                        logger.warning(f"Error searching evaluations by {field_name}: {str(field_error)}")
                                except Exception as e:
                                    logger.warning(f"Error in final evaluation search: {str(e)}")
                                
                                if not evaluation_id:
                                    logger.warning(f"No evaluation found for interview {interview_id} after all attempts")
                else:
                    if not user_id:
                        logger.warning(f"Could not determine user_id for application {application_id}")
                    if not role_id:
                        logger.warning(f"No role_id found for application {application_id}")
                    if not application_data.get("interviews"):
                        logger.warning(f"No interviews found for application {application_id}")
            except Exception as e:
                logger.error(f"Error checking for evaluations in user's role collections: {str(e)}")
                # Don't fail the whole request if this part fails

            return application_data

        except Exception as e:
            logger.error(f"Error in get_application_with_interviews for {application_id}: {str(e)}")
            return None

    async def get_applications_with_interviews(self, user_id: str, limit: int = None) -> List[Dict[str, Any]]:
        """
        Get all applications for a user with enhanced interview and evaluation data.

        This method fetches all applications for a user and enhances each one with
        complete interview and evaluation data.

        Args:
            user_id: The ID of the user
            limit: Optional limit on the number of applications to return

        Returns:
            A list of application data with complete interview and evaluation information
        """
        try:
            # First get all applications using the standard method
            applications = await self.firebase_service.get_applications_for_user(user_id, limit=limit)

            if not applications:
                logger.warning(f"No applications found for user {user_id}")
                return []

            # Process each application to add interview and evaluation data
            enhanced_applications = []
            for application in applications:
                application_id = application.get("id")
                if not application_id:
                    logger.warning(f"Application has no ID, skipping enhancement")
                    enhanced_applications.append(application)
                    continue

                # Get enhanced application data
                enhanced_application = await self.get_application_with_interviews(application_id)
                if enhanced_application:
                    # Also fetch resume evaluation data if not already present
                    logger.info(f"Checking for resume evaluation for application {application_id}")
                    if enhanced_application.get('evaluation'):
                        logger.debug(f"Application {application_id} already has evaluation data")

                    try:
                        # Get the role ID from the application
                        role_id = enhanced_application.get("roleId")
                        if not role_id:
                            # Try alternate field names
                            role_id = enhanced_application.get("role_id")

                        if role_id:
                            logger.info(f"Found role ID {role_id} for application {application_id}")
                            # Try to get the resume evaluation
                            evaluation = await self.firebase_service.get_resume_evaluation(user_id, role_id, application_id)
                            if evaluation:
                                enhanced_application["evaluation"] = evaluation
                                logger.info(f"Added resume evaluation to application {application_id} with decision={evaluation.get('decision')}, score={evaluation.get('overall_score')}")
                            else:
                                logger.info(f"No resume evaluation found for application {application_id}")

                                # Check if there's an evaluation directly in the application
                                if "evaluation" in enhanced_application and isinstance(enhanced_application["evaluation"], dict):
                                    logger.info(f"Using existing evaluation in application {application_id}")
                                else:
                                    # Create a placeholder evaluation if needed for UI display
                                    enhanced_application["evaluation"] = {
                                        "id": "unknown",
                                        "decision": None,
                                        "overall_score": None,
                                        "overallScore": None
                                    }
                        else:
                            logger.warning(f"No role ID found for application {application_id}")
                    except Exception as eval_error:
                        logger.error(f"Error fetching resume evaluation for application {application_id}: {str(eval_error)}")
                        # Continue even if evaluation fetch fails

                    enhanced_applications.append(enhanced_application)
                else:
                    # If enhancement failed, use the original application
                    enhanced_applications.append(application)

            return enhanced_applications

        except Exception as e:
            logger.error(f"Error in get_applications_with_interviews for user {user_id}: {str(e)}")
            return []
