# File: backend/app/services/resume_evaluator_service.py

from typing import Dict, Any, List, Optional, Union
import logging
import json
from datetime import datetime
from pydantic import BaseModel, Field, ValidationError

from app.services.openai.chat_completion_service import ChatCompletionService
from app.services.openai.prompts.manager import PromptManager

# Configure logging
logger = logging.getLogger(__name__)

# Pydantic models for validation
class SkillEvaluation(BaseModel):
    score: int = Field(..., ge=1, le=5)
    evaluation: str
    strengths: List[str]
    gaps: List[str]

class Scorecard(BaseModel):
    technicalSkills: SkillEvaluation
    softSkills: SkillEvaluation
    experienceRelevance: SkillEvaluation
    educationCertifications: SkillEvaluation
    achievementsImpact: SkillEvaluation
    overallScore: float = Field(..., ge=1, le=5)
    overallEvaluation: str

class Recommendation(BaseModel):
    decision: str
    confidence: str
    reasoning: str

class Feedback(BaseModel):
    candidateStrengths: List[str]
    improvementAreas: List[str]
    interviewFocus: List[str]

class ResumeEvaluation(BaseModel):
    scorecard: Scorecard
    recommendation: Recommendation
    feedback: Feedback

class ResumeEvaluatorService:
    """Service for evaluating resumes against job postings using AI"""

    _instance = None

    def __new__(cls):
        """Implement singleton pattern"""
        if cls._instance is None:
            cls._instance = super(ResumeEvaluatorService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the service"""
        self.chat_completion_service = ChatCompletionService()

        # Define the function schema for resume evaluation
        self.resume_evaluation_function = {
            "name": "evaluate_resume",
            "description": "Evaluate a resume against a job posting",
            "parameters": {
                "type": "object",
                "properties": {
                    "scorecard": {
                        "type": "object",
                        "properties": {
                            "technicalSkills": {
                                "type": "object",
                                "properties": {
                                    "score": {"type": "integer", "minimum": 1, "maximum": 5},
                                    "evaluation": {"type": "string"},
                                    "strengths": {"type": "array", "items": {"type": "string"}},
                                    "gaps": {"type": "array", "items": {"type": "string"}}
                                },
                                "required": ["score", "evaluation", "strengths", "gaps"]
                            },
                            "softSkills": {
                                "type": "object",
                                "properties": {
                                    "score": {"type": "integer", "minimum": 1, "maximum": 5},
                                    "evaluation": {"type": "string"},
                                    "strengths": {"type": "array", "items": {"type": "string"}},
                                    "gaps": {"type": "array", "items": {"type": "string"}}
                                },
                                "required": ["score", "evaluation", "strengths", "gaps"]
                            },
                            "experienceRelevance": {
                                "type": "object",
                                "properties": {
                                    "score": {"type": "integer", "minimum": 1, "maximum": 5},
                                    "evaluation": {"type": "string"},
                                    "strengths": {"type": "array", "items": {"type": "string"}},
                                    "gaps": {"type": "array", "items": {"type": "string"}}
                                },
                                "required": ["score", "evaluation", "strengths", "gaps"]
                            },
                            "educationCertifications": {
                                "type": "object",
                                "properties": {
                                    "score": {"type": "integer", "minimum": 1, "maximum": 5},
                                    "evaluation": {"type": "string"},
                                    "strengths": {"type": "array", "items": {"type": "string"}},
                                    "gaps": {"type": "array", "items": {"type": "string"}}
                                },
                                "required": ["score", "evaluation", "strengths", "gaps"]
                            },
                            "achievementsImpact": {
                                "type": "object",
                                "properties": {
                                    "score": {"type": "integer", "minimum": 1, "maximum": 5},
                                    "evaluation": {"type": "string"},
                                    "strengths": {"type": "array", "items": {"type": "string"}},
                                    "gaps": {"type": "array", "items": {"type": "string"}}
                                },
                                "required": ["score", "evaluation", "strengths", "gaps"]
                            },
                            "overallScore": {"type": "number", "minimum": 1, "maximum": 5},
                            "overallEvaluation": {"type": "string"}
                        },
                        "required": ["technicalSkills", "softSkills", "experienceRelevance",
                                    "educationCertifications", "achievementsImpact",
                                    "overallScore", "overallEvaluation"]
                    },
                    "recommendation": {
                        "type": "object",
                        "properties": {
                            "decision": {"type": "string", "enum": ["PASS", "FAIL"]},
                            "confidence": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH"]},
                            "reasoning": {"type": "string"}
                        },
                        "required": ["decision", "confidence", "reasoning"]
                    },
                    "feedback": {
                        "type": "object",
                        "properties": {
                            "candidateStrengths": {"type": "array", "items": {"type": "string"}},
                            "improvementAreas": {"type": "array", "items": {"type": "string"}},
                            "interviewFocus": {"type": "array", "items": {"type": "string"}}
                        },
                        "required": ["candidateStrengths", "improvementAreas", "interviewFocus"]
                    }
                },
                "required": ["scorecard", "recommendation", "feedback"]
            }
        }

    async def evaluate_resume(self, resume_text: str, job_posting: str, model: str = None) -> Dict[str, Any]:
        """
        Evaluate a resume against a job posting

        Args:
            resume_text: The text extracted from the resume
            job_posting: The job posting to evaluate against
            model: The OpenAI model to use

        Returns:
            Dictionary containing evaluation results including scorecard, recommendation, and feedback
        """
        try:
            # Prepare context for prompt template
            context = {
                "resume_text": resume_text,
                "job_posting": job_posting,
            }

            logger.info(f"Evaluating resume (length: {len(resume_text)}) against job posting (length: {len(job_posting)})")

            # Call OpenAI with full evaluation function schema
            full_function = {
                "name": "evaluate_resume",
                "description": "Comprehensive evaluation of a resume against a job posting",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "scorecard": {
                            "type": "object",
                            "properties": {
                                "technicalSkills": {
                                    "type": "object",
                                    "properties": {
                                        "score": {"type": "integer", "minimum": 1, "maximum": 5},
                                        "evaluation": {"type": "string"},
                                        "strengths": {"type": "array", "items": {"type": "string"}},
                                        "gaps": {"type": "array", "items": {"type": "string"}}
                                    }
                                },
                                "softSkills": {
                                    "type": "object",
                                    "properties": {
                                        "score": {"type": "integer", "minimum": 1, "maximum": 5},
                                        "evaluation": {"type": "string"},
                                        "strengths": {"type": "array", "items": {"type": "string"}},
                                        "gaps": {"type": "array", "items": {"type": "string"}}
                                    }
                                },
                                "experienceRelevance": {
                                    "type": "object",
                                    "properties": {
                                        "score": {"type": "integer", "minimum": 1, "maximum": 5},
                                        "evaluation": {"type": "string"},
                                        "strengths": {"type": "array", "items": {"type": "string"}},
                                        "gaps": {"type": "array", "items": {"type": "string"}}
                                    }
                                },
                                "educationCertifications": {
                                    "type": "object",
                                    "properties": {
                                        "score": {"type": "integer", "minimum": 1, "maximum": 5},
                                        "evaluation": {"type": "string"},
                                        "strengths": {"type": "array", "items": {"type": "string"}},
                                        "gaps": {"type": "array", "items": {"type": "string"}}
                                    }
                                },
                                "achievementsImpact": {
                                    "type": "object",
                                    "properties": {
                                        "score": {"type": "integer", "minimum": 1, "maximum": 5},
                                        "evaluation": {"type": "string"},
                                        "strengths": {"type": "array", "items": {"type": "string"}},
                                        "gaps": {"type": "array", "items": {"type": "string"}}
                                    }
                                },
                                "overallScore": {"type": "number", "minimum": 1, "maximum": 5},
                                "overallEvaluation": {"type": "string"},
                            }
                        },
                        "recommendation": {
                            "type": "object",
                            "properties": {
                                "decision": {"type": "string", "enum": ["STRONG_HIRE", "HIRE", "MAYBE", "PASS"]},
                                "confidence": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH"]},
                                "reasoning": {"type": "string"}
                            }
                        },
                        "feedback": {
                            "type": "object",
                            "properties": {
                                "candidateStrengths": {"type": "array", "items": {"type": "string"}},
                                "improvementAreas": {"type": "array", "items": {"type": "string"}},
                                "interviewFocus": {"type": "array", "items": {"type": "string"}}
                            }
                        }
                    }
                }
            }

            # Use the tools parameter format instead of functions for newer OpenAI API
            tools = [
                {
                    "type": "function",
                    "function": full_function
                }
            ]

            # Use the tool_choice parameter instead of function_call
            tool_choice = {
                "type": "function",
                "function": {
                    "name": "evaluate_resume"
                }
            }

            # Get the appropriate model for resume evaluation if not specified
            if model is None:
                from app.services.openai.config.model_configs import ModelConfigurationManager
                model = ModelConfigurationManager.get_appropriate_model("resume_evaluator_agent")

            logger.info(f"Calling OpenAI API with model={model}, tool={full_function['name']}")

            response = await self.chat_completion_service.generate_completion(
                prompt="Evaluate this resume against the job posting",
                tools=tools,  # Use tools instead of functions
                model=model,
                use_case="resume_evaluator_agent",
                context=context,
                temperature=0.1,
                tool_choice=tool_choice,  # Use tool_choice instead of function_call
                max_tokens=2000
            )

            # Log the response type for debugging
            logger.info(f"Response type: {type(response)}")

            # Extract the function call arguments from ChatCompletion object
            try:
                # Log more details about the response structure
                logger.info(f"Response has {len(response.choices) if hasattr(response, 'choices') else 0} choices")

                if hasattr(response, 'choices') and response.choices and len(response.choices) > 0:
                    choice = response.choices[0]

                    # Log message content if available
                    if hasattr(choice, 'message') and choice.message:
                        if hasattr(choice.message, 'content') and choice.message.content:
                            logger.info(f"Message content: {choice.message.content[:100]}...")

                        # Check for tool_calls instead of function_call
                        if hasattr(choice.message, 'tool_calls') and choice.message.tool_calls:
                            logger.info(f"Found {len(choice.message.tool_calls)} tool calls")
                            for tool_call in choice.message.tool_calls:
                                if tool_call.type == "function" and tool_call.function:
                                    logger.info(f"Processing tool call: {tool_call.function.name}")

                                    if tool_call.function.name == "evaluate_resume":
                                        # Parse the function arguments from JSON
                                        evaluation_data = json.loads(tool_call.function.arguments)

                                        # Use Pydantic for validation
                                        try:
                                            validated_data = ResumeEvaluation(**evaluation_data)
                                            logger.info(f"Resume evaluation completed successfully. Decision: {validated_data.recommendation.decision}")

                                            # Add metadata to the result
                                            result = evaluation_data.copy()
                                            result["metadata"] = {
                                                "timestamp": datetime.utcnow().isoformat(),
                                                "model": model,
                                                "resumeLength": len(resume_text),
                                                "jobPostingLength": len(job_posting)
                                            }

                                            return result
                                        except ValidationError as e:
                                            logger.error(f"Validation error in resume evaluation response: {str(e)}")
                                            raise ValueError(f"Invalid evaluation data structure: {str(e)}")

                        # For backward compatibility, also check for function_call
                        elif hasattr(choice.message, 'function_call') and choice.message.function_call:
                            function_call = choice.message.function_call

                            if function_call.name == "evaluate_resume":
                                # Parse the function arguments from JSON
                                evaluation_data = json.loads(function_call.arguments)

                                # Use Pydantic for validation
                                try:
                                    validated_data = ResumeEvaluation(**evaluation_data)
                                    logger.info(f"Resume evaluation completed successfully. Decision: {validated_data.recommendation.decision}")

                                    # Add metadata to the result
                                    result = evaluation_data.copy()
                                    result["metadata"] = {
                                        "timestamp": datetime.utcnow().isoformat(),
                                        "model": model,
                                        "resumeLength": len(resume_text),
                                        "jobPostingLength": len(job_posting)
                                    }

                                    return result
                                except ValidationError as e:
                                    logger.error(f"Validation error in resume evaluation response: {str(e)}")
                                    raise ValueError(f"Invalid evaluation data structure: {str(e)}")
                            else:
                                logger.error(f"Unexpected function call name: {function_call.name}")
                                raise ValueError(f"Unexpected function call: {function_call.name}")

                    # Neither tool_calls nor function_call found
                    logger.error("No tool_calls or function_call in response")
                    content = choice.message.content if (hasattr(choice, 'message') and hasattr(choice.message, 'content')) else "No content"
                    logger.error(f"Response content: {content}")

                    # Dump the full response structure for debugging
                    logger.error(f"Response structure: {dir(response)}")
                    logger.error(f"Choice structure: {dir(choice)}")
                    if hasattr(choice, 'message'):
                        logger.error(f"Message structure: {dir(choice.message)}")

                    raise ValueError("Failed to get evaluation function call from response")
                else:
                    logger.error("No choices in evaluation response")
                    raise ValueError("Failed to get choices from evaluation response")

            except AttributeError as ae:
                logger.error(f"Error accessing OpenAI response attributes: {str(ae)}")
                logger.error(f"Response type: {type(response)}")
                # Try to log the response data in a safer way
                try:
                    if hasattr(response, '__dict__'):
                        logger.error(f"Response dict: {response.__dict__}")
                    else:
                        logger.error(f"Response data: {str(response)}")
                except:
                    logger.error("Could not stringify response")

                raise ValueError(f"Failed to process OpenAI response: {str(ae)}")

        except Exception as e:
            logger.error(f"Error evaluating resume: {str(e)}")
            raise ValueError(f"Resume evaluation failed: {str(e)}")

    async def evaluate_resume_basic(self, resume_text: str, job_posting: str, model: str = None) -> Dict[str, Any]:
        """
        A simpler version of resume evaluation for quicker processing

        Args:
            resume_text: The text extracted from the resume
            job_posting: The job posting to evaluate against
            model: The OpenAI model to use

        Returns:
            Dictionary containing simplified evaluation results
        """
        try:
            # Prepare context for prompt template
            context = {
                "resume_text": resume_text,
                "job_posting": job_posting,
            }

            logger.info(f"Basic evaluation of resume (length: {len(resume_text)}) against job posting")

            # Call OpenAI with a simplified function schema for quicker processing
            simplified_function = {
                "name": "evaluate_resume_basic",
                "description": "Basic evaluation of a resume against a job posting",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "overallScore": {"type": "number", "minimum": 1, "maximum": 5},
                        "decision": {"type": "string", "enum": ["PASS", "FAIL"]},
                        "confidence": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH"]},
                        "reasoning": {"type": "string"},
                        "keyStrengths": {"type": "array", "items": {"type": "string"}},
                        "keyGaps": {"type": "array", "items": {"type": "string"}}
                    },
                    "required": ["overallScore", "decision", "confidence", "reasoning", "keyStrengths", "keyGaps"]
                }
            }

            # Use the tools parameter format instead of functions for newer OpenAI API
            tools = [
                {
                    "type": "function",
                    "function": simplified_function
                }
            ]

            # Use the tool_choice parameter instead of function_call
            tool_choice = {
                "type": "function",
                "function": {
                    "name": "evaluate_resume_basic"
                }
            }

            # Get the appropriate model for resume evaluation if not specified
            if model is None:
                from app.services.openai.config.model_configs import ModelConfigurationManager
                model = ModelConfigurationManager.get_appropriate_model("resume_evaluator_agent")

            logger.info(f"Calling OpenAI API with model={model}, tool={simplified_function['name']}")

            response = await self.chat_completion_service.generate_completion(
                prompt="Quickly evaluate this resume against the job posting",
                tools=tools,  # Use tools instead of functions
                model=model,
                use_case="resume_evaluator_agent",
                context=context,
                temperature=0.3,
                tool_choice=tool_choice,  # Use tool_choice instead of function_call
                max_tokens=1500,
            )

            # Log the response type for debugging
            logger.info(f"Response type: {type(response)}")

            # Extract the function call arguments from ChatCompletion object
            # The response is now a ChatCompletion object, not a dictionary
            try:
                # Log more details about the response structure
                logger.info(f"Response has {len(response.choices) if hasattr(response, 'choices') else 0} choices")

                if hasattr(response, 'choices') and response.choices and len(response.choices) > 0:
                    choice = response.choices[0]

                    # Log message content if available
                    if hasattr(choice, 'message') and choice.message:
                        if hasattr(choice.message, 'content') and choice.message.content:
                            logger.info(f"Message content: {choice.message.content[:100]}...")

                        # Check for tool_calls instead of function_call
                        if hasattr(choice.message, 'tool_calls') and choice.message.tool_calls:
                            logger.info(f"Found {len(choice.message.tool_calls)} tool calls")
                            for tool_call in choice.message.tool_calls:
                                if tool_call.type == "function" and tool_call.function:
                                    logger.info(f"Processing tool call: {tool_call.function.name}")

                                    if tool_call.function.name == "evaluate_resume_basic":
                                        # Parse the function arguments from JSON
                                        evaluation_data = json.loads(tool_call.function.arguments)

                                        # Add metadata
                                        evaluation_data["metadata"] = {
                                            "timestamp": datetime.utcnow().isoformat(),
                                            "model": model,
                                            "resumeLength": len(resume_text),
                                            "jobPostingLength": len(job_posting),
                                            "evaluationType": "basic"
                                        }

                                        return evaluation_data

                        # For backward compatibility, also check for function_call
                        elif hasattr(choice.message, 'function_call') and choice.message.function_call:
                            function_call = choice.message.function_call

                            if function_call.name == "evaluate_resume_basic":
                                # Parse the function arguments from JSON
                                evaluation_data = json.loads(function_call.arguments)

                                # Add metadata
                                evaluation_data["metadata"] = {
                                    "timestamp": datetime.utcnow().isoformat(),
                                    "model": model,
                                    "resumeLength": len(resume_text),
                                    "jobPostingLength": len(job_posting),
                                    "evaluationType": "basic"
                                }

                                return evaluation_data
                            else:
                                logger.error(f"Unexpected function call name: {function_call.name}")
                                raise ValueError(f"Unexpected function call: {function_call.name}")

                    # Neither tool_calls nor function_call found
                    logger.error("No tool_calls or function_call in response")
                    content = choice.message.content if (hasattr(choice, 'message') and hasattr(choice.message, 'content')) else "No content"
                    logger.error(f"Response content: {content}")

                    # Dump the full response structure for debugging
                    logger.error(f"Response structure: {dir(response)}")
                    logger.error(f"Choice structure: {dir(choice)}")
                    if hasattr(choice, 'message'):
                        logger.error(f"Message structure: {dir(choice.message)}")

                    raise ValueError("Failed to get basic evaluation function call from response")
                else:
                    logger.error("No choices in basic evaluation response")
                    raise ValueError("Failed to get choices from basic evaluation response")

            except AttributeError as ae:
                logger.error(f"Error accessing OpenAI response attributes: {str(ae)}")
                logger.error(f"Response type: {type(response)}")
                # Try to log the response data in a safer way
                try:
                    if hasattr(response, '__dict__'):
                        logger.error(f"Response dict: {response.__dict__}")
                    else:
                        logger.error(f"Response data: {str(response)}")
                except:
                    logger.error("Could not stringify response")

                raise ValueError(f"Failed to process OpenAI response: {str(ae)}")

        except Exception as e:
            logger.error(f"Error in basic resume evaluation: {str(e)}")
            raise ValueError(f"Basic resume evaluation failed: {str(e)}")