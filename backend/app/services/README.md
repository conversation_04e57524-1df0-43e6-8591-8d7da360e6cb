# Backend Services Documentation

## Overview
This directory contains the core services that power Recruiva's backend functionality. Each service is designed as a singleton to ensure consistent state management and resource utilization across the application.

## Service Architecture

### Core Services

#### 1. FirebaseService (`firebase_service.py`)
The central service managing all Firebase operations and data persistence.

**Key Features:**
- Singleton pattern implementation with test mode support
- Firebase Admin SDK initialization and configuration
- Role management (CRUD operations)
- User data management
- Document storage operations
- Data migration utilities

**Main Methods:**
- `create_role(user_id: str, role_data: Dict[str, Any]) -> str`
- `get_role(user_id: str, role_id: str) -> dict`
- `get_roles(user_id: str) -> list`
- `update_role(user_id: str, role_id: str, updates: Dict[str, Any]) -> None`
- `delete_role(user_id: str, role_id: str) -> None`

#### 2. AuthService (`auth_service.py`)
Handles user authentication, authorization, and user management.

**Key Features:**
- Firebase authentication integration
- User registration and verification
- Token management
- Pre-registration validation

**Main Methods:**
- `is_user_pre_registered(email: str) -> bool`
- `register_user(user_data: Dict[str, Any], firebase_uid: str) -> Dict[str, Any]`
- `verify_token(token: str, is_registering: bool = False) -> Optional[Dict[str, Any]]`
- `ensure_user_exists(uid: str, email: str) -> Dict[str, Any]`
- `create_custom_token(uid: str) -> str`

#### 3. EmailService (`email_service.py`)
Manages email operations and automated communications.

**Key Features:**
- SMTP/IMAP integration
- Email parsing and processing
- Automated response handling with OpenAI integration
- Email workflow management
- Markdown/HTML email support
- Sender validation against Firebase users

**Main Methods:**
- `send_email(subject: str, body: str, to_email: str, ...) -> None`
- `check_new_emails() -> List[Dict[str, Any]]`
- `send_intake_email(to_email: str, role_id: str, ...) -> None`
- `send_interview_invitation(to_email: str, ...) -> None`
- `start_email_listener() -> None`
- `process_email(email_data: Dict[str, Any]) -> None`

#### 4. RolesService (`roles_service.py`)
Manages job role operations and data.

**Key Features:**
- Role creation and management
- Role status tracking
- Interview process management
- Role filtering and search
- Intake call and transcript management

**Main Methods:**
- `create_role(role_data: Dict[str, Any]) -> Dict[str, Any]`
- `update_role(role_id: str, updates: Dict[str, Any], user_id: str) -> Dict[str, Any]`
- `get_role(role_id: str, user_id: str) -> Optional[Dict[str, Any]]`
- `get_roles(user_id: str, filters: Optional[Dict[str, Any]] = None, ...) -> List[Dict[str, Any]]`
- `create_intake_transcript(role_id: str, transcript_data: Dict[str, Any]) -> str`
- `update_intake_transcript(role_id: str, transcript_id: str, updates: Dict[str, Any]) -> Dict[str, Any]`
- `complete_intake_transcript(role_id: str, transcript_id: str) -> Dict[str, Any]`

### AI Services

#### 5. AIService (`ai_service.py`)
Manages AI operations and integrations.

**Key Features:**
- OpenAI integration (function calling, streaming, error handling)
- Function registry for AI operations
- Email content processing
- Role creation automation
- Function calling implementation

**Main Methods:**
- `process_email(email_content: str, metadata: Dict[str, Any], ...) -> Dict[str, Any]`
- `_handle_role_creation(params: Dict[str, Any]) -> Dict[str, Any]`
- `_handle_get_roles(params: Dict[str, Any]) -> Dict[str, Any]`
- `_handle_get_role_details(params: Dict[str, Any]) -> Dict[str, Any]`

---

### OpenAI Integration (`openai/`)
A modular, extensible architecture for OpenAI integration. This directory contains all logic for interacting with OpenAI's APIs, managing prompts, handling model configuration, and processing API responses.

#### **Core Components**

- **Base Client (`base.py`)**: Foundation for OpenAI API calls. Handles:
  - Error mapping and handling with custom exception classes (`OpenAIError`, `RateLimitError`, etc.)
  - Retry logic (using `backoff`)
  - Configurable timeouts/rate limits
  - Streaming support
  - Model management

- **Chat Completion Service (`chat_completion_service.py`)**: High-level async service for generating completions. Handles:
  - Model-specific configuration
  - Function/tool validation
  - Logging and error handling
  - Streaming and non-streaming completions

- **Function Registry (`function_registry.py`)**: Central registry for OpenAI function calling. Supports:
  - Agent-specific and context-aware function sets
  - Dynamic handler registration/retrieval
  - Legacy and new function/tool API compatibility

- **Processors (`processors/`)**: Specialized utilities for handling OpenAI content streams and outputs:
  - `json_processor.py`: Extracts and validates JSON from model outputs, validates against Pydantic models, and generates OpenAI-compatible function schemas.
  - `stream_processor.py`: Processes streaming responses (text, JSON, and function call streams) from OpenAI, yielding parsed content or structured function call info.
  - `function_registry.py`: Local registry for registering/validating function definitions and handlers, used for advanced agent/AI workflows.

- **Prompts (`prompts/`)**: Prompt management and templates:
  - `manager.py`: Loads, formats, and versions prompt templates (supports context variables, YAML, and modular prompt structure).
  - `.prompt` files: System prompts for agents/use cases (e.g., `email_agent.prompt`, `interview_question_agent.prompt`, `job_posting.prompt`, etc.)
  - Prompts are modular, versioned, and reusable for different AI/agent workflows.

- **Models (`models/`)**:
  - `base.py`: Abstract base class for OpenAI model clients, with fallback/retry logic.
  - `chat.py`: Implements chat-based interactions, function calling, response streaming, and test/fallback modes.

- **Config (`config/`)**:
  - `model_configs.py`: Centralizes all model-specific parameters, use-case overrides, and fallback logic. Provides `ModelConfigurationManager` for retrieving configs by model/use-case and for determining fallback models.
  - `error_types.py`: Defines a comprehensive set of custom error classes for API errors, rate limits, token limits, authentication, permission, timeouts, and more. Provides `map_openai_error()` for consistent error mapping.

#### **Prompt Files & Use Cases**
- `email_agent.prompt`: System prompt for email agent (intent detection, role ops, response templates)
- `interview_question_agent.prompt`: Prompt for generating interview questions
- `interview_evaluation_agent.prompt`: Prompt for evaluating candidate responses
- `criterion_agent.prompt`: Prompt for scoring/evaluating against criteria
- `job_posting.prompt`: Prompt for job posting generation
- `role_enrichment.prompt`: Prompt for enriching role/job data
- `resume_evaluator_agent.prompt`: Prompt for resume evaluation

Each prompt is managed via `PromptManager` and can be versioned, templated, and reused across agents/services.

#### **Advanced Features**
- **JSONProcessor**: Robust extraction/validation of JSON from model outputs, schema validation with Pydantic, and dynamic function schema generation for OpenAI function calling.
- **StreamProcessor**: Handles streaming of text, JSON, and function call responses, yielding parsed content or structured function call arguments in real-time.
- **ModelConfigurationManager**: Centralizes all model and use-case-specific configuration, including temperature, max tokens, fallback models, and JSON mode settings.
- **Error Handling**: Every OpenAI call is wrapped with detailed error mapping, logging, and graceful fallback mechanisms.
- **PromptManager**: Supports versioned, context-aware, and modular prompt loading for all AI/agent workflows.

#### **Usage Example**
```python
from app.services.openai.chat_completion_service import ChatCompletionService
response = await ChatCompletionService.generate_completion(
    prompt="You are a helpful assistant",
    model="gpt-4o"
)

from app.services.openai.models.chat import ChatClient
from app.services.openai.function_registry import FunctionRegistry
functions = FunctionRegistry.get_functions("realtime")
chat_client = ChatClient()
response = await chat_client.process_with_function_calling(
    messages=[{"role": "system", "content": "You are a helpful assistant"}, {"role": "user", "content": "Create a new role for a Senior Developer"}],
    functions=functions,
    model="gpt-4o"
)
```

#### **Best Practices**
1. Register all agent/AI functions in the FunctionRegistry for discoverability and API compatibility
2. Use PromptManager for all prompt loading and templating
3. Validate and parse all JSON outputs using JSONProcessor
4. Handle streaming outputs with StreamProcessor for real-time applications
5. Use ModelConfigurationManager for all model and use-case configuration
6. Always implement error handling via error_types and map_openai_error
7. Store prompts as separate files for maintainability and versioning
8. Document new prompts, processors, and models in this README when added

---

### Realtime Services (`realtime/`)
A modular agent architecture for real-time interactions:

#### Agent Loader (`agent_loader.py`)
- Dynamic agent loading utility
- Fallback to common modules
- Prompt retrieval

#### Agents (`agents/`)
- `common/`: Shared components across agents
  - `context.py`: Common context building
  - `functions.py`: Shared function definitions
  - `services.py`: Common service interfaces that include:
    - `update_role`: Updates role data with proper handling of enrichment mode
    - `complete_transcript`: Marks a transcript as completed
    - `get_transcript`: Retrieves transcript data
    - `safe_json_loads`: Utility for safely parsing JSON
  - `prompts/`: Shared prompts

- `intake_agent/`: Intake agent implementation
  - `context.py`: Intake-specific context
  - `functions.py`: Intake-specific functions
  - `services.py`: Intake-specific services
  - `prompts/`: Intake-specific prompts

- `interview_agent/`: Interview agent implementation
  - `context.py`: Interview-specific context
  - `functions.py`: Interview-specific functions
  - `services.py`: Interview-specific services
  - `prompts/`: Interview-specific prompts

### Utility Services

#### 6. ParserService (`parser_service.py`)
Handles document parsing and content extraction.

**Supported Formats:**
- PDF files
- Word documents (DOC/DOCX)
- Plain text files

**Main Methods:**
- `parse_job_description(file: UploadFile) -> str`

## Configuration

### Environment Variables
Required environment variables for services:

```env
# Firebase Configuration
FIREBASE_PROJECT_ID="recruiva"
FIREBASE_PRIVATE_KEY_ID="..."
FIREBASE_PRIVATE_KEY="..."
FIREBASE_CLIENT_EMAIL="..."
FIREBASE_CLIENT_ID="..."
FIREBASE_STORAGE_BUCKET="recruiva.appspot.com"

# Email Configuration
EMAIL_USERNAME="<EMAIL>"
EMAIL_PASSWORD="..."
EMAIL_FROM="Recruiva AI"

# OpenAI Configuration
OPENAI_API_KEY="..."
OPENAI_DEFAULT_MODEL="gpt-4o"
OPENAI_FALLBACK_MODEL="gpt-4o-mini"
OPENAI_TIMEOUT_SECONDS=30
OPENAI_MAX_RETRIES=3
```

## Error Handling
All services implement comprehensive error handling with:
- Detailed logging
- Error classification
- Retry mechanisms where appropriate
- Graceful degradation

## Usage Examples

### Role Creation
```python
roles_service = RolesService()
role_data = {
    "title": "Senior Software Engineer",
    "summary": "Leading backend development...",
    "user_id": "user123"
}
created_role = await roles_service.create_role(role_data)
```

### Email Processing
```python
email_service = EmailService()
await email_service.send_intake_email(
    to_email="<EMAIL>",
    role_id="role123",
    google_form_link="https://forms.google.com/..."
)
```

### Authentication
```python
auth_service = AuthService()
user_info = await auth_service.verify_token("user_token")
```

### OpenAI Chat Completion
```python
from app.services.openai.chat_completion_service import ChatCompletionService

response = await ChatCompletionService.generate_completion(
    prompt="You are a helpful assistant",
    model="gpt-4o"
)
```

### Realtime Agent Usage
```python
from app.services.realtime.agent_loader import AgentLoader

# Load an agent
agent_loader = AgentLoader("intake_agent")

# Get agent components
context_module = agent_loader.get_context()
functions_module = agent_loader.get_functions()
services_module = agent_loader.get_services()

# Get agent prompt
system_prompt = agent_loader.get_prompt("system_prompt.md")
```

## Best Practices
1. Always use the singleton instances of services
2. Handle service exceptions appropriately
3. Validate input data before service calls
4. Use proper typing for function parameters
5. Follow the established error handling patterns
6. Use the ChatCompletionService for simple completions
7. Leverage the ChatClient for advanced use cases
8. Store prompts as separate files for maintainability
9. Use the function registry for structured outputs

## Dependencies
- Firebase Admin SDK
- OpenAI API
- FastAPI
- PyPDF2
- python-docx
- BeautifulSoup4
- backoff 