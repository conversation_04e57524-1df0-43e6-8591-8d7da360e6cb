# File: backend/app/services/auth_service.py

import logging
from typing import Dict, Any, Optional
from firebase_admin import auth, firestore
from ..core.config import settings
from .firebase_service import FirebaseService
from fastapi import HTTPException
from google.cloud.firestore_v1.transaction import Transaction
from google.cloud.firestore_v1.base_query import FieldFilter

# Set up logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class AuthService:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AuthService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not AuthService._initialized:
            self.firebase = FirebaseService()
            self.db = firestore.client()
            AuthService._initialized = True

    async def is_user_pre_registered(self, email: str) -> bool:
        """Check if the user's email is pre-registered in the system."""
        try:
            logger.debug(f"Checking pre-registration for email: {email}")
            # Check if user exists in Firestore
            users_ref = self.db.collection('users')
            query = users_ref.where(filter=FieldFilter('email', '==', email)).limit(1)
            docs = query.get()
            is_registered = len(docs) > 0
            logger.debug(f"User pre-registration status: {is_registered}")
            return is_registered
        except Exception as e:
            logger.error(f"Error checking pre-registration: {str(e)}", exc_info=True)
            return False

    async def register_user(self, user_data: Dict[str, Any], firebase_uid: str) -> Dict[str, Any]:
        """Register a new user in the system with atomic transaction."""
        try:
            logger.debug(f"Starting registration for email: {user_data.get('email')}")
            
            # First verify the user doesn't already exist
            user_ref = self.db.collection('users').document(firebase_uid)
            user_doc = user_ref.get()
            
            if user_doc.exists:
                logger.debug(f"User already exists: {firebase_uid}")
                raise HTTPException(status_code=400, detail="User already exists")
            
            # Create new user document
            user_data_to_save = {
                'email': user_data['email'],
                'name': user_data['name'],
                'role': user_data.get('role', 'user'),
                'profileComplete': False,
                'is_active': True,
                'createdAt': firestore.SERVER_TIMESTAMP,
                'updatedAt': firestore.SERVER_TIMESTAMP
            }
            
            # Set the document
            user_ref.set(user_data_to_save)
            
            # Set custom claims after successful registration
            auth.set_custom_user_claims(firebase_uid, {
                'isRegistered': True,
                'role': user_data.get('role', 'user')
            })
            
            logger.info(f"Successfully registered user: {firebase_uid}")
            return {
                **user_data_to_save,
                'uid': firebase_uid,
                'needsTokenRefresh': True
            }
            
        except HTTPException as he:
            logger.error(f"HTTP Exception in register_user: {str(he)}")
            raise he
        except Exception as e:
            logger.error(f"Error in register_user: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Registration failed: {str(e)}")

    async def ensure_user_exists(self, uid: str, email: str) -> Dict[str, Any]:
        """Ensure user exists in Firestore, create if not."""
        try:
            # Check if user exists in Firestore
            user_doc = self.db.collection('users').document(uid).get()
            
            if not user_doc.exists:
                # Get user info from Firebase Auth
                auth_user = auth.get_user(uid)
                
                # Create user document
                user_data = {
                    'uid': uid,
                    'email': email,
                    'name': auth_user.display_name or email.split('@')[0],
                    'created_at': firestore.SERVER_TIMESTAMP,
                    'updated_at': firestore.SERVER_TIMESTAMP,
                    'is_active': True
                }
                
                # Add user to Firestore
                self.db.collection('users').document(uid).set(user_data)
                logger.info(f"Created missing user document for: {email}")
                
                # Set claims
                await auth.set_custom_user_claims(uid, {
                    'isRegistered': True,
                    'isRegistering': False,
                    'role': 'user'
                })
                
                return user_data
            
            return user_doc.to_dict()
        except Exception as e:
            logger.error(f"Error ensuring user exists: {e}")
            raise

    async def verify_token(self, token: str, is_registering: bool = False) -> Optional[Dict[str, Any]]:
        """Verify a Firebase ID token and return user info."""
        try:
            # Verify the token with Firebase
            decoded_token = auth.verify_id_token(token)
            uid = decoded_token.get('uid')
            email = decoded_token.get('email')

            if not uid or not email:
                raise HTTPException(status_code=401, detail="Invalid token")

            # Check if user exists in Firestore
            user_ref = self.db.collection('users').document(uid)
            user_doc = user_ref.get()

            # If user doesn't exist in Firestore
            if not user_doc.exists:
                # If this is a registration attempt, allow it to proceed
                if is_registering:
                    return {
                        'uid': uid,
                        'email': email,
                        'isRegistered': False
                    }
                
                # If not registering, check if email is pre-registered
                is_pre_registered = await self.is_user_pre_registered(email)
                if not is_pre_registered:
                    # For non-recruiva.ai emails, require pre-registration
                    if not email.endswith('@recruiva.ai'):
                        raise HTTPException(
                            status_code=403,
                            detail="Please contact your administrator or Recruiva's sales team to register."
                        )
                
                # For recruiva.ai emails or pre-registered users, allow them to proceed
                raise HTTPException(
                    status_code=403,
                    detail="User not registered"
                )

            # User exists in Firestore, return their data
            user_data = user_doc.to_dict()
            return {
                **user_data,
                'uid': uid,
                'email': email,
                'isRegistered': True
            }

        except auth.InvalidIdTokenError:
            raise HTTPException(status_code=401, detail="Invalid token")
        except auth.ExpiredIdTokenError:
            raise HTTPException(status_code=401, detail="Token has expired")
        except auth.RevokedIdTokenError:
            raise HTTPException(status_code=401, detail="Token has been revoked")
        except Exception as e:
            logger.error(f"Error verifying token: {str(e)}")
            raise HTTPException(status_code=401, detail=str(e))

    async def create_custom_token(self, uid: str) -> str:
        """Create a custom Firebase token for a user."""
        try:
            custom_token = auth.create_custom_token(uid)
            return custom_token.decode('utf-8')
        except Exception as e:
            logger.error(f"Error creating custom token: {e}")
            raise

    async def get_user(self, uid: str) -> Optional[Dict[str, Any]]:
        """Get user information from Firebase Auth."""
        try:
            user = auth.get_user(uid)
            return {
                'uid': user.uid,
                'email': user.email,
                'email_verified': user.email_verified,
                'display_name': user.display_name,
                'photo_url': user.photo_url,
                'disabled': user.disabled
            }
        except Exception as e:
            logger.error(f"Error getting user: {e}")
            return None 