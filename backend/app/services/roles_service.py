# File: backend/app/services/roles_service.py

from typing import Dict, Any, Optional, List, Union
import logging
from datetime import datetime
from firebase_admin import firestore
from ..core.config import settings
from .firebase_service import FirebaseService
from firebase_admin.firestore import FieldFilter
import json
from ..utils.data_transformers import normalize_role_data, normalize_enum_values
import uuid

class RolesService:
    def __init__(self):
        self.firebase = FirebaseService()
        self.db = firestore.client()

    async def create_role(self, role_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new role."""
        try:
            # Validate user_id first
            user_id = role_data.get("user_id")
            if not user_id:
                raise ValueError("user_id is required")

            # Normalize field names and enum values
            role_data = normalize_role_data(role_data)
            role_data = normalize_enum_values(role_data)

            # Validate required fields from frontend schema
            required_fields = ["title", "summary"]
            for field in required_fields:
                if field not in role_data:
                    raise ValueError(f"Missing required field: {field}")

            # Ensure interview process has required fields
            interview_process = role_data.get("interviewProcess", [])
            if not interview_process:
                interview_process = [{
                    "stage": "Screening",
                    "duration": "30 minutes",
                    "description": "Initial screening call to assess basic qualifications and mutual fit",
                    "customInstructions": ""
                }]
            else:
                for stage in interview_process:
                    if "duration" not in stage:
                        stage["duration"] = "30 minutes"
                    if "description" not in stage:
                        stage["description"] = f"Evaluate candidate's fit for {stage.get('stage', 'this stage')}"
                    if "customInstructions" not in stage:
                        stage["customInstructions"] = ""

            role_data["interviewProcess"] = interview_process

            # Validate status value
            valid_statuses = [
                "Intake", "Sourcing", "Screening", "Deep_Dive",
                "In_Person", "Offer", "Accepted", "Rejected", "Closed"
            ]
            status = role_data.get("status", "Intake")
            if status not in valid_statuses:
                raise ValueError(f"Invalid status value: {status}")

            # Add metadata
            role_data.update({
                "createdAt": datetime.utcnow(),
                "updatedAt": datetime.utcnow(),
                "status": status,  # Use validated status
                "priority": role_data.get("priority", "Normal"),
                "hiringManagerId": user_id  # Set hiring manager ID from user_id
            })

            # Create role in Firestore
            role_id = await self.firebase.create_role(user_id, role_data)

            # Get the created role
            role = await self.firebase.get_role(user_id, role_id)
            if not role:
                raise ValueError("Failed to retrieve created role")

            return role

        except Exception as e:
            logging.error(f"Error creating role: {str(e)}", exc_info=True)
            raise

    async def update_role(self, role_id: str, updates: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Update an existing role."""
        try:
            logging.info(f"Updating role {role_id} for user {user_id}")

            # Verify role exists and user has access
            existing_role = await self.firebase.get_role(user_id, role_id)
            if not existing_role:
                raise ValueError(f"Role not found or access denied: {role_id}")

            # Log the updates for debugging
            logging.info(f"Update data preview: {json.dumps(updates, default=str)[:200]}...")

            # Ensure updates is a dictionary
            if not isinstance(updates, dict):
                logging.error(f"Updates is not a dictionary: {type(updates)}")
                raise ValueError(f"Updates must be a dictionary, got {type(updates)}")

            # Normalize field names and enum values in updates
            updates = normalize_role_data(updates)
            updates = normalize_enum_values(updates)

            # Add metadata
            if "updatedAt" not in updates:
                updates["updatedAt"] = datetime.utcnow()

            # Update role in Firestore
            await self.firebase.update_role(user_id, role_id, updates)

            # Get the updated role
            updated_role = await self.firebase.get_role(user_id, role_id)
            if not updated_role:
                raise ValueError("Failed to retrieve updated role")

            logging.info(f"Successfully updated role {role_id}")

            return {**updated_role, "id": role_id}

        except Exception as e:
            logging.error(f"Error updating role: {str(e)}", exc_info=True)
            raise

    async def get_role(self, role_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get a role by ID."""
        try:
            role = await self.firebase.get_role(user_id, role_id)
            if role:
                return {**role, "id": role_id}
            return None

        except Exception as e:
            logging.error(f"Error getting role: {str(e)}", exc_info=True)
            raise

    async def get_roles(
        self,
        user_id: str,
        filters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        order_by: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get all roles for a user with optional filtering."""
        try:
            logging.info(f"Getting roles for user {user_id} with filters: {filters}")

            # Start with base query on the correct collection path
            query = self.db.collection('users').document(user_id).collection('roles')

            # Get all documents for the user
            docs = query.stream()

            # Convert to list and apply filters in memory
            roles = []
            for doc in docs:
                try:
                    role_data = doc.to_dict()
                    if not isinstance(role_data, dict):
                        logging.warning(f"Role data for {doc.id} is not a dictionary, skipping")
                        continue

                    role_data['id'] = doc.id

                    # Convert Firestore timestamps to naive datetime objects
                    if 'createdAt' in role_data:
                        if hasattr(role_data['createdAt'], 'timestamp'):
                            # Convert to naive datetime by using timestamp
                            timestamp = role_data['createdAt'].timestamp()
                            role_data['createdAt'] = datetime.utcfromtimestamp(timestamp)
                        elif isinstance(role_data['createdAt'], str):
                            try:
                                # Try to parse ISO format string and convert to naive UTC
                                dt = datetime.fromisoformat(role_data['createdAt'].replace('Z', '+00:00'))
                                # Convert to UTC naive datetime
                                role_data['createdAt'] = dt.astimezone().replace(tzinfo=None)
                            except ValueError:
                                # If parsing fails, use minimum datetime
                                role_data['createdAt'] = datetime.min
                        elif isinstance(role_data['createdAt'], datetime):
                            # If it's already a datetime, ensure it's naive
                            if role_data['createdAt'].tzinfo is not None:
                                role_data['createdAt'] = role_data['createdAt'].astimezone().replace(tzinfo=None)
                        else:
                            # If not a datetime, use minimum datetime
                            role_data['createdAt'] = datetime.min

                    if 'updatedAt' in role_data:
                        if hasattr(role_data['updatedAt'], 'timestamp'):
                            # Convert to naive datetime by using timestamp
                            timestamp = role_data['updatedAt'].timestamp()
                            role_data['updatedAt'] = datetime.utcfromtimestamp(timestamp)
                        elif isinstance(role_data['updatedAt'], str):
                            try:
                                # Try to parse ISO format string and convert to naive UTC
                                dt = datetime.fromisoformat(role_data['updatedAt'].replace('Z', '+00:00'))
                                # Convert to UTC naive datetime
                                role_data['updatedAt'] = dt.astimezone().replace(tzinfo=None)
                            except ValueError:
                                # If parsing fails, use minimum datetime
                                role_data['updatedAt'] = datetime.min
                        elif isinstance(role_data['updatedAt'], datetime):
                            # If it's already a datetime, ensure it's naive
                            if role_data['updatedAt'].tzinfo is not None:
                                role_data['updatedAt'] = role_data['updatedAt'].astimezone().replace(tzinfo=None)
                        else:
                            # If not a datetime, use minimum datetime
                            role_data['updatedAt'] = datetime.min

                    # Normalize field names and values
                    role_data = normalize_role_data(role_data)
                    role_data = normalize_enum_values(role_data)

                    # Ensure keyResponsibilities is a list
                    if 'keyResponsibilities' not in role_data or role_data['keyResponsibilities'] is None:
                        role_data['keyResponsibilities'] = []
                    elif not isinstance(role_data['keyResponsibilities'], list):
                        # Try to convert to list if possible
                        try:
                            if isinstance(role_data['keyResponsibilities'], str):
                                # If it's a string, try to split by commas
                                role_data['keyResponsibilities'] = [item.strip() for item in role_data['keyResponsibilities'].split(',') if item.strip()]
                            else:
                                # For other types, convert to string and make a single-item list
                                role_data['keyResponsibilities'] = [str(role_data['keyResponsibilities'])]
                        except Exception:
                            # If conversion fails, use empty list
                            role_data['keyResponsibilities'] = []
                            logging.warning(f"Could not convert keyResponsibilities to list for role {doc.id}, using empty list")

                    # Ensure team is a string
                    if 'team' not in role_data or role_data['team'] is None:
                        role_data['team'] = ""
                    elif not isinstance(role_data['team'], str):
                        # Convert to string
                        try:
                            role_data['team'] = str(role_data['team'])
                        except Exception:
                            role_data['team'] = ""
                            logging.warning(f"Could not convert team to string for role {doc.id}, using empty string")

                    # Apply filters in memory
                    if filters:
                        should_include = True
                        if 'dateRange' in filters:
                            date_range = filters['dateRange']
                            created_at = role_data.get('createdAt')
                            if created_at:
                                # Parse filter dates and convert to naive UTC datetime
                                if date_range.get('start'):
                                    try:
                                        # Parse the ISO format date and convert to naive UTC
                                        start_date = datetime.fromisoformat(date_range['start'].replace('Z', '+00:00'))
                                        if start_date.tzinfo:
                                            # Convert to UTC and remove timezone info
                                            start_date = start_date.astimezone().replace(tzinfo=None)
                                        if created_at < start_date:
                                            should_include = False
                                    except ValueError as e:
                                        logging.error(f"Invalid start date format: {e}")
                                        should_include = False

                                if date_range.get('end'):
                                    try:
                                        # Parse the ISO format date and convert to naive UTC
                                        end_date = datetime.fromisoformat(date_range['end'].replace('Z', '+00:00'))
                                        if end_date.tzinfo:
                                            # Convert to UTC and remove timezone info
                                            end_date = end_date.astimezone().replace(tzinfo=None)
                                        if created_at > end_date:
                                            should_include = False
                                    except ValueError as e:
                                        logging.error(f"Invalid end date format: {e}")
                                        should_include = False

                        if filters.get('status') and role_data.get('status') != filters['status']:
                            should_include = False

                        if filters.get('team') and role_data.get('team') != filters['team']:
                            should_include = False

                        if should_include:
                            roles.append(role_data)
                    else:
                        roles.append(role_data)
                except Exception as e:
                    # If processing a single role fails, log the error and continue with other roles
                    logging.error(f"Error processing role {doc.id} for user {user_id}: {str(e)}", exc_info=True)
                    continue

            # Sort results
            if order_by:
                roles.sort(key=lambda x: x.get(order_by, ''), reverse=True)
            else:
                # Default sort by createdAt
                roles.sort(
                    key=lambda x: x.get('createdAt', datetime.min) if isinstance(x.get('createdAt'), datetime) else datetime.min,
                    reverse=True
                )

            # Apply limit
            if limit and len(roles) > limit:
                roles = roles[:limit]

            # Convert datetime objects to ISO format strings for JSON serialization
            for role in roles:
                if 'createdAt' in role and isinstance(role['createdAt'], datetime):
                    role['createdAt'] = role['createdAt'].isoformat() + 'Z'  # Add UTC indicator
                if 'updatedAt' in role and isinstance(role['updatedAt'], datetime):
                    role['updatedAt'] = role['updatedAt'].isoformat() + 'Z'  # Add UTC indicator

            logging.info(f"Found {len(roles)} roles for user {user_id}")
            return roles

        except Exception as e:
            logging.error(f"Error getting roles for user {user_id}: {str(e)}", exc_info=True)
            # Return an empty list instead of raising an exception
            return []

    async def list_roles(self, user_id: str, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Alias for get_roles for backward compatibility."""
        try:
            return await self.get_roles(user_id, filters)
        except Exception as e:
            # If get_roles fails, log the error and return an empty list
            logging.error(f"Error in list_roles for user {user_id}: {str(e)}", exc_info=True)
            return []

    async def delete_role(self, role_id: str, user_id: str) -> bool:
        """Delete a role."""
        try:
            # Verify role exists and user has access
            existing_role = await self.firebase.get_role(user_id, role_id)
            if not existing_role:
                raise ValueError(f"Role not found or access denied: {role_id}")

            # Delete role from Firestore
            await self.firebase.delete_role(user_id, role_id)
            return True
        except Exception as e:
            logging.error(f"Error deleting role: {str(e)}", exc_info=True)
            raise

    async def enrich_role(self, role_id: str, updates: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """
        Enrich an existing role by merging new data with existing data.
        This is different from update_role as it preserves existing data and only adds new information.
        """
        try:
            logging.info(f"Enriching role {role_id} for user {user_id}")

            # Get existing role data
            existing_role = await self.firebase.get_role(user_id, role_id)
            if not existing_role:
                raise ValueError(f"Role not found or access denied: {role_id}")

            # Normalize field names and enum values in updates
            updates = normalize_role_data(updates)
            updates = normalize_enum_values(updates)

            # Create a merged data object that preserves existing data
            merged_data = {}

            # Handle simple fields - keep existing values if not in updates
            for field in ["title", "level", "summary", "yearsOfExperience", "team", "jobType",
                         "aboutTeam", "openEndedConsiderations", "aboutCompany", "hiringManagerContact"]:
                if updates.get(field) and (not existing_role.get(field) or existing_role.get(field) == "Placeholder role created for intake call. Details will be updated after the call."):
                    merged_data[field] = updates[field]

            # Handle array fields - merge arrays without duplicates
            for field in ["keyResponsibilities", "keyStakeholders"]:
                if updates.get(field):
                    existing_items = existing_role.get(field, [])
                    new_items = updates.get(field, [])
                    # Use a set to remove duplicates
                    merged_items = list(set(existing_items + new_items))
                    merged_data[field] = merged_items

            # Handle object fields - merge objects
            for field in ["requiredSkills", "preferredSkills"]:
                if updates.get(field):
                    existing_obj = existing_role.get(field, {})
                    new_obj = updates.get(field, {})
                    merged_obj = {**existing_obj, **new_obj}
                    merged_data[field] = merged_obj

            # Handle location object specially
            if updates.get("location"):
                existing_location = existing_role.get("location", {})
                new_location = updates.get("location", {})
                merged_location = {**existing_location, **new_location}
                merged_data["location"] = merged_location

            # Handle interview process - replace if provided
            if updates.get("interviewProcess"):
                merged_data["interviewProcess"] = updates["interviewProcess"]

            # Add metadata
            merged_data["updatedAt"] = datetime.utcnow()

            # Update the role with merged data
            await self.firebase.update_role(user_id, role_id, merged_data)

            # Get the updated role
            updated_role = await self.firebase.get_role(user_id, role_id)
            if not updated_role:
                raise ValueError("Failed to retrieve updated role")

            return updated_role

        except Exception as e:
            logging.error(f"Error enriching role: {str(e)}", exc_info=True)
            raise

    def _normalize_status(self, status: str) -> str:
        """Normalize role status to match the expected format."""
        if not status:
            return "Intake"

        # Map of possible status values to normalized values
        status_map = {
            "intake": "Intake",
            "sourcing": "Sourcing",
            "screening": "Screening",
            "deep_dive": "Deep_Dive",
            "deep dive": "Deep_Dive",
            "in_person": "In_Person",
            "in person": "In_Person",
            "offer": "Offer",
            "accepted": "Accepted",
            "rejected": "Rejected",
            "closed": "Closed"
        }

        # Try to normalize the status
        normalized = status_map.get(status.lower())
        if normalized:
            return normalized

        # If not found in the map, return the original status
        return status

    # Cache for public roles to avoid repeated Firestore queries
    _public_roles_cache = None
    _public_roles_cache_timestamp = 0
    _public_roles_cache_ttl = 5 * 60  # 5 minutes in seconds

    async def get_all_roles(self) -> List[Dict[str, Any]]:
        """
        Get all roles from all users.
        This is used for public endpoints where we want to show all roles.
        """
        try:
            # Check if we have a valid cache
            current_time = datetime.now().timestamp()
            if (self._public_roles_cache is not None and
                current_time - self._public_roles_cache_timestamp < self._public_roles_cache_ttl):
                logging.info(f"Using cached public roles ({len(self._public_roles_cache)} roles)")
                return self._public_roles_cache

            # Get all roles from the global roles collection
            roles = []

            # First, get all users
            users_ref = self.db.collection('users')
            users_docs = users_ref.stream()

            # For each user, get their roles
            for user_doc in users_docs:
                user_id = user_doc.id

                # Get all roles for this user - we'll filter based on isPublished flag later
                roles_ref = (self.db.collection('users')
                             .document(user_id)
                             .collection('roles'))

                roles_docs = roles_ref.stream()

                for doc in roles_docs:
                    role_data = doc.to_dict()
                    role_id = doc.id
                    role_data['id'] = role_id
                    role_data['userId'] = user_id  # Add user ID for reference

                    # Normalize status field
                    if 'status' in role_data:
                        role_data['status'] = self._normalize_status(role_data['status'])
                    else:
                        role_data['status'] = 'Intake'

                    # Ensure level field is valid
                    if 'level' not in role_data or role_data['level'] is None:
                        role_data['level'] = 'Entry Level'

                    # Convert timestamps to ISO format strings
                    if 'createdAt' in role_data and role_data['createdAt']:
                        # Check if createdAt is already a string
                        if not isinstance(role_data['createdAt'], str):
                            try:
                                role_data['createdAt'] = role_data['createdAt'].isoformat()
                            except AttributeError:
                                # If it's not a datetime object with isoformat method, convert to string
                                role_data['createdAt'] = str(role_data['createdAt'])

                    if 'updatedAt' in role_data and role_data['updatedAt']:
                        # Check if updatedAt is already a string
                        if not isinstance(role_data['updatedAt'], str):
                            try:
                                role_data['updatedAt'] = role_data['updatedAt'].isoformat()
                            except AttributeError:
                                # If it's not a datetime object with isoformat method, convert to string
                                role_data['updatedAt'] = str(role_data['updatedAt'])

                    # Ensure all required fields have valid values
                    self._ensure_valid_role_fields(role_data)

                    # Add the role to our list
                    roles.append(role_data)

            logging.info(f"Found {len(roles)} roles across all users")

            # Update cache
            self._public_roles_cache = roles
            self._public_roles_cache_timestamp = current_time

            return roles
        except Exception as e:
            logging.error(f"Error getting all roles: {str(e)}", exc_info=True)
            return []

    def _ensure_valid_role_fields(self, role_data: Dict[str, Any]) -> None:
        """Ensure all required fields have valid values."""
        # Ensure required fields
        if 'title' not in role_data or not role_data['title']:
            role_data['title'] = 'Untitled Role'

        # Ensure level is valid
        valid_levels = ['Entry Level', 'Junior', 'Mid-Level', 'Senior', 'Lead', 'Principal', 'Distinguished']
        if 'level' not in role_data or role_data['level'] not in valid_levels:
            role_data['level'] = 'Entry Level'

        # Ensure status is valid
        valid_statuses = ['Intake', 'Sourcing', 'Screening', 'Deep_Dive', 'In_Person', 'Offer', 'Accepted', 'Rejected', 'Closed']
        if 'status' not in role_data or role_data['status'] not in valid_statuses:
            role_data['status'] = 'Intake'

        # Ensure isPublished flag is present
        if 'isPublished' not in role_data:
            role_data['isPublished'] = False

        # Ensure priority is valid
        if 'priority' not in role_data or not role_data['priority']:
            role_data['priority'] = 'Normal'

        # Ensure other required fields
        if 'keyResponsibilities' not in role_data or not isinstance(role_data['keyResponsibilities'], list):
            role_data['keyResponsibilities'] = []

        if 'requiredSkills' not in role_data or not isinstance(role_data['requiredSkills'], dict):
            role_data['requiredSkills'] = {}

        if 'preferredSkills' not in role_data or not isinstance(role_data['preferredSkills'], dict):
            role_data['preferredSkills'] = {}

        if 'certificates' not in role_data or not isinstance(role_data['certificates'], list):
            role_data['certificates'] = []

        if 'keyStakeholders' not in role_data or not isinstance(role_data['keyStakeholders'], list):
            role_data['keyStakeholders'] = []

        if 'interviewProcess' not in role_data or not isinstance(role_data['interviewProcess'], list):
            role_data['interviewProcess'] = []

        # Ensure location is valid
        if 'location' not in role_data or not isinstance(role_data['location'], dict):
            role_data['location'] = {'city': '', 'remoteStatus': 'Remote'}
        elif 'remoteStatus' not in role_data['location'] or not role_data['location']['remoteStatus']:
            role_data['location']['remoteStatus'] = 'Remote'

        # Ensure job type is valid
        if 'jobType' not in role_data or not role_data['jobType']:
            role_data['jobType'] = 'Full-time'

    def _generate_fallback_job_posting(self, role_data: Dict[str, Any]) -> str:
        """Generate a simple job posting from role data as a fallback."""
        title = role_data.get('title', 'Untitled Role')
        summary = role_data.get('summary', 'No summary provided.')
        role_id = role_data.get('id', '')  # Get role_id for the application link

        # Get required skills
        required_skills = role_data.get('requiredSkills', {})
        required_skills_text = ""
        if required_skills:
            required_skills_text = "## Required Skills\n\n"
            for skill, level in required_skills.items():
                required_skills_text += f"- {skill}: {level}\n"

        # Get preferred skills
        preferred_skills = role_data.get('preferredSkills', {})
        preferred_skills_text = ""
        if preferred_skills:
            preferred_skills_text = "## Preferred Skills\n\n"
            for skill, level in preferred_skills.items():
                preferred_skills_text += f"- {skill}: {level}\n"

        # Get responsibilities
        responsibilities = role_data.get('keyResponsibilities', [])
        responsibilities_text = ""
        if responsibilities:
            responsibilities_text = "## Key Responsibilities\n\n"
            for resp in responsibilities:
                responsibilities_text += f"- {resp}\n"

        # Get location
        location = role_data.get('location', {})
        location_text = ""
        if location:
            city = location.get('city', '')
            remote_status = location.get('remoteStatus', location.get('type', 'Remote'))
            if city:
                location_text = f"## Location\n\n{city} ({remote_status})\n\n"
            else:
                location_text = f"## Location\n\n{remote_status}\n\n"

        # Get job type
        job_type = role_data.get('jobType', 'Full-time')
        job_type_text = f"## Job Type\n\n{job_type}\n\n"

        # Get experience
        experience = role_data.get('yearsOfExperience', '')
        experience_text = ""
        if experience:
            experience_text = f"## Experience\n\n{experience}\n\n"

        # Get education
        education = role_data.get('education', {})
        education_text = ""
        if education and education.get('value'):
            education_text = f"## Education\n\n{education.get('value')}"
            if education.get('isRequired'):
                education_text += " (Required)\n\n"
            else:
                education_text += " (Preferred)\n\n"

        # Get compensation
        compensation = role_data.get('compensation', {})
        compensation_text = ""
        if compensation and compensation.get('range'):
            compensation_text = f"## Compensation\n\n{compensation.get('range')}"
            if compensation.get('currency'):
                compensation_text += f" {compensation.get('currency')}"
            if compensation.get('equity'):
                compensation_text += "\n\nEquity available"
            compensation_text += "\n\n"

        # Get benefits
        benefits = role_data.get('benefits', {})
        benefits_text = ""
        if benefits:
            benefits_list = []
            if benefits.get('healthInsurance'):
                benefits_list.append("Health Insurance")
            if benefits.get('vacationDays'):
                benefits_list.append(f"{benefits.get('vacationDays')} Vacation Days")
            if benefits.get('dentalInsurance'):
                benefits_list.append("Dental Insurance")
            if benefits.get('visionInsurance'):
                benefits_list.append("Vision Insurance")
            if benefits.get('lifeInsurance'):
                benefits_list.append("Life Insurance")
            if benefits.get('retirement401k'):
                benefits_list.append("401(k) Retirement Plan")
            if benefits.get('stockOptions'):
                benefits_list.append("Stock Options")

            if benefits_list:
                benefits_text = "## Benefits\n\n"
                for benefit in benefits_list:
                    benefits_text += f"- {benefit}\n"
                benefits_text += "\n"

        # Get about company
        about_company = role_data.get('aboutCompany', '')
        about_company_text = ""
        if about_company:
            about_company_text = f"## About the Company\n\n{about_company}\n\n"

        # Get about team
        about_team = role_data.get('aboutTeam', '')
        about_team_text = ""
        if about_team:
            about_team_text = f"## About the Team\n\n{about_team}\n\n"

        # Generate the how to apply section with the application link
        how_to_apply_text = "## How to Apply\n\n"
        if role_id:
            how_to_apply_text += f"""Ready to take the next step in your career? Apply now by visiting our Instant Interview page:

[Apply for this position](https://recruiva.ai/jobs/{role_id})

Our AI-powered interview process is:
- **Fast and convenient**: Complete each stage on your own schedule
- **Fair and unbiased**: Every candidate receives the same objective evaluation
- **Transparent**: Get instant feedback after each interview stage

After successfully completing all interview stages, our team will reach out to discuss the next steps in the hiring process.

We look forward to meeting you!
"""
        else:
            how_to_apply_text += "Please submit your application with your resume and a cover letter."

        # Generate the job posting
        job_posting = f"""# {title}

## Summary

{summary}

{responsibilities_text}

{required_skills_text}

{preferred_skills_text}

{job_type_text}

{location_text}

{experience_text}

{education_text}

{compensation_text}

{benefits_text}

{about_company_text}

{about_team_text}

{how_to_apply_text}
"""

        return job_posting

    async def get_role_by_id(self, role_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a role by ID from any user.
        This is used for public endpoints where we want to show a specific role.
        """
        try:
            logging.info(f"Searching for role with ID: {role_id}")

            # First try to get from the global roles collection (for public roles)
            role_global_ref = self.db.collection('roles').document(role_id)
            role_global_doc = role_global_ref.get()  # Firebase operations are synchronous

            if role_global_doc.exists:
                role_data = role_global_doc.to_dict()
                role_data['id'] = role_id
                logging.info(f"Found role {role_id} in global collection")

                # Process the role data
                return self._process_role_data(role_data, role_id, role_data.get('userId'))

            # If not found in global collection, check all users
            users_ref = self.db.collection('users')
            users_docs = users_ref.stream()  # Firebase operations are synchronous

            # For each user, check if they have the role
            for user_doc in users_docs:
                user_id = user_doc.id

                # Check if this user has the role
                role_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id)
                role_doc = role_ref.get()  # Firebase operations are synchronous

                if role_doc.exists:
                    role_data = role_doc.to_dict()
                    role_data['id'] = role_id
                    role_data['userId'] = user_id  # Add user ID for reference

                    logging.info(f"Found role {role_id} for user {user_id}")

                    # Process the role data
                    return self._process_role_data(role_data, role_id, user_id)

            # If we get here, the role was not found
            logging.warning(f"Role {role_id} not found for any user")
            return None
        except Exception as e:
            logging.error(f"Error getting role by ID: {str(e)}", exc_info=True)
            return None

    def _process_role_data(self, role_data: Dict[str, Any], role_id: str, user_id: Optional[str]) -> Dict[str, Any]:
        """
        Process role data to normalize fields and fetch additional information.
        """
        # Normalize status field
        if 'status' in role_data:
            original_status = role_data['status']
            role_data['status'] = self._normalize_status(role_data['status'])
            logging.info(f"Role {role_id} status: {original_status} -> {role_data['status']}")
        else:
            role_data['status'] = 'Intake'
            logging.info(f"Role {role_id} missing status, setting to default: Intake")

        # Ensure level field is valid
        if 'level' not in role_data or role_data['level'] is None:
            role_data['level'] = 'Entry Level'
            logging.info(f"Role {role_id} missing level, setting to default: Entry Level")

        # Convert timestamps to ISO format strings
        if 'createdAt' in role_data and role_data['createdAt']:
            # Check if createdAt is already a string
            if not isinstance(role_data['createdAt'], str):
                try:
                    role_data['createdAt'] = role_data['createdAt'].isoformat()
                except AttributeError:
                    # If it's not a datetime object with isoformat method, convert to string
                    role_data['createdAt'] = str(role_data['createdAt'])

        if 'updatedAt' in role_data and role_data['updatedAt']:
            # Check if updatedAt is already a string
            if not isinstance(role_data['updatedAt'], str):
                try:
                    role_data['updatedAt'] = role_data['updatedAt'].isoformat()
                except AttributeError:
                    # If it's not a datetime object with isoformat method, convert to string
                    role_data['updatedAt'] = str(role_data['updatedAt'])

        # Only fetch job posting if we have a user ID
        if user_id:
            self._fetch_job_posting(role_data, role_id, user_id)

        # Ensure all required fields have valid values
        self._ensure_valid_role_fields(role_data)

        # Log job posting status
        if 'jobPosting' in role_data and role_data['jobPosting']:
            logging.info(f"Role {role_id} has job posting of length {len(role_data['jobPosting'])}")
        else:
            logging.info(f"Role {role_id} has no job posting")

        return role_data

    def _fetch_job_posting(self, role_data: Dict[str, Any], role_id: str, user_id: str) -> None:
        """
        Fetch job posting for a role.
        """
        # Check for job posting in various fields
        job_posting = None

        # Check if role already has a job posting in the main document
        if 'jobPosting' in role_data and role_data['jobPosting']:
            job_posting = role_data['jobPosting']
            logging.info(f"Role {role_id} has job posting in 'jobPosting' field")

        # Check for job posting in 'jobDescription' field
        elif 'jobDescription' in role_data and role_data['jobDescription']:
            job_posting = role_data['jobDescription']
            role_data['jobPosting'] = job_posting
            logging.info(f"Role {role_id} has job posting in 'jobDescription' field")

        # Check for job posting in 'description' field
        elif 'description' in role_data and role_data['description']:
            job_posting = role_data['description']
            role_data['jobPosting'] = job_posting
            logging.info(f"Role {role_id} has job posting in 'description' field")

        # If no job posting in the main document, check the jobPostings subcollection
        if not job_posting:
            try:
                # First try to get job posting from the 'current' document in jobPostings subcollection
                job_posting_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('jobPostings').document('current')
                job_posting_doc = job_posting_ref.get()  # Firebase operations are synchronous

                if job_posting_doc.exists:
                    job_posting_data = job_posting_doc.to_dict()
                    logging.info(f"Role {role_id} has job posting in 'current' document")

                    # Check for content in various fields
                    if job_posting_data:
                        self._extract_job_posting_content(role_data, job_posting_data)

                # If still no job posting, check for a 'generated' document
                if 'jobPosting' not in role_data or not role_data['jobPosting']:
                    job_posting_ref = self.db.collection('users').document(user_id).collection('roles').document(role_id).collection('jobPostings').document('generated')
                    job_posting_doc = job_posting_ref.get()  # Firebase operations are synchronous

                    if job_posting_doc.exists:
                        job_posting_data = job_posting_doc.to_dict()
                        logging.info(f"Role {role_id} has job posting in 'generated' document")

                        # Check for content in various fields
                        if job_posting_data:
                            self._extract_job_posting_content(role_data, job_posting_data)

                # If still no job posting, generate a fallback
                if ('jobPosting' not in role_data or not role_data['jobPosting']) and role_data.get('status') == 'Sourcing':
                    role_data['jobPosting'] = self._generate_fallback_job_posting(role_data)
                    logging.info(f"Generated fallback job posting for role {role_id}")
            except Exception as e:
                logging.error(f"Error fetching job posting for role {role_id}: {str(e)}")

    def _extract_job_posting_content(self, role_data: Dict[str, Any], job_posting_data: Dict[str, Any]) -> None:
        """
        Extract job posting content from various fields in the job posting data.
        """
        if 'content' in job_posting_data and job_posting_data['content']:
            role_data['jobPosting'] = job_posting_data['content']
        elif 'markdown' in job_posting_data and job_posting_data['markdown']:
            role_data['jobPosting'] = job_posting_data['markdown']
        elif 'text' in job_posting_data and job_posting_data['text']:
            role_data['jobPosting'] = job_posting_data['text']
        elif 'html' in job_posting_data and job_posting_data['html']:
            role_data['jobPosting'] = job_posting_data['html']
        # If no recognized field, use the entire document as the job posting
        else:
            role_data['jobPosting'] = str(job_posting_data)

    # New methods for handling public interviews

    async def get_public_role(self, role_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a role that's publicly accessible (has public templates)

        Parameters:
        - role_id: The ID of the role to retrieve

        Returns:
        - role: The role data or None if not found/not public
        """
        try:
            logging.info(f"Getting public role with ID: {role_id}")

            # First check in the 'jobs' collection for public roles
            job_ref = self.db.collection("jobs").document(role_id)
            job_doc = job_ref.get()  # Firebase operations are synchronous

            if job_doc.exists:
                logging.info(f"Role {role_id} found in jobs collection")
                role_data = job_doc.to_dict()
                role_data["id"] = role_id
                return self._filter_role_for_public_access(role_data)

            # If not found in 'jobs', try 'roles' collection
            role_ref = self.db.collection("roles").document(role_id)
            role_doc = role_ref.get()  # Firebase operations are synchronous

            if role_doc.exists:
                logging.info(f"Role {role_id} found in roles collection")
                role_data = role_doc.to_dict()
                role_data["id"] = role_id

                # Check if the role has is_public flag or has public templates
                if role_data.get("is_public", False):
                    logging.info(f"Role {role_id} is explicitly marked public")
                    return self._filter_role_for_public_access(role_data)

                # Check if it has public templates
                templates = await self.get_public_templates_for_role(role_id)
                has_public_template = len(templates) > 0

                if has_public_template:
                    logging.info(f"Role {role_id} has {len(templates)} public template(s)")
                    return self._filter_role_for_public_access(role_data)

            # If still not found, try to get it from a user's roles collection
            try:
                role = await self.get_role_by_id(role_id)
                if role:
                    logging.info(f"Role {role_id} found via get_role_by_id")
                    return self._filter_role_for_public_access(role)
            except Exception as e:
                logging.error(f"Error in fallback to get_role_by_id: {str(e)}")

            logging.warning(f"Role {role_id} not found")
            return None
        except Exception as e:
            logging.error(f"Error getting public role: {str(e)}")
            return None

    def _filter_role_for_public_access(self, role_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter sensitive information from role data for public access
        """
        # Create a copy to avoid modifying the original
        public_role = dict(role_data)

        # Remove sensitive fields
        sensitive_fields = [
            "hiringManagerId",
            "hiringManagerContact",
            "internal_notes",
            "keyStakeholders",
            "compensation",
            "benefits",
            "aboutCompany",
            "aboutTeam",
            "user_id",
            "userId"
        ]

        for field in sensitive_fields:
            if field in public_role:
                del public_role[field]

        return public_role

    async def get_public_templates_for_role(self, role_id: str) -> List[Dict[str, Any]]:
        """
        Get all public templates for a role

        Parameters:
        - role_id: The ID of the role

        Returns:
        - templates: List of public templates
        """
        try:
            logging.info(f"Getting public templates for role {role_id}")

            # First, find the role to get the user ID
            # No need to use await since get_role_by_id will handle its own async operations
            role = await self.get_role_by_id(role_id)
            if not role:
                logging.warning(f"Role {role_id} not found when trying to get templates")
                return []

            user_id = role.get('userId')
            if not user_id:
                logging.warning(f"Role {role_id} doesn't have a user ID, can't get templates")
                return []

            # Get templates from the correct location
            templates_ref = self.db.collection("users").document(user_id).collection("roles").document(role_id).collection("interviewTemplates")
            templates_docs = templates_ref.stream()  # Firebase operations are synchronous

            templates = []
            for doc in templates_docs:
                template_data = doc.to_dict()
                template_data["id"] = doc.id

                # For now, consider all templates as public for the purpose of instant interview
                # But still filter any sensitive information
                template_data = self._filter_template_for_public_access(template_data)
                templates.append(template_data)

            logging.info(f"Found {len(templates)} templates for role {role_id}")

            # If no templates found, create a default one
            if not templates:
                logging.info(f"No templates found for role {role_id}, returning empty list")

            return templates
        except Exception as e:
            logging.error(f"Error getting public templates for role {role_id}: {str(e)}", exc_info=True)
            return []

    def _filter_template_for_public_access(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter sensitive information from template data for public access
        """
        # Create a copy to avoid modifying the original
        public_template = dict(template_data)

        # Remove sensitive fields
        sensitive_fields = [
            "internal_notes",
            "user_id",
            "userId",
            "createdBy",
            "internalEvaluationNotes"
        ]

        for field in sensitive_fields:
            if field in public_template:
                del public_template[field]

        return public_template

    async def get_public_screening_template(self, role_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the screening template for a role that's publicly accessible

        Parameters:
        - role_id: The ID of the role

        Returns:
        - template: The template data or None if not found/not public
        """
        try:
            logging.info(f"Getting public screening template for role {role_id}")

            # Get all public templates
            public_templates = await self.get_public_templates_for_role(role_id)

            # Filter for screening templates
            screening_templates = [
                t for t in public_templates
                if t.get("stage", "").lower() == "screening"
            ]

            if not screening_templates:
                logging.warning(f"No public screening templates found for role {role_id}")
                return None

            # Return the first public screening template
            logging.info(f"Found public screening template for role {role_id}: {screening_templates[0]['id']}")
            return screening_templates[0]
        except Exception as e:
            logging.error(f"Error getting public screening template: {str(e)}")
            return None

    async def get_template(self, role_id: str, template_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific template for an authenticated user

        Parameters:
        - role_id: The ID of the role
        - template_id: The ID of the template
        - user_id: The ID of the user who owns the role

        Returns:
        - template: The template data or None if not found
        """
        try:
            logging.info(f"Getting template {template_id} for role {role_id} (user {user_id})")

            # Get template from the correct location
            template_ref = self.db.collection("users").document(user_id).collection("roles").document(role_id).collection("interviewTemplates").document(template_id)
            template_doc = template_ref.get()  # Firebase operations are synchronous

            if not template_doc.exists:
                logging.warning(f"Template {template_id} not found for role {role_id}")
                return None

            template_data = template_doc.to_dict()
            template_data["id"] = template_id

            logging.info(f"Found template {template_id} for role {role_id}")
            return template_data
        except Exception as e:
            logging.error(f"Error getting template: {str(e)}", exc_info=True)
            return None

    async def get_public_template(self, role_id: str, template_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific template that's publicly accessible

        Parameters:
        - role_id: The ID of the role
        - template_id: The ID of the template

        Returns:
        - template: The template data or None if not found/not public
        """
        try:
            logging.info(f"Getting public template {template_id} for role {role_id}")

            # First, find the role to get the user ID
            role = await self.get_role_by_id(role_id)
            if not role:
                logging.warning(f"Role {role_id} not found when trying to get template")
                return None

            user_id = role.get('userId')
            if not user_id:
                logging.warning(f"Role {role_id} doesn't have a user ID, can't get template")
                return None

            # Get template from the correct location
            template_ref = self.db.collection("users").document(user_id).collection("roles").document(role_id).collection("interviewTemplates").document(template_id)
            template_doc = template_ref.get()  # Firebase operations are synchronous

            if not template_doc.exists:
                logging.warning(f"Template {template_id} not found for role {role_id}")
                return None

            template_data = template_doc.to_dict()
            template_data["id"] = template_id

            # For now, consider all templates as public for the purpose of instant interview
            # But still filter any sensitive information
            logging.info(f"Found template {template_id} for role {role_id}")
            return self._filter_template_for_public_access(template_data)
        except Exception as e:
            logging.error(f"Error getting public template: {str(e)}", exc_info=True)
            return None

    def _normalize_template(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """
        Ensure a template has all the required fields with proper defaults

        Parameters:
        - template: The template data to normalize

        Returns:
        - The normalized template data
        """
        if not template:
            return template

        # Ensure ID is present
        if "id" not in template:
            template["id"] = str(uuid.uuid4())

        # Ensure stage is present
        if "stage" not in template or not template["stage"]:
            template["stage"] = "Screening"

        # Ensure stageIndex is present
        if "stageIndex" not in template:
            template["stageIndex"] = 0

        # Ensure duration is present
        if "duration" not in template or not template["duration"]:
            template["duration"] = "30 minutes"

        # Ensure customInstructions is present
        if "customInstructions" not in template:
            template["customInstructions"] = ""

        # Ensure questions is a list
        if "questions" not in template or not isinstance(template["questions"], list):
            template["questions"] = []

        # Normalize each question
        for i, question in enumerate(template["questions"]):
            if not isinstance(question, dict):
                template["questions"][i] = {
                    "id": str(uuid.uuid4()),
                    "question": str(question) if question else "Tell me about your experience",
                    "purpose": "To assess candidate qualifications",
                    "idealAnswerCriteria": "Clear, relevant response"
                }
                continue

            # Ensure question ID
            if "id" not in question:
                question["id"] = str(uuid.uuid4())

            # Ensure question text
            if "question" not in question or not question["question"]:
                question["question"] = "Tell me about your relevant experience"

            # Ensure purpose
            if "purpose" not in question or not question["purpose"]:
                question["purpose"] = "To assess candidate qualifications"

            # Ensure idealAnswerCriteria
            if "idealAnswerCriteria" not in question or not question["idealAnswerCriteria"]:
                question["idealAnswerCriteria"] = "Clear, relevant response"

        return template

    async def get_screening_template(self, role_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get the screening template for a role - works for both authenticated and public use cases

        Parameters:
        - role_id: The ID of the role
        - user_id: Optional user ID. If not provided, will be determined from the role

        Returns:
        - template: The template data or None if not found
        """
        try:
            logging.info(f"Getting screening template for role {role_id}")

            # First, determine the user ID if not provided
            if not user_id:
                # Try to find role to get the user ID
                role = await self.get_role_by_id(role_id)
                if not role:
                    logging.warning(f"Role {role_id} not found when trying to get screening template")
                    return None

                user_id = role.get('userId')
                if not user_id:
                    logging.warning(f"Role {role_id} doesn't have a user ID, can't get screening template")
                    return None

                logging.info(f"Determined user ID {user_id} from role {role_id}")

            # Get all templates for the role
            templates_ref = (self.db.collection("users")
                             .document(user_id)
                             .collection("roles")
                             .document(role_id)
                             .collection("interviewTemplates"))

            templates_docs = templates_ref.stream()  # Firebase operations are synchronous

            # Find the screening template (stageIndex = 0 or stage = "Screening")
            screening_templates = []
            all_templates = []

            for doc in templates_docs:
                template_data = doc.to_dict()
                template_data["id"] = doc.id

                # Normalize the template
                template_data = self._normalize_template(template_data)

                all_templates.append(template_data)

                # Check if this is a screening template
                stage = template_data.get("stage", "").lower()
                stage_index = template_data.get("stageIndex")

                # Various ways to identify a screening template
                is_screening = False

                if stage == "screening":
                    is_screening = True
                elif stage_index == 0:
                    is_screening = True
                elif "screening" in stage:
                    is_screening = True
                elif template_data.get("type", "").lower() == "screening":
                    is_screening = True

                if is_screening:
                    screening_templates.append(template_data)

            if not screening_templates:
                logging.warning(f"No explicit screening templates found for role {role_id}")

                # If no explicit screening templates, use the first template if available
                if all_templates:
                    template = all_templates[0]
                    logging.info(f"Using first available template as screening template: {template['id']}")
                    return template

                return None

            # Sort screening templates to prefer those with more questions
            screening_templates.sort(key=lambda t: len(t.get("questions", [])), reverse=True)

            # Return the first screening template (with most questions)
            template = screening_templates[0]
            logging.info(f"Found screening template for role {role_id}: {template['id']} with {len(template.get('questions', []))} questions")
            return template

        except Exception as e:
            logging.error(f"Error getting screening template: {str(e)}", exc_info=True)
            return None