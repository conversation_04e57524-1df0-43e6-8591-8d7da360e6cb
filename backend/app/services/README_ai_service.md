# AI Service Documentation

## Overview

The AI Service is a core component of Recruiva's backend that manages all AI-related operations, including:

- Email content processing with function calling
- Role creation and management via AI
- Chat completions for various use cases
- Prompt management and templating
- Function registry for structured AI operations

The service is implemented as a singleton to ensure consistent state management and resource utilization across the application.

## Architecture

### Core Components

#### 1. AI Service Core (`ai_service.py`)
- Singleton implementation for consistent state
- Integration with OpenAI API
- Function registry for structured operations
- Email processing pipeline
- Role management functions

#### 2. OpenAI Integration
- `chat_completion_service.py`: High-level service for chat completions
- `function_registry.py`: Registry for OpenAI function definitions
- `models/chat.py`: Chat model implementation with fallback logic
- `base.py`: Base OpenAI client with error handling and retry logic

#### 3. Prompt Management
- `prompts/manager.py`: Prompt loading and templating
- Various prompt files for different use cases:
  - `email_agent.prompt`: Email agent system prompt
  - `role_enrichment.prompt`: Role enrichment prompt
  - `job_posting.prompt`: Job posting generation prompt

### Implementation Details

#### Singleton Pattern
The AIService is implemented as a singleton to ensure only one instance exists throughout the application lifecycle:

```python
class AIService:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AIService, cls).__new__(cls)
        return cls._instance
```

#### Email Processing
The service processes emails by:
1. Formatting the email content with context
2. Loading the email agent prompt
3. Calling the OpenAI API with function definitions
4. Processing the response and executing functions
5. Generating appropriate email responses

```python
async def process_email(
    self,
    user_message: str,
    metadata: Dict[str, Any],
    temperature: float = 0.7,
    max_tokens: int = 2048,
    presence_penalty: float = 0.0,
    frequency_penalty: float = 0.0
) -> Dict[str, Any]:
    """Process an email message with OpenAI API and execute appropriate functions."""
    # Format the user message with context
    formatted_message = f"""
    Email Content:
    {user_message}

    Context:
    """
    # Add all metadata as context
    for key, value in metadata.items():
        if key == 'previous_messages':
            continue
        
        if isinstance(value, dict):
            formatted_message += f"- {key}:\n"
            for sub_key, sub_value in value.items():
                formatted_message += f"  - {sub_key}: {sub_value}\n"
        else:
            formatted_message += f"- {key}: {value}\n"
    
    # Check for available functions
    available_functions = {
        "create_role": self._handle_role_creation,
        "get_roles": self._handle_get_roles,
        "get_role_details": self._handle_get_role_details,
        "update_role": self._handle_role_update
    }
    
    # Get the appropriate model for email processing from configuration
    email_model = ModelConfigurationManager.get_appropriate_model("email_agent")
    
    # Prepare functions for the API
    functions = self.function_registry.get_functions()
    
    # Load the system prompt for email agent
    system_prompt = self._load_system_prompt("email_agent")
    
    # Create messages array
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": formatted_message}
    ]
    
    # Use the ChatClient directly for more control
    chat_client = ChatClient(use_case="email_agent")
    response = await chat_client.process_with_function_calling(
        messages=messages,
        functions=functions,
        model=email_model,
        function_call="auto",
        temperature=temperature,
        max_tokens=max_tokens,
        presence_penalty=presence_penalty,
        frequency_penalty=frequency_penalty
    )
    
    # Extract content from response
    message = response.choices[0].message
    
    # Check for function calls
    function_call = None
    function_name = None
    function_args = {}
    has_function_call = False
    
    # Check for tool_calls (new API format)
    if hasattr(message, 'tool_calls') and message.tool_calls:
        for tool_call in message.tool_calls:
            if tool_call.type == 'function':
                function_call = tool_call.function
                function_name = function_call.name
                has_function_call = True
                function_args = json.loads(function_call.arguments)
                break
    # Check for function_call (old API format)
    elif hasattr(message, 'function_call') and message.function_call:
        function_call = message.function_call
        function_name = function_call.name
        has_function_call = True
        function_args = json.loads(function_call.arguments)
    
    # If function call is detected and it's a valid function, execute it
    if has_function_call and function_name and function_name in available_functions:
        # Add user_id to function arguments if not present
        if 'user_id' not in function_args and user_id != 'unknown':
            function_args['user_id'] = user_id
        
        # Add metadata to function arguments
        function_args['metadata'] = metadata
        
        # Execute function and get result
        function_to_call = available_functions[function_name]
        function_result = await function_to_call(function_args)
        
        # Return result with action
        return {
            'action': function_name,
            'result': function_result,
            'content': message.content
        }
    else:
        # No function call detected or invalid function, return the content directly
        action = metadata.get('action', 'direct_response')
        
        return {
            'action': action,
            'result': {
                'status': 'success',
                'message': 'Processed without function call'
            },
            'content': message.content
        }
```

#### Function Calling
The service implements function handlers for various operations:

```python
async def _handle_role_creation(self, params: Dict[str, Any]) -> Dict[str, Any]:
    """Handle role creation function call."""
    # Extract role data from parameters
    role_data = {k: v for k, v in params.items() if k not in ['metadata', 'user_id']}
    
    # Extract user_id and metadata
    user_id = params.get('user_id')
    metadata = params.get('metadata', {})
    
    # Add user_id to role_data
    role_data['user_id'] = user_id
    
    # Create the role using RolesService
    role = await self.roles_service.create_role(role_data)
    
    # Get the role ID
    role_id = role.get('id')
    
    # Create base response with role details
    base_url = "https://www.recruiva.ai"
    role_url = f"{base_url}/roles/{role_id}"
    
    # Create success response
    response = {
        'status': 'success',
        'role': role,
        'role_id': role_id,
        'role_url': role_url,
        'message': f'Role created successfully with ID: {role_id}'
    }
    
    return response
```

#### Prompt Management
The service uses the `PromptManager` to load and format prompts:

```python
def _load_system_prompt(self, prompt_name: str, context: Dict[str, Any] = None) -> str:
    """Load a system prompt with optional context variables."""
    try:
        # Use the PromptManager to load the prompt
        prompt = PromptManager.load_prompt(prompt_name, context=context)
        return prompt
    except Exception as e:
        logging.error(f"Error loading system prompt {prompt_name}: {str(e)}")
        # Return default system prompt as fallback
        return DEFAULT_SYSTEM_PROMPT
```

## Configuration

### Environment Variables
```env
# OpenAI Configuration
OPENAI_API_KEY="your-api-key"
OPENAI_DEFAULT_MODEL="gpt-4o"
OPENAI_FALLBACK_MODEL="gpt-4o-mini"
OPENAI_TIMEOUT_SECONDS=30
OPENAI_MAX_RETRIES=3
```

### Model Configuration
```python
OPENAI_MODELS_CONFIG = {
    "chat": {
        "default": "gpt-4o",
        "fallback": "gpt-4o-mini"
    },
    "email_agent": {
        "default": "gpt-4o",
        "fallback": "gpt-4o-mini"
    }
}
```

## Usage Examples

### Processing an Email
```python
ai_service = AIService()
response = await ai_service.process_email(
    user_message="Hi, I need to create a new role for a Senior Software Engineer.",
    metadata={
        "user_id": "user123",
        "sender": "<EMAIL>",
        "sender_name": "User",
        "subject": "New Role Request"
    }
)
```

### Generating a Job Posting
```python
ai_service = AIService()
job_posting = await ai_service.generate_job_posting(
    role={
        "title": "Senior Software Engineer",
        "summary": "Leading backend development",
        "requiredSkills": {"1": "Python", "2": "Cloud technologies"}
    },
    transcript={
        "content": "We need someone with strong leadership skills who can mentor junior developers."
    }
)
```

### Using the Chat Completion Service
```python
from app.services.openai.chat_completion_service import ChatCompletionService

response = await ChatCompletionService.generate_completion(
    prompt="You are a helpful assistant",
    model="gpt-4o",
    use_case="general"
)
```

## Error Handling

The AI Service implements comprehensive error handling:

1. **API Errors**
   - Automatic retry with exponential backoff
   - Detailed error logging
   - Graceful degradation

2. **Token Limits**
   - Model fallback mechanisms
   - Chunking of large inputs
   - Streaming for large outputs

3. **Function Calling Errors**
   - Parameter validation
   - Error reporting
   - Fallback to direct responses

## Integration with Other Services

The AI Service integrates with:

1. **EmailService**
   - For processing email content
   - For generating email responses

2. **RolesService**
   - For role creation and management
   - For role data retrieval

3. **FirebaseService**
   - For data persistence
   - For user validation

## Best Practices

1. **Model Usage**
   - Use the appropriate model for each use case
   - Implement fallback mechanisms
   - Set appropriate parameters (temperature, max_tokens, etc.)

2. **Prompt Engineering**
   - Keep prompts modular and reusable
   - Use context variables for dynamic content
   - Include examples for better results

3. **Function Calling**
   - Define clear function signatures
   - Validate parameters before execution
   - Handle errors gracefully
   - Return structured responses

4. **Error Handling**
   - Implement proper error handling
   - Log errors for debugging
   - Provide helpful error messages
   - Implement fallback mechanisms 