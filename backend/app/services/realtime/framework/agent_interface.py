"""
Defines the formal interface that all agents must implement.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional

class AgentInterface(ABC):
    """
    Abstract base class defining the interface for all agents.
    All agent implementations must inherit from this class and implement its methods.
    """
    
    @abstractmethod
    async def initialize(self, context: Dict[str, Any]) -> None:
        """
        Initialize the agent with context information.
        
        Args:
            context: Dictionary containing initialization context
        """
        pass
    
    @abstractmethod
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a message and generate a response.
        
        Args:
            message: The message to process
            
        Returns:
            Response dictionary
        """
        pass
    
    @abstractmethod
    async def get_functions(self) -> List[Dict[str, Any]]:
        """
        Get the list of functions this agent can use.
        
        Returns:
            List of function definitions
        """
        pass
    
    @abstractmethod
    async def handle_function_call(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle a function call from the model.
        
        Args:
            function_name: Name of the function to call
            arguments: Function arguments
            
        Returns:
            Function result
        """
        pass
    
    @abstractmethod
    async def terminate(self) -> None:
        """
        Clean up resources when the agent is no longer needed.
        """
        pass
    
    @property
    @abstractmethod
    def agent_type(self) -> str:
        """
        Get the type of this agent.
        
        Returns:
            Agent type string
        """
        pass
    
    @property
    @abstractmethod
    def model(self) -> str:
        """
        Get the model used by this agent.
        
        Returns:
            Model identifier
        """
        pass 