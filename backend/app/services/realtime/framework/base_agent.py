"""
Base agent implementation with common functionality.
"""

import logging
from typing import Dict, Any, List, Optional
from .agent_interface import AgentInterface
from ...openai.models.chat import ChatClient
from ...openai.config.model_configs import ModelConfigurationManager

logger = logging.getLogger(__name__)

class BaseAgent(AgentInterface):
    """
    Base agent implementation with common functionality.
    Specific agent types should inherit from this class.
    """

    def __init__(self, agent_type: str, model: Optional[str] = None):
        """
        Initialize the base agent.

        Args:
            agent_type: Type identifier for this agent
            model: Optional model override (defaults to configuration)
        """
        self._agent_type = agent_type
        # Use the default model from settings if not specified
        self._model = model or ModelConfigurationManager.get_appropriate_model(agent_type)
        self._context = {}
        self._initialized = False
        self._chat_client = None

        logger.info(f"Created {agent_type} agent with model {self._model}")

    async def initialize(self, context: Dict[str, Any]) -> None:
        """
        Initialize the agent with context information.

        Args:
            context: Dictionary containing initialization context
        """
        self._context = context

        # Initialize the chat client with the appropriate use case
        self._chat_client = ChatClient(use_case=self._agent_type)

        # Override model if specified in context
        if 'model' in context:
            self._model = context['model']

        self._initialized = True
        logger.info(f"Initialized {self._agent_type} agent with context: {list(context.keys())}")

    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a message and generate a response.

        Args:
            message: The message to process

        Returns:
            Response dictionary
        """
        if not self._initialized:
            raise RuntimeError("Agent not initialized")

        # This is a base implementation that should be overridden
        logger.warning("Using base implementation of process_message")
        return {"error": "Not implemented in base class"}

    async def get_functions(self) -> List[Dict[str, Any]]:
        """
        Get the list of functions this agent can use.

        Returns:
            List of function definitions
        """
        # This should be overridden by subclasses
        logger.warning("Using base implementation of get_functions")
        return []

    async def handle_function_call(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle a function call from the model.

        Args:
            function_name: Name of the function to call
            arguments: Function arguments

        Returns:
            Function result
        """
        # This should be overridden by subclasses
        logger.warning(f"No handler for function {function_name}")
        return {"error": f"Function {function_name} not implemented"}

    async def terminate(self) -> None:
        """
        Clean up resources when the agent is no longer needed.
        """
        logger.info(f"Terminating {self._agent_type} agent")
        self._initialized = False
        self._context = {}

    @property
    def agent_type(self) -> str:
        """
        Get the type of this agent.

        Returns:
            Agent type string
        """
        return self._agent_type

    @property
    def model(self) -> str:
        """
        Get the model used by this agent.

        Returns:
            Model identifier
        """
        return self._model

    @property
    def context(self) -> Dict[str, Any]:
        """
        Get the agent's context.

        Returns:
            Context dictionary
        """
        return self._context.copy()  # Return a copy to prevent modification