"""
Factory for creating and initializing agents.
"""

import importlib
import logging
from typing import Dict, Any, Optional, Type
from .agent_interface import AgentInterface

logger = logging.getLogger(__name__)

class AgentFactory:
    """
    Factory for creating and initializing agent instances.
    Uses lazy loading to only load agent components when needed.
    """
    
    # Registry of agent implementations
    _agent_registry = {}
    
    @classmethod
    def register_agent(cls, agent_type: str, agent_class: Type[AgentInterface]) -> None:
        """
        Register an agent implementation.
        
        Args:
            agent_type: Type identifier for the agent
            agent_class: Agent class that implements AgentInterface
        """
        cls._agent_registry[agent_type] = agent_class
        logger.info(f"Registered agent type: {agent_type}")
    
    @classmethod
    async def create_agent(cls, agent_type: str, context: Optional[Dict[str, Any]] = None) -> AgentInterface:
        """
        Create and initialize an agent instance.
        
        Args:
            agent_type: Type of agent to create
            context: Optional initialization context
            
        Returns:
            Initialized agent instance
            
        Raises:
            ValueError: If agent_type is not registered
        """
        context = context or {}
        
        # Check if agent type is registered
        if agent_type in cls._agent_registry:
            agent_class = cls._agent_registry[agent_type]
            logger.info(f"Creating agent of type {agent_type} from registry")
        else:
            # Try to dynamically load the agent class
            try:
                logger.info(f"Attempting to dynamically load agent type: {agent_type}")
                module_path = f"app.services.realtime.agents.{agent_type}.agent"
                module = importlib.import_module(module_path)
                
                # Look for a class that implements AgentInterface
                agent_class = None
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if isinstance(attr, type) and issubclass(attr, AgentInterface) and attr != AgentInterface:
                        agent_class = attr
                        break
                
                if not agent_class:
                    raise ValueError(f"No agent implementation found in {module_path}")
                
                # Register for future use
                cls.register_agent(agent_type, agent_class)
                
            except (ImportError, AttributeError) as e:
                logger.error(f"Failed to load agent type {agent_type}: {str(e)}")
                raise ValueError(f"Unknown agent type: {agent_type}")
        
        # Create and initialize the agent
        agent = agent_class()
        await agent.initialize(context)
        
        return agent
    
    @classmethod
    def get_available_agent_types(cls) -> list[str]:
        """
        Get a list of all registered agent types.
        
        Returns:
            List of agent type strings
        """
        return list(cls._agent_registry.keys()) 