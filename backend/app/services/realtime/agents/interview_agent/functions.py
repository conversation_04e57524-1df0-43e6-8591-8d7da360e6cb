"""
Functions for the Interview Agent.
This module defines the functions available to the Interview Agent during realtime sessions.
"""

# Define the realtime functions for the interview agent
realtime_functions = [
    {
        "type": "function",
        "name": "return_interview_results",
        "description": "Return the complete interview results with candidate responses and evaluation notes.",
        "parameters": {
            "type": "object",
            "properties": {
                "candidateResponses": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "questionId": {
                                "type": "string",
                                "description": "The ID of the question that was asked."
                            },
                            "question": {
                                "type": "string",
                                "description": "The question that was asked."
                            },
                            "response": {
                                "type": "string",
                                "description": "The candidate's response to the question."
                            },
                            "notes": {
                                "type": "string",
                                "description": "Observations about the quality of the response."
                            }
                        },
                        "required": ["questionId", "question", "response", "notes"]
                    },
                    "description": "Array of candidate responses to interview questions."
                },
                "evaluationNotes": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    },
                    "description": "Notes on how the candidate performed against each evaluation criterion. Keys are criterion IDs, values are notes."
                },
                "overallImpression": {
                    "type": "string",
                    "description": "Overall impression of the candidate based on the interview."
                },
                "recommendedNextSteps": {
                    "type": "string",
                    "description": "Recommendation for next steps in the hiring process."
                }
            },
            "required": ["candidateResponses", "evaluationNotes", "overallImpression", "recommendedNextSteps"]
        }
    },
    {
        "type": "function",
        "name": "end_the_interview",
        "description": "Signal the end of the interview.",
        "parameters": {
            "type": "object",
            "properties": {
                "reason": {
                    "type": "string",
                    "description": "The reason for ending the interview."
                }
            },
            "required": ["reason"]
        }
    },
    {
        "type": "function",
        "name": "signal_end_call",
        "description": "Signal the end of the interview (alternative name for compatibility).",
        "parameters": {
            "type": "object",
            "properties": {
                "reason": {
                    "type": "string",
                    "description": "Optional reason for ending the call."
                }
            },
            "required": []
        }
    }
] 