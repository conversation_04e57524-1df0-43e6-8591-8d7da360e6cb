"""
Context for the Interview Agent.
This module defines the context and standardized values used by the Interview Agent.
"""

# Define standardized values for data validation
INTERVIEW_STAGES = [
    "Screening",
    "Cultural & Company fit",
    "Behavioral",
    "Domain Expert",
    "Technical Challenge with Code",
    "Home Assignment",
    "Experience & Role fit",
    "Advance Problem Solving",
    "Team Ethics"
]

# Define context for prompt formatting
DEFAULT_CONTEXT = {
    "role_title": "Software Engineer",
    "role_level": "Senior",
    "role_summary": "This role involves developing and maintaining software applications.",
    "required_skills": "Python, JavaScript, SQL",
    "preferred_skills": "React, AWS, Docker",
    "years_of_experience": "5+ years",
    "interview_stage": "Technical Challenge with Code",
    "interview_duration": "45 minutes",
    "custom_instructions": "Focus on problem-solving abilities and technical knowledge.",
    "interview_questions": [
        {
            "id": "q1",
            "question": "Tell me about your experience with Python.",
            "purpose": "To assess technical knowledge and experience.",
            "idealAnswerCriteria": "Should mention specific projects, libraries, and problem-solving examples."
        }
    ],
    "evaluation_criteria": [
        {
            "id": "c1",
            "type": "ScoreCard",
            "criteria": "Technical Knowledge",
            "description": "Understanding of technical concepts and ability to explain them clearly.",
            "competency": "Technical Skills",
            "weight": 5
        }
    ]
}

# Define validation rules
VALIDATION_RULES = {
    "interviewStages": INTERVIEW_STAGES
} 