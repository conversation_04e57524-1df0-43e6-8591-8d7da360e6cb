"""
Services for the Interview Agent.
This module provides services for the Interview Agent, including validation, normalization,
error handling, and transcript processing.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import uuid

from app.services.firebase_service import FirebaseService
from app.services.roles_service import RolesService
from app.services.realtime.agents.common.services import safe_json_loads
from app.services.templates_service import TemplatesService

# Set up logging
logger = logging.getLogger(__name__)

# Initialize services
firebase_service = FirebaseService()
roles_service = RolesService()
templates_service = TemplatesService()

async def complete_transcript(user_id: str, role_id: str, transcript_id: str) -> None:
    """
    Mark a transcript as completed.
    
    Args:
        user_id: The ID of the user
        role_id: The ID of the role
        transcript_id: The ID of the transcript
    """
    logger.info(f"Marking interview transcript {transcript_id} as completed")
    await firebase_service.complete_interview_transcript(
        user_id=user_id,
        role_id=role_id,
        transcript_id=transcript_id
    )

async def get_transcript(user_id: str, role_id: str, transcript_id: str) -> Dict[str, Any]:
    """
    Get a transcript.
    
    Args:
        user_id: The ID of the user
        role_id: The ID of the role
        transcript_id: The ID of the transcript
        
    Returns:
        The transcript
    """
    logger.info(f"Retrieving interview transcript {transcript_id}")
    return await firebase_service.get_interview_transcript(
        user_id=user_id,
        role_id=role_id,
        transcript_id=transcript_id
    )

async def save_interview_results(user_id: str, role_id: str, transcript_id: str, results: Dict[str, Any]) -> Dict[str, Any]:
    """
    Save interview results.
    
    Args:
        user_id: The ID of the user
        role_id: The ID of the role
        transcript_id: The ID of the transcript
        results: The interview results
        
    Returns:
        The saved results
    """
    logger.info(f"Saving interview results for transcript {transcript_id}")
    
    try:
        # Validate results structure
        if not isinstance(results, dict):
            logger.error(f"Invalid results format: {type(results)}")
            raise ValueError("Results must be a dictionary")
        
        required_fields = ["candidateResponses", "evaluationNotes", "overallImpression", "recommendedNextSteps"]
        for field in required_fields:
            if field not in results:
                logger.error(f"Missing required field in results: {field}")
                raise ValueError(f"Results missing required field: {field}")
        
        # Save results to Firebase
        saved_results = await firebase_service.save_interview_results(
            user_id=user_id,
            role_id=role_id,
            transcript_id=transcript_id,
            results=results
        )
        
        logger.info(f"Successfully saved interview results for transcript {transcript_id}")
        return saved_results
    
    except Exception as e:
        logger.error(f"Error saving interview results: {str(e)}", exc_info=True)
        raise

async def get_interview_context(role_id: str, template_id: str, user_id: str) -> Dict[str, Any]:
    """
    Build context for the interview agent.
    
    Args:
        role_id: The ID of the role
        template_id: The ID of the template
        user_id: The ID of the user
        
    Returns:
        Context dictionary for the interview agent
    """
    logger.info(f"Building interview context for role {role_id} and template {template_id}")
    
    try:
        # Get role details
        role = await roles_service.get_role(role_id, user_id)
        if not role:
            logger.error(f"Role {role_id} not found")
            raise ValueError(f"Role {role_id} not found")
        
        # Get template details
        template = await templates_service.get_template(role_id, template_id, user_id)
        if not template:
            logger.error(f"Template {template_id} not found")
            raise ValueError(f"Template {template_id} not found")
        
        # Find the interview stage in the role's interview process
        stage_info = None
        if role.get("interviewProcess") and isinstance(role["interviewProcess"], list):
            for i, stage in enumerate(role["interviewProcess"]):
                if i == template.get("stageIndex"):
                    stage_info = stage
                    break
        
        if not stage_info:
            logger.warning(f"Interview stage not found for template {template_id}, using template data")
            stage_info = {
                "stage": template.get("stage", "Unknown"),
                "duration": template.get("duration", "30 minutes"),
                "customInstructions": template.get("customInstructions", "")
            }
        
        # Format required skills
        required_skills_str = ""
        if role.get("requiredSkills") and isinstance(role["requiredSkills"], dict):
            required_skills_str = ", ".join(role["requiredSkills"].values())
        
        # Format preferred skills
        preferred_skills_str = ""
        if role.get("preferredSkills") and isinstance(role["preferredSkills"], dict):
            preferred_skills_str = ", ".join(role["preferredSkills"].values())
        
        # Format interview questions
        interview_questions_str = ""
        if template.get("questions") and isinstance(template["questions"], list):
            questions = []
            for q in template["questions"]:
                question_str = f"Question ID: {q.get('id', 'unknown')}\n"
                question_str += f"Question: {q.get('question', '')}\n"
                question_str += f"Purpose: {q.get('purpose', '')}\n"
                question_str += f"Ideal Answer Criteria: {q.get('idealAnswerCriteria', '')}\n"
                questions.append(question_str)
            interview_questions_str = "\n\n".join(questions)
        
        # Format evaluation criteria
        evaluation_criteria_str = ""
        if template.get("evaluationCriteria") and isinstance(template["evaluationCriteria"], list):
            criteria = []
            for c in template["evaluationCriteria"]:
                criterion_str = f"Criterion ID: {c.get('id', 'unknown')}\n"
                criterion_str += f"Type: {c.get('type', '')}\n"
                criterion_str += f"Criteria: {c.get('criteria', '')}\n"
                criterion_str += f"Description: {c.get('description', '')}\n"
                
                # Add type-specific fields
                if c.get("type") == "ScoreCard":
                    criterion_str += f"Competency: {c.get('competency', '')}\n"
                    criterion_str += f"Weight: {c.get('weight', 1)}\n"
                
                criteria.append(criterion_str)
            evaluation_criteria_str = "\n\n".join(criteria)
        
        # Format metadata
        metadata_str = f"""
### METADATA (DO NOT REVEAL TO CANDIDATE)
Role ID: {role_id}
Template ID: {template_id}
Agent Type: interview_agent
Session Time: {datetime.now().isoformat()}
"""
        
        # Build context
        context = {
            "role_title": role.get("title", ""),
            "role_level": role.get("level", ""),
            "role_summary": role.get("summary", ""),
            "required_skills": required_skills_str,
            "preferred_skills": preferred_skills_str,
            "years_of_experience": role.get("yearsOfExperience", ""),
            "interview_stage": stage_info.get("stage", ""),
            "interview_duration": stage_info.get("duration", ""),
            "custom_instructions": stage_info.get("customInstructions", ""),
            "interview_questions": interview_questions_str,
            "evaluation_criteria": evaluation_criteria_str,
            "metadata": metadata_str
        }
        
        logger.info(f"Successfully built interview context with {len(context)} fields")
        return context
    
    except Exception as e:
        logger.error(f"Error building interview context: {str(e)}", exc_info=True)
        raise

async def process_interview_results(user_id: str, role_id: str, transcript_id: str) -> Dict[str, Any]:
    """
    Process interview results from a transcript.
    
    Args:
        user_id: The ID of the user
        role_id: The ID of the role
        transcript_id: The ID of the transcript
        
    Returns:
        Processed interview results
    """
    logger.info(f"Processing interview results from transcript {transcript_id}")
    
    try:
        # Get the transcript
        transcript = await get_transcript(user_id, role_id, transcript_id)
        if not transcript:
            logger.error(f"Transcript {transcript_id} not found")
            raise ValueError(f"Transcript {transcript_id} not found")
        
        # Extract function calls from messages
        messages = transcript.get("messages", [])
        results = None
        
        for message in messages:
            if message.get("role") != "assistant":
                continue
            
            function_calls = message.get("function_calls", [])
            for call in function_calls:
                if call.get("name") == "return_interview_results":
                    # Extract results from function call arguments
                    args = call.get("arguments", "{}")
                    try:
                        results = safe_json_loads(args)
                        logger.info(f"Found interview results in transcript: {len(results) if results else 0} fields")
                        break
                    except Exception as e:
                        logger.error(f"Error parsing interview results: {str(e)}", exc_info=True)
            
            if results:
                break
        
        if not results:
            logger.warning(f"No interview results found in transcript {transcript_id}")
            # Create minimal results
            results = {
                "candidateResponses": [],
                "evaluationNotes": {},
                "overallImpression": "No formal evaluation was completed.",
                "recommendedNextSteps": "Consider conducting another interview."
            }
            
            # Try to extract some information from the conversation
            user_messages = [m.get("content", "") for m in messages if m.get("role") == "user"]
            assistant_messages = [m.get("content", "") for m in messages if m.get("role") == "assistant"]
            
            if user_messages and assistant_messages:
                # Create a basic response entry
                results["candidateResponses"].append({
                    "questionId": "auto-generated",
                    "question": "General conversation",
                    "response": "\n".join(user_messages),
                    "notes": "Automatically extracted from conversation."
                })
        
        # Save the results
        saved_results = await save_interview_results(user_id, role_id, transcript_id, results)
        
        return saved_results
    
    except Exception as e:
        logger.error(f"Error processing interview results: {str(e)}", exc_info=True)
        raise

async def create_interview_transcript(user_id: str, role_id: str, transcript_id: str, transcript_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a new interview transcript.
    
    Args:
        user_id: The ID of the user
        role_id: The ID of the role
        transcript_id: The ID of the transcript
        transcript_data: The transcript data
        
    Returns:
        The created transcript
    """
    logger.info(f"Creating interview transcript {transcript_id} for role {role_id}")
    
    try:
        # Create the transcript in Firebase
        transcript = await firebase_service.create_interview_transcript(
            user_id=user_id,
            role_id=role_id,
            transcript_id=transcript_id,
            transcript_data=transcript_data
        )
        
        logger.info(f"Successfully created interview transcript {transcript_id}")
        return transcript
    
    except Exception as e:
        logger.error(f"Error creating interview transcript: {str(e)}", exc_info=True)
        raise

async def update_interview_transcript(user_id: str, role_id: str, transcript_id: str, transcript_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Update an existing interview transcript.
    
    Args:
        user_id: The ID of the user
        role_id: The ID of the role
        transcript_id: The ID of the transcript
        transcript_data: The transcript data to update
        
    Returns:
        The updated transcript
    """
    logger.info(f"Updating interview transcript {transcript_id} for role {role_id}")
    
    try:
        # Extract messages from transcript data if present
        updates = {}
        
        # If the entire transcript data is provided, use it
        if transcript_data.get('messages'):
            updates['messages'] = transcript_data.get('messages')
        
        # Add any other fields that should be updated
        if transcript_data.get('status'):
            updates['status'] = transcript_data.get('status')
        
        if transcript_data.get('messageCount'):
            updates['messageCount'] = transcript_data.get('messageCount')
        
        # Update the transcript in Firebase
        await firebase_service.update_interview_transcript(
            user_id=user_id,
            role_id=role_id,
            transcript_id=transcript_id,
            updates=updates or transcript_data  # Use extracted updates or full data as fallback
        )
        
        logger.info(f"Successfully updated interview transcript {transcript_id}")
        
        # Get the updated transcript
        updated_transcript = await get_transcript(user_id, role_id, transcript_id)
        return updated_transcript
    
    except Exception as e:
        logger.error(f"Error updating interview transcript: {str(e)}", exc_info=True)
        raise

async def get_public_interview_context(role_id: str, template_id: str = None, application_id: str = None) -> Dict[str, Any]:
    """
    Build context for the interview agent in a public interview scenario.
    
    Args:
        role_id: The ID of the role
        template_id: Optional ID of the template. If not provided, will look for the screening template
        application_id: Optional ID of the application to include candidate data
        
    Returns:
        Context dictionary for the interview agent
    """
    logger.info(f"Building public interview context for role {role_id}")
    
    try:
        # Get role details using service method
        role = await roles_service.get_role_by_id(role_id)
        
        if not role:
            logger.error(f"Role {role_id} not found")
            raise ValueError(f"Role {role_id} not found")
        
        logger.info(f"Found role: {role.get('title', 'Untitled')} (ID: {role_id})")
        
        # Get application data if application_id is provided
        application_data = None
        resume_text = None
        
        if application_id:
            try:
                # Try to get application data
                logger.info(f"Fetching application data for application ID: {application_id}")
                application_data = await firebase_service.get_application(application_id)
                logger.info(f"Application data found: {application_data is not None}")
                
                # Extract resume data if available
                if application_data:
                    # Try to get resume text from evaluations
                    if "evaluations" in application_data and application_data["evaluations"]:
                        evaluations = application_data["evaluations"]
                        if isinstance(evaluations, list) and len(evaluations) > 0:
                            latest_eval = evaluations[0]
                            resume_text = latest_eval.get("parsedResumeText")
                            
                    # If not in evaluations, check other possible locations
                    if not resume_text and "resumeText" in application_data:
                        resume_text = application_data["resumeText"]
                    
                    if resume_text:
                        logger.info(f"Found resume text in application data (length: {len(resume_text)})")
                    else:
                        logger.warning("No resume text found in application data")
            except Exception as e:
                logger.warning(f"Error fetching application data: {str(e)}")
                # Continue without application data
        
        # Get user ID from role
        user_id = role.get('userId')
        if not user_id:
            logger.warning(f"Role {role_id} doesn't have a userId, some features may not work properly")
            # Create a fallback user ID to avoid errors
            user_id = "public_user"
        
        # Get the template, either the specified one or find the screening template
        template = None
        
        # First, check if we have a specific template ID to use
        if template_id:
            try:
                logger.info(f"Looking for specific template ID: {template_id}")
                template = await roles_service.get_public_template(role_id, template_id)
                if template:
                    logger.info(f"Found template by ID {template_id}")
                else:
                    logger.warning(f"Template {template_id} not found")
            except Exception as e:
                logger.error(f"Error getting template by ID: {str(e)}")
                # Fall through to try screening template
        
        # If no template_id provided or the specific template wasn't found,
        # try to get the screening template using our new method
        if not template:
            try:
                logger.info(f"Looking for screening template for role {role_id}")
                template = await roles_service.get_screening_template(role_id)
                if template:
                    logger.info(f"Found screening template for role {role_id}: {template.get('id')}")
                    template_id = template.get('id')
                else:
                    logger.warning(f"No screening template found for role {role_id} via get_screening_template")
            except Exception as e:
                logger.error(f"Error getting screening template: {str(e)}")
                # Will create default template below
        
        # If still no template, we need to create a default one
        if not template:
            logger.warning(f"No template found, creating default screening template")
            template = {
                "id": template_id or str(uuid.uuid4()),
                "stage": "Screening",
                "stageIndex": 0,
                "questions": [
                    {
                        "id": str(uuid.uuid4()),
                        "question": "Can you tell me about your background and experience relevant to this role?",
                        "purpose": "To understand the candidate's overall experience and fit",
                        "idealAnswerCriteria": "Clear articulation of relevant experience and skills"
                    },
                    {
                        "id": str(uuid.uuid4()),
                        "question": "What are your key strengths that make you suitable for this position?",
                        "purpose": "To assess candidate's self-awareness and relevant skills",
                        "idealAnswerCriteria": "Specific examples of strengths that align with the role requirements"
                    },
                    {
                        "id": str(uuid.uuid4()),
                        "question": "Why are you interested in this position?",
                        "purpose": "To assess motivation and alignment with the role",
                        "idealAnswerCriteria": "Demonstrates genuine interest and understanding of the role"
                    }
                ],
                "duration": "20 minutes",
                "customInstructions": "This is a screening interview to assess basic qualifications and fit for the role."
            }
            # Set the template_id in case it was None
            template_id = template["id"]
            logger.info(f"Created default template with ID: {template_id}")
        
        logger.info(f"Using template: {template.get('stage', 'Unknown')} (ID: {template.get('id', 'unknown')})")
        logger.debug(f"Template structure: keys={list(template.keys())}, questions_count={len(template.get('questions', []))}")
        
        # Find the interview stage in the role's interview process
        stage_info = None
        if role.get("interviewProcess") and isinstance(role["interviewProcess"], list):
            for i, stage in enumerate(role["interviewProcess"]):
                if i == template.get("stageIndex"):
                    stage_info = stage
                    break
            
            if not stage_info:
                logger.debug(f"Interview stages available: {len(role['interviewProcess'])}, template stageIndex: {template.get('stageIndex')}")
        
        if not stage_info:
            logger.warning(f"Interview stage not found for template {template_id}, using template data")
            stage_info = {
                "stage": template.get("stage", "Unknown"),
                "duration": template.get("duration", "30 minutes"),
                "customInstructions": template.get("customInstructions", "")
            }
        
        # Format required skills
        required_skills_str = ""
        if role.get("requiredSkills") and isinstance(role["requiredSkills"], dict):
            skills = [f"{skill}: {level}" for skill, level in role["requiredSkills"].items()]
            required_skills_str = ", ".join(skills)
        
        # Format preferred skills
        preferred_skills_str = ""
        if role.get("preferredSkills") and isinstance(role["preferredSkills"], dict):
            skills = [f"{skill}: {level}" for skill, level in role["preferredSkills"].items()]
            preferred_skills_str = ", ".join(skills)
        
        # Format interview questions
        interview_questions_str = ""
        if template.get("questions") and isinstance(template["questions"], list):
            questions = []
            for q in template["questions"]:
                # Skip empty or invalid questions
                if not q.get("question"):
                    continue
                    
                # Ensure all fields exist with defaults
                question_id = q.get("id", str(uuid.uuid4()))
                question_text = q.get("question", "")
                purpose = q.get("purpose", "To assess candidate's fit for the role")
                ideal_criteria = q.get("idealAnswerCriteria", "Demonstrates relevant experience and skills")
                
                question_str = f"Question ID: {question_id}\n"
                question_str += f"Question: {question_text}\n"
                question_str += f"Purpose: {purpose}\n"
                question_str += f"Ideal Answer Criteria: {ideal_criteria}\n"
                questions.append(question_str)
                
            # If we found no valid questions, create a default set
            if not questions:
                default_questions = [
                    {
                        "id": str(uuid.uuid4()),
                        "question": "Can you tell me about your background and experience relevant to this role?",
                        "purpose": "To understand the candidate's overall experience and fit",
                        "idealAnswerCriteria": "Clear articulation of relevant experience and skills"
                    },
                    {
                        "id": str(uuid.uuid4()),
                        "question": "What are your key strengths that make you suitable for this position?",
                        "purpose": "To assess candidate's self-awareness and relevant skills",
                        "idealAnswerCriteria": "Specific examples of strengths that align with the role requirements"
                    },
                    {
                        "id": str(uuid.uuid4()),
                        "question": "Why are you interested in this position?",
                        "purpose": "To assess motivation and alignment with the role",
                        "idealAnswerCriteria": "Demonstrates genuine interest and understanding of the role"
                    }
                ]
                
                for q in default_questions:
                    question_str = f"Question ID: {q['id']}\n"
                    question_str += f"Question: {q['question']}\n"
                    question_str += f"Purpose: {q['purpose']}\n"
                    question_str += f"Ideal Answer Criteria: {q['idealAnswerCriteria']}\n"
                    questions.append(question_str)
                
                logger.warning("No valid questions found in template, using default questions instead")
                
            interview_questions_str = "\n\n".join(questions)
            logger.info(f"Prepared {len(questions)} interview questions for the context")
        
        # Format evaluation criteria
        evaluation_criteria_str = ""
        if template.get("evaluationCriteria") and isinstance(template["evaluationCriteria"], list):
            criteria = []
            for c in template["evaluationCriteria"]:
                criterion_str = f"Criterion ID: {c.get('id', 'unknown')}\n"
                criterion_str += f"Type: {c.get('type', '')}\n"
                criterion_str += f"Criteria: {c.get('criteria', '')}\n"
                criterion_str += f"Description: {c.get('description', '')}\n"
                
                # Add type-specific fields
                if c.get("type") == "ScoreCard":
                    criterion_str += f"Competency: {c.get('competency', '')}\n"
                    criterion_str += f"Weight: {c.get('weight', 1)}\n"
                
                criteria.append(criterion_str)
            evaluation_criteria_str = "\n\n".join(criteria)
        
        # Format additional public interview instructions
        public_interview_info = """
### SPECIAL INSTRUCTIONS FOR PUBLIC INTERVIEW
This is a public interview for a candidate applying to this role. The candidate has not signed in and is accessing the interview through a public link.

Special handling:
1. Introduce yourself as the AI Interviewer for the role and explain the interview process clearly.
2. Be particularly welcoming and supportive to put the candidate at ease.
3. Explain that this is an initial screening interview for the role.
4. Ask for their name at the beginning of the interview.
5. Let them know their responses will be shared with the hiring team.
6. At the end, thank them and explain what happens next in the process.
"""
        
        # Format metadata
        metadata_str = f"""
### METADATA (DO NOT REVEAL TO CANDIDATE)
Role ID: {role_id}
Template ID: {template_id}
Agent Type: interview_agent
Public Interview: true
Session Time: {datetime.now().isoformat()}
"""
        
        # Build context
        context = {
            "role_title": role.get("title", ""),
            "role_level": role.get("level", ""),
            "role_summary": role.get("summary", ""),
            "required_skills": required_skills_str,
            "preferred_skills": preferred_skills_str,
            "years_of_experience": role.get("yearsOfExperience", ""),
            "interview_stage": stage_info.get("stage", ""),
            "interview_duration": stage_info.get("duration", ""),
            "custom_instructions": stage_info.get("customInstructions", ""),
            "interview_questions": interview_questions_str,
            "evaluation_criteria": evaluation_criteria_str,
            "public_interview_info": public_interview_info,
            
            # Include candidate resume if found in application
            "candidate_resume": resume_text or "No resume data available.",
            
            # Include job posting from role data
            "job_posting": role.get("description", role.get("summary", "No detailed job posting available.")),
            
            "metadata": metadata_str
        }
        
        logger.info(f"Successfully built public interview context with {len(context)} fields")
        return context
    
    except Exception as e:
        logger.error(f"Error building public interview context: {str(e)}", exc_info=True)
        raise 