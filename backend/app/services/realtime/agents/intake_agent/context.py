"""
Context for the Intake Agent.
This module defines the context and standardized values used by the Intake Agent.
"""

# Define standardized values for reference
ROLE_LEVELS = [
    "Entry Level",
    "Junior",
    "Mid-Level",
    "Senior",
    "Lead",
    "Principal",
    "Distinguished"
]

JOB_TYPES = [
    "Full-Time",
    "Part-Time",
    "Contract"
]

PRIORITIES = [
    "Expedited",
    "Normal"
]

REMOTE_STATUSES = [
    "Remote",
    "Hybrid",
    "On-site"
]

INTERVIEW_STAGES = [
    "Screening",
    "Cultural & Company fit",
    "Behavioral",
    "Domain Expert",
    "Technical Challenge with Code",
    "Home Assignment",
    "Experience & Role fit",
    "Advance Problem Solving",
    "Team Ethics"
]

# Define context for prompt formatting if needed
DEFAULT_CONTEXT = {
    "role_levels": ", ".join(ROLE_LEVELS),
    "job_types": ", ".join(JOB_TYPES),
    "priorities": ", ".join(PRIORITIES),
    "remote_statuses": ", ".join(REMOTE_STATUSES),
    "interview_stages": ", ".join(INTERVIEW_STAGES)
}
