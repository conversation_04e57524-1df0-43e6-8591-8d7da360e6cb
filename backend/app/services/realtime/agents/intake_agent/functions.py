"""
Functions for the Intake Agent.
This module defines the functions available to the Intake Agent during realtime sessions.
"""

# Define the realtime functions for the intake agent
realtime_functions = [
    {
        "type": "function",
        "name": "signal_end_call",
        "description": "Signal the end of the conversation.",
        "parameters": {
            "type": "object",
            "properties": {
                "reason": {
                    "type": "string",
                    "description": "Optional reason for ending the call."
                }
            },
            "required": []
        }
    }
]
