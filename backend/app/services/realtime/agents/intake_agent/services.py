"""
Services for the Intake Agent.
This module provides services for the Intake Agent, including transcript handling
and basic processing.
"""

import json
import logging
from typing import Dict, Any, List, Optional

from app.services.firebase_service import FirebaseService
from app.services.roles_service import RolesService
from app.services.roles import roles_service
from app.services.firebase import firebase
from app.services.ai_service import AIService

# Set up logging
logger = logging.getLogger(__name__)

# Initialize services
firebase_service = FirebaseService()
roles_service = RolesService()

async def complete_transcript(user_id: str, role_id: str, transcript_id: str) -> None:
    """
    Mark a transcript as completed.

    Args:
        user_id: The ID of the user
        role_id: The ID of the role
        transcript_id: The ID of the transcript
    """
    logger.info(f"Marking transcript {transcript_id} as completed")
    await firebase_service.complete_intake_transcript(
        user_id=user_id,
        role_id=role_id,
        transcript_id=transcript_id
    )

async def get_transcript(user_id: str, role_id: str, transcript_id: str) -> Dict[str, Any]:
    """
    Get a transcript.

    Args:
        user_id: The ID of the user
        role_id: The ID of the role
        transcript_id: The ID of the transcript

    Returns:
        The transcript
    """
    logger.info(f"Retrieving transcript {transcript_id}")
    return await firebase_service.get_intake_transcript(
        user_id=user_id,
        role_id=role_id,
        transcript_id=transcript_id
    )

async def create_minimal_update(role_id: str, user_id: str, transcript_id: str) -> Dict[str, Any]:
    """
    Create an intelligent update for a role using the role enrichment agent
    to extract information from the transcript.

    Args:
        role_id: The ID of the role
        user_id: The ID of the user
        transcript_id: The ID of the transcript

    Returns:
        The updated role
    """
    logger.info(f"Using role enrichment for transcript processing of role {role_id}")

    try:
        # Get the current role
        current_role = await roles_service.get_role(role_id, user_id)
        if not current_role:
            logger.error(f"Role {role_id} not found for user {user_id}")
            raise ValueError(f"Role {role_id} not found")

        # Get the transcript
        try:
            transcripts = await firebase.get_intake_transcripts(user_id=user_id, role_id=role_id)
            transcript = next((t for t in transcripts if t.get("id") == transcript_id), None)

            if not transcript:
                logger.error(f"Transcript {transcript_id} not found for role {role_id}")
                raise ValueError(f"Transcript {transcript_id} not found")
        except Exception as e:
            logger.error(f"Error retrieving transcript: {str(e)}", exc_info=True)
            raise ValueError(f"Error retrieving transcript: {str(e)}")

        # Prepare transcript content if needed
        try:
            if "messages" in transcript:
                # Extract content from messages
                messages = transcript.get("messages", [])
                logger.info(f"Found {len(messages)} messages in transcript")

                # Log message roles for debugging
                role_counts = {}
                for msg in messages:
                    role = msg.get("role", "unknown")
                    role_counts[role] = role_counts.get(role, 0) + 1
                logger.info(f"Message role counts: {role_counts}")

                content = "\n".join([msg.get("content", "") for msg in messages if msg.get("role") == "user" or msg.get("role") == "Recruiva"])

                # Ensure content is not empty
                if not content.strip():
                    logger.warning("Extracted content is empty, using default content")
                    content = "No transcript content available."

                transcript["content"] = content
                logger.info(f"Created content field from {len(messages)} messages with length {len(content)} characters")
            else:
                # If no messages field, create an empty content field
                transcript["content"] = "No transcript content available."
                logger.warning("No messages field found in transcript, using default content")
        except Exception as e:
            logger.error(f"Error preparing transcript content: {str(e)}", exc_info=True)
            transcript["content"] = "Error extracting transcript content."

        # Use AIService to enrich the role with transcript data
        try:
            ai_service = AIService()
            logger.info("AIService initialized for role enrichment")

            enriched_data = await ai_service.enrich_role(current_role, transcript)
            logger.info(f"Role enrichment completed: {type(enriched_data)}")

            # Log the enriched data for debugging
            if isinstance(enriched_data, dict):
                logger.info(f"Enriched data keys: {list(enriched_data.keys())}")
                if "status" in enriched_data:
                    logger.info(f"Enriched data status: {enriched_data.get('status')}")
            else:
                logger.warning(f"Unexpected enriched data type: {type(enriched_data)}")

            # Check if there was an error in the enrichment process
            if not enriched_data or not isinstance(enriched_data, dict):
                logger.error(f"Invalid enriched data format: {type(enriched_data)}")
                raise ValueError(f"Role enrichment failed: Invalid data format")

            if enriched_data.get("status") == "error":
                error_message = enriched_data.get("message", "Unknown error")
                logger.error(f"Error in role enrichment: {error_message}")
                raise ValueError(f"Role enrichment failed: {error_message}")

            # Validate that we have meaningful enriched data
            required_fields = ["title", "summary", "requiredSkills"]
            missing_fields = [field for field in required_fields if field not in enriched_data or not enriched_data.get(field)]

            if missing_fields:
                logger.error(f"Enriched data missing required fields: {missing_fields}")
                raise ValueError(f"Role enrichment failed: Missing required fields {missing_fields}")

            # Additional validation for requiredSkills - ensure we have at least 3 skills
            if "requiredSkills" in enriched_data:
                required_skills = enriched_data.get("requiredSkills", {})
                if len(required_skills) < 3:
                    logger.error(f"Insufficient required skills: only {len(required_skills)} found, minimum 3 needed")
                    raise ValueError(f"Role enrichment failed: Insufficient required skills")

            # Validate that the title is not generic
            generic_titles = ["software engineer", "developer", "engineer", "manager", "analyst", "consultant"]
            if "title" in enriched_data:
                title = enriched_data.get("title", "").lower()
                if title in generic_titles:
                    logger.error(f"Title is too generic: {title}")
                    raise ValueError(f"Role enrichment failed: Title is too generic")

            # Validate that the summary is meaningful (at least 50 characters)
            if "summary" in enriched_data:
                summary = enriched_data.get("summary", "")
                if len(summary) < 50:
                    logger.error(f"Summary is too short: {len(summary)} characters, minimum 50 needed")
                    raise ValueError(f"Role enrichment failed: Summary is too short")

            # Add transcript ID to the enriched data
            enriched_data["intake_transcript_id"] = transcript_id

            # Only update status if it's not already set
            if not current_role.get('status'):
                enriched_data["status"] = "Intake"

            # Update the role with the enriched data
            logger.info(f"Updating role with enriched data containing {len(enriched_data)} fields")
            return await roles_service.update_role(role_id, enriched_data, user_id)
        except ValueError as e:
            # Re-raise ValueError for specific errors
            raise e
        except Exception as e:
            logger.error(f"Error in AI service enrichment: {str(e)}", exc_info=True)
            raise ValueError(f"AI service error: {str(e)}")
    except ValueError as e:
        # Handle specific errors
        logger.error(f"Specific error in role enrichment: {str(e)}", exc_info=True)
        # No fallback - propagate the error
        raise ValueError(f"Role enrichment failed: {str(e)}")
    except Exception as e:
        # Handle general errors
        logger.error(f"Unexpected error in role enrichment: {str(e)}", exc_info=True)
        # No fallback - propagate the error
        raise ValueError(f"Role enrichment failed due to an unexpected error: {str(e)}")
