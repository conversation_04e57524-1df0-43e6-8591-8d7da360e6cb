"""
Common services that can be shared across agents.
This module provides services that are common to all agents.
"""

import json
import logging
from typing import Dict, Any, Optional

from app.services.firebase_service import FirebaseService
from app.services.roles_service import RolesService

# Set up logging
logger = logging.getLogger(__name__)

# Initialize services
firebase_service = FirebaseService()
roles_service = RolesService()

def safe_json_loads(json_str: str, default_value: Any = None) -> Any:
    """
    Safely load JSON string with fallback to default value.
    
    Args:
        json_str: The JSON string to parse
        default_value: The default value to return if parsing fails
        
    Returns:
        Parsed JSON object or default value
    """
    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing JSON: {str(e)}")
        return default_value

async def update_role(role_id: str, role_data: Dict[str, Any], user_id: str, is_enrichment: bool = False) -> Dict[str, Any]:
    """
    Update a role.
    
    Args:
        role_id: The ID of the role
        role_data: The role data to update
        user_id: The ID of the user
        is_enrichment: Whether to enrich the role or replace existing data
        
    Returns:
        The updated role
    """
    try:
        # Update the role
        if is_enrichment:
            # In enrichment mode, we only add new information
            logger.info(f"Updating role {role_id} in enrichment mode")
            return await roles_service.enrich_role(role_id, role_data, user_id)
        else:
            # In normal mode, we replace existing information
            logger.info(f"Updating role {role_id} in normal mode")
            return await roles_service.update_role(role_id, role_data, user_id)
    except Exception as e:
        logger.error(f"Error updating role: {str(e)}", exc_info=True)
        raise

async def complete_transcript(user_id: str, role_id: str, transcript_id: str) -> None:
    """
    Mark a transcript as completed. This is a common implementation that works for all agent types.
    
    Args:
        user_id: The ID of the user
        role_id: The ID of the role
        transcript_id: The ID of the transcript
    """
    logger.info(f"Marking transcript {transcript_id} as completed")
    try:
        # For intake transcripts
        await firebase_service.complete_intake_transcript(
            user_id=user_id,
            role_id=role_id,
            transcript_id=transcript_id
        )
        logger.info(f"Successfully marked transcript {transcript_id} as completed")
    except Exception as e:
        logger.error(f"Error completing transcript {transcript_id}: {str(e)}")
        raise

async def get_transcript(user_id: str, role_id: str, transcript_id: str) -> Dict[str, Any]:
    """
    Get a transcript.
    
    Args:
        user_id: The ID of the user
        role_id: The ID of the role
        transcript_id: The ID of the transcript
        
    Returns:
        The transcript data
    """
    logger.info(f"Getting transcript {transcript_id}")
    try:
        transcript = await firebase_service.get_intake_transcript(
            user_id=user_id,
            role_id=role_id,
            transcript_id=transcript_id
        )
        if not transcript:
            raise ValueError(f"Transcript {transcript_id} not found")
        return transcript
    except Exception as e:
        logger.error(f"Error getting transcript {transcript_id}: {str(e)}")
        raise
