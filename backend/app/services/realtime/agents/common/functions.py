"""
Common functions that can be shared across agents.
This module defines functions that are common to all agents.
"""

# Define common functions that can be shared across agents
realtime_functions = [
    {
        "type": "function",
        "name": "end_the_call",
        "description": "Signal the end of the conversation.",
        "parameters": {
            "type": "object",
            "properties": {
                "reason": { "type": "string", "description": "The reason for ending the call." }
            },
            "required": ["reason"]
        }
    }
]
