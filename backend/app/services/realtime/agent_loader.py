"""
Enhanced agent loader with lazy loading capabilities.
"""

import importlib
import os
import logging
from typing import Dict, Any, Optional, List, Callable
from .framework.agent_factory import AgentFactory

logger = logging.getLogger(__name__)

class AgentLoader:
    """
    Enhanced agent loader with lazy loading capabilities.
    Only loads components when they are actually needed.
    """
    
    def __init__(self, agent_type: str):
        """
        Initialize the agent loader.
        
        Args:
            agent_type: Type of agent to load
        """
        self.agent_type = agent_type
        self.base_path = f"app.services.realtime.agents.{agent_type}"
        self.common_path = "app.services.realtime.agents.common"
        
        # Track loaded modules to avoid reloading
        self._loaded_modules = {}
        
        logger.info(f"Initialized AgentLoader for {agent_type}")

    def load_module(self, module_name: str):
        """
        Lazily load a module only when needed.
        
        Args:
            module_name: Name of the module to load
            
        Returns:
            Loaded module
        """
        # Check if already loaded
        cache_key = f"{module_name}"
        if cache_key in self._loaded_modules:
            return self._loaded_modules[cache_key]
        
        # Try to load from agent-specific path first
        try:
            module = importlib.import_module(f"{self.base_path}.{module_name}")
            self._loaded_modules[cache_key] = module
            logger.debug(f"Loaded module {module_name} from {self.base_path}")
            return module
        except ModuleNotFoundError:
            # Fall back to common path
            try:
                module = importlib.import_module(f"{self.common_path}.{module_name}")
                self._loaded_modules[cache_key] = module
                logger.debug(f"Loaded module {module_name} from {self.common_path}")
                return module
            except ModuleNotFoundError as e:
                logger.error(f"Failed to load module {module_name}: {str(e)}")
                raise

    def get_prompt(self, prompt_name: str):
        """
        Get a prompt file.
        
        Args:
            prompt_name: Name of the prompt file
            
        Returns:
            Prompt content
        """
        # Ensure prompt_name has the .prompt extension
        if not prompt_name.endswith('.prompt'):
            prompt_name = f"{prompt_name}.prompt"
            
        # Only look in the agent-specific prompt directory
        prompt_path = os.path.join(os.path.dirname(__file__), "agents", self.agent_type, "prompts", prompt_name)
        if not os.path.exists(prompt_path):
            logger.error(f"Prompt not found: {prompt_name}")
            raise FileNotFoundError(f"Prompt not found: {prompt_name}")
        
        with open(prompt_path, 'r') as file:
            content = file.read()
            logger.debug(f"Loaded prompt {prompt_name} from {prompt_path}")
            return content

    def get_functions(self):
        """
        Get the functions module.
        
        Returns:
            Functions module
        """
        return self.load_module("functions")

    def get_context(self):
        """
        Get the context module.
        
        Returns:
            Context module
        """
        return self.load_module("context")

    def get_services(self):
        """
        Get the services module.
        
        Returns:
            Services module
        """
        return self.load_module("services")
    
    async def create_agent(self, context: Optional[Dict[str, Any]] = None):
        """
        Create an agent instance using the agent factory.
        
        Args:
            context: Optional initialization context
            
        Returns:
            Agent instance
        """
        return await AgentFactory.create_agent(self.agent_type, context) 