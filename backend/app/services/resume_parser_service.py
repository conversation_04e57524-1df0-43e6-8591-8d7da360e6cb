from typing import Dict, Any, Op<PERSON>, Tuple, List
from fastapi import UploadFile, HTTPException
import PyPDF2
import docx
import io
import os
import logging
import traceback
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)

class FileType(Enum):
    """Enum for supported file types"""
    PDF = "pdf"
    DOCX = "docx"
    DOC = "doc"
    TXT = "txt"
    UNKNOWN = "unknown"

class ResumeParserService:
    """Service for parsing text from resume files in various formats"""
    
    _instance = None
    
    def __new__(cls):
        """Implement singleton pattern"""
        if cls._instance is None:
            cls._instance = super(ResumeParserService, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize the service"""
        # List of supported MIME types
        self.supported_mime_types = {
            'application/pdf': FileType.PDF,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': FileType.DOCX,
            'application/msword': FileType.DOC,
            'text/plain': FileType.TXT
        }
    
    def _detect_file_type(self, file: UploadFile) -> FileType:
        """
        Detect file type based on content type and filename
        
        Args:
            file: The uploaded file object
            
        Returns:
            FileType enum representing the detected file type
        """
        # First try to use the content_type
        if file.content_type in self.supported_mime_types:
            return self.supported_mime_types[file.content_type]
        
        # Fallback to checking the file extension
        if file.filename:
            extension = os.path.splitext(file.filename)[1].lower().lstrip('.')
            if extension == 'pdf':
                return FileType.PDF
            elif extension == 'docx':
                return FileType.DOCX
            elif extension == 'doc':
                return FileType.DOC
            elif extension == 'txt':
                return FileType.TXT
        
        return FileType.UNKNOWN
    
    async def _parse_pdf(self, content: bytes) -> str:
        """
        Parse text from PDF content
        
        Args:
            content: The binary content of the PDF file
            
        Returns:
            Extracted text from the PDF
            
        Raises:
            ValueError: If the PDF is corrupted or cannot be parsed
        """
        try:
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(content))
            if len(pdf_reader.pages) == 0:
                raise ValueError("PDF file has no pages")
                
            text = ""
            for page in pdf_reader.pages:
                extracted_text = page.extract_text()
                if extracted_text:
                    text += extracted_text + "\n"
                    
            return text.strip()
        except PyPDF2.errors.PdfReadError as e:
            logger.error(f"Error parsing PDF: {str(e)}")
            logger.debug(traceback.format_exc())
            raise ValueError(f"Failed to parse PDF: {str(e)}") from e
        except Exception as e:
            logger.error(f"Unexpected error parsing PDF: {str(e)}")
            logger.debug(traceback.format_exc())
            raise ValueError(f"Failed to parse PDF: {str(e)}") from e
    
    async def _parse_docx(self, content: bytes) -> str:
        """
        Parse text from DOCX content
        
        Args:
            content: The binary content of the DOCX file
            
        Returns:
            Extracted text from the DOCX
            
        Raises:
            ValueError: If the DOCX is corrupted or cannot be parsed
        """
        try:
            doc = docx.Document(io.BytesIO(content))
            
            text = ""
            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text:
                    text += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            if paragraph.text:
                                text += paragraph.text + "\n"
            
            return text.strip()
        except Exception as e:
            logger.error(f"Error parsing DOCX: {str(e)}")
            logger.debug(traceback.format_exc())
            raise ValueError(f"Failed to parse DOCX: {str(e)}") from e
    
    async def _parse_text(self, content: bytes) -> str:
        """
        Parse text from plain text content
        
        Args:
            content: The binary content of the text file
            
        Returns:
            Extracted text
            
        Raises:
            ValueError: If the text cannot be decoded
        """
        try:
            # Attempt to decode with UTF-8 first
            return content.decode('utf-8').strip()
        except UnicodeDecodeError:
            # Fallback to other encodings
            try:
                return content.decode('latin-1').strip()
            except Exception as e:
                logger.error(f"Error decoding text file: {str(e)}")
                raise ValueError(f"Failed to decode text file: {str(e)}") from e
    
    async def parse_resume(self, file: UploadFile) -> Dict[str, Any]:
        """
        Parse resume from various file formats
        
        Args:
            file: The uploaded file object
            
        Returns:
            Dictionary containing parsed text and metadata
            
        Raises:
            ValueError: If the file is corrupted or unsupported
        """
        if file is None:
            raise ValueError("No file provided")
        
        # Read file content
        try:
            content = await file.read()
            if not content:
                raise ValueError("Empty file provided")
        except Exception as e:
            logger.error(f"Error reading file: {str(e)}")
            raise ValueError(f"Failed to read file: {str(e)}") from e
        
        # Reset file position for potential future reads
        await file.seek(0)
        
        # Detect file type
        file_type = self._detect_file_type(file)
        
        # Parse based on file type
        try:
            if file_type == FileType.PDF:
                text = await self._parse_pdf(content)
            elif file_type in [FileType.DOCX, FileType.DOC]:
                text = await self._parse_docx(content)
            elif file_type == FileType.TXT:
                text = await self._parse_text(content)
            else:
                supported_types = ', '.join([t.name for t in FileType if t != FileType.UNKNOWN])
                raise ValueError(f"Unsupported file type. Supported types: {supported_types}")
            
            # Return parsed data with metadata
            return {
                "text": text,
                "metadata": {
                    "filename": file.filename,
                    "file_type": file_type.value,
                    "content_type": file.content_type,
                    "file_size": len(content)
                }
            }
        except ValueError as e:
            # Re-raise ValueError for expected errors
            raise
        except Exception as e:
            # Catch and convert other exceptions
            logger.error(f"Unexpected error parsing file: {str(e)}")
            logger.debug(traceback.format_exc())
            raise ValueError(f"Failed to parse file: {str(e)}") from e 