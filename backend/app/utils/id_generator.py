# File: backend/app/utils/id_generator.py

import uuid
import random
import string
import time

def generate_unique_id(prefix: str = "", length: int = 10) -> str:
    """
    Generate a unique ID with an optional prefix.
    
    Args:
        prefix: Optional prefix for the ID
        length: Length of the random part of the ID
        
    Returns:
        A unique ID string
    """
    # Generate a random string of the specified length
    random_part = ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    # Add the prefix if provided
    if prefix:
        return f"{prefix}_{random_part}"
    
    return random_part

def generate_question_id() -> str:
    """
    Generate a unique ID for a question.
    
    Returns:
        A unique ID string for a question
    """
    return f"q_{generate_unique_id(length=8)}"

def generate_uuid() -> str:
    """
    Generate a UUID string.
    
    Returns:
        A UUID string
    """
    return str(uuid.uuid4())

def generate_criterion_id() -> str:
    """
    Generate a unique ID for an evaluation criterion.
    
    Returns:
        A unique ID string for a criterion
    """
    return f"crit_{generate_unique_id(length=8)}" 