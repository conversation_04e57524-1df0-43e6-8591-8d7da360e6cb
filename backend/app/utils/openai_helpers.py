"""
Utility functions for OpenAI API integrations.
"""

import os
import logging
import httpx
import backoff
from typing import Dict, Any, Optional
from app.core.config import settings
import uuid
import time

logger = logging.getLogger(__name__)

@backoff.on_exception(backoff.expo, 
                     (httpx.HTTPError, httpx.TimeoutException), 
                     max_tries=2, 
                     max_time=10)
async def generate_ephemeral_key() -> str:
    """
    Generate an API key for OpenAI API access.
    
    Note: The ephemeral keys endpoint (v1/ephemeral_keys) appears to be unavailable
    or deprecated. This function now returns the main API key directly.
    
    In a production environment with higher security requirements, consider
    implementing a different approach such as:
    1. Use a proxy server that adds the API key to requests
    2. Create a short-lived API key with limited permissions
    3. Set up a token service with rate limiting
    
    Returns:
        str: The API key to use
    """
    # Log that we're using the main API key
    logger.info("Using main OpenAI API key - ephemeral keys endpoint unavailable")
    
    # Return the main API key
    return settings.OPENAI_API_KEY 