# File: backend/app/utils/validation.py

from typing import List, Dict, Any
import logging

def validate_scorecard_weights(criteria: List[Dict[str, Any]]) -> bool:
    """
    Validate that the weights of ScoreCard criteria are valid.
    Note: We no longer require weights to sum to 1.0 as we're using an importance scale (1-5).
    
    Args:
        criteria: List of evaluation criteria
        
    Returns:
        Always returns True as we don't validate weight sum anymore
    """
    # We no longer validate that weights sum to 1.0
    # This is because we're using an importance scale (1-5) instead of percentages
    return True

def validate_criteria_consistency(criteria: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Validate the consistency of evaluation criteria.
    
    Args:
        criteria: List of evaluation criteria
        
    Returns:
        A dictionary with validation results
    """
    result = {
        'valid': True,
        'errors': []
    }
    
    # Check for duplicate IDs
    ids = [c.get('id') for c in criteria if c.get('id')]
    if len(ids) != len(set(ids)):
        result['valid'] = False
        result['errors'].append("Duplicate criterion IDs found")
    
    # We no longer validate ScoreCard weights sum
    # Removed: if not validate_scorecard_weights(criteria):
    #    result['valid'] = False
    #    result['errors'].append("ScoreCard weights do not sum to 1.0")
    
    # Validate required fields for each criterion type
    for criterion in criteria:
        criterion_type = criterion.get('type')
        
        if criterion_type == 'ScoreCard':
            if 'competency' not in criterion or not criterion['competency']:
                result['valid'] = False
                result['errors'].append(f"Missing competency for ScoreCard criterion {criterion.get('id', 'unknown')}")
            
            if 'weight' not in criterion:
                result['valid'] = False
                result['errors'].append(f"Missing weight for ScoreCard criterion {criterion.get('id', 'unknown')}")
        
        if 'criteria' not in criterion or not criterion['criteria']:
            result['valid'] = False
            result['errors'].append(f"Missing criteria for {criterion_type} criterion {criterion.get('id', 'unknown')}")
    
    return result 