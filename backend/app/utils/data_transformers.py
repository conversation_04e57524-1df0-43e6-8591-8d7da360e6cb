"""Data transformation utilities for the application."""

import logging
import json
from typing import Dict, Any, List, Optional, Type
from enum import Enum

# Set up logging
logger = logging.getLogger(__name__)

def normalize_role_data(role_data):
    """
    Normalize role data to ensure consistent field names.

    Args:
        role_data (dict): The role data to normalize

    Returns:
        dict: The normalized role data
    """
    if not role_data or not isinstance(role_data, dict):
        logger.warning("Invalid role data provided for normalization")
        return role_data

    # Create a new dictionary with normalized field names
    normalized_data = {}

    # First, copy all existing fields to the normalized data
    for key, value in role_data.items():
        normalized_data[key] = value

    # Map createdAt to created_at for frontend compatibility
    if 'createdAt' in normalized_data and 'created_at' not in normalized_data:
        normalized_data['created_at'] = normalized_data['createdAt']

    # Map updatedAt to updated_at for frontend compatibility
    if 'updatedAt' in normalized_data and 'updated_at' not in normalized_data:
        normalized_data['updated_at'] = normalized_data['updatedAt']

    return normalized_data

def normalize_interview_data(interview_data):
    """
    Normalize interview data to ensure consistent field names.

    Args:
        interview_data (dict): The interview data to normalize

    Returns:
        dict: The normalized interview data
    """
    if not interview_data or not isinstance(interview_data, dict):
        logger.warning("Invalid interview data provided for normalization")
        return interview_data

    # Create a new dictionary with normalized field names
    normalized_data = {}

    # First, copy all existing fields to the normalized data
    for key, value in interview_data.items():
        normalized_data[key] = value

    # Map createdAt to created_at for frontend compatibility
    if 'createdAt' in normalized_data and 'created_at' not in normalized_data:
        normalized_data['created_at'] = normalized_data['createdAt']

    # Map updatedAt to updated_at for frontend compatibility
    if 'updatedAt' in normalized_data and 'updated_at' not in normalized_data:
        normalized_data['updated_at'] = normalized_data['updatedAt']

    # Map completedAt to completed_at for frontend compatibility
    if 'completedAt' in normalized_data and 'completed_at' not in normalized_data:
        normalized_data['completed_at'] = normalized_data['completedAt']

    # Map templateId to template_id for frontend compatibility
    if 'templateId' in normalized_data and 'template_id' not in normalized_data:
        normalized_data['template_id'] = normalized_data['templateId']

    # Map roleId to role_id for frontend compatibility
    if 'roleId' in normalized_data and 'role_id' not in normalized_data:
        normalized_data['role_id'] = normalized_data['roleId']

    # Map candidateId to candidate_id for frontend compatibility
    if 'candidateId' in normalized_data and 'candidate_id' not in normalized_data:
        normalized_data['candidate_id'] = normalized_data['candidateId']

    # Ensure status is in the expected format
    if 'status' in normalized_data:
        status = normalized_data['status'].lower()
        if status == 'complete' or status == 'finished':
            normalized_data['status'] = 'completed'
        elif status == 'progress' or status == 'ongoing':
            normalized_data['status'] = 'in_progress'
        elif status == 'schedule' or status == 'planned':
            normalized_data['status'] = 'scheduled'
    else:
        normalized_data['status'] = 'unknown'

    # Add ID if not present
    if 'id' not in normalized_data and 'docId' in normalized_data:
        normalized_data['id'] = normalized_data['docId']

    return normalized_data

    # Create a mapping of alternative field names to standard names
    field_mapping = {
        "about_the_role": "summary",
        "key_responsibilities": "keyResponsibilities",
        "required_skills": "requiredSkills",
        "preferred_skills": "preferredSkills",
        "years_of_experience": "yearsOfExperience",
        "key_stakeholders": "keyStakeholders",
        "job_type": "jobType",
        "about_team": "aboutTeam",
        "open_ended_considerations": "openEndedConsiderations",
        "about_company": "aboutCompany",
        "hiring_manager_contact": "hiringManagerContact",
        "interview_process": "interviewProcess",
        # Add more variations
        "responsibilities": "keyResponsibilities",
        "skills": "requiredSkills",
        "experience": "yearsOfExperience",
        "stakeholders": "keyStakeholders",
        "company": "aboutCompany",
        "team_info": "aboutTeam",
        "considerations": "openEndedConsiderations",
        "hiring_manager": "hiringManagerContact",
        "interviews": "interviewProcess"
    }

    # Create a mapping for interview stage values to match the expected enum values
    interview_stage_mapping = {
        "cultural and company fit": "Cultural & Company fit",
        "cultural fit": "Cultural & Company fit",
        "culture fit": "Cultural & Company fit",
        "company fit": "Cultural & Company fit",
        "cultural": "Cultural & Company fit",
        "culture": "Cultural & Company fit",
        "experience and domain knowledge": "Domain Expert",
        "domain knowledge": "Domain Expert",
        "domain expertise": "Domain Expert",
        "domain expert": "Domain Expert",
        "domain": "Domain Expert",
        "expertise": "Domain Expert",
        "expert": "Domain Expert",
        "advanced problem-solving": "Advance Problem Solving",
        "advanced problem solving": "Advance Problem Solving",
        "problem solving": "Advance Problem Solving",
        "problem-solving": "Advance Problem Solving",
        "advanced": "Advance Problem Solving",
        "problem": "Advance Problem Solving",
        "initial screening": "Screening",
        "screening": "Screening",
        "initial": "Screening",
        "screen": "Screening",
        "technical challenge": "Technical Challenge with Code",
        "technical": "Technical Challenge with Code",
        "coding challenge": "Technical Challenge with Code",
        "code challenge": "Technical Challenge with Code",
        "coding": "Technical Challenge with Code",
        "code": "Technical Challenge with Code",
        "challenge": "Technical Challenge with Code",
        "home assignment": "Home Assignment",
        "take home": "Home Assignment",
        "assignment": "Home Assignment",
        "home": "Home Assignment",
        "experience fit": "Experience & Role fit",
        "experience and role fit": "Experience & Role fit",
        "role fit": "Experience & Role fit",
        "fit": "Experience & Role fit",
        "team ethics": "Team Ethics",
        "ethics": "Team Ethics",
        "team": "Team Ethics",
        "behavioral": "Behavioral",
        "behavior": "Behavioral"
    }

    # Create a mapping for priority values
    priority_mapping = {
        "high": "Expedited",
        "urgent": "Expedited",
        "critical": "Expedited",
        "expedited": "Expedited",
        "normal": "Normal",
        "standard": "Normal",
        "regular": "Normal",
        "low": "Normal"
    }

    # Create a new dictionary with normalized field names
    normalized_data = {}

    # First, copy all existing fields to the normalized data
    for key, value in role_data.items():
        normalized_data[key] = value

    # Map createdAt to created_at for frontend compatibility
    if 'createdAt' in normalized_data and 'created_at' not in normalized_data:
        normalized_data['created_at'] = normalized_data['createdAt']

    # Map updatedAt to updated_at for frontend compatibility
    if 'updatedAt' in normalized_data and 'updated_at' not in normalized_data:
        normalized_data['updated_at'] = normalized_data['updatedAt']

    # Then, check for alternative field names and normalize them
    for alt_key, std_key in field_mapping.items():
        if alt_key in role_data and alt_key != std_key:
            # If the standard key doesn't exist or is None, use the alternative value
            if std_key not in normalized_data or normalized_data[std_key] is None:
                normalized_data[std_key] = role_data[alt_key]

    # Ensure keyResponsibilities is a list
    if 'keyResponsibilities' not in normalized_data or normalized_data['keyResponsibilities'] is None:
        normalized_data['keyResponsibilities'] = []
    elif not isinstance(normalized_data['keyResponsibilities'], list):
        # Try to convert to list if possible
        try:
            if isinstance(normalized_data['keyResponsibilities'], str):
                # If it's a string, try to split by commas
                normalized_data['keyResponsibilities'] = [item.strip() for item in normalized_data['keyResponsibilities'].split(',') if item.strip()]
            else:
                # For other types, convert to string and make a single-item list
                normalized_data['keyResponsibilities'] = [str(normalized_data['keyResponsibilities'])]
        except Exception:
            # If conversion fails, use empty list
            normalized_data['keyResponsibilities'] = []
            logger.warning(f"Could not convert keyResponsibilities to list, using empty list")

    # Ensure team is a string
    if 'team' not in normalized_data or normalized_data['team'] is None:
        normalized_data['team'] = ""
    elif not isinstance(normalized_data['team'], str):
        # Convert to string
        try:
            normalized_data['team'] = str(normalized_data['team'])
        except Exception:
            normalized_data['team'] = ""
            logger.warning(f"Could not convert team to string, using empty string")

    return normalized_data

def normalize_enum_values(role_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Normalize all enum values in a role document to ensure they match expected values.

    Args:
        role_data (Dict[str, Any]): The role data to normalize

    Returns:
        Dict[str, Any]: The normalized role data
    """
    try:
        if not role_data or not isinstance(role_data, dict):
            logger.warning("Invalid role data provided for enum normalization")
            return role_data

        # Create a copy to avoid modifying the original
        normalized_data = role_data.copy()

        # Normalize job type
        if 'jobType' not in normalized_data or not normalized_data['jobType']:
            normalized_data['jobType'] = 'Full-time'
            logger.debug("Added missing jobType field with default value 'Full-time'")
        elif isinstance(normalized_data['jobType'], str):
            job_type = normalized_data['jobType'].lower()
            if job_type in ['full-time', 'full time', 'fulltime', 'full']:
                normalized_data['jobType'] = 'Full-time'
            elif job_type in ['part-time', 'part time', 'parttime', 'part']:
                normalized_data['jobType'] = 'Part-time'
            elif job_type in ['contract', 'contractor', 'freelance', 'temporary', 'temp']:
                normalized_data['jobType'] = 'Contract'
            else:
                # Default to Full-time if no match
                normalized_data['jobType'] = 'Full-time'
                logger.debug(f"Defaulted jobType from '{normalized_data['jobType']}' to 'Full-time'")

        # Normalize remote status in location
        if 'location' not in normalized_data or not isinstance(normalized_data['location'], dict):
            normalized_data['location'] = {'remoteStatus': 'Remote', 'city': ''}
            logger.debug("Added missing location field with default values")
        elif isinstance(normalized_data['location'], dict):
            if 'remoteStatus' not in normalized_data['location'] or not normalized_data['location']['remoteStatus']:
                normalized_data['location']['remoteStatus'] = 'Remote'
                logger.debug("Added missing remoteStatus field with default value 'Remote'")
            elif isinstance(normalized_data['location']['remoteStatus'], str):
                remote_status = normalized_data['location']['remoteStatus'].lower()
                if remote_status in ['remote', 'fully remote', 'work from home', 'wfh']:
                    normalized_data['location']['remoteStatus'] = 'Remote'
                elif remote_status in ['hybrid', 'partially remote', 'flexible']:
                    normalized_data['location']['remoteStatus'] = 'Hybrid'
                elif remote_status in ['on-site', 'onsite', 'on site', 'in-office', 'in office', 'office']:
                    normalized_data['location']['remoteStatus'] = 'On-site'
                elif remote_status in ['not specified', 'unspecified', 'any', 'tbd', 'to be determined']:
                    # Default to Remote for "Not Specified"
                    normalized_data['location']['remoteStatus'] = 'Remote'
                    logger.debug(f"Defaulted remoteStatus from '{normalized_data['location']['remoteStatus']}' to 'Remote'")
                else:
                    # Default to Remote if no match
                    normalized_data['location']['remoteStatus'] = 'Remote'
                    logger.debug(f"Defaulted remoteStatus from '{normalized_data['location']['remoteStatus']}' to 'Remote'")

            # Ensure city field exists
            if 'city' not in normalized_data['location']:
                normalized_data['location']['city'] = ''
                logger.debug("Added missing city field with default empty value")

        # Normalize role status
        if 'status' not in normalized_data or not normalized_data['status']:
            normalized_data['status'] = 'Intake'
            logger.debug("Added missing status field with default value 'Intake'")
        elif isinstance(normalized_data['status'], str):
            status = normalized_data['status'].lower()
            status_mapping = {
                'intake': 'Intake',
                'sourcing': 'Sourcing',
                'screening': 'Screening',
                'deep_dive': 'Deep_Dive',
                'deep dive': 'Deep_Dive',
                'deepdive': 'Deep_Dive',
                'in_person': 'In_Person',
                'in person': 'In_Person',
                'inperson': 'In_Person',
                'offer': 'Offer',
                'accepted': 'Accepted',
                'rejected': 'Rejected',
                'closed': 'Closed'
            }

            if status in status_mapping:
                normalized_data['status'] = status_mapping[status]
            else:
                # Default to Intake if no match
                normalized_data['status'] = 'Intake'
                logger.debug(f"Defaulted status from '{normalized_data['status']}' to 'Intake'")

        # Normalize priority
        if 'priority' not in normalized_data or not normalized_data['priority']:
            normalized_data['priority'] = 'Normal'
            logger.debug("Added missing priority field with default value 'Normal'")
        elif isinstance(normalized_data['priority'], str):
            priority = normalized_data['priority'].lower()
            if priority in ['high', 'urgent', 'critical', 'expedited', 'top', 'important']:
                normalized_data['priority'] = 'Expedited'
            elif priority in ['normal', 'standard', 'regular', 'medium', 'low']:
                normalized_data['priority'] = 'Normal'
            else:
                # Default to Normal if no match
                normalized_data['priority'] = 'Normal'
                logger.debug(f"Defaulted priority from '{normalized_data['priority']}' to 'Normal'")

        # Normalize role level
        if 'level' not in normalized_data or not normalized_data['level']:
            normalized_data['level'] = 'Entry Level'
            logger.debug("Added missing level field with default value 'Entry Level'")
        elif isinstance(normalized_data['level'], str):
            level = normalized_data['level'].lower()
            level_mapping = {
                'entry level': 'Entry Level',
                'entry': 'Entry Level',
                'junior': 'Junior',
                'mid-level': 'Mid-Level',
                'mid level': 'Mid-Level',
                'mid': 'Mid-Level',
                'senior': 'Senior',
                'lead': 'Lead',
                'principal': 'Principal',
                'distinguished': 'Distinguished',
                'not specified': 'Entry Level',  # Map "Not Specified" to "Entry Level"
                'unspecified': 'Entry Level',
                'n/a': 'Entry Level',
                'none': 'Entry Level'
            }

            if level in level_mapping:
                normalized_data['level'] = level_mapping[level]
            else:
                # Default to Entry Level if no match
                normalized_data['level'] = 'Entry Level'
                logger.debug(f"Defaulted level from '{normalized_data['level']}' to 'Entry Level'")

        # Normalize interview stages
        if 'interviewProcess' not in normalized_data or not normalized_data['interviewProcess']:
            normalized_data['interviewProcess'] = [{
                'stage': 'Screening',
                'duration': '30 minutes',
                'customInstructions': ''
            }]
            logger.debug("Added missing interviewProcess field with default screening stage")
        elif isinstance(normalized_data['interviewProcess'], list):
            for i, stage in enumerate(normalized_data['interviewProcess']):
                if not isinstance(stage, dict):
                    normalized_data['interviewProcess'][i] = {
                        'stage': 'Screening',
                        'duration': '30 minutes',
                        'customInstructions': ''
                    }
                    logger.debug(f"Replaced invalid interview stage at index {i} with default values")
                    continue

                # Ensure stage field exists and is valid
                if 'stage' not in stage or not stage['stage']:
                    stage['stage'] = 'Screening'
                    logger.debug(f"Added missing stage field to interview stage at index {i}")
                elif isinstance(stage['stage'], str):
                    stage_value = stage['stage'].lower()
                    stage_mapping = {
                        'screening': 'Screening',
                        'cultural & company fit': 'Cultural & Company fit',
                        'cultural and company fit': 'Cultural & Company fit',
                        'cultural fit': 'Cultural & Company fit',
                        'culture fit': 'Cultural & Company fit',
                        'company fit': 'Cultural & Company fit',
                        'cultural': 'Cultural & Company fit',
                        'culture': 'Cultural & Company fit',
                        'behavioral': 'Behavioral',
                        'behavior': 'Behavioral',
                        'domain expert': 'Domain Expert',
                        'domain expertise': 'Domain Expert',
                        'domain knowledge': 'Domain Expert',
                        'domain': 'Domain Expert',
                        'expertise': 'Domain Expert',
                        'expert': 'Domain Expert',
                        'technical challenge with code': 'Technical Challenge with Code',
                        'technical challenge': 'Technical Challenge with Code',
                        'coding challenge': 'Technical Challenge with Code',
                        'code challenge': 'Technical Challenge with Code',
                        'technical': 'Technical Challenge with Code',
                        'coding': 'Technical Challenge with Code',
                        'code': 'Technical Challenge with Code',
                        'challenge': 'Technical Challenge with Code',
                        'home assignment': 'Home Assignment',
                        'take home': 'Home Assignment',
                        'assignment': 'Home Assignment',
                        'home': 'Home Assignment',
                        'experience & role fit': 'Experience & Role fit',
                        'experience and role fit': 'Experience & Role fit',
                        'experience fit': 'Experience & Role fit',
                        'role fit': 'Experience & Role fit',
                        'fit': 'Experience & Role fit',
                        'advance problem solving': 'Advance Problem Solving',
                        'advanced problem solving': 'Advance Problem Solving',
                        'problem solving': 'Advance Problem Solving',
                        'problem-solving': 'Advance Problem Solving',
                        'advanced': 'Advance Problem Solving',
                        'problem': 'Advance Problem Solving',
                        'team ethics': 'Team Ethics',
                        'ethics': 'Team Ethics',
                        'team': 'Team Ethics'
                    }

                    if stage_value in stage_mapping:
                        stage['stage'] = stage_mapping[stage_value]
                    else:
                        # Default to Screening if no match
                        stage['stage'] = 'Screening'
                        logger.debug(f"Defaulted interview stage from '{stage['stage']}' to 'Screening'")

                # Ensure duration field exists
                if 'duration' not in stage or not stage['duration']:
                    stage['duration'] = '30 minutes'
                    logger.debug(f"Added missing duration field to interview stage at index {i}")

                # Ensure customInstructions field exists
                if 'customInstructions' not in stage:
                    stage['customInstructions'] = ''
                    logger.debug(f"Added missing customInstructions field to interview stage at index {i}")

        # Ensure keyResponsibilities is a list
        if 'keyResponsibilities' not in normalized_data or normalized_data['keyResponsibilities'] is None:
            normalized_data['keyResponsibilities'] = []
            logger.debug("Added missing keyResponsibilities field with default empty list")
        elif not isinstance(normalized_data['keyResponsibilities'], list):
            # Try to convert to list if possible
            try:
                if isinstance(normalized_data['keyResponsibilities'], str):
                    # If it's a string, try to split by commas
                    normalized_data['keyResponsibilities'] = [item.strip() for item in normalized_data['keyResponsibilities'].split(',') if item.strip()]
                else:
                    # For other types, convert to string and make a single-item list
                    normalized_data['keyResponsibilities'] = [str(normalized_data['keyResponsibilities'])]
            except Exception:
                # If conversion fails, use empty list
                normalized_data['keyResponsibilities'] = []
                logger.warning(f"Could not convert keyResponsibilities to list, using empty list")

        # Ensure team is a string
        if 'team' not in normalized_data or normalized_data['team'] is None:
            normalized_data['team'] = ""
            logger.debug("Added missing team field with default empty string")
        elif not isinstance(normalized_data['team'], str):
            # Convert to string
            try:
                normalized_data['team'] = str(normalized_data['team'])
            except Exception:
                normalized_data['team'] = ""
                logger.warning(f"Could not convert team to string, using empty string")

        return normalized_data
    except Exception as e:
        # If any error occurs during normalization, log it and return the original data
        logger.error(f"Error normalizing enum values: {str(e)}", exc_info=True)

        # Ensure we at least have the minimum required fields with valid types
        if not role_data or not isinstance(role_data, dict):
            return {}

        result = role_data.copy()

        # Ensure these critical fields have valid values
        if 'keyResponsibilities' not in result or result['keyResponsibilities'] is None or not isinstance(result['keyResponsibilities'], list):
            result['keyResponsibilities'] = []

        if 'team' not in result or result['team'] is None or not isinstance(result['team'], str):
            result['team'] = ""

        return result