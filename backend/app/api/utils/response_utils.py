"""
Utility functions for handling API responses and error cases.
"""

import logging
import async<PERSON>
from typing import Async<PERSON>enerator, Any, Dict, List, Optional, Union
from fastapi import HTT<PERSON>Ex<PERSON>, status
from fastapi.responses import StreamingResponse, JSONResponse
from starlette.requests import ClientDisconnect
import h11
import json

logger = logging.getLogger(__name__)

async def safe_streaming_response(
    content_generator: AsyncGenerator[str, None],
    content_type: str = "application/json",
    status_code: int = 200,
    headers: Optional[Dict[str, str]] = None
) -> StreamingResponse:
    """
    Create a streaming response that handles client disconnections gracefully.
    
    Args:
        content_generator: An async generator that yields response content
        content_type: The content type of the response
        status_code: The HTTP status code
        headers: Optional additional headers
        
    Returns:
        A streaming response
    """
    async def protected_iterator() -> AsyncGenerator[str, None]:
        try:
            async for chunk in content_generator:
                yield chunk
        except ClientDisconnect:
            logger.warning("Client disconnected during streaming response")
        except h11._util.LocalProtocolError as protocol_error:
            logger.warning(f"HTTP protocol error during streaming: {str(protocol_error)}")
        except Exception as e:
            logger.error(f"Error during streaming response: {str(e)}")
            # Yield a final error message if possible
            try:
                yield json.dumps({"status": "error", "message": "Error during streaming response"})
            except:
                pass
    
    return StreamingResponse(
        protected_iterator(),
        media_type=content_type,
        status_code=status_code,
        headers=headers
    )

async def safe_json_response(
    content: Any,
    status_code: int = 200,
    headers: Optional[Dict[str, str]] = None
) -> JSONResponse:
    """
    Create a JSON response with error handling.
    
    Args:
        content: The response content
        status_code: The HTTP status code
        headers: Optional additional headers
        
    Returns:
        A JSON response
    """
    try:
        # Validate that content can be serialized to JSON
        json.dumps(content)
        return JSONResponse(
            content=content,
            status_code=status_code,
            headers=headers
        )
    except Exception as e:
        logger.error(f"Error serializing JSON response: {str(e)}")
        return JSONResponse(
            content={"status": "error", "message": "Error serializing response"},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def handle_client_disconnect(func):
    """
    Decorator to handle client disconnections in an endpoint.
    
    Args:
        func: The endpoint function to wrap
        
    Returns:
        Wrapped function with client disconnect handling
    """
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except ClientDisconnect:
            logger.warning(f"Client disconnected during execution of {func.__name__}")
            raise HTTPException(status_code=499, detail="Client Disconnected")
        except h11._util.LocalProtocolError as protocol_error:
            error_message = str(protocol_error)
            if "can't handle event type ConnectionClosed" in error_message or "Can't send data when our state is ERROR" in error_message:
                logger.warning(f"HTTP protocol error (client likely disconnected): {error_message}")
                raise HTTPException(status_code=499, detail="Client Disconnected")
            logger.error(f"HTTP protocol error in {func.__name__}: {error_message}")
            raise HTTPException(status_code=500, detail="HTTP Protocol Error")
    
    return wrapper 