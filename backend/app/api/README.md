# Recruiva API Documentation

## Overview

This document provides comprehensive, up-to-date documentation for the Recruiva API, an AI-driven recruitment platform backend built with FastAPI. The API is organized by logical area, with endpoints grouped and described according to your actual implementation in `backend/app/api/v1` and `backend/app/api/v1/endpoints`.

## Base URL

All endpoints are prefixed with: `/api/v1`

## Authentication

Most endpoints require Firebase Authentication. Supply a valid Firebase ID token in the Authorization header:

```
Authorization: Bearer <firebase_id_token>
```

Some endpoints (notably certain public interview and application endpoints) are accessible without authentication; these are marked accordingly.

## Error Handling

Standard HTTP status codes are used. Error responses:

```json
{
  "detail": "Error message description"
}
```

## API Endpoints

---

## Roles API

Base path: `/api/v1/roles` (authenticated)

Role and job management for hiring teams.

- **POST** `/roles/` — Create a new role
- **GET** `/roles/` — List all roles for the authenticated user
- **GET** `/roles/{role_id}` — Retrieve a specific role
- **PUT** `/roles/{role_id}` — Update a role
- **DELETE** `/roles/{role_id}` — Delete a role
- **PATCH** `/roles/{role_id}/status` — Update role status
- **PATCH** `/roles/{role_id}/priority` — Update role priority
- **POST** `/roles/parse-job-description` — Parse job description file
- **POST** `/roles/migrate-to-team-structure` — Migrate roles to team structure
- **POST** `/roles/{role_id}/intake-recording` — Upload intake recording (file)
- **GET** `/roles/{role_id}/intake-transcripts` — Get all intake transcripts
- **POST** `/roles/{role_id}/intake-transcripts` — Create intake transcript
- **PUT** `/roles/{role_id}/intake-transcripts/{transcript_id}` — Update intake transcript
- **PATCH** `/roles/{role_id}/intake-transcripts/{transcript_id}/complete` — Mark intake transcript complete
- **POST** `/roles/{role_id}/intake-calls` — Create intake call
- **GET** `/roles/{role_id}/intake-calls` — List intake calls

### Public Roles Endpoints

Base path: `/api/v1/public/jobs` (public)

- **GET** `/public/jobs/` — List all published roles
- **GET** `/public/jobs/{role_id}` — Get a published role by ID
- **GET** `/public/jobs/{role_id}/job-posting` — Get job posting for a role

---

## Applications API

Base path: `/api/v1/public/applications` (public)

- **POST** `/public/applications` — Create a new application (for candidates)
  - Request Body: `ApplicationCreate`
  - Response: `ApplicationResponse`

#### Models

- **ApplicationCreate**
  ```json
  {
    "role_id": "string",
    "candidate_id": "string (optional)",
    "email": "string (optional)",
    "full_name": "string (optional)",
    "resume_url": "string (optional)",
    "status": "applied|... (optional)",
    "is_public": true
  }
  ```
- **ApplicationResponse**
  ```json
  {
    "id": "string",
    "role_id": "string",
    "candidate_id": "string|null",
    "email": "string|null",
    "full_name": "string|null",
    "resume_url": "string|null",
    "status": "applied|...",
    "is_public": true,
    "created_at": "timestamp"
  }
  ```

---

## Dashboard API

Base path: `/api/v1/dashboard` (authenticated)

- **GET** `/dashboard/data` — Get all dashboard data (roles, applications, evaluations)
- **GET** `/dashboard/applications` — List all applications for user
- **GET** `/dashboard/applications/{application_id}` — Get application with interview data
- **GET** `/dashboard/interviews` — List all interview sessions for user
- **GET** `/dashboard/interviews/count` — Get interview count
- **GET** `/dashboard/interviews/{interview_id}` — Get a single interview session
- **GET** `/dashboard/evaluations` — Get evaluations (optionally by role)

---

## Evaluation Fix API

Base path: `/api/v1/evaluation-fix` (authenticated)

- **POST** `/evaluation-fix/link-evaluation-to-interview` — Link evaluation to interview
  - Body: `{ "application_id": "string", "interview_id": "string" }`
- **POST** `/evaluation-fix/direct-link-evaluation` — Directly link evaluation ID to interview
  - Body: `{ "interview_id": "string", "evaluation_id": "string" }`

---

## Interview Evaluation API

Base path: `/api/v1/interview-evaluation` (authenticated unless noted)

- **POST** `/interview-evaluation/evaluate` — Evaluate an interview transcript
  - Body: `InterviewEvaluationRequest`
- **GET** `/interview-evaluation/evaluations/{evaluation_id}` — Get an evaluation by ID
- **POST** `/interview-evaluation/evaluate-public` — Evaluate a public interview (no authentication required)
  - Body: `PublicInterviewEvaluationRequest`
- **POST** `/interview-evaluation/auto-evaluate-public/{session_id}` — Auto-evaluate public interview (webhook)

#### Models

- **InterviewEvaluationRequest**
  ```json
  {
    "interview_id": "string",
    "role_id": "string",
    "application_id": "string|null",
    "template_id": "string|null",
    "stage_name": "string|null",
    "stage_index": 0
  }
  ```
- **PublicInterviewEvaluationRequest**
  ```json
  {
    "session_id": "string",
    "role_id": "string",
    "application_id": "string|null",
    "template_id": "string|null",
    "stage_name": "string|null",
    "stage_index": 0,
    "template_data": {},
    "resume_text": "string|null"
  }
  ```
- **EvaluationResponse**
  ```json
  {
    "status": "success|error",
    "message": "string|null",
    "evaluation": { /* evaluation results */ }
  }
  ```

### Debug Endpoints

Base path: `/api/v1/interview-evaluation/debug` (authenticated)

- **GET** `/interview-evaluation/debug/find-session/{session_id}` — Diagnostic info for session
- **GET** `/interview-evaluation/debug/find-evaluations/{role_id}` — List all evaluations for a role
- **POST** `/interview-evaluation/debug/trigger-evaluation/{session_id}` — Manually trigger evaluation

---

## Realtime API

Base path: `/api/v1/realtime` (mixed authentication)

- **POST** `/realtime/session` — Create default session (authenticated)
- **POST** `/realtime/session/{agent_type}` — Create agent-specific session (authenticated)
- **POST** `/realtime/interview-session` — Create interview session (authenticated)
- **POST** `/realtime/public-interview-session` — Create public interview session (public)
- **PUT** `/realtime/public-transcript` — Update public interview transcript (public)
- **POST** `/realtime/end-call` — End a call session (authenticated)
- **POST** `/realtime/end-interview` — End an interview session (authenticated)
- **GET** `/realtime/debug-role/{role_id}` — Debug endpoint for role
- **GET** `/realtime/debug-public-role/{role_id}` — Debug endpoint for public role
- **GET** `/realtime/debug-openai` — Test OpenAI realtime API (debug)
- **GET** `/realtime/debug-prompt-format` — Test prompt formatting (debug)

#### Models

- **SessionResponse**
  ```json
  {
    "session_id": "string",
    "client_secret": { /* ephemeral key info */ },
    "transcript_id": "string|null",
    "role_id": "string|null",
    "template_id": "string|null",
    "candidate_id": "string|null",
    "application_id": "string|null",
    "is_public": true|false
  }
  ```
- **PublicInterviewSessionRequest**
  ```json
  {
    "role_id": "string",
    "template_id": "string|null",
    "candidate_id": "string|null",
    "application_id": "string|null",
    "resume_text": "string|null",
    "job_posting": "string|null"
  }
  ```
- **PublicTranscriptUpdate**
  ```json
  {
    "session_id": "string",
    "transcript_id": "string",
    "role_id": "string",
    "messages": [ { /* message objects */ } ],
    "application_id": "string|null"
  }
  ```

---

## Resume Evaluation API

Base path: `/api/v1/resume-evaluation` (mixed authentication)

- **POST** `/resume-evaluation/evaluate-upload` — Evaluate resume file (PDF/DOCX/TXT)
- **POST** `/resume-evaluation/evaluate` — Evaluate resume text
- **GET** `/resume-evaluation/health` — Health check

#### Models
- **ResumeEvaluationRequest**
  ```json
  {
    "resume_text": "string",
    "job_posting": "string",
    "model": "string (optional)",
    "basic_evaluation": true|false
  }
  ```
- **EvaluationResponse**
  ```json
  {
    "scorecard": {},
    "recommendation": {},
    "feedback": {},
    "metadata": {}
  }
  ```
- **BasicEvaluationResponse**
  ```json
  {
    "overallScore": 4.5,
    "decision": "string",
    "confidence": "string",
    "reasoning": "string",
    "keyStrengths": ["string"],
    "keyGaps": ["string"],
    "metadata": {}
  }
  ```

---

## Chat Completion API

Base path: `/api/v1/chat-completion` (authenticated)

- **POST** `/chat-completion/generate` — Generate a chat completion
  - Body: `ChatCompletionRequest`
  - Response: `{ "success": true|false, "response": ..., "model": "string", "use_case": "string" }`

#### Models
- **ChatCompletionRequest**
  ```json
  {
    "model_configuration": { "name": "gpt-4o", "parameters": {} },
    "prompt_template": {
      "template_name": "string",
      "template_variables": {}
    },
    "functions": [ { "name": "string", "description": "string", "parameters": {} } ],
    "use_case": "string",
    "context": {}
  }
  ```

---

## Auth API

- **Dependency:** Most endpoints use `Depends(get_current_user)` for authentication, which verifies the Firebase token and fetches user info from Firestore.
- **User Info:**
  ```json
  {
    "id": "firebase_uid",
    "email": "<EMAIL>",
    "role": "user|admin|..."
  }
  ```

---

## Data Models

### ClientSecret
```json
{
  "value": "string",
  "expires_at": 1234567890
}
```

### SessionResponse
```json
{
  "session_id": "string",
  "client_secret": { /* see above */ },
  "transcript_id": "string|null",
  "role_id": "string|null",
  "template_id": "string|null",
  "candidate_id": "string|null",
  "application_id": "string|null",
  "is_public": true|false
}
```

---

## Notes

- All endpoints return JSON responses.
- Timestamps are in ISO 8601 format unless otherwise noted.
- Some endpoints may return additional fields for backward compatibility (e.g., both `roleId` and `role_id`).
- For full details on request/response fields, refer to the Pydantic models in the codebase.

---

This documentation is now fully synchronized with your actual backend implementation as of 2025-05-12.

##### Update Role Status

- **PATCH** `/{role_id}/status`
- Updates the status of a specific role
- Request Body: `RoleStatusUpdate` object
- Response: `Role` object

##### Update Role Priority

- **PATCH** `/{role_id}/priority`
- Updates the priority of a specific role
- Request Body: `RolePriorityUpdate` object
- Response: `Role` object

#### Job Description Processing

##### Parse Job Description

- **POST** `/parse-job-description`
- Parses a job description file and extracts structured data
- Request Body: Multipart form with file
- Response: Parsed job description text

##### Migrate to Team Structure

- **POST** `/migrate-to-team-structure`
- Migrates roles to the team structure
- Response: Success message

#### Intake Management

##### Upload Intake Recording

- **POST** `/{role_id}/intake-recording`
- Uploads an intake recording for a specific role
- Request Body: Multipart form with file
- Response: Upload confirmation

##### Get Intake Transcripts

- **GET** `/{role_id}/intake-transcripts`
- Gets all intake transcripts for a specific role
- Response: Array of `Transcript` objects

##### Create Intake Transcript

- **POST** `/{role_id}/intake-transcripts`
- Creates a new intake transcript for a specific role
- Request Body: `TranscriptCreate` object
- Response: `Transcript` object

##### Update Intake Transcript

- **PUT** `/{role_id}/intake-transcripts/{transcript_id}`
- Updates an intake transcript for a specific role
- Request Body: `TranscriptUpdate` object
- Response: `Transcript` object

##### Complete Intake Transcript

- **PATCH** `/{role_id}/intake-transcripts/{transcript_id}/complete`
- Marks an intake transcript as complete
- Response: `Transcript` object

##### Create Intake Call

- **POST** `/{role_id}/intake-calls`
- Creates a new intake call for a specific role
- Request Body: `IntakeCallCreate` object
- Response: `IntakeCall` object

##### List Intake Calls

- **GET** `/{role_id}/intake-calls`
- Lists all intake calls for a specific role
- Response: Array of `IntakeCall` objects

### Realtime API

Base path: `/api/v1/realtime`

#### Session Management

##### Create Default Session

- **POST** `/session`
- Creates a new realtime session with the default intake agent (for backward compatibility)
- Response: `SessionResponse` object with session ID and client secret

##### Create Agent-Specific Session

- **POST** `/session/{agent_type}`
- Creates a new realtime session with the specified agent type
- Path Parameters:
  - `agent_type`: The type of agent to use (e.g., `intake_agent`, `interview_agent`)
- Response: `SessionResponse` object with session ID and client secret

##### End Call

- **POST** `/end-call`
- Processes the end of a call with transcript generation
- Request Body: `EndCallRequest` object
- Response: Success message

### Authentication API

Base path: `/api/v1/auth`

#### User Management

##### Register User

- **POST** `/register`
- Registers a new user
- Request Body: `UserRegister` object
- Response: User information

##### Verify Token

- **POST** `/verify-token`
- Verifies a Firebase ID token
- Request Body: `TokenVerify` object
- Response: User information

## Data Models

### Role Models

#### RoleCreate

```json
{
  "title": "string",
  "summary": "string",
  "level": "string",
  "status": "string",
  "priority": "string",
  "team": "string",
  "keyResponsibilities": ["string"],
  "requiredSkills": {"key": "value"},
  "preferredSkills": {"key": "value"},
  "yearsOfExperience": "string",
  "education": {
    "value": "string",
    "isRequired": true
  },
  "certificates": ["string"],
  "location": {
    "city": "string",
    "remoteStatus": "string"
  },
  "jobType": "string",
  "compensation": {
    "range": "string",
    "currency": "string",
    "equity": true
  },
  "benefits": {
    "healthInsurance": true,
    "vacationDays": 0,
    "additionalBenefits": ["string"]
  },
  "startDate": "string",
  "hiringManagerId": "string",
  "hiringManagerContact": "string",
  "aboutCompany": "string",
  "aboutTeam": "string",
  "keyStakeholders": ["string"],
  "interviewProcess": [
    {
      "stage": "string",
      "duration": "string",
      "customInstructions": "string"
    }
  ]
}
```

#### RoleUpdate

Similar to RoleCreate but all fields are optional.

#### RoleStatusUpdate

```json
{
  "status": "string"
}
```

#### RolePriorityUpdate

```json
{
  "priority": "string"
}
```

### Transcript Models

#### TranscriptCreate

```json
{
  "title": "string",
  "content": "string",
  "metadata": {
    "key": "value"
  }
}
```

#### TranscriptUpdate

```json
{
  "title": "string",
  "content": "string",
  "metadata": {
    "key": "value"
  }
}
```

### Intake Call Models

#### IntakeCallCreate

```json
{
  "title": "string",
  "scheduledTime": "string",
  "participants": ["string"],
  "metadata": {
    "key": "value"
  }
}
```

### Realtime Models

#### SessionResponse

```json
{
  "session_id": "string",
  "client_secret": {
    "key": "value"
  }
}
```

#### EndCallRequest

```json
{
  "role_id": "string",
  "transcript_id": "string",
  "user_id": "string",
  "is_enrichment": false,
  "role_data": {
    "key": "value"
  },
  "agent_type": "string"
}
```

### Authentication Models

#### UserRegister

```json
{
  "email": "string",
  "name": "string",
  "company": "string",
  "role": "string"
}
```

#### TokenVerify

```json
{
  "token": "string",
  "is_registering": false
}
```

## Implementation Details

### Roles API Implementation

The Roles API is implemented in `app/api/v1/endpoints/roles.py` and uses the `RolesService` for business logic. The service handles role creation, retrieval, updating, and deletion, as well as intake management.

### Realtime API Implementation

The Realtime API is implemented in `app/api/v1/endpoints/realtime.py` and uses a modular agent architecture for real-time interactions. The API supports different agent types through the `AgentLoader` utility, which dynamically loads agent-specific components.

#### Agent Architecture

The agent architecture consists of:

1. **Agent Loader**: Dynamically loads agent components based on the agent type
2. **Agent Components**:
   - Context: Prepares the context for the agent
   - Functions: Defines the capabilities of the agent
   - Services: Implements the business logic
   - Prompts: Defines the agent's behavior

#### Supported Agent Types

- `intake_agent`: For intake calls to gather role requirements
- `interview_agent`: For conducting interviews with candidates

### Authentication API Implementation

The Authentication API is implemented in `app/api/v1/auth.py` and uses the `AuthService` for business logic. The service handles user registration, token verification, and user management.

## Best Practices

### API Usage

1. Always include the Authorization header with a valid Firebase ID token
2. Handle error responses appropriately
3. Use proper content types for requests
4. Follow the RESTful principles

### Error Handling

1. Check the status code of the response
2. Parse the error message from the response
3. Implement proper retry logic for transient errors
4. Log errors for debugging

### Performance Considerations

1. Use pagination for large result sets
2. Minimize the number of API calls
3. Use appropriate caching strategies
4. Optimize request payloads

## API Versioning

The API is versioned using the URL path (`/api/v1`). Future versions will be available at `/api/v2`, etc.

## Rate Limiting

The API implements rate limiting to prevent abuse. Clients should implement proper retry logic with exponential backoff.

## Security Considerations

1. Always use HTTPS
2. Keep Firebase tokens secure
3. Implement proper token refresh logic
4. Follow the principle of least privilege

## Support

For API support or questions, please contact the development <NAME_EMAIL>.

## Interview Templates API

Base path: `/templates`

### List templates for a role

**Endpoint:** `GET /templates/{role_id}`

**Authentication:** Required

**Response:**
```json
[
  {
    "id": "template123",
    "stageIndex": 0,
    "stage": "Screening",
    "duration": "15 minutes",
    "customInstructions": "Initial screening to assess candidate's basic qualifications and interest.",
    "interviewGuide": "# Screening Interview Guide\n\nThis is a basic guide for conducting the Screening interview.",
    "questions": [],
    "evaluationGuide": "# Screening Evaluation Guide\n\nUse this guide to evaluate candidate responses.",
    "evaluationCriteria": [],
    "status": "Draft",
    "statistics": {
      "candidatesInterviewed": 0,
      "passRate": 0,
      "averageScore": 0,
      "topScore": 0,
      "topPerformerIds": []
    },
    "createdAt": "2023-05-01T12:00:00Z",
    "updatedAt": "2023-05-01T12:00:00Z",
    "createdBy": "user123"
  }
]
```

### Get a specific template

**Endpoint:** `GET /templates/{role_id}/{template_id}`

**Authentication:** Required

**Response:**
```json
{
  "id": "template123",
  "stageIndex": 0,
  "stage": "Screening",
  "duration": "15 minutes",
  "customInstructions": "Initial screening to assess candidate's basic qualifications and interest.",
  "interviewGuide": "# Screening Interview Guide\n\nThis is a basic guide for conducting the Screening interview.",
  "questions": [],
  "evaluationGuide": "# Screening Evaluation Guide\n\nUse this guide to evaluate candidate responses.",
  "evaluationCriteria": [],
  "status": "Draft",
  "statistics": {
    "candidatesInterviewed": 0,
    "passRate": 0,
    "averageScore": 0,
    "topScore": 0,
    "topPerformerIds": []
  },
  "createdAt": "2023-05-01T12:00:00Z",
  "updatedAt": "2023-05-01T12:00:00Z",
  "createdBy": "user123"
}
```

### Create a new template

**Endpoint:** `POST /templates/{role_id}`

**Authentication:** Required

**Request Body:**
```json
{
  "stageIndex": 1,
  "stage": "Technical",
  "duration": "45 minutes",
  "customInstructions": "Technical interview to assess coding skills.",
  "interviewGuide": "# Technical Interview Guide",
  "questions": [],
  "evaluationGuide": "# Technical Evaluation Guide",
  "evaluationCriteria": [],
  "status": "Draft"
}
```

**Response:**
```json
{
  "id": "template456",
  "stageIndex": 1,
  "stage": "Technical",
  "duration": "45 minutes",
  "customInstructions": "Technical interview to assess coding skills.",
  "interviewGuide": "# Technical Interview Guide",
  "questions": [],
  "evaluationGuide": "# Technical Evaluation Guide",
  "evaluationCriteria": [],
  "status": "Draft",
  "statistics": {
    "candidatesInterviewed": 0,
    "passRate": 0,
    "averageScore": 0,
    "topScore": 0,
    "topPerformerIds": []
  },
  "createdAt": "2023-05-01T12:00:00Z",
  "updatedAt": "2023-05-01T12:00:00Z",
  "createdBy": "user123"
}
```

### Update a template

**Endpoint:** `PUT /templates/{role_id}/{template_id}`

**Authentication:** Required

**Request Body:**
```json
{
  "duration": "60 minutes",
  "customInstructions": "Updated instructions",
  "status": "Active"
}
```

**Response:**
```json
{
  "id": "template123",
  "stageIndex": 0,
  "stage": "Screening",
  "duration": "60 minutes",
  "customInstructions": "Updated instructions",
  "interviewGuide": "# Screening Interview Guide\n\nThis is a basic guide for conducting the Screening interview.",
  "questions": [],
  "evaluationGuide": "# Screening Evaluation Guide\n\nUse this guide to evaluate candidate responses.",
  "evaluationCriteria": [],
  "status": "Active",
  "statistics": {
    "candidatesInterviewed": 0,
    "passRate": 0,
    "averageScore": 0,
    "topScore": 0,
    "topPerformerIds": []
  },
  "createdAt": "2023-05-01T12:00:00Z",
  "updatedAt": "2023-05-01T13:00:00Z",
  "createdBy": "user123"
}
```

## Question Management API

### Add a question to a template

**Endpoint:** `POST /templates/{role_id}/{template_id}/questions`

**Authentication:** Required

**Request Body:**
```json
{
  "question": "What is your experience with Python?",
  "purpose": "Assess technical skills and experience level",
  "idealAnswerCriteria": "Candidate should demonstrate 3+ years of Python experience with specific examples of projects"
}
```

**Response:**
```json
{
  "id": "q123abc",
  "question": "What is your experience with Python?",
  "purpose": "Assess technical skills and experience level",
  "idealAnswerCriteria": "Candidate should demonstrate 3+ years of Python experience with specific examples of projects",
  "statistics": {
    "topScore": 0,
    "topScoringCandidateId": "",
    "averageScore": 0
  }
}
```

### Update a question

**Endpoint:** `PUT /templates/{role_id}/{template_id}/questions/{question_id}`

**Authentication:** Required

**Request Body:**
```json
{
  "question": "Updated question text",
  "purpose": "Updated purpose",
  "idealAnswerCriteria": "Updated criteria"
}
```

**Response:**
```json
{
  "id": "q123abc",
  "question": "Updated question text",
  "purpose": "Updated purpose",
  "idealAnswerCriteria": "Updated criteria",
  "statistics": {
    "topScore": 0,
    "topScoringCandidateId": "",
    "averageScore": 0
  }
}
```

### Delete a question

**Endpoint:** `DELETE /templates/{role_id}/{template_id}/questions/{question_id}`

**Authentication:** Required

**Response:** 204 No Content

### Reorder questions

**Endpoint:** `PUT /templates/{role_id}/{template_id}/questions/reorder`

**Authentication:** Required

**Request Body:**
```json
{
  "questionIds": ["q123abc", "q456def", "q789ghi"]
}
```

**Response:**
```json
[
  {
    "id": "q123abc",
    "question": "Question 1",
    "purpose": "Purpose 1",
    "idealAnswerCriteria": "Criteria 1",
    "statistics": {
      "topScore": 0,
      "topScoringCandidateId": "",
      "averageScore": 0
    }
  },
  {
    "id": "q456def",
    "question": "Question 2",
    "purpose": "Purpose 2",
    "idealAnswerCriteria": "Criteria 2",
    "statistics": {
      "topScore": 0,
      "topScoringCandidateId": "",
      "averageScore": 0
    }
  },
  {
    "id": "q789ghi",
    "question": "Question 3",
    "purpose": "Purpose 3",
    "idealAnswerCriteria": "Criteria 3",
    "statistics": {
      "topScore": 0,
      "topScoringCandidateId": "",
      "averageScore": 0
    }
  }
]
```

## Chat Completion API

Base path: `/chat-completion`

### Create chat completion

**Endpoint:** `POST /chat-completion`

**Authentication:** Required

**Request Body:**
```json
{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "model": "gpt-4",
  "temperature": 0.7
}
```

**Response:**
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677858242,
  "model": "gpt-4",
  "choices": [
    {
      "message": {
        "role": "assistant",
        "content": "I'm doing well, thank you for asking! How can I assist you today?"
      },
      "finish_reason": "stop",
      "index": 0
    }
  ],
  "usage": {
    "prompt_tokens": 13,
    "completion_tokens": 15,
    "total_tokens": 28
  }
}
```

## Templates API

### Endpoints

- `GET /templates/{role_id}` - List all templates for a role
- `GET /templates/{role_id}/{template_id}` - Get a specific template
- `POST /templates/{role_id}` - Create a new template
- `PUT /templates/{role_id}/{template_id}` - Update a template
- `POST /templates/{role_id}/{template_id}/questions` - Add a question to a template
- `PUT /templates/{role_id}/{template_id}/questions/{question_id}` - Update a question
- `DELETE /templates/{role_id}/{template_id}/questions/{question_id}` - Delete a question
- `PUT /templates/{role_id}/{template_id}/questions/reorder` - Reorder questions

#### Evaluation Criteria Endpoints

- `POST /templates/{role_id}/{template_id}/criteria` - Add an evaluation criterion
- `PUT /templates/{role_id}/{template_id}/criteria/{criterion_id}` - Update a criterion
- `DELETE /templates/{role_id}/{template_id}/criteria/{criterion_id}` - Delete a criterion
- `PUT /templates/{role_id}/{template_id}/pass-rate` - Set the pass rate

### Evaluation Criteria Types

The API supports the following evaluation criterion types:

1. **ScoreCard** - Weighted scoring criteria for competencies
   ```json
   {
     "type": "ScoreCard",
     "competency": "Strategic Thinking",
     "weight": 0.3,
     "criteria": "Can they solve complex problems logically and proactively?",
     "description": "Look for structured approach to problem-solving"
   }
   ```

2. **BetweenTheLines** - Qualitative assessments without scoring
   ```json
   {
     "type": "BetweenTheLines",
     "criteria": "Do they show structured thought processes?",
     "description": "Observe how they approach the problem"
   }
   ```

3. **Disqualifier** - Conditions that would disqualify a candidate
   ```json
   {
     "type": "Disqualifier",
     "criteria": "No structured problem-solving approach",
     "description": "Candidate should demonstrate a clear methodology"
   }
   ```

### Pass Rate

The pass rate is a threshold value between 0 and 1 (representing 0% to 100%) that determines whether a candidate passes the interview stage.

```json
{
  "passRate": 0.85
}
```
