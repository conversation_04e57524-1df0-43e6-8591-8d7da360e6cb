# File: backend/app/api/v1/endpoints/interview_evaluation.py

from fastapi import APIRouter, HTTPException, status, Body, Depends, Path, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

from app.services.interview_evaluation_service import InterviewEvaluationService
from app.services.firebase_service import FirebaseService
from ..auth import get_current_user

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Initialize services
firebase_service = FirebaseService()

# Request models
class InterviewEvaluationRequest(BaseModel):
    """Request model for evaluating an interview"""
    interview_id: str = Field(..., description="ID of the interview transcript")
    role_id: str = Field(..., description="ID of the role")
    application_id: Optional[str] = Field(None, description="Optional ID of the application")
    template_id: Optional[str] = Field(None, description="Optional ID of the interview template")
    stage_name: Optional[str] = Field(None, description="Optional name of the interview stage")
    stage_index: Optional[int] = Field(None, description="Optional index of the interview stage")

class PublicInterviewEvaluationRequest(BaseModel):
    """Request model for evaluating a public interview"""
    session_id: str = Field(..., description="ID of the public interview session")
    role_id: str = Field(..., description="ID of the role")
    application_id: Optional[str] = Field(None, description="Optional ID of the application")
    template_id: Optional[str] = Field(None, description="Optional ID of the interview template")
    stage_name: Optional[str] = Field(None, description="Optional name of the interview stage")
    stage_index: Optional[int] = Field(None, description="Optional index of the interview stage")
    template_data: Optional[Dict[str, Any]] = Field(None, description="Optional template data to use for evaluation")
    resume_text: Optional[str] = Field(None, description="Optional resume text to use for evaluation")

# Response models
class EvaluationResponse(BaseModel):
    """Response model for evaluation results"""
    status: str = Field(..., description="Status of the evaluation (success or error)")
    message: Optional[str] = Field(None, description="Error message if status is error")
    evaluation: Optional[Dict[str, Any]] = Field(None, description="Evaluation results if status is success")

# Endpoints
@router.post("/evaluate", response_model=EvaluationResponse)
async def evaluate_interview(
    request: InterviewEvaluationRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Evaluate an interview transcript for an authenticated user.

    This endpoint:
    1. Retrieves the interview transcript
    2. Gathers required data (job posting, resume, questions, criteria)
    3. Calls the OpenAI API with the interview evaluation agent
    4. Parses and validates the response
    5. Stores the evaluation results
    6. Returns the evaluation to the client

    Authentication is required.
    """
    try:
        logger.info(f"Authenticated evaluation request for interview {request.interview_id}")

        # Get user ID from authenticated user
        user_id = current_user.get("id")  # Changed from "uid" to "id" to match auth.py
        if not user_id:
            logger.error("User ID not found in authenticated user data")
            # Log the current_user object for debugging
            logger.error(f"Current user data: {current_user}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials"
            )

        # Call the evaluation service
        result = await InterviewEvaluationService.evaluate_interview_authenticated(
            request.interview_id,
            request.role_id,
            request.application_id,
            user_id,
            request.template_id,
            request.stage_name,
            request.stage_index
        )

        if result.get("status") == "error":
            logger.error(f"Error evaluating interview: {result.get('message')}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("message", "Error evaluating interview")
            )

        # Ensure the response includes evaluation_id
        if "evaluation" in result and "evaluation_id" in result["evaluation"]:
            # Add evaluation_id to the top level for frontend compatibility
            result["evaluation_id"] = result["evaluation"]["evaluation_id"]
        elif "evaluation" in result and "id" in result["evaluation"]:
            # Use id as evaluation_id if available
            result["evaluation_id"] = result["evaluation"]["id"]
        else:
            # Generate a unique ID if none exists
            import uuid
            result["evaluation_id"] = f"eval-{uuid.uuid4()}"
            logger.warning(f"Generated new evaluation ID: {result['evaluation_id']}")

        logger.info(f"Successfully evaluated interview {request.interview_id} with evaluation ID: {result.get('evaluation_id')}")
        return result

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.exception(f"Unexpected error in evaluate_interview: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}"
        )

@router.post("/public/evaluate", response_model=EvaluationResponse)
async def evaluate_public_interview(
    request: PublicInterviewEvaluationRequest
):
    """
    Evaluate a public interview session without authentication.

    This endpoint:
    1. Retrieves the public interview session
    2. Gathers required data (job posting, resume, questions, criteria)
    3. Calls the OpenAI API with the interview evaluation agent
    4. Parses and validates the response
    5. Stores the evaluation results in public collections
    6. Returns the evaluation to the client

    No authentication required.
    """
    try:
        logger.info(f"Public evaluation request for session {request.session_id} with role_id={request.role_id}, application_id={request.application_id}")

        # Call the public evaluation service
        result = await InterviewEvaluationService.evaluate_interview_public(
            request.session_id,
            request.role_id,
            request.application_id,
            request.template_id,
            request.stage_name,
            request.stage_index,
            None,  # owner_user_id will be determined by the service
            request.template_data,  # Pass template data
            request.resume_text     # Pass resume text if provided
        )

        if result.get("status") == "error":
            logger.error(f"Error evaluating public interview: {result.get('message')}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("message", "Error evaluating interview")
            )

        # Ensure the response includes evaluation_id
        if "evaluation" in result and "evaluation_id" in result["evaluation"]:
            # Add evaluation_id to the top level for frontend compatibility
            result["evaluation_id"] = result["evaluation"]["evaluation_id"]
        elif "evaluation" in result and "id" in result["evaluation"]:
            # Use id as evaluation_id if available
            result["evaluation_id"] = result["evaluation"]["id"]
        else:
            # Generate a unique ID if none exists
            import uuid
            result["evaluation_id"] = f"eval-{uuid.uuid4()}"
            logger.warning(f"Generated new evaluation ID: {result['evaluation_id']}")

        logger.info(f"Successfully evaluated public interview {request.session_id} with evaluation ID: {result.get('evaluation_id')}")
        return result

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.exception(f"Unexpected error in evaluate_public_interview: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}"
        )

@router.post("/public/auto-evaluate/{session_id}", response_model=EvaluationResponse)
async def auto_evaluate_public_interview(
    session_id: str = Path(..., description="ID of the public interview session"),
    request: dict = Body(default={}),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Automatically evaluate a public interview session at the end of the session.
    
    This endpoint is designed to be called as a webhook at the end of public interview sessions.
    It can be triggered by the frontend or other services without authentication.
    
    It accepts an optional request body with role_id and application_id to help identify the session
    when the session information in Firestore might be incomplete.

    The evaluation is always performed asynchronously in the background to avoid blocking.
    """
    try:
        # Extract optional parameters from request body if provided
        role_id = request.get("role_id")
        application_id = request.get("application_id")
        template_id = request.get("template_id")
        stage_name = request.get("stage_name")
        stage_index = request.get("stage_index")
        template_data = request.get("template_data")
        
        logger.info(f"Auto-evaluation request for public session {session_id} with role_id={role_id}, application_id={application_id}, template_id={template_id}")

        # Always run the evaluation in the background
        logger.info(f"Scheduling background evaluation for session {session_id}")

        async def run_evaluation():
            try:
                logger.info(f"Starting background evaluation for session {session_id}")
                result = await InterviewEvaluationService.auto_evaluate_public_interview(
                    session_id, 
                    explicit_role_id=role_id,
                    explicit_application_id=application_id,
                    explicit_template_id=template_id,
                    explicit_stage_name=stage_name,
                    explicit_stage_index=stage_index,
                    explicit_template_data=template_data
                )
                
                if result.get("status") == "error":
                    logger.error(f"Background evaluation failed: {result.get('message')}")
                else:
                    evaluation_id = result.get("evaluation_id")
                    if "evaluation" in result and "evaluation_id" in result["evaluation"]:
                        evaluation_id = result["evaluation"]["evaluation_id"]
                    elif "evaluation" in result and "id" in result["evaluation"]:
                        evaluation_id = result["evaluation"]["id"]
                    
                    logger.info(f"Background evaluation completed successfully for session {session_id} with evaluation ID: {evaluation_id}")
            except Exception as e:
                logger.error(f"Background evaluation failed with exception: {str(e)}", exc_info=True)

        # Add the evaluation task to the background
        background_tasks.add_task(run_evaluation)

        # Return immediately with success message
        return {
            "status": "success",
            "message": f"Evaluation for session {session_id} scheduled in background"
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.exception(f"Unexpected error in auto_evaluate_public_interview: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}"
        )

@router.get("/evaluations/{evaluation_id}")
async def get_evaluation(
    evaluation_id: str = Path(..., description="ID of the evaluation"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get an evaluation by ID for an authenticated user.

    This endpoint:
    1. Checks if the evaluation exists in the user's collection
    2. If not, checks if it exists in the global evaluations collection
    3. Returns the evaluation data

    Authentication is required.
    """
    try:
        logger.info(f"Getting evaluation {evaluation_id} for user {current_user.get('id')}")
        user_id = current_user.get("id")

        # First, try to find the evaluation in the user's roles collection
        # We need to search across all roles since we don't know which role it belongs to
        roles_ref = firebase_service.db.collection("users").document(user_id).collection("roles")
        roles = list(roles_ref.stream())

        for role in roles:
            role_id = role.id
            # Check all interviews in this role
            interviews_ref = roles_ref.document(role_id).collection("interviews")
            interviews = list(interviews_ref.stream())

            for interview in interviews:
                interview_id = interview.id
                # Check if this interview has the evaluation we're looking for
                eval_ref = interviews_ref.document(interview_id).collection("evaluations").document(evaluation_id)
                eval_doc = eval_ref.get()

                if eval_doc.exists:
                    logger.info(f"Found evaluation {evaluation_id} in user's collection")
                    evaluation_data = eval_doc.to_dict()
                    return {
                        "status": "success",
                        "evaluation": {
                            "id": evaluation_id,
                            "data": evaluation_data,
                            "createdAt": evaluation_data.get("created_at", datetime.now())
                        }
                    }

        # If not found in user's collection, try the global evaluations collection
        eval_ref = firebase_service.db.collection("evaluations").document(evaluation_id)
        eval_doc = eval_ref.get()

        if eval_doc.exists:
            logger.info(f"Found evaluation {evaluation_id} in global collection")
            evaluation_data = eval_doc.to_dict()
            return {
                "status": "success",
                "evaluation": {
                    "id": evaluation_id,
                    "data": evaluation_data,
                    "createdAt": evaluation_data.get("created_at", datetime.now())
                }
            }

        # If we get here, the evaluation wasn't found
        logger.error(f"Evaluation {evaluation_id} not found")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Evaluation {evaluation_id} not found"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Unexpected error in get_evaluation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error: {str(e)}"
        )
