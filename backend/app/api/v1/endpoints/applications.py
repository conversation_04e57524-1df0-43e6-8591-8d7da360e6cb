# File: backend/app/api/v1/endpoints/applications.py

from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Body
from pydantic import BaseModel, Field
import logging
import uuid
from firebase_admin import firestore
from firebase_admin.firestore import SERVER_TIMESTAMP

# Configure logging
logger = logging.getLogger(__name__)

# Initialize Firestore client
db = firestore.client()

# Setup router
router = APIRouter()

# Models
class ApplicationCreate(BaseModel):
    """Model for creating an application"""
    role_id: str = Field(..., description="ID of the role")
    candidate_id: Optional[str] = Field(None, description="ID of the candidate")
    email: Optional[str] = Field(None, description="Email of the candidate")
    full_name: Optional[str] = Field(None, description="Full name of the candidate")
    resume_url: Optional[str] = Field(None, description="URL to the resume")
    status: Optional[str] = Field("applied", description="Status of the application")
    is_public: Optional[bool] = Field(True, description="Whether the application is public")

class ApplicationResponse(BaseModel):
    """Model for application response"""
    id: str
    role_id: str
    candidate_id: Optional[str] = None
    email: Optional[str] = None
    full_name: Optional[str] = None
    resume_url: Optional[str] = None
    status: str
    is_public: bool
    created_at: str

@router.post("/applications", response_model=ApplicationResponse, tags=["applications"])
async def create_application(
    application: ApplicationCreate = Body(...),
) -> Dict[str, Any]:
    """
    Create a new application.

    This endpoint creates a new application document in Firestore.
    """
    try:
        # Generate a unique application ID
        application_id = str(uuid.uuid4())

        # Prepare application data with correct status
        application_data = {
            "id": application_id,
            "roleId": application.role_id,
            "status": application.status,  # Now defaults to "applied" from the model
            "created_at": SERVER_TIMESTAMP,
            "updated_at": SERVER_TIMESTAMP,
            "is_public": application.is_public
        }

        # Add optional fields if provided
        if application.candidate_id:
            application_data["candidateId"] = application.candidate_id

        if application.email:
            application_data["email"] = application.email
            # Use email as candidateId if not provided
            if not application.candidate_id:
                application_data["candidateId"] = application.email

        if application.full_name:
            application_data["fullName"] = application.full_name

        if application.resume_url:
            application_data["resumeUrl"] = application.resume_url

        # Create candidate document if email is provided
        if application.email:
            try:
                # Prepare candidate data
                candidate_data = {
                    "email": application.email,
                    "created_at": SERVER_TIMESTAMP,
                    "updated_at": SERVER_TIMESTAMP
                }

                # Add name if provided
                if application.full_name:
                    candidate_data["fullName"] = application.full_name

                # Save to candidates collection
                candidates_ref = db.collection("candidates").document(application.email)
                candidates_ref.set(candidate_data, merge=True)
                logger.info(f"Candidate document created/updated with ID: {application.email}")

                # Also save application as subcollection under candidate
                candidate_app_ref = db.collection("candidates").document(application.email).collection("applications").document(application_id)
                candidate_app_ref.set(application_data)
                logger.info(f"Application document created in candidate subcollection with ID: {application_id}")
            except Exception as candidate_error:
                logger.warning(f"Failed to create/update candidate document: {str(candidate_error)}")

        # Save to applications collection - single source of truth
        applications_ref = db.collection("applications").document(application_id)
        applications_ref.set(application_data)
        logger.info(f"Application document created in applications collection with ID: {application_id}")

        # Return the application data
        return {
            "id": application_id,
            "role_id": application.role_id,
            "candidate_id": application_data.get("candidateId"),
            "email": application.email,
            "full_name": application.full_name,
            "resume_url": application.resume_url,
            "status": application.status,
            "is_public": application.is_public,
            "created_at": str(SERVER_TIMESTAMP)
        }
    except Exception as e:
        logger.error(f"Error creating application: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create application: {str(e)}")
