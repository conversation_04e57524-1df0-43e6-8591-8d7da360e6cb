# File: backend/app/api/v1/endpoints/resume_evaluation.py

from typing import Dict, Any, List, Optional, Callable, TypeVar, ParamSpec, Union
from fastapi import APIRouter, HTTPException, UploadFile, File, Depends, Body, Query, Path, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, validator
from enum import Enum
import logging
from functools import wraps
import json
import uuid

from app.services.resume_parser_service import ResumeParserService
from app.services.resume_evaluator_service import ResumeEvaluatorService
from app.services.firebase_service import FirebaseService
from app.services.auth_service import AuthService
from app.services.roles_service import RolesService
from firebase_admin import firestore
from firebase_admin.firestore import SERVER_TIMESTAMP

# Configure logging
logger = logging.getLogger(__name__)

# Initialize Firestore client
db = firestore.client()

# Type variables for generic function signatures
T = TypeVar("T")
P = ParamSpec("P")

# Setup models
class EvaluationResponse(BaseModel):
    """Model for evaluation response"""
    scorecard: Dict[str, Any]
    recommendation: Dict[str, Any]
    feedback: Dict[str, Any]
    metadata: Dict[str, Any]

class BasicEvaluationResponse(BaseModel):
    """Model for basic evaluation response"""
    overallScore: float = Field(..., ge=1, le=5)
    decision: str
    confidence: str
    reasoning: str
    keyStrengths: List[str]
    keyGaps: List[str]
    metadata: Dict[str, Any]

class ResumeEvaluationRequest(BaseModel):
    """Model for resume evaluation request with text instead of file upload"""
    resume_text: str = Field(..., min_length=10)
    job_posting: str = Field(..., min_length=10)
    model: Optional[str] = Field(default="gpt-4o")
    basic_evaluation: Optional[bool] = Field(default=False)

# Custom exception handler
def handle_evaluation_errors(func: Callable[P, T]) -> Callable[P, T]:
    """Decorator to handle evaluation errors consistently."""
    @wraps(func)
    async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
        try:
            return await func(*args, **kwargs)
        except ValueError as e:
            logger.error(f"Validation error in evaluation: {str(e)}")
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            logger.error(f"Unexpected error in evaluation: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Evaluation failed: {str(e)}")
    return wrapper

# Initialize services
resume_parser_service = ResumeParserService()
resume_evaluator_service = ResumeEvaluatorService()
firebase_service = FirebaseService()
auth_service = AuthService()
roles_service = RolesService()
security = HTTPBearer(auto_error=False)  # Make authentication optional

# Setup router
router = APIRouter(
    prefix="/resume-evaluation",
    tags=["resume-evaluation"]
)

# Define API endpoints
@router.post("/evaluate-upload", response_model=Union[EvaluationResponse, BasicEvaluationResponse])
@handle_evaluation_errors
async def evaluate_resume_upload(
    request: Request,
    resume_file: UploadFile = File(...),
    job_id: Optional[str] = Query(None, description="Job/Role ID to evaluate against"),
    job_posting: Optional[str] = Body(None, description="Job posting text to evaluate against"),
    model: Optional[str] = Query("gpt-4o", description="OpenAI model to use for evaluation"),
    basic_evaluation: Optional[bool] = Query(False, description="Use simplified evaluation for faster results"),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
):
    """
    Evaluate a resume against a job posting or role

    - Upload a resume file (PDF, DOCX, or TXT)
    - Provide either a job_id or job_posting text
    - Returns a comprehensive evaluation
    """
    # Authenticate user if credentials provided
    user_id = None
    if credentials:
        try:
            token = credentials.credentials
            user = await auth_service.verify_token(token)
            user_id = user.get('id')
            logger.info(f"Authenticated user: {user_id}")
        except Exception as e:
            logger.warning(f"Authentication failed: {str(e)}")
            # Continue without authentication

    # Parse resume text
    try:
        parse_result = await resume_parser_service.parse_resume(resume_file)
        resume_text = parse_result["text"]
        logger.info(f"Successfully parsed resume, length: {len(resume_text)} characters")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Failed to parse resume: {str(e)}")

    # Get job posting text - either from job_id or directly provided
    if job_id:
        try:
            # If user_id is available, use it to get the role
            if user_id:
                role = await firebase_service.get_role(user_id=user_id, role_id=job_id)
            else:
                # Try to get public role using RolesService instead of FirebaseService
                role = await roles_service.get_public_role(role_id=job_id)

            if not role:
                logger.error(f"Role not found: {job_id}")
                raise HTTPException(status_code=404, detail=f"Role not found: {job_id}")

            # Use job posting from role if available
            if "jobPosting" in role and role["jobPosting"]:
                job_posting = role["jobPosting"]
                logger.info(f"Using job posting from role {job_id}, length: {len(job_posting)} characters")
            else:
                # Fall back to role description
                job_posting = json.dumps(role)
                logger.info(f"No job posting found for role {job_id}, using role data as fallback")

        except Exception as e:
            error_msg = f"Failed to retrieve role: {str(e)}"
            logger.error(f"Error retrieving role: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=error_msg)
    elif not job_posting:
        raise HTTPException(status_code=400, detail="Either job_id or job_posting must be provided")

    # Evaluate resume
    try:
        if basic_evaluation:
            logger.info(f"Performing basic evaluation with model: {model}")
            result = await resume_evaluator_service.evaluate_resume_basic(
                resume_text=resume_text,
                job_posting=job_posting,
                model=model
            )
        else:
            logger.info(f"Performing full evaluation with model: {model}")
            result = await resume_evaluator_service.evaluate_resume(
                resume_text=resume_text,
                job_posting=job_posting,
                model=model
            )

        # Add file metadata
        result["metadata"]["filename"] = resume_file.filename
        result["metadata"]["content_type"] = resume_file.content_type
        result["metadata"]["parsedText"] = resume_text  # Include the parsed text in the response

        # Store evaluation in Firestore if job_id is provided
        if job_id:
            try:
                logger.info(f"Storing resume evaluation for role {job_id}")

                # Prepare evaluation data - include the full result
                evaluation_data = {
                    "scorecard": result.get("scorecard", {}),
                    "recommendation": result.get("recommendation", {}),
                    "feedback": result.get("feedback", {}),
                    "evaluation_summary": result.get("recommendation", {}).get("reasoning", ""),
                    "overall_score": result.get("recommendation", {}).get("overallScore", 0),
                    "full_evaluation": result  # Include the complete evaluation response
                }

                # Generate a unique application ID if not provided
                application_id = str(uuid.uuid4())

                # Create an application document in Firestore
                try:
                    # Extract candidate information from the request headers or form data
                    # This information is stored in localStorage on the frontend
                    candidate_email = None
                    candidate_name = None

                    # Try to get candidate information from the request headers
                    # These are custom headers that could be added by the frontend
                    # First try to get from the request headers directly
                    headers = request.headers
                    logger.info(f"All request headers: {headers}")

                    # Check for headers in both lowercase and capitalized formats
                    for email_header in ['x-candidate-email', 'X-Candidate-Email']:
                        if email_header in headers:
                            candidate_email = headers.get(email_header)
                            logger.info(f"Found candidate email in headers: {candidate_email}")
                            break

                    for name_header in ['x-candidate-name', 'X-Candidate-Name']:
                        if name_header in headers:
                            candidate_name = headers.get(name_header)
                            logger.info(f"Found candidate name in headers: {candidate_name}")
                            break

                    # If not found in request headers, try the file headers
                    if not candidate_email or not candidate_name:
                        file_headers = resume_file.headers
                        if not candidate_email:
                            for email_header in ['x-candidate-email', 'X-Candidate-Email']:
                                if email_header in file_headers:
                                    candidate_email = file_headers.get(email_header)
                                    logger.info(f"Found candidate email in file headers: {candidate_email}")
                                    break

                        if not candidate_name:
                            for name_header in ['x-candidate-name', 'X-Candidate-Name']:
                                if name_header in file_headers:
                                    candidate_name = file_headers.get(name_header)
                                    logger.info(f"Found candidate name in file headers: {candidate_name}")
                                    break

                    # If not found in headers, try to extract from the filename
                    # Some applications include email in the filename
                    if not candidate_email and resume_file.filename:
                        filename = resume_file.filename
                        logger.info(f"Checking filename for email: {filename}")
                        # Simple check for email pattern in filename
                        if '@' in filename and '.' in filename.split('@')[1]:
                            parts = filename.split('@')
                            domain_parts = parts[1].split('.')
                            if len(domain_parts) >= 2:
                                potential_email = f"{parts[0]}@{parts[1]}"
                                if '@' in potential_email and '.' in potential_email.split('@')[1]:
                                    candidate_email = potential_email
                                    logger.info(f"Extracted email from filename: {candidate_email}")

                    # If still not found, try to extract from form data
                    if not candidate_email or not candidate_name:
                        try:
                            form_data = await request.form()
                            logger.info(f"Form data keys: {form_data.keys()}")

                            # Check for candidate email in form data
                            if 'candidate_email' in form_data and not candidate_email:
                                candidate_email = form_data.get('candidate_email')
                                logger.info(f"Found candidate email in form data: {candidate_email}")

                            # Check for candidate name in form data
                            if 'candidate_name' in form_data and not candidate_name:
                                candidate_name = form_data.get('candidate_name')
                                logger.info(f"Found candidate name in form data: {candidate_name}")
                        except Exception as form_error:
                            logger.warning(f"Error extracting from form data: {str(form_error)}")

                    # Prepare application data
                    application_data = {
                        "id": application_id,
                        "roleId": job_id,
                        "status": "pending",
                        "created_at": SERVER_TIMESTAMP,
                        "updated_at": SERVER_TIMESTAMP,
                        "is_public": True,
                        "resume_text": resume_text,
                        "evaluation_summary": evaluation_data.get("evaluation_summary", ""),
                        "overall_score": evaluation_data.get("overall_score", 0)
                    }

                    # Add candidate information if available
                    if candidate_email:
                        application_data["email"] = candidate_email
                        application_data["candidateId"] = candidate_email
                    if candidate_name:
                        application_data["fullName"] = candidate_name

                    # Create candidate document if email is available
                    if candidate_email:
                        logger.info(f"Attempting to create/update candidate document with email: {candidate_email}")
                        try:
                            # Use the evaluation service to create the candidate
                            from app.services.evaluation_service import EvaluationService
                            evaluation_service = EvaluationService()

                            # Create the candidate document
                            candidate_id = await evaluation_service.create_candidate(
                                email=candidate_email,
                                name=candidate_name,
                                additional_data={
                                    "last_application_id": application_id,
                                    "last_role_id": job_id
                                }
                            )

                            if candidate_id:
                                logger.info(f"Successfully created/updated candidate with ID: {candidate_id}")

                                # Also save application as subcollection under candidate
                                candidate_app_ref = db.collection("candidates").document(candidate_email).collection("applications").document(application_id)
                                candidate_app_ref.set(application_data)
                                logger.info(f"Application document created in candidate subcollection with ID: {application_id}")
                            else:
                                logger.warning(f"Failed to create candidate document for email: {candidate_email}")
                        except Exception as candidate_error:
                            logger.error(f"Failed to create/update candidate document: {str(candidate_error)}")
                            logger.error(f"Error details: {type(candidate_error).__name__}")
                    else:
                        logger.warning("No candidate email available, skipping candidate document creation")

                    # Save application to applications collection (single source of truth)
                    try:
                        # Save to applications collection
                        applications_ref = db.collection("applications").document(application_id)
                        applications_ref.set(application_data)  # Remove await - Firestore client is not async
                        logger.info(f"Application document created in applications collection with ID: {application_id}")
                    except Exception as app_error:
                        logger.warning(f"Failed to create application in applications collection: {str(app_error)}")

                except Exception as app_creation_error:
                    logger.warning(f"Failed to create application document: {str(app_creation_error)}")
                    # Continue without creating application - don't fail the request

                # Store evaluation in Firestore
                evaluation_id = await firebase_service.create_resume_evaluation(
                    role_id=job_id,
                    evaluation_data=evaluation_data,
                    resume_text=resume_text,
                    user_id=user_id,
                    application_id=application_id,  # Link evaluation to the application
                    metadata=result.get("metadata", {})
                )

                # Also save the evaluation directly to the application document
                try:
                    # Save evaluation to applications collection
                    app_eval_ref = db.collection("applications").document(application_id).collection("evaluations").document(evaluation_id)
                    app_eval_ref.set({
                        "id": evaluation_id,
                        "role_id": job_id,
                        "evaluation_data": evaluation_data,
                        "created_at": SERVER_TIMESTAMP,
                        "updated_at": SERVER_TIMESTAMP
                    })
                    logger.info(f"Evaluation saved to application document in applications collection")
                except Exception as app_eval_error:
                    logger.warning(f"Failed to save evaluation to application document: {str(app_eval_error)}")

                # Add IDs to result
                result["evaluation_id"] = evaluation_id
                result["application_id"] = application_id
                logger.info(f"Resume evaluation stored with ID: {evaluation_id} linked to application {application_id}")
            except Exception as e:
                logger.warning(f"Failed to store resume evaluation: {str(e)}")
                # Continue without storing - don't fail the request

        logger.info(f"Resume evaluation completed successfully")
        return result
    except Exception as e:
        error_msg = f"Resume evaluation failed: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/evaluate", response_model=Union[EvaluationResponse, BasicEvaluationResponse])
@handle_evaluation_errors
async def evaluate_resume_text(
    request: ResumeEvaluationRequest,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
):
    """
    Evaluate a resume text against a job posting

    - Provide resume text and job posting text directly
    - Returns a comprehensive evaluation
    """
    # Authenticate user if credentials provided
    user_id = None
    if credentials:
        try:
            token = credentials.credentials
            user = await auth_service.verify_token(token)
            user_id = user.get('id')
            logger.info(f"Authenticated user: {user_id}")
        except Exception as e:
            logger.warning(f"Authentication failed: {str(e)}")
            # Continue without authentication

    # Evaluate resume
    try:
        if request.basic_evaluation:
            result = await resume_evaluator_service.evaluate_resume_basic(
                resume_text=request.resume_text,
                job_posting=request.job_posting,
                model=request.model
            )
        else:
            result = await resume_evaluator_service.evaluate_resume(
                resume_text=request.resume_text,
                job_posting=request.job_posting,
                model=request.model
            )

        # Include the original resume text in the metadata
        result["metadata"]["parsedText"] = request.resume_text

        # Note: We don't store this evaluation since we don't have a role_id
        # This endpoint is primarily for testing/development purposes

        logger.info(f"Resume evaluation completed successfully")
        return result
    except Exception as e:
        logger.error(f"Resume evaluation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Resume evaluation failed: {str(e)}")

@router.get("/health")
async def health_check():
    """Simple health check endpoint for the resume evaluation API"""
    return {"status": "ok", "service": "resume-evaluation"}