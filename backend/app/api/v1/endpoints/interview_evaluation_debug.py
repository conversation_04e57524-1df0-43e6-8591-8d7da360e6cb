# interview_evaluation_debug.py - Diagnostic endpoints for interview evaluation
from fastapi import APIRouter, HTTPException, status, Path, Query, Depends
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

from app.services.firebase_service import FirebaseService
from app.services.interview_evaluation_service import InterviewEvaluationService
from ..auth import get_current_user

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Initialize services
firebase_service = FirebaseService()

@router.get("/debug/find-session/{session_id}")
async def debug_find_session(
    session_id: str = Path(..., description="ID of the session to find"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Find a session by ID and return diagnostic information.
    This endpoint is for debugging purposes only.
    
    Authentication is required to prevent abuse.
    """
    try:
        logger.info(f"Debug request to find session {session_id}")
        
        # First check public_interview_sessions collection
        public_session_ref = firebase_service.db.collection("public_interview_sessions").document(session_id)
        public_session = public_session_ref.get()
        
        if public_session.exists:
            session_data = public_session.to_dict()
            session_data["id"] = session_id
            session_data["location"] = "public_interview_sessions"
            
            # Check if this session has an evaluation
            if "evaluation_id" in session_data:
                evaluation_id = session_data["evaluation_id"]
                eval_ref = firebase_service.db.collection("evaluations").document(evaluation_id)
                eval_doc = eval_ref.get()
                
                if eval_doc.exists:
                    session_data["has_evaluation"] = True
                    session_data["evaluation_exists"] = True
                    session_data["evaluation_data"] = {
                        "id": evaluation_id,
                        "created_at": eval_doc.to_dict().get("created_at")
                    }
                else:
                    session_data["has_evaluation"] = True
                    session_data["evaluation_exists"] = False
            else:
                session_data["has_evaluation"] = False
            
            return {
                "status": "success",
                "session_found": True,
                "session_data": session_data
            }
        
        # Next check if we can find it in interview_sessions
        # This would require searching across all users and roles
        results = []
        
        users_ref = firebase_service.db.collection("users")
        users = list(users_ref.stream())
        
        for user_doc in users:
            user_id = user_doc.id
            roles_ref = firebase_service.db.collection("users").document(user_id).collection("roles")
            roles = list(roles_ref.stream())
            
            for role_doc in roles:
                role_id = role_doc.id
                # Check interviews collection
                interviews_ref = firebase_service.db.collection("users").document(user_id).collection("roles").document(role_id).collection("interviews")
                
                # Check exact ID match
                interview_ref = interviews_ref.document(session_id)
                interview_doc = interview_ref.get()
                
                if interview_doc.exists:
                    interview_data = interview_doc.to_dict()
                    interview_data["id"] = session_id
                    interview_data["location"] = f"users/{user_id}/roles/{role_id}/interviews"
                    interview_data["user_id"] = user_id
                    interview_data["role_id"] = role_id
                    results.append(interview_data)
                
                # Check if it matches session_id field
                try:
                    session_query = interviews_ref.where("session_id", "==", session_id).limit(1)
                    session_matches = list(session_query.stream())
                    
                    for match in session_matches:
                        match_data = match.to_dict()
                        match_data["id"] = match.id
                        match_data["location"] = f"users/{user_id}/roles/{role_id}/interviews"
                        match_data["match_type"] = "session_id"
                        match_data["user_id"] = user_id
                        match_data["role_id"] = role_id
                        results.append(match_data)
                except Exception as e:
                    logger.warning(f"Error querying by session_id: {str(e)}")
                
                # Check if it matches transcript_id field
                try:
                    transcript_query = interviews_ref.where("transcript_id", "==", session_id).limit(1)
                    transcript_matches = list(transcript_query.stream())
                    
                    for match in transcript_matches:
                        match_data = match.to_dict()
                        match_data["id"] = match.id
                        match_data["location"] = f"users/{user_id}/roles/{role_id}/interviews"
                        match_data["match_type"] = "transcript_id"
                        match_data["user_id"] = user_id
                        match_data["role_id"] = role_id
                        results.append(match_data)
                except Exception as e:
                    logger.warning(f"Error querying by transcript_id: {str(e)}")
        
        if results:
            return {
                "status": "success",
                "session_found": True,
                "match_count": len(results),
                "matches": results
            }
        
        return {
            "status": "success",
            "session_found": False,
            "message": "Session not found in any collection"
        }
    
    except Exception as e:
        logger.exception(f"Error in debug_find_session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error: {str(e)}"
        )

@router.get("/debug/find-evaluations/{role_id}")
async def debug_find_evaluations(
    role_id: str = Path(..., description="ID of the role to find evaluations for"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Find all evaluations for a specific role.
    This endpoint is for debugging purposes only.
    
    Authentication is required to prevent abuse.
    """
    try:
        logger.info(f"Debug request to find evaluations for role {role_id}")
        user_id = current_user.get("id")
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User ID not found in authentication data"
            )
        
        # First check in the user's collection
        evaluations = []
        
        # Get evaluations from user's role collection
        user_evals_ref = firebase_service.db.collection("users").document(user_id).collection("roles").document(role_id).collection("evaluations")
        user_evals = list(user_evals_ref.stream())
        
        for eval_doc in user_evals:
            eval_data = eval_doc.to_dict()
            eval_data["id"] = eval_doc.id
            eval_data["location"] = f"users/{user_id}/roles/{role_id}/evaluations"
            evaluations.append(eval_data)
        
        # Get evaluations from interviews subcollection
        interviews_ref = firebase_service.db.collection("users").document(user_id).collection("roles").document(role_id).collection("interviews")
        interviews = list(interviews_ref.stream())
        
        for interview_doc in interviews:
            interview_id = interview_doc.id
            interview_evals_ref = interviews_ref.document(interview_id).collection("evaluations")
            interview_evals = list(interview_evals_ref.stream())
            
            for eval_doc in interview_evals:
                eval_data = eval_doc.to_dict()
                eval_data["id"] = eval_doc.id
                eval_data["location"] = f"users/{user_id}/roles/{role_id}/interviews/{interview_id}/evaluations"
                eval_data["interview_id"] = interview_id
                evaluations.append(eval_data)
        
        # Also check in public evaluations
        public_evals_ref = firebase_service.db.collection("evaluations").where("role_id", "==", role_id)
        public_evals = list(public_evals_ref.stream())
        
        for eval_doc in public_evals:
            eval_data = eval_doc.to_dict()
            eval_data["id"] = eval_doc.id
            eval_data["location"] = "evaluations"
            evaluations.append(eval_data)
        
        # Check in public_evaluations collection
        public_evals_ref2 = firebase_service.db.collection("public_evaluations").where("role_id", "==", role_id)
        public_evals2 = list(public_evals_ref2.stream())
        
        for eval_doc in public_evals2:
            eval_data = eval_doc.to_dict()
            eval_data["id"] = eval_doc.id
            eval_data["location"] = "public_evaluations"
            evaluations.append(eval_data)
        
        return {
            "status": "success",
            "evaluation_count": len(evaluations),
            "evaluations": evaluations
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error in debug_find_evaluations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error: {str(e)}"
        )

@router.post("/debug/trigger-evaluation/{session_id}")
async def debug_trigger_evaluation(
    session_id: str = Path(..., description="ID of the session to evaluate"),
    role_id: str = Query(..., description="ID of the role"),
    application_id: Optional[str] = Query(None, description="Optional application ID"),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Manually trigger an evaluation for a session.
    This endpoint is for debugging purposes only.
    
    Authentication is required to prevent abuse.
    """
    try:
        logger.info(f"Debug request to trigger evaluation for session {session_id}, role {role_id}")
        user_id = current_user.get("id")
        
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User ID not found in authentication data"
            )
        
        # Call the evaluation service with the explicit params
        result = await InterviewEvaluationService.auto_evaluate_public_interview(
            session_id,
            explicit_role_id=role_id,
            explicit_application_id=application_id
        )
        
        return {
            "status": "success",
            "message": "Evaluation triggered",
            "result": result
        }
    
    except Exception as e:
        logger.exception(f"Error in debug_trigger_evaluation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error: {str(e)}"
        )