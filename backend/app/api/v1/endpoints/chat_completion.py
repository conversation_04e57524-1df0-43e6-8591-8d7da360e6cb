# File: backend/app/api/v1/endpoints/chat_completion.py

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Body
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
import logging
from functools import wraps

from app.services.openai.chat_completion_service import ChatCompletionService
from app.services.auth_service import AuthService

router = APIRouter(
    prefix="/chat-completion",
    tags=["chat-completion"]
)

auth_service = AuthService()
security = HTTPBearer()

# Models for request validation
class PromptTemplate(BaseModel):
    """Model for prompt template configuration"""
    template_name: str = Field(..., description="Name of the prompt template to use")
    template_variables: Dict[str, Any] = Field(default={}, description="Variables to inject into the template")

class FunctionDefinition(BaseModel):
    """Model for function definition"""
    name: str
    description: str
    parameters: Dict[str, Any]

class ChatCompletionRequest(BaseModel):
    """Model for chat completion request"""
    model_configuration: Dict[str, Any] = Field(..., description="Model configuration including name and parameters")
    prompt_template: PromptTemplate = Field(..., description="Prompt template configuration")
    functions: Optional[List[FunctionDefinition]] = Field(default=None, description="Optional function definitions")
    use_case: str = Field(..., description="Use case identifier (e.g., email_agent, job_posting)")
    context: Dict[str, Any] = Field(default={}, description="Additional context for the completion")

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Get the current user from the token"""
    try:
        # Verify the token
        token = credentials.credentials
        user = await auth_service.verify_token(token)
        return user
    except Exception as e:
        logging.error(f"Authentication error: {str(e)}")
        raise HTTPException(
            status_code=401,
            detail=f"Authentication error: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

def handle_errors(func):
    """Decorator to handle common errors in chat completion endpoints"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except ValueError as e:
            logging.error(f"Value error in {func.__name__}: {str(e)}")
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            logging.error(f"Unexpected error in {func.__name__}: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")
    return wrapper

@router.post("/generate")
@handle_errors
async def generate_completion(
    request: ChatCompletionRequest = Body(...),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Generate a completion using the specified model, prompt template, and functions.
    
    This endpoint provides a standardized interface for all chat completion needs,
    supporting different use cases with appropriate prompt templates and model configurations.
    """
    try:
        # Load the prompt template
        from app.services.openai.prompt_loader import PromptLoader
        prompt_loader = PromptLoader()
        
        # Get the prompt template with variables injected
        prompt = prompt_loader.load_prompt(
            request.prompt_template.template_name,
            request.prompt_template.template_variables
        )
        
        # Extract model configuration
        model = request.model_configuration.get("name", "gpt-4o")
        model_params = request.model_configuration.get("parameters", {})
        
        # Generate the completion
        response = await ChatCompletionService.generate_completion(
            prompt=prompt,
            functions=[func.dict() for func in request.functions] if request.functions else None,
            model=model,
            use_case=request.use_case,
            **model_params
        )
        
        return {
            "success": True,
            "response": response,
            "model": model,
            "use_case": request.use_case
        }
        
    except Exception as e:
        logging.error(f"Error generating completion: {str(e)}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        } 