from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from app.services.firebase_service import FirebaseService
from app.services.application_service import ApplicationService
from app.api.v1.auth import get_current_user
from app.utils.data_transformers import normalize_interview_data
from datetime import datetime, timedelta, timezone
import logging
from app.core.log_utils import log_data_summary

# Configure logging
logger = logging.getLogger(__name__)

# Setup router
router = APIRouter()

# Initialize services
firebase_service = FirebaseService()
application_service = ApplicationService()

@router.get("/data")
async def get_dashboard_data(
    current_user: Dict[str, Any] = Depends(get_current_user),
    limit_roles: int = Query(10, description="Limit the number of roles returned"),
    limit_applications: int = Query(20, description="Limit the number of applications returned"),
    limit_evaluations: int = Query(20, description="Limit the number of evaluations returned")
) -> Dict[str, Any]:
    """
    Get all data needed for the dashboard in a single request.
    This includes:
    - Recent roles
    - Applications
    - Evaluations

    Parameters:
    - limit_roles: Limit the number of roles returned (default: 10)
    - limit_applications: Limit the number of applications returned (default: 20)
    - limit_evaluations: Limit the number of evaluations returned (default: 20)
    """
    try:
        user_id = current_user["id"]
        logger.info(f"Fetching dashboard data for user {user_id} with limits: roles={limit_roles}, applications={limit_applications}, evaluations={limit_evaluations}")

        # Get roles (limited)
        roles = await firebase_service.get_roles(user_id, limit=limit_roles)
        logger.info(f"Retrieved {len(roles)} roles for dashboard")

        # Get applications with detailed information (including interviews and evaluations)
        applications = await application_service.get_applications_with_interviews(user_id, limit=limit_applications)
        logger.info(f"Retrieved {len(applications)} applications for dashboard")

        # Get evaluations (limited)
        evaluations = await firebase_service.get_evaluations_for_user(user_id, limit=limit_evaluations)
        logger.info(f"Retrieved {len(evaluations)} evaluations for dashboard")

        # Return all data in a single response
        return {
            "roles": roles,
            "applications": applications,
            "evaluations": evaluations
        }
    except Exception as e:
        logger.error(f"Error fetching dashboard data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching dashboard data: {str(e)}")

@router.get("/applications")
async def get_applications(
    current_user: Dict[str, Any] = Depends(get_current_user),
    limit: int = Query(20, description="Limit the number of applications returned")
) -> List[Dict[str, Any]]:
    """
    Get all applications for the current user.

    Parameters:
    - limit: Limit the number of applications returned (default: 20)
    """
    try:
        user_id = current_user["id"]
        logger.info(f"Fetching applications for user {user_id} with limit {limit}")

        # Get applications with detailed information (including interviews and evaluations)
        applications = await application_service.get_applications_with_interviews(user_id, limit=limit)

        return applications
    except Exception as e:
        logger.error(f"Error fetching applications: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching applications: {str(e)}")

@router.get("/applications/{application_id}")
async def get_application(
    application_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get a single application by ID with complete interview data.
    """
    try:
        user_id = current_user["id"]
        logger.info(f"Fetching application {application_id} for user {user_id}")

        # Get application with enhanced interview data
        application = await application_service.get_application_with_interviews(application_id)

        if not application:
            raise HTTPException(status_code=404, detail=f"Application {application_id} not found")

        return application
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching application {application_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching application: {str(e)}")

@router.get("/interviews")
async def get_interviews(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get all interview sessions for the current user.
    """
    try:
        user_id = current_user["id"]
        logger.info(f"Fetching interview sessions for user {user_id}")

        # Get all roles for this user
        roles = await firebase_service.get_roles(user_id)
        role_ids = [role["id"] for role in roles]

        # Get interview sessions for all roles
        interview_sessions = []
        for role_id in role_ids:
            try:
                # Get interview transcripts for this role
                role_transcripts = await firebase_service.get_interview_transcripts(user_id, role_id)

                # Add role transcripts to the list
                for transcript in role_transcripts:
                    # Normalize the transcript data
                    normalized_transcript = normalize_interview_data(transcript)

                    # Add role ID if not present
                    if 'roleId' not in normalized_transcript and 'role_id' not in normalized_transcript:
                        normalized_transcript['roleId'] = role_id

                    # Try to get role information
                    try:
                        role_data = await firebase_service.get_role(user_id, role_id)
                        if role_data:
                            # Add role name if not present
                            if 'roleName' not in normalized_transcript:
                                normalized_transcript['roleName'] = role_data.get('title', '')

                            # Add role object for additional information
                            normalized_transcript['role'] = {
                                'id': role_id,
                                'title': role_data.get('title', ''),
                                'company': role_data.get('company', '')
                            }
                    except Exception as e:
                        logger.warning(f"Error getting role data for role {role_id}: {str(e)}")

                    # Try to get candidate information if candidateId is present
                    candidate_id = normalized_transcript.get('candidateId') or normalized_transcript.get('candidate_id')
                    if candidate_id:
                        try:
                            candidate_data = await firebase_service.get_candidate(candidate_id)
                            if candidate_data:
                                # Add candidate name if not present
                                if 'candidateName' not in normalized_transcript:
                                    normalized_transcript['candidateName'] = candidate_data.get('fullName', '')

                                # Add candidate object for additional information
                                normalized_transcript['candidate'] = {
                                    'id': candidate_id,
                                    'name': candidate_data.get('fullName', ''),
                                    'email': candidate_data.get('email', '')
                                }
                        except Exception as e:
                            logger.warning(f"Error getting candidate data for candidate {candidate_id}: {str(e)}")

                    # Add interview stage information if available
                    if 'stage' not in normalized_transcript and 'interviewStage' not in normalized_transcript:
                        # Default to a generic stage name based on the interview type or template
                        template_id = normalized_transcript.get('templateId') or normalized_transcript.get('template_id')
                        if template_id:
                            try:
                                template_data = await firebase_service.get_template(template_id)
                                if template_data:
                                    normalized_transcript['stage'] = template_data.get('name', 'Technical Interview')
                                    normalized_transcript['template'] = {
                                        'id': template_id,
                                        'name': template_data.get('name', ''),
                                        'url': f"/templates/{template_id}"
                                    }
                            except Exception as e:
                                logger.warning(f"Error getting template data for template {template_id}: {str(e)}")
                                normalized_transcript['stage'] = 'Technical Interview'
                        else:
                            normalized_transcript['stage'] = 'Technical Interview'

                    interview_sessions.append(normalized_transcript)

                # Also get public interview sessions for this role
                try:
                    public_transcripts = await firebase_service.get_public_interview_sessions_for_role(role_id)
                    # Add public transcripts to the list
                    for transcript in public_transcripts:
                        # Normalize the transcript data
                        normalized_transcript = normalize_interview_data(transcript)

                        # Add role ID if not present
                        if 'roleId' not in normalized_transcript and 'role_id' not in normalized_transcript:
                            normalized_transcript['roleId'] = role_id

                        # Try to get role information
                        try:
                            role_data = await firebase_service.get_role(user_id, role_id)
                            if role_data:
                                # Add role name if not present
                                if 'roleName' not in normalized_transcript:
                                    normalized_transcript['roleName'] = role_data.get('title', '')

                                # Add role object for additional information
                                normalized_transcript['role'] = {
                                    'id': role_id,
                                    'title': role_data.get('title', ''),
                                    'company': role_data.get('company', '')
                                }
                        except Exception as e:
                            logger.warning(f"Error getting role data for role {role_id}: {str(e)}")

                        # Try to get candidate information if candidateId is present
                        candidate_id = normalized_transcript.get('candidateId') or normalized_transcript.get('candidate_id')
                        if candidate_id:
                            try:
                                candidate_data = await firebase_service.get_candidate(candidate_id)
                                if candidate_data:
                                    # Add candidate name if not present
                                    if 'candidateName' not in normalized_transcript:
                                        normalized_transcript['candidateName'] = candidate_data.get('fullName', '')

                                    # Add candidate object for additional information
                                    normalized_transcript['candidate'] = {
                                        'id': candidate_id,
                                        'name': candidate_data.get('fullName', ''),
                                        'email': candidate_data.get('email', '')
                                    }
                            except Exception as e:
                                logger.warning(f"Error getting candidate data for candidate {candidate_id}: {str(e)}")

                        # Add interview stage information if available
                        if 'stage' not in normalized_transcript and 'interviewStage' not in normalized_transcript:
                            # Default to a generic stage name based on the interview type or template
                            template_id = normalized_transcript.get('templateId') or normalized_transcript.get('template_id')
                            if template_id:
                                try:
                                    template_data = await firebase_service.get_template(template_id)
                                    if template_data:
                                        normalized_transcript['stage'] = template_data.get('name', 'Technical Interview')
                                        normalized_transcript['template'] = {
                                            'id': template_id,
                                            'name': template_data.get('name', ''),
                                            'url': f"/templates/{template_id}"
                                        }
                                except Exception as e:
                                    logger.warning(f"Error getting template data for template {template_id}: {str(e)}")
                                    normalized_transcript['stage'] = 'Technical Interview'
                            else:
                                normalized_transcript['stage'] = 'Technical Interview'

                        interview_sessions.append(normalized_transcript)
                except Exception as e:
                    logger.warning(f"Error getting public interview sessions for role {role_id}: {str(e)}")
            except Exception as e:
                logger.warning(f"Error getting interview transcripts for role {role_id}: {str(e)}")

        return {
            "status": "success",
            "data": interview_sessions
        }
    except Exception as e:
        logger.error(f"Error fetching interview sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching interview sessions: {str(e)}")

@router.get("/interviews/count")
async def get_interviews_count(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get the count of interviews for the current user.
    """
    try:
        user_id = current_user["id"]
        logger.info(f"Fetching interview count for user {user_id}")

        # Get all roles for this user
        roles = await firebase_service.get_roles(user_id)
        role_ids = [role["id"] for role in roles]

        # Count interviews for all roles
        total_count = 0
        this_week_count = 0

        # Get the start of the current week (use UTC for consistency)
        now = datetime.utcnow()
        start_of_week = now - timedelta(days=now.weekday())
        start_of_week = start_of_week.replace(hour=0, minute=0, second=0, microsecond=0)

        # Use a simpler approach - just count the number of interviews
        # without processing each one individually
        try:
            # Get a count of public interview sessions for all roles
            # This is much faster than fetching all the data
            public_count = await firebase_service.get_public_interview_sessions_count(role_ids)
            total_count += public_count.get("total", 0)
            this_week_count += public_count.get("this_week", 0)

            # We don't need to process each role separately anymore
            # as the Firebase service will handle that efficiently
        except Exception as e:
            logger.warning(f"Error getting public interview sessions count: {str(e)}")

        return {
            "status": "success",
            "total_count": total_count,
            "this_week_count": this_week_count
        }
    except Exception as e:
        logger.error(f"Error fetching interview count: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching interview count: {str(e)}")

@router.get("/interviews/{interview_id}")
async def get_interview(
    interview_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get a single interview session by ID.
    """
    try:
        user_id = current_user["id"]
        logger.info(f"Fetching interview session {interview_id} for user {user_id}")

        # Get all roles for this user
        roles = await firebase_service.get_roles(user_id)
        role_ids = [role["id"] for role in roles]

        # Try to find the interview in each role's transcripts
        for role_id in role_ids:
            try:
                # Get interview transcripts for this role
                role_transcripts = await firebase_service.get_interview_transcripts(user_id, role_id)

                # Look for the interview in the role transcripts
                for transcript in role_transcripts:
                    if transcript.get("id") == interview_id:
                        # Normalize the transcript data
                        normalized_transcript = normalize_interview_data(transcript)
                        # Add role ID if not present
                        if 'roleId' not in normalized_transcript and 'role_id' not in normalized_transcript:
                            normalized_transcript['roleId'] = role_id
                        return {
                            "status": "success",
                            "data": normalized_transcript
                        }

                # Also check public interview sessions for this role
                try:
                    public_transcripts = await firebase_service.get_public_interview_sessions_for_role(role_id)
                    # Look for the interview in the public transcripts
                    for transcript in public_transcripts:
                        if transcript.get("id") == interview_id:
                            # Normalize the transcript data
                            normalized_transcript = normalize_interview_data(transcript)
                            # Add role ID if not present
                            if 'roleId' not in normalized_transcript and 'role_id' not in normalized_transcript:
                                normalized_transcript['roleId'] = role_id
                            return {
                                "status": "success",
                                "data": normalized_transcript
                            }
                except Exception as e:
                    logger.warning(f"Error getting public interview sessions for role {role_id}: {str(e)}")
            except Exception as e:
                logger.warning(f"Error getting interview transcripts for role {role_id}: {str(e)}")

        # If we get here, the interview was not found
        raise HTTPException(status_code=404, detail=f"Interview session {interview_id} not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching interview session {interview_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching interview session: {str(e)}")

@router.get("/evaluations")
async def get_evaluations(
    current_user: Dict[str, Any] = Depends(get_current_user),
    role_id: Optional[str] = Query(None, description="Filter evaluations by role ID"),
    limit: int = Query(20, description="Limit the number of evaluations returned")
) -> List[Dict[str, Any]]:
    """
    Get evaluations for the current user, optionally filtered by role.

    Parameters:
    - role_id: Filter evaluations by role ID
    - limit: Limit the number of evaluations returned (default: 20)
    """
    try:
        user_id = current_user["id"]
        logger.info(f"Fetching evaluations for user {user_id}" +
                   (f" for role {role_id}" if role_id else "") +
                   f" with limit {limit}")

        # Get evaluations
        evaluations = await firebase_service.get_evaluations_for_user(user_id, role_id, limit=limit)

        return evaluations
    except Exception as e:
        logger.error(f"Error fetching evaluations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching evaluations: {str(e)}")
