# File: backend/app/api/v1/endpoints/roles.py

from typing import Dict, Any, List, Optional, Callable, TypeVar, ParamSpec, Union, Type
from fastapi import APIRouter, HTTPException, UploadFile, File, Depends, Request, Body, Query, BackgroundTasks, Path
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr, HttpUrl, validator, root_validator
from datetime import datetime
from enum import Enum
from firebase_admin import auth, storage
import firebase_admin.exceptions
import logging
from functools import wraps
from fastapi.responses import JSONResponse
from fastapi import status
from starlette.concurrency import run_in_threadpool
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.background import BackgroundTask as StarletteBackgroundTask
from starlette.requests import ClientDisconnect

from app.services.firebase_service import FirebaseService
from app.services.parser_service import parse_job_description
from app.services.auth_service import AuthService
from app.services.roles_service import RolesService
from app.utils.data_transformers import normalize_role_data, normalize_enum_values
import json

# Type variables for generic function signatures
T = TypeVar("T")
P = ParamSpec("P")

# Global variable to store results for error handling
result = None

def verify_role_access():
    """Decorator to verify role access before executing endpoint."""
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @wraps(func)
        async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            # Extract role_id and current_user from kwargs
            role_id = kwargs.get('role_id')
            current_user = kwargs.get('current_user')

            if not role_id or not current_user:
                raise HTTPException(status_code=400, detail="Missing required parameters")

            # Verify access
            role = await roles_service.get_role(role_id=role_id, user_id=current_user["id"])
            if not role:
                raise HTTPException(status_code=404, detail="Role not found")

            # Continue with the original function
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def handle_role_errors(func: Callable[P, T]) -> Callable[P, T]:
    """
    Decorator to handle common errors in role endpoints.
    """
    @wraps(func)
    async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
        try:
            return await func(*args, **kwargs)
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except ValueError as e:
            logging.error(f"Value error in {func.__name__}: {str(e)}")
            raise HTTPException(status_code=400, detail=str(e))
        except KeyError as e:
            logging.error(f"Key error in {func.__name__}: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Missing required field: {str(e)}")
        except firebase_admin.exceptions.FirebaseError as e:
            logging.error(f"Firebase error in {func.__name__}: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Authentication error: {str(e)}")
        except Exception as e:
            logging.error(f"Unexpected error in {func.__name__}: {str(e)}", exc_info=True)
            # For public endpoints, return a more generic error to avoid exposing internal details
            if func.__name__ in ['list_public_roles', 'get_public_role']:
                raise HTTPException(status_code=500, detail="An error occurred while processing your request")
            else:
                raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")
    return wrapper

# Main router for authenticated endpoints
router = APIRouter(
    prefix="/roles",
    tags=["roles"]
)

# Public router for unauthenticated endpoints - using a completely different prefix
public_router = APIRouter(
    prefix="/public/jobs",
    tags=["public-jobs"]
)

firebase = FirebaseService()
auth_service = AuthService()
roles_service = RolesService()
security = HTTPBearer()

# Get Firebase storage bucket
bucket = storage.bucket()

async def get_current_user(request: Request, credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Get the current authenticated user."""
    try:
        token = credentials.credentials
        logging.info(f"Verifying token: {token[:20]}...")

        try:
            # Get Firebase service instance
            firebase_service = FirebaseService()

            # Verify the Firebase token with clock tolerance
            logging.info("Attempting to verify Firebase token...")
            try:
                # First attempt with clock skew tolerance
                decoded_token = auth.verify_id_token(
                    token,
                    check_revoked=True,  # Check if the token has been revoked
                    app=firebase_service.app,  # Use the singleton app instance
                    clock_skew_seconds=30  # Allow 30 seconds of clock skew
                )
                logging.info(f"Token verified successfully with clock skew tolerance. User ID: {decoded_token.get('uid')}")
            except auth.InvalidIdTokenError as e:
                if "Token used too early" not in str(e):
                    raise  # Re-raise if it's not a clock skew issue
                logging.warning(f"Clock skew error: {str(e)}")
                # Try again with a larger tolerance if needed
                decoded_token = auth.verify_id_token(
                    token,
                    check_revoked=True,
                    app=firebase_service.app,
                    clock_skew_seconds=60  # Increase tolerance to 60 seconds as a fallback
                )
                logging.info(f"Token verified successfully with increased clock skew tolerance. User ID: {decoded_token.get('uid')}")

            uid = decoded_token.get('uid')
            if not uid:
                logging.error("No UID found in decoded token")
                raise HTTPException(status_code=401, detail="Invalid token")

            logging.info(f"Getting user data for UID: {uid}")
            # Get user from Firestore using FirebaseService
            user_ref = firebase_service.db.collection('users').document(uid)
            user_doc = user_ref.get()

            if not user_doc.exists:
                logging.error(f"No user document found for UID: {uid}")
                raise HTTPException(status_code=404, detail="User not found")

            user_data = user_doc.to_dict()
            logging.info(f"User data retrieved successfully for UID: {uid}")

            return {
                "id": uid,
                "email": user_data.get('email'),
                "role": user_data.get('role', 'user')
            }

        except auth.InvalidIdTokenError as e:
            logging.error(f"Invalid token error: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")
        except auth.ExpiredIdTokenError as e:
            logging.error(f"Token expired error: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Token has expired: {str(e)}")
        except auth.RevokedIdTokenError as e:
            logging.error(f"Token revoked error: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Token has been revoked: {str(e)}")
        except Exception as e:
            logging.error(f"Unexpected error during token verification: {str(e)}")
            raise HTTPException(status_code=401, detail=str(e))

    except Exception as e:
        logging.error(f"Error in get_current_user: {str(e)}")
        raise HTTPException(status_code=401, detail="Could not authenticate user")

# Helper function to normalize enum values
def normalize_enum_value(value: str, enum_class: Type[Enum]) -> str:
    """
    Normalize a string value to match an enum value, case-insensitive.
    If no match is found, return the original value.
    """
    if not value:
        # Return the first enum value as default if value is empty
        return list(enum_class)[0].value

    # Try exact match first
    for enum_item in enum_class:
        if value == enum_item.value:
            return value

    # Try case-insensitive match
    value_lower = value.lower()
    for enum_item in enum_class:
        if value_lower == enum_item.value.lower():
            return enum_item.value

    # Special handling for RemoteStatus
    if enum_class.__name__ == 'RemoteStatus':
        # Map common remote status terms
        remote_status_terms = {
            'remote': 'Remote',
            'hybrid': 'Hybrid',
            'on-site': 'On-site',
            'onsite': 'On-site',
            'on site': 'On-site',
            'in-office': 'On-site',
            'in office': 'On-site',
            'office': 'On-site',
            'not specified': 'Remote',  # Default to Remote for "Not Specified"
            'unspecified': 'Remote',
            'any': 'Remote',
            'flexible': 'Remote'
        }

        # Check for exact match in our mapping
        if value_lower in remote_status_terms:
            return remote_status_terms[value_lower]

        # Check for partial match
        for term, mapped_value in remote_status_terms.items():
            if term in value_lower:
                return mapped_value

        # Default to Remote if no match
        return 'Remote'

    # Special handling for JobType
    if enum_class.__name__ == 'JobType':
        # Map common job type terms
        job_type_terms = {
            'full-time': 'Full-time',
            'full time': 'Full-time',
            'fulltime': 'Full-time',
            'full-Time': 'Full-time',
            'full Time': 'Full-time',
            'full': 'Full-time',
            'ft': 'Full-time',
            'part-time': 'Part-time',
            'part time': 'Part-time',
            'parttime': 'Part-time',
            'part-Time': 'Part-time',
            'part Time': 'Part-time',
            'part': 'Part-time',
            'pt': 'Part-time',
            'contract': 'Contract',
            'contractor': 'Contract',
            'freelance': 'Contract',
            'temporary': 'Contract',
            'temp': 'Contract'
        }

        # Check for exact match in our mapping
        if value_lower in job_type_terms:
            return job_type_terms[value_lower]

        # Check for partial match
        for term, mapped_value in job_type_terms.items():
            if term in value_lower:
                return mapped_value

        # Default to Full-time if no match
        return 'Full-time'

    # For RolePriority, add special handling
    if enum_class.__name__ == 'RolePriority':
        # Map common priority terms
        priority_terms = {
            'high': 'Expedited',
            'urgent': 'Expedited',
            'critical': 'Expedited',
            'expedited': 'Expedited',
            'top': 'Expedited',
            'important': 'Expedited',
            'normal': 'Normal',
            'standard': 'Normal',
            'regular': 'Normal',
            'medium': 'Normal',
            'low': 'Normal'
        }

        # Check for exact match in our mapping
        if value_lower in priority_terms:
            return priority_terms[value_lower]

        # Check for partial match
        for term, mapped_value in priority_terms.items():
            if term in value_lower:
                return mapped_value

        # Default to Normal for priority
        return 'Normal'

    # If no match, return the original value
    return value

class RoleStatus(str, Enum):
    INTAKE = "Intake"
    SOURCING = "Sourcing"
    SCREENING = "Screening"
    DEEP_DIVE = "Deep_Dive"
    IN_PERSON = "In_Person"
    OFFER = "Offer"
    ACCEPTED = "Accepted"
    REJECTED = "Rejected"
    CLOSED = "Closed"

class RoleLevel(str, Enum):
    ENTRY_LEVEL = "Entry Level"
    JUNIOR = "Junior"
    MID_LEVEL = "Mid-Level"
    SENIOR = "Senior"
    LEAD = "Lead"
    PRINCIPAL = "Principal"
    DISTINGUISHED = "Distinguished"

class RolePriority(str, Enum):
    NORMAL = "Normal"
    EXPEDITED = "Expedited"

class JobType(str, Enum):
    CONTRACT = "Contract"
    FULL_TIME = "Full-time"
    PART_TIME = "Part-time"

class RemoteStatus(str, Enum):
    REMOTE = "Remote"
    HYBRID = "Hybrid"
    ON_SITE = "On-site"

class InterviewType(str, Enum):
    SCREENING = "Screening"
    CULTURAL_FIT = "Cultural & Company fit"
    BEHAVIORAL = "Behavioral"
    DOMAIN_EXPERT = "Domain Expert"
    TECHNICAL_CHALLENGE = "Technical Challenge with Code"
    HOME_ASSIGNMENT = "Home Assignment"
    EXPERIENCE_FIT = "Experience & Role fit"
    PROBLEM_SOLVING = "Advance Problem Solving"
    TEAM_ETHICS = "Team Ethics"

class Location(BaseModel):
    city: str = ""
    remoteStatus: RemoteStatus = RemoteStatus.ON_SITE

class Education(BaseModel):
    value: str = ""
    isRequired: bool = False

class Compensation(BaseModel):
    range: str = ""
    currency: str = "USD"
    equity: bool = False

class Benefits(BaseModel):
    healthInsurance: bool = False
    vacationDays: int = 0

class InterviewStage(BaseModel):
    stage: InterviewType
    duration: str
    customInstructions: str = ""

class RoleBase(BaseModel):
    title: str
    level: RoleLevel = RoleLevel.ENTRY_LEVEL
    status: RoleStatus = RoleStatus.INTAKE
    priority: RolePriority = RolePriority.NORMAL
    summary: str = ""
    keyResponsibilities: List[str] = []
    yearsOfExperience: str = ""
    requiredSkills: Dict[str, str] = {}
    preferredSkills: Dict[str, str] = {}
    education: Education = Education()
    certificates: List[str] = []
    team: str = ""
    keyStakeholders: List[str] = []
    location: Location = Location()
    jobType: JobType = JobType.FULL_TIME
    aboutCompany: str = ""
    aboutTeam: str = ""
    compensation: Compensation = Compensation()
    benefits: Benefits = Benefits()
    startDate: str = ""
    hiringManagerId: str = ""
    hiringManagerContact: str = ""
    interviewProcess: List[InterviewStage] = []
    jobPosting: str = ""  # Add jobPosting field to include it in the response
    isPublished: bool = False  # Add isPublished field to track publication status

    # Validators to normalize enum values
    @validator('jobType', pre=True)
    def normalize_job_type(cls, v):
        if isinstance(v, str):
            return normalize_enum_value(v, JobType)
        return v

    @validator('status', pre=True)
    def normalize_status(cls, v):
        if isinstance(v, str):
            return normalize_enum_value(v, RoleStatus)
        return v

    @validator('priority', pre=True)
    def normalize_priority(cls, v):
        if isinstance(v, str):
            return normalize_enum_value(v, RolePriority)
        return v

    @root_validator(pre=True)
    def normalize_location_remote_status(cls, values):
        location = values.get('location')
        if isinstance(location, dict) and 'remoteStatus' in location:
            if isinstance(location['remoteStatus'], str):
                location['remoteStatus'] = normalize_enum_value(location['remoteStatus'], RemoteStatus)
        return values

    class Config:
        extra = "ignore"  # Ignore extra fields
        validate_assignment = True
        arbitrary_types_allowed = True

class RoleCreate(RoleBase):
    pass

class RoleUpdate(BaseModel):
    """Model for role updates that allows partial updates."""
    title: Optional[str] = None
    level: Optional[RoleLevel] = None
    status: Optional[RoleStatus] = None
    priority: Optional[RolePriority] = None
    summary: Optional[str] = None
    keyResponsibilities: Optional[List[str]] = None
    yearsOfExperience: Optional[str] = None
    requiredSkills: Optional[Dict[str, str]] = None
    preferredSkills: Optional[Dict[str, str]] = None
    education: Optional[Education] = None
    certificates: Optional[List[str]] = None
    team: Optional[str] = None
    keyStakeholders: Optional[List[str]] = None
    location: Optional[Location] = None
    jobType: Optional[JobType] = None
    aboutCompany: Optional[str] = None
    aboutTeam: Optional[str] = None
    compensation: Optional[Compensation] = None
    benefits: Optional[Benefits] = None
    startDate: Optional[str] = None
    hiringManagerId: Optional[str] = None
    hiringManagerContact: Optional[str] = None
    interviewProcess: Optional[List[InterviewStage]] = None
    isPublished: Optional[bool] = None

    class Config:
        extra = "ignore"  # Ignore extra fields

class RoleStatusUpdate(BaseModel):
    status: RoleStatus

class RolePriorityUpdate(BaseModel):
    priority: RolePriority

class Role(RoleBase):
    id: str
    createdAt: datetime
    updatedAt: datetime

    class Config:
        from_attributes = True

class RoleCreateRequest(BaseModel):
    title: str
    level: Optional[str] = None
    yearsOfExperience: Optional[str] = None
    jobType: Optional[str] = None
    summary: Optional[str] = None
    team: Optional[str] = None
    aboutTeam: Optional[str] = None
    aboutCompany: Optional[str] = None
    requiredSkills: Optional[Dict[str, str]] = None
    preferredSkills: Optional[Dict[str, str]] = None
    keyResponsibilities: Optional[List[str]] = None
    keyStakeholders: Optional[List[str]] = None
    hiringManagerContact: Optional[Dict[str, str]] = None
    openEndedConsiderations: Optional[str] = None
    interviewProcess: Optional[List[Dict[str, str]]] = None
    location: Optional[Dict[str, str]] = None
    status: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class TranscriptUpdate(BaseModel):
    messages: List[Dict[str, Any]]
    status: Optional[str] = "in_progress"

@router.post("", response_model=Role)
@handle_role_errors
async def create_role(role: RoleCreate, current_user: Dict = Depends(get_current_user)) -> Dict[str, Any]:
    """Create a new role."""
    role_data = role.dict()
    role_data["user_id"] = current_user["id"]
    created_role = await roles_service.create_role(role_data)
    return created_role

@router.get("", response_model=List[Role])
@handle_role_errors
async def list_roles(current_user: Dict = Depends(get_current_user)) -> List[Dict[str, Any]]:
    """Get all roles."""
    try:
        roles_data = await roles_service.list_roles(user_id=current_user["id"])
        validated_roles = []

        for role_data in roles_data:
            try:
                # First normalize field names
                role_data = normalize_role_data(role_data)

                # Then normalize enum values
                role_data = normalize_enum_values(role_data)

                # Ensure required fields are present
                if 'title' not in role_data or not role_data['title']:
                    role_data['title'] = "Untitled Role"

                if 'status' not in role_data or not role_data['status']:
                    role_data['status'] = "Intake"

                if 'jobType' not in role_data or not role_data['jobType']:
                    role_data['jobType'] = "Full-time"

                if 'priority' not in role_data or not role_data['priority']:
                    role_data['priority'] = "Normal"

                if 'level' not in role_data or not role_data['level'] or role_data['level'] == 'Not Specified':
                    role_data['level'] = "Entry Level"

                if 'location' not in role_data or not isinstance(role_data['location'], dict):
                    role_data['location'] = {"city": "", "remoteStatus": "Remote"}
                elif 'remoteStatus' not in role_data['location'] or not role_data['location']['remoteStatus']:
                    role_data['location']['remoteStatus'] = "Remote"

                if 'interviewProcess' not in role_data or not isinstance(role_data['interviewProcess'], list):
                    role_data['interviewProcess'] = [
                        {
                            "stage": "Screening",
                            "duration": "30 minutes",
                            "customInstructions": ""
                        }
                    ]

                # Fix preferredSkills - ensure all values are strings
                if 'preferredSkills' in role_data and isinstance(role_data['preferredSkills'], dict):
                    for key, value in role_data['preferredSkills'].items():
                        if isinstance(value, list):
                            # Convert list to comma-separated string
                            role_data['preferredSkills'][key] = ", ".join(value)
                        elif not isinstance(value, str):
                            # Convert any non-string value to string
                            role_data['preferredSkills'][key] = str(value)

                # Fix requiredSkills - ensure all values are strings
                if 'requiredSkills' in role_data and isinstance(role_data['requiredSkills'], dict):
                    for key, value in role_data['requiredSkills'].items():
                        if isinstance(value, list):
                            # Convert list to comma-separated string
                            role_data['requiredSkills'][key] = ", ".join(value)
                        elif not isinstance(value, str):
                            # Convert any non-string value to string
                            role_data['requiredSkills'][key] = str(value)

                # Fix hiringManagerContact - ensure it's a string
                if 'hiringManagerContact' in role_data:
                    if isinstance(role_data['hiringManagerContact'], dict):
                        # Convert dict to string representation
                        contact_dict = role_data['hiringManagerContact']
                        if 'email' in contact_dict and contact_dict['email']:
                            role_data['hiringManagerContact'] = contact_dict['email']
                        elif 'name' in contact_dict and contact_dict['name']:
                            role_data['hiringManagerContact'] = contact_dict['name']
                        else:
                            role_data['hiringManagerContact'] = "Not provided"
                    elif not isinstance(role_data['hiringManagerContact'], str):
                        # Convert any non-string value to string
                        role_data['hiringManagerContact'] = "Not provided"
                else:
                    role_data['hiringManagerContact'] = "Not provided"

                # Ensure keyResponsibilities is a list
                if 'keyResponsibilities' not in role_data or role_data['keyResponsibilities'] is None:
                    role_data['keyResponsibilities'] = []
                elif not isinstance(role_data['keyResponsibilities'], list):
                    # Try to convert to list if possible
                    try:
                        if isinstance(role_data['keyResponsibilities'], str):
                            # If it's a string, try to split by commas
                            role_data['keyResponsibilities'] = [item.strip() for item in role_data['keyResponsibilities'].split(',') if item.strip()]
                        else:
                            # For other types, convert to string and make a single-item list
                            role_data['keyResponsibilities'] = [str(role_data['keyResponsibilities'])]
                    except Exception:
                        # If conversion fails, use empty list
                        role_data['keyResponsibilities'] = []
                        logging.warning(f"Could not convert keyResponsibilities to list for role {role_data.get('id', 'unknown')}, using empty list")

                # Ensure team is a string
                if 'team' not in role_data or role_data['team'] is None:
                    role_data['team'] = ""
                elif not isinstance(role_data['team'], str):
                    # Convert to string
                    try:
                        role_data['team'] = str(role_data['team'])
                    except Exception:
                        role_data['team'] = ""
                        logging.warning(f"Could not convert team to string for role {role_data.get('id', 'unknown')}, using empty string")

                validated_roles.append(role_data)
            except Exception as e:
                logging.error(f"Error validating role {role_data.get('id', 'unknown')}: {str(e)}", exc_info=True)
                # Skip invalid roles
                continue

        return validated_roles

    except Exception as e:
        logging.error(f"Error listing roles: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error listing roles: {str(e)}")

@router.get("/{role_id}", response_model=Role)
@verify_role_access()
@handle_role_errors
async def get_role(role_id: str, current_user: Dict = Depends(get_current_user)) -> Dict[str, Any]:
    """Get a specific role by ID."""
    role_data = await roles_service.get_role(role_id=role_id, user_id=current_user["id"])

    # Normalize the role data using our utility functions
    if role_data:
        try:
            # First normalize field names
            role_data = normalize_role_data(role_data)

            # Then normalize enum values
            role_data = normalize_enum_values(role_data)

            # Ensure level is a valid enum value
            if 'level' not in role_data or not role_data['level'] or role_data['level'] == 'Not Specified':
                role_data['level'] = "Entry Level"
                logging.debug(f"Defaulted level to 'Entry Level' for role {role_id}")

            return role_data
        except Exception as e:
            logging.error(f"Error normalizing role data: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Error normalizing role data: {str(e)}")

    # If we get here, the role was not found
    logging.error(f"Role not found for ID: {role_id}")
    raise HTTPException(status_code=404, detail="Role not found")

@router.put("/{role_id}", response_model=Role)
@verify_role_access()
@handle_role_errors
async def update_role(
    *,  # Force keyword arguments
    role_id: str,
    role: RoleUpdate = Body(...),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Update a role."""
    # Convert Pydantic model to dict, excluding None values
    updates = {k: v for k, v in role.model_dump().items() if v is not None}

    # Normalize the updates
    updates = normalize_role_data(updates)
    updates = normalize_enum_values(updates)

    # Ensure level is a valid enum value if it's being updated
    if 'level' in updates and (not updates['level'] or updates['level'] == 'Not Specified'):
        updates['level'] = "Entry Level"
        logging.debug(f"Defaulted level to 'Entry Level' for role update {role_id}")

    # Update the role
    updated_role = await roles_service.update_role(
        role_id=role_id,
        updates=updates,
        user_id=current_user["id"]
    )

    return updated_role

@router.delete("/{role_id}")
@verify_role_access()
@handle_role_errors
async def delete_role(role_id: str, current_user: Dict = Depends(get_current_user)) -> Dict[str, str]:
    """Delete a specific role."""
    await roles_service.delete_role(role_id=role_id, user_id=current_user["id"])
    return {
        "status": "success",
        "message": "Role deleted successfully"
    }

@router.patch("/{role_id}/status")
@verify_role_access()
@handle_role_errors
async def update_role_status(role_id: str, status_update: RoleStatusUpdate, current_user: Dict = Depends(get_current_user)) -> Dict[str, Any]:
    """Update a role's status."""
    await roles_service.update_role(
        role_id=role_id,
        updates={"status": status_update.status},
        user_id=current_user["id"]
    )
    return {
        "status": "success",
        "message": "Role status updated successfully"
    }

@router.patch("/{role_id}/priority")
@verify_role_access()
@handle_role_errors
async def update_role_priority(role_id: str, priority_update: RolePriorityUpdate, current_user: Dict = Depends(get_current_user)) -> Dict[str, Any]:
    """Update a role's priority."""
    await roles_service.update_role(
        role_id=role_id,
        updates={"priority": priority_update.priority},
        user_id=current_user["id"]
    )
    return {
        "status": "success",
        "message": "Role priority updated successfully"
    }

@router.post("/{role_id}/intake-recording")
@verify_role_access()
@handle_role_errors
async def upload_intake_recording(role_id: str, recording: UploadFile = File(...), current_user: Dict = Depends(get_current_user)) -> Dict[str, Any]:
    """Upload an intake recording for a role."""
    # Generate a unique blob path
    timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    blob_path = f"roles/{current_user['id']}/{role_id}/intake_recordings/{timestamp}_{recording.filename}"

    # Create a new blob and upload the file
    blob = bucket.blob(blob_path)

    # Read the file content
    content = await recording.read()

    # Upload the content
    blob.upload_from_string(
        content,
        content_type=recording.content_type
    )

    # Generate a signed URL that expires in 1 hour (3600 seconds)
    url = blob.generate_signed_url(
        version="v4",
        expiration=3600,
        method="GET"
    )

    return {
        "message": "Recording uploaded successfully",
        "url": url,
        "path": blob_path
    }

@router.post("/parse-job-description")
@handle_role_errors
async def parse_jd(jd_file: UploadFile = File(...)) -> Dict[str, Any]:
    """Parse a job description file."""
    return await parse_job_description(jd_file)

@router.post("/migrate-to-team-structure")
@handle_role_errors
async def migrate_roles_to_team_structure(current_user: Dict = Depends(get_current_user)) -> Dict[str, str]:
    """Migrate existing roles to use team_name instead of department fields."""
    await firebase.migrate_roles_to_team_structure()
    return {
        "status": "success",
        "message": "Successfully migrated roles to team structure"
    }

@router.get("/{role_id}/intake-transcripts")
@verify_role_access()
@handle_role_errors
async def get_intake_transcripts(role_id: str, current_user: Dict = Depends(get_current_user)) -> List[Dict[str, Any]]:
    """Get all intake transcripts for a role."""
    return await firebase.get_intake_transcripts(user_id=current_user["id"], role_id=role_id)

@router.get("/{role_id}/interview-transcripts")
@verify_role_access()
@handle_role_errors
async def get_interview_transcripts(role_id: str, current_user: Dict = Depends(get_current_user)) -> List[Dict[str, Any]]:
    """Get all interview transcripts for a role."""
    # Get transcripts from Firebase
    transcripts = await firebase.get_interview_transcripts(
        user_id=current_user["id"],
        role_id=role_id
    )

    # Also check for public interview sessions for this role
    try:
        public_transcripts = await firebase.get_public_interview_sessions_for_role(role_id)
        # Merge and return the results
        all_transcripts = transcripts + public_transcripts
        return all_transcripts
    except Exception as e:
        # If the method doesn't exist or there's an error, just return the regular transcripts
        logging.error(f"Error fetching public interview sessions: {str(e)}")
        return transcripts

@router.post("/{role_id}/intake-transcripts")
@verify_role_access()
@handle_role_errors
async def create_intake_transcript(
    role_id: str,
    transcript_data: dict,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Create a new intake transcript."""
    transcript_id = transcript_data.get('id')
    if not transcript_id:
        raise HTTPException(status_code=400, detail="Transcript ID is required")

    await firebase.create_intake_transcript(
        user_id=current_user["id"],
        role_id=role_id,
        transcript_id=transcript_id,
        transcript_data=transcript_data
    )

    return {"id": transcript_id}

@router.post("/{role_id}/interview-transcripts")
@verify_role_access()
@handle_role_errors
async def create_interview_transcript(
    role_id: str,
    transcript_data: dict,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Create a new interview transcript."""
    transcript_id = transcript_data.get('id')
    if not transcript_id:
        raise HTTPException(status_code=400, detail="Transcript ID is required")

    # Initialize agent loader and services
    agent_loader = AgentLoader(agent_type="interview_agent")
    agent_services = agent_loader.get_services()

    # Create the transcript using the interview agent service
    await agent_services.create_interview_transcript(
        user_id=current_user["id"],
        role_id=role_id,
        transcript_id=transcript_id,
        transcript_data=transcript_data
    )

    return {"id": transcript_id}

@router.put("/{role_id}/intake-transcripts/{transcript_id}")
@verify_role_access()
@handle_role_errors
async def update_intake_transcript(
    role_id: str,
    transcript_id: str,
    transcript_data: dict,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Update an intake transcript."""
    # Check if this is a completion request
    if transcript_data.get('completed'):
        try:
            await firebase.complete_intake_transcript(
                user_id=current_user["id"],
                role_id=role_id,
                transcript_id=transcript_id
            )
            logging.info(f"Successfully completed intake transcript {transcript_id}")
            return {"status": "completed"}
        except Exception as e:
            logging.error(f"Error completing intake transcript: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to complete transcript: {str(e)}"
            )

    # Check if this is a deletion request (soft delete)
    if transcript_data.get('deleted'):
        # TODO: Implement soft delete
        return {"status": "deleted"}

    # Extract messages from the transcript data
    messages = transcript_data.get('messages', [])

    # Log the message count for debugging
    logging.info(f"Updating intake transcript {transcript_id} for role {role_id} with {len(messages)} messages")

    # Make sure messages is a list
    if not isinstance(messages, list):
        logging.error(f"Expected messages to be a list, got: {type(messages)}")
        messages = []

    # Update the transcript messages with better error handling
    try:
        # Use a background task to update the transcript
        # This ensures the operation completes even if the client disconnects
        result = await firebase.update_intake_transcript(
            user_id=current_user["id"],
            role_id=role_id,
            transcript_id=transcript_id,
            messages=messages
        )
        logging.info(f"Successfully updated intake transcript {transcript_id}")
        return {"status": "updated"}
    except Exception as e:
        logging.error(f"Error updating intake transcript: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update transcript: {str(e)}"
        )

@router.put("/{role_id}/interview-transcripts/{transcript_id}")
@verify_role_access()
@handle_role_errors
async def update_interview_transcript(
    role_id: str,
    transcript_id: str,
    transcript_data: dict,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Update an interview transcript."""
    # Initialize agent loader and services
    agent_loader = AgentLoader(agent_type="interview_agent")
    agent_services = agent_loader.get_services()

    # Check if this is a completion request
    if transcript_data.get('completed'):
        try:
            await agent_services.complete_transcript(
                user_id=current_user["id"],
                role_id=role_id,
                transcript_id=transcript_id
            )
            logging.info(f"Successfully completed interview transcript {transcript_id}")
            return {"status": "completed"}
        except Exception as e:
            logging.error(f"Error completing interview transcript: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to complete transcript: {str(e)}"
            )

    # Check if this is a deletion request (soft delete)
    if transcript_data.get('deleted'):
        # TODO: Implement soft delete for interview transcripts
        return {"status": "deleted"}

    # Extract content from transcript data
    messages = transcript_data.get('messages', [])

    # Log the update for debugging
    logging.info(f"Updating interview transcript {transcript_id} with {len(messages)} messages")

    # Make sure messages is a list
    if not isinstance(messages, list):
        logging.error(f"Expected messages to be a list, got: {type(messages)}")
        messages = []

    # Prepare the update data
    update_data = {
        "messages": messages,
        "messageCount": len(messages),
        "status": transcript_data.get('status', 'in_progress')
    }

    # Otherwise, update the transcript
    try:
        await agent_services.update_interview_transcript(
            user_id=current_user["id"],
            role_id=role_id,
            transcript_id=transcript_id,
            transcript_data=update_data
        )
        logging.info(f"Successfully updated interview transcript {transcript_id}")
        return {"status": "updated"}
    except Exception as e:
        logging.error(f"Error updating interview transcript: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update transcript: {str(e)}"
        )

@router.post("/{role_id}/generate-job-posting")
@verify_role_access()
@handle_role_errors
async def generate_job_posting(
    role_id: str,
    request: Request,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Generate a job posting from a transcript using OpenAI.

    Args:
        role_id: The ID of the role
        request: The request object containing the transcript_id
        current_user: The current authenticated user

    Returns:
        A dictionary containing the generated job posting
    """
    try:
        from app.services.ai_service import AIService

        # Parse the request body
        body = await request.json()
        transcript_id = body.get("transcript_id")

        logging.info(f"Generating job posting for role {role_id} with transcript {transcript_id}")

        if not transcript_id:
            raise HTTPException(status_code=400, detail="transcript_id is required")

        # Get the role and transcript
        role = await roles_service.get_role(role_id=role_id, user_id=current_user["id"])
        if not role:
            raise HTTPException(status_code=404, detail="Role not found")

        # Ensure role_id is included in the role object
        role["id"] = role_id

        # Get the transcript using firebase service directly
        transcripts = await firebase.get_intake_transcripts(user_id=current_user["id"], role_id=role_id)
        transcript = next((t for t in transcripts if t.get("id") == transcript_id), None)

        if not transcript:
            raise HTTPException(status_code=404, detail="Transcript not found")

        # Log the transcript structure
        logging.info(f"Transcript structure: {json.dumps({k: type(v).__name__ for k, v in transcript.items()})}")

        # Check if the transcript has a messages field
        if "messages" in transcript:
            # Extract content from messages
            messages = transcript.get("messages", [])
            content = "\n".join([msg.get("content", "") for msg in messages if msg.get("role") == "user" or msg.get("role") == "assistant"])
            transcript["content"] = content
            logging.info(f"Created content field from {len(messages)} messages")
        else:
            # If no messages field, create an empty content field
            transcript["content"] = "No transcript content available."
            logging.warning("No messages field found in transcript, using default content")

        # Generate the job posting
        try:
            ai_service = AIService()
            logging.info("AIService initialized successfully")

            result = await ai_service.generate_job_posting(role, transcript)
            logging.info("Job posting generated successfully")

            if not result or "job_posting" not in result:
                logging.error(f"Invalid result from generate_job_posting: {result}")
                raise HTTPException(status_code=500, detail="Failed to generate job posting")

            # Update only the jobPosting field without affecting other fields
            # Use firebase service directly to avoid normalization that might reset interviewProcess
            await firebase.update_role(
                user_id=current_user["id"],
                role_id=role_id,
                updates={"jobPosting": result["job_posting"]}
            )
            logging.info("Role updated with job posting")

            # Save the job posting to the subcollection
            job_posting_data = {
                "content": result["job_posting"],
                "transcriptId": transcript_id,
                "generatedAt": datetime.utcnow()
            }

            await firebase.save_job_posting(
                user_id=current_user["id"],
                role_id=role_id,
                job_posting_data=job_posting_data
            )
            logging.info("Job posting saved to subcollection")

            return {
                "status": "success",
                "job_posting": result["job_posting"]
            }
        except Exception as e:
            logging.exception(f"Error in AI service or saving job posting: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error generating job posting: {str(e)}")
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logging.exception(f"Unexpected error in generate_job_posting: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

@router.post("/{role_id}/enrich-role")
@verify_role_access()
@handle_role_errors
async def enrich_role(
    role_id: str,
    request: Request,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Enrich a role with data extracted from a transcript using OpenAI.

    Args:
        role_id: The ID of the role
        request: The request object containing the transcript_id
        current_user: The current authenticated user

    Returns:
        A dictionary containing the enriched role data
    """
    try:
        from app.services.ai_service import AIService

        # Parse the request body
        body = await request.json()
        transcript_id = body.get("transcript_id")

        logging.info(f"Enriching role {role_id} with transcript {transcript_id}")

        if not transcript_id:
            raise HTTPException(status_code=400, detail="transcript_id is required")

        # Get the role and transcript
        role = await roles_service.get_role(role_id=role_id, user_id=current_user["id"])
        if not role:
            raise HTTPException(status_code=404, detail="Role not found")

        # Get the transcript using firebase service directly
        transcripts = await firebase.get_intake_transcripts(user_id=current_user["id"], role_id=role_id)
        transcript = next((t for t in transcripts if t.get("id") == transcript_id), None)

        if not transcript:
            raise HTTPException(status_code=404, detail="Transcript not found")

        # Log the transcript structure
        logging.info(f"Transcript structure: {json.dumps({k: type(v).__name__ for k, v in transcript.items()})}")

        # Check if the transcript has a messages field
        if "messages" in transcript:
            # Extract content from messages
            messages = transcript.get("messages", [])
            content = "\n".join([msg.get("content", "") for msg in messages if msg.get("role") == "user" or msg.get("role") == "assistant"])

            # Ensure content is not empty
            if not content.strip():
                logging.warning("Extracted content is empty, using default content")
                content = "No transcript content available."

            transcript["content"] = content
            logging.info(f"Created content field from {len(messages)} messages with length {len(content)} characters")
        else:
            # If no messages field, create an empty content field
            transcript["content"] = "No transcript content available."
            logging.warning("No messages field found in transcript, using default content")

        # Enrich the role with transcript data
        try:
            ai_service = AIService()
            logging.info("AIService initialized successfully")

            enriched_data = await ai_service.enrich_role(role, transcript)
            logging.info("Role enrichment completed")

            # Check if enrichment returned an error status
            if isinstance(enriched_data, dict) and enriched_data.get("status") == "error":
                error_message = enriched_data.get("message", "Unknown error")
                logging.error(f"Error in role enrichment: {error_message}")
                raise HTTPException(status_code=500, detail=f"Role enrichment failed: {error_message}")

            # Check if we got a valid result
            if not enriched_data or not isinstance(enriched_data, dict):
                logging.error(f"Invalid result from enrich_role: {enriched_data}")
                raise HTTPException(status_code=500, detail="Failed to enrich role data: Invalid response format")

            # Validate that we have meaningful enriched data
            # Ensure we have at least some key fields populated
            required_fields = ["title", "summary", "requiredSkills"]
            missing_fields = [field for field in required_fields if field not in enriched_data or not enriched_data.get(field)]

            if missing_fields:
                logging.error(f"Enriched data missing required fields: {missing_fields}")
                raise HTTPException(status_code=500, detail=f"Enrichment failed: Missing required fields {missing_fields}")

            # Log the enriched data fields
            logging.info(f"Enriched data fields: {list(enriched_data.keys())}")

            # Update the role with the enriched data
            # Use firebase service directly to avoid normalization that might reset interviewProcess
            await firebase.update_role(
                user_id=current_user["id"],
                role_id=role_id,
                updates=enriched_data
            )
            logging.info("Role updated with enriched data")

            return {
                "status": "success",
                "message": "Role successfully enriched with transcript data",
                "enriched_data": enriched_data
            }
        except Exception as e:
            logging.exception(f"Error in AI service or updating role: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error enriching role: {str(e)}")
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logging.exception(f"Unexpected error in enrich_role: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

@router.get("/{role_id}/job-posting")
@verify_role_access()
@handle_role_errors
async def get_job_posting(
    role_id: str,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get the job posting for a role.

    Args:
        role_id: The ID of the role
        current_user: The current authenticated user

    Returns:
        A dictionary containing the job posting if it exists
    """
    # Verify role exists
    role = await roles_service.get_role(role_id=role_id, user_id=current_user["id"])
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")

    # Get the job posting from the subcollection
    job_posting = await firebase.get_job_posting(user_id=current_user["id"], role_id=role_id)

    if not job_posting:
        # Check if the role has a jobPosting field (for backward compatibility)
        if "jobPosting" in role and role["jobPosting"]:
            # Create a job posting document from the existing field
            job_posting_data = {
                "content": role["jobPosting"],
                "generatedAt": datetime.utcnow()
            }

            # Save it to the subcollection
            await firebase.save_job_posting(
                user_id=current_user["id"],
                role_id=role_id,
                job_posting_data=job_posting_data
            )

            return {
                "status": "success",
                "job_posting": role["jobPosting"]
            }

        # No job posting found
        return {
            "status": "not_found",
            "message": "No job posting found for this role"
        }

    return {
        "status": "success",
        "job_posting": job_posting["content"],
        "generated_at": job_posting.get("generatedAt", None)
    }

# Public endpoints for job board
@public_router.get("", response_model=List[Role])
@handle_role_errors
async def list_public_roles() -> List[Dict[str, Any]]:
    """
    List all public roles that have isPublished=True.
    This endpoint is accessible without authentication.
    """
    try:
        logging.info("Fetching all public roles")

        # Get all roles from the service - the service now handles filtering and caching
        public_roles = await roles_service.get_all_roles()

        # Additional logging to debug the issue
        logging.info(f"Retrieved {len(public_roles)} roles from service")

        # Count roles with isPublished flag
        published_count = sum(1 for role in public_roles if role.get('isPublished') == True)

        logging.info(f"Pre-filtering statistics: {published_count} roles have isPublished=True")

        # Only filter based on isPublished flag
        filtered_roles = [
            role for role in public_roles
            if role.get('isPublished') == True
        ]

        logging.info(f"Returning {len(filtered_roles)} public roles after filtering (isPublished=True)")

        # Log the first few roles for debugging
        if filtered_roles:
            for i, role in enumerate(filtered_roles[:3]):
                logging.info(f"Sample role {i+1}: ID={role.get('id')}, Title={role.get('title')}, Status={role.get('status')}, isPublished={role.get('isPublished')}")

        return filtered_roles
    except Exception as e:
        logging.error(f"Error listing public roles: {str(e)}", exc_info=True)
        # Return an empty list instead of raising an exception to avoid connection errors
        return []

@public_router.get("/{role_id}", response_model=Role)
@handle_role_errors
async def get_public_role(role_id: str) -> Dict[str, Any]:
    """
    Get a public role by ID.
    This endpoint is accessible without authentication.
    """
    try:
        logging.info(f"Fetching public role with ID: {role_id}")

        # Get the role from the service
        role = await roles_service.get_role_by_id(role_id)

        if not role:
            logging.warning(f"Role not found with ID: {role_id}")
            raise HTTPException(status_code=404, detail=f"Role not found with ID: {role_id}")

        # Only check if the role is published
        if not role.get('isPublished'):
            logging.warning(f"Role with ID {role_id} is not published")
            raise HTTPException(status_code=404, detail=f"Role not found with ID: {role_id}")

        logging.info(f"Successfully fetched public role with ID: {role_id}")
        return role
    except Exception as e:
        logging.error(f"Error getting public role: {str(e)}", exc_info=True)
        raise

@public_router.get("/{role_id}/job-posting")
@handle_role_errors
async def get_public_job_posting(role_id: str) -> Dict[str, Any]:
    """
    Get a public job posting by role ID.
    This endpoint is accessible without authentication.
    """
    try:
        logging.info(f"Fetching public job posting for role with ID: {role_id}")

        # Get the role from the service
        role = await roles_service.get_role_by_id(role_id)

        if not role:
            logging.warning(f"Role not found with ID: {role_id}")
            raise HTTPException(status_code=404, detail=f"Role not found with ID: {role_id}")

        # Only check if the role is published
        if not role.get('isPublished'):
            logging.warning(f"Role with ID {role_id} is not published")
            raise HTTPException(status_code=404, detail=f"Role not found with ID: {role_id}")

        # Check if the role has a job posting
        if not role.get('jobPosting'):
            # Generate a fallback job posting
            role['id'] = role_id  # Ensure role_id is included for the How to Apply link
            job_posting = roles_service._generate_fallback_job_posting(role)

            logging.info(f"Generated fallback job posting for role {role_id}")

            return {
                "status": "success",
                "job_posting": job_posting
            }

        logging.info(f"Successfully fetched public job posting for role with ID: {role_id}")

        return {
            "status": "success",
            "job_posting": role.get('jobPosting')
        }
    except Exception as e:
        logging.error(f"Error getting public job posting: {str(e)}", exc_info=True)
        raise