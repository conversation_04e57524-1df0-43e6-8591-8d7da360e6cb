from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Body
import logging
from firebase_admin import firestore

from app.api.v1.auth import get_current_user
from app.services.firebase_service import FirebaseService
from app.services.evaluation_service import EvaluationService

# Configure logging
logger = logging.getLogger(__name__)

# Initialize services
firebase_service = FirebaseService()
evaluation_service = EvaluationService()

# Setup router
router = APIRouter()

@router.post("/link-evaluation-to-interview")
async def link_evaluation_to_interview(
    data: Dict[str, Any] = Body(...),
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Link an evaluation to an interview.

    This endpoint:
    1. Checks if the application has evaluation data
    2. Creates a new evaluation record if one doesn't exist
    3. Updates the interview record with the evaluation ID

    Required fields in request body:
    - application_id: The ID of the application
    - interview_id: The ID of the interview to link
    """
    try:
        user_id = current_user["id"]
        application_id = data.get("application_id")
        interview_id = data.get("interview_id")

        if not application_id or not interview_id:
            raise HTTPException(status_code=400, detail="application_id and interview_id are required")

        logger.info(f"Linking evaluation to interview {interview_id} for application {application_id}")

        # Get the application data
        application = await firebase_service.get_application(application_id)
        if not application:
            raise HTTPException(status_code=404, detail=f"Application {application_id} not found")

        # Get the role ID from the application
        role_id = application.get("roleId") or application.get("role_id")
        if not role_id:
            raise HTTPException(status_code=400, detail=f"Application {application_id} has no role ID")

        # Check if the application has evaluation data
        evaluation_data = application.get("evaluationData") or application.get("evaluation_data")

        # Try to get the interview data
        interview_ref = firebase_service.db.collection("public_interview_sessions").document(interview_id)
        interview_doc = interview_ref.get()

        # If the interview doesn't exist in public_interview_sessions, we'll create a placeholder
        if not interview_doc.exists:
            logger.info(f"Interview {interview_id} not found in public_interview_sessions, creating placeholder")
            interview_data = {
                "id": interview_id,
                "role_id": role_id,
                "application_id": application_id,
                "status": "completed",
                "created_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
            }

            # Create the interview document
            interview_ref.set(interview_data)
            logger.info(f"Created placeholder interview document for {interview_id}")
        else:
            interview_data = interview_doc.to_dict()

        # Check if the interview already has an evaluation ID
        existing_evaluation_id = interview_data.get("evaluationId") or interview_data.get("evaluation_id")

        if existing_evaluation_id:
            logger.info(f"Interview {interview_id} already has evaluation ID: {existing_evaluation_id}")
            return {
                "status": "success",
                "message": f"Interview already linked to evaluation {existing_evaluation_id}",
                "evaluation_id": existing_evaluation_id
            }

        # If we have application data with evaluation, create a direct evaluation
        if evaluation_data:
            logger.info(f"Creating evaluation directly from application data for interview {interview_id}")

        # Create a direct evaluation from application data
        try:
            # Extract the evaluation data
            if isinstance(evaluation_data, dict) and "full_evaluation" in evaluation_data:
                # Use the full evaluation data if available
                eval_content = evaluation_data["full_evaluation"]
            else:
                # Otherwise use the evaluation data as is
                eval_content = evaluation_data

            # Create the evaluation
            evaluation_id = await evaluation_service.create_evaluation(
                role_id=role_id,
                interview_id=interview_id,
                evaluation_data=eval_content,
                application_id=application_id,
                user_id=user_id,
                metadata={
                    "created_from": "application_data",
                    "created_by": "evaluation_fix_endpoint"
                }
            )

            logger.info(f"Created new evaluation {evaluation_id} for interview {interview_id}")
        except Exception as e:
            logger.error(f"Error creating evaluation: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error creating evaluation: {str(e)}"
            )

        # Update the interview with the evaluation ID
        interview_ref.update({
            "evaluationId": evaluation_id,
            "evaluation_id": evaluation_id,  # Add both camelCase and snake_case for compatibility
            "updated_at": firestore.SERVER_TIMESTAMP
        })

        logger.info(f"Updated interview {interview_id} with evaluation ID {evaluation_id}")

        return {
            "status": "success",
            "message": f"Evaluation {evaluation_id} linked to interview {interview_id}",
            "evaluation_id": evaluation_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error linking evaluation to interview: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error linking evaluation to interview: {str(e)}")


@router.post("/direct-link-evaluation")
async def direct_link_evaluation(
    data: Dict[str, Any] = Body(...),
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Directly link a specific evaluation ID to an interview.

    Required fields in request body:
    - interview_id: The ID of the interview to link
    - evaluation_id: The ID of the evaluation to link
    """
    try:
        interview_id = data.get("interview_id")
        evaluation_id = data.get("evaluation_id")

        if not interview_id:
            raise HTTPException(status_code=400, detail="interview_id is required")

        if not evaluation_id:
            raise HTTPException(status_code=400, detail="evaluation_id is required")

        logger.info(f"Directly linking evaluation {evaluation_id} to interview {interview_id}")

        # Update the interview document in public_interview_sessions
        try:
            interview_ref = firebase_service.db.collection("public_interview_sessions").document(interview_id)
            interview_doc = interview_ref.get()

            if interview_doc.exists:
                interview_ref.update({
                    "evaluationId": evaluation_id,
                    "evaluation_id": evaluation_id,  # Add both camelCase and snake_case for compatibility
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                logger.info(f"Updated public interview {interview_id} with evaluation ID {evaluation_id}")
            else:
                # Create a placeholder interview document
                interview_data = {
                    "id": interview_id,
                    "evaluationId": evaluation_id,
                    "evaluation_id": evaluation_id,
                    "status": "completed",
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }
                interview_ref.set(interview_data)
                logger.info(f"Created placeholder interview document for {interview_id} with evaluation ID {evaluation_id}")
        except Exception as e:
            logger.error(f"Error updating public interview: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Error updating public interview: {str(e)}")

        return {
            "status": "success",
            "message": f"Evaluation {evaluation_id} directly linked to interview {interview_id}",
            "evaluation_id": evaluation_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error directly linking evaluation: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error directly linking evaluation: {str(e)}")
