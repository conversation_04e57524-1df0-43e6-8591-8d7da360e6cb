# File: backend/app/api/v1/endpoints/realtime.py

from fastapi import APIRouter, HTTPException, status, Body, Depends, Path, BackgroundTasks
from pydantic import BaseModel, Field
import httpx
from app.core.config import settings
import logging
import traceback
from typing import Dict, Any, Optional, List, Union
import json
import yaml
import os
from app.services.ai_service import AIService
from app.services.roles_service import RolesService
from app.services.firebase_service import FirebaseService
from app.utils.data_transformers import normalize_role_data
from ..auth import get_current_user
from datetime import datetime, timedelta
from app.services.openai.prompts.manager import PromptManager
from app.services.openai.function_registry import FunctionRegistry
from app.services.realtime.agent_loader import AgentLoader
from app.services.openai.config.model_configs import ModelConfigurationManager
import uuid
from ..models import SessionResponse, PublicInterviewSessionRequest
from app.utils.openai_helpers import generate_ephemeral_key
from fastapi.responses import JSONResponse
import asyncio

# Set up logging
logger = logging.getLogger(__name__)

router = APIRouter()

class SessionResponse(BaseModel):
    session_id: str
    client_secret: dict
    transcript_id: Optional[str] = None
    role_id: Optional[str] = None
    template_id: Optional[str] = None
    template_name: Optional[str] = None
    stage: Optional[str] = None
    stage_index: Optional[int] = None
    candidate_id: Optional[str] = None
    application_id: Optional[str] = None
    is_public: bool = False

class EndCallRequest(BaseModel):
    role_id: str
    transcript_id: str
    user_id: str
    is_enrichment: bool = False
    role_data: Optional[Dict[str, Any]] = None
    agent_type: str = "intake_agent"  # Default to intake_agent for backward compatibility
    process_method: str = "enrichment_agent"  # Options: "enrichment_agent", "direct_data"

class PublicInterviewSessionRequest(BaseModel):
    role_id: str
    template_id: Optional[str] = None
    candidate_id: Optional[str] = None
    application_id: Optional[str] = None
    resume_text: Optional[str] = None
    job_posting: Optional[str] = None

# Add a new model for handling public interview transcript updates
class PublicTranscriptUpdate(BaseModel):
    session_id: str = Field(..., description="The session ID")
    transcript_id: str = Field(..., description="The transcript ID")
    role_id: str = Field(..., description="The role ID")
    messages: List[Dict[str, Any]] = Field(..., description="The conversation messages")
    application_id: Optional[str] = Field(None, description="Optional application ID")

# Add a debug endpoint to check if roles exist
@router.get("/debug-role/{role_id}")
async def debug_role(
    role_id: str = Path(...),
    roles_service: RolesService = Depends(lambda: RolesService())
):
    """
    Debug endpoint to check if a role exists and get details about it.
    This is a temporary endpoint for debugging purposes.
    """
    try:
        # Try to get the role directly from Firestore
        role_ref = roles_service.db.collection("roles").document(role_id)
        role_doc = role_ref.get()

        if not role_doc.exists:
            return {
                "exists": False,
                "message": f"Role with ID {role_id} does not exist in the database"
            }

        # Get the role data
        role_data = role_doc.to_dict()

        # Try to get public templates
        templates = await roles_service.get_public_templates_for_role(role_id)

        # Check if the role has the is_public flag
        is_public = role_data.get("is_public", False)

        # Return debug information
        return {
            "exists": True,
            "is_public": is_public,
            "has_public_templates": len(templates) > 0,
            "public_template_count": len(templates),
            "public_templates": templates,
            "role_data_preview": {k: v for k, v in role_data.items() if k in ["title", "status", "id", "is_public"]}
        }
    except Exception as e:
        logger.error(f"Error in debug-role endpoint: {str(e)}")
        return {
            "error": str(e),
            "trace": traceback.format_exc()
        }

@router.get("/debug-public-role/{role_id}")
async def debug_public_role(
    role_id: str = Path(...),
    roles_service: RolesService = Depends(lambda: RolesService())
):
    """
    Diagnostic endpoint to check if a role is accessible for public interviews.
    This is used for debugging and should be removed in production.
    """
    try:
        logger.info(f"DEBUG: Checking public access for role {role_id}")

        # Try direct Firestore access
        role_ref = roles_service.db.collection("roles").document(role_id)
        role_doc = role_ref.get()

        exists = role_doc.exists

        if exists:
            role_data = role_doc.to_dict()

            # Try to list templates
            templates_ref = roles_service.db.collection("roles").document(role_id).collection("templates")
            templates_docs = templates_ref.stream()

            templates = []
            for doc in templates_docs:
                template_data = doc.to_dict()
                templates.append({
                    "id": doc.id,
                    "stage": template_data.get("stage", "Unknown"),
                    "title": template_data.get("title", "Untitled")
                })

            return {
                "exists": exists,
                "role_id": role_id,
                "data_available": bool(role_data),
                "data_sample": {
                    "title": role_data.get("title", "N/A"),
                    "is_public": role_data.get("is_public", False),
                    "status": role_data.get("status", "N/A")
                },
                "templates": templates,
                "templates_count": len(templates)
            }
        else:
            return {
                "exists": False,
                "role_id": role_id,
                "message": "Role document not found in Firestore"
            }
    except Exception as e:
        logger.error(f"Error in debug-public-role: {str(e)}", exc_info=True)
        return {
            "error": str(e),
            "role_id": role_id,
            "exists": "unknown due to error"
        }

@router.post("/session")
async def create_default_session(
    role_id: str = None,
    template_id: str = None,
    is_enrichment: bool = False
):
    """Create a new realtime session with the default intake agent (for backward compatibility)"""
    return await create_session(agent_type="intake_agent", role_id=role_id, template_id=template_id, is_enrichment=is_enrichment)

@router.post("/session/{agent_type}")
async def create_session(
    agent_type: str = Path(),
    role_id: str = None,
    template_id: str = None,
    is_enrichment: bool = False
):
    """Create a new realtime session with OpenAI"""
    try:
        # Record start time for performance tracking
        start_time = datetime.now()

        # Default to intake_agent if not specified (though this shouldn't happen with path params)
        if not agent_type:
            agent_type = "intake_agent"

        # Initialize the agent loader with the specified agent type
        agent_loader = AgentLoader(agent_type=agent_type)
        logger.info(f"Initializing realtime session with agent type: {agent_type}")

        try:
            # Dynamically load the prompt and functions based on agent type
            # Use the agent_type as the prompt name (the loader will append .prompt extension)
            prompt = agent_loader.get_prompt(agent_type)
            functions_module = agent_loader.get_functions()
            functions = functions_module.realtime_functions

            logger.info(f"Successfully loaded prompt for {agent_type} with {len(functions)} tools")
        except FileNotFoundError as e:
            logger.error(f"Error loading agent components: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Agent prompt not found: {agent_type}. Please ensure the prompt file exists."
            )
        except Exception as e:
            logger.error(f"Error loading agent components: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error loading agent components: {str(e)}"
            )

        # Get the appropriate model for the agent type
        model = ModelConfigurationManager.get_appropriate_model(agent_type)
        logger.info(f"Selected model for {agent_type}: {model}")

        # Ensure the model is a realtime model
        if not ModelConfigurationManager.is_realtime_model(model):
            logger.warning(f"Model {model} is not a realtime model, falling back to default realtime model")
            model = "gpt-4o-mini-realtime-preview-2024-12-17"

        # Get model-specific configuration for realtime
        model_config = ModelConfigurationManager.get_model_config(model, agent_type)
        logger.info(f"Using model config for {model}: {model_config}")

        # Create the prompt context
        context = {}

        # If this is an enrichment call for a role, fetch the role data and add it to the context
        if is_enrichment and role_id:
            logger.info(f"Enrichment call detected for role_id: {role_id}, fetching role data")
            try:
                # Initialize roles service
                roles_service = RolesService()

                # Get the role data - Note: We don't have user_id here since this is an open endpoint,
                # so we'll use the role_id directly via the roles_service's special method
                role_data = await roles_service.get_role_by_id(role_id)

                if role_data:
                    # Format role data for the prompt context - updated format
                    # Process responsibilities
                    resp_items = role_data.get("keyResponsibilities", [])
                    resp_text = ""
                    if resp_items:
                        resp_text = "- " + "\n- ".join(resp_items)

                    # Process required skills
                    req_skills = role_data.get("requiredSkills", {})
                    req_skills_text = ""
                    if req_skills:
                        req_skills_text = "- " + "\n- ".join([f"{skill}: {level}" for skill, level in req_skills.items()])

                    # Process preferred skills
                    pref_skills = role_data.get("preferredSkills", {})
                    pref_skills_text = ""
                    if pref_skills:
                        pref_skills_text = "- " + "\n- ".join([f"{skill}: {level}" for skill, level in pref_skills.items()])

                    # Build the section part by part
                    existing_role_data_section = "### EXISTING ROLE INFORMATION\n"
                    existing_role_data_section += "You already have some information about this role. This is an enrichment call to gather additional details:\n"
                    existing_role_data_section += f"- Title: {role_data.get('title', '')}\n"
                    existing_role_data_section += f"- Team: {role_data.get('team', '')}\n"
                    existing_role_data_section += f"- Summary: {role_data.get('summary', '')}\n"
                    existing_role_data_section += f"- Job Type: {role_data.get('jobType', '')}\n"
                    existing_role_data_section += f"- Years of Experience: {role_data.get('yearsOfExperience', '')}\n\n"

                    existing_role_data_section += "Key Responsibilities: \n"
                    existing_role_data_section += resp_text + "\n\n"

                    existing_role_data_section += "Required Skills: \n"
                    existing_role_data_section += req_skills_text + "\n\n"

                    existing_role_data_section += "Preferred Skills: \n"
                    existing_role_data_section += pref_skills_text + "\n\n"

                    existing_role_data_section += "About the Team: \n"
                    existing_role_data_section += role_data.get("aboutTeam", "") + "\n\n"

                    existing_role_data_section += "In this enrichment call, focus on filling gaps and gathering more comprehensive information about the role. Ask targeted questions to expand on existing information."

                    # Add the formatted section to the context
                    context["existing_role_data_section"] = existing_role_data_section
                    context["enrichment_intro"] = ", mentioning this is an enrichment call to gather more detailed information"

                    logger.info(f"Added role data to context for enrichment call: {list(context.keys())}")
                else:
                    logger.warning(f"Role data not found for enrichment call with role_id: {role_id}")
                    context["existing_role_data_section"] = ""
                    context["enrichment_intro"] = ""
            except Exception as e:
                logger.error(f"Error fetching role data for enrichment call: {str(e)}")
                logger.error(traceback.format_exc())
                context["existing_role_data_section"] = ""
                context["enrichment_intro"] = ""
        else:
            context["existing_role_data_section"] = ""
            context["enrichment_intro"] = ""

        # Format the prompt with the context if needed
        formatted_prompt = prompt.format(**context) if context else prompt

        # Prepare the request payload with model-specific configuration
        payload = {
            "model": model,
            "voice": "shimmer",
            "instructions": formatted_prompt,
            "tools": functions,
            "tool_choice": "auto",
        }

        # Add model-specific parameters, excluding unsupported ones
        unsupported_params = ["top_p", "frequency_penalty", "presence_penalty", "max_tokens"]
        for key, value in model_config.items():
            if key not in payload and key not in ["timeout"] and key not in unsupported_params:
                payload[key] = value

        logger.info(f"Realtime session payload keys: {list(payload.keys())}")
        logger.info(f"Creating session with role_id: {role_id}, template_id: {template_id}, is_enrichment: {is_enrichment}")

        api_key = settings.OPENAI_API_KEY

        async with httpx.AsyncClient() as client:
            # Send the request to the sessions endpoint
            response = await client.post(
                'https://api.openai.com/v1/realtime/sessions',
                headers={
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json'
                },
                json=payload,
                timeout=model_config.get("timeout", 30.0)
            )

            if response.status_code != 200:
                error_detail = response.json() if response.text else "No error details available"
                logger.error(f"OpenAI API Error: {error_detail}")

                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail=f"Failed to create OpenAI session: {error_detail}"
                )

            # Extract session data from response
            session_data = response.json()

            # Generate a transcript ID
            transcript_id = str(uuid.uuid4())

            # Create session response
            session_response = SessionResponse(
                session_id=session_data.get("id"),
                client_secret={
                    "value": session_data.get("client_secret", {}).get("value"),
                    "expires_at": session_data.get("client_secret", {}).get("expires_at", 0)
                },
                transcript_id=transcript_id,
                role_id=role_id,
                template_id=template_id
            )

            # Calculate and log the time taken
            time_taken = (datetime.now() - start_time).total_seconds()
            logger.info(f"Session created in {time_taken:.2f} seconds")

            return session_response

    except Exception as e:
        logger.error(f"Error creating realtime session: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error: {str(e)}"
        )

@router.post("/end-call", response_class=JSONResponse)
async def end_call(
    request: EndCallRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    End a call session and process the transcript based on the call type
    Different call types (intake, interview, etc.) have different ending processes
    """
    # Create a request ID for tracking
    request_id = str(uuid.uuid4())

    # Function to execute in the background after client disconnect
    async def process_in_background(
        request_data: EndCallRequest,
        user_data: Dict[str, Any],
        req_id: str
    ):
        # Process the transcript after the response has been sent
        try:
            logger.info(f"[{req_id}] Starting background processing for end_call")

            # Sleep briefly to ensure the response has been sent
            await asyncio.sleep(0.1)

            # Validate the role exists
            roles_service = RolesService()
            role = await roles_service.get_role(request_data.role_id, request_data.user_id)
            if not role:
                logger.error(f"[{req_id}] Role with ID {request_data.role_id} not found or access denied")
                return

            # Load the appropriate agent services based on agent_type
            agent_loader = AgentLoader(agent_type=request_data.agent_type)
            agent_services = agent_loader.get_services()

            # Mark the transcript as completed
            try:
                await agent_services.complete_transcript(request_data.user_id, request_data.role_id, request_data.transcript_id)
                logger.info(f"[{req_id}] Successfully completed transcript: {request_data.transcript_id}")
            except Exception as transcript_error:
                logger.warning(f"[{req_id}] Error completing transcript: {str(transcript_error)}")

            # Get transcript
            target_transcript = None
            try:
                target_transcript = await agent_services.get_transcript(
                    user_id=request_data.user_id,
                    role_id=request_data.role_id,
                    transcript_id=request_data.transcript_id
                )
            except Exception as get_error:
                logger.error(f"[{req_id}] Error getting transcript: {str(get_error)}")
                return

            if not target_transcript:
                logger.warning(f"[{req_id}] Could not find transcript {request_data.transcript_id} for processing")
                return

            # Process based on agent type
            if request_data.agent_type == "interview_agent":
                logger.info(f"[{req_id}] Processing interview transcript in background: {request_data.transcript_id}")

                # Process interview results
                try:
                    results = await agent_services.process_interview_results(
                        user_id=request_data.user_id,
                        role_id=request_data.role_id,
                        transcript_id=request_data.transcript_id
                    )
                    logger.info(f"[{req_id}] Successfully processed interview results")
                except Exception as process_error:
                    logger.error(f"[{req_id}] Error processing interview results: {str(process_error)}")
                    return

            # For intake agents with enrichment method
            elif request_data.process_method == "enrichment_agent":
                logger.info(f"[{req_id}] Processing intake transcript with enrichment in background")
                ai_service = AIService()

                # Use the transcript we already fetched
                transcript = target_transcript

                if not transcript or not transcript.get("messages"):
                    logger.warning(f"[{req_id}] No transcript messages found for enrichment")
                    return

                # Process the transcript to enrich the role data
                try:
                    # Attempt to enrich the role with transcript data
                    enriched_data = await ai_service.enrich_role(role, transcript)

                    # Check for error status in the response
                    if not enriched_data or not isinstance(enriched_data, dict):
                        logger.error(f"[{req_id}] Invalid enriched data format: {type(enriched_data)}")
                        return

                    if enriched_data.get("status") == "error":
                        error_message = enriched_data.get('message', 'Unknown error')
                        logger.error(f"[{req_id}] Error enriching role: {error_message}")
                        # Don't proceed with updating the role if enrichment failed
                        # This ensures we don't create a default/fallback role
                        return

                    # Validate that we have meaningful enriched data
                    # Ensure we have at least some key fields populated
                    required_fields = ["title", "summary", "requiredSkills"]
                    missing_fields = [field for field in required_fields if field not in enriched_data or not enriched_data.get(field)]

                    if missing_fields:
                        logger.error(f"[{req_id}] Enriched data missing required fields: {missing_fields}")
                        return

                    # Additional validation for requiredSkills - ensure we have at least 3 skills
                    if "requiredSkills" in enriched_data:
                        required_skills = enriched_data.get("requiredSkills", {})
                        if len(required_skills) < 3:
                            logger.error(f"[{req_id}] Insufficient required skills: only {len(required_skills)} found, minimum 3 needed")
                            return

                    # Validate that the title is not generic
                    generic_titles = ["software engineer", "developer", "engineer", "manager", "analyst", "consultant"]
                    if "title" in enriched_data:
                        title = enriched_data.get("title", "").lower()
                        if title in generic_titles:
                            logger.error(f"[{req_id}] Title is too generic: {title}")
                            return

                    # Validate that the summary is meaningful (at least 50 characters)
                    if "summary" in enriched_data:
                        summary = enriched_data.get("summary", "")
                        if len(summary) < 50:
                            logger.error(f"[{req_id}] Summary is too short: {len(summary)} characters, minimum 50 needed")
                            return

                    # Add transcript ID to the enriched data
                    enriched_data["intake_transcript_id"] = request_data.transcript_id

                    try:
                        # First try to use the agent_services update_role method
                        if hasattr(agent_services, 'update_role'):
                            await agent_services.update_role(
                                request_data.role_id,
                                enriched_data,
                                request_data.user_id,
                                request_data.is_enrichment
                            )
                        else:
                            # Fall back to direct role service update
                            logger.warning(f"[{req_id}] Agent service doesn't have update_role method, falling back to roles_service")
                            if request_data.is_enrichment:
                                await roles_service.enrich_role(request_data.role_id, enriched_data, request_data.user_id)
                            else:
                                await roles_service.update_role(request_data.role_id, enriched_data, request_data.user_id)

                        logger.info(f"[{req_id}] Role enriched and updated successfully in background")
                    except Exception as update_error:
                        logger.error(f"[{req_id}] Error updating role with enriched data: {str(update_error)}")
                        logger.error(traceback.format_exc())
                        return
                except Exception as enrich_error:
                    logger.error(f"[{req_id}] Error in role enrichment process: {str(enrich_error)}")
                    # Don't proceed with any fallback updates
                    return
            elif request_data.process_method == "direct_data" and request_data.role_data:
                logger.info(f"[{req_id}] Processing direct data update in background")

                try:
                    # First try to use the agent_services update_role method
                    if hasattr(agent_services, 'update_role'):
                        await agent_services.update_role(
                            request_data.role_id,
                            request_data.role_data,
                            request_data.user_id,
                            request_data.is_enrichment
                        )
                    else:
                        # Fall back to direct role service update
                        logger.warning(f"[{req_id}] Agent service doesn't have update_role method, falling back to roles_service")
                        if request_data.is_enrichment:
                            await roles_service.enrich_role(request_data.role_id, request_data.role_data, request_data.user_id)
                        else:
                            await roles_service.update_role(request_data.role_id, request_data.role_data, request_data.user_id)

                    logger.info(f"[{req_id}] Role updated successfully with direct data in background")
                except Exception as update_error:
                    logger.error(f"[{req_id}] Error updating role with direct data: {str(update_error)}")
                    logger.error(traceback.format_exc())
                    return
            else:
                logger.warning(f"[{req_id}] Invalid process_method ({request_data.process_method}) or missing role_data")
                return

            logger.info(f"[{req_id}] Background processing completed successfully")
        except Exception as e:
            logger.error(f"[{req_id}] Unhandled error in background processing: {str(e)}")
            logger.error(traceback.format_exc())

    try:
        logger.info(f"[{request_id}] Received end_call request for role: {request.role_id}, transcript: {request.transcript_id}, agent_type: {request.agent_type}")
        logger.info(f"[{request_id}] Process method: {request.process_method}")

        if current_user["id"] != request.user_id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="User ID mismatch: Unauthorized action")

        # Create a background task for processing
        background_task = asyncio.create_task(
            process_in_background(request, current_user, request_id)
        )

        # Return an immediate response
        return JSONResponse(
            status_code=status.HTTP_202_ACCEPTED,
            content={
                "status": "accepted",
                "message": "Request accepted for processing",
                "request_id": request_id
            }
        )
    except Exception as e:
        logger.error(f"[{request_id}] Error in end_call: {str(e)}")
        logger.error(f"[{request_id}] Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing end call request: {str(e)}"
        )

@router.post("/interview-session")
async def create_interview_session(
    role_id: str = Body(...),
    template_id: str = Body(...),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new realtime session for an interview"""
    try:
        user_id = current_user["id"]
        logger.info(f"Creating interview session for role {role_id} and template {template_id}")

        # Initialize the agent loader for the interview agent
        agent_type = "interview_agent"
        logger.info(f"Using agent type: {agent_type}")
        agent_loader = AgentLoader(agent_type=agent_type)

        try:
            # Load the interview agent services
            agent_services = agent_loader.get_services()

            # Build context for the interview agent
            logger.info(f"Building context for interview agent with role_id={role_id}, template_id={template_id}")
            context = await agent_services.get_interview_context(role_id, template_id, user_id)

            # Dynamically load the prompt and functions
            logger.info(f"Loading prompt for {agent_type}")
            prompt = agent_loader.get_prompt(agent_type)
            functions_module = agent_loader.get_functions()
            functions = functions_module.realtime_functions

            # Format the prompt with context
            logger.info(f"Formatting prompt with context keys: {list(context.keys())}")
            formatted_prompt = prompt.format(**context)

            logger.info(f"Successfully loaded prompt and functions for {agent_type}")
        except Exception as e:
            logger.error(f"Error loading agent components: {str(e)}")
            # Include traceback for better debugging
            import traceback
            logger.error(traceback.format_exc())
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error loading agent components: {str(e)}"
            )

        # Get the appropriate model for the agent type
        model = ModelConfigurationManager.get_appropriate_model(agent_type)
        logger.info(f"Selected model for {agent_type}: {model}")

        # Ensure the model is a realtime model
        if not ModelConfigurationManager.is_realtime_model(model):
            logger.warning(f"Model {model} is not a realtime model, falling back to default realtime model")
            model = "gpt-4o-mini-realtime-preview-2024-12-17"

        # Get model-specific configuration for realtime
        model_config = ModelConfigurationManager.get_model_config(model, agent_type)
        logger.info(f"Using model config for {model}: {model_config}")

        # Prepare the request payload with model-specific configuration
        payload = {
            "model": model,
            "voice": "shimmer",
            "modalities": ["audio", "text"],
            "input_audio_format": "pcm16",
            "output_audio_format": "pcm16",
            "input_audio_transcription": {"model": "whisper-1"},
            "turn_detection": {
                "type": "server_vad",
                "threshold": 0.3,
                "prefix_padding_ms": 300,
                "silence_duration_ms": 1000,
                "create_response": True
            },
            "instructions": formatted_prompt,
            "tools": functions,
            "tool_choice": "auto",
            "max_response_output_tokens": 4096
        }

        # List of parameters not supported by the realtime API
        unsupported_params = ["top_p", "frequency_penalty", "presence_penalty", "max_tokens"]

        # Add model-specific parameters, excluding unsupported ones
        for key, value in model_config.items():
            if key not in payload and key not in ["timeout"] and key not in unsupported_params:
                payload[key] = value

        logger.info(f"Realtime session payload keys: {list(payload.keys())}")
        logger.debug(f"Realtime session payload: {json.dumps(payload, indent=2)}")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                'https://api.openai.com/v1/realtime/sessions',
                headers={
                    'Authorization': f'Bearer {settings.OPENAI_API_KEY}',
                    'Content-Type': 'application/json'
                },
                json=payload,
                timeout=model_config.get("timeout", 30.0)
            )

            if response.status_code != 200:
                error_detail = response.json() if response.text else "No error details available"
                logger.error(f"OpenAI API Error: {error_detail}")

                # Extract specific error message for better debugging
                error_message = "Failed to create OpenAI session"
                if isinstance(error_detail, dict) and 'error' in error_detail:
                    if 'message' in error_detail['error']:
                        error_message = f"{error_message}: {error_detail['error']['message']}"
                    if 'param' in error_detail['error']:
                        error_message = f"{error_message} (parameter: {error_detail['error']['param']})"

                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail=error_message
                )

            session_data = response.json()

            # Generate a transcript ID but don't create a transcript record yet
            transcript_id = str(uuid.uuid4())

            # Transform the session data to match the expected format
            transformed_data = {
                "session_id": session_data.get("id"),
                "client_secret": {
                    "value": session_data.get("client_secret", {}).get("value"),
                    "expires_at": session_data.get("client_secret", {}).get("expires_at", 0)
                },
                "transcript_id": transcript_id,
                "role_id": role_id,
                "template_id": template_id
            }

            logger.info("Successfully created interview session")
            return transformed_data

    except httpx.TimeoutException:
        logger.error("Timeout while connecting to OpenAI API")
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Request to OpenAI API timed out"
        )
    except Exception as e:
        logger.error(f"Unexpected error in create_interview_session: {str(e)}")
        # Include traceback for better debugging
        import traceback
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

class EndInterviewRequest(BaseModel):
    role_id: str
    transcript_id: str
    user_id: str

@router.post("/end-interview")
async def end_interview(
    request: EndInterviewRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """End an interview session and process the results"""
    try:
        logger.info(f"Ending interview for role: {request.role_id}, transcript: {request.transcript_id}")

        if current_user["id"] != request.user_id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Unauthorized")

        # Initialize the agent loader for the interview agent
        agent_loader = AgentLoader(agent_type="interview_agent")
        agent_services = agent_loader.get_services()

        # Mark the transcript as completed
        await agent_services.complete_transcript(request.user_id, request.role_id, request.transcript_id)

        # Process the interview results
        results = await agent_services.process_interview_results(request.user_id, request.role_id, request.transcript_id)

        return {
            "status": "success",
            "message": "Interview completed and results processed successfully.",
            "results": results
        }

    except Exception as e:
        logger.error(f"Error in end_interview: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.head("/check-agent/{agent_type}")
async def check_agent(agent_type: str = Path()):
    """Check if an agent's prompt file exists"""
    try:
        # Find the prompt file
        agent_loader = AgentLoader(agent_type=agent_type)
        prompt_path = agent_loader.get_prompt_path()

        # Return ok if the prompt file exists
        return {"status": "ok"}

    except Exception as e:
        logger.error(f"Error in check_agent: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.post("/public-interview-session", response_model=SessionResponse)
async def create_public_interview_session(
    request: PublicInterviewSessionRequest,
    roles_service: RolesService = Depends(lambda: RolesService()),
    firebase_service: FirebaseService = Depends(lambda: FirebaseService())
):
    """
    Create a public interview session for candidates without authentication.

    Parameters:
    - role_id: The ID of the role to interview for
    - template_id: Optional template ID (defaults to screening template)
    - candidate_id: Optional candidate ID to link the session to a candidate
    - application_id: Optional application ID to link the session to an application

    Returns:
    - session_id: The WebRTC session ID
    - client_secret: The ephemeral API key for the session
    - transcript_id: The ID for the interview transcript
    - role_id: The role ID
    - template_id: The template ID
    - is_public: Flag indicating this is a public session
    """
    try:
        # Start timing
        start_time = datetime.now()
        logger.info(f"Creating public interview session for role {request.role_id}")

        # Handle application tracking
        application_id = request.application_id

        # If no application ID is provided, create an anonymous application
        if not application_id:
            try:
                logger.info(f"No application ID provided, creating anonymous application")
                application_id = str(uuid.uuid4())

                # Create a minimal application record
                anonymous_application = {
                    "id": application_id,
                    "roleId": request.role_id,
                    "fullName": "Anonymous Candidate",
                    "email": f"anonymous-{application_id[:8]}@example.com",
                    "status": "interviewing",
                    "source": "instant_interview",
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "is_anonymous": True
                }

                # Save to applications collection
                try:
                    app_ref = firebase_service.db.collection("applications").document(application_id)
                    app_ref.set(anonymous_application)
                    logger.info(f"Created anonymous application with ID: {application_id}")
                except Exception as app_error:
                    # Log but continue, application tracking is non-critical
                    logger.warning(f"Failed to save anonymous application: {str(app_error)}")
                    logger.warning("Continuing with session creation without application tracking")
            except Exception as app_error:
                # Log but continue, application creation is non-critical
                logger.warning(f"Error creating anonymous application: {str(app_error)}")
                application_id = None  # Reset to None since creation failed

        # Try to get the role from the get_role_by_id method
        logger.info(f"Fetching role data from get_role_by_id")
        role_data = await roles_service.get_role_by_id(request.role_id)

        if not role_data:
            logger.warning(f"Role {request.role_id} not found in database, falling back to default data")
            # If we can't find the role in the database, create a default one for the interview
            role_data = {
                "id": request.role_id,
                "title": "Position",
                "summary": "No detailed information available for this position.",
                "status": "Sourcing",
                # Add minimal required fields
                "level": "Entry Level",
                "requiredSkills": {},
                "preferredSkills": {},
                "jobType": "Full-time",
                "location": {"remoteStatus": "Remote"}
            }

        # TEMPORARILY BYPASS PUBLIC CHECK FOR DEVELOPMENT
        role = role_data
        logger.info(f"Using role: {role['id']} for public interview")

        # Get template_id from request, but we'll pass None to the service to let it find the screening template
        template_id = request.template_id
        logger.info(f"Template ID from request: {template_id if template_id else 'None, will use screening template'}")

        # Initialize the agent loader for the interview agent
        agent_type = "interview_agent"
        logger.info(f"Using agent type: {agent_type}")
        agent_loader = AgentLoader(agent_type=agent_type)

        try:
            # Load the agent services
            agent_services = agent_loader.get_services()

            # Build context for the interview agent
            logger.info(f"Building context for interview agent with role_id={request.role_id}, template_id={template_id}")

            # Pass application_id to the context building function if available
            if application_id:
                logger.info(f"Including application data from application_id={application_id}")
                context = await agent_services.get_public_interview_context(request.role_id, template_id, application_id)
            else:
                context = await agent_services.get_public_interview_context(request.role_id, template_id)

            # Add resume_text and job_posting from the request if provided
            if request.resume_text:
                logger.info("Using resume_text from request")
                context["candidate_resume"] = request.resume_text

            if request.job_posting:
                logger.info("Using job_posting from request")
                context["job_posting"] = request.job_posting

            # Dynamically load the prompt and functions
            logger.info(f"Loading prompt for {agent_type}")
            prompt = agent_loader.get_prompt(agent_type)
            functions_module = agent_loader.get_functions()
            functions = functions_module.realtime_functions

            # Format the prompt with context
            logger.info(f"Formatting prompt with context keys: {list(context.keys())}")
            formatted_prompt = prompt.format(**context)

            logger.info(f"Successfully loaded prompt and functions for {agent_type}")
        except Exception as e:
            logger.error(f"Error loading agent components: {str(e)}")
            # Include traceback for better debugging
            import traceback
            logger.error(traceback.format_exc())
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error loading agent components: {str(e)}"
            )

        # Get the appropriate model for the agent type
        model = ModelConfigurationManager.get_appropriate_model(agent_type)
        logger.info(f"Selected model for {agent_type}: {model}")

        # Ensure the model is a realtime model
        if not ModelConfigurationManager.is_realtime_model(model):
            logger.warning(f"Model {model} is not a realtime model, falling back to default realtime model")
            model = "gpt-4o-mini-realtime-preview-2024-12-17"

        # Get model-specific configuration for realtime
        model_config = ModelConfigurationManager.get_model_config(model, agent_type)
        logger.info(f"Using model config for {model}: {model_config}")

        # Prepare the request payload with model-specific configuration
        payload = {
            "model": model,
            "voice": "shimmer",
            "instructions": formatted_prompt,
            "tools": functions,
            "tool_choice": "auto",
        }

        # List of parameters not supported by the realtime API
        unsupported_params = ["top_p", "frequency_penalty", "presence_penalty", "max_tokens"]

        # Add model-specific parameters, excluding unsupported ones
        for key, value in model_config.items():
            if key not in payload and key not in ["timeout"] and key not in unsupported_params:
                payload[key] = value

        logger.info(f"Realtime session payload keys: {list(payload.keys())}")
        logger.debug(f"Realtime session payload: {json.dumps(payload, indent=2)}")

        # Use the main API key directly for increased reliability
        api_key = settings.OPENAI_API_KEY
        logger.info(f"Using direct API key authentication (key starts with: {api_key[:5]}...)")

        async with httpx.AsyncClient() as client:
            # Send the request to the sessions endpoint
            response = await client.post(
                'https://api.openai.com/v1/realtime/sessions',
                headers={
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json'
                },
                json=payload,
                timeout=model_config.get("timeout", 30.0)
            )

            if response.status_code != 200:
                error_detail = response.json() if response.text else "No error details available"
                logger.error(f"OpenAI API Error: {error_detail}")

                # Extract specific error message for better debugging
                error_message = "Failed to create OpenAI session"
                if isinstance(error_detail, dict) and 'error' in error_detail:
                    if 'message' in error_detail['error']:
                        error_message = f"{error_message}: {error_detail['error']['message']}"
                    if 'param' in error_detail['error']:
                        error_message = f"{error_message} (parameter: {error_detail['error']['param']})"

                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail=error_message
                )

            # Extract session data from response
            session_data = response.json()

            # Generate a transcript ID but don't create a transcript record yet
            transcript_id = str(uuid.uuid4())

            # Create session response
            session_response = SessionResponse(
                session_id=session_data.get("id"),
                client_secret={
                    "value": session_data.get("client_secret", {}).get("value"),
                    "expires_at": session_data.get("client_secret", {}).get("expires_at", 0)
                },
                transcript_id=transcript_id,
                role_id=request.role_id,
                template_id=template_id,
                candidate_id=request.candidate_id,
                application_id=application_id,
                is_public=True
            )

            # Try to get template information if template_id is provided
            if template_id:
                try:
                    # Find the role owner
                    users_ref = firebase_service.db.collection("users")
                    roles_query = users_ref.limit(10).stream()  # Limit to avoid excessive reads

                    user_id = None
                    for user_doc in roles_query:
                        # Check if this user has the role
                        role_ref = firebase_service.db.collection("users").document(user_doc.id).collection("roles").document(request.role_id)
                        if role_ref.get().exists:
                            user_id = user_doc.id
                            break

                    if user_id:
                        # Get the template
                        template_ref = firebase_service.db.collection("users").document(user_id).collection("roles").document(request.role_id).collection("interviewTemplates").document(template_id)
                        template_doc = template_ref.get()

                        if template_doc.exists:
                            template_data = template_doc.to_dict()
                            # Add template name to session response
                            session_response.template_name = template_data.get("name", "Interview Template")

                            # Add stage information if available
                            if "stage" in template_data:
                                session_response.stage = template_data["stage"]
                            elif "type" in template_data:
                                session_response.stage = template_data["type"]
                except Exception as e:
                    logger.warning(f"Error getting template information: {str(e)}")

            # Additional validation of session response
            if not session_response.session_id:
                logger.error("Missing session_id in OpenAI response")
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail="Invalid session response from OpenAI: missing session_id"
                )

            if not session_response.client_secret.get("value"):
                logger.error("Missing client_secret value in OpenAI response")
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail="Invalid session response from OpenAI: missing client_secret.value"
                )

            # Verify expiration time is reasonable (more than 5 minutes in the future)
            current_time = int(datetime.now().timestamp())
            expires_at = session_response.client_secret.get("expires_at", 0)
            if expires_at - current_time < 300:  # Less than 5 minutes
                logger.error(f"Session token expires too soon: {expires_at - current_time} seconds")
                raise HTTPException(
                    status_code=status.HTTP_502_BAD_GATEWAY,
                    detail="Session token will expire too soon"
                )

            # Log token expiration time
            logger.info(f"Session token will expire in {expires_at - current_time} seconds")

            # Store session information for later reference
            session_dict = {
                "session_id": session_response.session_id,
                "client_secret": {
                    "value": session_response.client_secret["value"],
                    "expires_at": session_response.client_secret["expires_at"],
                },
                "transcript_id": transcript_id,
                "role_id": request.role_id,
                "template_id": template_id,
                "is_public": True,
                "created_at": datetime.utcnow(),
                "status": "initialized"
            }

            # Add template name if available
            if hasattr(session_response, 'template_name') and session_response.template_name:
                session_dict["template_name"] = session_response.template_name
                session_dict["templateName"] = session_response.template_name

            # Add stage information if available
            if hasattr(session_response, 'stage') and session_response.stage:
                session_dict["stage"] = session_response.stage
                session_dict["interviewStage"] = session_response.stage

            # Add stage index if available
            if hasattr(session_response, 'stage_index') and session_response.stage_index is not None:
                session_dict["stage_index"] = session_response.stage_index
                session_dict["stageIndex"] = session_response.stage_index

            # Add candidate and application IDs if provided
            if request.candidate_id:
                session_dict["candidateId"] = request.candidate_id
                session_dict["candidate_id"] = request.candidate_id
            if request.application_id:
                session_dict["applicationId"] = request.application_id
                session_dict["application_id"] = request.application_id

            # Add candidate name if provided
            try:
                if hasattr(request, 'candidate_name') and request.candidate_name:
                    session_dict["candidateName"] = request.candidate_name
                    session_dict["candidate_name"] = request.candidate_name
            except Exception as e:
                logger.warning(f"Error adding candidate name to session: {str(e)}")

            # Save the session data in Firestore for reference
            try:
                # The method is defined as async, so we need to await it
                await firebase_service.create_public_interview_session(session_dict)
                logger.info(f"Successfully saved public interview session data for session {session_response.session_id}")
            except Exception as e:
                # Log but don't fail if session storage fails - we can still proceed with the interview
                logger.warning(f"Failed to save public interview session data: {str(e)}")
                logger.warning("Continuing with session creation despite storage failure")

            # Calculate and log the time taken
            time_taken = (datetime.now() - start_time).total_seconds()
            logger.info(f"Public interview session created in {time_taken:.2f} seconds")

            # Return the session response
            return session_response

    except httpx.TimeoutException:
        logger.error("Timeout while connecting to OpenAI API")
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Request to OpenAI API timed out"
        )
    except HTTPException:
        # Re-raise HTTPExceptions to preserve status codes
        raise
    except Exception as e:
        logger.error(f"Unexpected error in create_public_interview_session: {str(e)}")
        # Include traceback for better debugging
        import traceback
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating public interview session: {str(e)}"
        )

@router.get("/debug-openai")
async def debug_openai():
    """
    Debug endpoint to test OpenAI's realtime API connection
    This endpoint attempts to create a minimal session and returns diagnostic information
    """
    try:
        logger.info("Testing OpenAI realtime API connection")

        # Create a minimal working payload
        payload = {
            "model": "gpt-4o-mini-realtime-preview-2024-12-17",
            "voice": "shimmer",
            "modalities": ["audio", "text"],
            "input_audio_format": "pcm16",
            "output_audio_format": "pcm16",
            "instructions": "You are a helpful assistant.",
            "max_response_output_tokens": 4096
        }

        # Get the API key
        api_key = settings.OPENAI_API_KEY

        # Log useful diagnostic info
        logger.info(f"OpenAI API key starts with: {api_key[:5]}...")
        logger.info(f"Using model: {payload['model']}")

        # Attempt connection
        async with httpx.AsyncClient() as client:
            response = await client.post(
                'https://api.openai.com/v1/realtime/sessions',
                headers={
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json'
                },
                json=payload,
                timeout=30.0
            )

            logger.info(f"OpenAI API response status: {response.status_code}")

            if response.status_code != 200:
                error_detail = response.json() if response.text else "No error details available"
                logger.error(f"OpenAI API Error: {error_detail}")
                return {
                    "status": "error",
                    "message": "Failed to connect to OpenAI API",
                    "http_status": response.status_code,
                    "details": error_detail
                }

            session_data = response.json()

            # Return diagnostic info without full client_secret
            return {
                "status": "success",
                "message": "Successfully connected to OpenAI API",
                "session_id": session_data.get("id"),
                "client_secret_provided": bool(session_data.get("client_secret")),
                "expires_at": session_data.get("client_secret", {}).get("expires_at")
            }

    except httpx.TimeoutException:
        logger.error("Timeout while connecting to OpenAI API")
        return {
            "status": "error",
            "message": "Request to OpenAI API timed out"
        }
    except Exception as e:
        logger.error(f"Unexpected error in debug_openai: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {
            "status": "error",
            "message": f"Error in debug endpoint: {str(e)}"
        }

@router.get("/debug-prompt-format")
async def debug_prompt_format(
    role_id: str = None
):
    """
    Test endpoint to validate prompt formatting logic
    """
    try:
        logger.info(f"Debug prompt formatting with role_id: {role_id}")

        # Get a simplified test prompt
        test_prompt = """
Role: You are a helpful assistant.

{existing_role_data_section}

Ask questions about the following topics:
1. Introduction{enrichment_intro}
2. Experience
3. Skills
        """

        context = {}

        # If role_id is provided, add some test data
        if role_id:
            # Create a section with role details for testing - avoid f-string with backslashes
            existing_role_data_section = "### TEST ROLE DATA\n"
            existing_role_data_section += f"This is a test for role {role_id}:\n"
            existing_role_data_section += "- Title: Test Role\n"
            existing_role_data_section += "- Team: Test Team\n"

            # Add the formatted section to the context
            context["existing_role_data_section"] = existing_role_data_section
            context["enrichment_intro"] = ", mentioning this is an enrichment call"
        else:
            context["existing_role_data_section"] = ""
            context["enrichment_intro"] = ""

        # Format the prompt with the context
        try:
            formatted_prompt = test_prompt.format(**context)

            return {
                "status": "success",
                "original_prompt": test_prompt,
                "context_keys": list(context.keys()),
                "formatted_prompt": formatted_prompt
            }
        except Exception as format_error:
            logger.error(f"Error formatting prompt: {str(format_error)}")
            return {
                "status": "error",
                "message": f"Error formatting prompt: {str(format_error)}",
                "original_prompt": test_prompt,
                "context": context
            }
    except Exception as e:
        logger.error(f"Error in debug_prompt_format: {str(e)}")
        return {
            "status": "error",
            "message": f"Unexpected error: {str(e)}"
        }

@router.post("/update-public-transcript")
async def update_public_transcript(
    request: PublicTranscriptUpdate,
    firebase_service: FirebaseService = Depends(lambda: FirebaseService())
) -> Dict[str, Any]:
    """
    Update a transcript for a public interview session.
    This allows direct updates from the frontend for public interviews.
    """
    try:
        logger.info(f"Updating public interview transcript for session {request.session_id}, transcript {request.transcript_id}")

        # Use the provided session ID, which may be in OpenAI format (sess_*) or a custom format
        session_id = request.session_id

        # First check if the session exists in the public_interview_sessions collection
        session_ref = firebase_service.db.collection("public_interview_sessions").document(session_id)
        session_doc = session_ref.get()

        # If the session doesn't exist and it's not in OpenAI format, try to find a matching OpenAI session
        if not session_doc.exists and not session_id.startswith('sess_') and request.transcript_id and request.transcript_id.startswith('sess_'):
            # Try using the transcript ID as the session ID if it has the OpenAI format
            logger.info(f"Trying transcript_id {request.transcript_id} as session_id since it has OpenAI format")
            session_id = request.transcript_id
            session_ref = firebase_service.db.collection("public_interview_sessions").document(session_id)
            session_doc = session_ref.get()

        if not session_doc.exists:
            logger.warning(f"Session {session_id} not found in public_interview_sessions collection")

            # If the session ID doesn't exist, try to find a session with the same role_id and applicationId
            # This helps handle cases where the frontend is using a different ID than what was created
            if request.application_id:
                query = firebase_service.db.collection("public_interview_sessions")\
                    .where("role_id", "==", request.role_id)\
                    .where("applicationId", "==", request.application_id)\
                    .limit(1)
                matching_sessions = query.get()

                if len(matching_sessions) > 0:
                    # Use the existing session instead
                    existing_session = matching_sessions[0]
                    session_id = existing_session.id
                    session_ref = firebase_service.db.collection("public_interview_sessions").document(session_id)
                    session_doc = session_ref.get()
                    logger.info(f"Found existing session {session_id} for role {request.role_id} and application {request.application_id}")

            # If we still don't have a valid session, create a new one
            if not session_doc.exists:
                # Create a new document for this session
                session_data = {
                    "session_id": session_id,
                    "transcript_id": request.transcript_id,
                    "role_id": request.role_id,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                    "status": "in_progress",
                    "is_public": True
                }

                if request.application_id:
                    session_data["applicationId"] = request.application_id

                session_ref.set(session_data)
                logger.info(f"Created new session document for {session_id}")

        # Now update the transcript field in the session document
        transcript_data = {
            "messages": request.messages,
            "updated_at": datetime.utcnow(),
            "message_count": len(request.messages)
        }

        # Update the session document with the transcript data
        session_ref.update({
            "messages": request.messages,  # Save messages directly in the document
            "transcript": transcript_data, # Keep the nested structure for backward compatibility
            "updated_at": datetime.utcnow(),
            "message_count": len(request.messages)
        })

        logger.info(f"Successfully updated public interview transcript for session {session_id}")

        return {
            "status": "success",
            "message": "Transcript updated successfully"
        }
    except Exception as e:
        logger.error(f"Error updating public interview transcript: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating public interview transcript: {str(e)}"
        )