# File: backend/app/api/v1/endpoints/templates.py

from typing import Dict, Any, List, Optional, Callable, TypeVar, ParamSpec, Union
from fastapi import APIRouter, HTTPException, Depends, Body, Query, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, validator, root_validator, Field
from datetime import datetime
from enum import Enum
import firebase_admin.exceptions
import logging
from functools import wraps
from firebase_admin import auth
import uuid

from app.services.firebase_service import FirebaseService
from app.services.auth_service import AuthService

# Type variables for generic function signatures
T = TypeVar("T")
P = ParamSpec("P")

def verify_role_access():
    """Decorator to verify role access before executing endpoint."""
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @wraps(func)
        async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            # Extract role_id and current_user from kwargs
            role_id = kwargs.get('role_id')
            current_user = kwargs.get('current_user')

            if not role_id or not current_user:
                raise HTTPException(status_code=400, detail="Missing required parameters")

            # Verify access
            role = await firebase_service.get_role(user_id=current_user["id"], role_id=role_id)
            if not role:
                raise HTTPException(status_code=404, detail="Role not found")

            # Continue with the original function
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def handle_template_errors(func: Callable[P, T]) -> Callable[P, T]:
    """
    Decorator to handle common errors in template endpoints.
    """
    @wraps(func)
    async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
        try:
            return await func(*args, **kwargs)
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except ValueError as e:
            logging.error(f"Value error in {func.__name__}: {str(e)}")
            raise HTTPException(status_code=400, detail=str(e))
        except KeyError as e:
            logging.error(f"Key error in {func.__name__}: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Missing required field: {str(e)}")
        except firebase_admin.exceptions.FirebaseError as e:
            logging.error(f"Firebase error in {func.__name__}: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Authentication error: {str(e)}")
        except Exception as e:
            logging.error(f"Unexpected error in {func.__name__}: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")
    return wrapper

# Router for interview templates
router = APIRouter(
    prefix="/templates",
    tags=["templates"]
)

firebase_service = FirebaseService()
auth_service = AuthService()
security = HTTPBearer()

# Pydantic models for interview templates
class TemplateStatus(str, Enum):
    DRAFT = "Draft"
    ACTIVE = "Active"
    PAUSED = "Paused"
    ARCHIVED = "Archived"

class TemplateStatistics(BaseModel):
    candidatesInterviewed: int = 0
    passRate: float = 0
    averageScore: float = 0
    topScore: float = 0
    topPerformerIds: List[str] = []

# Question models
class QuestionStatistics(BaseModel):
    """Statistics for a question in an interview template."""
    topScore: float = 0
    topScoringCandidateId: str = ""
    averageScore: float = 0
    totalAnswers: int = 0

class QuestionBase(BaseModel):
    """Base model for a question in an interview template."""
    question: str
    purpose: str = ""
    idealAnswerCriteria: str = ""
    statistics: QuestionStatistics = Field(default_factory=QuestionStatistics)

class QuestionCreate(QuestionBase):
    """Model for creating a new question."""
    pass

class QuestionUpdate(BaseModel):
    """Model for updating an existing question."""
    question: Optional[str] = None
    purpose: Optional[str] = None
    idealAnswerCriteria: Optional[str] = None
    statistics: Optional[QuestionStatistics] = None

class Question(QuestionBase):
    """Model for a question with ID."""
    id: str

class QuestionReorder(BaseModel):
    """Model for reordering questions."""
    questionIds: List[str]

# Evaluation Criteria Models
class CriterionType(str, Enum):
    """Enum for evaluation criterion types."""
    SCORECARD = "ScoreCard"
    BETWEEN_THE_LINES = "BetweenTheLines"
    DISQUALIFIER = "Disqualifier"

class CriterionBase(BaseModel):
    """Base model for an evaluation criterion."""
    type: CriterionType
    criteria: str
    description: Optional[str] = None

class ScoreCardCriterion(CriterionBase):
    """Model for a ScoreCard criterion."""
    type: CriterionType = CriterionType.SCORECARD
    competency: str
    weight: float

    @validator('weight')
    def validate_weight(cls, v):
        """Validate that weight is between 0 and 1."""
        if v < 0 or v > 1:
            raise ValueError("Weight must be between 0 and 1")
        return v

class BetweenTheLinesCriterion(CriterionBase):
    """Model for a Between the Lines criterion."""
    type: CriterionType = CriterionType.BETWEEN_THE_LINES

class DisqualifierCriterion(CriterionBase):
    """Model for a Disqualifier criterion."""
    type: CriterionType = CriterionType.DISQUALIFIER

class CriterionCreate(BaseModel):
    """Model for creating a new criterion."""
    type: CriterionType
    criteria: str
    description: Optional[str] = None
    competency: Optional[str] = None
    weight: Optional[float] = None

    @root_validator(skip_on_failure=True)
    def validate_fields(cls, values):
        """Validate that the required fields are present based on the criterion type."""
        criterion_type = values.get('type')

        if criterion_type == CriterionType.SCORECARD:
            if not values.get('competency'):
                raise ValueError("Competency is required for ScoreCard criteria")
            if values.get('weight') is None:
                raise ValueError("Weight is required for ScoreCard criteria")
            if values.get('weight') < 0 or values.get('weight') > 1:
                raise ValueError("Weight must be between 0 and 1")

        return values

class CriterionUpdate(BaseModel):
    """Model for updating an existing criterion."""
    criteria: Optional[str] = None
    description: Optional[str] = None
    competency: Optional[str] = None
    weight: Optional[float] = None

    @validator('weight')
    def validate_weight(cls, v):
        """Validate that weight is between 0 and 1 (representing min score from 1-10)."""
        if v is not None and (v < 0 or v > 1):
            raise ValueError("Weight must be between 0 and 1")
        return v

class Criterion(BaseModel):
    """Model for a criterion with ID."""
    id: str
    type: CriterionType
    criteria: str
    description: Optional[str] = None
    competency: Optional[str] = None
    weight: Optional[float] = None

class PassRateUpdate(BaseModel):
    """Model for updating the pass rate."""
    passRate: float

    @validator('passRate')
    def validate_pass_rate(cls, v):
        """Validate that pass rate is between 0 and 1."""
        if v < 0 or v > 1:
            raise ValueError("Pass rate must be between 0 and 1")
        return v

class InterviewTemplateBase(BaseModel):
    stageIndex: int
    stage: str
    duration: str
    customInstructions: str = ""
    interviewGuide: str = ""
    questions: List[Question] = []
    evaluationCriteria: List[Dict[str, Any]] = []
    status: TemplateStatus = TemplateStatus.DRAFT
    statistics: TemplateStatistics = Field(default_factory=TemplateStatistics)

class InterviewTemplateCreate(InterviewTemplateBase):
    pass

class InterviewTemplateUpdate(BaseModel):
    stageIndex: Optional[int] = None
    stage: Optional[str] = None
    duration: Optional[str] = None
    customInstructions: Optional[str] = None
    questions: Optional[List[Question]] = None
    evaluationCriteria: Optional[List[Dict[str, Any]]] = None
    status: Optional[TemplateStatus] = None
    statistics: Optional[TemplateStatistics] = None

class InterviewTemplate(InterviewTemplateBase):
    id: str
    createdAt: datetime
    updatedAt: datetime
    createdBy: str

    class Config:
        from_attributes = True

async def get_current_user(request: Request, credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Get the current user from the Firebase token."""
    try:
        # Verify the token
        token = credentials.credentials

        try:
            # Get Firebase service instance
            firebase_service = FirebaseService()

            # Verify the Firebase token with clock tolerance
            logging.info("Attempting to verify Firebase token...")
            try:
                # First attempt with clock skew tolerance
                decoded_token = auth.verify_id_token(
                    token,
                    check_revoked=True,  # Check if the token has been revoked
                    app=firebase_service.app,  # Use the singleton app instance
                    clock_skew_seconds=30  # Allow 30 seconds of clock skew
                )
                logging.info(f"Token verified successfully with clock skew tolerance. User ID: {decoded_token.get('uid')}")
            except auth.InvalidIdTokenError as e:
                if "Token used too early" not in str(e):
                    raise  # Re-raise if it's not a clock skew issue
                logging.warning(f"Clock skew error: {str(e)}")
                # Try again with a larger tolerance if needed
                decoded_token = auth.verify_id_token(
                    token,
                    check_revoked=True,
                    app=firebase_service.app,
                    clock_skew_seconds=60  # Increase tolerance to 60 seconds as a fallback
                )
                logging.info(f"Token verified successfully with increased clock skew tolerance. User ID: {decoded_token.get('uid')}")

            uid = decoded_token.get('uid')
            if not uid:
                logging.error("No UID found in decoded token")
                raise HTTPException(status_code=401, detail="Invalid token")

            logging.info(f"Getting user data for UID: {uid}")
            # Get user from Firestore using FirebaseService
            user = await firebase_service.get_user_by_id(uid)

            if not user:
                logging.error(f"No user document found for UID: {uid}")
                raise HTTPException(status_code=404, detail="User not found")

            # Check if the user is active
            if not await firebase_service.verify_user_active(uid):
                raise HTTPException(status_code=403, detail="User account is not active")

            return user

        except auth.InvalidIdTokenError as e:
            logging.error(f"Invalid token error: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")
        except auth.ExpiredIdTokenError as e:
            logging.error(f"Token expired error: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Token has expired: {str(e)}")
        except auth.RevokedIdTokenError as e:
            logging.error(f"Token revoked error: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Token has been revoked: {str(e)}")
        except Exception as e:
            logging.error(f"Unexpected error during token verification: {str(e)}")
            raise HTTPException(status_code=401, detail=str(e))

    except Exception as e:
        logging.error(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=401, detail=f"Authentication error: {str(e)}")

@router.get("/{role_id}", response_model=List[InterviewTemplate])
@handle_template_errors
async def list_templates(role_id: str, current_user: Dict = Depends(get_current_user)) -> List[Dict[str, Any]]:
    """List all interview templates for a specific role."""
    try:
        # Verify role access
        role = await firebase_service.get_role(user_id=current_user["id"], role_id=role_id)
        if not role:
            raise HTTPException(status_code=404, detail="Role not found")

        # Get templates
        templates = await firebase_service.get_interview_templates(user_id=current_user["id"], role_id=role_id)
        return templates
    except Exception as e:
        logging.error(f"Error listing templates: {str(e)}")
        raise

@router.get("/{role_id}/{template_id}", response_model=InterviewTemplate)
@verify_role_access()
@handle_template_errors
async def get_template(role_id: str, template_id: str, current_user: Dict = Depends(get_current_user)) -> Dict[str, Any]:
    """Get a specific interview template."""
    try:
        template = await firebase_service.get_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id
        )

        if not template:
            raise HTTPException(status_code=404, detail="Template not found")

        return template
    except Exception as e:
        logging.error(f"Error getting template: {str(e)}")
        raise

@router.post("/{role_id}", response_model=InterviewTemplate)
@verify_role_access()
@handle_template_errors
async def create_template(
    role_id: str,
    template: InterviewTemplateCreate = Body(...),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Create a new interview template for a role."""
    try:
        # Convert the template to a dictionary
        template_data = template.dict()

        # Add the creator ID
        template_data["createdBy"] = current_user["id"]

        # Create the template
        template_id = await firebase_service.create_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_data=template_data
        )

        # Get the created template
        created_template = await firebase_service.get_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id
        )

        return created_template
    except Exception as e:
        logging.error(f"Error creating template: {str(e)}")
        raise

@router.put("/{role_id}/{template_id}", response_model=InterviewTemplate)
@verify_role_access()
@handle_template_errors
async def update_template(
    role_id: str,
    template_id: str,
    template: InterviewTemplateUpdate = Body(...),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Update an interview template."""
    try:
        # Verify template exists
        existing_template = await firebase_service.get_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id
        )

        if not existing_template:
            raise HTTPException(status_code=404, detail="Template not found")

        # Convert the template to a dictionary and remove None values
        updates = {k: v for k, v in template.dict().items() if v is not None}

        # Update the template
        await firebase_service.update_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id,
            updates=updates
        )

        # Get the updated template
        updated_template = await firebase_service.get_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id
        )

        return updated_template
    except Exception as e:
        logging.error(f"Error updating template: {str(e)}")
        raise

# Question Management Endpoints

@router.post("/{role_id}/{template_id}/questions", response_model=Question)
@verify_role_access()
@handle_template_errors
async def add_question(
    role_id: str,
    template_id: str,
    question: QuestionCreate = Body(...),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Add a question to an interview template."""
    try:
        # Convert the question to a dictionary
        question_data = question.dict()

        # Add the question to the template
        question_id = await firebase_service.add_question_to_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id,
            question_data=question_data
        )

        # Add the ID to the response
        question_data["id"] = question_id

        return question_data
    except Exception as e:
        logging.error(f"Error adding question: {str(e)}")
        raise

@router.put("/{role_id}/{template_id}/questions/reorder", response_model=List[Question])
@verify_role_access()
@handle_template_errors
async def reorder_questions(
    role_id: str,
    template_id: str,
    reorder_data: QuestionReorder = Body(...),
    current_user: Dict = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """Reorder questions in an interview template."""
    try:
        # Reorder the questions in the template
        await firebase_service.reorder_questions_in_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id,
            question_ids=reorder_data.questionIds
        )

        # Get the updated template
        template = await firebase_service.get_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id
        )

        if not template or 'questions' not in template:
            raise HTTPException(status_code=404, detail="Template or questions not found")

        return template['questions']
    except Exception as e:
        logging.error(f"Error reordering questions: {str(e)}")
        raise

@router.put("/{role_id}/{template_id}/questions/{question_id}", response_model=Question)
@verify_role_access()
@handle_template_errors
async def update_question(
    role_id: str,
    template_id: str,
    question_id: str,
    question: QuestionUpdate = Body(...),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Update a question in an interview template."""
    try:
        # Convert the question to a dictionary and remove None values
        updates = {k: v for k, v in question.dict().items() if v is not None}

        # Update the question in the template
        await firebase_service.update_question_in_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id,
            question_id=question_id,
            updates=updates
        )

        # Get the updated template
        template = await firebase_service.get_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id
        )

        if not template or 'questions' not in template:
            raise HTTPException(status_code=404, detail="Template or questions not found")

        # Find the updated question
        updated_question = None
        for q in template['questions']:
            if q.get('id') == question_id:
                updated_question = q
                break

        if not updated_question:
            raise HTTPException(status_code=404, detail="Question not found")

        return updated_question
    except Exception as e:
        logging.error(f"Error updating question: {str(e)}")
        raise

@router.delete("/{role_id}/{template_id}/questions/{question_id}", status_code=204)
@verify_role_access()
@handle_template_errors
async def delete_question(
    role_id: str,
    template_id: str,
    question_id: str,
    current_user: Dict = Depends(get_current_user)
) -> None:
    """Delete a question from an interview template."""
    try:
        # Delete the question from the template
        await firebase_service.delete_question_from_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id,
            question_id=question_id
        )
    except Exception as e:
        logging.error(f"Error deleting question: {str(e)}")
        raise

# Evaluation Criteria Management Endpoints

@router.post("/{role_id}/{template_id}/criteria", response_model=Criterion)
@verify_role_access()
@handle_template_errors
async def add_criterion(
    role_id: str,
    template_id: str,
    criterion: CriterionCreate = Body(...),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Add an evaluation criterion to an interview template."""
    try:
        # Convert the criterion to a dictionary
        criterion_data = criterion.dict()

        # Add the criterion to the template
        criterion_id = await firebase_service.add_criterion_to_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id,
            criterion_data=criterion_data
        )

        # Add the ID to the response
        criterion_data["id"] = criterion_id

        return criterion_data
    except Exception as e:
        logging.error(f"Error adding criterion: {str(e)}")
        raise

@router.put("/{role_id}/{template_id}/criteria/{criterion_id}", response_model=Criterion)
@verify_role_access()
@handle_template_errors
async def update_criterion(
    role_id: str,
    template_id: str,
    criterion_id: str,
    criterion: CriterionUpdate = Body(...),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """Update an evaluation criterion in an interview template."""
    try:
        # Convert the criterion to a dictionary and remove None values
        updates = {k: v for k, v in criterion.dict().items() if v is not None}

        # Update the criterion in the template
        await firebase_service.update_criterion_in_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id,
            criterion_id=criterion_id,
            updates=updates
        )

        # Get the updated template
        template = await firebase_service.get_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id
        )

        if not template or 'evaluationCriteria' not in template:
            raise HTTPException(status_code=404, detail="Template or criteria not found")

        # Find the updated criterion
        updated_criterion = None
        for c in template['evaluationCriteria']:
            if c.get('id') == criterion_id:
                updated_criterion = c
                break

        if not updated_criterion:
            raise HTTPException(status_code=404, detail="Criterion not found")

        return updated_criterion
    except Exception as e:
        logging.error(f"Error updating criterion: {str(e)}")
        raise

@router.delete("/{role_id}/{template_id}/criteria/{criterion_id}", status_code=204)
@verify_role_access()
@handle_template_errors
async def delete_criterion(
    role_id: str,
    template_id: str,
    criterion_id: str,
    current_user: Dict = Depends(get_current_user)
) -> None:
    """Delete an evaluation criterion from an interview template."""
    try:
        # Delete the criterion from the template
        await firebase_service.delete_criterion_from_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id,
            criterion_id=criterion_id
        )
    except Exception as e:
        logging.error(f"Error deleting criterion: {str(e)}")
        raise

@router.put("/{role_id}/{template_id}/pass-rate", status_code=200)
@verify_role_access()
@handle_template_errors
async def set_pass_rate(
    role_id: str,
    template_id: str,
    pass_rate_data: PassRateUpdate = Body(...),
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Set the pass rate for an interview template.

    Args:
        role_id: The ID of the role
        template_id: The ID of the template
        pass_rate_data: The pass rate data
        current_user: The current authenticated user

    Returns:
        A dictionary with the updated pass rate
    """
    try:
        # Validate the pass rate
        pass_rate = pass_rate_data.passRate

        # Update the template
        await firebase_service.set_template_pass_rate(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id,
            pass_rate=pass_rate
        )

        return {"passRate": pass_rate}
    except Exception as e:
        logging.exception(f"Error setting pass rate: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error setting pass rate: {str(e)}")

@router.post("/{role_id}/{template_id}/generate-questions", status_code=200)
@verify_role_access()
@handle_template_errors
async def generate_interview_questions(
    role_id: str,
    template_id: str,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Generate interview questions for a template using AI.

    Args:
        role_id: The ID of the role
        template_id: The ID of the template
        current_user: The current authenticated user

    Returns:
        A dictionary containing the generated questions
    """
    # Generate a unique request ID for tracking
    request_id = str(uuid.uuid4())[:8]

    try:
        from app.services.ai_service import AIService
        from starlette.requests import ClientDisconnect
        import h11

        logging.info(f"Request {request_id}: Generating interview questions for template {template_id} (role {role_id})")

        # Get the role and template
        role = await firebase_service.get_role(user_id=current_user["id"], role_id=role_id)
        if not role:
            logging.error(f"Request {request_id}: Role not found: {role_id}")
            raise HTTPException(status_code=404, detail="Role not found")

        template = await firebase_service.get_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id
        )
        if not template:
            logging.error(f"Request {request_id}: Template not found: {template_id}")
            raise HTTPException(status_code=404, detail="Template not found")

        # Generate the questions
        ai_service = AIService()
        logging.info(f"Request {request_id}: Calling AI service to generate questions for template {template_id}")

        try:
            result = await ai_service.generate_interview_questions(role, template)
        except (ClientDisconnect, h11._util.LocalProtocolError) as conn_error:
            # If client disconnects during AI processing, continue processing but log it
            logging.warning(f"Request {request_id}: Client disconnected during AI processing: {str(conn_error)}")
            # Continue processing in the background
            logging.info(f"Request {request_id}: Continuing question generation in background despite client disconnect")
            result = await ai_service.generate_interview_questions(role, template)

        if result["status"] != "success":
            logging.error(f"Request {request_id}: Failed to generate interview questions: {result.get('message', 'Unknown error')}")
            raise HTTPException(status_code=500, detail=result.get("message", "Failed to generate interview questions"))

        # Get the generated questions
        questions = result.get("questions", [])
        logging.info(f"Request {request_id}: Generated {len(questions)} questions")

        # Add IDs to the questions and ensure required fields exist
        for i, question in enumerate(questions):
            question["id"] = str(uuid.uuid4())

            # Handle field name mismatch - ensure both 'text' and 'question' fields exist
            if "text" in question and "question" not in question:
                logging.info(f"Question {i}: Copying 'text' to 'question' field")
                question["question"] = question["text"]
            elif "question" in question and "text" not in question:
                logging.info(f"Question {i}: Copying 'question' to 'text' field")
                question["text"] = question["question"]
            elif "text" not in question and "question" not in question:
                logging.warning(f"Question {i}: Missing both 'text' and 'question' fields")
                question["text"] = "Missing question text"
                question["question"] = "Missing question text"

            # Ensure other required fields exist
            if "purpose" not in question:
                logging.warning(f"Question {i}: Missing 'purpose' field")
                question["purpose"] = "General evaluation"

            if "idealAnswerCriteria" not in question:
                logging.warning(f"Question {i}: Missing 'idealAnswerCriteria' field")
                question["idealAnswerCriteria"] = "Clear, concise, and relevant response"

            # Log the question structure for debugging
            logging.info(f"Question {i} structure: {list(question.keys())}")

        # Update the template with the generated questions
        logging.info(f"Updating template {template_id} with generated questions")
        await firebase_service.update_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id,
            updates={"questions": questions}
        )

        # Final validation before returning to client
        # This ensures that the response will pass Pydantic validation
        response_questions = []
        for i, question in enumerate(questions):
            # Create a validated question object
            validated_question = {
                "id": question.get("id", str(uuid.uuid4())),
                "question": question.get("question", "Missing question text"),
                "purpose": question.get("purpose", "General evaluation"),
                "idealAnswerCriteria": question.get("idealAnswerCriteria", "Clear, concise, and relevant response"),
                "statistics": question.get("statistics", {
                    "topScore": 0,
                    "topScoringCandidateId": "",
                    "averageScore": 0,
                    "totalAnswers": 0
                })
            }

            # Add text field for backward compatibility
            validated_question["text"] = question.get("text", validated_question["question"])

            response_questions.append(validated_question)

        logging.info(f"Request {request_id}: Successfully validated {len(response_questions)} questions for response")
        return {"status": "success", "questions": response_questions}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except (ClientDisconnect, h11._util.LocalProtocolError) as conn_error:
        # Handle client disconnections gracefully
        logging.warning(f"Request {request_id}: Client disconnected: {str(conn_error)}")
        # Continue processing in the background if we've already generated questions
        if 'questions' in locals() and questions:
            logging.info(f"Request {request_id}: Client disconnected but continuing to update template with {len(questions)} questions")
            try:
                # Update the template with the generated questions in the background
                await firebase_service.update_interview_template(
                    user_id=current_user["id"],
                    role_id=role_id,
                    template_id=template_id,
                    updates={"questions": questions}
                )
                logging.info(f"Request {request_id}: Successfully updated template despite client disconnect")
            except Exception as bg_error:
                logging.error(f"Request {request_id}: Error updating template in background: {str(bg_error)}")
        # Return a special status code but continue processing in the background if possible
        raise HTTPException(status_code=499, detail="Client disconnected")
    except Exception as e:
        logging.exception(f"Request {request_id}: Error generating interview questions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating interview questions: {str(e)}")

@router.post("/{role_id}/{template_id}/generate-criteria", status_code=200)
@verify_role_access()
@handle_template_errors
async def generate_evaluation_criteria(
    role_id: str,
    template_id: str,
    current_user: Dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Generate evaluation criteria for a template using AI.

    Args:
        role_id: The ID of the role
        template_id: The ID of the template
        current_user: The current authenticated user

    Returns:
        A dictionary containing the generated evaluation criteria
    """
    try:
        from app.services.ai_service import AIService

        logging.info(f"Generating evaluation criteria for template {template_id} (role {role_id})")

        # Get the role and template
        role = await firebase_service.get_role(user_id=current_user["id"], role_id=role_id)
        if not role:
            logging.error(f"Role not found: {role_id}")
            raise HTTPException(status_code=404, detail="Role not found")

        template = await firebase_service.get_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id
        )
        if not template:
            logging.error(f"Template not found: {template_id}")
            raise HTTPException(status_code=404, detail="Template not found")

        # Generate the evaluation criteria
        ai_service = AIService()
        logging.info(f"Calling AI service to generate evaluation criteria for template {template_id}")
        result = await ai_service.generate_evaluation_criteria(role, template)

        if result["status"] != "success":
            logging.error(f"Failed to generate evaluation criteria: {result.get('message', 'Unknown error')}")
            raise HTTPException(status_code=500, detail=result.get("message", "Failed to generate evaluation criteria"))

        # Get the generated criteria
        score_card_criteria = result.get("scoreCardCriteria", [])
        between_the_lines_criteria = result.get("betweenTheLinesCriteria", [])
        disqualifier_criteria = result.get("disqualifierCriteria", [])
        pass_rate = result.get("passRate", 0.8)

        logging.info(f"Generated {len(score_card_criteria)} ScoreCard criteria")
        logging.info(f"Generated {len(between_the_lines_criteria)} Between the Lines criteria")
        logging.info(f"Generated {len(disqualifier_criteria)} Disqualifier criteria")
        logging.info(f"Pass rate: {pass_rate}")

        # Add IDs to the criteria
        for criterion in score_card_criteria:
            criterion["id"] = str(uuid.uuid4())

        for criterion in between_the_lines_criteria:
            criterion["id"] = str(uuid.uuid4())

        for criterion in disqualifier_criteria:
            criterion["id"] = str(uuid.uuid4())

        # Combine all criteria into a single array
        evaluation_criteria = score_card_criteria + between_the_lines_criteria + disqualifier_criteria

        # Update the template with the generated criteria and pass rate
        logging.info(f"Updating template {template_id} with generated evaluation criteria")
        await firebase_service.update_interview_template(
            user_id=current_user["id"],
            role_id=role_id,
            template_id=template_id,
            updates={
                "evaluationCriteria": evaluation_criteria,
                "passRate": pass_rate
            }
        )

        # Return the generated criteria
        return {
            "status": "success",
            "evaluationCriteria": evaluation_criteria,
            "passRate": pass_rate
        }
    except HTTPException:
        raise
    except Exception as e:
        logging.exception(f"Error generating evaluation criteria: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating evaluation criteria: {str(e)}")