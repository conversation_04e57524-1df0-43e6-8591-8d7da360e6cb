from typing import Optional
from pydantic import BaseModel, Field

class ClientSecret(BaseModel):
    value: str
    expires_at: int

class PublicInterviewSessionRequest(BaseModel):
    role_id: str
    template_id: Optional[str] = None
    candidate_id: Optional[str] = Field(None, description="ID of the candidate (usually email)")
    application_id: Optional[str] = Field(None, description="ID of the application")
    resume_text: Optional[str] = Field(None, description="Resume text for context")
    job_posting: Optional[str] = Field(None, description="Job posting text for context")
    candidate_name: Optional[str] = Field(None, description="Name of the candidate")

class SessionResponse(BaseModel):
    session_id: str
    client_secret: ClientSecret
    transcript_id: Optional[str] = None
    role_id: Optional[str] = None
    template_id: Optional[str] = None
    candidate_id: Optional[str] = None
    application_id: Optional[str] = None
    is_public: bool = False