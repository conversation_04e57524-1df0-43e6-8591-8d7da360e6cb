# File: backend/app/api/v1/auth.py

from typing import Dict, Any
from fastapi import HTT<PERSON>Ex<PERSON>, Depends, Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from firebase_admin import auth
import logging

from app.services.firebase_service import FirebaseService

# Set up logging
logger = logging.getLogger(__name__)

# Setup security
security = HTTPBearer()

async def get_current_user(request: Request, credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Get the current authenticated user."""
    try:
        token = credentials.credentials
        logging.info(f"Verifying token: {token[:20]}...")
        
        try:
            # Get Firebase service instance
            firebase_service = FirebaseService()
            
            # Verify the Firebase token with clock tolerance
            logging.info("Attempting to verify Firebase token...")
            try:
                # First attempt with increased clock skew tolerance
                decoded_token = auth.verify_id_token(
                    token,
                    check_revoked=True,  # Check if the token has been revoked
                    app=firebase_service.app,  # Use the singleton app instance
                    clock_skew_seconds=60  # Increased from 30 to 60 seconds
                )
                logging.info(f"Token verified successfully with clock skew tolerance. User ID: {decoded_token.get('uid')}")
            except auth.InvalidIdTokenError as e:
                if "Token used too early" in str(e) or "Token expired" in str(e):
                    logging.warning(f"Clock skew error: {str(e)}")
                    # Try again with a larger tolerance
                    decoded_token = auth.verify_id_token(
                        token,
                        check_revoked=True,
                        app=firebase_service.app,
                        clock_skew_seconds=120  # Increase tolerance to 120 seconds as a fallback
                    )
                    logging.info(f"Token verified successfully with increased clock skew tolerance. User ID: {decoded_token.get('uid')}")
                else:
                    raise  # Re-raise if it's not a clock skew issue
            
            uid = decoded_token.get('uid')
            if not uid:
                logging.error("No UID found in decoded token")
                raise HTTPException(status_code=401, detail="Invalid token")
            
            logging.info(f"Getting user data for UID: {uid}")
            # Get user from Firestore using FirebaseService
            user_ref = firebase_service.db.collection('users').document(uid)
            user_doc = user_ref.get()
            
            if not user_doc.exists:
                logging.error(f"No user document found for UID: {uid}")
                raise HTTPException(status_code=404, detail="User not found")
            
            user_data = user_doc.to_dict()
            logging.info(f"User data retrieved successfully for UID: {uid}")
            
            return {
                "id": uid,
                "email": user_data.get('email'),
                "role": user_data.get('role', 'user')
            }
            
        except auth.InvalidIdTokenError as e:
            logging.error(f"Invalid token error: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")
        except auth.ExpiredIdTokenError as e:
            logging.error(f"Token expired error: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Token has expired: {str(e)}")
        except auth.RevokedIdTokenError as e:
            logging.error(f"Token revoked error: {str(e)}")
            raise HTTPException(status_code=401, detail=f"Token has been revoked: {str(e)}")
        except Exception as e:
            logging.error(f"Unexpected error during token verification: {str(e)}", exc_info=True)
            raise HTTPException(status_code=401, detail=str(e))
            
    except Exception as e:
        logging.error(f"Error in get_current_user: {str(e)}", exc_info=True)
        raise HTTPException(status_code=401, detail="Could not authenticate user") 