[pytest]
pythonpath = .
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --cov=app --cov-report=term-missing
markers =
    asyncio: mark test as an async test
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
log_cli = true
log_cli_level = INFO
log_cli_format = %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning 
