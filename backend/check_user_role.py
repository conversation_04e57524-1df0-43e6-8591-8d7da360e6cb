from app.services.roles_service import RolesService
import asyncio

async def check_user_role():
    roles_service = RolesService()
    
    # Get the user ID from the role data
    role = await roles_service.get_role_by_id('dtEfkOauSNHobSAf8Yw1')
    if not role:
        print("Role not found globally")
        return
    
    user_id = role.get('userId') or role.get('user_id')
    if not user_id:
        print("User ID not found in role data")
        return
    
    print(f"Checking role for user ID: {user_id}")
    
    # Try to get the role from the user's collection
    user_role = await roles_service.get_role('dtEfkOauSNHobSAf8Yw1', user_id)
    print(f"Role exists in user collection: {user_role is not None}")
    
    # Check if the role exists in the global roles collection
    db = roles_service.db
    role_ref = db.collection('roles').document('dtEfkOauSNHobSAf8Yw1')
    role_doc = role_ref.get()
    print(f"Role exists in global collection: {role_doc.exists}")

if __name__ == "__main__":
    asyncio.run(check_user_role())
