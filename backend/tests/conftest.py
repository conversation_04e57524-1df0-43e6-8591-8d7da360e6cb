import os
import pytest
from pathlib import Path
from httpx import Async<PERSON>lient
from dotenv import load_dotenv
from fastapi import FastAPI
from typing import AsyncGenerator, Generator
from fastapi.testclient import TestClient
from httpx import ASGITransport
from unittest.mock import Mock
import firebase_admin
from firebase_admin import credentials

# Load test environment variables
@pytest.fixture(autouse=True)
def load_test_env():
    """Load test environment variables."""
    test_env_path = os.path.join(os.path.dirname(__file__), '.env.test')
    load_dotenv(test_env_path, override=True)

from app.main import app
from app.services.firebase_service import FirebaseService

@pytest.fixture
async def client() -> AsyncClient:
    """Create a test client for the FastAPI application."""
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as client:
        yield client

@pytest.fixture
def test_app():
    """Create a test instance of the application."""
    return app

@pytest.fixture
def mock_firebase_service(mocker):
    """Create a mock FirebaseService instance for testing."""
    mock = mocker.Mock(spec=FirebaseService)
    return mock

@pytest.fixture(autouse=True)
def setup_test_environment():
    """Set up test environment variables."""
    # Store original environment variables
    original_env = dict(os.environ)
    
    # Set test environment variables
    os.environ["ENVIRONMENT"] = "test"
    
    yield
    
    # Restore original environment variables
    os.environ.clear()
    os.environ.update(original_env)

# Mock Firebase initialization
@pytest.fixture(autouse=True)
def mock_firebase_init(monkeypatch):
    """Mock Firebase initialization for testing."""
    def mock_init(*args, **kwargs):
        return None
    
    def mock_get_app(*args, **kwargs):
        return Mock()
    
    def mock_client(*args, **kwargs):
        return Mock()
    
    monkeypatch.setattr(credentials, "Certificate", mock_init)
    monkeypatch.setattr(firebase_admin, "initialize_app", mock_init)
    monkeypatch.setattr(firebase_admin, "get_app", mock_get_app)
    monkeypatch.setattr("firebase_admin.firestore.client", mock_client)

@pytest.fixture(autouse=True)
def firebase_service():
    """Create a test instance of FirebaseService."""
    return FirebaseService(test_mode=True)

@pytest.fixture
def test_client(test_app):
    """Create a test client for the application."""
    return TestClient(test_app) 
