import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_client_fixture(client: AsyncClient):
    """Test that the client fixture is working correctly."""
    response = await client.get("/api/v1/health")  # Assuming you have a health check endpoint
    assert response is not None
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}
    print(f"Client test response: {response}") 