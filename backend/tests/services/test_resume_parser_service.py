# File: backend/tests/services/test_resume_parser_service.py

import pytest
import io
import os
from unittest.mock import patch, <PERSON><PERSON>, Async<PERSON>ock, MagicMock
from fastapi import UploadFile
import PyPDF2
from docx import Document
from docx.shared import Pt

from app.services.resume_parser_service import ResumeParserService, FileType

# Test fixtures
@pytest.fixture
def resume_parser_service():
    """Return an instance of the ResumeParserService."""
    return ResumeParserService()

@pytest.fixture
def mock_pdf_file():
    """Create a mock PDF file for testing."""
    # Create a mock UploadFile
    mock_file = AsyncMock()
    mock_file.filename = "test_resume.pdf"
    mock_file.content_type = "application/pdf"
    
    # Set up the file read method to return some binary content
    sample_content = b"Sample PDF content"
    mock_file.read = AsyncMock(return_value=sample_content)
    mock_file.seek = AsyncMock()
    
    return mock_file

@pytest.fixture
def mock_docx_file():
    """Create a mock DOCX file for testing."""
    # Create a mock UploadFile
    mock_file = AsyncMock()
    mock_file.filename = "test_resume.docx"
    mock_file.content_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    
    # Set up the file read method to return some binary content
    sample_content = b"Sample DOCX content"
    mock_file.read = AsyncMock(return_value=sample_content)
    mock_file.seek = AsyncMock()
    
    return mock_file

@pytest.fixture
def mock_text_file():
    """Create a mock text file for testing."""
    # Create a mock UploadFile
    mock_file = AsyncMock()
    mock_file.filename = "test_resume.txt"
    mock_file.content_type = "text/plain"
    
    # Set up the file read method to return some UTF-8 text
    sample_content = "Sample resume text content".encode('utf-8')
    mock_file.read = AsyncMock(return_value=sample_content)
    mock_file.seek = AsyncMock()
    
    return mock_file

@pytest.fixture
def mock_unsupported_file():
    """Create a mock unsupported file for testing."""
    # Create a mock UploadFile
    mock_file = AsyncMock()
    mock_file.filename = "test_resume.jpg"
    mock_file.content_type = "image/jpeg"
    
    # Set up the file read method to return some binary content
    sample_content = b"Sample JPEG content"
    mock_file.read = AsyncMock(return_value=sample_content)
    mock_file.seek = AsyncMock()
    
    return mock_file

@pytest.fixture
def mock_corrupted_pdf_file():
    """Create a mock corrupted PDF file for testing."""
    # Create a mock UploadFile
    mock_file = AsyncMock()
    mock_file.filename = "corrupted_resume.pdf"
    mock_file.content_type = "application/pdf"
    
    # Set up the file read method to return content that will cause a parsing error
    sample_content = b"This is not a valid PDF file content"
    mock_file.read = AsyncMock(return_value=sample_content)
    mock_file.seek = AsyncMock()
    
    return mock_file

@pytest.fixture
def mock_empty_file():
    """Create a mock empty file for testing."""
    # Create a mock UploadFile
    mock_file = AsyncMock()
    mock_file.filename = "empty_resume.pdf"
    mock_file.content_type = "application/pdf"
    
    # Set up the file read method to return empty content
    mock_file.read = AsyncMock(return_value=b"")
    mock_file.seek = AsyncMock()
    
    return mock_file

# Begin tests
class TestResumeParserService:
    """Tests for the ResumeParserService."""
    
    @pytest.mark.asyncio
    async def test_file_type_detection(self, resume_parser_service):
        """Test file type detection based on content type and extension."""
        # Create mock files with different content types
        pdf_file = Mock(content_type="application/pdf", filename="test.pdf")
        docx_file = Mock(content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document", filename="test.docx")
        doc_file = Mock(content_type="application/msword", filename="test.doc")
        txt_file = Mock(content_type="text/plain", filename="test.txt")
        
        # Test content type detection
        assert resume_parser_service._detect_file_type(pdf_file) == FileType.PDF
        assert resume_parser_service._detect_file_type(docx_file) == FileType.DOCX
        assert resume_parser_service._detect_file_type(doc_file) == FileType.DOC
        assert resume_parser_service._detect_file_type(txt_file) == FileType.TXT
        
        # Test extension-based detection when content type is not recognized
        unknown_file = Mock(content_type="unknown/type", filename="test.pdf")
        assert resume_parser_service._detect_file_type(unknown_file) == FileType.PDF
        
        # Test unknown file type
        unknown_file = Mock(content_type="unknown/type", filename="test.xyz")
        assert resume_parser_service._detect_file_type(unknown_file) == FileType.UNKNOWN
    
    @pytest.mark.asyncio
    async def test_parse_pdf_success(self, resume_parser_service):
        """Test successful PDF parsing."""
        # Create a simple PDF in memory
        pdf_content = b"Sample PDF content"
        
        # Mock PyPDF2.PdfReader to return a controlled result
        with patch("PyPDF2.PdfReader") as mock_pdf_reader:
            # Set up mock page with text
            mock_page = Mock()
            mock_page.extract_text.return_value = "Resume content from PDF"
            
            # Set up mock PDF reader
            mock_reader_instance = Mock()
            mock_reader_instance.pages = [mock_page]
            mock_pdf_reader.return_value = mock_reader_instance
            
            # Call the method and verify result
            result = await resume_parser_service._parse_pdf(pdf_content)
            assert result == "Resume content from PDF"
            
            # Verify the reader was created with BytesIO containing our content
            mock_pdf_reader.assert_called_once()
            args, _ = mock_pdf_reader.call_args
            assert isinstance(args[0], io.BytesIO)
    
    @pytest.mark.asyncio
    async def test_parse_pdf_error(self, resume_parser_service):
        """Test PDF parsing with error handling."""
        # Create invalid PDF content
        invalid_content = b"This is not a valid PDF"
        
        # Mock PyPDF2.PdfReader to raise an exception
        with patch("PyPDF2.PdfReader") as mock_pdf_reader:
            mock_pdf_reader.side_effect = PyPDF2.errors.PdfReadError("Invalid PDF file")
            
            # Verify exception is caught and converted
            with pytest.raises(ValueError, match="Failed to parse PDF: Invalid PDF file"):
                await resume_parser_service._parse_pdf(invalid_content)
    
    @pytest.mark.asyncio
    async def test_parse_docx_success(self, resume_parser_service):
        """Test successful DOCX parsing."""
        docx_content = b"Sample DOCX content"
        
        # Mock the docx.Document to return a controlled result
        with patch("docx.Document") as mock_document:
            # Create mock paragraphs
            mock_paragraph1 = Mock()
            mock_paragraph1.text = "Resume paragraph 1"
            mock_paragraph2 = Mock()
            mock_paragraph2.text = "Resume paragraph 2"
            
            # Create mock table cells
            mock_cell_para = Mock()
            mock_cell_para.text = "Table cell content"
            mock_cell = Mock()
            mock_cell.paragraphs = [mock_cell_para]
            mock_row = Mock()
            mock_row.cells = [mock_cell]
            mock_table = Mock()
            mock_table.rows = [mock_row]
            
            # Set up mock document
            mock_doc_instance = Mock()
            mock_doc_instance.paragraphs = [mock_paragraph1, mock_paragraph2]
            mock_doc_instance.tables = [mock_table]
            mock_document.return_value = mock_doc_instance
            
            # Call the method and verify result
            result = await resume_parser_service._parse_docx(docx_content)
            expected = "Resume paragraph 1\nResume paragraph 2\nTable cell content"
            assert result == expected
            
            # Verify document was created with BytesIO containing our content
            mock_document.assert_called_once()
            args, _ = mock_document.call_args
            assert isinstance(args[0], io.BytesIO)
    
    @pytest.mark.asyncio
    async def test_parse_docx_error(self, resume_parser_service):
        """Test DOCX parsing with error handling."""
        # Create invalid DOCX content
        invalid_content = b"This is not a valid DOCX"
        
        # Mock docx.Document to raise an exception
        with patch("docx.Document") as mock_document:
            mock_document.side_effect = Exception("Invalid DOCX file")
            
            # Verify exception is caught and converted
            with pytest.raises(ValueError, match="Failed to parse DOCX: Invalid DOCX file"):
                await resume_parser_service._parse_docx(invalid_content)
    
    @pytest.mark.asyncio
    async def test_parse_text_success(self, resume_parser_service):
        """Test successful text file parsing."""
        # Create UTF-8 encoded text
        text_content = "Sample resume text content".encode('utf-8')
        
        # Parse the text content
        result = await resume_parser_service._parse_text(text_content)
        assert result == "Sample resume text content"
        
        # Test with non-UTF8 encoding
        latin1_content = "Résumé with accents".encode('latin-1')
        with patch("io.TextIOWrapper") as mock_wrapper:
            # First attempt (UTF-8) fails, second attempt (latin-1) succeeds
            result = await resume_parser_service._parse_text(latin1_content)
            # The result will be decoded using fallback latin-1
            assert "Résumé" in result or "R" in result
    
    @pytest.mark.asyncio
    async def test_parse_text_error(self, resume_parser_service):
        """Test text parsing with decoding error handling."""
        # Create binary data that can't be decoded
        binary_content = bytes([0xFF, 0xFE, 0x00, 0x00])  # Invalid UTF-8 sequence
        
        # Mock the decode methods to simulate both UTF-8 and latin-1 failing
        with patch.object(bytes, 'decode') as mock_decode:
            mock_decode.side_effect = UnicodeDecodeError('utf-8', binary_content, 0, 1, 'invalid start byte')
            
            # Verify exception is caught and converted
            with pytest.raises(ValueError, match="Failed to decode text file"):
                await resume_parser_service._parse_text(binary_content)
    
    @pytest.mark.asyncio
    async def test_parse_resume_pdf(self, resume_parser_service, mock_pdf_file):
        """Test parsing a PDF resume."""
        # Mock the _parse_pdf method to return a controlled result
        with patch.object(resume_parser_service, '_parse_pdf', new_callable=AsyncMock) as mock_parse_pdf:
            mock_parse_pdf.return_value = "Parsed PDF content"
            
            # Call parse_resume and verify result
            result = await resume_parser_service.parse_resume(mock_pdf_file)
            
            # Verify the result structure
            assert result["text"] == "Parsed PDF content"
            assert result["metadata"]["filename"] == "test_resume.pdf"
            assert result["metadata"]["file_type"] == "pdf"
            assert result["metadata"]["content_type"] == "application/pdf"
            
            # Verify the correct parser was called
            mock_parse_pdf.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_parse_resume_docx(self, resume_parser_service, mock_docx_file):
        """Test parsing a DOCX resume."""
        # Mock the _parse_docx method to return a controlled result
        with patch.object(resume_parser_service, '_parse_docx', new_callable=AsyncMock) as mock_parse_docx:
            mock_parse_docx.return_value = "Parsed DOCX content"
            
            # Call parse_resume and verify result
            result = await resume_parser_service.parse_resume(mock_docx_file)
            
            # Verify the result structure
            assert result["text"] == "Parsed DOCX content"
            assert result["metadata"]["filename"] == "test_resume.docx"
            assert result["metadata"]["file_type"] == "docx"
            assert result["metadata"]["content_type"] == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            
            # Verify the correct parser was called
            mock_parse_docx.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_parse_resume_text(self, resume_parser_service, mock_text_file):
        """Test parsing a text resume."""
        # Mock the _parse_text method to return a controlled result
        with patch.object(resume_parser_service, '_parse_text', new_callable=AsyncMock) as mock_parse_text:
            mock_parse_text.return_value = "Parsed text content"
            
            # Call parse_resume and verify result
            result = await resume_parser_service.parse_resume(mock_text_file)
            
            # Verify the result structure
            assert result["text"] == "Parsed text content"
            assert result["metadata"]["filename"] == "test_resume.txt"
            assert result["metadata"]["file_type"] == "txt"
            assert result["metadata"]["content_type"] == "text/plain"
            
            # Verify the correct parser was called
            mock_parse_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_parse_resume_unsupported(self, resume_parser_service, mock_unsupported_file):
        """Test parsing an unsupported file type."""
        # Call parse_resume and expect ValueError for unsupported file
        with pytest.raises(ValueError, match="Unsupported file type"):
            await resume_parser_service.parse_resume(mock_unsupported_file)
    
    @pytest.mark.asyncio
    async def test_parse_resume_corrupted(self, resume_parser_service, mock_corrupted_pdf_file):
        """Test parsing a corrupted PDF file."""
        # Mock the _parse_pdf method to raise a ValueError
        with patch.object(resume_parser_service, '_parse_pdf', new_callable=AsyncMock) as mock_parse_pdf:
            mock_parse_pdf.side_effect = ValueError("Failed to parse PDF: Corrupted file")
            
            # Call parse_resume and expect ValueError
            with pytest.raises(ValueError, match="Failed to parse PDF: Corrupted file"):
                await resume_parser_service.parse_resume(mock_corrupted_pdf_file)
    
    @pytest.mark.asyncio
    async def test_parse_resume_empty(self, resume_parser_service, mock_empty_file):
        """Test parsing an empty file."""
        # Call parse_resume and expect ValueError for empty file
        with pytest.raises(ValueError, match="Empty file provided"):
            await resume_parser_service.parse_resume(mock_empty_file)
    
    @pytest.mark.asyncio
    async def test_singleton_pattern(self):
        """Test that the service follows the singleton pattern."""
        # Create two instances and verify they are the same object
        service1 = ResumeParserService()
        service2 = ResumeParserService()
        assert service1 is service2 