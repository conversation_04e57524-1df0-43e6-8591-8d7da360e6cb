"""
Integration tests for the interview agent.
"""

import pytest
from unittest.mock import patch, AsyncMock
from app.services.realtime.agent_loader import <PERSON><PERSON><PERSON><PERSON>

@pytest.mark.asyncio
async def test_interview_agent_loading():
    """Test that the interview agent can be loaded."""
    # Initialize the agent loader
    agent_loader = AgentLoader(agent_type="interview_agent")
    
    # Load the prompt
    prompt = agent_loader.get_prompt("interview_agent")
    assert prompt is not None
    assert "You are <PERSON><PERSON><PERSON><PERSON>, an AI interviewer" in prompt
    
    # Load the functions
    functions_module = agent_loader.get_functions()
    functions = functions_module.realtime_functions
    assert functions is not None
    assert len(functions) == 2
    
    # Verify function names
    function_names = [f["name"] for f in functions]
    assert "return_interview_results" in function_names
    assert "end_the_interview" in function_names
    
    # Load the services
    services = agent_loader.get_services()
    assert services is not None
    assert hasattr(services, "get_interview_context")
    assert hasattr(services, "process_interview_results")
    assert hasattr(services, "save_interview_results")

@pytest.mark.asyncio
async def test_interview_context_building():
    """Test building interview context with mock data."""
    # Mock data
    role_id = "test-role-id"
    template_id = "test-template-id"
    user_id = "test-user-id"
    
    # Mock role and template data
    mock_role = {
        "title": "Software Engineer",
        "level": "Senior",
        "summary": "This is a test role",
        "requiredSkills": {"1": "Python", "2": "JavaScript"},
        "preferredSkills": {"1": "React", "2": "AWS"},
        "yearsOfExperience": "5+ years",
        "interviewProcess": [
            {
                "stage": "Technical Challenge with Code",
                "duration": "45 minutes",
                "customInstructions": "Focus on problem-solving abilities"
            }
        ]
    }
    
    mock_template = {
        "stageIndex": 0,
        "stage": "Technical Challenge with Code",
        "duration": "45 minutes",
        "customInstructions": "Focus on problem-solving abilities",
        "questions": [
            {
                "id": "q1",
                "question": "Tell me about your experience with Python",
                "purpose": "To assess technical knowledge",
                "idealAnswerCriteria": "Should mention specific projects"
            }
        ],
        "evaluationCriteria": [
            {
                "id": "c1",
                "type": "ScoreCard",
                "criteria": "Technical Knowledge",
                "description": "Understanding of technical concepts",
                "competency": "Technical Skills",
                "weight": 5
            }
        ]
    }
    
    # Initialize the agent loader
    agent_loader = AgentLoader(agent_type="interview_agent")
    services = agent_loader.get_services()
    
    # Mock the services
    with patch("app.services.realtime.agents.interview_agent.services.roles_service") as mock_roles_service, \
         patch("app.services.realtime.agents.interview_agent.services.templates_service") as mock_templates_service:
        
        # Set up the mocks
        mock_roles_service.get_role = AsyncMock(return_value=mock_role)
        mock_templates_service.get_template = AsyncMock(return_value=mock_template)
        
        # Build the context
        context = await services.get_interview_context(role_id, template_id, user_id)
        
        # Verify the context
        assert context["role_title"] == "Software Engineer"
        assert context["role_level"] == "Senior"
        assert context["required_skills"] == "Python, JavaScript"
        assert context["preferred_skills"] == "React, AWS"
        assert context["years_of_experience"] == "5+ years"
        assert context["interview_stage"] == "Technical Challenge with Code"
        assert context["interview_duration"] == "45 minutes"
        assert context["custom_instructions"] == "Focus on problem-solving abilities"
        assert "interview_questions" in context
        assert "evaluation_criteria" in context 