"""Integration tests for the email listener service."""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock
import logging
from typing import AsyncGenerator, List, Dict, Any

from app.services.email_service import EmailService
from app.core.config import settings

# Test configuration
TEST_EMAIL = settings.TEST_EMAIL_ADDRESS
TEST_PASSWORD = settings.TEST_EMAIL_PASSWORD
TEST_RECIPIENT = settings.EMAIL_USERNAME

if not TEST_PASSWORD:
    raise ValueError("TEST_EMAIL_PASSWORD not set in environment variables")

@pytest.fixture(scope="module")
def event_loop():
    """Create an event loop for the test module."""
    loop = asyncio.get_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="module")
async def email_service() -> AsyncGenerator[EmailService, None]:
    """Create and initialize the email service."""
    service = EmailService()
    yield service

@pytest.fixture
def mock_smtp():
    """Mock SMTP server."""
    with patch('smtplib.SMTP') as mock:
        server = Mock()
        mock.return_value.__enter__.return_value = server
        yield server

@pytest.fixture
def mock_imap():
    """Mock IMAP server."""
    with patch('imaplib.IMAP4_SSL') as mock:
        server = Mock()
        mock.return_value = server
        
        # Mock email fetch response
        server.search.return_value = ('OK', [b'1 2 3'])
        server.fetch.return_value = ('OK', [(b'1', b'EMAIL_DATA')])
        
        yield server

@pytest.fixture
def mock_firebase_service():
    """Mock Firebase service."""
    with patch('app.services.firebase_service.FirebaseService', autospec=True) as mock:
        service = Mock()
        mock.return_value = service
        service.get_user_by_email = AsyncMock(return_value={"email": "<EMAIL>"})
        yield service

@pytest.fixture
def mock_ai_service():
    """Mock AI service."""
    with patch('app.services.ai_service.AIService', autospec=True) as mock:
        service = Mock()
        mock.return_value = service
        service.process_email = AsyncMock()
        
        # Mock singleton pattern
        mock._instance = None
        mock.__new__ = Mock(return_value=service)
        
        yield service

@pytest.fixture(autouse=True)
def mock_services(mock_smtp, mock_imap, mock_firebase_service, mock_ai_service):
    """Automatically mock all services for each test."""
    with patch('app.services.email_service.FirebaseService', return_value=mock_firebase_service), \
         patch('app.services.email_service.AIService', return_value=mock_ai_service):
        yield

async def simulate_incoming_email(email_data: Dict[str, Any]) -> None:
    """Simulate an incoming email."""
    # This will be used instead of actually sending emails
    pass

@pytest.mark.asyncio
async def test_email_listener_role_creation(email_service, mock_ai_service):
    """Test the email listener with a role creation request."""
    # Prepare test data
    test_email_data = {
        'subject': 'New Role Request: Senior Python Developer',
        'from': '<EMAIL>',
        'to': settings.EMAIL_USERNAME,
        'date': datetime.now().isoformat(),
        'body': """
        Hi Recruiva,

        We need to open a new position for a Senior Python Developer.

        Requirements:
        - 5+ years of Python experience
        - Strong background in Django/FastAPI
        - Experience with AWS
        - Team leadership experience

        Department: Engineering
        Location: Hybrid (New York)
        Employment: Full-time

        Please help us create this role.

        Best regards,
        Test Manager
        """
    }

    # Mock AI service response
    mock_ai_service.process_email.return_value = {
        'action': 'create_role',
        'role_details': {
            'title': 'Senior Python Developer',
            'department': 'Engineering',
            'location': {
                'type': 'hybrid',
                'primary_location': 'New York'
            }
        }
    }

    # Process the email
    await email_service._handle_email(test_email_data)
    
    # Verify processing
    mock_ai_service.process_email.assert_called_once()
    args = mock_ai_service.process_email.call_args
    assert test_email_data['body'] in args[1]['content']

@pytest.mark.asyncio
async def test_email_listener_invalid_sender(email_service, mock_firebase_service):
    """Test the email listener with an unauthorized sender."""
    # Setup unauthorized sender
    mock_firebase_service.get_user_by_email.return_value = None
    
    test_email_data = {
        'subject': 'Unauthorized Request',
        'from': '<EMAIL>',
        'to': settings.EMAIL_USERNAME,
        'date': datetime.now().isoformat(),
        'body': 'This should be rejected'
    }
    
    # Process the email
    await email_service._validate_sender(test_email_data['from'])
    
    # Verify the email was rejected
    mock_firebase_service.get_user_by_email.assert_called_with('<EMAIL>')

@pytest.mark.asyncio
async def test_email_listener_unclear_request(email_service, mock_ai_service):
    """Test the email listener with an unclear role request."""
    test_email_data = {
        'subject': 'New Role',
        'from': '<EMAIL>',
        'to': settings.EMAIL_USERNAME,
        'date': datetime.now().isoformat(),
        'body': 'We need to hire someone for our team.'
    }
    
    # Mock AI service to request clarification
    mock_ai_service.process_email.return_value = {
        'action': 'request_clarification',
        'clarification_points': ['Role title', 'Department'],
        'context': 'Need more details about the role'
    }
    
    # Process the email
    await email_service._handle_email(test_email_data)
    
    # Verify clarification was requested
    mock_ai_service.process_email.assert_called_once()

@pytest.mark.asyncio
async def test_email_listener_with_attachments(email_service):
    """Test the email listener with attachments."""
    # This would test attachment handling
    # Implement when attachment handling is needed
    pass

@pytest.mark.asyncio
async def test_email_listener_concurrent_requests(email_service, mock_ai_service):
    """Test the email listener with concurrent requests."""
    test_emails = [
        {
            'subject': f'Role Request {i}',
            'from': '<EMAIL>',
            'to': settings.EMAIL_USERNAME,
            'date': datetime.now().isoformat(),
            'body': f'Test role request {i}'
        }
        for i in range(3)
    ]
    
    # Process emails concurrently
    await asyncio.gather(
        *[email_service._handle_email(email) for email in test_emails]
    )
    
    # Verify all emails were processed
    assert mock_ai_service.process_email.call_count == len(test_emails)

@pytest.mark.asyncio
async def test_email_listener_error_handling(email_service):
    """Test error handling in the email listener."""
    # Send a malformed email or trigger error conditions
    test_email_data = {
        'subject': 'Malformed Request',
        'from': '<EMAIL>',
        'to': settings.EMAIL_USERNAME,
        'date': datetime.now().isoformat(),
        'body': '{invalid json}'
    }
    
    # Process the email
    await email_service._handle_email(test_email_data)
    
    # Verify error was handled gracefully
    # The test should complete without raising exceptions

# Helper function to run the tests
async def run_integration_tests():
    """Run all integration tests in sequence."""
    email_service = EmailService()
    
    # Start the email listener
    listener_task = asyncio.create_task(
        email_service.start_email_listener()
    )
    
    try:
        # Run the tests
        await test_email_listener_role_creation(email_service)
        await test_email_listener_invalid_sender(email_service)
        await test_email_listener_unclear_request(email_service)
        await test_email_listener_concurrent_requests(email_service)
        await test_email_listener_error_handling(email_service)
        
    finally:
        # Clean up
        listener_task.cancel()
        try:
            await listener_task
        except asyncio.CancelledError:
            pass

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the tests
    asyncio.run(run_integration_tests()) 