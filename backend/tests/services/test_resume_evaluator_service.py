# File: backend/tests/services/test_resume_evaluator_service.py

import pytest
import json
from unittest.mock import patch, As<PERSON><PERSON><PERSON>, <PERSON>Mock, Mock
from datetime import datetime

from app.services.resume_evaluator_service import ResumeEvaluatorService, ResumeEvaluation

# Sample data for tests
SAMPLE_RESUME_TEXT = """
John Doe
123 Main St, Anytown, USA
<EMAIL>
(123) 456-7890

SUMMARY
Experienced software engineer with 5 years of experience in full-stack development.

SKILLS
- Programming: JavaScript, TypeScript, Python, Java
- Frameworks: React, Node.js, Express, Django
- Databases: MongoDB, PostgreSQL
- Tools: Git, Docker, AWS, CI/CD

EXPERIENCE
Senior Software Engineer, ABC Tech (2020-Present)
- Developed and maintained RESTful APIs using Node.js and Express
- Implemented front-end features with React and TypeScript
- Optimized database queries resulting in 30% performance improvement

Software Engineer, XYZ Inc. (2018-2020)
- Built responsive web applications using React and Redux
- Collaborated with cross-functional teams to deliver high-quality software

EDUCATION
Bachelor of Science in Computer Science, State University (2014-2018)
"""

SAMPLE_JOB_POSTING = """
Senior Full-Stack Developer

We are seeking an experienced Senior Full-Stack Developer to join our team.

Required Skills:
- 5+ years of experience in software development
- Expert knowledge of JavaScript, TypeScript, and React
- Experience with Node.js and Express
- Strong understanding of RESTful APIs
- Experience with relational databases (PostgreSQL preferred)
- Familiarity with CI/CD pipelines

Responsibilities:
- Develop and maintain web applications
- Write clean, maintainable, and efficient code
- Collaborate with cross-functional teams
- Mentor junior developers

Education:
- Bachelor's degree in Computer Science or related field
"""

SAMPLE_EVALUATION_RESPONSE = {
    "choices": [
        {
            "message": {
                "function_call": {
                    "name": "evaluate_resume",
                    "arguments": json.dumps({
                        "scorecard": {
                            "technicalSkills": {
                                "score": 4,
                                "evaluation": "The candidate has strong technical skills that match the job requirements.",
                                "strengths": ["JavaScript", "TypeScript", "React", "Node.js", "Express"],
                                "gaps": ["No explicit mention of CI/CD experience"]
                            },
                            "softSkills": {
                                "score": 3,
                                "evaluation": "The resume demonstrates some soft skills through collaboration.",
                                "strengths": ["Collaboration with cross-functional teams"],
                                "gaps": ["Limited evidence of leadership", "No explicit mention of mentoring"]
                            },
                            "experienceRelevance": {
                                "score": 4,
                                "evaluation": "The candidate's experience is highly relevant to the role.",
                                "strengths": ["Full-stack development experience", "RESTful API development"],
                                "gaps": ["Limited evidence of mentoring junior developers"]
                            },
                            "educationCertifications": {
                                "score": 3,
                                "evaluation": "The candidate meets the educational requirements.",
                                "strengths": ["Bachelor's degree in Computer Science"],
                                "gaps": ["No additional certifications mentioned"]
                            },
                            "achievementsImpact": {
                                "score": 3,
                                "evaluation": "The candidate has demonstrated some measurable achievements.",
                                "strengths": ["30% performance improvement through database optimization"],
                                "gaps": ["Limited quantifiable achievements"]
                            },
                            "overallScore": 3.5,
                            "overallEvaluation": "Overall, the candidate appears to be a good fit for the role."
                        },
                        "recommendation": {
                            "decision": "PASS",
                            "confidence": "MEDIUM",
                            "reasoning": "The candidate meets most of the requirements for the role."
                        },
                        "feedback": {
                            "candidateStrengths": ["Strong technical skills", "Relevant experience"],
                            "improvementAreas": ["Highlight leadership experience", "Add certifications"],
                            "interviewFocus": ["CI/CD experience", "Mentoring capabilities"]
                        }
                    })
                }
            }
        }
    ]
}

SAMPLE_BASIC_EVALUATION_RESPONSE = {
    "choices": [
        {
            "message": {
                "function_call": {
                    "name": "evaluate_resume_basic",
                    "arguments": json.dumps({
                        "overallScore": 3.5,
                        "decision": "PASS",
                        "confidence": "MEDIUM",
                        "reasoning": "The candidate meets most of the requirements for the role.",
                        "keyStrengths": ["JavaScript/TypeScript experience", "React and Node.js skills"],
                        "keyGaps": ["Limited CI/CD experience", "No mentoring evidence"]
                    })
                }
            }
        }
    ]
}

@pytest.fixture
def resume_evaluator_service():
    """Return an instance of the ResumeEvaluatorService."""
    return ResumeEvaluatorService()

class TestResumeEvaluatorService:
    """Tests for the ResumeEvaluatorService."""
    
    @pytest.mark.asyncio
    async def test_evaluate_resume_success(self, resume_evaluator_service):
        """Test successful resume evaluation."""
        # Mock ChatCompletionService.generate_completion
        with patch('app.services.openai.chat_completion_service.ChatCompletionService.generate_completion', new_callable=AsyncMock) as mock_generate:
            # Set up the mock to return our sample response
            mock_generate.return_value = SAMPLE_EVALUATION_RESPONSE
            
            # Call the method
            result = await resume_evaluator_service.evaluate_resume(
                SAMPLE_RESUME_TEXT,
                SAMPLE_JOB_POSTING
            )
            
            # Check that the mock was called with the expected arguments
            mock_generate.assert_called_once()
            call_args = mock_generate.call_args[1]
            assert call_args["use_case"] == "resume_evaluator_agent"
            assert call_args["context"]["resume_text"] == SAMPLE_RESUME_TEXT
            assert call_args["context"]["job_posting"] == SAMPLE_JOB_POSTING
            
            # Verify the result structure
            assert "scorecard" in result
            assert "recommendation" in result
            assert "feedback" in result
            assert "metadata" in result
            
            # Check specific values
            assert result["recommendation"]["decision"] == "PASS"
            assert result["scorecard"]["overallScore"] == 3.5
    
    @pytest.mark.asyncio
    async def test_evaluate_resume_validation_error(self, resume_evaluator_service):
        """Test handling of validation errors in resume evaluation."""
        # Create a response with invalid data structure
        invalid_response = {
            "choices": [
                {
                    "message": {
                        "function_call": {
                            "name": "evaluate_resume",
                            "arguments": json.dumps({
                                "scorecard": {
                                    # Missing required fields
                                    "technicalSkills": {
                                        "score": 4,
                                        # Missing "evaluation" field
                                        "strengths": ["JavaScript", "TypeScript"],
                                        "gaps": ["CI/CD experience"]
                                    },
                                    # Missing other required fields
                                },
                                "recommendation": {
                                    "decision": "PASS",
                                    "confidence": "MEDIUM",
                                    "reasoning": "The candidate meets requirements."
                                },
                                # Missing "feedback" field
                            })
                        }
                    }
                }
            ]
        }
        
        # Mock ChatCompletionService.generate_completion
        with patch('app.services.openai.chat_completion_service.ChatCompletionService.generate_completion', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = invalid_response
            
            # Verify that validation error is raised
            with pytest.raises(ValueError, match="Invalid evaluation data structure"):
                await resume_evaluator_service.evaluate_resume(
                    SAMPLE_RESUME_TEXT,
                    SAMPLE_JOB_POSTING
                )
    
    @pytest.mark.asyncio
    async def test_evaluate_resume_api_error(self, resume_evaluator_service):
        """Test handling of API errors in resume evaluation."""
        # Mock ChatCompletionService.generate_completion to raise an exception
        with patch('app.services.openai.chat_completion_service.ChatCompletionService.generate_completion', new_callable=AsyncMock) as mock_generate:
            mock_generate.side_effect = Exception("API error")
            
            # Verify that the error is caught and converted to ValueError
            with pytest.raises(ValueError, match="Resume evaluation failed: API error"):
                await resume_evaluator_service.evaluate_resume(
                    SAMPLE_RESUME_TEXT,
                    SAMPLE_JOB_POSTING
                )
    
    @pytest.mark.asyncio
    async def test_evaluate_resume_wrong_function_call(self, resume_evaluator_service):
        """Test handling of wrong function call name in response."""
        # Create a response with wrong function call name
        wrong_function_response = {
            "choices": [
                {
                    "message": {
                        "function_call": {
                            "name": "wrong_function",
                            "arguments": "{}"
                        }
                    }
                }
            ]
        }
        
        # Mock ChatCompletionService.generate_completion
        with patch('app.services.openai.chat_completion_service.ChatCompletionService.generate_completion', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = wrong_function_response
            
            # Verify that error is raised
            with pytest.raises(ValueError, match="Unexpected function call: wrong_function"):
                await resume_evaluator_service.evaluate_resume(
                    SAMPLE_RESUME_TEXT,
                    SAMPLE_JOB_POSTING
                )
    
    @pytest.mark.asyncio
    async def test_evaluate_resume_no_function_call(self, resume_evaluator_service):
        """Test handling of missing function call in response."""
        # Create a response with no function call
        no_function_response = {
            "choices": [
                {
                    "message": {
                        "content": "I'll evaluate this resume for you."
                        # No function_call key
                    }
                }
            ]
        }
        
        # Mock ChatCompletionService.generate_completion
        with patch('app.services.openai.chat_completion_service.ChatCompletionService.generate_completion', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = no_function_response
            
            # Verify that error is raised
            with pytest.raises(ValueError, match="Failed to get evaluation function call from response"):
                await resume_evaluator_service.evaluate_resume(
                    SAMPLE_RESUME_TEXT,
                    SAMPLE_JOB_POSTING
                )
    
    @pytest.mark.asyncio
    async def test_evaluate_resume_basic_success(self, resume_evaluator_service):
        """Test successful basic resume evaluation."""
        # Mock ChatCompletionService.generate_completion
        with patch('app.services.openai.chat_completion_service.ChatCompletionService.generate_completion', new_callable=AsyncMock) as mock_generate:
            # Set up the mock to return our sample response
            mock_generate.return_value = SAMPLE_BASIC_EVALUATION_RESPONSE
            
            # Call the method
            result = await resume_evaluator_service.evaluate_resume_basic(
                SAMPLE_RESUME_TEXT,
                SAMPLE_JOB_POSTING
            )
            
            # Check that the mock was called with the expected arguments
            mock_generate.assert_called_once()
            call_args = mock_generate.call_args[1]
            assert call_args["use_case"] == "resume_evaluator_agent"
            
            # Verify the result structure
            assert "overallScore" in result
            assert "decision" in result
            assert "confidence" in result
            assert "reasoning" in result
            assert "keyStrengths" in result
            assert "keyGaps" in result
            assert "metadata" in result
            
            # Check specific values
            assert result["decision"] == "PASS"
            assert result["overallScore"] == 3.5
            assert result["metadata"]["evaluationType"] == "basic"
    
    @pytest.mark.asyncio
    async def test_singleton_pattern(self):
        """Test that the service follows the singleton pattern."""
        # Create two instances and verify they are the same object
        service1 = ResumeEvaluatorService()
        service2 = ResumeEvaluatorService()
        assert service1 is service2 