"""
Unit tests for the interview agent.
"""

import pytest
import json
from unittest.mock import patch, MagicMock, AsyncMock
from app.services.realtime.agents.interview_agent.services import (
    get_interview_context,
    process_interview_results,
    save_interview_results
)

@pytest.fixture
def mock_role():
    """Mock role data for testing."""
    return {
        "id": "test-role-id",
        "title": "Software Engineer",
        "level": "Senior",
        "summary": "This is a test role summary.",
        "requiredSkills": {
            "1": "Python",
            "2": "JavaScript",
            "3": "SQL"
        },
        "preferredSkills": {
            "1": "React",
            "2": "AWS",
            "3": "Docker"
        },
        "yearsOfExperience": "5+ years",
        "interviewProcess": [
            {
                "stage": "Technical Challenge with Code",
                "duration": "45 minutes",
                "customInstructions": "Focus on problem-solving abilities and technical knowledge."
            }
        ]
    }

@pytest.fixture
def mock_template():
    """Mock template data for testing."""
    return {
        "id": "test-template-id",
        "stageIndex": 0,
        "stage": "Technical Challenge with Code",
        "duration": "45 minutes",
        "customInstructions": "Focus on problem-solving abilities and technical knowledge.",
        "questions": [
            {
                "id": "q1",
                "question": "Tell me about your experience with Python.",
                "purpose": "To assess technical knowledge and experience.",
                "idealAnswerCriteria": "Should mention specific projects, libraries, and problem-solving examples."
            },
            {
                "id": "q2",
                "question": "Describe a challenging project you worked on.",
                "purpose": "To assess problem-solving abilities.",
                "idealAnswerCriteria": "Should describe the challenge, approach, and outcome."
            }
        ],
        "evaluationCriteria": [
            {
                "id": "c1",
                "type": "ScoreCard",
                "criteria": "Technical Knowledge",
                "description": "Understanding of technical concepts and ability to explain them clearly.",
                "competency": "Technical Skills",
                "weight": 5
            },
            {
                "id": "c2",
                "type": "ScoreCard",
                "criteria": "Problem Solving",
                "description": "Ability to analyze problems and develop effective solutions.",
                "competency": "Analytical Skills",
                "weight": 4
            }
        ]
    }

@pytest.fixture
def mock_transcript():
    """Mock transcript data for testing."""
    return {
        "id": "test-transcript-id",
        "templateId": "test-template-id",
        "sessionId": "test-session-id",
        "status": "completed",
        "messages": [
            {
                "role": "assistant",
                "content": "Hello, I'm Recruiva, an AI interviewer. I'll be conducting your technical interview today.",
                "function_calls": []
            },
            {
                "role": "user",
                "content": "Hi, I'm ready for the interview."
            },
            {
                "role": "assistant",
                "content": "Great! Let's start with your experience with Python.",
                "function_calls": []
            },
            {
                "role": "user",
                "content": "I've been using Python for 5 years, primarily for web development with Django and data analysis with pandas."
            },
            {
                "role": "assistant",
                "content": "Thank you for sharing. Now, could you describe a challenging project you worked on?",
                "function_calls": []
            },
            {
                "role": "user",
                "content": "I worked on a real-time analytics dashboard that processed millions of events per day."
            },
            {
                "role": "assistant",
                "content": "Thank you for your responses. I'll now conclude the interview.",
                "function_calls": [
                    {
                        "name": "return_interview_results",
                        "arguments": json.dumps({
                            "candidateResponses": [
                                {
                                    "questionId": "q1",
                                    "question": "Tell me about your experience with Python.",
                                    "response": "I've been using Python for 5 years, primarily for web development with Django and data analysis with pandas.",
                                    "notes": "Candidate has solid experience with Python and mentioned specific frameworks."
                                },
                                {
                                    "questionId": "q2",
                                    "question": "Describe a challenging project you worked on.",
                                    "response": "I worked on a real-time analytics dashboard that processed millions of events per day.",
                                    "notes": "Candidate mentioned a high-scale project but didn't provide many details about challenges or solutions."
                                }
                            ],
                            "evaluationNotes": {
                                "c1": "Candidate demonstrates good technical knowledge of Python and its ecosystem.",
                                "c2": "Candidate mentioned a complex project but didn't elaborate on problem-solving approach."
                            },
                            "overallImpression": "The candidate has strong technical skills but could improve on providing detailed examples.",
                            "recommendedNextSteps": "Consider moving forward to the next interview stage with focus on detailed problem-solving scenarios."
                        })
                    }
                ]
            },
            {
                "role": "assistant",
                "content": "Thank you for your time today. We'll be in touch with next steps.",
                "function_calls": [
                    {
                        "name": "end_the_interview",
                        "arguments": json.dumps({
                            "reason": "Interview completed successfully."
                        })
                    }
                ]
            }
        ]
    }

@pytest.mark.asyncio
async def test_get_interview_context(mock_role, mock_template):
    """Test building interview context from role and template."""
    # Mock the roles and templates services
    with patch("app.services.realtime.agents.interview_agent.services.roles_service") as mock_roles_service, \
         patch("app.services.realtime.agents.interview_agent.services.templates_service") as mock_templates_service:
        
        # Set up the mocks
        mock_roles_service.get_role = AsyncMock(return_value=mock_role)
        mock_templates_service.get_template = AsyncMock(return_value=mock_template)
        
        # Call the function
        context = await get_interview_context("test-role-id", "test-template-id", "test-user-id")
        
        # Verify the calls
        mock_roles_service.get_role.assert_called_once_with("test-role-id", "test-user-id")
        mock_templates_service.get_template.assert_called_once_with("test-role-id", "test-template-id", "test-user-id")
        
        # Verify the context
        assert context["role_title"] == "Software Engineer"
        assert context["role_level"] == "Senior"
        assert context["required_skills"] == "Python, JavaScript, SQL"
        assert context["preferred_skills"] == "React, AWS, Docker"
        assert context["years_of_experience"] == "5+ years"
        assert context["interview_stage"] == "Technical Challenge with Code"
        assert context["interview_duration"] == "45 minutes"
        assert context["custom_instructions"] == "Focus on problem-solving abilities and technical knowledge."
        assert "interview_questions" in context
        assert "evaluation_criteria" in context

@pytest.mark.asyncio
async def test_process_interview_results(mock_transcript):
    """Test processing interview results from a transcript."""
    # Mock the services
    with patch("app.services.realtime.agents.interview_agent.services.get_transcript") as mock_get_transcript, \
         patch("app.services.realtime.agents.interview_agent.services.save_interview_results") as mock_save_results:
        
        # Set up the mocks
        mock_get_transcript.return_value = mock_transcript
        mock_save_results.return_value = {"id": "test-results-id", "status": "success"}
        
        # Call the function
        results = await process_interview_results("test-user-id", "test-role-id", "test-transcript-id")
        
        # Verify the calls
        mock_get_transcript.assert_called_once_with("test-user-id", "test-role-id", "test-transcript-id")
        
        # Verify the results were extracted and saved
        mock_save_results.assert_called_once()
        
        # Get the call arguments
        call_args = mock_save_results.call_args
        
        # Check that the function was called with the correct arguments
        assert call_args[0][0] == "test-user-id"  # First positional argument
        assert call_args[0][1] == "test-role-id"  # Second positional argument
        assert call_args[0][2] == "test-transcript-id"  # Third positional argument
        
        # Verify the extracted results
        results_data = call_args[0][3]  # Fourth positional argument (results)
        assert "candidateResponses" in results_data
        assert len(results_data["candidateResponses"]) == 2
        assert "evaluationNotes" in results_data
        assert "overallImpression" in results_data
        assert "recommendedNextSteps" in results_data

@pytest.mark.asyncio
async def test_process_interview_results_no_function_calls():
    """Test processing interview results when no function calls are found."""
    # Create a transcript with no function calls
    transcript = {
        "id": "test-transcript-id",
        "templateId": "test-template-id",
        "sessionId": "test-session-id",
        "status": "completed",
        "messages": [
            {
                "role": "assistant",
                "content": "Hello, I'm Recruiva, an AI interviewer."
            },
            {
                "role": "user",
                "content": "Hi, I'm ready for the interview."
            },
            {
                "role": "assistant",
                "content": "Great! Let's start with your experience."
            },
            {
                "role": "user",
                "content": "I've been working as a developer for 5 years."
            }
        ]
    }
    
    # Mock the services
    with patch("app.services.realtime.agents.interview_agent.services.get_transcript") as mock_get_transcript, \
         patch("app.services.realtime.agents.interview_agent.services.save_interview_results") as mock_save_results:
        
        # Set up the mocks
        mock_get_transcript.return_value = transcript
        mock_save_results.return_value = {"id": "test-results-id", "status": "success"}
        
        # Call the function
        results = await process_interview_results("test-user-id", "test-role-id", "test-transcript-id")
        
        # Verify the calls
        mock_get_transcript.assert_called_once_with("test-user-id", "test-role-id", "test-transcript-id")
        
        # Verify default results were created and saved
        mock_save_results.assert_called_once()
        
        # Get the call arguments
        call_args = mock_save_results.call_args
        
        # Check that the function was called with the correct arguments
        assert call_args[0][0] == "test-user-id"  # First positional argument
        assert call_args[0][1] == "test-role-id"  # Second positional argument
        assert call_args[0][2] == "test-transcript-id"  # Third positional argument
        
        # Verify the default results
        results_data = call_args[0][3]  # Fourth positional argument (results)
        assert "candidateResponses" in results_data
        assert "evaluationNotes" in results_data
        assert "overallImpression" in results_data
        assert results_data["overallImpression"] == "No formal evaluation was completed."
        assert "recommendedNextSteps" in results_data

@pytest.mark.asyncio
async def test_save_interview_results():
    """Test saving interview results."""
    # Mock results data
    results = {
        "candidateResponses": [
            {
                "questionId": "q1",
                "question": "Test question",
                "response": "Test response",
                "notes": "Test notes"
            }
        ],
        "evaluationNotes": {
            "c1": "Test evaluation"
        },
        "overallImpression": "Test impression",
        "recommendedNextSteps": "Test next steps"
    }
    
    # Mock the Firebase service
    with patch("app.services.realtime.agents.interview_agent.services.firebase_service") as mock_firebase:
        # Set up the mocks
        mock_firebase.save_interview_results = AsyncMock(return_value={"id": "test-results-id"})
        
        # Call the function
        saved_results = await save_interview_results("test-user-id", "test-role-id", "test-transcript-id", results)
        
        # Verify the calls
        mock_firebase.save_interview_results.assert_called_once_with(
            user_id="test-user-id",
            role_id="test-role-id",
            transcript_id="test-transcript-id",
            results=results
        )
        
        # Verify the results
        assert saved_results["id"] == "test-results-id" 