# File: backend/tests/services/test_interview_evaluation_service.py

import pytest
import json
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime

from app.services.interview_evaluation_service import InterviewEvaluationService
from app.services.openai.chat_completion_service import ChatCompletionService
from app.services.firebase_service import FirebaseService
from app.services.roles_service import RolesService

# Sample data for testing
SAMPLE_INTERVIEW_ID = "interview123"
SAMPLE_ROLE_ID = "role456"
SAMPLE_APPLICATION_ID = "app789"
SAMPLE_USER_ID = "user123"
SAMPLE_SESSION_ID = "session123"

# Sample transcript data
SAMPLE_TRANSCRIPT = {
    "id": SAMPLE_INTERVIEW_ID,
    "messages": [
        {"role": "interviewer", "content": "Tell me about your experience with Python."},
        {"role": "candidate", "content": "I have 5 years of experience with Python, primarily in web development and data analysis."},
        {"role": "interviewer", "content": "How do you handle tight deadlines?"},
        {"role": "candidate", "content": "I prioritize tasks, communicate with stakeholders, and focus on delivering the most critical features first."}
    ],
    "template_id": "template123"
}

# Sample role data
SAMPLE_ROLE_DATA = {
    "id": SAMPLE_ROLE_ID,
    "title": "Senior Software Engineer",
    "description": "We are looking for a Senior Software Engineer with strong Python skills.",
    "summary": "Senior role requiring 5+ years of experience in software development."
}

# Sample template data
SAMPLE_TEMPLATE = {
    "id": "template123",
    "stage": "Technical Interview",
    "questions": [
        {
            "question": "Tell me about your experience with Python.",
            "purpose": "Assess technical expertise",
            "idealAnswerCriteria": "Should demonstrate deep knowledge and practical experience"
        },
        {
            "question": "How do you handle tight deadlines?",
            "purpose": "Assess work management",
            "idealAnswerCriteria": "Should show prioritization skills and stress management"
        }
    ],
    "evaluationCriteria": [
        {
            "type": "ScoreCard",
            "competency": "Technical Skills",
            "weight": 0.4,
            "criteria": "Assess technical knowledge and experience",
            "description": "Look for depth of understanding and practical application"
        },
        {
            "type": "ScoreCard",
            "competency": "Problem Solving",
            "weight": 0.3,
            "criteria": "Evaluate approach to solving problems",
            "description": "Look for structured thinking and creativity"
        },
        {
            "type": "BetweenTheLines",
            "criteria": "Enthusiasm for the role",
            "description": "Assess genuine interest in the position"
        },
        {
            "type": "Disqualifier",
            "criteria": "Dishonesty",
            "description": "Any evidence of fabricating experience"
        }
    ],
    "passRate": 0.7
}

# Sample application data
SAMPLE_APPLICATION_DATA = {
    "id": SAMPLE_APPLICATION_ID,
    "resumeText": "John Doe\nSenior Software Engineer\n5+ years of Python experience\nExpertise in web development and data analysis",
    "evaluations": [
        {
            "parsedResumeText": "John Doe\nSenior Software Engineer\n5+ years of Python experience\nExpertise in web development and data analysis"
        }
    ]
}

# Sample OpenAI response
SAMPLE_OPENAI_RESPONSE = MagicMock()
SAMPLE_OPENAI_RESPONSE.choices = [
    MagicMock(
        message=MagicMock(
            content="""```json
{
  "evaluation_summary": "The candidate demonstrated strong technical skills and good communication.",
  "scorecard_evaluation": {
    "technical_skills": {
      "score": 4.5,
      "comments": "Strong technical knowledge demonstrated in answers."
    },
    "problem_solving": {
      "score": 3.5,
      "comments": "Good problem-solving approach but could improve on efficiency."
    },
    "overall_score": 4.0
  },
  "question_analysis": [
    {
      "question": "Tell me about your experience with Python.",
      "answer_quality": "Excellent",
      "strengths": ["Detailed examples", "Relevant experience"],
      "areas_for_improvement": ["Could provide more specific metrics"]
    },
    {
      "question": "How do you handle tight deadlines?",
      "answer_quality": "Good",
      "strengths": ["Structured approach", "Prioritization skills"],
      "areas_for_improvement": ["More examples of past situations"]
    }
  ],
  "between_the_lines": {
    "enthusiasm": "High",
    "confidence": "Medium-High",
    "authenticity": "High",
    "notes": "Candidate showed genuine interest in the role and company."
  },
  "disqualifier_check": {
    "flags": [],
    "passed": true
  },
  "decision_reasoning": "The candidate meets all the key requirements for the role with strong technical skills and good cultural fit. Recommend proceeding to the next stage."
}
```"""
        )
    )
]

# Sample evaluation result
SAMPLE_EVALUATION_RESULT = {
    "evaluation_summary": "The candidate demonstrated strong technical skills and good communication.",
    "scorecard_evaluation": {
        "technical_skills": {
            "score": 4.5,
            "comments": "Strong technical knowledge demonstrated in answers."
        },
        "problem_solving": {
            "score": 3.5,
            "comments": "Good problem-solving approach but could improve on efficiency."
        },
        "overall_score": 4.0
    },
    "question_analysis": [
        {
            "question": "Tell me about your experience with Python.",
            "answer_quality": "Excellent",
            "strengths": ["Detailed examples", "Relevant experience"],
            "areas_for_improvement": ["Could provide more specific metrics"]
        },
        {
            "question": "How do you handle tight deadlines?",
            "answer_quality": "Good",
            "strengths": ["Structured approach", "Prioritization skills"],
            "areas_for_improvement": ["More examples of past situations"]
        }
    ],
    "between_the_lines": {
        "enthusiasm": "High",
        "confidence": "Medium-High",
        "authenticity": "High",
        "notes": "Candidate showed genuine interest in the role and company."
    },
    "disqualifier_check": {
        "flags": [],
        "passed": True
    },
    "decision_reasoning": "The candidate meets all the key requirements for the role with strong technical skills and good cultural fit. Recommend proceeding to the next stage."
}


@pytest.mark.asyncio
async def test_evaluate_interview_error_handling():
    """Test error handling in the interview evaluation flow."""
    # Test case where interview transcript is not found
    with patch('app.services.firebase_service.FirebaseService', autospec=True) as MockFirebaseService, \
         patch('app.services.roles_service.RolesService', autospec=True) as MockRolesService:
        
        # Setup Firebase service mock to return None for transcript
        mock_firebase = MockFirebaseService.return_value
        mock_firebase.get_interview_transcript = AsyncMock(return_value=None)
        
        # Call the service method
        result = await InterviewEvaluationService.evaluate_interview_authenticated(
            SAMPLE_INTERVIEW_ID,
            SAMPLE_ROLE_ID,
            SAMPLE_APPLICATION_ID,
            SAMPLE_USER_ID
        )
        
        # Verify the error result
        assert result["status"] == "error"
        assert "message" in result
        assert f"Interview transcript {SAMPLE_INTERVIEW_ID} not found" in result["message"]


@pytest.mark.asyncio
async def test_public_interview_session_not_found():
    """Test error handling when public interview session is not found."""
    with patch('app.services.firebase_service.FirebaseService', autospec=True) as MockFirebaseService:
        # Setup mock Firestore document references and data
        mock_session_doc = MagicMock()
        mock_session_doc.exists = False
        
        mock_session_ref = MagicMock()
        mock_session_ref.get.return_value = mock_session_doc
        
        # Setup Firebase service mock
        mock_firebase = MockFirebaseService.return_value
        mock_firebase.db = MagicMock()
        mock_firebase.db.collection.return_value.document.return_value = mock_session_ref
        
        # Call the service method
        result = await InterviewEvaluationService.auto_evaluate_public_interview(SAMPLE_SESSION_ID)
        
        # Verify the error result
        assert result["status"] == "error"
        assert "message" in result
        assert f"Interview session {SAMPLE_SESSION_ID} not found" in result["message"]
