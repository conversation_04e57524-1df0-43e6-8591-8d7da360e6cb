import pytest
from unittest.mock import Mock, patch, AsyncMock
import json
from typing import Dict, Any

from app.services.ai_service import AIService
from app.core.config import settings

@pytest.fixture
def ai_service():
    """Create an AI service instance for testing."""
    return AIService()

@pytest.fixture
def mock_openai_response():
    """Create a mock OpenAI response."""
    return {
        "choices": [
            {
                "message": {
                    "content": json.dumps({
                        "action": "create_role",
                        "response_text": "I'll help you create the role.",
                        "needs_clarification": False
                    })
                }
            }
        ]
    }

@pytest.fixture
def mock_function_call_response():
    """Create a mock OpenAI response with function call."""
    return {
        "choices": [
            {
                "message": {
                    "content": "",
                    "function_call": {
                        "name": "create_role",
                        "arguments": json.dumps({
                            "title": "Senior Python Developer",
                            "department": "Engineering",
                            "requirements": [
                                "5+ years Python experience",
                                "Strong AWS knowledge",
                                "Team leadership experience"
                            ]
                        })
                    }
                }
            }
        ]
    }

async def test_process_email_basic(ai_service, mock_openai_response):
    """Test basic email processing without function calls."""
    with patch('openai.chat.completions.create', new_callable=AsyncMock) as mock_create:
        # Create a mock response object with the structure expected by the code
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message = Mock()
        mock_response.choices[0].message.content = json.dumps({
            "action": "create_role",
            "response_text": "I'll help you create the role.",
            "needs_clarification": False
        })
        mock_response.choices[0].message.tool_calls = None
        
        mock_create.return_value = mock_response
        
        result = await ai_service.process_email(
            user_message="We need to open a new role.",
            metadata={
                "from": "<EMAIL>",
                "to": "<EMAIL>",
                "subject": "New Role Request",
                "date": "2024-03-20"
            }
        )
        
        assert "action" in result
        assert result.get("action") == "create_role"
        assert "needs_clarification" in result
        assert result.get("needs_clarification") == False
        assert "response_text" in result

async def test_process_email_with_function_call(ai_service, mock_function_call_response, mock_openai_response):
    """Test email processing with function calls."""
    with patch('openai.chat.completions.create', new_callable=AsyncMock) as mock_create:
        # Create a mock response object with the structure expected by the code
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message = Mock()
        mock_response.choices[0].message.content = ""
        
        # Create tool_calls with function call
        tool_call = Mock()
        tool_call.type = 'function'
        tool_call.function = Mock()
        tool_call.function.name = 'create_role'
        tool_call.function.arguments = json.dumps({
            "title": "Senior Python Developer",
            "summary": "Experienced Python developer for our engineering team",
            "team": "Engineering",
            "keyResponsibilities": [
                "Develop and maintain Python applications",
                "Lead technical projects",
                "Mentor junior developers"
            ]
        })
        
        mock_response.choices[0].message.tool_calls = [tool_call]
        
        # Mock the function execution result
        with patch.object(ai_service, '_handle_role_creation', return_value={
            'role_id': '123456',
            'email_response': {
                'subject': 'Role Created',
                'body': 'The role has been created successfully.'
            }
        }):
            mock_create.return_value = mock_response
            
            result = await ai_service.process_email(
                user_message="""
                Hi Sia,
                
                We need to open a new role for a Senior Python Developer.
                Department: Engineering
                Requirements:
                - 5+ years Python experience
                - Strong AWS knowledge
                - Team leadership experience
                
                Best regards,
                John
                """,
                metadata={
                    "from": "<EMAIL>",
                    "subject": "New Role Opening",
                    "to": "<EMAIL>"
                }
            )
            
            # Verify the function was called
            assert mock_create.called
            
            # Verify final response
            assert result['action'] == 'create_role'
            assert 'result' in result
            assert 'email_response' in result

async def test_process_email_missing_info(ai_service):
    """Test email processing with missing information."""
    with patch('openai.chat.completions.create', new_callable=AsyncMock) as mock_create:
        # Create a mock response object with the structure expected by the code
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message = Mock()
        mock_response.choices[0].message.content = json.dumps({
            "action": "request_clarification",
            "response_text": "I need more information about the role.",
            "needs_clarification": True,
            "clarification_points": [
                "What is the required experience level?",
                "Is this role remote or office-based?"
            ]
        })
        mock_response.choices[0].message.tool_calls = None
        
        mock_create.return_value = mock_response
        
        result = await ai_service.process_email(
            user_message="We need to open a new developer role.",
            metadata={
                "from": "<EMAIL>",
                "to": "<EMAIL>",
                "subject": "New Role",
                "date": "2024-03-20"
            }
        )
        
        assert "action" in result
        assert result.get("action") == "request_clarification"
        assert "needs_clarification" in result
        assert result.get("needs_clarification") == True
        assert "clarification_points" in result
        assert len(result.get("clarification_points")) == 2

async def test_function_registry(ai_service):
    """Test function registry functionality."""
    # Verify create_role function is registered
    functions = ai_service.function_registry.get_functions()
    create_role = next((f for f in functions if f["name"] == "create_role"), None)
    
    assert create_role is not None
    assert "title" in create_role["parameters"]["properties"]
    assert "team" in create_role["parameters"]["properties"]
    
    # Verify handler is registered
    handler = ai_service.function_registry.get_handler("create_role")
    assert handler is not None
    assert callable(handler)

async def test_generate_evaluation_criteria(ai_service):
    """Test generating evaluation criteria for an interview template."""
    with patch('app.services.openai.chat_completion_service.ChatCompletionService.generate_completion', new_callable=AsyncMock) as mock_generate:
        # Create a mock response object with the structure expected by the code
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message = Mock()
        
        # Create tool_calls with function call
        tool_call = Mock()
        tool_call.type = 'function'
        tool_call.function = Mock()
        tool_call.function.name = 'generate_evaluation_criteria'
        tool_call.function.arguments = json.dumps({
            "scoreCardCriteria": [
                {
                    "type": "ScoreCard",
                    "competency": "Technical Knowledge",
                    "weight": 0.3,
                    "criteria": "Demonstrates deep understanding of relevant technologies and concepts",
                    "description": "Look for specific examples of applying technical knowledge to solve problems"
                },
                {
                    "type": "ScoreCard",
                    "competency": "Problem-Solving",
                    "weight": 0.25,
                    "criteria": "Shows ability to analyze complex problems and develop effective solutions",
                    "description": "Evaluate approach to breaking down problems and considering alternatives"
                },
                {
                    "type": "ScoreCard",
                    "competency": "Communication",
                    "weight": 0.2,
                    "criteria": "Articulates ideas clearly and effectively",
                    "description": "Assess clarity, conciseness, and ability to explain technical concepts"
                },
                {
                    "type": "ScoreCard",
                    "competency": "Experience",
                    "weight": 0.25,
                    "criteria": "Has relevant experience for the role requirements",
                    "description": "Look for depth and breadth of experience in required areas"
                }
            ],
            "betweenTheLinesCriteria": [
                {
                    "type": "BetweenTheLines",
                    "criteria": "Thought Process Structure",
                    "description": "Does the candidate demonstrate structured thinking or just guess their way through?"
                },
                {
                    "type": "BetweenTheLines",
                    "criteria": "Cultural Fit",
                    "description": "Would the candidate work well with the existing team culture?"
                }
            ],
            "disqualifierCriteria": [
                {
                    "type": "Disqualifier",
                    "criteria": "Lack of Core Technical Skills",
                    "description": "Missing fundamental skills required for the role"
                },
                {
                    "type": "Disqualifier",
                    "criteria": "Poor Communication",
                    "description": "Unable to articulate thoughts clearly or respond appropriately to questions"
                }
            ],
            "passRate": 0.8
        })
        
        mock_response.choices[0].message.tool_calls = [tool_call]
        mock_response.choices[0].message.function_call = None
        
        mock_generate.return_value = mock_response
        
        # Mock role and template data
        role = {
            "id": "role123",
            "title": "Senior Software Engineer",
            "level": "Senior",
            "summary": "Experienced software engineer for our platform team",
            "team": "Platform Engineering",
            "yearsOfExperience": "5+ years",
            "requiredSkills": {
                "1": "Python",
                "2": "Cloud Infrastructure",
                "3": "System Design"
            },
            "preferredSkills": {
                "1": "Kubernetes",
                "2": "CI/CD",
                "3": "Microservices"
            },
            "keyResponsibilities": [
                "Design and implement scalable services",
                "Mentor junior engineers",
                "Contribute to architecture decisions"
            ],
            "aboutTeam": "Our platform team builds the foundation for all our products",
            "aboutCompany": "We are a leading technology company"
        }
        
        template = {
            "id": "template123",
            "stage": "Technical Interview",
            "duration": "60 minutes",
            "customInstructions": "Focus on system design and coding skills",
            "questions": [
                {
                    "id": "q1",
                    "text": "Describe a complex technical problem you've solved recently",
                    "purpose": "Evaluate problem-solving approach",
                    "idealAnswerCriteria": "Clear problem definition, structured approach, successful outcome"
                },
                {
                    "id": "q2",
                    "text": "How would you design a scalable notification system?",
                    "purpose": "Assess system design skills",
                    "idealAnswerCriteria": "Considers scale, reliability, and performance tradeoffs"
                }
            ]
        }
        
        # Call the method
        result = await ai_service.generate_evaluation_criteria(role, template)
        
        # Verify the function was called with correct parameters
        mock_generate.assert_called_once()
        call_args = mock_generate.call_args[1]
        assert call_args["use_case"] == "criterion_agent"
        assert call_args["function_call"]["name"] == "generate_evaluation_criteria"
        
        # Verify the result
        assert result["status"] == "success"
        assert "scoreCardCriteria" in result
        assert len(result["scoreCardCriteria"]) == 4
        assert "betweenTheLinesCriteria" in result
        assert len(result["betweenTheLinesCriteria"]) == 2
        assert "disqualifierCriteria" in result
        assert len(result["disqualifierCriteria"]) == 2
        assert "passRate" in result
        assert result["passRate"] == 0.8
        
        # Verify the weights sum to 1.0
        total_weight = sum(criterion["weight"] for criterion in result["scoreCardCriteria"])
        assert abs(total_weight - 1.0) < 0.01 