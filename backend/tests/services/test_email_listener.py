"""Tests for the email listener service."""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
import json

from app.services.email_service import EmailService
from app.services.firebase_service import FirebaseService
from app.services.ai_service import AIService

@pytest.fixture
def email_service():
    """Create an email service instance for testing."""
    return EmailService()

@pytest.fixture
def mock_firebase_service():
    """Create a mock Firebase service."""
    with patch('app.services.firebase_service.FirebaseService') as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        mock_instance.get_user_by_email = AsyncMock()
        yield mock_instance

@pytest.fixture
def mock_ai_service():
    """Create a mock AI service."""
    with patch('app.services.ai_service.AIService') as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        mock_instance.process_email = AsyncMock()
        yield mock_instance

@pytest.mark.asyncio
async def test_validate_sender_authorized(email_service, mock_firebase_service):
    """Test sender validation with authorized user."""
    # Setup
    test_email = "<EMAIL>"
    mock_firebase_service.get_user_by_email.return_value = {"email": test_email}
    
    # Execute
    result = await email_service._validate_sender(test_email)
    
    # Assert
    assert result is True
    mock_firebase_service.get_user_by_email.assert_called_once_with(test_email)

@pytest.mark.asyncio
async def test_validate_sender_unauthorized(email_service, mock_firebase_service):
    """Test sender validation with unauthorized user."""
    # Setup
    test_email = "<EMAIL>"
    mock_firebase_service.get_user_by_email.return_value = None
    
    # Execute
    result = await email_service._validate_sender(test_email)
    
    # Assert
    assert result is False
    mock_firebase_service.get_user_by_email.assert_called_once_with(test_email)

@pytest.mark.asyncio
async def test_handle_email_role_creation(email_service, mock_ai_service):
    """Test handling of role creation email."""
    # Setup
    test_email_data = {
        'subject': 'New Role Request',
        'from': '<EMAIL>',
        'to': '<EMAIL>',
        'cc': None,
        'date': datetime.now().isoformat(),
        'body': 'We need to hire a Senior Python Developer'
    }
    
    mock_ai_response = {
        'action': 'create_role',
        'role_details': {
            'title': 'Senior Python Developer',
            'department': 'Engineering'
        }
    }
    mock_ai_service.process_email.return_value = mock_ai_response
    
    # Execute
    await email_service._handle_email(test_email_data)
    
    # Assert
    mock_ai_service.process_email.assert_called_once()
    assert mock_ai_service.process_email.call_args[1]['content'] == test_email_data['body']

@pytest.mark.asyncio
async def test_handle_email_clarification_needed(email_service, mock_ai_service):
    """Test handling of email needing clarification."""
    # Setup
    test_email_data = {
        'subject': 'New Role',
        'from': '<EMAIL>',
        'to': '<EMAIL>',
        'cc': None,
        'date': datetime.now().isoformat(),
        'body': 'We need to hire someone'
    }
    
    mock_ai_response = {
        'action': 'request_clarification',
        'clarification_points': ['Role title', 'Department'],
        'context': 'Need more details about the role'
    }
    mock_ai_service.process_email.return_value = mock_ai_response
    
    # Execute
    await email_service._handle_email(test_email_data)
    
    # Assert
    mock_ai_service.process_email.assert_called_once()
    assert mock_ai_service.process_email.call_args[1]['content'] == test_email_data['body']

@pytest.mark.asyncio
async def test_extract_metadata(email_service):
    """Test metadata extraction from email."""
    # Setup
    test_email_data = {
        'subject': 'Test Subject',
        'from': '<EMAIL>',
        'to': '<EMAIL>',
        'cc': '<EMAIL>',
        'date': '2024-01-22T10:00:00',
        'attachments': [{'filename': 'test.pdf'}]
    }
    
    # Execute
    metadata = await email_service._extract_metadata(test_email_data)
    
    # Assert
    assert metadata['subject'] == test_email_data['subject']
    assert metadata['from'] == test_email_data['from']
    assert metadata['to'] == test_email_data['to']
    assert metadata['cc'] == test_email_data['cc']
    assert metadata['has_attachments'] is True
    assert 'timestamp' in metadata

@pytest.mark.asyncio
async def test_create_role(email_service):
    """Test role creation from email data."""
    # Setup
    test_role_details = {
        'title': 'Senior Python Developer',
        'department': 'Engineering',
        'location': {
            'type': 'hybrid',
            'primary_location': 'New York'
        }
    }
    
    # Execute
    role_id = await email_service._create_role(test_role_details)
    
    # Assert
    assert role_id is not None
    assert isinstance(role_id, str)

@pytest.mark.asyncio
async def test_send_clarification_request(email_service):
    """Test sending clarification request."""
    # Setup
    with patch.object(email_service, 'send_email', new_callable=AsyncMock) as mock_send:
        test_email = {
            'from': '<EMAIL>',
            'subject': 'New Role',
            'cc': None
        }
        test_ai_response = {
            'clarification_points': ['Role title', 'Department']
        }
        
        # Execute
        await email_service._send_clarification_request(test_ai_response, test_email)
        
        # Assert
        mock_send.assert_called_once()
        call_args = mock_send.call_args[1]
        assert call_args['to_email'] == test_email['from']
        assert 'Additional Information Needed' in call_args['subject']
        assert 'Role title' in call_args['body']

@pytest.mark.asyncio
async def test_send_role_confirmation(email_service):
    """Test sending role confirmation email."""
    # Setup
    with patch.object(email_service, 'send_email', new_callable=AsyncMock) as mock_send:
        test_role_details = {
            'title': 'Senior Python Developer',
            'department': 'Engineering',
            'location': 'New York'
        }
        test_role_id = 'test123'
        test_email = '<EMAIL>'
        
        # Execute
        await email_service.send_role_confirmation(
            to_email=test_email,
            role_details=test_role_details,
            role_id=test_role_id
        )
        
        # Assert
        mock_send.assert_called_once()
        call_args = mock_send.call_args[1]
        assert call_args['to_email'] == test_email
        assert test_role_details['title'] in call_args['subject']
        assert test_role_id in call_args['body'] 