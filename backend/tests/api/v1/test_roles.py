import pytest
from httpx import AsyncClient, ASGITransport
from app.main import app
from app.api.v1.endpoints.roles import <PERSON>Status, RoleLevel, RolePriority
from app.services.firebase_service import FirebaseService
from fastapi.testclient import TestClient
from datetime import datetime
import json
from unittest.mock import AsyncMock, MagicMock, patch
import firebase_admin
from firebase_admin import storage

@pytest.fixture
def mock_firebase_service(mocker):
    mock = mocker.Mock(spec=FirebaseService)
    return mock

@pytest.fixture
def mock_firebase_storage(mocker):
    """Mock Firebase storage bucket."""
    mock_blob = MagicMock()
    mock_blob.generate_signed_url.return_value = "https://storage.googleapis.com/test-bucket/test-file.mp3"
    mock_blob.upload_from_string = AsyncMock()

    mock_bucket = MagicMock()
    mock_bucket.blob.return_value = mock_blob

    mocker.patch('firebase_admin.storage.bucket', return_value=mock_bucket)
    return mock_bucket

@pytest.fixture
def mock_parser_service(mocker):
    mock = mocker.patch("app.api.v1.endpoints.roles.parse_job_description")
    mock.return_value = {
        "title": "Software Engineer",
        "level": "Senior",
        "required_skills": ["Python", "FastAPI"],
        "preferred_skills": ["AWS", "Docker"]
    }
    return mock

@pytest.fixture
def sample_role_data():
    return {
        "title": "Senior Software Engineer",
        "level": RoleLevel.SENIOR,
        "status": RoleStatus.INTAKE,
        "priority": RolePriority.NORMAL,
        "details": {
            "summary": "We are looking for a senior software engineer",
            "required_skills": ["Python", "FastAPI", "React"],
            "preferred_skills": ["AWS", "Docker"],
            "team_dynamics": "Collaborative team environment",
            "success_metrics": "Deliver high-quality code",
            "technical_challenges": "Scale microservices architecture",
            "stakeholder_interactions": "Regular interaction with product team"
        },
        "number_of_positions": 2,
        "hiring_manager_name": "John Doe",
        "hiring_manager_email": "<EMAIL>",
        "team_name": "Engineering",
        "recruiter_name": "Jane Smith",
        "recruiter_email": "<EMAIL>"
    }

@pytest.fixture
def mock_auth_user():
    return {
        "id": "test-user-id",
        "email": "<EMAIL>",
        "role": "hiring_manager"
    }

@pytest.fixture
async def client() -> AsyncClient:
    """Create a test client for the FastAPI application."""
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as client:
        yield client

@pytest.mark.asyncio
async def test_health_check(client: AsyncClient):
    """Test the health check endpoint."""
    response = await client.get("/api/v1/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}

@pytest.mark.asyncio
async def test_create_role(client: AsyncClient, mock_firebase_service, mock_auth_user, sample_role_data, monkeypatch):
    """Test creating a new role."""
    # Mock the firebase service
    mock_firebase_service.create_role.return_value = "test-role-id"
    monkeypatch.setattr("app.api.v1.endpoints.roles.firebase", mock_firebase_service)
    monkeypatch.setattr("app.api.v1.endpoints.roles.get_current_user", lambda: mock_auth_user)

    response = await client.post("/api/v1/roles/", json=sample_role_data)

    assert response.status_code == 200
    assert response.json()["data"]["id"] == "test-role-id"
    
    # Get the actual call arguments
    call_args = mock_firebase_service.create_role.call_args
    assert call_args is not None
    
    # Check that user_id matches
    assert call_args.kwargs["user_id"] == mock_auth_user["id"]
    
    # Check that the required fields in role_data match
    role_data = call_args.kwargs["role_data"]
    for key, value in sample_role_data.items():
        assert role_data[key] == value
    
    # Check that created_at and updated_at are present
    assert "created_at" in role_data
    assert "updated_at" in role_data

@pytest.mark.asyncio
async def test_get_role(client: AsyncClient, mock_firebase_service, mock_auth_user, monkeypatch):
    role_id = "test-role-id"
    mock_role = {
        "id": role_id,
        "title": "Test Role",
        "description": "Test Description",
        "createdAt": datetime.utcnow(),
        "updatedAt": datetime.utcnow(),
        "hiringManagerId": mock_auth_user["id"]
    }
    mock_firebase_service.get_role.return_value = mock_role
    monkeypatch.setattr("app.api.v1.endpoints.roles.firebase", mock_firebase_service)
    monkeypatch.setattr("app.api.v1.endpoints.roles.get_current_user", lambda: mock_auth_user)
    
    response = await client.get(f"/api/v1/roles/{role_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "Test Role"
    assert data["description"] == "Test Description"
    mock_firebase_service.get_role.assert_called_once_with(
        user_id=mock_auth_user["id"],
        role_id=role_id
    )

@pytest.mark.asyncio
async def test_get_role_not_found(client: AsyncClient, mock_firebase_service, mock_auth_user, monkeypatch):
    role_id = "non-existent-role"
    mock_firebase_service.get_role.return_value = None
    monkeypatch.setattr("app.api.v1.endpoints.roles.firebase", mock_firebase_service)
    monkeypatch.setattr("app.api.v1.endpoints.roles.get_current_user", lambda: mock_auth_user)
    
    response = await client.get(f"/api/v1/roles/{role_id}")
    assert response.status_code == 404
    assert response.json()["detail"] == "Role not found"

@pytest.mark.asyncio
async def test_list_roles(client: AsyncClient, mock_firebase_service, mock_auth_user, monkeypatch):
    mock_roles = [
        {"id": "role-1", "title": "Role 1", "description": "Description 1", "hiringManagerId": mock_auth_user["id"]},
        {"id": "role-2", "title": "Role 2", "description": "Description 2", "hiringManagerId": mock_auth_user["id"]}
    ]
    mock_firebase_service.get_roles.return_value = mock_roles
    monkeypatch.setattr("app.api.v1.endpoints.roles.firebase", mock_firebase_service)
    monkeypatch.setattr("app.api.v1.endpoints.roles.get_current_user", lambda: mock_auth_user)
    
    response = await client.get("/api/v1/roles/")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    assert data[0]["id"] == "role-1"
    assert data[1]["id"] == "role-2"
    mock_firebase_service.get_roles.assert_called_once_with(user_id=mock_auth_user["id"])

@pytest.mark.asyncio
async def test_update_role(client: AsyncClient, mock_firebase_service, mock_auth_user, monkeypatch):
    role_id = "test-role-id"
    update_data = {
        "title": "Updated Role",
        "details": {
            "summary": "Updated summary",
            "required_skills": ["Python", "FastAPI"],
            "preferred_skills": ["AWS", "Docker"]
        }
    }
    mock_firebase_service.get_role.return_value = {
        "id": role_id,
        "title": "Test Role",
        "details": {
            "summary": "Original summary",
            "required_skills": ["Python"],
            "preferred_skills": ["AWS"]
        },
        "hiringManagerId": mock_auth_user["id"]
    }
    monkeypatch.setattr("app.api.v1.endpoints.roles.firebase", mock_firebase_service)
    monkeypatch.setattr("app.api.v1.endpoints.roles.get_current_user", lambda: mock_auth_user)
    
    response = await client.put(f"/api/v1/roles/{role_id}", json=update_data)
    assert response.status_code == 200
    assert response.json()["status"] == "success"
    assert response.json()["role_id"] == role_id
    
    # Get the actual call arguments
    call_args = mock_firebase_service.update_role.call_args
    assert call_args is not None
    
    # Check that user_id and role_id match
    assert call_args.kwargs["user_id"] == mock_auth_user["id"]
    assert call_args.kwargs["role_id"] == role_id
    
    # Check that the required fields in role_data match
    role_data = call_args.kwargs["role_data"]
    assert role_data["title"] == update_data["title"]
    
    # Check details fields
    details = role_data["details"]
    assert details["summary"] == update_data["details"]["summary"]
    assert details["required_skills"] == update_data["details"]["required_skills"]
    assert details["preferred_skills"] == update_data["details"]["preferred_skills"]
    
    # Check that updated_at is present
    assert "updated_at" in role_data

@pytest.mark.asyncio
async def test_delete_role(client: AsyncClient, mock_firebase_service, mock_auth_user, monkeypatch):
    role_id = "test-role-id"
    mock_firebase_service.get_role.return_value = {
        "id": role_id,
        "hiringManagerId": mock_auth_user["id"]
    }
    monkeypatch.setattr("app.api.v1.endpoints.roles.firebase", mock_firebase_service)
    monkeypatch.setattr("app.api.v1.endpoints.roles.get_current_user", lambda: mock_auth_user)
    
    response = await client.delete(f"/api/v1/roles/{role_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["message"] == "Role deleted successfully"
    mock_firebase_service.delete_role.assert_called_once_with(
        user_id=mock_auth_user["id"],
        role_id=role_id
    )

@pytest.mark.asyncio
async def test_update_role_status(client: AsyncClient, mock_firebase_service, mock_auth_user, monkeypatch):
    role_id = "test-role-id"
    mock_firebase_service.get_role.return_value = {"id": role_id}
    monkeypatch.setattr("app.api.v1.endpoints.roles.firebase", mock_firebase_service)
    monkeypatch.setattr("app.api.v1.endpoints.roles.get_current_user", lambda: mock_auth_user)

    response = await client.patch(f"/api/v1/roles/{role_id}/status", json={
        "status": "CLOSED"
    })
    assert response.status_code == 200
    assert response.json()["status"] == "success"

@pytest.mark.asyncio
async def test_update_role_priority(client: AsyncClient, mock_firebase_service, mock_auth_user, monkeypatch):
    role_id = "test-role-id"
    mock_firebase_service.get_role.return_value = {"id": role_id}
    monkeypatch.setattr("app.api.v1.endpoints.roles.firebase", mock_firebase_service)
    monkeypatch.setattr("app.api.v1.endpoints.roles.get_current_user", lambda: mock_auth_user)

    response = await client.patch(f"/api/v1/roles/{role_id}/priority", json={
        "priority": "Expedited"
    })
    assert response.status_code == 200
    assert response.json()["status"] == "success"

@pytest.mark.asyncio
async def test_upload_intake_recording(client: AsyncClient, mock_firebase_service, mock_firebase_storage, mock_auth_user, monkeypatch):
    """Test uploading an intake recording."""
    # Mock file upload
    test_file = "test_recording.mp3"
    test_content = b"test content"
    
    response = await client.post(
        "/api/v1/roles/test-role-id/intake-recording",
        files={"recording": (test_file, test_content, "audio/mpeg")},
        headers={"Authorization": "Bearer test-token"}
    )
    
    assert response.status_code == 200
    assert response.json()["message"] == "Recording uploaded successfully"
    assert "url" in response.json()
    assert "path" in response.json()
    
    # Verify the blob was created and uploaded
    mock_blob = mock_firebase_storage.blob.return_value
    mock_blob.upload_from_string.assert_called_once_with(
        test_content,
        content_type="audio/mpeg"
    )
    mock_blob.generate_signed_url.assert_called_once_with(
        version="v4",
        expiration=3600,
        method="GET"
    )

@pytest.mark.asyncio
async def test_parse_job_description(client: AsyncClient, mock_parser_service, monkeypatch):
    # Create a mock PDF file for testing
    test_file_content = b"test pdf content"
    files = {"jd_file": ("test.pdf", test_file_content, "application/pdf")}

    response = await client.post("/api/v1/roles/parse-job-description", files=files)
    assert response.status_code == 200
    assert response.json()["title"] == "Software Engineer"
    assert response.json()["level"] == "Senior"
    assert "Python" in response.json()["required_skills"]

@pytest.mark.asyncio
async def test_update_candidate_progression(client: AsyncClient, mock_firebase_service, mock_auth_user, monkeypatch):
    role_id = "test-role-id"
    mock_firebase_service.get_role.return_value = {"id": role_id}
    monkeypatch.setattr("app.api.v1.endpoints.roles.firebase", mock_firebase_service)
    
    progression_data = {
        "total_candidates": 10,
        "in_review": 5,
        "interviewed": 3,
        "offered": 1,
        "rejected": 1
    }
    
    response = await client.patch(f"/api/v1/roles/{role_id}/candidate-progression", json=progression_data)
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert data["message"] == "Candidate progression updated successfully"
    mock_firebase_service.update_role.assert_called_once() 