# File: backend/tests/api/v1/test_resume_evaluation.py

import pytest
import json
from unittest.mock import patch, AsyncMock, MagicMock
import io
from fastapi import UploadFile

from httpx import AsyncClient
from app.main import app
from app.services.resume_parser_service import ResumeParserService
from app.services.resume_evaluator_service import ResumeEvaluatorService

# Sample data for testing
SAMPLE_RESUME_TEXT = "<PERSON>\nExperienced Software Engineer\nSkills: Python, JavaScript"
SAMPLE_JOB_POSTING = "Software Engineer\nRequired Skills: Python, JavaScript"

# Sample response data
SAMPLE_EVALUATION_RESPONSE = {
    "scorecard": {
        "technicalSkills": {
            "score": 4,
            "evaluation": "Good technical match",
            "strengths": ["Python", "JavaScript"],
            "gaps": ["No mention of TypeScript"]
        },
        "softSkills": {
            "score": 3, 
            "evaluation": "Some soft skills evident",
            "strengths": ["Communication"],
            "gaps": ["Leadership"]
        },
        "experienceRelevance": {
            "score": 4,
            "evaluation": "Relevant experience",
            "strengths": ["Software development"],
            "gaps": ["No specific industry experience"]
        },
        "educationCertifications": {
            "score": 3,
            "evaluation": "Adequate education",
            "strengths": ["Computer Science degree"],
            "gaps": ["No specialized certifications"]
        },
        "achievementsImpact": {
            "score": 3,
            "evaluation": "Some achievements noted",
            "strengths": ["Project completion"],
            "gaps": ["Limited quantifiable metrics"]
        },
        "overallScore": 3.5,
        "overallEvaluation": "Good overall fit for the role"
    },
    "recommendation": {
        "decision": "PASS",
        "confidence": "MEDIUM",
        "reasoning": "The candidate meets the core requirements"
    },
    "feedback": {
        "candidateStrengths": ["Technical skills", "Relevant experience"],
        "improvementAreas": ["Add quantifiable achievements", "Highlight soft skills"],
        "interviewFocus": ["Problem-solving approach", "Communication skills"]
    },
    "metadata": {
        "timestamp": "2023-10-15T14:30:00Z",
        "model": "gpt-4o",
        "resumeLength": 59,
        "jobPostingLength": 50
    }
}

SAMPLE_BASIC_EVALUATION_RESPONSE = {
    "overallScore": 3.5,
    "decision": "PASS",
    "confidence": "MEDIUM",
    "reasoning": "The candidate meets the core requirements",
    "keyStrengths": ["Python", "JavaScript"],
    "keyGaps": ["No TypeScript"],
    "metadata": {
        "timestamp": "2023-10-15T14:30:00Z",
        "model": "gpt-4o",
        "resumeLength": 59,
        "jobPostingLength": 50,
        "evaluationType": "basic"
    }
}

@pytest.mark.asyncio
async def test_evaluate_resume_text():
    """Test the resume evaluation endpoint with plain text."""
    with patch('app.services.resume_evaluator_service.ResumeEvaluatorService.evaluate_resume', new_callable=AsyncMock) as mock_evaluate:
        # Setup the mock
        mock_evaluate.return_value = SAMPLE_EVALUATION_RESPONSE
        
        # Make the request
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/resume-evaluation/evaluate",
                json={
                    "resume_text": SAMPLE_RESUME_TEXT,
                    "job_posting": SAMPLE_JOB_POSTING,
                    "model": "gpt-4o",
                    "basic_evaluation": False
                }
            )
        
        # Check the response
        assert response.status_code == 200
        data = response.json()
        assert "scorecard" in data
        assert "recommendation" in data
        assert "feedback" in data
        assert "metadata" in data
        assert data["recommendation"]["decision"] == "PASS"
        
        # Verify the mock was called correctly
        mock_evaluate.assert_called_once_with(
            resume_text=SAMPLE_RESUME_TEXT,
            job_posting=SAMPLE_JOB_POSTING,
            model="gpt-4o"
        )

@pytest.mark.asyncio
async def test_evaluate_resume_text_basic():
    """Test the basic resume evaluation endpoint with plain text."""
    with patch('app.services.resume_evaluator_service.ResumeEvaluatorService.evaluate_resume_basic', new_callable=AsyncMock) as mock_evaluate:
        # Setup the mock
        mock_evaluate.return_value = SAMPLE_BASIC_EVALUATION_RESPONSE
        
        # Make the request
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/resume-evaluation/evaluate",
                json={
                    "resume_text": SAMPLE_RESUME_TEXT,
                    "job_posting": SAMPLE_JOB_POSTING,
                    "model": "gpt-4o",
                    "basic_evaluation": True
                }
            )
        
        # Check the response
        assert response.status_code == 200
        data = response.json()
        assert "overallScore" in data
        assert "decision" in data
        assert "confidence" in data
        assert "keyStrengths" in data
        assert "keyGaps" in data
        assert "metadata" in data
        assert data["decision"] == "PASS"
        
        # Verify the mock was called correctly
        mock_evaluate.assert_called_once_with(
            resume_text=SAMPLE_RESUME_TEXT,
            job_posting=SAMPLE_JOB_POSTING,
            model="gpt-4o"
        )

@pytest.mark.asyncio
async def test_evaluate_resume_upload():
    """Test the resume evaluation endpoint with file upload."""
    with patch('app.services.resume_parser_service.ResumeParserService.parse_resume', new_callable=AsyncMock) as mock_parser, \
         patch('app.services.resume_evaluator_service.ResumeEvaluatorService.evaluate_resume', new_callable=AsyncMock) as mock_evaluate:
        
        # Setup the mocks
        mock_parser.return_value = {"text": SAMPLE_RESUME_TEXT, "metadata": {"filename": "resume.pdf", "content_type": "application/pdf"}}
        mock_evaluate.return_value = SAMPLE_EVALUATION_RESPONSE
        
        # Create a mock file
        mock_file = io.BytesIO(b"Mock PDF Content")
        
        # Make the request
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/resume-evaluation/evaluate-upload",
                files={"resume_file": ("resume.pdf", mock_file, "application/pdf")},
                data={"job_posting": SAMPLE_JOB_POSTING, "model": "gpt-4o"}
            )
        
        # Check the response
        assert response.status_code == 200
        data = response.json()
        assert "scorecard" in data
        assert "recommendation" in data
        assert "feedback" in data
        assert "metadata" in data
        assert data["recommendation"]["decision"] == "PASS"
        
        # Verify the mocks were called correctly
        mock_parser.assert_called_once()
        mock_evaluate.assert_called_once_with(
            resume_text=SAMPLE_RESUME_TEXT,
            job_posting=SAMPLE_JOB_POSTING,
            model="gpt-4o"
        )

@pytest.mark.asyncio
async def test_evaluate_resume_with_job_id():
    """Test resume evaluation with job ID."""
    with patch('app.services.resume_parser_service.ResumeParserService.parse_resume', new_callable=AsyncMock) as mock_parser, \
         patch('app.services.firebase_service.FirebaseService.get_public_role', new_callable=AsyncMock) as mock_get_role, \
         patch('app.services.resume_evaluator_service.ResumeEvaluatorService.evaluate_resume', new_callable=AsyncMock) as mock_evaluate:
        
        # Setup the mocks
        mock_parser.return_value = {"text": SAMPLE_RESUME_TEXT, "metadata": {"filename": "resume.pdf", "content_type": "application/pdf"}}
        mock_get_role.return_value = {"id": "job123", "title": "Software Engineer", "jobPosting": SAMPLE_JOB_POSTING}
        mock_evaluate.return_value = SAMPLE_EVALUATION_RESPONSE
        
        # Create a mock file
        mock_file = io.BytesIO(b"Mock PDF Content")
        
        # Make the request
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/resume-evaluation/evaluate-upload",
                files={"resume_file": ("resume.pdf", mock_file, "application/pdf")},
                params={"job_id": "job123", "model": "gpt-4o"}
            )
        
        # Check the response
        assert response.status_code == 200
        data = response.json()
        assert "scorecard" in data
        assert "recommendation" in data
        assert "feedback" in data
        assert "metadata" in data
        assert data["recommendation"]["decision"] == "PASS"
        
        # Verify the mocks were called correctly
        mock_parser.assert_called_once()
        mock_get_role.assert_called_once_with(role_id="job123")
        mock_evaluate.assert_called_once_with(
            resume_text=SAMPLE_RESUME_TEXT,
            job_posting=SAMPLE_JOB_POSTING,
            model="gpt-4o"
        )

@pytest.mark.asyncio
async def test_resume_evaluation_error_handling():
    """Test error handling in resume evaluation."""
    with patch('app.services.resume_parser_service.ResumeParserService.parse_resume', new_callable=AsyncMock) as mock_parser:
        # Setup the mock to raise an error
        mock_parser.side_effect = ValueError("Invalid PDF format")
        
        # Create a mock file
        mock_file = io.BytesIO(b"Invalid PDF Content")
        
        # Make the request
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/resume-evaluation/evaluate-upload",
                files={"resume_file": ("resume.pdf", mock_file, "application/pdf")},
                data={"job_posting": SAMPLE_JOB_POSTING}
            )
        
        # Check the response
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "Invalid PDF format" in data["detail"]
        
        # Verify the mock was called
        mock_parser.assert_called_once()

@pytest.mark.asyncio
async def test_health_check():
    """Test the health check endpoint."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.get("/api/v1/resume-evaluation/health")
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"
    assert data["service"] == "resume-evaluation" 