# File: backend/tests/api/v1/test_interview_evaluation.py

import pytest
import json
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime

from httpx import AsyncClient
from app.main import app
from app.services.interview_evaluation_service import InterviewEvaluationService

# Sample data for testing
SAMPLE_INTERVIEW_ID = "interview123"
SAMPLE_ROLE_ID = "role456"
SAMPLE_APPLICATION_ID = "app789"
SAMPLE_SESSION_ID = "session123"
SAMPLE_USER_ID = "user123"

# Sample evaluation response
SAMPLE_EVALUATION_RESULT = {
    "evaluation_summary": "The candidate demonstrated strong technical skills and good communication.",
    "scorecard_evaluation": {
        "technical_skills": {
            "score": 4.5,
            "comments": "Strong technical knowledge demonstrated in answers."
        },
        "communication": {
            "score": 4.0,
            "comments": "Clear and concise communication throughout the interview."
        },
        "problem_solving": {
            "score": 3.5,
            "comments": "Good problem-solving approach but could improve on efficiency."
        },
        "cultural_fit": {
            "score": 4.0,
            "comments": "Values align well with company culture."
        },
        "overall_score": 4.0
    },
    "question_analysis": [
        {
            "question": "Describe your experience with Python.",
            "answer_quality": "Excellent",
            "strengths": ["Detailed examples", "Relevant experience"],
            "areas_for_improvement": ["Could provide more specific metrics"]
        },
        {
            "question": "How do you handle tight deadlines?",
            "answer_quality": "Good",
            "strengths": ["Structured approach", "Prioritization skills"],
            "areas_for_improvement": ["More examples of past situations"]
        }
    ],
    "between_the_lines": {
        "enthusiasm": "High",
        "confidence": "Medium-High",
        "authenticity": "High",
        "notes": "Candidate showed genuine interest in the role and company."
    },
    "disqualifier_check": {
        "flags": [],
        "passed": True
    },
    "decision_reasoning": "The candidate meets all the key requirements for the role with strong technical skills and good cultural fit. Recommend proceeding to the next stage.",
    "evaluation_id": "eval123",
    "metadata": {
        "interview_id": SAMPLE_INTERVIEW_ID,
        "role_id": SAMPLE_ROLE_ID,
        "application_id": SAMPLE_APPLICATION_ID,
        "evaluated_at": datetime.now().isoformat(),
        "evaluated_by": "AI"
    }
}

# Sample service response
SAMPLE_SERVICE_RESPONSE = {
    "status": "success",
    "evaluation": SAMPLE_EVALUATION_RESULT
}

# Sample error response
SAMPLE_ERROR_RESPONSE = {
    "status": "error",
    "message": "Error evaluating interview: Interview transcript not found"
}

# Mock authentication dependency
def get_current_active_user():
    return {"uid": SAMPLE_USER_ID}

def get_current_user_no_uid():
    return {"name": "Test User"}  # No uid


@pytest.mark.asyncio
async def test_evaluate_public_interview():
    """Test the public interview evaluation endpoint."""
    with patch('app.services.interview_evaluation_service.InterviewEvaluationService.evaluate_interview_public', 
               new_callable=AsyncMock) as mock_evaluate:
        # Setup the mock
        mock_evaluate.return_value = SAMPLE_SERVICE_RESPONSE
        
        # Make the request
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/interview-evaluation/public/evaluate",
                json={
                    "session_id": SAMPLE_SESSION_ID,
                    "role_id": SAMPLE_ROLE_ID,
                    "application_id": SAMPLE_APPLICATION_ID
                }
            )
        
        # Check the response
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "evaluation" in data
        assert data["evaluation"]["evaluation_summary"] == SAMPLE_EVALUATION_RESULT["evaluation_summary"]
        
        # Verify the mock was called correctly
        mock_evaluate.assert_called_once_with(
            SAMPLE_SESSION_ID,
            SAMPLE_ROLE_ID,
            SAMPLE_APPLICATION_ID
        )


@pytest.mark.asyncio
async def test_evaluate_public_interview_error():
    """Test error handling in public interview evaluation endpoint."""
    with patch('app.services.interview_evaluation_service.InterviewEvaluationService.evaluate_interview_public', 
               new_callable=AsyncMock) as mock_evaluate:
        # Setup the mock to return an error
        mock_evaluate.return_value = SAMPLE_ERROR_RESPONSE
        
        # Make the request
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.post(
                "/api/v1/interview-evaluation/public/evaluate",
                json={
                    "session_id": SAMPLE_SESSION_ID,
                    "role_id": SAMPLE_ROLE_ID,
                    "application_id": SAMPLE_APPLICATION_ID
                }
            )
        
        # Check the response - should be a 400 Bad Request
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "Interview transcript not found" in data["detail"]


@pytest.mark.asyncio
async def test_auto_evaluate_public_interview_background():
    """Test the auto-evaluation endpoint with background tasks."""
    # Make the request with background parameter
    async with AsyncClient(app=app, base_url="http://test") as client:
        # Override the auto_evaluate_public_interview method to avoid actual execution
        with patch('app.services.interview_evaluation_service.InterviewEvaluationService.auto_evaluate_public_interview', 
                   new_callable=AsyncMock) as mock_evaluate:
            # Return success response for background task
            mock_evaluate.return_value = {"status": "success", "message": "Evaluation scheduled"}
            
            # The background task will be scheduled but not executed during the test
            response = await client.post(
                f"/api/v1/interview-evaluation/public/auto-evaluate/{SAMPLE_SESSION_ID}",
                params={"background": "true"}
            )
    
    # Check the response - should be success
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "message" in data
