# File: backend/tests/api/test_template_questions.py

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime

from app.main import app
from app.services.firebase_service import FirebaseService
from app.utils.id_generator import generate_question_id

client = TestClient(app)

# Mock data
mock_user = {
    "id": "tNVpVwJA3cVXJQZzHluFY5cAre13",
    "email": "<EMAIL>",
    "displayName": "Test User",
    "active": True
}

mock_role = {
    "id": "role123",
    "title": "Software Engineer",
    "createdAt": datetime.utcnow(),
    "updatedAt": datetime.utcnow()
}

mock_template = {
    "id": "template123",
    "stageIndex": 0,
    "stage": "Screening",
    "duration": "15 minutes",
    "customInstructions": "Initial screening to assess candidate's basic qualifications and interest.",
    "questions": [],
    "evaluationCriteria": [],
    "status": "Draft",
    "statistics": {
        "candidatesInterviewed": 0,
        "passRate": 0,
        "averageScore": 0,
        "topScore": 0,
        "topPerformerIds": []
    },
    "createdAt": datetime.utcnow(),
    "updatedAt": datetime.utcnow(),
    "createdBy": "tNVpVwJA3cVXJQZzHluFY5cAre13"
}

mock_question = {
    "id": "q_87654321",
    "question": "What is your experience with Python?",
    "purpose": "Assess technical skills",
    "idealAnswerCriteria": "3+ years of experience with specific examples",
    "statistics": {
        "topScore": 0,
        "topScoringCandidateId": "",
        "averageScore": 0
    }
}

# Mock the auth.verify_id_token method
@pytest.fixture(autouse=True)
def mock_auth():
    with patch("firebase_admin.auth.verify_id_token") as mock_verify_token:
        mock_verify_token.return_value = {"uid": mock_user["id"]}
        yield mock_verify_token

# Mock the firebase_service methods
@pytest.fixture(autouse=True)
def mock_firebase():
    with patch("app.api.v1.endpoints.templates.firebase_service") as mock_firebase_service:
        # Mock app property
        mock_firebase_service.app = MagicMock()
        
        # Mock get_user_by_id
        mock_firebase_service.get_user_by_id = AsyncMock(return_value=mock_user)
        
        # Mock verify_user_active
        mock_firebase_service.verify_user_active = AsyncMock(return_value=True)
        
        # Mock get_role
        mock_firebase_service.get_role = AsyncMock(return_value=mock_role)
        
        # Mock get_interview_template
        mock_firebase_service.get_interview_template = AsyncMock(return_value=mock_template)
        
        # Mock add_question_to_template
        mock_firebase_service.add_question_to_template = AsyncMock(return_value=mock_question["id"])
        
        # Mock update_question_in_template
        mock_firebase_service.update_question_in_template = AsyncMock(return_value=None)
        
        # Mock delete_question_from_template
        mock_firebase_service.delete_question_from_template = AsyncMock(return_value=None)
        
        # Mock reorder_questions_in_template
        mock_firebase_service.reorder_questions_in_template = AsyncMock(return_value=None)
        
        yield mock_firebase_service

# Mock ID generator
@pytest.fixture(autouse=True)
def mock_id_generator():
    with patch("app.utils.id_generator.generate_question_id") as mock_gen:
        mock_gen.return_value = mock_question["id"]
        yield mock_gen

def test_add_question():
    """Test adding a question to a template."""
    # Prepare request data
    question_data = {
        "question": "What is your experience with Python?",
        "purpose": "Assess technical skills",
        "idealAnswerCriteria": "3+ years of experience with specific examples"
    }
    
    # Make the request
    response = client.post(
        f"/api/v1/templates/{mock_role['id']}/{mock_template['id']}/questions",
        json=question_data,
        headers={"Authorization": "Bearer fake_token"}
    )
    
    # Check response
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == mock_question["id"]
    assert data["question"] == question_data["question"]
    assert data["purpose"] == question_data["purpose"]
    assert data["idealAnswerCriteria"] == question_data["idealAnswerCriteria"]
    assert "statistics" in data

def test_update_question():
    """Test updating a question in a template."""
    # Prepare request data
    update_data = {
        "question": "Updated question text",
        "purpose": "Updated purpose",
        "idealAnswerCriteria": "Updated criteria"
    }
    
    # Update the mock template to include the updated question
    updated_template = dict(mock_template)
    updated_template["questions"] = [
        {
            "id": mock_question["id"],
            "question": update_data["question"],
            "purpose": update_data["purpose"],
            "idealAnswerCriteria": update_data["idealAnswerCriteria"],
            "statistics": {
                "topScore": 0,
                "topScoringCandidateId": "",
                "averageScore": 0
            }
        }
    ]
    
    # Mock get_interview_template to return the updated template
    with patch("app.api.v1.endpoints.templates.firebase_service.get_interview_template", new_callable=AsyncMock) as mock_get_template:
        mock_get_template.return_value = updated_template
        
        # Make the request
        response = client.put(
            f"/api/v1/templates/{mock_role['id']}/{mock_template['id']}/questions/{mock_question['id']}",
            json=update_data,
            headers={"Authorization": "Bearer fake_token"}
        )
        
        # Check response
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == mock_question["id"]
        assert data["question"] == update_data["question"]
        assert data["purpose"] == update_data["purpose"]
        assert data["idealAnswerCriteria"] == update_data["idealAnswerCriteria"]

def test_delete_question():
    """Test deleting a question from a template."""
    # Make the request
    response = client.delete(
        f"/api/v1/templates/{mock_role['id']}/{mock_template['id']}/questions/{mock_question['id']}",
        headers={"Authorization": "Bearer fake_token"}
    )
    
    # Check response
    assert response.status_code == 204

def test_reorder_questions():
    """Test reordering questions in a template."""
    # Prepare request data
    reorder_data = {
        "questionIds": ["q_12345678", "q_87654321"]
    }
    
    # Create a mock template with reordered questions
    reordered_template = dict(mock_template)
    reordered_template["questions"] = [
        {
            "id": "q_12345678",
            "question": "Tell me about your background.",
            "purpose": "Assess overall fit",
            "idealAnswerCriteria": "Clear articulation of relevant experience",
            "statistics": {
                "topScore": 0,
                "topScoringCandidateId": "",
                "averageScore": 0
            }
        },
        {
            "id": "q_87654321",
            "question": "What is your experience with Python?",
            "purpose": "Assess technical skills",
            "idealAnswerCriteria": "3+ years of experience with specific examples",
            "statistics": {
                "topScore": 0,
                "topScoringCandidateId": "",
                "averageScore": 0
            }
        }
    ]
    
    # Mock reorder_questions_in_template
    with patch("app.api.v1.endpoints.templates.firebase_service.reorder_questions_in_template", new_callable=AsyncMock) as mock_reorder:
        # Mock get_interview_template to return the reordered template
        with patch("app.api.v1.endpoints.templates.firebase_service.get_interview_template", new_callable=AsyncMock) as mock_get_template:
            mock_get_template.return_value = reordered_template
            
            # Make the request
            response = client.put(
                f"/api/v1/templates/{mock_role['id']}/{mock_template['id']}/questions/reorder",
                json=reorder_data,
                headers={"Authorization": "Bearer fake_token"}
            )
            
            # Check response
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
            assert data[0]["id"] == "q_12345678"
            assert data[1]["id"] == "q_87654321"

def test_question_not_found():
    """Test error handling when a question is not found."""
    # Mock update_question_in_template to raise a ValueError
    with patch("app.api.v1.endpoints.templates.firebase_service.update_question_in_template", new_callable=AsyncMock) as mock_update:
        mock_update.side_effect = ValueError("Question not found")
        
        # Make the request
        response = client.put(
            f"/api/v1/templates/{mock_role['id']}/{mock_template['id']}/questions/nonexistent",
            json={"question": "Updated question"},
            headers={"Authorization": "Bearer fake_token"}
        )
        
        # Check response
        assert response.status_code == 400
        assert "Question not found" in response.json()["detail"]

def test_template_not_found_for_question():
    """Test error handling when a template is not found for a question operation."""
    # Mock add_question_to_template to raise ValueError
    with patch("app.api.v1.endpoints.templates.firebase_service.add_question_to_template", new_callable=AsyncMock) as mock_add_question:
        mock_add_question.side_effect = ValueError("Interview template nonexistent not found")

        # Make the request
        response = client.post(
            f"/api/v1/templates/{mock_role['id']}/nonexistent/questions",
            json={"question": "New question"},
            headers={"Authorization": "Bearer fake_token"}
        )

        # Check response
        assert response.status_code == 400
        assert "not found" in response.json()["detail"].lower() 