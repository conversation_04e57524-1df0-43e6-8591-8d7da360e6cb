# File: backend/tests/api/test_template_criteria.py

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime

from app.main import app
from app.services.firebase_service import FirebaseService
from app.utils.id_generator import generate_criterion_id

client = TestClient(app)

# Mock data
mock_user = {
    "id": "tNVpVwJA3cVXJQZzHluFY5cAre13",
    "email": "<EMAIL>",
    "displayName": "Test User",
    "active": True
}

mock_role = {
    "id": "role123",
    "title": "Software Engineer",
    "createdAt": datetime.utcnow(),
    "updatedAt": datetime.utcnow()
}

mock_criterion = {
    "id": "crit_abc123",
    "type": "ScoreCard",
    "competency": "Strategic Thinking",
    "weight": 0.3,
    "criteria": "Can they solve complex problems logically and proactively?",
    "description": "Look for structured approach to problem-solving"
}

mock_between_lines_criterion = {
    "id": "crit_def456",
    "type": "BetweenTheLines",
    "criteria": "Do they show structured thought processes?",
    "description": "Observe how they approach the problem"
}

mock_disqualifier_criterion = {
    "id": "crit_ghi789",
    "type": "Disqualifier",
    "criteria": "No structured problem-solving approach",
    "description": "Candidate should demonstrate a clear methodology"
}

mock_template = {
    "id": "template123",
    "stageIndex": 1,
    "stage": "Technical",
    "duration": "45 minutes",
    "customInstructions": "Technical interview to assess coding skills.",
    "questions": [],
    "evaluationCriteria": [],
    "status": "Draft",
    "statistics": {
        "candidatesInterviewed": 0,
        "passRate": 0,
        "averageScore": 0,
        "topScore": 0,
        "topPerformerIds": []
    },
    "createdAt": datetime.utcnow(),
    "updatedAt": datetime.utcnow(),
    "createdBy": "tNVpVwJA3cVXJQZzHluFY5cAre13"
}

# Mock the auth.verify_id_token method
@pytest.fixture(autouse=True)
def mock_auth():
    with patch("firebase_admin.auth.verify_id_token") as mock_verify_token:
        mock_verify_token.return_value = {"uid": mock_user["id"]}
        yield mock_verify_token

# Mock the firebase_service methods
@pytest.fixture(autouse=True)
def mock_firebase():
    with patch("app.api.v1.endpoints.templates.firebase_service") as mock_firebase_service:
        # Mock app property
        mock_firebase_service.app = MagicMock()
        
        # Mock get_user_by_id
        mock_firebase_service.get_user_by_id = AsyncMock(return_value=mock_user)
        
        # Mock verify_user_active
        mock_firebase_service.verify_user_active = AsyncMock(return_value=True)
        
        # Mock get_role
        mock_firebase_service.get_role = AsyncMock(return_value=mock_role)
        
        # Mock get_interview_template
        mock_firebase_service.get_interview_template = AsyncMock(return_value=mock_template)
        
        # Mock add_criterion_to_template
        mock_firebase_service.add_criterion_to_template = AsyncMock(return_value="crit_new123")
        
        # Mock update_criterion_in_template
        mock_firebase_service.update_criterion_in_template = AsyncMock(return_value=None)
        
        # Mock delete_criterion_from_template
        mock_firebase_service.delete_criterion_from_template = AsyncMock(return_value=None)
        
        # Mock set_template_pass_rate
        mock_firebase_service.set_template_pass_rate = AsyncMock(return_value=None)
        
        yield mock_firebase_service

def test_add_scorecard_criterion():
    """Test adding a ScoreCard criterion to a template."""
    # Create a new criterion
    new_criterion = {
        "type": "ScoreCard",
        "competency": "Crisis Management",
        "weight": 0.2,
        "criteria": "Can they handle unexpected setbacks and risks effectively?",
        "description": "Look for calm and structured approach to crisis"
    }
    
    # Mock generate_criterion_id
    with patch("app.utils.id_generator.generate_criterion_id") as mock_gen:
        mock_gen.return_value = "crit_new123"
        
        response = client.post(
            "/api/v1/templates/role123/template123/criteria",
            json=new_criterion,
            headers={"Authorization": "Bearer fake_token"}
        )
        
        assert response.status_code == 200
        assert response.json()["id"] == "crit_new123"
        assert response.json()["type"] == "ScoreCard"
        assert response.json()["competency"] == "Crisis Management"
        assert response.json()["weight"] == 0.2

def test_add_between_lines_criterion():
    """Test adding a BetweenTheLines criterion to a template."""
    # Create a new criterion
    new_criterion = {
        "type": "BetweenTheLines",
        "criteria": "Are they thinking long-term & strategically, or are they reactive?",
        "description": "Observe strategic vs tactical thinking"
    }
    
    # Mock generate_criterion_id
    with patch("app.utils.id_generator.generate_criterion_id") as mock_gen:
        mock_gen.return_value = "crit_new123"
        
        response = client.post(
            "/api/v1/templates/role123/template123/criteria",
            json=new_criterion,
            headers={"Authorization": "Bearer fake_token"}
        )
        
        assert response.status_code == 200
        assert response.json()["id"] == "crit_new123"
        assert response.json()["type"] == "BetweenTheLines"
        assert response.json()["criteria"] == "Are they thinking long-term & strategically, or are they reactive?"

def test_add_disqualifier_criterion():
    """Test adding a Disqualifier criterion to a template."""
    # Create a new criterion
    new_criterion = {
        "type": "Disqualifier",
        "criteria": "Cannot justify why they made a decision",
        "description": "Candidate must be able to explain their reasoning"
    }
    
    # Mock generate_criterion_id
    with patch("app.utils.id_generator.generate_criterion_id") as mock_gen:
        mock_gen.return_value = "crit_new123"
        
        response = client.post(
            "/api/v1/templates/role123/template123/criteria",
            json=new_criterion,
            headers={"Authorization": "Bearer fake_token"}
        )
        
        assert response.status_code == 200
        assert response.json()["id"] == "crit_new123"
        assert response.json()["type"] == "Disqualifier"
        assert response.json()["criteria"] == "Cannot justify why they made a decision"

def test_update_criterion():
    """Test updating a criterion in a template."""
    # Update data
    update_data = {
        "criteria": "Updated criteria text",
        "description": "Updated description"
    }
    
    # Mock get_interview_template to return the updated template
    with patch("app.api.v1.endpoints.templates.firebase_service.get_interview_template", new_callable=AsyncMock) as mock_get_template:
        # Create a copy of the template with the updated criterion
        updated_template = mock_template.copy()
        updated_criterion = mock_criterion.copy()
        updated_criterion.update(update_data)
        updated_template["evaluationCriteria"] = [updated_criterion, mock_between_lines_criterion, mock_disqualifier_criterion]
        
        mock_get_template.return_value = updated_template
        
        response = client.put(
            f"/api/v1/templates/role123/template123/criteria/{mock_criterion['id']}",
            json=update_data,
            headers={"Authorization": "Bearer fake_token"}
        )
        
        assert response.status_code == 200
        assert response.json()["id"] == mock_criterion["id"]
        assert response.json()["criteria"] == "Updated criteria text"
        assert response.json()["description"] == "Updated description"

def test_delete_criterion():
    """Test deleting a criterion from a template."""
    response = client.delete(
        f"/api/v1/templates/role123/template123/criteria/{mock_criterion['id']}",
        headers={"Authorization": "Bearer fake_token"}
    )
    
    assert response.status_code == 204

def test_set_pass_rate():
    """Test setting the pass rate for a template."""
    # Update data
    pass_rate_data = {
        "passRate": 0.75
    }
    
    # Mock get_interview_template to return the updated template
    with patch("app.api.v1.endpoints.templates.firebase_service.get_interview_template", new_callable=AsyncMock) as mock_get_template:
        # Create a copy of the template with the updated pass rate
        updated_template = mock_template.copy()
        updated_template["passRate"] = 0.75
        
        mock_get_template.return_value = updated_template
        
        response = client.put(
            "/api/v1/templates/role123/template123/pass-rate",
            json=pass_rate_data,
            headers={"Authorization": "Bearer fake_token"}
        )
        
        assert response.status_code == 200
        assert response.json()["passRate"] == 0.75

def test_invalid_scorecard_criterion():
    """Test validation for ScoreCard criterion."""
    # Missing competency
    invalid_criterion = {
        "type": "ScoreCard",
        "weight": 0.2,
        "criteria": "Can they handle unexpected setbacks and risks effectively?"
    }
    
    response = client.post(
        "/api/v1/templates/role123/template123/criteria",
        json=invalid_criterion,
        headers={"Authorization": "Bearer fake_token"}
    )
    
    assert response.status_code == 422  # Validation error
    
    # Invalid weight (> 1.0)
    invalid_criterion = {
        "type": "ScoreCard",
        "competency": "Crisis Management",
        "weight": 1.2,
        "criteria": "Can they handle unexpected setbacks and risks effectively?"
    }
    
    response = client.post(
        "/api/v1/templates/role123/template123/criteria",
        json=invalid_criterion,
        headers={"Authorization": "Bearer fake_token"}
    )
    
    assert response.status_code == 422  # Validation error

def test_invalid_pass_rate():
    """Test validation for pass rate."""
    # Invalid pass rate (> 1.0)
    invalid_pass_rate = {
        "passRate": 1.2
    }
    
    response = client.put(
        "/api/v1/templates/role123/template123/pass-rate",
        json=invalid_pass_rate,
        headers={"Authorization": "Bearer fake_token"}
    )
    
    assert response.status_code == 422  # Validation error 