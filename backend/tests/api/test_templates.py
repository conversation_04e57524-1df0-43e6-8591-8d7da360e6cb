# File: backend/tests/api/test_templates.py

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime

from app.main import app
from app.services.firebase_service import FirebaseService

client = TestClient(app)

# Mock data
mock_user = {
    "id": "tNVpVwJA3cVXJQZzHluFY5cAre13",
    "email": "<EMAIL>",
    "displayName": "Test User",
    "active": True
}

mock_role = {
    "id": "role123",
    "title": "Software Engineer",
    "createdAt": datetime.utcnow(),
    "updatedAt": datetime.utcnow()
}

mock_template = {
    "id": "template123",
    "stageIndex": 0,
    "stage": "Screening",
    "duration": "15 minutes",
    "customInstructions": "Initial screening to assess candidate's basic qualifications and interest.",
    "questions": [],
    "evaluationCriteria": [],
    "status": "Draft",
    "statistics": {
        "candidatesInterviewed": 0,
        "passRate": 0,
        "averageScore": 0,
        "topScore": 0,
        "topPerformerIds": []
    },
    "createdAt": datetime.utcnow(),
    "updatedAt": datetime.utcnow(),
    "createdBy": "tNVpVwJA3cVXJQZzHluFY5cAre13"
}

mock_templates = [mock_template]

# Mock the auth.verify_id_token method
@pytest.fixture(autouse=True)
def mock_auth():
    with patch("firebase_admin.auth.verify_id_token") as mock_verify_token:
        mock_verify_token.return_value = {"uid": mock_user["id"]}
        yield mock_verify_token

# Mock the firebase_service methods
@pytest.fixture(autouse=True)
def mock_firebase():
    with patch("app.api.v1.endpoints.templates.firebase_service") as mock_firebase_service:
        # Mock app property
        mock_firebase_service.app = MagicMock()
        
        # Mock get_user_by_id
        mock_firebase_service.get_user_by_id = AsyncMock(return_value=mock_user)
        
        # Mock verify_user_active
        mock_firebase_service.verify_user_active = AsyncMock(return_value=True)
        
        # Mock get_role
        mock_firebase_service.get_role = AsyncMock(return_value=mock_role)
        
        # Mock get_interview_templates
        mock_firebase_service.get_interview_templates = AsyncMock(return_value=mock_templates)
        
        # Mock get_interview_template
        mock_firebase_service.get_interview_template = AsyncMock(return_value=mock_template)
        
        # Mock create_interview_template
        mock_firebase_service.create_interview_template = AsyncMock(return_value="template123")
        
        # Mock update_interview_template
        mock_firebase_service.update_interview_template = AsyncMock(return_value=None)
        
        yield mock_firebase_service

def test_list_templates():
    """Test listing templates for a role."""
    response = client.get(
        "/api/v1/templates/role123",
        headers={"Authorization": "Bearer fake_token"}
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["id"] == "template123"

def test_get_template():
    """Test getting a specific template."""
    response = client.get(
        "/api/v1/templates/role123/template123",
        headers={"Authorization": "Bearer fake_token"}
    )
    assert response.status_code == 200
    assert response.json()["id"] == "template123"

def test_create_template():
    """Test creating a new template."""
    template_data = {
        "stageIndex": 1,
        "stage": "Technical",
        "duration": "45 minutes",
        "customInstructions": "Technical interview to assess coding skills.",
        "interviewGuide": "# Technical Interview Guide",
        "questions": [],
        "evaluationGuide": "# Technical Evaluation Guide",
        "evaluationCriteria": [],
        "status": "Draft"
    }
    
    response = client.post(
        "/api/v1/templates/role123",
        headers={"Authorization": "Bearer fake_token"},
        json=template_data
    )
    assert response.status_code == 200
    assert response.json()["id"] == "template123"

def test_update_template():
    """Test updating a template."""
    update_data = {
        "duration": "60 minutes",
        "customInstructions": "Updated instructions"
    }
    
    response = client.put(
        "/api/v1/templates/role123/template123",
        headers={"Authorization": "Bearer fake_token"},
        json=update_data
    )
    assert response.status_code == 200
    assert response.json()["id"] == "template123"

def test_template_not_found():
    """Test getting a non-existent template."""
    # Mock get_interview_template to return None
    with patch("app.api.v1.endpoints.templates.firebase_service.get_interview_template", new_callable=AsyncMock) as mock_get_template:
        mock_get_template.return_value = None
        response = client.get(
            "/api/v1/templates/role123/nonexistent",
            headers={"Authorization": "Bearer fake_token"}
        )
        assert response.status_code == 404
        assert "Template not found" in response.json()["detail"]

def test_role_not_found():
    """Test accessing templates for a non-existent role."""
    # Mock get_role to return None
    with patch("app.api.v1.endpoints.templates.firebase_service.get_role", new_callable=AsyncMock) as mock_get_role:
        mock_get_role.return_value = None
        response = client.get(
            "/api/v1/templates/nonexistent",
            headers={"Authorization": "Bearer fake_token"}
        )
        assert response.status_code == 404
        assert "Role not found" in response.json()["detail"]

def test_unauthorized_access():
    """Test unauthorized access to templates."""
    # Mock verify_id_token to raise an exception
    with patch("firebase_admin.auth.verify_id_token", side_effect=Exception("Invalid token")):
        response = client.get(
            "/api/v1/templates/role123",
            headers={"Authorization": "Bearer invalid_token"}
        )
        assert response.status_code == 401

def test_generate_interview_questions():
    """Test generating interview questions for a template."""
    # Mock the AIService
    mock_questions = [
        {
            "text": "Tell me about your experience with Python.",
            "purpose": "Assess technical skills and experience level",
            "idealAnswerCriteria": "Clear explanation of projects, libraries used, and problem-solving approach"
        },
        {
            "text": "Describe a challenging technical problem you've solved recently.",
            "purpose": "Evaluate problem-solving abilities and technical depth",
            "idealAnswerCriteria": "Structured approach to problem-solving, consideration of alternatives, clear solution"
        }
    ]
    
    mock_result = {
        "status": "success",
        "questions": mock_questions
    }
    
    with patch("app.services.ai_service.AIService") as MockAIService:
        # Configure the mock
        mock_ai_service = MockAIService.return_value
        mock_ai_service.generate_interview_questions = AsyncMock(return_value=mock_result)
        
        # Make the request
        response = client.post(
            "/api/v1/templates/role123/template123/generate-questions",
            headers={"Authorization": "Bearer fake_token"}
        )
        
        # Verify the response
        assert response.status_code == 200
        assert response.json()["status"] == "success"
        assert len(response.json()["questions"]) == 2
        
        # Verify the AI service was called with the correct parameters
        mock_ai_service.generate_interview_questions.assert_called_once()
        args = mock_ai_service.generate_interview_questions.call_args[0]
        assert args[0]["id"] == "role123"  # role
        assert args[1]["id"] == "template123"  # template

def test_generate_interview_questions_failure():
    """Test handling of AI service failure when generating questions."""
    # Mock the AIService to return an error
    mock_error_result = {
        "status": "error",
        "message": "Failed to generate questions"
    }
    
    with patch("app.services.ai_service.AIService") as MockAIService:
        # Configure the mock
        mock_ai_service = MockAIService.return_value
        mock_ai_service.generate_interview_questions = AsyncMock(return_value=mock_error_result)
        
        # Make the request
        response = client.post(
            "/api/v1/templates/role123/template123/generate-questions",
            headers={"Authorization": "Bearer fake_token"}
        )
        
        # Verify the response
        assert response.status_code == 500
        assert "Failed to generate questions" in response.json()["detail"] 