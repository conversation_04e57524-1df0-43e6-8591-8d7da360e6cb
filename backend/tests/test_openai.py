import pytest
import asyncio
from typing import Dict, Any
from app.services.openai.models.chat import Chat<PERSON>lient
from app.services.openai.prompts.manager import PromptManager
from app.services.openai.processors.json_processor import JSONProcessor
from app.services.openai.processors.stream_processor import StreamProcessor
from app.core.config import settings

@pytest.fixture
def chat_client():
    """Fixture for chat client with test mode enabled"""
    client = ChatClient(test_mode=True)
    # Set default parameters after initialization
    client.default_model = "gpt-4"
    return client

@pytest.fixture
def prompt_manager():
    """Fixture for prompt manager"""
    return PromptManager()

@pytest.fixture
def json_processor():
    """Fixture for JSON processor"""
    return JSONProcessor()

@pytest.fixture
def stream_processor():
    """Fixture for stream processor"""
    return StreamProcessor()

@pytest.mark.asyncio
async def test_chat_completion(chat_client):
    """Test basic chat completion."""
    messages = [
        {"role": "system", "content": "You are a helpful AI assistant."},
        {"role": "user", "content": "Say hello!"}
    ]
    
    response = await chat_client.create_chat_completion(
        messages=messages,
        temperature=0.8  # Test different temperature
    )
    assert response.choices[0].message.content is not None
    assert len(response.choices[0].message.content) > 0

@pytest.mark.asyncio
async def test_function_calling(chat_client):
    """Test function calling capability."""
    messages = [
        {"role": "system", "content": "You are a helpful weather assistant."},
        {"role": "user", "content": "What's the weather in London?"}
    ]
    
    functions = [
        {
            "name": "get_weather",
            "description": "Get the weather in a location",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA"
                    },
                    "unit": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"]
                    }
                },
                "required": ["location"]
            }
        }
    ]
    
    result = await chat_client.process_with_function_calling(
        messages=messages,
        functions=functions,
        temperature=0.2  # Lower temperature for more deterministic function calls
    )
    
    assert result["function_call"] is not None
    assert result["function_call"]["name"] == "get_weather"
    assert "location" in result["function_call"]["arguments"]

@pytest.mark.asyncio
async def test_streaming(chat_client, stream_processor):
    """Test streaming capability."""
    messages = [
        {"role": "system", "content": "You are a counting assistant."},
        {"role": "user", "content": "Count from 1 to 5."}
    ]
    
    stream = await chat_client.process_with_fallback(
        messages=messages,
        stream=True,
        temperature=0.1  # Very low temperature for consistent counting
    )
    
    chunks = []
    async for chunk in stream_processor.process_text_stream(stream):
        chunks.append(chunk)
    
    assert len(chunks) > 0
    full_text = "".join(chunks)
    assert len(full_text) > 0

@pytest.mark.asyncio
async def test_model_fallback(chat_client):
    """Test model fallback mechanism."""
    messages = [
        {"role": "system", "content": "You are a test assistant."},
        {"role": "user", "content": "Test fallback mechanism"}
    ]
    
    # Force an error with the default model
    chat_client.default_model = "invalid-model"
    
    # Should fallback to the backup model
    response = await chat_client.process_with_fallback(
        messages=messages,
        temperature=0.5
    )
    assert response.choices[0].message.content is not None
    assert len(response.choices[0].message.content) > 0

@pytest.mark.asyncio
async def test_stream_processing_with_functions(chat_client, stream_processor):
    """Test stream processing with function calls."""
    messages = [
        {"role": "system", "content": "You are a scheduling assistant."},
        {"role": "user", "content": "Schedule an interview for John Doe"}
    ]
    
    functions = [
        {
            "name": "schedule_interview",
            "description": "Schedule an interview with a candidate",
            "parameters": {
                "type": "object",
                "properties": {
                    "candidate_name": {"type": "string"},
                    "datetime": {"type": "string", "format": "date-time"},
                    "duration_minutes": {"type": "integer"}
                },
                "required": ["candidate_name", "datetime"]
            }
        }
    ]
    
    stream = await chat_client.process_with_fallback(
        messages=messages,
        functions=functions,
        stream=True,
        temperature=0.3
    )
    
    chunks = []
    async for chunk in stream_processor.process_text_stream(stream):
        chunks.append(chunk)
    
    assert len(chunks) > 0

@pytest.mark.asyncio
async def test_error_recovery(chat_client):
    """Test error recovery mechanisms."""
    messages = [
        {"role": "system", "content": "You are a test assistant."},
        {"role": "user", "content": "Test rate limit"}
    ]
    
    # Simulate multiple rapid requests
    responses = await asyncio.gather(
        *[chat_client.process_with_fallback(
            messages=messages,
            temperature=0.5
          ) for _ in range(3)],
        return_exceptions=True
    )
    
    # Check that some responses succeeded
    successful_responses = [r for r in responses if not isinstance(r, Exception)]
    assert len(successful_responses) > 0

@pytest.mark.asyncio
async def test_prompt_manager(prompt_manager):
    """Test prompt management."""
    messages = prompt_manager.create_messages(
        role="recruiter",
        template_name="role_intake",
        template_vars={"role_details": "Senior Python Developer position"},
        task="Create job description"
    )
    
    assert len(messages) > 0
    assert messages[0]["role"] == "system"
    assert messages[-1]["role"] == "user"
    assert "Senior Python Developer" in messages[-1]["content"]

@pytest.mark.asyncio
async def test_json_processing(json_processor):
    """Test JSON processing."""
    test_json = '{"name": "John", "age": 30}'
    
    # Test extraction
    data = json_processor.extract_json(test_json)
    assert data["name"] == "John"
    assert data["age"] == 30
    
    # Test extraction from text with JSON
    text_with_json = "Some text before {\"name\": \"John\", \"age\": 30} and after"
    data = json_processor.extract_json(text_with_json)
    assert data["name"] == "John"
    assert data["age"] == 30

@pytest.mark.asyncio
async def test_json_schema_validation(json_processor):
    """Test JSON schema validation and processing."""
    valid_json = '''
    {
        "name": "John Doe",
        "skills": ["Python", "AI"],
        "experience": 5
    }
    '''
    
    invalid_json = '''
    This is not a JSON string at all
    '''
    
    # Test valid JSON
    result = json_processor.extract_json(valid_json)
    assert result["name"] == "John Doe"
    assert len(result["skills"]) == 2
    
    # Test invalid JSON
    with pytest.raises(ValueError):
        json_processor.extract_json(invalid_json)

@pytest.mark.asyncio
async def test_vision_analysis(chat_client):
    """Test vision model capabilities."""
    messages = [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": "What's in this image?"},
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "https://example.com/test-image.jpg"
                    }
                }
            ]
        }
    ]
    
    # Skip vision test if model not available
    pytest.skip("Vision model not available or access denied")

@pytest.mark.asyncio
async def test_audio_transcription():
    """Test audio transcription."""
    # Skip this test as it requires a separate AudioClient implementation
    pytest.skip("Audio transcription requires separate client implementation")

@pytest.mark.asyncio
async def test_complex_prompt_management(prompt_manager):
    """Test complex prompt management with multiple roles and contexts."""
    # Test system prompt generation with task
    system_prompt = prompt_manager.get_system_prompt(
        role="recruiter",
        task="Evaluate a senior engineer candidate"
    )
    assert len(system_prompt) > 0
    assert "recruiter" in system_prompt.lower()
    assert "Evaluate a senior engineer candidate" in system_prompt
    
    # Test message creation with template
    messages = prompt_manager.create_messages(
        role="recruiter",
        template_name="role_analysis",
        task="Analyze market fit",
        template_vars={
            "role_info": "Senior Software Engineer position requiring 5+ years of experience in Python and AI",
            "market_context": "High demand for AI engineers in Silicon Valley"
        }
    )
    assert len(messages) > 0
    assert messages[0]["role"] == "system"
    assert "Senior Software Engineer" in messages[1]["content"]
    assert "High demand for AI engineers" in messages[1]["content"]

@pytest.mark.asyncio
async def test_error_handling(chat_client):
    """Test error handling with invalid requests."""
    with pytest.raises(Exception):
        await chat_client.create_chat_completion(
            messages=[],  # Empty messages should raise an error
        ) 