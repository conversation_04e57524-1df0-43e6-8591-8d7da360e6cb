import json
import logging
from functools import wraps
from typing import Any, Callable, Dict, List

logger = logging.getLogger("openai.conversation")

def log_openai_conversation(func: Callable) -> Callable:
    """Decorator to log OpenAI conversations during tests"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        logger.info("\n=== OpenAI Conversation Log ===")
        logger.info(f"Test: {func.__name__}")
        
        # Log request details
        logger.info("\nRequest Details:")
        logger.info(f"Model: {kwargs.get('model', 'default')}")
        logger.info(f"Temperature: {kwargs.get('temperature', 0.7)}")
        if 'max_tokens' in kwargs:
            logger.info(f"Max Tokens: {kwargs['max_tokens']}")
        if 'functions' in kwargs:
            logger.info("\nFunctions:")
            logger.info(json.dumps(kwargs['functions'], indent=2))
        if 'function_call' in kwargs:
            logger.info(f"\nFunction Call: {kwargs['function_call']}")
        
        # Extract messages from kwargs or args
        messages = kwargs.get('messages', None)
        if messages is None and args:
            for arg in args:
                if isinstance(arg, list) and all(isinstance(m, dict) for m in arg):
                    messages = arg
                    break
        
        if messages:
            logger.info("\nPrompt Messages:")
            logger.info(json.dumps(messages, indent=2))
        
        # Call the original function
        response = await func(*args, **kwargs)
        
        logger.info("\nFull Response:")
        if isinstance(response, dict):
            logger.info(json.dumps(response, indent=2))
        else:
            logger.info(f"Response type: {type(response)}")
            logger.info(str(response))
        
        logger.info("\n=== End of Conversation ===\n")
        return response
    
    return wrapper
