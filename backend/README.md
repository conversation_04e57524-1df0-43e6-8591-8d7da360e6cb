# Recruiva Backend

## Overview

The Recruiva backend is a robust FastAPI-based application that powers the AI-driven recruitment platform. It provides a comprehensive set of services for managing roles, handling real-time AI interviews, processing documents, managing user authentication, and evaluating resumes.

## Tech Stack

### Core Framework

- FastAPI 0.103.2
- Python 3.11.0
- Uvicorn 0.23.2
- Pydantic 2.10.5

### Database & Storage

- Firebase Admin 6.6.0
- Google Cloud Storage 2.19.0
- Google Cloud Firestore 2.19.0
- AsyncPG 0.30.0
- SQLAlchemy 2.0.37

### AI & ML Integration

- OpenAI 1.59.6
- GPT-4o, GPT-4o-mini, GPT-4o-mini-realtime models
- Real-time WebRTC Integration
- Advanced NLP Processing
- Function calling architecture

### Security

- Python-Jose 3.3.0
- PyJWT 2.10.1
- Passlib 1.7.4
- BCrypt 4.2.1
- Cryptography 44.0.0

## Project Structure

```
backend/
├── app/
│   ├── api/
│   │   └── v1/
│   │       ├── endpoints/              # REST API endpoints (roles, realtime, chat, templates, resume)
│   │       └── api.py                  # API router configuration
│   ├── core/
│   │   ├── config.py                   # Core configuration settings
│   │   ├── log_utils.py                # Logging utilities (token masking, structured logging)
│   │   └── logging_config.py           # Logging configuration
│   ├── services/
│   │   ├── openai/                     # OpenAI integration (see below)
│   │   ├── realtime/                   # Modular realtime agent architecture (see below)
│   │   ├── ai_service.py               # AI operations management (OpenAI, agent workflows)
│   │   ├── auth_service.py             # Authentication (Firebase, JWT)
│   │   ├── email_service.py            # Email operations (SMTP/IMAP, notifications)
│   │   ├── firebase_service.py         # Firebase operations (CRUD, user, storage)
│   │   ├── parser_service.py           # Document parsing (PDF, DOCX, text)
│   │   ├── roles_service.py            # Role management
│   │   ├── evaluation_service.py       # Evaluation logic (interview, resume)
│   │   ├── resume_evaluator_service.py # Resume evaluation
│   │   ├── resume_parser_service.py    # Resume parsing
│   │   └── templates_service.py        # Template management
│   ├── tasks/                          # Background tasks
│   ├── utils/                          # Utility functions (data transformers, validation, id generation)
│   └── main.py                         # Application entry point
├── tests/                              # Test suite (unit/integration tests)
├── scripts/                            # Utility scripts (email handler, migrations)
└── requirements.txt                    # Dependencies
```

---

## Architectural Layers

### 1. API Layer
- FastAPI-based REST endpoints for all major backend features
- Modular routing (roles, interviews, chat, templates, resume, etc.)
- API versioning under `/api/v1/`

### 2. Services Layer
- Singleton services for core business logic and integrations:
  - **FirebaseService**: Data persistence, user/role CRUD, storage
  - **AuthService**: User authentication/authorization (Firebase/JWT)
  - **EmailService**: Email sending/receiving, workflow automation
  - **RolesService**: Role/job management, metadata, validation
  - **AIService**: OpenAI integration, agent workflows, function calling
  - **ParserService**: PDF/DOCX/text parsing, job/resume extraction
  - **Evaluation/Resume/Template Services**: Specialized logic for candidate/interview evaluation, resume parsing, template management

### 3. OpenAI & AI Integration
- Centralized in `services/openai/`:
  - **Base client, chat completion, streaming, error handling**
  - **Processors**: JSON extraction/validation, streaming, function registry
  - **Prompts**: Modular, versioned prompt templates for all agent/AI workflows
  - **Models**: Base and chat model classes, fallback logic, streaming
  - **Config**: Model configuration manager, error types
  - **FunctionRegistry**: Central registry for OpenAI function calling
- Used by AIService and agent workflows for:
  - Role automation, email/communication analysis, interview Q&A, resume evaluation, etc.

### 4. Realtime Agent Architecture
- Modular agent loader (`services/realtime/agent_loader.py`)
- Agents for intake, interview, resume evaluation, etc.
- Each agent has its own context, functions, services, and prompts
- Supports real-time WebRTC interview flows, dynamic agent loading, and fallback to shared modules

### 5. Utility Modules
- `utils/`: Data transformers, validation, ID generation, OpenAI helpers
- `core/`: Logging, config, environment management

---

## API Endpoints (Summary)
- **Roles**: CRUD, search, status, transcripts
- **Realtime**: Interview session, feedback, scoring
- **Chat Completion**: AI chat, function calling
- **Templates**: Management and retrieval
- **Resume Evaluation**: Parsing and scoring

---

## Testing & Development
- Test suite in `/tests/`, includes unit and integration tests
- Utility scripts for migrations, email testing, logging validation
- Example: `pytest --cov=app tests/`

---

## Deployment & Configuration
- Docker-based deployment (see Dockerfile, render.yaml)
- Environment variables for Firebase, OpenAI, email, storage, security
- Logging with token masking, structured logs, configurable verbosity
- Automatic deployments via Render (main branch)

---

## OpenAI & AI Services (Details)
For full details, see [`app/services/README.md`](app/services/README.md).
- Describes all OpenAI processors, prompt management, models, error handling, and agent/AI workflows
- Documents prompt files, function registry, streaming, configuration, and error types

---

## Best Practices & Security
- Always use singleton service instances
- Validate all input data
- Mask sensitive data in logs
- Use environment variables for all secrets/keys
- Follow modular, testable architecture for all new features

---

## Core Services

### Firebase Service

- Singleton pattern implementation for Firebase operations
- CRUD operations for roles and user data
- Document storage and retrieval
- User management and authentication
- Data migration utilities

### Authentication Service

- User authentication and authorization
- Token management (Firebase)
- Custom claims handling
- User verification and validation
- Pre-registration checks

### Email Service

- Email operations and notifications
- SMTP/IMAP integration
- Sender validation
- Communication processing
- Firebase user validation

### Roles Service

- Job role management
- CRUD operations
- Metadata handling
- Data validation
- Role-specific transformations

### AI Service

- OpenAI API integration
- Role-related AI processing
- Email content analysis
- Function registry for AI operations
- Real-time interview management

### Parser Service

- Document parsing (PDF, DOCX)
- Job description processing
- Structured data extraction
- Configuration management

## API Endpoints

### Roles API (/api/v1/roles)

- POST / - Create role
- GET / - List roles
- GET /{role_id} - Get role details
- PUT /{role_id} - Update role
- DELETE /{role_id} - Delete role
- PATCH /{role_id}/status - Update status
- PATCH /{role_id}/priority - Update priority
- POST /{role_id}/intake-recording - Upload recording
- POST /parse-job-description - Parse description
- POST /migrate-to-team-structure - Team migration
- GET /{role_id}/intake-transcripts - Get transcripts
- POST /{role_id}/intake-transcripts - Create transcript
- PUT /{role_id}/intake-transcripts/{transcript_id} - Update transcript
- PATCH /{role_id}/intake-transcripts/{transcript_id}/complete - Complete transcript
- POST /{role_id}/intake-calls - Create intake call
- GET /{role_id}/intake-calls - List intake calls

### Realtime API (/api/v1/realtime)

- POST /session - Create realtime session

### Templates API (/api/v1/templates)

- POST / - Create template
- GET / - List templates
- GET /{template_id} - Get template details
- PUT /{template_id} - Update template
- DELETE /{template_id} - Delete template

### Resume Evaluation API (/api/v1/resume-evaluation)

- POST / - Evaluate resume
- GET /{evaluation_id} - Get evaluation results

### Chat Completion API (/api/v1/chat-completion)

- POST / - Get chat completion response

## Real-time Interview Architecture

The real-time interview system leverages OpenAI's latest models with WebRTC for seamless interviewing:

### Components

- **WebRTC Integration**: Real-time audio/video communication
- **OpenAI Realtime Model**: Using `gpt-4o-mini-realtime-preview-2024-12-17`
- **Interview Agent**: Specialized system prompt for conducting interviews
- **Evaluation Engine**: Real-time candidate assessment and scoring
- **Transcript Generation**: Automatic transcription of interviews

### Interview Flow

1. Session creation and setup
2. Candidate connection via WebRTC
3. AI-driven interview conducting
4. Real-time evaluation and scoring
5. Feedback generation and reporting

## Security Features

### Authentication

- Firebase Authentication integration
- JWT token validation
- Role-based access control
- Custom claims management

### Data Protection

- Encrypted storage
- Secure file handling
- Environment variable protection
- API key management

## Development Setup

1. Create virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # Unix
venv\Scripts\activate     # Windows
```

2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. Configure environment variables:

```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Run development server:

```bash
uvicorn app.main:app --reload
```

## Environment Variables

Required environment variables (see .env.example):

- Firebase configuration
- OpenAI API keys
- Email settings
- Storage configuration
- Security settings

## Testing

Run tests with:

```bash
pytest
```

Coverage report:

```bash
pytest --cov=app tests/
```

## Deployment

### Production Deployment (Render)

- Configured via render.yaml
- Automatic deployments from main branch
- Environment variable management
- Health checks and monitoring
- Docker-based deployment

## Documentation

API documentation available at:

- Swagger UI: /docs
- ReDoc: /redoc

## OpenAI Integration

The OpenAI integration is structured for scalability with the following key components:

### Models Used

- **GPT-4o**: For comprehensive analysis and complex decision-making
- **GPT-4o-mini**: For lightweight processing and faster response times
- **GPT-4o-mini-realtime-preview**: For real-time interview scenarios

### Directory Structure
```
backend/app/services/openai/
├── base.py                  # Base class for OpenAI services
├── chat_completion_service.py  # Chat completion implementation
├── function_registry.py     # Function registry for AI operations
├── models/                  # Model-specific implementations
├── prompts/                 # System prompts
│   ├── manager.py           # Prompt management
│   ├── intake_agent.md      # Intake agent prompt
│   ├── interview_agent.md   # Interview agent prompt
│   ├── resume_eval.md       # Resume evaluation prompt
│   └── template_agent.md    # Template agent prompt
```

### Function Calling Architecture

The backend implements OpenAI's function calling for structured operations:

1. **Function Registry**: Central registry of available functions
2. **Parameter Extraction**: AI-powered parameter extraction
3. **Function Execution**: Secure execution with validation
4. **Response Formatting**: Standardized response formatting

## Logging

The application has a comprehensive logging system configured to provide visibility into application operations while protecting sensitive information:

### Features

- **Token Masking**: Authorization tokens are automatically masked in logs to prevent sensitive data exposure
- **Protocol Error Filtering**: Common HTTP protocol errors are filtered out to reduce noise in logs
- **Structured Logging**: Option for JSON-formatted logs for better integration with log aggregation tools
- **Configurable Verbosity**: Logging levels can be adjusted based on environment needs

### Debug Mode

For development and troubleshooting, you can enable verbose logging:

```bash
# In the logging_config.py
configure_logging(level=logging.DEBUG)
```

### Sensitive Data Protection

The application masks sensitive data in logs:

```python
# Example of masked headers in logs
Request headers: {'host': 'localhost:8000', 'authorization': 'Bearer [TOKEN_MASKED]', 'user-agent': 'Mozilla/5.0'}
```

To test the token masking functionality:

```bash
python -m backend.scripts.test_logging_mask
```
