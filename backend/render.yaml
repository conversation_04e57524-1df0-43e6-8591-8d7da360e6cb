services:
  - type: web
    name: recruiva-backend
    env: docker
    dockerfilePath: ./Dockerfile
    healthCheckPath: /api/v1/health
    envVars:
      - key: PORT
        value: 8000
      - key: WORKERS
        value: 4
      - key: ENVIRONMENT
        value: production
    scaling:
      minInstances: 1
      maxInstances: 3
      targetMemoryPercent: 80
    buildCommand: |
      pip install --upgrade pip
      pip install PyYAML==6.0.1
      pip install -r requirements.txt
    startCommand: uvicorn app.main:app --host 0.0.0.0 --port $PORT --workers $WORKERS