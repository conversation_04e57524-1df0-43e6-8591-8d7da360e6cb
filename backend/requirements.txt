aiofiles==24.1.0
aiohttp==3.9.3
annotated-types==0.7.0
anyio==3.7.1
asyncpg==0.30.0
bcrypt==4.2.1
CacheControl==0.14.2
cachetools==5.5.0
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
coverage==7.6.10
cryptography==44.0.0
distro==1.9.0
dnspython==2.7.0
ecdsa==0.19.0
email_validator==2.2.0
fastapi==0.103.2
firebase-admin==6.6.0
google-api-core==2.24.0
google-api-python-client==2.158.0
google-auth==2.37.0
google-auth-httplib2==0.2.0
google-cloud-core==2.4.1
google-cloud-firestore==2.19.0
google-cloud-storage==2.19.0
google-crc32c==1.6.0
google-resumable-media==2.7.2
googleapis-common-protos==1.66.0
greenlet==3.1.1
grpcio==1.69.0
grpcio-status==1.69.0
h11==0.14.0
httpcore==0.17.3
httplib2==0.22.0
httpx==0.24.1
idna==3.10
iniconfig==2.0.0
jiter==0.8.2
lxml==5.3.0
markdown>=3.5.1
msgpack==1.1.0
openai==1.59.6
packaging==24.2
passlib==1.7.4
pluggy==1.5.0
proto-plus==1.25.0
protobuf==5.29.3
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.10.5
pydantic-settings==2.7.1
pydantic_core==2.27.2
PyJWT==2.10.1
pyparsing==3.2.1
PyPDF2==3.0.1
pytest==7.4.4
pytest-asyncio==0.21.2
pytest-cov==4.1.0
pytest-mock==3.14.0
python-dotenv==1.0.1
python-jose==3.3.0
python-multipart==0.0.20
pytz==2024.2
requests==2.32.3
rsa==4.9
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.37
starlette==0.27.0
tqdm==4.67.1
typing_extensions==4.12.2
uritemplate==4.1.1
urllib3==2.3.0
uvicorn==0.23.2
backoff==2.2.1
beautifulsoup4>=4.12.2