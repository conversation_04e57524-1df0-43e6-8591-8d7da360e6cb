import asyncio
from app.services.roles_service import RolesService

async def test_prompt_format():
    # Get a role
    rs = RolesService()
    role = await rs.get_role_by_id('crcwMB6w6dpDmGRLvpxe')
    
    # Get a simplified test prompt
    test_prompt = """
Role: You are a helpful assistant.

{existing_role_data_section}

Ask questions about the following topics:
1. Introduction{enrichment_intro}
2. Experience
3. Skills
    """
    
    context = {}
    
    if role:
        # Create a section with role details for testing
        existing_role_data_section = f"""
### TEST ROLE DATA
This is a test for role {role.get('id')}:
- Title: {role.get('title')}
- Team: {role.get('team', '')}
"""
        # Add the formatted section to the context
        context["existing_role_data_section"] = existing_role_data_section
        context["enrichment_intro"] = ", mentioning this is an enrichment call"
    else:
        context["existing_role_data_section"] = ""
        context["enrichment_intro"] = ""
    
    # Format the prompt with the context
    try:
        formatted_prompt = test_prompt.format(**context)
        print("String formatting succeeded!")
        print("\nContext keys:", list(context.keys()))
        print("\nFormatted prompt:", formatted_prompt)
    except Exception as format_error:
        print(f"Error formatting prompt: {str(format_error)}")

asyncio.run(test_prompt_format()) 