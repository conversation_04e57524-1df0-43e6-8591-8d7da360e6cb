import asyncio
import os
from app.services.roles_service import RolesService

async def test_intake_prompt_format():
    # Get a role
    rs = RolesService()
    role = await rs.get_role_by_id('crcwMB6w6dpDmGRLvpxe')
    
    # Load the intake agent prompt
    prompt_path = os.path.join('app', 'services', 'realtime', 'agents', 'intake_agent', 'prompts', 'intake_agent.prompt')
    
    if not os.path.exists(prompt_path):
        print(f"Prompt file not found at {prompt_path}")
        return
    
    with open(prompt_path, 'r') as file:
        intake_prompt = file.read()
    
    context = {}
    
    if role:
        # Handle key responsibilities
        resp_items = role.get("keyResponsibilities", [])
        resp_text = ""
        if resp_items:
            resp_text = "- " + "\n- ".join(resp_items)
        
        # Handle required skills
        req_skills = role.get("requiredSkills", {})
        req_skills_text = ""
        if req_skills:
            req_skills_text = "- " + "\n- ".join([f"{skill}: {level}" for skill, level in req_skills.items()])
        
        # Handle preferred skills
        pref_skills = role.get("preferredSkills", {})
        pref_skills_text = ""
        if pref_skills:
            pref_skills_text = "- " + "\n- ".join([f"{skill}: {level}" for skill, level in pref_skills.items()])
        
        # Create a section with role details
        existing_role_data_section = f"""
### EXISTING ROLE INFORMATION
You already have some information about this role. This is an enrichment call to gather additional details:
- Title: {role.get("title", "")}
- Team: {role.get("team", "")}
- Summary: {role.get("summary", "")}
- Job Type: {role.get("jobType", "")}
- Years of Experience: {role.get("yearsOfExperience", "")}

Key Responsibilities: 
{resp_text}

Required Skills: 
{req_skills_text}

Preferred Skills: 
{pref_skills_text}

About the Team: 
{role.get("aboutTeam", "")}

In this enrichment call, focus on filling gaps and gathering more comprehensive information about the role. Ask targeted questions to expand on existing information.
"""
        # Add the formatted section to the context
        context["existing_role_data_section"] = existing_role_data_section
        context["enrichment_intro"] = ", mentioning this is an enrichment call to gather more detailed information"
    else:
        context["existing_role_data_section"] = ""
        context["enrichment_intro"] = ""
    
    # Format the prompt with the context
    try:
        formatted_prompt = intake_prompt.format(**context)
        print("Intake agent prompt formatting succeeded!")
        print("\nContext keys:", list(context.keys()))
        print("\nFirst 500 characters of formatted prompt:", formatted_prompt[:500])
        print("\nLast 500 characters of formatted prompt:", formatted_prompt[-500:])
    except Exception as format_error:
        print(f"Error formatting intake agent prompt: {str(format_error)}")

asyncio.run(test_intake_prompt_format()) 